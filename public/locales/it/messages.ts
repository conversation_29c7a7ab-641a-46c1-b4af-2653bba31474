// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = {
  "addressLookup.input.error": "",
  "addressLookup.toggle.default": "",
  "addressLookup.toggle.description": "",
  "addressLookup.toggle.other": "",
  "arViews.button.openOneDrive": "",
  "arViews.button.openVaillantApp": "",
  "arViews.button.unit.100L_AIO": "",
  "arViews.button.unit.100L_buffer": "",
  "arViews.button.unit.12kW_outdoor": "",
  "arViews.button.unit.150L_cylinder_slimline": "",
  "arViews.button.unit.200L_cylinder": "",
  "arViews.button.unit.250L_AIO": "",
  "arViews.button.unit.250L_cylinder": "",
  "arViews.button.unit.300L_cylinder": "",
  "arViews.button.unit.40L_buffer": "",
  "arViews.button.unit.6kW_outdoor": "",
  "arViews.button.unit.8kW_outdoor": "",
  "arViews.button.unit.compact_hydrobox": "",
  "arViews.button.unit.thermostat": "",
  "arViews.text.arDescription": "",
  "arViews.text.howToAltDescription": "",
  "arViews.text.howToDescription": "",
  "arViews.text.vaillantHowToDescription": "",
  "arViews.title.airaHP": "",
  "arViews.title.arViews": "",
  "arViews.title.bufferTanks": "",
  "arViews.title.cylinders": "",
  "arViews.title.indoorUnits": "",
  "arViews.title.outdoorUnits": "",
  "arViews.title.thermostat": "",
  "arViews.title.vaillantHP": "",
  "baselineCalc.boolean.false": "",
  "baselineCalc.boolean.true": "",
  "baselineCalc.label.complexityElectricalWorks": "",
  "baselineCalc.label.complexityExistingUnit": "",
  "baselineCalc.label.complexityIndoorPlumbing": "",
  "baselineCalc.label.complexityOutdoorPlumbing": "",
  "baselineCalc.label.indoorAndOutdoorDistance": "",
  "baselineCalc.label.lengthElectricCableDuct": "",
  "baselineCalc.label.modelVersion": "",
  "baselineCalc.label.numberThermostats": "",
  "baselineCalc.label.radiators": "",
  "baselineCalc.label.thermostaticValves": "",
  "baselineCalc.label.wallsThickness": "",
  "baselineCalc.notify.error": "",
  "baselineCalc.notify.saving": "",
  "baselineCalc.notify.success": "",
  "baselineCalc.table.macroActivity": "",
  "baselineCalc.table.microActivity": "",
  "baselineCalc.table.totalManHours": "",
  "baselineCalc.text.electricianHours": "",
  "baselineCalc.text.installationHours": "",
  "baselineCalc.text.noModel": "",
  "baselineCalc.text.singleSaveWarning": "",
  "baselineCalc.text.totalManHours": "",
  "baselineCalc.title.byActivity": "",
  "baselineCalc.title.byRole": "",
  "baselineCalc.title.calculateInstallationBaseline": "",
  "baselineCalc.title.inputs": "",
  "baselineCalc.title.output": "",
  "baselineCalc.title.scenarioDefinition": "",
  "baselineCalc.title.summary": "",
  "billOfMaterials.addBundle": "",
  "billOfMaterials.addBundleCollection": "",
  "billOfMaterials.addItemsToEquivalencyGroup.success": "",
  "billOfMaterials.addItemsToEquivalencyGroupTooltip": "",
  "billOfMaterials.addVersion": "",
  "billOfMaterials.advanced": "",
  "billOfMaterials.advancedSearch": "",
  "billOfMaterials.advancedSearchTooltip": "",
  "billOfMaterials.bulkSetLabelTooltip": "",
  "billOfMaterials.bundleCollections": "",
  "billOfMaterials.bundles": "",
  "billOfMaterials.bundleSelect.description": "",
  "billOfMaterials.bundleSelect.error": "",
  "billOfMaterials.bundleSelect.loading": "",
  "billOfMaterials.bundleSelect.outdatedBundle": "",
  "billOfMaterials.bundleSelect.title": "",
  "billOfMaterials.clearInterchangeable.confirmDescription": "",
  "billOfMaterials.clearInterchangeable.confirmTitle": "",
  "billOfMaterials.confirmCloseEdit.description": "",
  "billOfMaterials.confirmCloseEdit.title": "",
  "billOfMaterials.confirmDeleteBundle.description": "",
  "billOfMaterials.confirmDeleteBundle.title": "",
  "billOfMaterials.confirmDeleteCollection.description": "",
  "billOfMaterials.confirmDeleteCollection.title": "",
  "billOfMaterials.confirmDeleteItem.description": "",
  "billOfMaterials.confirmDeleteItem.title": "",
  "billOfMaterials.copyBundle": "",
  "billOfMaterials.copyVersion": "",
  "billOfMaterials.costOfAllItems": "",
  "billOfMaterials.costOfAllItems.info": "",
  "billOfMaterials.deleteBundle": "",
  "billOfMaterials.deleteCollection": "",
  "billOfMaterials.deleteItem": "",
  "billOfMaterials.deleteItemEquivalence.confirm.description": "",
  "billOfMaterials.deleteItemEquivalence.confirm.title": "",
  "billOfMaterials.deleteVersion": "",
  "billOfMaterials.design.hlc.itemNotFoundInERP": "",
  "billOfMaterials.editBundle": "",
  "billOfMaterials.editBundle.browseItems": "",
  "billOfMaterials.editBundle.descriptionFieldLabel": "",
  "billOfMaterials.editBundle.itemsInBundle": "",
  "billOfMaterials.editBundle.itemsInInstallationKit": "",
  "billOfMaterials.editBundle.itemsInInstallationKitTooltip": "",
  "billOfMaterials.editBundle.itemsInVersion": "",
  "billOfMaterials.editBundle.itemsTable.amountColumn": "",
  "billOfMaterials.editBundle.itemsTable.amountColumn.infoBox": "",
  "billOfMaterials.editBundle.itemsTable.descritionColumn": "",
  "billOfMaterials.editBundle.itemsTable.erpId": "",
  "billOfMaterials.editBundle.itemsTable.instructionsColumn": "",
  "billOfMaterials.editBundle.itemsTable.nameColumn": "",
  "billOfMaterials.editBundle.titleAdd": "",
  "billOfMaterials.editBundle.titleEdit": "",
  "billOfMaterials.editBundle.titleFieldLabel": "",
  "billOfMaterials.editBundleCollection.descriptionFieldLabel": "",
  "billOfMaterials.editBundleCollection.titleAdd": "",
  "billOfMaterials.editBundleCollection.titleEdit": "",
  "billOfMaterials.editBundleCollection.titleFieldLabel": "",
  "billOfMaterials.editCollection": "",
  "billOfMaterials.editVersion": "",
  "billOfMaterials.editVersion.titleAdd": "",
  "billOfMaterials.editVersion.titleEdit": "",
  "billOfMaterials.grossMarginTwo": "",
  "billOfMaterials.grossMarginTwo.info": "",
  "billOfMaterials.installationKit.AikItems": "",
  "billOfMaterials.installationKit.itemNotUsedElsewhere": "",
  "billOfMaterials.installationKit.setAikItems": "",
  "billOfMaterials.interchangeability": "",
  "billOfMaterials.itemCatalogue.duplicates.description": "",
  "billOfMaterials.itemCatalogue.filter.category": "",
  "billOfMaterials.itemCatalogue.filter.search": "",
  "billOfMaterials.itemCatalogue.select": "",
  "billOfMaterials.itemCatalogue.subtitle": "",
  "billOfMaterials.itemCatalogue.table.archived": "",
  "billOfMaterials.itemCatalogue.table.category": "",
  "billOfMaterials.itemCatalogue.table.costPerUnit": "",
  "billOfMaterials.itemCatalogue.table.description": "",
  "billOfMaterials.itemCatalogue.table.details": "",
  "billOfMaterials.itemCatalogue.table.erpId": "",
  "billOfMaterials.itemCatalogue.table.partOfBundles": "",
  "billOfMaterials.itemCatalogue.table.supplier": "",
  "billOfMaterials.itemCatalogue.table.version": "",
  "billOfMaterials.itemCatalogue.title": "",
  "billOfMaterials.itemDetails": "",
  "billOfMaterials.itemRelations": "",
  "billOfMaterials.labels.addNew": "",
  "billOfMaterials.labels.newLabel": "",
  "billOfMaterials.labels.selectLabel": "",
  "billOfMaterials.mandatoryBundle": "",
  "billOfMaterials.mandatoryBundleTooltip": "",
  "billOfMaterials.miscellaneousItems.addCustomItem": "",
  "billOfMaterials.moveDown": "",
  "billOfMaterials.moveUp": "",
  "billOfMaterials.quantity": "",
  "billOfMaterials.removeItemsFromEquivalencyGroup.success": "",
  "billOfMaterials.setInterchangeable.confirmDescription": "",
  "billOfMaterials.setInterchangeable.confirmTitle": "",
  "billOfMaterials.setLabel": "",
  "billOfMaterials.showLess": "",
  "billOfMaterials.showMore": "",
  "billOfMaterials.unit": "",
  "billOfMaterials.vanStock.duplicateError": "",
  "billOfMaterials.vanStock.regions": "",
  "billOfMaterials.vanStock.selectAllRegions": "",
  "billOfMaterials.vanStock.selectRegions": "",
  "billOfMaterials.vanStock.stockType": "",
  "billOfMaterials.versions": "",
  "billOfMaterialsDesignPage.addMiscellaneousItems": "",
  "billOfMaterialsDesignPage.addMiscellaneousItemsDescription": "",
  "billOfMaterialsDesignPage.Items": "",
  "billOfMaterialsDesignPage.notification.bomLocked": "",
  "billOfMaterialsDesignPage.notification.locking": "",
  "billOfMaterialsDesignPage.notification.locking.error": "",
  "billOfMaterialsDesignPage.notification.locking.success": "",
  "billOfMaterialsDesignPage.notification.markingAsReady": "",
  "billOfMaterialsDesignPage.notification.markingAsReady.error": "",
  "billOfMaterialsDesignPage.notification.markingAsReady.success": "",
  "billOfMaterialsDesignPage.notification.saving": "",
  "billOfMaterialsDesignPage.notification.saving.error": "",
  "billOfMaterialsDesignPage.notification.saving.success": "",
  "billOfMaterialsDesignPage.notification.unlocking": "",
  "billOfMaterialsDesignPage.notification.unlocking.error": "",
  "billOfMaterialsDesignPage.notification.unlocking.success": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.error": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.success": "",
  "billOfMaterialsDesignPage.readyForProcurement": "",
  "billOfMaterialsDesignPage.saveAndLock.button": "",
  "billOfMaterialsDesignPage.saveAndMarkAsReady": "",
  "billOfMaterialsDesignPage.status.readyForProcurement": "",
  "billOfMaterialsDesignPage.title": "",
  "billOfMaterialsDesignPage.unlock.button": "",
  "billOfMaterialsDesignPage.unmarkAsReady": "",
  "booking.button.schedule.survey": "",
  "booking.no.survey.scheduled": "",
  "bookingTool.body.completedOn": "",
  "bookingTool.body.dateAndTime": "",
  "bookingTool.body.loading": "",
  "bookingTool.body.noTimeSlotsAvailable": "",
  "bookingTool.body.scheduledForDateAndTime": "",
  "bookingTool.body.scheduledForTimeWindow": "",
  "bookingTool.body.surveyorName": "",
  "bookingTool.body.surveyUnscheduled": "",
  "bookingTool.body.unsavedChanges": "",
  "bookingTool.button.bookJob": "",
  "bookingTool.button.goToHouseDataTab": "",
  "bookingTool.button.hideScheduler": "",
  "bookingTool.button.openMap": "",
  "bookingTool.button.salesVisitMap": "",
  "bookingTool.button.scheduleSurvey": "",
  "bookingTool.button.unschedule": "",
  "bookingTool.checkbox.battery": "",
  "bookingTool.checkbox.photovoltaic": "",
  "bookingTool.checkbox.solarThermal": "",
  "bookingTool.deactivatedUser": "",
  "bookingTool.error.jobSchedulingFailure": "",
  "bookingTool.error.magicplanAccountNeeded": "",
  "bookingTool.error.noAddress": "",
  "bookingTool.error.noResourceSelected": "",
  "bookingTool.error.noTimeSlotSelected": "",
  "bookingTool.error.resourceNotAvailable": "",
  "bookingTool.label.assignSpecificPerson": "",
  "bookingTool.label.dateAndTime": "",
  "bookingTool.label.installer": "",
  "bookingTool.label.magicPlan": "",
  "bookingTool.label.mobileLink": "",
  "bookingTool.label.notes": "",
  "bookingTool.label.salesSurvey": "",
  "bookingTool.label.salesSurveyor": "",
  "bookingTool.label.skills": "",
  "bookingTool.label.surveyBookings": "",
  "bookingTool.label.surveyDuration": "",
  "bookingTool.label.surveyorType": "",
  "bookingTool.label.surveyPDF": "",
  "bookingTool.label.technicalSurvey": "",
  "bookingTool.label.technicalSurveyor": "",
  "bookingTool.label.timeSlotsAvailable": "",
  "bookingTool.label.unknown": "",
  "bookingTool.label.videoSalesMeeting": "",
  "bookingTool.label.webLink": "",
  "bookingTool.placeholder.selectPerson": "",
  "bookingTool.popup.unscheduleDescription": "",
  "bookingTool.popup.unscheduleHeader": "",
  "bookingTool.radio.both": "",
  "bookingTool.radio.duration": "",
  "bookingTool.radio.salesSurveyor": "",
  "bookingTool.radio.technicalSurveyor": "",
  "bookingTool.skillsChip.BATTERY": "",
  "bookingTool.skillsChip.HEAT_PUMP": "",
  "bookingTool.skillsChip.PHOTOVOLTAIC": "",
  "bookingTool.skillsChip.SOLAR_THERMAL": "",
  "bookingTool.surveyStatus.CANCELLED": "",
  "bookingTool.surveyStatus.FINISHED": "",
  "bookingTool.surveyStatus.IN_PROGRESS": "",
  "bookingTool.surveyStatus.NOT_SCHEDULED": "",
  "bookingTool.surveyStatus.READY": "",
  "bookingTool.surveyStatus.SCHEDULED": "",
  "bookingTool.surveyStatus.UNKNOWN": "",
  "bookingTool.tab.setTimeLater": "",
  "bookingTool.tab.specificTime": "",
  "bookingTool.tab.timeWindow": "",
  "bookingTool.timeWindow.afternoon": "",
  "bookingTool.timeWindow.morning": "",
  "bookingTool.title.homeSurveys": "",
  "bookingTool.title.noAddress": "",
  "bookingTool.title.salesSurvey": "",
  "bookingTool.title.scheduledBookings": "",
  "bookingTool.title.scheduleSuccess": "",
  "bookingTool.title.summary": "",
  "bookingTool.title.technicalSurvey": "",
  "bookingTool.title.videoSalesBooking": "",
  "common.aira.nothing": "",
  "common.country.DE": "Germania",
  "common.country.GB": "Regno Unito",
  "common.country.GB.SCT": "Scozia",
  "common.country.IT": "Italia",
  "common.deprecated": "",
  "common.error.contactDevelopmentTeam": "",
  "common.error.errorDownloadingSignedQuote": "",
  "common.error.installationProjectNotFound.title": "",
  "common.error.missing": "Mancante",
  "common.error.pageNotFound.description": "",
  "common.error.pageNotFound.title": "",
  "common.error.retry": "",
  "common.error.schedulingNotSupported": "",
  "common.error.unableToFindEnergySolution": "",
  "common.error.unknown": "",
  "common.file.browse.text": "",
  "common.file.drag&drop.text": "",
  "common.label.addItems": "",
  "common.label.address": "",
  "common.label.address.google": "",
  "common.label.address.loqate": "",
  "common.label.beta": "",
  "common.label.cancel": "",
  "common.label.catalogueRadiator": "",
  "common.label.clear": "",
  "common.label.comment": "",
  "common.label.confirm": "",
  "common.label.confirmed": "",
  "common.label.continue": "",
  "common.label.copyText": "",
  "common.label.copyURL": "",
  "common.label.date": "",
  "common.label.delete": "",
  "common.label.description": "",
  "common.label.discard": "",
  "common.label.download": "",
  "common.label.downloadPDF": "Scarica il PDF",
  "common.label.edit": "",
  "common.label.emailAddress": "",
  "common.label.fabric": "",
  "common.label.hlcDisplayName": "",
  "common.label.items": "",
  "common.label.label": "",
  "common.label.latest": "",
  "common.label.load": "",
  "common.label.loggedInAsEmail": "",
  "common.label.measurement.area": "",
  "common.label.measurement.height": "",
  "common.label.measurement.heightmm": "",
  "common.label.measurement.length": "",
  "common.label.measurement.lengthmm": "",
  "common.label.measurement.width": "",
  "common.label.measurement.widthmm": "",
  "common.label.name": "",
  "common.label.next": "Avanti",
  "common.label.none": "",
  "common.label.order": "",
  "common.label.phoneNumber": "",
  "common.label.preliminary": "",
  "common.label.radiators": "",
  "common.label.reload": "",
  "common.label.room": "Stanza",
  "common.label.save": "",
  "common.label.sendEmail": "",
  "common.label.set": "",
  "common.label.surveyor": "",
  "common.label.type": "",
  "common.label.unknown": "",
  "common.link.arViews": "",
  "common.link.assistant": "",
  "common.link.back": "",
  "common.link.baselineCalculator": "",
  "common.link.booking": "",
  "common.link.contact": "",
  "common.link.designTools": "",
  "common.link.heatDesign": "",
  "common.link.heatPumpConfig": "",
  "common.link.houseData": "",
  "common.link.installation": "",
  "common.link.installationHandover": "",
  "common.link.installationReport": "Rapporto",
  "common.link.installationReview": "",
  "common.link.invoicing": "",
  "common.link.next": "",
  "common.link.orderSummary": "",
  "common.link.print": "",
  "common.link.productSummary": "",
  "common.link.quotation": "",
  "common.link.quote": "",
  "common.link.salesSurvey": "",
  "common.link.service": "",
  "common.link.survey": "",
  "common.link.technicalSurvey": "",
  "common.link.videoCall": "",
  "common.link.videoSalesBooking": "",
  "common.max": "",
  "common.min": "",
  "common.minutes": "",
  "common.months": "",
  "common.no": "",
  "common.note.label": "",
  "common.notify.accessDenied": "",
  "common.notify.appealAccessDenied": "",
  "common.notify.copySuccess": "",
  "common.notify.error": "",
  "common.notify.invitationSent": "",
  "common.notify.loading": "",
  "common.notify.loadingContent": "",
  "common.notify.loadSuccess": "",
  "common.notify.saveSuccess": "",
  "common.notify.success": "",
  "common.notify.unsupportedArea": "",
  "common.pageOfTotal": "",
  "common.select.upload-file": "",
  "common.test.demo": "",
  "common.unlock": "",
  "common.years": "",
  "common.yes": "",
  "configuration.product.confirm": "",
  "configuration.product.description": "",
  "configuration.product.title": "",
  "contact.facility.createNew": "",
  "contact.facility.placeholder": "",
  "contact.houseData.cannotBeReused": "",
  "contact.houseData.facility": "",
  "contact.houseUnitSource.custom": "",
  "contact.houseUnitSource.guestHouse": "",
  "contact.houseUnitSource.leftWing": "",
  "contact.houseUnitSource.mainHouse": "",
  "contact.houseUnitSource.rightWing": "",
  "contact.houseUnitSource.standard": "",
  "contact.label.addHouseData": "",
  "contact.label.country": "",
  "contact.label.customerEmail": "",
  "contact.label.customerInformation": "",
  "contact.label.duplicateEmail": "",
  "contact.label.emailAddress": "",
  "contact.label.external.duplicateEmail": "",
  "contact.label.facility": "",
  "contact.label.firstName": "",
  "contact.label.hideHouseData": "",
  "contact.label.houseData": "",
  "contact.label.lastName": "",
  "contact.label.leadSource": "",
  "contact.label.number": "",
  "contact.label.region": "",
  "contact.label.required": "",
  "contact.label.submit": "",
  "contact.leadSource.canvasing": "",
  "contact.leadSource.other": "",
  "contact.leadSource.referral": "",
  "contact.leadSource.required": "",
  "contact.notification.addressAndFacilityUsed": "",
  "contact.notification.addressMultipleBuildings": "",
  "contact.notification.addressUsed": "",
  "contact.notification.canBeReused": "",
  "contact.notification.cannotBeReused": "",
  "contact.notification.enterValidEmail": "",
  "contact.notification.facilityAlreadyUsed": "",
  "contact.notification.facilityCanBeReused": "",
  "contact.notification.facilityDescription": "",
  "contact.notification.facilityInfo": "",
  "contact.notification.isAddressContainer": "",
  "contact.notification.unsupportedArea": "",
  "contact.notification.writeFacilityName": "",
  "contact.placeholder.newFacilityName": "",
  "dashboard.button.addLead": "",
  "dashboard.button.contact": "",
  "dashboard.button.installationProject": "",
  "dashboard.button.quotation": "",
  "dashboard.button.salesVisitMap": "",
  "dashboard.customerDetails": "",
  "dashboard.filter.accepted": "",
  "dashboard.filter.all": "",
  "dashboard.filter.notAccepted": "",
  "dashboard.filter.openForModification": "",
  "dashboard.filter.priceLocked": "",
  "dashboard.filter.productsLocked": "",
  "dashboard.filters.show": "",
  "dashboard.noOngoingWork": "",
  "dashboard.noOngoingWorkAccordingToFilters": "",
  "dashboard.solutionStatus.CANCELLED": "",
  "dashboard.solutionStatus.OPEN_FOR_MODIFICATION": "",
  "dashboard.solutionStatus.openForModification": "",
  "dashboard.solutionStatus.ORDERED": "",
  "dashboard.solutionStatus.PRICE_LOCKED": "",
  "dashboard.solutionStatus.priceLocked": "",
  "dashboard.solutionStatus.PRODUCTS_LOCKED": "",
  "dashboard.solutionStatus.productsLocked": "",
  "dashboard.solutionStatus.READY_FOR_ORDER": "",
  "dashboard.solutionStatus.readyForOrder": "",
  "dashboard.status": "",
  "dashboard.title.overview": "",
  "error.hlc.product.selection.incompatibleTankSize.description": "",
  "error.hlc.product.selection.incompatibleTankSize.heading": "",
  "error.hlc.unlock.invalidState": "",
  "error.support.card.askHelpText": "",
  "error.support.card.description": "",
  "error.support.card.description.lock": "",
  "error.support.card.description.save": "",
  "error.support.card.description.unlock": "",
  "error.support.card.hlc.locked.description": "",
  "error.support.card.hlc.locked.heading": "",
  "error.support.card.hlc.product.selection.description": "",
  "error.support.card.hlc.product.selection.heading": "",
  "error.support.card.hlc.sendDesignReviewEmail.description": "",
  "error.support.card.hlc.sendDesignReviewEmail.heading": "",
  "error.support.card.hlc.sendDesignReviewEmail.wrongState.description": "",
  "error.support.card.subTitle": "",
  "error.support.card.title": "",
  "error.support.card.title.lock": "",
  "error.support.card.title.save": "",
  "error.support.card.title.unlock": "",
  "floorPlans.error.noDwellingInProject": "",
  "floorPlans.error.noFloorsInProject": "",
  "floorPlans.symbols.bufferTank": "Serbatoio",
  "floorPlans.symbols.cylinder": "",
  "floorPlans.symbols.expansionVessel": "",
  "floorPlans.symbols.indoorUnit": "Unità interna",
  "floorPlans.symbols.outdoorUnit": "Unità esterna",
  "floorPlans.symbols.thermostat": "Termostato",
  "floorPlans.title.floorPlan": "",
  "form-redirect.manual-redirect.title": "Il reindirizzamento non funziona?",
  "form-redirect.status.aerospace.failure": "Impossibile caricare i dati Aerospace. Riprovare. Se il problema persiste, contattare l'assistenza.",
  "form-redirect.status.aerospace.header": "Caricamento dei dati Aerospace...",
  "form-redirect.status.aerospace.success": "Caricati con successo i dati Aerospace.",
  "form-redirect.status.magicplan.check.failure": "Errore durante la verifica del progetto Magicplan. Riprovare. Se il problema persiste, contattare l'assistenza.",
  "form-redirect.status.magicplan.check.header": "Controllo del progetto Magicplan...",
  "form-redirect.status.magicplan.check.incorrect": "Questo progetto Magicplan potrebbe non essere configurato correttamente.",
  "form-redirect.status.magicplan.check.success": "Il progetto Magicplan sembra buono. Reindirizzamento al progetto Magicplan...",
  "form-redirect.status.magicplan.create.failure": "Errore nella creazione del progetto Magicplan. Riprovare. Se il problema persiste, contattare l'assistenza.",
  "form-redirect.status.magicplan.create.header": "Creazione del progetto Magicplan...",
  "form-redirect.status.magicplan.create.notice": "Errore nella creazione del progetto Magicplan.\n\nIl rilevatore assegnato a questa indagine non dispone di un account Magicplan. Si prega di richiedere un account Magicplan tramite Aira Breeze → Digital workplace support → Software License Request e di riprovare quando l'utente dispone di un account.",
  "form-redirect.status.magicplan.create.success": "Creato un nuovo progetto Magicplan. Reindirizzamento al nuovo progetto Magicplan...",
  "form-redirect.status.magicplan.failure": "Errore nella ricerca del progetto Magicplan. Riprovare. Se il problema persiste, contattare l'assistenza.",
  "form-redirect.status.magicplan.header": "Trovare il progetto Magicplan...",
  "form-redirect.status.magicplan.not-found": "Non è stato trovato alcun progetto Magicplan per questo progetto Aerospace.",
  "form-redirect.status.magicplan.success": "Un progetto Magicplan è già collegato a questo progetto Aerospace. Reindirizzamento a Magicplan...",
  "form-redirect.status.magicplan.user-mismatch.advice": "Per poter effettuare un'indagine tecnica, è necessario accedere a Magicplan con lo stesso utente assegnato al progetto Magicplan.",
  "form-redirect.status.magicplan.user-mismatch.aerospace-user": "Ma sei connesso ad Aerospace come:",
  "form-redirect.status.magicplan.user-mismatch.alternative": "In alternativa, se si sa cosa fare, è possibile aprire il progetto Magicplan senza riassegnarlo:",
  "form-redirect.status.magicplan.user-mismatch.magicplan-user": "Il progetto Magicplan è attualmente assegnato a:",
  "form-redirect.status.magicplan.user-mismatch.reassign.failure": "Impossibile riassegnare il progetto Magicplan. Riprovare. Se il problema persiste, contattare l'assistenza.",
  "form-redirect.status.magicplan.user-mismatch.reassign.success": "Progetto Magicplan riassegnato con successo. Reindirizzamento al nuovo progetto Magicplan...",
  "form-redirect.title": "",
  "handoverSolution.tabs.externalLinks": "Documenti extra",
  "handoverSolution.tabs.floorPlan": "Pianta del piano",
  "handoverSolution.tabs.hardware": "Hardware",
  "handoverSolution.tabs.overview": "Panoramica",
  "handoverSolution.tabs.radiators": "Radiatori",
  "handoverSolution.tabs.schematics": "Schemi",
  "heatDesign.additionalLossTypes.exposedLocation": "",
  "heatDesign.additionalLossTypes.highCeiling": "",
  "heatDesign.additionalLossTypes.intermittentHeating": "",
  "heatDesign.additionalLossTypes.thermalBridging": "",
  "heatDesign.adjacentKind.heated": "",
  "heatDesign.adjacentKind.outside": "",
  "heatDesign.adjacentKind.room": "",
  "heatDesign.adjacentKind.soil": "",
  "heatDesign.adjacentKind.solidFloor": "",
  "heatDesign.adjacentKind.suspendedFloor": "",
  "heatDesign.adjacentKind.unheated": "",
  "heatDesign.billOfMaterial.summary.miscellaneousAddition": "",
  "heatDesign.billOfMaterials.heatDesignSummary.accessoriesTitle": "",
  "heatDesign.billOfMaterials.heatDesignSummary.appliancesTitle": "",
  "heatDesign.billOfMaterials.heatDesignSummary.customRadiatorTooltip": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model\n": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.quantity": "",
  "heatDesign.billOfMaterials.heatDesignSummary.summary": "",
  "heatDesign.billOfMaterials.heatDesignSummary.title": "",
  "heatDesign.billOfMaterials.heatDesignSummary.updatedWarning": "",
  "heatDesign.billOfMaterials.miscellaneousItemsTable.customItemTooltip": "",
  "heatDesign.billOfMaterials.notLockedWarning": "",
  "heatDesign.billOfMaterials.summary.cost": "",
  "heatDesign.billOfMaterials.summary.designedPrice": "",
  "heatDesign.billOfMaterials.summary.itemDeletedFromErp": "",
  "heatDesign.billOfMaterials.summary.itemsPerUnit": "",
  "heatDesign.billOfMaterials.summary.prefilledFromHeatDesign": "",
  "heatDesign.billOfMaterials.summary.salesPrice": "",
  "heatDesign.cancelRadiatorEdit.detail": "",
  "heatDesign.cancelRadiatorEdit.title": "",
  "heatDesign.climate.data.error": "",
  "heatDesign.climate.data.error.title": "",
  "heatDesign.common.erpId": "",
  "heatDesign.customerAcceptance.sendEmailConfirmation": "",
  "heatDesign.customerReport.button": "",
  "heatDesign.customerReport.calculationResults.flowTemperature": "La temperatura dell’acqua in uscita dalla pompa di calore verso il sistema di emissione.",
  "heatDesign.customerReport.calculationResults.heatLossColdestDay": "La dispersione di calore della tua casa e la potenza della tua pompa di calore alla temperatura più bassa prevista nella tua proprietà (<b>{temp} ⁰C</b>).",
  "heatDesign.customerReport.calculationResults.showerTime": "Goditi la tua doccia calda. Se tutta l'acqua calda è stata utilizzata, saranno necessari <b>{reheatTime}</b> minuti per riscaldare nuovamente il serbatoio.",
  "heatDesign.customerReport.calculationResults.title": "I risultati calcolati per la tua casa",
  "heatDesign.customerReport.calculationResults.ufhFlowTemperature": "",
  "heatDesign.customerReport.comment.label": "Commenti sull'installazione",
  "heatDesign.customerReport.comment.placeholder": "Es. Sulla base di cosa è stato scelta la posizione in cui installare la pompa di calore, cosa devi fare per permettere l’installazione delle unità interne, cosa rende speciale questa installazione, cosa è necessario tenere in considerazione...",
  "heatDesign.customerReport.comment.template.buttonTitle": "Usa il modello",
  "heatDesign.customerReport.comment.template.text": "L'unità esterna verrà posizionata...\n\nL'unità interna insieme al Puffer e agli altri componenti di impianto verranno posizionati...\n\nLa termoregolazione avverrà tramite il sistema AIRA che modulerà la temperatura di mandata in base alle reali esigenze dell'immobile...\n\nIl solare termico andrà in preriscaldo al nuovo bollitore per la produzione di ACS...\n\nRimangono a vostro carico le opere murarie per il passaggio delle tubazioni, la creazione del basamento, il box esterno coibentato come da nostre specifiche e tutte le opere non specificate nel presente preventivo...\n\nSe dovessimo montare un ripartitore di carichi per montaggio di PdC in monofase sopra 9kW aggiungere: \"Verrà installato un ripartitore dei carichi che rileverà la potenza istantanea prelevata dalla linea generale e darà priorità ai servizi primari (linea di casa esclusa la pompa di calore) spegnendo la pompa di calore in caso di superamento della soglia massima del contatore. Tale dispositivo riattiverà  in automatico la stessa, all'abbassamento della potenza prelevata. La continuità di servizio del riscaldamento verrà favorita dalla presenza dell'accumulo inerziale da cento litri\"...\n\nSe passaggio a trifase specificare che ci occuperemo del passaggio della nuova linea dal contatore, rilasceremo il progetto per l'adeguamento del sezionatore principale e, se possibile in base all'impianto esistente, bilanceremo i carichi sulle fasi.",
  "heatDesign.customerReport.floorPlans.disclaimer": "Nota: alcuni elementi potrebbero non essere stati aggiunti alla planimetria.",
  "heatDesign.customerReport.floorPlans.title": "Piantine",
  "heatDesign.customerReport.instructions": "Questo riepilogo può essere scaricato e inviato al cliente come contratto di installazione.",
  "heatDesign.customerReport.instructions.description": "Questi sono i risultati dei calcoli riguardanti le dispersioni di calore per il tuo immobile e della conferma dei prodotti proposti. Questo rapporto funge da accordo tra Aira e il cliente specificando i prodotti che verranno installati e il loro posizionamento all’interno abitazione, per evitare inutili modifiche durante l'installazione.",
  "heatDesign.customerReport.instructions.title": "Questo riepilogo può essere scaricato e inviato al cliente come contratto di installazione.",
  "heatDesign.customerReport.newRadiators.text": "",
  "heatDesign.customerReport.newRadiators.title": "Nuovi termosifoni da installare",
  "heatDesign.customerReport.outdoorUnits": "Unità {count, plural, one{esterna} other {esterne}}",
  "heatDesign.customerReport.preview.description": "",
  "heatDesign.customerReport.preview.embed.title": "",
  "heatDesign.customerReport.preview.refresh.button": "",
  "heatDesign.customerReport.preview.refresh.description": "",
  "heatDesign.customerReport.preview.title": "",
  "heatDesign.customerReport.preview.wrongState": "",
  "heatDesign.customerReport.subtitle": "Design del sistema",
  "heatDesign.customerReport.title": "",
  "heatDesign.customerReport.upcomingChanges.description": "La progettazione dell'impianto comprende tutto, dalle specifiche delle unità interne ed esterne al posizionamento dei radiatori. Non abbiate paura di approfondire i dettagli e fateci sapere se avete domande.",
  "heatDesign.customerReport.upcomingChanges.title": "Cosa faremo a casa tua?",
  "heatDesign.customRadiator": "",
  "heatDesign.deleteRadiator.detail": "",
  "heatDesign.deleteRadiator.title": "",
  "heatDesign.deleteUnderfloorHeating.detail": "",
  "heatDesign.deleteUnderfloorHeating.title": "",
  "heatDesign.development.resetProject": "",
  "heatDesign.discardChanges.detail": "",
  "heatDesign.discardChanges.title": "",
  "heatDesign.dwellingDefaultsModal.description": "",
  "heatDesign.dwellingDefaultsModal.title": "",
  "heatDesign.dwellingValidationModal.title": "",
  "heatDesign.emitterList.emitterType.radiator": "",
  "heatDesign.emitterList.emitterType.underfloor": "",
  "heatDesign.emitterList.noEmitters": "",
  "heatDesign.emitterList.summary.flowNeededForRadiators": "",
  "heatDesign.emitterList.summary.flowNeededForUnderfloorHeating": "",
  "heatDesign.emitterList.summary.radiatorFlowTemperature": "",
  "heatDesign.emitterList.summary.radiatorReturnTemperature": "",
  "heatDesign.emitterList.summary.totalFlowNeeded": "",
  "heatDesign.emitterList.summary.totalFlowNeededForHouse": "",
  "heatDesign.emitterList.summary.underfloorHeatingFlowTemperature": "",
  "heatDesign.emitterList.summary.underfloorHeatingReturnTemperature": "",
  "heatDesign.emitterList.underfloorHeatingDisclaimer": "",
  "heatDesign.emitterReport.calculationResults.circuitDeltaT": "",
  "heatDesign.emitterReport.calculationResults.emitterAverageTemperature": "",
  "heatDesign.emitterReport.calculationResults.flowTemperature": "",
  "heatDesign.emitterReport.calculationResults.returnTemperature": "",
  "heatDesign.emitterReport.tab.title": "",
  "heatDesign.emitterReport.title": "",
  "heatDesign.emitterReport.upcomingChanges.description": "",
  "heatDesign.emittersValidationModal.body": "",
  "heatDesign.emittersValidationModal.title": "",
  "heatDesign.error.application.description": "",
  "heatDesign.error.application.title": "",
  "heatDesign.error.cannotRetrieveMagicplanProject": "",
  "heatDesign.error.checkMagicplanProjectNotDeleted": "",
  "heatDesign.error.dwellingAddress": "",
  "heatDesign.error.magicplan.errorRetrievingProject": "",
  "heatDesign.error.noFormFound": "",
  "heatDesign.error.noHouseData.description": "",
  "heatDesign.error.noHouseData.details": "",
  "heatDesign.error.noHouseData.title": "",
  "heatDesign.error.noMagicplanSubmission": "",
  "heatDesign.error.noMagicplanSubmission.howTo": "",
  "heatDesign.error.noMagicplanSubmission.latestSurvey": "",
  "heatDesign.error.noMagicplanSubmission.title": "",
  "heatDesign.error.noRoomsOnFloor": "",
  "heatDesign.error.unableToLoadMagicplanProject": "",
  "heatDesign.existingRadiator": "",
  "heatDesign.floorDefaultsModal.description": "",
  "heatDesign.floorDefaultsModal.generalSection.title": "",
  "heatDesign.floorDefaultsModal.soilInput.label": "",
  "heatDesign.floorDefaultsModal.soilInput.tooltip": "",
  "heatDesign.floorDefaultsModal.title": "",
  "heatDesign.floorOverviewValidationModal.body": "",
  "heatDesign.floorOverviewValidationModal.invalidFloors": "",
  "heatDesign.floorOverviewValidationModal.invalidRooms": "",
  "heatDesign.flowRate": "",
  "heatDesign.helper.floorPlan": "",
  "heatDesign.helpModal.faq.body": "",
  "heatDesign.helpModal.faq.button": "",
  "heatDesign.helpModal.faq.title": "",
  "heatDesign.helpModal.request.body": "",
  "heatDesign.helpModal.request.title": "",
  "heatDesign.isCustomTooltip": "",
  "heatDesign.keep": "",
  "heatDesign.lock.confirmation": "Do you want to lock the design? If it's the first time locking the design this will send an e-mail to the customer with the Design Report.",
  "heatDesign.lock.confirmation.designReview": "Do you want to lock the design?",
  "heatDesign.lock.confirmation.sendDesignReviewEmail": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.accepted": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.not-ready": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.ready": "",
  "heatDesign.magicplan.advanced": "",
  "heatDesign.magicplan.heading": "",
  "heatDesign.magicplan.links.mobile": "",
  "heatDesign.magicplan.links.title": "Link a questo progetto Magicplan:",
  "heatDesign.magicplan.links.web": "",
  "heatDesign.magicplan.merge.buttonLabel": "",
  "heatDesign.magicplan.merge.buttonLabel.loading": "",
  "heatDesign.magicplan.merge.description": "",
  "heatDesign.magicplan.merge.error.card.description": "",
  "heatDesign.magicplan.merge.error.card.description.duplicates": "",
  "heatDesign.magicplan.merge.error.card.title": "",
  "heatDesign.magicplan.merge.locked": "",
  "heatDesign.magicplan.merge.modal.body": "",
  "heatDesign.magicplan.merge.modal.title": "",
  "heatDesign.magicplan.merge.success": "",
  "heatDesign.magicplan.merge.unsaved": "",
  "heatDesign.magicplan.reassign.buttonLabel": "Riassegnare il progetto a me",
  "heatDesign.magicplan.reassign.buttonLabel.loading": "Riassegnare...",
  "heatDesign.magicplan.reassign.description": "",
  "heatDesign.magicplan.reassign.error.card.description": "",
  "heatDesign.magicplan.reassign.error.card.title": "",
  "heatDesign.magicplan.reassign.success": "",
  "heatDesign.magicplan.reimport.buttonLabel": "",
  "heatDesign.magicplan.reimport.buttonLabel.loading": "",
  "heatDesign.magicplan.reimport.description": "",
  "heatDesign.magicplan.reimport.error.card.description": "",
  "heatDesign.magicplan.reimport.error.card.title": "",
  "heatDesign.magicplan.reimport.locked": "",
  "heatDesign.magicplan.reimport.modal.body": "",
  "heatDesign.magicplan.reimport.modal.title": "",
  "heatDesign.magicplan.reimport.success": "",
  "heatDesign.newEmitterDeltaT": "",
  "heatDesign.newRadiator": "",
  "heatDesign.newRadiator.NewRadiators": "",
  "heatDesign.notify.designReviewEmailSent": "",
  "heatDesign.notify.lockingProject": "",
  "heatDesign.notify.projectLocked": "",
  "heatDesign.notify.projectUnlocked": "",
  "heatDesign.notify.sendingDesignReviewEmail": "",
  "heatDesign.notify.unlockingProject": "",
  "heatDesign.procurement.aik": "",
  "heatDesign.procurement.aikItemError": "",
  "heatDesign.procurement.bom": "",
  "heatDesign.procurement.customItemError": "",
  "heatDesign.procurement.deleteEquivalenceGroup.description": "",
  "heatDesign.procurement.deleteEquivalenceGroup.title": "",
  "heatDesign.procurement.duplicateItemError": "",
  "heatDesign.procurement.itemName": "",
  "heatDesign.procurement.quantity": "",
  "heatDesign.procurement.quantity.tooltip": "",
  "heatDesign.procurement.removedFromErpError": "",
  "heatDesign.procurement.vanStock": "",
  "heatDesign.productSelection.heatDetails.calculatedTotalHeatLoss": "",
  "heatDesign.productSelection.heatDetails.chosenFlowTemp": "",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp": "",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.bivalencePoint.heatOutput.without.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.with.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.without.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.temperature": "",
  "heatDesign.productSelection.heatDetails.title": "",
  "heatDesign.productSelection.heatPumpPackageSelection": "",
  "heatDesign.productSelection.heatPumpPackageSelection.designLocked": "",
  "heatDesign.productSelection.heatPumpPackageSelection.productsLocked": "",
  "heatDesign.productSelection.indoorUnitDetails.cylinderReheatTime": "",
  "heatDesign.productSelection.indoorUnitDetails.houseWaterSection.title": "",
  "heatDesign.productSelection.indoorUnitDetails.title": "",
  "heatDesign.productSelection.noNewRadiators": "",
  "heatDesign.productSelection.noPerformanceData": "",
  "heatDesign.productSelection.outdoorUnitDetails.bivalencePoint": "",
  "heatDesign.productSelection.outdoorUnitDetails.bivalenceSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.heatOutput.with.electric": "",
  "heatDesign.productSelection.outdoorUnitDetails.houseHeatingSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutput": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithElectric": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithoutElectric": "",
  "heatDesign.productSelection.outdoorUnitDetails.performanceODTSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.scop": "",
  "heatDesign.productSelection.outdoorUnitDetails.scop.description": "",
  "heatDesign.productSelection.outdoorUnitDetails.title": "",
  "heatDesign.productSelection.radiators.showRadiatorsButton": "",
  "heatDesign.productSelection.radiators.title": "",
  "heatDesign.productSelection.radiators.tooltip": "",
  "heatDesign.productSelection.radiators.total": "",
  "heatDesign.productSelection.radiatorsList.groupHeader": "",
  "heatDesign.productSelection.radiatorsList.groupHeader.v2": "",
  "heatDesign.productSelection.radiatorsList.noRadiators": "",
  "heatDesign.productSelection.radiatorsList.title": "",
  "heatDesign.productSelection.showerTime": "",
  "heatDesign.productSelection.unlockProducts.error.body": "",
  "heatDesign.productSelection.unlockProducts.error.title": "",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity": "",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity.tooltip": "",
  "heatDesign.productSelection.waterUsage.numBathrooms": "",
  "heatDesign.productSelection.waterUsage.numBedrooms": "",
  "heatDesign.productSelection.waterUsage.numOccupants": "",
  "heatDesign.propertyDetails.ACPHDefault": "",
  "heatDesign.propertyDetails.AddressTemperatureData": "",
  "heatDesign.propertyDetails.altitude.label": "",
  "heatDesign.propertyDetails.altitude.tooltip": "",
  "heatDesign.propertyDetails.bathrooms": "",
  "heatDesign.propertyDetails.bedrooms": "",
  "heatDesign.propertyDetails.climateZone": "",
  "heatDesign.propertyDetails.DegreeDays": "",
  "heatDesign.propertyDetails.ExternalTempForLocation": "",
  "heatDesign.propertyDetails.floors": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.description": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.disabled": "",
  "heatDesign.propertyDetails.MeanAirTemp": "",
  "heatDesign.propertyDetails.noStreetView": "",
  "heatDesign.propertyDetails.NumberOfBathrooms": "",
  "heatDesign.propertyDetails.NumberOfBathrooms.tooltip": "",
  "heatDesign.propertyDetails.NumberOfBedrooms": "",
  "heatDesign.propertyDetails.NumberOfBedrooms.tooltip": "",
  "heatDesign.propertyDetails.NumberOfFloors": "",
  "heatDesign.propertyDetails.NumberOfOccupants": "",
  "heatDesign.propertyDetails.occupants": "",
  "heatDesign.propertyDetails.OutsideTemp": "",
  "heatDesign.propertyDetails.temperatureCompensated": "",
  "heatDesign.propertyDetails.TemperatureCompensation": "",
  "heatDesign.propertyDetails.TemperatureCompensation.description": "",
  "heatDesign.propertyDetails.title": "",
  "heatDesign.propertyDetails.updateUValuesModal.cancelButton": "",
  "heatDesign.propertyDetails.updateUValuesModal.confirmButton": "",
  "heatDesign.propertyDetails.updateUValuesModal.currentUValueHeader": "",
  "heatDesign.propertyDetails.updateUValuesModal.helper": "",
  "heatDesign.propertyDetails.updateUValuesModal.suggestedUValueHeader": "",
  "heatDesign.propertyDetails.updateUValuesModal.title": "",
  "heatDesign.propertyDetails.YearBuilt": "",
  "heatDesign.propertyOverview.ventilation.acphDefaultsTooltip": "",
  "heatDesign.propertyOverview.ventilation.airPermeabilityTooltip": "",
  "heatDesign.propertyOverview.ventilation.calculationMethodTooltip": "",
  "heatDesign.propertyOverview.ventilation.exposureTooltip": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bathroom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bedroom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.diningRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.dressingRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.gamesRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.hall": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.kitchen": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.landing": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.livingRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.loungeRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.other": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.storeRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.study": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.toilet": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.utilityRoom": "",
  "heatDesign.radiator.FlowReturnDeltaT": "",
  "heatDesign.radiator.FlowTemperature": "",
  "heatDesign.radiator.heatOutput.totals.meanWaterAirTransferInvalid": "",
  "heatDesign.radiator.heatOutput.totals.sum": "",
  "heatDesign.radiator.heatOutput.totals.title": "",
  "heatDesign.radiator.MeanWaterTemperature": "",
  "heatDesign.radiator.ReturnTemperature": "",
  "heatDesign.radiatorModal.browse": "",
  "heatDesign.radiatorModal.catalogue.brand": "",
  "heatDesign.radiatorModal.catalogue.category": "",
  "heatDesign.radiatorModal.catalogue.tab.radiators": "",
  "heatDesign.radiatorModal.catalogue.title": "",
  "heatDesign.radiatorModal.catalogue.type": "",
  "heatDesign.radiatorModal.discard": "",
  "heatDesign.radiatorModal.emitterPanel.description": "",
  "heatDesign.radiatorModal.emitterPanel.title.new": "",
  "heatDesign.radiatorModal.emitterPanel.title.replace": "",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.label": "",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.tooltip": "",
  "heatDesign.radiatorModal.heatBalance.title": "",
  "heatDesign.radiatorModal.heatBalance.tooltip": "",
  "heatDesign.radiatorModal.noRadiators": "",
  "heatDesign.radiatorModal.removedFromErp": "",
  "heatDesign.radiatorModal.select": "",
  "heatDesign.radiatorModal.shared.all": "",
  "heatDesign.radiatorModal.shared.calculatedOutput.dynamic": "",
  "heatDesign.radiatorModal.shared.calculatedOutput.static": "",
  "heatDesign.radiatorModal.shared.catalog": "",
  "heatDesign.radiatorModal.shared.columns": "",
  "heatDesign.radiatorModal.shared.height": "",
  "heatDesign.radiatorModal.shared.image": "",
  "heatDesign.radiatorModal.shared.length": "",
  "heatDesign.radiatorModal.shared.material": "",
  "heatDesign.radiatorModal.shared.model": "",
  "heatDesign.radiatorModal.shared.search": "",
  "heatDesign.radiatorModal.shared.type": "",
  "heatDesign.radiatorModal.table.details": "",
  "heatDesign.radiatorModal.table.factor": "",
  "heatDesign.radiatorModal.table.nominalOutput.detailed": "",
  "heatDesign.radiatorModal.table.nominalOutput.generic": "",
  "heatDesign.radiatorModal.table.weight": "",
  "heatDesign.radiatorModal.title.add": "",
  "heatDesign.radiatorModal.title.replace": "",
  "heatDesign.radiatorRenderer.AddRadiator": "",
  "heatDesign.radiatorRenderer.AddRadiatorDetail": "",
  "heatDesign.radiatorRenderer.AddUnderfloorHeating": "",
  "heatDesign.radiatorRenderer.DeltaT": "",
  "heatDesign.radiatorRenderer.emitterTempAndRoomDiff": "",
  "heatDesign.radiatorRenderer.footer.HeatOutput": "",
  "heatDesign.radiatorRenderer.footer.ReturnTemp": "",
  "heatDesign.radiatorRenderer.footer.TotalRoomHeatLoss": "",
  "heatDesign.radiatorRenderer.footer.WattDifference": "",
  "heatDesign.radiatorRenderer.header.DeltaT": "",
  "heatDesign.radiatorRenderer.header.FlowTemp": "",
  "heatDesign.radiatorRenderer.header.MeanRadiatorTemp": "",
  "heatDesign.radiatorRenderer.header.ReturnTemp": "",
  "heatDesign.radiatorRenderer.RemoveRadiator": "",
  "heatDesign.radiatorRenderer.RoomTemp": "",
  "heatDesign.radiators.calculatedOutputTooltip": "",
  "heatDesign.radiators.conversionFactor": "",
  "heatDesign.radiators.externalVendorId": "",
  "heatDesign.radiators.manufacturerId": "",
  "heatDesign.radiators.waterContent": "",
  "heatDesign.radiatorSizeCategory.design": "",
  "heatDesign.radiatorSizeCategory.large": "",
  "heatDesign.radiatorSizeCategory.standard": "",
  "heatDesign.radiatorsOverview.emitterList.title": "",
  "heatDesign.radiatorsOverview.floorOverview.title": "",
  "heatDesign.radiatorTable.addExisting": "",
  "heatDesign.radiatorTable.addNew": "",
  "heatDesign.radiatorTable.calculated.output": "",
  "heatDesign.radiatorTable.description": "",
  "heatDesign.radiatorTable.ExistingRadiators": "",
  "heatDesign.radiatorTable.flowRateAdjustment.cannotAdjustElectricRadiators": "",
  "heatDesign.radiatorTable.flowRateAdjustment.flowReturnTempLabel": "",
  "heatDesign.radiatorTable.flowRateAdjustment.label": "",
  "heatDesign.radiatorTable.meanWaterTemp": "",
  "heatDesign.radiatorTable.meanWaterTemp.tooltip": "",
  "heatDesign.radiatorTable.meanWaterTempPerRadiator": "",
  "heatDesign.radiatorTable.nominalOutput.deltaT": "ΔT (⁰C)",
  "heatDesign.radiatorTable.nominalOutput.title": "",
  "heatDesign.radiatorTable.nominalOutput.watts": "",
  "heatDesign.radiatorTable.replacedBy": "",
  "heatDesign.radiatorTable.replaces": "",
  "heatDesign.radiatorTable.temperatureAdjustment.title": "",
  "heatDesign.radiatorTable.TotalOutputOfAllEnabledEmittersWatts": "",
  "heatDesign.radiatorTable.underfloorHeating.matchRoomHeatLossLabel": "",
  "heatDesign.radiatorTable.underfloorHeating.output": "",
  "heatDesign.radiatorTable.underfloorHeating.tooltip": "",
  "heatDesign.radiatorTable.Use": "",
  "heatDesign.replaceRadiator": "",
  "heatDesign.report.clause.effectiveAirChangesPerHour": "",
  "heatDesign.report.dwellingData.climateData.adjustedExternalTemp": "",
  "heatDesign.report.dwellingData.climateData.degreeDays": "",
  "heatDesign.report.dwellingData.climateData.meanAirTemp": "",
  "heatDesign.report.dwellingData.climateData.outsideTemp": "",
  "heatDesign.report.dwellingData.climateData.temperatureAdjustmentExposedLocation": "",
  "heatDesign.report.dwellingData.climateData.temperatureCompensation": "",
  "heatDesign.report.dwellingData.climateData.title": "",
  "heatDesign.report.dwellingData.energyDemand.dailyHotWaterEnergyDemand": "",
  "heatDesign.report.dwellingData.energyDemand.fabricEnergyDemand": "",
  "heatDesign.report.dwellingData.energyDemand.hotWaterEnergyDemand": "",
  "heatDesign.report.dwellingData.energyDemand.title": "",
  "heatDesign.report.dwellingData.energyDemand.totalEnergyDemand": "",
  "heatDesign.report.dwellingData.energyDemand.ventilationEnergyDemand": "",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowDeltaT": "",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowTemp": "",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.title": "",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.totalHeatEmittance": "",
  "heatDesign.report.dwellingData.generalInfo.address": "",
  "heatDesign.report.dwellingData.generalInfo.customerName": "",
  "heatDesign.report.dwellingData.generalInfo.numBedrooms": "",
  "heatDesign.report.dwellingData.generalInfo.numFloors": "",
  "heatDesign.report.dwellingData.generalInfo.numOccupants": "",
  "heatDesign.report.dwellingData.generalInfo.title": "",
  "heatDesign.report.dwellingData.generalInfo.totalHousingArea": "",
  "heatDesign.report.dwellingData.generalInfo.yearBuilt": "",
  "heatDesign.report.dwellingData.heatLoss.fabricHeatLoss": "",
  "heatDesign.report.dwellingData.heatLoss.fabricTitle": "",
  "heatDesign.report.dwellingData.heatLoss.title": "",
  "heatDesign.report.dwellingData.heatLoss.totalHeatLoss": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphCustom": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphDefaultsUsed": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.airPermeability": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposedFacadesFactor": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposureCoefficient": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.simple": "",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.standard": "",
  "heatDesign.report.dwellingData.heatLoss.ventilationHeatLoss": "",
  "heatDesign.report.dwellingData.heatLoss.ventilationTitle": "",
  "heatDesign.report.dwellingData.indoorUnitDetails.reheatTime": "",
  "heatDesign.report.dwellingData.indoorUnitDetails.selectedIndoorUnit": "",
  "heatDesign.report.dwellingData.indoorUnitDetails.title": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.bivalencePoint": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.capacity": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.maxHeatOutput": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.scop": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.selectedOutdoorUnit": "",
  "heatDesign.report.dwellingData.outdoorUnitDetails.title": "",
  "heatDesign.report.dwellingData.title": "",
  "heatDesign.report.externalWallAdditionalSurfaceArea": "",
  "heatDesign.report.floorDetails.generalInfo.numberOfRooms": "",
  "heatDesign.report.floorDetails.generalInfo.title": "",
  "heatDesign.report.floorDetails.generalInfo.totalArea": "",
  "heatDesign.report.floorDetails.generalInfo.totalHeatEmittance": "",
  "heatDesign.report.floorDetails.generalInfo.totalHeatLoss": "",
  "heatDesign.report.floorDetails.rooms.title": "",
  "heatDesign.report.instructions": "",
  "heatDesign.report.instructions.description": "",
  "heatDesign.report.instructions.title": "",
  "heatDesign.report.pdf.footer.generatedAt": "",
  "heatDesign.report.pdf.footer.generatedAtBy": "",
  "heatDesign.report.pdf.locked": "",
  "heatDesign.report.pdf.noReports": "",
  "heatDesign.report.pdf.preview": "",
  "heatDesign.report.pdf.select": "",
  "heatDesign.report.roomDetails.avgRoomHeight": "",
  "heatDesign.report.roomDetails.designRoomTemp": "",
  "heatDesign.report.roomDetails.emitterDetails.calculatedOutput": "",
  "heatDesign.report.roomDetails.emitterDetails.description": "Descrizione [altezza x larghezza]",
  "heatDesign.report.roomDetails.emitterDetails.missingEmitters": "",
  "heatDesign.report.roomDetails.emitterDetails.nominalOutput": "",
  "heatDesign.report.roomDetails.emitterDetails.note": "",
  "heatDesign.report.roomDetails.emitterDetails.status": "",
  "heatDesign.report.roomDetails.emitterDetails.title": "",
  "heatDesign.report.roomDetails.emitterDetails.type": "",
  "heatDesign.report.roomDetails.fabricDetails.area": "",
  "heatDesign.report.roomDetails.fabricDetails.heatLoss": "",
  "heatDesign.report.roomDetails.fabricDetails.opposingTemperature": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.doors": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.externalWalls": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.floors": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.internalWalls": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.partyWalls": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofGlazings": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofsOrCeilings": "",
  "heatDesign.report.roomDetails.fabricDetails.surface.windows": "",
  "heatDesign.report.roomDetails.fabricDetails.surfaceType": "",
  "heatDesign.report.roomDetails.fabricDetails.title": "",
  "heatDesign.report.roomDetails.fabricDetails.uValue": "",
  "heatDesign.report.roomDetails.fabricHeatLoss": "",
  "heatDesign.report.roomDetails.heatFlux": "",
  "heatDesign.report.roomDetails.otherEmitters.title": "",
  "heatDesign.report.roomDetails.roomArea": "",
  "heatDesign.report.roomDetails.roomType": "",
  "heatDesign.report.roomDetails.total": "",
  "heatDesign.report.roomDetails.totalHeatEmittance": "",
  "heatDesign.report.roomDetails.totalHeatLoss": "",
  "heatDesign.report.roomDetails.underfloorHeating.output": "",
  "heatDesign.report.roomDetails.underfloorHeating.title": "",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour": "",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.simple": "",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.standard": "",
  "heatDesign.report.roomDetails.ventilationDetails.deltaT": "",
  "heatDesign.report.roomDetails.ventilationDetails.externalEnvelopeArea": "Superficie delle superfici esterne (m²)",
  "heatDesign.report.roomDetails.ventilationDetails.heatLoss": "",
  "heatDesign.report.roomDetails.ventilationDetails.title": "",
  "heatDesign.report.roomDetails.ventilationDetails.totalVolume": "",
  "heatDesign.report.roomDetails.ventilationHeatLoss": "",
  "heatDesign.report.thermalBridgingIncludedInHeatLoss": "",
  "heatDesign.reportInstructions.emitterReport.description": "",
  "heatDesign.reportInstructions.emitterReport.title": "",
  "heatDesign.resultSnapshotBanner.description": "",
  "heatDesign.resultSnapshotBanner.designReviewState.accepted": "",
  "heatDesign.resultSnapshotBanner.designReviewState.not-ready": "",
  "heatDesign.resultSnapshotBanner.designReviewState.ready": "",
  "heatDesign.resultSnapshotBanner.flowTemperature": "",
  "heatDesign.resultSnapshotBanner.houseHeatLoss": "",
  "heatDesign.resultSnapshotBanner.lockedAt": "",
  "heatDesign.resultSnapshotBanner.title": "",
  "heatDesign.resultSnapshotBanner.totalEmitterOutput": "",
  "heatDesign.returnTemperatureTooltip": "",
  "heatDesign.RoofGlazingsRenderer.addRoofGlazing": "",
  "heatDesign.RoofGlazingsRenderer.UVAlueAngleCompensation": "",
  "heatDesign.room.averageCeilingHeight": "",
  "heatDesign.room.avgAirChangesPerHour": "",
  "heatDesign.room.avgAirChangesPerHour.disabledByPulseTest": "",
  "heatDesign.room.avgAirChangesPerHour.helperText.custom": "",
  "heatDesign.room.avgAirChangesPerHour.helperText.standardized": "",
  "heatDesign.room.avgAirChangesPerHourHelperText": "",
  "heatDesign.room.avgAirChangesPerHourSuffix": "",
  "heatDesign.room.belowFloorLabel.heatedRoom": "",
  "heatDesign.room.belowFloorLabel.solidFloor": "",
  "heatDesign.room.belowFloorLabel.suspendedFloor": "",
  "heatDesign.room.belowFloorLabel.unheatedRoom": "",
  "heatDesign.room.designRoomTemp": "",
  "heatDesign.room.designRoomUnheated": "",
  "heatDesign.room.heatedRoom": "",
  "heatDesign.room.heatedRoom.tooltip": "",
  "heatDesign.room.openFlue": "",
  "heatDesign.room.openFlue.disabledByPulseTest": "",
  "heatDesign.room.referenceAirChangesPerHour": "",
  "heatDesign.room.referenceAirChangesPerHourTooltip": "",
  "heatDesign.room.roomName": "",
  "heatDesign.room.roomType": "",
  "heatDesign.room.tempOfSpaceAbove": "",
  "heatDesign.room.typeOfSpaceAbove": "",
  "heatDesign.room.whatIsBelowTheFloor": "",
  "heatDesign.roomDetails.belowFloor.tooltip": "",
  "heatDesign.roomEditor.internalDoorsAndWindowsNotIncluded": "",
  "heatDesign.roomOutput.helperText": "",
  "heatDesign.roomRenderer.selectASurface": "",
  "heatDesign.roomSurfaceTypes.ceilings": "",
  "heatDesign.roomSurfaceTypes.doors": "",
  "heatDesign.roomSurfaceTypes.externalDoors": "",
  "heatDesign.roomSurfaceTypes.externalWalls": "",
  "heatDesign.roomSurfaceTypes.externalWindows": "",
  "heatDesign.roomSurfaceTypes.floors": "",
  "heatDesign.roomSurfaceTypes.foundation": "",
  "heatDesign.roomSurfaceTypes.intermediateFloors": "",
  "heatDesign.roomSurfaceTypes.internalWalls": "",
  "heatDesign.roomSurfaceTypes.partyWalls": "",
  "heatDesign.roomSurfaceTypes.roof": "",
  "heatDesign.roomSurfaceTypes.roofGlazings": "",
  "heatDesign.roomSurfaceTypes.roofs": "",
  "heatDesign.roomSurfaceTypes.roofsOrCeilings": "",
  "heatDesign.roomSurfaceTypes.windows": "",
  "heatDesign.roomType.bathroom": "",
  "heatDesign.roomType.bedroom": "",
  "heatDesign.roomType.hallway": "",
  "heatDesign.roomType.kitchen": "",
  "heatDesign.roomType.livingRoom": "",
  "heatDesign.roomType.other": "",
  "heatDesign.roomValidationModal.title": "",
  "heatDesign.sidebar.title": "",
  "heatDesign.surfaces.door": "",
  "heatDesign.surfaces.doors": "",
  "heatDesign.surfaces.externalDoor": "",
  "heatDesign.surfaces.externalDoors": "",
  "heatDesign.surfaces.externalWall": "",
  "heatDesign.surfaces.externalWalls": "",
  "heatDesign.surfaces.externalWindow": "",
  "heatDesign.surfaces.externalWindows": "",
  "heatDesign.surfaces.floor": "",
  "heatDesign.surfaces.floors": "",
  "heatDesign.surfaces.foundation": "",
  "heatDesign.surfaces.intermediateFloors": "",
  "heatDesign.surfaces.internalWall": "",
  "heatDesign.surfaces.internalWalls": "",
  "heatDesign.surfaces.partyWall": "",
  "heatDesign.surfaces.partyWalls": "",
  "heatDesign.surfaces.roof": "",
  "heatDesign.surfaces.roofGlazing": "",
  "heatDesign.surfaces.roofGlazings": "",
  "heatDesign.surfaces.roofsOrCeilings": "",
  "heatDesign.surfaces.wall": "",
  "heatDesign.surfaces.window": "",
  "heatDesign.surfaces.windows": "",
  "heatDesign.surveyForms.download.notSubmitted": "",
  "heatDesign.surveyForms.download.section.description": "",
  "heatDesign.tableHeaders.annualAdditionalEnergyDemand": "",
  "heatDesign.tableHeaders.annualFabricEnergyDemand": "",
  "heatDesign.tableHeaders.annualHeatingEnergyDemand": "",
  "heatDesign.tableHeaders.annualVentilationEnergyDemand": "",
  "heatDesign.tableHeaders.annualWaterEnergyDemand": "",
  "heatDesign.tableHeaders.ceilingHeight": "",
  "heatDesign.tableHeaders.dailyWaterEnergyDemand": "",
  "heatDesign.tableHeaders.effectiveAirChangesPerHour": "",
  "heatDesign.tableHeaders.emitterDeltaT": "",
  "heatDesign.tableHeaders.emitterDescription": "",
  "heatDesign.tableHeaders.energyDemand": "",
  "heatDesign.tableHeaders.existing-new": "",
  "heatDesign.tableHeaders.floorArea": "",
  "heatDesign.tableHeaders.flowRate": "",
  "heatDesign.tableHeaders.heatLossSummary": "",
  "heatDesign.tableHeaders.HouseAcph": "",
  "heatDesign.tableHeaders.OutputWatt": "",
  "heatDesign.tableHeaders.roomHeatLoss": "",
  "heatDesign.tableHeaders.roomName": "",
  "heatDesign.tableHeaders.TotalAdditionalHeatLoss": "",
  "heatDesign.tableHeaders.totalAnnualEnergyDemand": "",
  "heatDesign.tableHeaders.TotalFabricHeatLoss": "",
  "heatDesign.tableHeaders.TotalHeatLoss": "",
  "heatDesign.tableHeaders.TotalVentilationHeatLoss": "",
  "heatDesign.tableHeaders.value": "",
  "heatDesign.tableHeaders.ventilationHeatLoss": "",
  "heatDesign.tableHeaders.wattPerSqM": "",
  "heatDesign.technicalReport.title": "",
  "heatDesign.title.dropdownAlternativeSelect": "",
  "heatDesign.title.dwellingDefaultValues": "",
  "heatDesign.title.floorLevelDefaultValues": "",
  "heatDesign.title.floorOverview": "Dettaglio piani",
  "heatDesign.title.heatLossOverview": "Risultato calcolo dispersioni",
  "heatDesign.title.heatPumpConfig": "Configurazione pompa di calore",
  "heatDesign.title.productSelection": "Selezione prodotti",
  "heatDesign.title.propertyDetails": "Dettagli casa",
  "heatDesign.title.pulseTestAirPermeability": "",
  "heatDesign.title.radiatorsOverview": "Dimensionamento sist. di emissione",
  "heatDesign.title.resultsExport": "Riepilogo & risultati",
  "heatDesign.title.selectASurface": "",
  "heatDesign.title.surfaceEditor": "",
  "heatDesign.title.totalArea": "",
  "heatDesign.title.totalVolume": "",
  "heatDesign.underfloorHeating.editTitle": "",
  "heatDesign.underFloorHeating.UnderfloorHeating": "",
  "heatDesign.unlock.confirmation": "",
  "heatDesign.usingDefaultUValue.dwelling": "",
  "heatDesign.usingDefaultUValue.floorLevel": "",
  "heatDesign.usingDefaultUValue.floorLevelAbove": "",
  "heatDesign.uValues.addCustomUValue": "",
  "heatDesign.uValues.addCustomUValue.save": "",
  "heatDesign.uValues.name": "",
  "heatDesign.uValues.placeholder": "",
  "heatDesign.uValues.placeholder.noFabricTypeInProject": "",
  "heatDesign.uValues.placeholder.setAtDwelling": "",
  "heatDesign.uValues.uValue": "",
  "heatDesign.uValues.uValues": "",
  "heatDesign.uValues.valueLabel": "",
  "heatDesign.uValues.worseThanBuildingRegulations": "",
  "heatDesign.uValues.worseThanBuildingRegulations.v2": "",
  "heatDesign.validationModal.customValue.missingOrInvalid": "",
  "heatDesign.validationModal.missingOrInvalid": "",
  "heatDesign.ventilation.acphDefaults": "",
  "heatDesign.ventilation.acphDefaultsDisabledPulseTest": "",
  "heatDesign.ventilation.airPermeability": "",
  "heatDesign.ventilation.calculationMethod": "",
  "heatDesign.ventilation.exposureOfHouse": "",
  "heatDesign.ventilation.houseWideAcph": "",
  "heatDesign.ventilation.houseWideAcphTooltip": "",
  "heatDesign.ventilation.roomType": "",
  "heatDesign.ventilation.ventilation": "",
  "heatDesign.wallsRenderer.adjoiningRoom.heated": "",
  "heatDesign.wallsRenderer.adjoiningRoom.roomNameLabel": "",
  "heatDesign.wallsRenderer.adjoiningRoom.tempLabel": "",
  "heatDesign.wallsRenderer.adjoiningRoom.title": "",
  "heatDesign.wallsRenderer.adjoiningRoom.unheated": "",
  "heatDesign.wallsRenderer.area": "",
  "heatDesign.wallsRenderer.area.tooltip": "",
  "heatDesign.wallsRenderer.length": "",
  "heatDesign.wallsRenderer.soilPercentage.label": "",
  "heatDesign.wallsRenderer.soilPercentage.resetToDefaults": "",
  "heatDesign.wallsRenderer.soilPercentage.tooltip": "",
  "heatDesign.waterReheatTime": "",
  "heatPumpConfig.advancedSettings": "",
  "heatPumpConfig.advancedSettings.cooling.title": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.0kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.3kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.6kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.bridgeWarning": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold": "",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold": "",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold": "",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.title": "",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp": "",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp.tooltip": "",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp": "",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp.tooltip": "",
  "heatPumpConfig.advancedSettingsDescription": "",
  "heatPumpConfig.domesticHotWater.tankSizeMismatch": "",
  "heatPumpConfig.general.dhw100": "",
  "heatPumpConfig.general.dhw150": "",
  "heatPumpConfig.general.dhw200": "",
  "heatPumpConfig.general.dhw250": "",
  "heatPumpConfig.general.dhw300": "",
  "heatPumpConfig.general.dhwNone": "",
  "heatPumpConfig.general.domesticHotWaterTankSize": "",
  "heatPumpConfig.general.domesticHotWaterTankSize.outdoorCapacityConstraintViolation": "",
  "heatPumpConfig.general.electricityMeterType": "",
  "heatPumpConfig.general.electricityMeterTypeDisclaimer": "",
  "heatPumpConfig.general.et112": "",
  "heatPumpConfig.general.et340": "",
  "heatPumpConfig.general.hotWaterTemperature": "",
  "heatPumpConfig.general.title": "",
  "heatPumpConfig.indoorUnitType.hydrobox": "",
  "heatPumpConfig.indoorUnitType.mismatch": "",
  "heatPumpConfig.indoorUnitType.title": "",
  "heatPumpConfig.indoorUnitType.tooltip": "",
  "heatPumpConfig.indoorUnitType.unitower": "",
  "heatPumpConfig.lastUpdatedAt": "",
  "heatPumpConfig.neverSaved": "",
  "heatPumpConfig.notAiraBrand.description": "",
  "heatPumpConfig.notAiraBrand.title": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity12kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity6kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity8kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacityMismatch": "",
  "heatPumpConfig.outdoorUnitCapacity.title": "",
  "heatPumpConfig.outdoorUnitCapacity.tooltip": "",
  "heatPumpConfig.output": "",
  "heatPumpConfig.saveButtonTitle": "",
  "heatPumpConfig.saveButtonTitle.saving": "",
  "heatPumpConfig.silent.mode": "",
  "heatPumpConfig.silent.mode.tooltip": "",
  "heatPumpConfig.title": "",
  "heatPumpConfig.zone.coolingCurveTitle": "",
  "heatPumpConfig.zone.emitterType.radiator": "",
  "heatPumpConfig.zone.emitterType.title": "",
  "heatPumpConfig.zone.emitterType.underfloor": "",
  "heatPumpConfig.zone.features.both": "",
  "heatPumpConfig.zone.features.cooling": "",
  "heatPumpConfig.zone.features.heating": "",
  "heatPumpConfig.zone.features.title": "",
  "heatPumpConfig.zone.flowTemperature.subtitle": "",
  "heatPumpConfig.zone.flowTemperature.title": "",
  "heatPumpConfig.zone.flowTemperature.tooltip": "",
  "heatPumpConfig.zone.flowTemperature.warning": "",
  "heatPumpConfig.zone.graph.coolingCurveInvalid": "",
  "heatPumpConfig.zone.graph.flowTemperature": "",
  "heatPumpConfig.zone.graph.heatingCurveInvalid": "",
  "heatPumpConfig.zone.graph.odtFlowPointLabel": "",
  "heatPumpConfig.zone.graph.outdoorDesignTemperature": "",
  "heatPumpConfig.zone.heatingCurveTitle": "",
  "heatPumpConfig.zone.outdoorDesignTemperature.subtitle": "",
  "heatPumpConfig.zone.outdoorDesignTemperature.title": "",
  "heatPumpConfig.zone.temperaturePlaceholder": "",
  "heatPumpConfig.zone.thermostatType.helperText": "",
  "heatPumpConfig.zone.thermostatType.title": "",
  "heatPumpConfig.zone.thermostatType.wired": "",
  "heatPumpConfig.zone.thermostatType.wireless": "",
  "heatPumpConfig.zone.title": "",
  "heatPumpConfig.zonesConfiguration.diagram.altText": "",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.description": "",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.label": "",
  "heatPumpConfig.zonesConfiguration.title": "",
  "heatPumpConfig.zonesConfiguration.tooltip": "",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.description": "",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.label": "",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.description": "",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.label": "",
  "hlc.label.save": "",
  "hlc.label.save.and.lock": "",
  "hlc.label.unlock.design": "",
  "hlc.unsavedChanges.modal.description": "",
  "hlc.unsavedChanges.modal.tittle": "",
  "houseData.addressChangeConfirmation.title": "",
  "houseData.addressChangeModal.distanceBetweenOldAndNewAddress": "",
  "houseData.addressChangeModal.ensureSurveysAndInstallationsAreAssignedCorrectly": "",
  "houseData.addressChangeModal.timeBetweenOldAndNewAddress": "",
  "houseData.addressChangeModal.updateAddressInErp": "",
  "houseData.addressChangeModal.urgent": "",
  "houseData.addressChangeModal.warning": "",
  "houseData.fuelType.GAS": "",
  "houseData.fuelType.LIQUID_GAS": "",
  "houseData.fuelType.OIL": "",
  "houseData.houseType.APARTMENT": "",
  "houseData.houseType.BUNGALOW": "",
  "houseData.houseType.DETACHED": "",
  "houseData.houseType.SEMI_DETACHED": "",
  "houseData.houseType.TERRACED": "",
  "houseData.label.bedroomCount": "",
  "houseData.label.fuelConsumption": "",
  "houseData.label.houseSize": "",
  "houseData.label.houseType": "",
  "houseData.label.housingUnits": "",
  "houseData.label.numberOfPanels": "",
  "houseData.label.postalCode": "",
  "houseData.notification.isAddressContainer": "",
  "houseData.title.consumptionEstimate": "",
  "houseData.title.solarPanelsInstalled": "",
  "infobar.airaZeroSignedAt": "",
  "infoBar.contactId": "",
  "infoBar.downloadSignedQuote": "",
  "infoBar.emailAddress": "",
  "infoBar.errorDownloadingSignedQuote": "",
  "infoBar.expiryDate": "",
  "infoBar.mobileNumber": "",
  "infoBar.phoneNumber": "",
  "infoBar.previewQuote": "",
  "infoBar.reference": "",
  "infoBar.resendTariffEmail": "",
  "infoBar.sendTariffEmail": "",
  "infoBar.showSignableQuote": "",
  "infoBar.signing.cancel": "",
  "infoBar.signing.content": "",
  "infoBar.signing.optionInstalments": "Finanziamento",
  "infoBar.signing.optionInstalmentsFinancing": "",
  "infoBar.signing.optionInvoice": "Senza finanziamento",
  "infoBar.signing.title": "",
  "infobar.tariff.modal.description": "",
  "infoBar.tariff.modal.title": "",
  "infoBar.viewAcceptedQuote": "",
  "infoBar.viewFinalOrderSummary": "",
  "infoBar.viewOrderSummary": "",
  "installationBooking.emptyState.noInstallation": "",
  "installationBooking.errors.hours": "",
  "installationBooking.fields.hours": "",
  "installationBooking.fields.jobsPerDay": "",
  "installationBooking.fields.jobsPerDayCaption": "",
  "installationBooking.main.bookButton": "",
  "installationBooking.main.description": "",
  "installationBooking.main.descriptionfrombaseline": "",
  "installationBooking.main.title": "",
  "installationBooking.moreOptions": "",
  "installationBooking.notify.booking": "",
  "installationBooking.notify.bookingCreated": "",
  "installationBooking.roles.electrician": "",
  "installationBooking.roles.installer": "",
  "installationBooking.roles.landscaper": "",
  "installationHandover.addUrl": "Aggiungere un URL",
  "installationHandover.appButtonTitle": "Accedere all'app Aira per configurare la pompa di calore",
  "installationHandover.externalLinks.deviationsTracker": "Traccia degli scostamenti",
  "installationHandover.externalLinks.fillInInstallationReport": "Compilare il rapporto di installazione",
  "installationHandover.externalLinks.heatPumpConfiguration": "Configurazione della pompa di calore",
  "installationHandover.extraDocuments.downloadPDF": "Scarica Magicplan PDF",
  "installationHandover.extraDocuments.openMagicplanApp": "Aprire l'applicazione Magicplan",
  "installationHandover.extraDocuments.openMagicplanWebsite": "Aprire il sito web di Magicplan",
  "installationHandover.extraDocuments.sharepointFolder": "Cartella di Sharepoint",
  "installationHandover.floorPlans.disclaimer": "Non tutti gli oggetti elencati possono essere presenti nelle planimetrie. Sono indicati solo gli oggetti aggiunti durante il sondaggio.",
  "installationHandover.hardware.common.unspecified": "Non specificato",
  "installationHandover.hardware.electrical.airaFuseBoxLocationPhotos": "Fare foto alla posizione suggerita per il quadretto della Pompa di Calore",
  "installationHandover.hardware.electrical.cableCrossSection": "Sezione cavi da contatore a quadro generale (mmq)",
  "installationHandover.hardware.electrical.cableRunsDrawnOnFloorPlan": "Hai verificato il percorso dell’alimentazione elettrica tra UI UE e quadro generale?",
  "installationHandover.hardware.electrical.comment": "Commento",
  "installationHandover.hardware.electrical.earthingArrangement": "",
  "installationHandover.hardware.electrical.earthingArrangementCustomerSubstation": "",
  "installationHandover.hardware.electrical.earthingArrangementTnCs": "",
  "installationHandover.hardware.electrical.earthingArrangementTnS": "",
  "installationHandover.hardware.electrical.earthingArrangementTt": "",
  "installationHandover.hardware.electrical.electricityBillPhotos": "Fare foto alla bolletta della luce (includendo lo storico a 12 mesi)",
  "installationHandover.hardware.electrical.electricityNetworkOperator": "",
  "installationHandover.hardware.electrical.fuseBoardPhotos": "Fare foto al quadro generale (mostrare la classe del quadro)",
  "installationHandover.hardware.electrical.isIncomingThreePhase": "Il cavo di alimentazione trifase arriva al contatore?",
  "installationHandover.hardware.electrical.loopedService": "",
  "installationHandover.hardware.electrical.mainFuseRating": "Valore nominale (Ampere) interruttore generale",
  "installationHandover.hardware.electrical.maxDemandTest": "",
  "installationHandover.hardware.electrical.meterBoxPhotos": "Foto vano contatore",
  "installationHandover.hardware.electrical.mpanNumber": "",
  "installationHandover.hardware.electrical.phases": "Fasi",
  "installationHandover.hardware.electrical.singlePhase": "1",
  "installationHandover.hardware.electrical.spaceForNewCables": "C’è spazio sufficiente per il passaggio dei nuovi cavi nel corrugato esistente? ",
  "installationHandover.hardware.electrical.threePhase": "3",
  "installationHandover.hardware.existingHeatSource.a2aHp": "Pompa di Calore Aria-Aria",
  "installationHandover.hardware.existingHeatSource.a2wHp": "Pompa di calore Aria-Acqua",
  "installationHandover.hardware.existingHeatSource.additionalHotWaterTankPhotos": "Foto dei bollitori esistenti e della targhetta",
  "installationHandover.hardware.existingHeatSource.boilerType": "Tipo di caldaia?",
  "installationHandover.hardware.existingHeatSource.boilerTypeCombi": "Combi",
  "installationHandover.hardware.existingHeatSource.boilerTypeRegular": "Regolare",
  "installationHandover.hardware.existingHeatSource.boilerTypeSystem": "Sistema",
  "installationHandover.hardware.existingHeatSource.coldWaterPressureBar": "",
  "installationHandover.hardware.existingHeatSource.comments": "Commenti",
  "installationHandover.hardware.existingHeatSource.connectionType": "",
  "installationHandover.hardware.existingHeatSource.connectionTypeMetal": "",
  "installationHandover.hardware.existingHeatSource.connectionTypePlastic": "",
  "installationHandover.hardware.existingHeatSource.consumptionKwh": "Consumo (Smc)",
  "installationHandover.hardware.existingHeatSource.consumptionLitres": "Consumo [litri]",
  "installationHandover.hardware.existingHeatSource.coveringType": "",
  "installationHandover.hardware.existingHeatSource.eaHp": "EAHP",
  "installationHandover.hardware.existingHeatSource.electricHeaters": "Riscaldatori elettrici",
  "installationHandover.hardware.existingHeatSource.electricityConsumptionMeasurements": "Misurazioni del consumo di energia elettrica (kWh)",
  "installationHandover.hardware.existingHeatSource.existingHeatingSourcePhotos": "Foto del generatore di calore primario e dei collettori",
  "installationHandover.hardware.existingHeatSource.flowTemperatureCelsius": "",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypeNeedsBrickingUp": "",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypePlastic": "",
  "installationHandover.hardware.existingHeatSource.flueNeedsCovering": "",
  "installationHandover.hardware.existingHeatSource.fluePhotos": "",
  "installationHandover.hardware.existingHeatSource.gasBoiler": "Caldaia a metano",
  "installationHandover.hardware.existingHeatSource.gasBoilerTechnicalDataPhotos": "Allegare foto libretto + Codice unità di generazione di calore esistente (es: Curit per la Lombardia)",
  "installationHandover.hardware.existingHeatSource.gasConsumptionMeasurements": "Misurazioni del consumo di gas (kWh)",
  "installationHandover.hardware.existingHeatSource.gasMeterNumber": "",
  "installationHandover.hardware.existingHeatSource.gasMeterPhotos": "",
  "installationHandover.hardware.existingHeatSource.gasNetworkOperator": "",
  "installationHandover.hardware.existingHeatSource.gsHp": "Caldaia a Vapore",
  "installationHandover.hardware.existingHeatSource.hasSeparateCirculationPump": "È presente una pompa di circolazione",
  "installationHandover.hardware.existingHeatSource.heatRegulation": "Come il cliente usa il riscaldamento?",
  "installationHandover.hardware.existingHeatSource.heatRegulationFixedTemperature": "Temperatura unica (impostata con il Termostato) ",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightLowering": "Temperatura a due livelli (giorno e notte)",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightOff": "Riscaldamento spento di notte",
  "installationHandover.hardware.existingHeatSource.heatRegulationTurnOnWhenPresent": "Regolazione manuale (funzionamento on/off quando è in casa)",
  "installationHandover.hardware.existingHeatSource.heatSourceType": "Generatore di calore esistente",
  "installationHandover.hardware.existingHeatSource.hotWaterProduction": "",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionExistingHeatSource": "",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherIntegrationDesired": "",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherNoIntegrationDesired": "",
  "installationHandover.hardware.existingHeatSource.isCondensingBoiler": "È una caldaia a condensazione?",
  "installationHandover.hardware.existingHeatSource.lpgBoiler": "Caldaia a GPL",
  "installationHandover.hardware.existingHeatSource.numberOfHeatingZones": "Numero di zone",
  "installationHandover.hardware.existingHeatSource.oilBurner": "Caldaia a Olio combustibile",
  "installationHandover.hardware.existingHeatSource.oilConsumptionMeasurements": "Misurazioni del consumo di olio (litri)",
  "installationHandover.hardware.existingHeatSource.oilLeftLitres": "",
  "installationHandover.hardware.existingHeatSource.otherComponentsPhotos": "Foto altri componenti (valvole di zona, testine, valvola miscelatrice acs ecc)",
  "installationHandover.hardware.existingHeatSource.pellets": "Caldaia a Pellet",
  "installationHandover.hardware.existingHeatSource.pipeSizeToDhw": "Dimensioni del tubo per l'acqua calda sanitaria",
  "installationHandover.hardware.existingHeatSource.pipeSizeToExistingBoiler": "Dimensioni del tubo verso la caldaia esistente",
  "installationHandover.hardware.existingHeatSource.removeExistingHeatingSource": "",
  "installationHandover.hardware.existingHeatSource.showerPumpNeedsReplacement": "",
  "installationHandover.hardware.existingHeatSource.solarThermal": "",
  "installationHandover.hardware.existingHeatSource.underfloorHeating": "La casa ha riscaldamento a pavimento?",
  "installationHandover.hardware.existingHeatSource.underfloorHeatingManifoldPhotos": "",
  "installationHandover.hardware.existingHeatSource.ventingType": "",
  "installationHandover.hardware.existingHeatSource.ventingTypeOpenVented": "",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnknown": "",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnvented": "",
  "installationHandover.hardware.existingHeatSource.year": "Anno",
  "installationHandover.hardware.existingHeatSource.yearOfManufacture": "Anno di installazione generatore di calore attuale",
  "installationHandover.hardware.existingHeatSource.yesRemoveExistingHeatSource": "",
  "installationHandover.hardware.existingHeatSource.yesUnderfloorHeating": "",
  "installationHandover.hardware.indoorInstallation.allInOneUnitFits": "Un All-in-one/Unitower è adatto?",
  "installationHandover.hardware.indoorInstallation.asbestosRisk": "",
  "installationHandover.hardware.indoorInstallation.bufferTankSize": "Dimensione massima del serbatoio tampone",
  "installationHandover.hardware.indoorInstallation.bufferTankSize100L": "100l (940x570 mm)",
  "installationHandover.hardware.indoorInstallation.bufferTankSize45L": "45l (880x370 mm)",
  "installationHandover.hardware.indoorInstallation.chipBoardFloorType": "",
  "installationHandover.hardware.indoorInstallation.coldWaterFlowRate": "Portata di acqua fredda (litri / minuto)",
  "installationHandover.hardware.indoorInstallation.comments": "Commenti (potenziali ostacoli sul percorso, gradini, porte strette, ecc.)",
  "installationHandover.hardware.indoorInstallation.componentsShouldFitWhenTilted": "",
  "installationHandover.hardware.indoorInstallation.crampedInstallationLocation": "Gli spazi di lavoro sono ridotti? Per esempio, può lavorarci una sola persona alla volta?",
  "installationHandover.hardware.indoorInstallation.distanceBetweenTundishAndTermination": "",
  "installationHandover.hardware.indoorInstallation.distanceOfNewPrimaryPipeworkNeeded": "",
  "installationHandover.hardware.indoorInstallation.drainPipePresent": "È presente un tubo di scarico?",
  "installationHandover.hardware.indoorInstallation.drinkingWaterFilter": "Esiste un filtro per l'acqua potabile",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupply": "",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyNetwork": "",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyOwnWell": "",
  "installationHandover.hardware.indoorInstallation.emergencyDischargePipeReusable": "",
  "installationHandover.hardware.indoorInstallation.floorBoardFloorType": "",
  "installationHandover.hardware.indoorInstallation.floorType": "",
  "installationHandover.hardware.indoorInstallation.hydraulicUnitFits": "N: È possibile montare un'unità idraulica (770x440x350)?",
  "installationHandover.hardware.indoorInstallation.indoorPrimaryPipingRoutePhotos": "Fare foto al percorso dell'unità interna, dal punto di consegna alla posizione individuata",
  "installationHandover.hardware.indoorInstallation.installationLocationPhotos": "Panoramica della posizione dell’Unità Interna, compreso il percorso per raggiungerla (non aggiungere foto se coincidente con attuale centrale termica)",
  "installationHandover.hardware.indoorInstallation.largestAllInOneSizeThatFits": "Y: L'unità all-in-one più grande che può essere installata",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits": "Y: Dimensioni del serbatoio dell'acqua calda sanitaria",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits150L": "150l (955x595)",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits200L": "200l (1265x595)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsLarge": "Grande (1880x600x600)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsSmall": "Piccolo (1100x500x500)",
  "installationHandover.hardware.indoorInstallation.minimumPathHeight": "Altezza minima del percorso fino al luogo di installazione",
  "installationHandover.hardware.indoorInstallation.minimumPathWidth": "Larghezza minima del percorso fino al luogo di installazione",
  "installationHandover.hardware.indoorInstallation.pipeLayoutDrawn": "Avete disegnato sulla piantina MagicPlan la disposizione dei tubi dall'unità esterna all'unità interna?",
  "installationHandover.hardware.indoorInstallation.plasticDuctOnPipesNeeded": "Il cliente vuole una canalina",
  "installationHandover.hardware.indoorInstallation.primaryRunsUnderneathFloor": "",
  "installationHandover.hardware.indoorInstallation.wifiReachesInstallationLocation": "Wi-fi stabile nei pressi di UI?",
  "installationHandover.hardware.outdoorInstallation.comments": "Specificare come si intende trasportare l'unità esterna fino al luogo individuato",
  "installationHandover.hardware.outdoorInstallation.condensationPipedToDrain": "Basamento",
  "installationHandover.hardware.outdoorInstallation.condensationSoakAway": "Grondaia (pluviale)",
  "installationHandover.hardware.outdoorInstallation.condensationSolution": "Dove scarica condensa?",
  "installationHandover.hardware.outdoorInstallation.distance": "Quanto lunga?",
  "installationHandover.hardware.outdoorInstallation.distanceFromOutdoorUnitToWall": "Distanza dell'unità esterna alla parete",
  "installationHandover.hardware.outdoorInstallation.distanceOfTrunkingRequired": "Quanto lunga? [m]",
  "installationHandover.hardware.outdoorInstallation.extraResourcesNeeded": "Sono necessarie risorse extra oltre alle due persone del team per movimentare l'unità esterna?",
  "installationHandover.hardware.outdoorInstallation.holePlacement": "Posizione del foro per il passaggio dei tubi",
  "installationHandover.hardware.outdoorInstallation.holePlacementAboveGround": "A muro",
  "installationHandover.hardware.outdoorInstallation.holePlacementBelowGround": "A terra",
  "installationHandover.hardware.outdoorInstallation.holePlacementHighAboveGround": "In alto (scala necessaria)",
  "installationHandover.hardware.outdoorInstallation.isGroundworkRequired": "Necessario basamento in cemento?",
  "installationHandover.hardware.outdoorInstallation.isOutdoorPipingBelowGroundNeeded": "E' necessaria traccia a terra?",
  "installationHandover.hardware.outdoorInstallation.isOutdoorTrunkingRequired": "Necessaria canalina all'esterno?",
  "installationHandover.hardware.outdoorInstallation.minimumDistanceToClosestOpening": "Misurare la distanza da eventuali finestre, porte e/o bocche di lupo",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnGround": "A terra",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnWall": "Staffa a muro",
  "installationHandover.hardware.outdoorInstallation.obstacleElectricalSocket": "Piante",
  "installationHandover.hardware.outdoorInstallation.obstacleOutsideTab": "Costruzione esistente",
  "installationHandover.hardware.outdoorInstallation.obstacles": "Quali ostacoli?",
  "installationHandover.hardware.outdoorInstallation.obstacleSoilPipe": "Muretto",
  "installationHandover.hardware.outdoorInstallation.obstaclesToRemove": "Ci sono ostacoli da rimuovere per installare unità esterna?",
  "installationHandover.hardware.outdoorInstallation.otherCondensationSolution": "Altro",
  "installationHandover.hardware.outdoorInstallation.outdoorPrimaryPipingRoutePhotos": "Fare foto al percorso dell'unità esterna (dal punto di consegna alla posizione individuata)",
  "installationHandover.hardware.outdoorInstallation.outdoorUnitMounting": "Fissaggio unità esterna",
  "installationHandover.hardware.outdoorInstallation.proposedInstallationLocationPhotos": "Allegare foto posizione unità esterna, di dettaglio e panoramica",
  "installationHandover.hardware.outdoorInstallation.shinglesRequired": "",
  "installationHandover.hardware.outdoorInstallation.trenchDigger": "Chi si occupa della traccia?",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerAira": "Aira",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerCustomer": "Cliente",
  "installationHandover.hardware.outdoorInstallation.trunkingColour": "",
  "installationHandover.hardware.outdoorInstallation.trunkingColourBlack": "",
  "installationHandover.hardware.outdoorInstallation.trunkingColourWhite": "",
  "installationHandover.hardware.outdoorInstallation.wallMaterial": "Tipologia di muro",
  "installationHandover.hardware.outdoorInstallation.wallMaterialBrick": "",
  "installationHandover.hardware.outdoorInstallation.wallMaterialConcrete": "",
  "installationHandover.hardware.outdoorInstallation.wallMaterialHardStone": "Pietra dura (es: Granito)",
  "installationHandover.hardware.outdoorInstallation.wallMaterialNaturalStone": "Muratura in pietra",
  "installationHandover.hardware.outdoorInstallation.wallMaterialPerforatedBrick": "Mattoni cavi",
  "installationHandover.hardware.outdoorInstallation.wallMaterialReinforcedConcrete": "Cemento armato",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSoftStone": "",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSolidBrick": "Mattoni pieni",
  "installationHandover.hardware.outdoorInstallation.wallMaterialWood": "Legno",
  "installationHandover.hardware.outdoorInstallation.wallThickness": "Spessore del muro",
  "installationHandover.hardware.outdoorInstallation.yesObstaclesToRemove": "Se sì",
  "installationHandover.hardware.outdoorInstallation.yesOutdoorPipingBelowGroundNeeded": "Se sì",
  "installationHandover.hardware.title.electrical": "Elettrico",
  "installationHandover.hardware.title.existingHeatSource": "Centrale termica/Fonte di generazione di calore esistente",
  "installationHandover.hardware.title.indoorInstallation": "Posizione Unità Interna",
  "installationHandover.hardware.title.outdoorInstallation": "Posizione unità esterna",
  "installationHandover.heatPumpConfigAvailable": "I parametri di configurazione della pompa di calore sono disponibili per questa installazione.",
  "installationHandover.noInstallation": "Nessuna installazione",
  "installationHandover.objectDetailsPanel.buttons.info": "Info",
  "installationHandover.objectDetailsPanel.buttons.photos": "Foto",
  "installationHandover.objectDetailsPanel.radiator": "Radiatore",
  "installationHandover.pasteLink": "Incolla il link",
  "installationHandover.projectOveriew.title.mainTitle": "Panoramica del progetto",
  "installationHandover.projectOveriew.title.product": "Prodotto",
  "installationHandover.projectOveriew.title.quantity": "Quantità",
  "installationHandover.projectOverview.people": "Dipendenti coinvolti",
  "installationHandover.radiator": "Radiatore",
  "installationHandover.radiatorInfoPanel.comment": "Commento:",
  "installationHandover.radiatorInfoPanel.deltaT": "DeltaT:",
  "installationHandover.radiatorInfoPanel.electricRadiator": "Radiatore elettrico",
  "installationHandover.radiatorInfoPanel.enabled": "Abilitato?",
  "installationHandover.radiatorInfoPanel.height": "Altezza:",
  "installationHandover.radiatorInfoPanel.inRoom": "In quale stanza si trova:",
  "installationHandover.radiatorInfoPanel.outputWatts": "Potenza [W]:",
  "installationHandover.radiatorInfoPanel.radiatorType": "Tipo di radiatore:",
  "installationHandover.radiatorInfoPanel.toBeInstalled": "Da installare?:",
  "installationHandover.radiatorInfoPanel.waterRadiator": "Radiatore ad acqua",
  "installationHandover.radiatorInfoPanel.width": "Larghezza:",
  "installationHandover.radiators.existingRadiators": "Comodi radiatori",
  "installationHandover.radiators.newRadiators": "Nuovi radiatori da installare",
  "installationHandover.radiators.noExisitingRadiatorsInRoom": "In questa stanza non sono presenti radiatori",
  "installationHandover.radiators.noNewRadiatorsInRoom": "Non è prevista l'installazione di nuovi radiatori in questa stanza.",
  "installationHandover.radiators.noRadiatorsInRoom": "In questa stanza non ci sono radiatori",
  "installationHandover.radiators.noRadiatorsToRemoveInRoom": "Non ci sono radiatori che devono essere rimossi da questa stanza.",
  "installationHandover.radiators.removedRadiators": "Radiatori remoti",
  "installationHandover.roomInfoPanel.existingRadiators": "Comodi radiatori",
  "installationHandover.sharePointUrlError": "⛔ Qualcosa è andato storto, controllare l'url e riprovare.",
  "installationHandover.sharePointUrlSuccess": "✅ L'url di Sharepoint è stato aggiornato con successo.",
  "installationHandover.title": "Consegna dell'installazione",
  "installationHandover.titles.mainTitle": "Consegna dell'installazione",
  "installationPlanning.addResourcesToJob.availableResources": "",
  "installationPlanning.addResourcesToJob.currentResources": "",
  "installationPlanning.addResourcesToJob.noAvailableResources": "",
  "installationPlanning.addResourcesToJob.noResources": "",
  "installationPlanning.addResourcesToJob.title": "",
  "installationPlanning.addResourcesToTeam.availableResources": "",
  "installationPlanning.addResourcesToTeam.defaultResources": "",
  "installationPlanning.addResourcesToTeam.noAvailableResources": "",
  "installationPlanning.addResourcesToTeam.noResources": "",
  "installationPlanning.assignResourceToSegment.conflictingSegments": "",
  "installationPlanning.assignResourceToSegment.conflictingServiceVisits": "",
  "installationPlanning.assignResourceToSegment.unavailable": "",
  "installationPlanning.changeLog.title": "",
  "installationPlanning.dateRangePicker.endDate": "",
  "installationPlanning.dateRangePicker.selectDateRange": "",
  "installationPlanning.dateRangePicker.startDate": "",
  "installationPlanning.dispatchJobs.description": "",
  "installationPlanning.dispatchJobs.dispatchAll": "",
  "installationPlanning.dispatchJobs.dispatchSelected": "",
  "installationPlanning.dispatchJobs.failedToDispatchJobs": "",
  "installationPlanning.dispatchJobs.successfullyDispatchedJobs": "",
  "installationPlanning.dispatchJobs.title": "",
  "installationPlanning.highlightFlexibleProjects": "",
  "installationPlanning.highlightProjects": "",
  "installationPlanning.highlightProjectsWithin": "",
  "installationPlanning.incompleteHours.description": "",
  "installationPlanning.incompleteHours.errors.failedToAddHours": "",
  "installationPlanning.incompleteHours.save": "",
  "installationPlanning.incompleteHours.success": "",
  "installationPlanning.incompleteHours.title": "",
  "installationPlanning.inSalesRecoveryProjects": "",
  "installationPlanning.jobBar.segmentOverviewModal.baselineManHours": "",
  "installationPlanning.jobBar.segmentOverviewModal.expectedTravelTime": "",
  "installationPlanning.jobBar.segmentOverviewModal.extraTimeNeeded": "",
  "installationPlanning.jobBar.segmentOverviewModal.scheduledManHours": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_FINANCE_NOT_COMPLETED": "Cliente problemi di pagamento",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_ILLNESS": "Cliente malato",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_RESCHEDULED": "Cliente non più disponibile in quella data",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_SCOPE_CHANGED": "Cliente richieste di modifica del progetto",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_HEAT_DESIGN_NOT_COMPLETED": "Progetto non ancora completato",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_INSTALMENT_CAPACITY": "Mancanza di team di installazione disponibile",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_DISTRIBUTION_NETWORK_OPERATOR": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_PLANNING_APPLICATION": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_SUBSIDY": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_MATERIAL_UNAVAILABLE": "Product supply materiale non disponibile",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_RUSH_ORDER": "Product Supply ordine troppo di fretta",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_TECHNICAL_SURVEY_NOT_COMPLETED": "Sopralluogo tecnico non ancora completato",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_CUSTOMER_TRADES": "Terze parti lavori del cliente più lunghi del previsto",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_DISTRIBUTION_NETWORK_OPERATOR": "Terze parti aumento potenza elettrica",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_SUBCONTRACTOR": "Terze parti altri lavori (es. basamento)",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNCATEGORIZED": "Altro",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNSPECIFIED": "Altro",
  "installationPlanning.jobRescheduledReasonGroup.Customer": "Cliente",
  "installationPlanning.jobRescheduledReasonGroup.Heat Design": "Progetto",
  "installationPlanning.jobRescheduledReasonGroup.Installment Capacity": "Team di installazione",
  "installationPlanning.jobRescheduledReasonGroup.Permissions": "",
  "installationPlanning.jobRescheduledReasonGroup.Product Supply": "Product supply",
  "installationPlanning.jobRescheduledReasonGroup.Technical Survey": "Sopralluogo tecnico",
  "installationPlanning.jobRescheduledReasonGroup.Third Party": "Terze parti",
  "installationPlanning.jobRescheduledReasonGroup.Uncategorized": "Altro",
  "installationPlanning.newBaselinesModal.apply": "",
  "installationPlanning.newBaselinesModal.currentDuration": "",
  "installationPlanning.newBaselinesModal.discard": "",
  "installationPlanning.newBaselinesModal.newDuration": "",
  "installationPlanning.newBaselinesModal.title": "",
  "installationPlanning.noLongerResource": "",
  "installationPlanning.notificationCenter.newBaselinesAvailable": "",
  "installationPlanning.notificationCenter.plannerToolUpdates": "",
  "installationPlanning.notificationCenter.projectsWithUnappliedBaseline": "",
  "installationPlanning.notificationCenter.title": "",
  "installationPlanning.notify.updatedJobTeam": "",
  "installationPlanning.notify.updatedJobTeamError": "",
  "installationPlanning.notify.updateWorkSegmentsError": "",
  "installationPlanning.notify.updateWorkSegmentsSuccess": "",
  "installationPlanning.onHoldProjects": "",
  "installationPlanning.overlappingSegments.explanation": "",
  "installationPlanning.overlappingSegments.title": "",
  "installationPlanning.projectSidebar.bufferTank": "",
  "installationPlanning.projectSidebar.heatPumpIndoorUnit": "",
  "installationPlanning.projectSidebar.heatPumpOutdoorUnit": "",
  "installationPlanning.projectSidebar.installationPackageSize": "",
  "installationPlanning.projectSidebar.manufacturer": "",
  "installationPlanning.projectSidebar.openTicketsInHubspot": "",
  "installationPlanning.projectSidebar.radiators": "",
  "installationPlanning.projectSidebar.solution": "",
  "installationPlanning.removeResourceUnavailability.button.cancel": "",
  "installationPlanning.removeResourceUnavailability.button.confirm": "",
  "installationPlanning.removeResourceUnavailability.errors": "",
  "installationPlanning.removeResourceUnavailability.success": "",
  "installationPlanning.removeResourceUnavailability.title": "",
  "installationPlanning.saveJobChangesToChangeAssignees": "",
  "installationPlanning.showFilters": "",
  "installationPlanning.stage.installation": "",
  "installationPlanning.stage.new": "",
  "installationPlanning.stage.postInstallation": "",
  "installationPlanning.stage.preInstallation": "",
  "installationPlanning.stage.technicalDesign": "",
  "installationPlanning.stage.technicalSurvey": "",
  "installationPlanning.suggestionsMap.showProjectsWithNoDate": "",
  "installationPlanning.team.clickToManageResources": "",
  "installationPlanning.topBar.refreshButton.error": "",
  "installationPlanning.topBar.refreshButton.success": "",
  "installationPlanning.topBar.refreshButton.tooltip": "",
  "installationPlanning.unutilization.addAbsence": "",
  "installationPlanning.unutilization.addAbsenceError": "",
  "installationPlanning.unutilization.addAbsenceSuccess": "",
  "installationPlanning.unutilization.endDayDuration": "",
  "installationPlanning.unutilization.firstDayDuration": "",
  "installationPlanning.unutilization.fromTo": "",
  "installationPlanning.unutilization.lentOutRegion": "",
  "installationPlanning.unutilization.otherReasonHelperText": "",
  "installationPlanning.unutilization.reason": "",
  "installationPlanning.unutilization.resourceAlreadyInActive": "",
  "installationPlanning.viewProjectInAerospace": "",
  "installationPlanning.viewProjectInHubspot": "",
  "installationReport.button.sign": "Firma",
  "installationReport.commissioningDate.label": "Messa in servizio completata {date}",
  "installationReport.commissioningIncomplete.label": "Messa in servizio incompleta",
  "installationReport.confirmation.journey.financingApplication": "Richiesta di finanziamento",
  "installationReport.confirmation.journey.header": "Il tuo percorso con Aira",
  "installationReport.confirmation.journey.homeEnergyAssessment": "Valutazione energetica domestica",
  "installationReport.confirmation.journey.installation": "Installazione",
  "installationReport.confirmation.journey.reviewAndAcceptQuote": "Rivedi & accetta il preventivo",
  "installationReport.confirmation.journey.technicalSurveyAndDesign": "Sopralluogo tecnico & progetto",
  "installationReport.confirmation.link.installationReport": "Guarda il tuo installation report",
  "installationReport.confirmation.nextSteps.accepted.body": "Abbiamo inviato un'e-mail con una copia del rapporto di installazione.",
  "installationReport.confirmation.nextSteps.accepted.heading": "I prossimi passi",
  "installationReport.confirmation.nextSteps.rejected.body": "Ti contatteremo presto per completare la tua installazione.",
  "installationReport.confirmation.nextSteps.rejected.heading": "Cosa succede adesso?",
  "installationReport.confirmation.title": "Grazie",
  "installationReport.customer.title": "Cliente",
  "installationReport.customerChecklistIntro.shareScreen.body": "Per compilare questo modulo, il cliente deve essere presente. Rispondete insieme alle domande e ricorda di far firmare il modulo al cliente.",
  "installationReport.customerChecklistIntro.shareScreen.heading": "Condividi il tuo schermo",
  "installationReport.customerChecklistIntro.title": "Onboarding del cliente",
  "installationReport.customerChecklistIntro.wantToKnowMore.body": "È possibile tornare indietro alla Checklist su installazione e sistema se il cliente desidera rivedere dettagli tecnici sulla propria installazione.",
  "installationReport.customerChecklistIntro.wantToKnowMore.heading": "Vuoi saperne di più?",
  "installationReport.incomplete.title": "Report di installazione incompleto",
  "installationReport.instructions.checklists.body": "Una serie di domande tecniche sul sistema e sull’installazione. Puoi completarli da solo.",
  "installationReport.instructions.checklists.heading": "Checklist su installazione e sistema",
  "installationReport.instructions.intro": "Compilare queste checklist dopo aver completato l'installazione della pompa di calore. Ci sono due parti:",
  "installationReport.instructions.onboarding.body": "Istruzioni al cliente su come utilizzare la pompa di calore. Per questa parte, il cliente deve essere presente.",
  "installationReport.instructions.onboarding.heading": "Onboarding del cliente",
  "installationReport.instructions.title": "Installazione completata",
  "installationReport.products.bufferTank": "Accumulatore",
  "installationReport.products.indoorUnit": "Unità interna",
  "installationReport.products.outdoorUnit": "Unità esterne",
  "installationReport.products.radiators": "Radiatori",
  "installationReport.products.solutionId": "Numero ESID",
  "installationReport.products.title": "Prodotti installati",
  "installationReport.question.validation.greaterThan": "Il valore deve essere maggiore di {minValue}",
  "installationReport.question.validation.greaterThanAndLessThan": "Il valore deve essere maggiore di {minValue} e minore di {maxValue}",
  "installationReport.question.validation.lessThan": "Il valore deve essere inferiore a {maxValue}",
  "installationReport.reportSelector.label": "Rapporto presentato",
  "installationReport.signature.customer.title": "Firma del cliente",
  "installationReport.signature.installer.title": "Firma dell'installatore",
  "installationReport.signatureModal.installationComplete.label": "Firmando questo documento, confermi che la tua installazione è stata completata.",
  "installationReport.signatureModal.installationIncomplete.label": "Firmando questo documento, confermi di aver segnalato tutti i lavori/materiali rimanenti necessari per completare la tua installazione.",
  "installationReport.startPage.installers.title": "Installatori",
  "installationReport.startPage.locked.body": "È già stata presentata una relazione accettata per questo cliente.",
  "installationReport.startPage.locked.title": "Bloccato",
  "installationReport.startPage.start": "Inizio",
  "installationReport.textInput.placeholder": "Testo",
  "installationReport.title": "Aira Rapporto di messa in servizio - Installazione di una pompa di calore",
  "installationReview.button.approveActualDuration": "",
  "installationReview.button.cleanEnergyTechnician": "",
  "installationReview.button.deleteAll": "",
  "installationReview.button.deleteJob": "",
  "installationReview.button.electrician": "",
  "installationReview.button.landscaper": "",
  "installationReview.button.unschedule": "",
  "installationReview.columnHeaders.actualEnd": "",
  "installationReview.columnHeaders.actualManHours": "",
  "installationReview.columnHeaders.actualStart": "",
  "installationReview.columnHeaders.addTravelTime": "",
  "installationReview.columnHeaders.day": "",
  "installationReview.columnHeaders.duration": "",
  "installationReview.columnHeaders.expectedDuration": "",
  "installationReview.columnHeaders.expectedManHours": "",
  "installationReview.columnHeaders.expectedStart": "",
  "installationReview.columnHeaders.resources": "",
  "installationReview.columnHeaders.scheduledEnd": "",
  "installationReview.columnHeaders.scheduledManHours": "",
  "installationReview.columnHeaders.scheduledStart": "",
  "installationReview.columnHeaders.startTravel": "",
  "installationReview.columnHeaders.status": "",
  "installationReview.errors.failedToDelete": "",
  "installationReview.errors.failedToDeleteJobs": "",
  "installationReview.errors.failedToPublish": "",
  "installationReview.errors.failedToUnschedule": "",
  "installationReview.errors.failedToUnscheduleJobs": "",
  "installationReview.errors.notYetScheduled": "",
  "installationReview.errors.unfinishedJob": "",
  "installationReview.jobSegmentStatus.FINISHED": "",
  "installationReview.jobSegmentStatus.IN_PROGRESS": "",
  "installationReview.jobSegmentStatus.NOT_STARTED": "",
  "installationReview.labels.defaultResourceCount": "",
  "installationReview.labels.linkToSchedulingTool": "",
  "installationReview.labels.reviewedAt": "",
  "installationReview.labels.totalActualDuration": "",
  "installationReview.labels.totalActualManHours": "",
  "installationReview.labels.totalExpectedDuration": "",
  "installationReview.labels.totalExpectedManHours": "",
  "installationReview.modals.addJobHours.button.cancel": "",
  "installationReview.modals.addJobHours.button.confirm": "",
  "installationReview.modals.addJobHours.helperText": "",
  "installationReview.modals.addJobHours.title": "",
  "installationReview.modals.confirmDeletion.button.cancel": "",
  "installationReview.modals.confirmDeletion.button.delete": "",
  "installationReview.modals.confirmDeletion.label.reasonCategory": "",
  "installationReview.modals.confirmDeletion.label.reasonDescription": "",
  "installationReview.modals.confirmDeletion.title": "",
  "installationReview.modals.confirmDeletionAll.title": "",
  "installationReview.modals.confirmUnschedule.button.cancel": "",
  "installationReview.modals.confirmUnschedule.button.unschedule": "",
  "installationReview.modals.confirmUnschedule.label.reasonCategory": "",
  "installationReview.modals.confirmUnschedule.label.reasonDescription": "",
  "installationReview.modals.confirmUnschedule.title": "",
  "installationReview.modals.removeJobHours.button.cancel": "",
  "installationReview.modals.removeJobHours.button.remove": "",
  "installationReview.modals.removeJobHours.title": "",
  "installationReview.notify.addHoursSuccess": "",
  "installationReview.notify.deletionSuccess": "",
  "installationReview.notify.removeHoursSuccess": "",
  "installationReview.notify.unscheduleSuccess": "",
  "installationReview.role.ELECTRICIAN": "",
  "installationReview.role.INSTALLER": "",
  "installationReview.role.LANDSCAPER": "",
  "installationReview.role.UNKNOWN": "",
  "installationReview.tableContent.noResources": "",
  "installationReview.tableContent.resourceCount": "",
  "installationReview.title": "",
  "installationReview.values.duration": "",
  "installationReview.values.expectedManHoursPerResource": "",
  "installationReview.values.totalExpectedDuration": "",
  "invoice.finalInvoice": "",
  "invoice.loading.error": "",
  "invoice.pastDue": "",
  "invoice.prepayment": "",
  "invoice.total.remainingToPay": "",
  "invoice.total.value": "",
  "invoiceRefund.amount": "",
  "invoiceRefund.amountSummary": "",
  "invoiceRefund.confirm": "",
  "invoiceRefund.description": "",
  "invoiceRefund.error.amountRequired": "",
  "invoiceRefund.error.exceedsTotal": "",
  "invoiceRefund.error.invalidAmount": "",
  "invoiceRefund.error.reasonRequired": "",
  "invoiceRefund.fullRefund": "",
  "invoiceRefund.partialRefund": "",
  "invoiceRefund.processing": "",
  "invoiceRefund.reason": "",
  "invoiceRefund.title": "",
  "invoiceRefund.totalAmount": "",
  "invoiceRefund.warning": "",
  "invoiceSatus.draft": "",
  "invoiceSatus.open": "",
  "invoiceSatus.uncollectable": "",
  "invoicesTable.amountHeader": "",
  "invoicesTable.dateCreatedHeader": "",
  "invoicesTable.descriptionHeader": "",
  "invoicesTable.dueDateHeader": "",
  "invoicesTable.emptyState": "",
  "invoicesTable.invoiceNumberHeader": "",
  "invoicesTable.noPaymentReceived": "",
  "invoicesTable.paymentId": "",
  "invoicesTable.paymentReceived": "",
  "invoicesTable.paymentSummary": "",
  "invoicesTable.previewInvoice": "",
  "invoicesTable.refundButton": "",
  "invoicesTable.statusHeader": "",
  "invoicesTable.title": "",
  "invoiceStatus.cancelled": "",
  "invoiceStatus.creditNote": "",
  "invoiceStatus.draft": "",
  "invoiceStatus.Error": "",
  "invoiceStatus.error": "",
  "invoiceStatus.open": "",
  "invoiceStatus.paid": "",
  "invoiceStatus.partiallyPaid": "",
  "invoiceStatus.partiallyRefunded": "",
  "invoiceStatus.pastDue": "",
  "invoiceStatus.refunded": "",
  "invoiceStatus.uncollectable": "",
  "invoiceStatus.unknown": "",
  "invoicing.createInvoice": "",
  "invoicing.erp.customerId": "",
  "invoicing.erp.projectId": "",
  "invoicing.error.fetchingInvoices": "",
  "invoicing.title": "",
  "localized.string": "",
  "numberInputHelperText.canBeDecimal": "",
  "numberInputHelperText.canBeInteger": "",
  "numberInputHelperText.canBeNegative": "",
  "numberInputHelperText.canBeNegativeAndDecimal": "",
  "ongoingInstallations.actualStartDate": "",
  "ongoingInstallations.commissioningDate": "",
  "ongoingInstallations.completed": "",
  "ongoingInstallations.customers": "",
  "ongoingInstallations.daysUntilInstallation": "",
  "ongoingInstallations.filters.country": "",
  "ongoingInstallations.filters.from": "",
  "ongoingInstallations.filters.region": "",
  "ongoingInstallations.filters.to": "",
  "ongoingInstallations.hoursForCompletion": "",
  "ongoingInstallations.installationComplete": "",
  "ongoingInstallations.installationCompleteDate": "",
  "ongoingInstallations.installationIncomplete": "",
  "ongoingInstallations.installationIncompleteDate": "",
  "ongoingInstallations.jobsOverview": "",
  "ongoingInstallations.link": "",
  "ongoingInstallations.noStartDate": "",
  "ongoingInstallations.plannedStartDate": "",
  "ongoingInstallations.projectStage": "",
  "ongoingInstallations.regions": "",
  "ongoingInstallations.reworkCompletionHours": "",
  "ongoingInstallations.status": "",
  "ongoingInstallations.status.completed": "",
  "ongoingInstallations.status.installation": "",
  "ongoingInstallations.status.invoice": "",
  "ongoingInstallations.status.postInstallation": "",
  "ongoingInstallations.status.preInstallation": "",
  "ongoingInstallations.status.unknown": "",
  "ongoingInstallations.teamLead": "",
  "ongoingInstallations.title": "",
  "outdoorUnit.add.cascading.button": "",
  "outdoorUnit.add.cascading.description": "",
  "procurement.actions.bomDeprecated": "",
  "procurement.actions.bomNotReadyForProcurement": "",
  "procurement.actions.designReviewNotAccepted": "",
  "procurement.actions.errorPrefix": "",
  "procurement.actions.installationDateRequired": "",
  "procurement.actions.installationKitRequired": "",
  "procurement.actions.markedAsOrdered": "",
  "procurement.actions.markingAsOrdered": "",
  "procurement.actions.markOrderFailed": "",
  "procurement.actions.ordered": "",
  "procurement.actions.orderedSuccess": "",
  "procurement.actions.orderFailed": "",
  "procurement.actions.orderFailedToast": "",
  "procurement.actions.ordering": "",
  "procurement.actions.preliminaryDateConfirmDescription": "",
  "procurement.actions.preliminaryDateConfirmTitle": "",
  "procurement.actions.procurementCommentMissing": "",
  "procurement.actions.reorderConfirmDescription": "",
  "procurement.actions.reorderConfirmTitle": "",
  "procurement.actions.saveAndOrder": "",
  "procurement.actions.saved": "",
  "procurement.actions.saveFailed": "",
  "procurement.actions.saveFailedToast": "",
  "procurement.actions.saving": "",
  "procurement.actions.solutionNotReadyForOrder": "",
  "procurement.actions.vanStockBundleRequired": "",
  "procurement.comment.label": "",
  "procurement.comment.placeholder": "",
  "procurement.description": "",
  "procurement.label.installationDate": "",
  "procurement.label.selectInstallationKitVersion": "",
  "procurement.label.selectInstallationKitVersion.tooltip": "",
  "procurement.label.selectVanStockBundles": "",
  "procurement.label.selectVanStockBundles.tooltip": "",
  "procurement.label.selectVanStockBundlesPlaceholder": "",
  "procurement.status.ordered": "",
  "procurement.title": "",
  "procurement.warning.bomBundleOutdated": "",
  "procurement.warning.installationDatePassed": "",
  "product-price-adjustment.adjust-price": "",
  "product-price-adjustment.excluding-vat": "",
  "product-price-adjustment.handled-products": "",
  "product-price-adjustment.handled-products-note": "",
  "product-price-adjustment.initial-quote": "",
  "product-price-adjustment.note": "",
  "product-price-adjustment.products-with-price-increase": "",
  "product-price-adjustment.products-with-price-increase-note": "",
  "product-price-adjustment.products-with-price-reduction": "",
  "product-price-adjustment.products-with-price-reduction-note": "",
  "product-price-adjustment.reverted-products": "",
  "product-price-adjustment.reverted-products-note": "",
  "product-price-adjustment.revised-products": "",
  "product-price-adjustment.subtitle": "",
  "product-price-adjustment.title": "",
  "product-price-adjustment.updated-price": "",
  "productSummary.comparison.added": "",
  "productSummary.comparison.displayOriginalOffer": "",
  "productSummary.comparison.hideOriginalOffer": "",
  "productSummary.comparison.removed": "",
  "productSummary.comparison.unchanged": "",
  "productSummary.comparison.updated": "",
  "productSummary.discountsTable.discountExVat": "",
  "productSummary.discountsTable.discountIncVat": "",
  "productSummary.discountsTable.discounts": "",
  "productSummary.discountsTable.vatPercentage": "",
  "productSummary.invoice.alreadySent": "",
  "productSummary.invoice.error.unknown": "",
  "productSummary.invoice.final.label": "",
  "productSummary.invoice.final.send": "",
  "productSummary.invoice.final.send.popup.infoCard.title": "",
  "productSummary.invoice.final.send.popup.readyTitle": "",
  "productSummary.invoice.invoice": "",
  "productSummary.invoice.paid": "",
  "productSummary.invoice.partial.label": "",
  "productSummary.invoice.partial.notPaid": "",
  "productSummary.invoice.partial.send": "",
  "productSummary.invoice.send": "",
  "productSummary.invoice.send.loading": "",
  "productSummary.invoice.send.popup.description": "",
  "productSummary.invoice.send.popup.infoCard.text": "",
  "productSummary.invoice.send.popup.infoCard.title": "",
  "productSummary.invoice.send.popup.readyTitle": "",
  "productSummary.invoice.send.popup.title": "",
  "productSummary.invoice.send.tooltip": "",
  "productSummary.invoice.sent": "",
  "productSummary.productsTable.finalOrderSummaryInformation": "",
  "productSummary.productsTable.itemNumber": "",
  "productSummary.productsTable.offerSummaryInformation": "",
  "productSummary.productsTable.orderSummaryInformation": "",
  "productSummary.productsTable.PAYMENT_TYPE_INSTALMENTS": "",
  "productSummary.productsTable.PAYMENT_TYPE_INVOICE": "",
  "productSummary.productsTable.paymentMethod": "",
  "productSummary.productsTable.priceExVat": "",
  "productSummary.productsTable.priceIncVat": "",
  "productSummary.productsTable.quantity": "",
  "productSummary.productsTable.region": "",
  "productSummary.productsTable.vatPercentage": "",
  "productSummary.subsidyTable.subsidy": "",
  "productSummary.subsidyTable.subsidyExVat": "",
  "productSummary.subsidyTable.subsidyIncVat": "",
  "productSummary.subsidyTable.ukSubsidyTitle": "",
  "productSummary.subsidyTable.vatPercentage": "",
  "productSummary.totalPriceTable.effectiveInterestRate": "",
  "productSummary.totalPriceTable.interestRate": "",
  "productSummary.totalPriceTable.monthlyCost": "",
  "productSummary.totalPriceTable.totalPrice": "",
  "productSummary.totalPriceTable.totalPriceExVat": "",
  "productSummary.totalPriceTable.totalPriceIncVat": "",
  "quotation.action.AWAITING_DESIGN": "",
  "quotation.action.COMPLETE_DESIGN": "",
  "quotation.action.COMPLETE_SURVEY": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_DOCUMENTATION_AND_APPLICATIONS": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.action.error.aborted": "",
  "quotation.action.error.cancelled": "",
  "quotation.action.error.dataLoss": "",
  "quotation.action.error.deadlineExceeded": "",
  "quotation.action.error.failedPrecondition": "",
  "quotation.action.error.internal": "",
  "quotation.action.error.invalidArgument": "",
  "quotation.action.error.notFound": "",
  "quotation.action.error.outOfRange": "",
  "quotation.action.error.permissionDenied": "",
  "quotation.action.error.resourceExhausted": "",
  "quotation.action.error.unauthenticated": "",
  "quotation.action.error.unavailable": "",
  "quotation.action.error.unimplemented": "",
  "quotation.action.error.unknownError": "",
  "quotation.action.INSTALLED": "",
  "quotation.action.lockingNotAllowed": "",
  "quotation.action.markAsOrderedNotAllowed": "",
  "quotation.action.REQUEST_SURVEY": "",
  "quotation.button.add": "",
  "quotation.button.resetProducts": "",
  "quotation.button.update": "",
  "quotation.button.viewGuide": "",
  "quotation.errorCard.checkDiscount.text": "",
  "quotation.errorCard.checkDiscount.title": "",
  "quotation.errorCard.paymentType": "",
  "quotation.errorCard.paymentTypeDescription": "",
  "quotation.label.action": "",
  "quotation.label.amountIncVat": "",
  "quotation.label.battery": "",
  "quotation.label.bufferTank": "",
  "quotation.label.compatibilityGroup": "",
  "quotation.label.defaultTaxRateDescription": "",
  "quotation.label.digging": "",
  "quotation.label.disableFinancing": "",
  "quotation.label.displayPricing": "",
  "quotation.label.displayTaxRateOverride": "",
  "quotation.label.editDefaultTaxRateDescription": "",
  "quotation.label.editOverrideTaxRateDescription": "",
  "quotation.label.electricalBonding": "",
  "quotation.label.enableFinancing": "",
  "quotation.label.evCharger": "",
  "quotation.label.financingTerm": "",
  "quotation.label.heatingCircuits": "",
  "quotation.label.heatingRoom": "",
  "quotation.label.heatingSystem": "",
  "quotation.label.heatPump": "",
  "quotation.label.heatPumpIndoor": "",
  "quotation.label.hidePricing": "",
  "quotation.label.hideTaxRateOverride": "",
  "quotation.label.hotWaterExpansionVessel": "",
  "quotation.label.indoorUnitOutdoorUnitDistance": "",
  "quotation.label.installationKit": "",
  "quotation.label.installationPackage": "",
  "quotation.label.isolatorFitting": "",
  "quotation.label.joinery": "",
  "quotation.label.liftingEquipment": "",
  "quotation.label.manifold": "",
  "quotation.label.mcs-estimate": "",
  "quotation.label.meterBoxChange": "",
  "quotation.label.oilTankRemoval": "",
  "quotation.label.oilTransport": "",
  "quotation.label.oilWallRemoval": "",
  "quotation.label.outdoorMounting": "",
  "quotation.label.outdoorUnitPipeConnection": "",
  "quotation.label.outdoorUnitWall": "Parete esterna per unità indipendente",
  "quotation.label.paymentMethod": "",
  "quotation.label.piping": "",
  "quotation.label.predefinedDiscounts": "",
  "quotation.label.qty": "",
  "quotation.label.quantity": "",
  "quotation.label.radiatorControl": "",
  "quotation.label.rePiping": "",
  "quotation.label.scaffolding": "",
  "quotation.label.solar": "",
  "quotation.label.status": "",
  "quotation.label.stoneWallDrilling": "",
  "quotation.label.systemDesign": "",
  "quotation.label.systemFlush": "",
  "quotation.label.taxRateOverride": "",
  "quotation.label.technicalSurvey": "",
  "quotation.label.temperatureZones": "",
  "quotation.label.totalCost": "",
  "quotation.label.totalCostExVat": "",
  "quotation.label.underfloorHeatingCommission": "",
  "quotation.label.uploadMCS": "",
  "quotation.linkToFinancialPortal": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.notify.performingAction": "",
  "quotation.notify.sendingQuote": "",
  "quotation.notify.unsavedChanges": "",
  "quotation.popup.cancel.confirm": "",
  "quotation.popup.cancel.content": "",
  "quotation.popup.financing.confirm": "",
  "quotation.popup.financing.content": "",
  "quotation.popup.financing.title": "",
  "quotation.popup.resendQuote.confirm": "",
  "quotation.popup.resendQuote.content": "",
  "quotation.popup.resetProducts.confirm": "",
  "quotation.popup.resetProducts.content": "",
  "quotation.popup.saveBeforeAction.confirm": "",
  "quotation.popup.saveBeforeAction.content": "",
  "quotation.popup.unlockPrice.confirm": "",
  "quotation.popup.unlockPrice.content": "",
  "quotation.popup.unlockPrice.title": "",
  "quotation.progress.designAcceptedAt": "",
  "quotation.progress.documentationAndApplicationsCompletedAt": "",
  "quotation.progress.financingSecuredAt": "",
  "quotation.progress.priceLockedAt": "",
  "quotation.progress.productsLockedAt": "",
  "quotation.progress.quoteAcceptedAt": "",
  "quotation.progress.quoteSentAt": "",
  "quotation.progress.techFeePaidAt": "",
  "quotation.progress.technicalDataCollectionCompletedAt": "",
  "quotation.status.CANCELLED": "",
  "quotation.status.cancelled": "",
  "quotation.status.expired": "",
  "quotation.status.installed": "",
  "quotation.status.missing": "",
  "quotation.status.OPEN_FOR_MODIFICATION": "",
  "quotation.status.openForModification": "",
  "quotation.status.ORDERED": "",
  "quotation.status.ordered": "",
  "quotation.status.PRICE_LOCKED": "",
  "quotation.status.priceLocked": "",
  "quotation.status.PRODUCTS_LOCKED": "",
  "quotation.status.productsLocked": "",
  "quotation.status.quoteExpirationAddition": "",
  "quotation.status.READY_FOR_ORDER": "",
  "quotation.status.readyForOrder": "",
  "quotation.table.description": "",
  "quotation.table.price": "",
  "quotation.table.quantity": "",
  "quotation.title.addons": "",
  "quotation.title.batteryPackage": "",
  "quotation.title.discounts": "",
  "quotation.title.evChargerPackage": "",
  "quotation.title.heatPumpPackage": "",
  "quotation.title.installationAddons": "",
  "quotation.title.insulation": "",
  "quotation.title.miscellaneous": "",
  "quotation.title.packages": "",
  "quotation.title.progress": "",
  "quotation.title.quotation": "",
  "quotation.title.radiators": "",
  "quotation.title.region": "",
  "quotation.title.solarPackage": "",
  "quotation.title.solarPanels": "",
  "quotation.title.subsidy": "",
  "quotation.title.subsidyAndPromotions": "",
  "quotation.title.taxRate": "",
  "quotation.title.total": "",
  "quotation.warning.goToHeatDesign": "",
  "quotation.warning.unlockToChange": "",
  "quotations.paymentMethod.INSTALMENTS": "",
  "quotations.paymentMethod.INVOICE": "",
  "quotations.paymentMethod.popup.p": "",
  "quotations.paymentMethod.SPLIT_INVOICE": "",
  "quotations.paymentMethod.UNDECIDED": "",
  "region.button.installation": "",
  "region.button.resources": "",
  "region.button.salesSurvey": "",
  "serviceBookingTool.body.resourceName": "",
  "serviceBookingTool.button.bookMaintenance": "",
  "serviceBookingTool.button.bookServiceJob": "",
  "serviceBookingTool.button.schedule.maintenance": "",
  "serviceBookingTool.emptyState.noInstallation": "",
  "serviceBookingTool.error.cannotBookServiceJob": "",
  "serviceBookingTool.error.maintenanceSchedulingFailure": "",
  "serviceBookingTool.error.partialAddress": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_CANCELLED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_FINISHED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_IN_PROGRESS": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_NOT_SCHEDULED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_READY": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_SCHEDULED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_UNSPECIFIED": "",
  "serviceBookingTool.jobStatus.UNRECOGNIZED": "",
  "serviceBookingTool.jobType.installThermostat": "",
  "serviceBookingTool.jobType.JOB_TYPE_ELECTRICAL_MAINTENANCE": "",
  "serviceBookingTool.jobType.JOB_TYPE_HEAT_PUMP_MAINTENANCE": "",
  "serviceBookingTool.jobType.JOB_TYPE_THERMOSTAT_MAINTENANCE": "",
  "serviceBookingTool.jobType.serviceElectricalSystem": "",
  "serviceBookingTool.jobType.serviceHeatPump": "",
  "serviceBookingTool.jobType.serviceJobType": "",
  "serviceBookingTool.label.duration": "",
  "serviceBookingTool.no.maintenance.scheduled": "",
  "serviceBookingTool.popup.unscheduleDescription": "",
  "serviceBookingTool.popup.unscheduleHeader": "",
  "serviceBookingTool.popup.unscheduleReason.customerCancelled": "",
  "serviceBookingTool.popup.unscheduleReason.customerNoShow": "",
  "serviceBookingTool.popup.unscheduleReason.description": "",
  "serviceBookingTool.popup.unscheduleReason.noLongerNeeded": "",
  "serviceBookingTool.popup.unscheduleReason.uncategorized": "",
  "serviceBookingTool.title.homeService": "",
  "surveysPlanning.suggestionsMap.cleanEnergyExperts": "",
  "surveysPlanning.suggestionsMap.filterByResource": "",
  "surveysPlanning.suggestionsMap.findOnMap": "",
  "surveysPlanning.suggestionsMap.showVisitsWithNoAssignedResources": "",
  "surveysPlanning.suggestionsMap.showVisitsWithNoDate": "",
  "surveysPlanning.suggestionsMap.technicalSurveyors": "",
  "upload.max-file-size": "",
  "upload.mcs.description": "",
  "upload.mcs.link.text": ""
};
