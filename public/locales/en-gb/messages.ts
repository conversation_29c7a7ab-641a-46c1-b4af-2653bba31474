// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = {
  "addressLookup.input.error": "Not a valid address.",
  "addressLookup.toggle.default": "Default",
  "addressLookup.toggle.description": "Address lookup provider:",
  "addressLookup.toggle.other": "Other",
  "arViews.button.openOneDrive": "Aira AR views",
  "arViews.button.openVaillantApp": "Vaillant AR app",
  "arViews.button.unit.100L_AIO": "Aira All in One (100L)",
  "arViews.button.unit.100L_buffer": "Aira buffer tank (100L)",
  "arViews.button.unit.12kW_outdoor": "Aira 12kW (3-phase)",
  "arViews.button.unit.150L_cylinder_slimline": "Aira hot water cylinder slimline (150L)",
  "arViews.button.unit.200L_cylinder": "Aira hot water cylinder (200L)",
  "arViews.button.unit.250L_AIO": "Aira All in One (250L)",
  "arViews.button.unit.250L_cylinder": "Aira hot water cylinder (250L)",
  "arViews.button.unit.300L_cylinder": "Aira hot water cylinder (300L)",
  "arViews.button.unit.40L_buffer": "Aira buffer tank (40L)",
  "arViews.button.unit.6kW_outdoor": "Aira 6kW",
  "arViews.button.unit.8kW_outdoor": "Aira 8kW",
  "arViews.button.unit.compact_hydrobox": "Aira Compact (Hydrobox)",
  "arViews.button.unit.thermostat": "Thermostat",
  "arViews.text.arDescription": "These tools work on iPad or iPhone only.",
  "arViews.text.howToAltDescription": "You can also access the same views as above using the OneDrive app. Open the Aira AR views folder to access each unit. As the files are quite large you can download them to your device before home visits to ensure they load faster.",
  "arViews.text.howToDescription": "To generate AR previews of Aira heat pump units, tap a link below. When the link opens, ensure that the 'AR' tab is selected and follow the instructions on the screen.",
  "arViews.text.vaillantHowToDescription": "Open the Vaillant AR app to generate AR previews of Vaillant heat pump units.",
  "arViews.title.airaHP": "Aira heat pumps",
  "arViews.title.arViews": "AR views",
  "arViews.title.bufferTanks": "Buffer tanks",
  "arViews.title.cylinders": "Hot water cylinders",
  "arViews.title.indoorUnits": "Indoor units",
  "arViews.title.outdoorUnits": "Outdoor units",
  "arViews.title.thermostat": "Thermostat",
  "arViews.title.vaillantHP": "Vaillant heat pumps",
  "baselineCalc.boolean.false": "No",
  "baselineCalc.boolean.true": "Yes",
  "baselineCalc.label.complexityElectricalWorks": "Complexity of electrical works",
  "baselineCalc.label.complexityExistingUnit": "Complexity of existing unit",
  "baselineCalc.label.complexityIndoorPlumbing": "Complexity of indoor plumbing",
  "baselineCalc.label.complexityOutdoorPlumbing": "Complexity of outdoor plumbing",
  "baselineCalc.label.indoorAndOutdoorDistance": "Indoor and outdoor distance",
  "baselineCalc.label.lengthElectricCableDuct": "Length of electric cable duct",
  "baselineCalc.label.modelVersion": "Model version",
  "baselineCalc.label.numberThermostats": "Number of thermostats",
  "baselineCalc.label.radiators": "Radiators",
  "baselineCalc.label.thermostaticValves": "Thermostatic valves",
  "baselineCalc.label.wallsThickness": "Wall's thickness",
  "baselineCalc.notify.error": "Error",
  "baselineCalc.notify.saving": "Saving...",
  "baselineCalc.notify.success": "Saved",
  "baselineCalc.table.macroActivity": "Macro activity",
  "baselineCalc.table.microActivity": "Micro activity",
  "baselineCalc.table.totalManHours": "Total man-hours",
  "baselineCalc.text.electricianHours": "Electrician hours",
  "baselineCalc.text.installationHours": "Installation hours",
  "baselineCalc.text.noModel": "There is currently no baseline model available for this country.",
  "baselineCalc.text.singleSaveWarning": "Once saved, this baseline calculation can no longer be changed.",
  "baselineCalc.text.totalManHours": "Total man-hours",
  "baselineCalc.title.byActivity": "By activity",
  "baselineCalc.title.byRole": "By role",
  "baselineCalc.title.calculateInstallationBaseline": "Calculate installation baseline",
  "baselineCalc.title.inputs": "Inputs",
  "baselineCalc.title.output": "Output",
  "baselineCalc.title.scenarioDefinition": "Scenario definition",
  "baselineCalc.title.summary": "Summary",
  "billOfMaterials.addBundle": "Add bundle",
  "billOfMaterials.addBundleCollection": "Add bundle collection",
  "billOfMaterials.addItemsToEquivalencyGroup.success": "Successfully set items to the same equivalency group",
  "billOfMaterials.addItemsToEquivalencyGroupTooltip": "Selected items will be marked as interchangeable and combined into a single row during procurement if they appear together.",
  "billOfMaterials.addVersion": "Add version",
  "billOfMaterials.advanced": "Advanced",
  "billOfMaterials.advancedSearch": "Advanced search",
  "billOfMaterials.advancedSearchTooltip": "Below you can find several items at once by providing an single ERP ID on every new row. Only exact matches will show.",
  "billOfMaterials.bulkSetLabelTooltip": "This will apply the chosen label from the dropdown to all selected items, overwriting the previous label if the item has one.",
  "billOfMaterials.bundleCollections": "Bundle collections",
  "billOfMaterials.bundles": "Bundles",
  "billOfMaterials.bundleSelect.description": "Bundles are a collection of materials that are needed for a specific installation. They are defined by your country and act as guidelines for you to properly assess what materials are needed for an installation.",
  "billOfMaterials.bundleSelect.error": "Error loading bundles",
  "billOfMaterials.bundleSelect.loading": "Loading bundles...",
  "billOfMaterials.bundleSelect.outdatedBundle": "The selected bundle has been changed since this BOM was marked ready for procurement. It would be advisable to review the changes by unmarking the BOM",
  "billOfMaterials.bundleSelect.title": "Select bundles based on your heat design",
  "billOfMaterials.clearInterchangeable.confirmDescription": "The selected items will have their all their interchangeabilities removed, and won’t be linked to any other item.",
  "billOfMaterials.clearInterchangeable.confirmTitle": "Clear interchangeability?",
  "billOfMaterials.confirmCloseEdit.description": "You have unsaved changes. Are you sure you want to close without saving?",
  "billOfMaterials.confirmCloseEdit.title": "Discard changes?",
  "billOfMaterials.confirmDeleteBundle.description": "Are you sure you want to delete this bundle?",
  "billOfMaterials.confirmDeleteBundle.title": "Delete bundle?",
  "billOfMaterials.confirmDeleteCollection.description": "Are you sure you want to delete this collection?",
  "billOfMaterials.confirmDeleteCollection.title": "Delete collection?",
  "billOfMaterials.confirmDeleteItem.description": "Are you sure you want to remove this item from the bundle?",
  "billOfMaterials.confirmDeleteItem.title": "Delete item?",
  "billOfMaterials.copyBundle": "Duplicate bundle",
  "billOfMaterials.copyVersion": "Duplicate version",
  "billOfMaterials.costOfAllItems": "Hardware cost",
  "billOfMaterials.costOfAllItems.info": "Of which:\n- Cost of ordered items: {costOfOrderedItems}\n- Cost of AIK: {costOfAIKs}\n- Cost of estimate of consumed van stock: {costOfEstimatedConsumedVanStock}",
  "billOfMaterials.deleteBundle": "Delete bundle",
  "billOfMaterials.deleteCollection": "Delete collection",
  "billOfMaterials.deleteItem": "Delete item",
  "billOfMaterials.deleteItemEquivalence.confirm.description": "These items will no longer be connected & marked as interchangeable",
  "billOfMaterials.deleteItemEquivalence.confirm.title": "Remove connection?",
  "billOfMaterials.deleteVersion": "Delete version",
  "billOfMaterials.design.hlc.itemNotFoundInERP": "This item could not be added to the bill of materials automatically. Please add or check that this item has been added in the miscellaneous section.",
  "billOfMaterials.editBundle": "Edit bundle",
  "billOfMaterials.editBundle.browseItems": "Browse items",
  "billOfMaterials.editBundle.descriptionFieldLabel": "Description",
  "billOfMaterials.editBundle.itemsInBundle": "Items in bundle",
  "billOfMaterials.editBundle.itemsInInstallationKit": "Items contained in the AIK",
  "billOfMaterials.editBundle.itemsInInstallationKitTooltip": "These items are already included in the AIK and will not be ordered separately. They are listed here for reference during final procurement.",
  "billOfMaterials.editBundle.itemsInVersion": "Items in version",
  "billOfMaterials.editBundle.itemsTable.amountColumn": "Qty",
  "billOfMaterials.editBundle.itemsTable.amountColumn.infoBox": "The 'Qty' value sets a default that users can modify when creating the BoM. Leave it blank to let users enter a value without a default. Include an item instruction for more clarity.",
  "billOfMaterials.editBundle.itemsTable.descritionColumn": "Description",
  "billOfMaterials.editBundle.itemsTable.erpId": "ERP ID",
  "billOfMaterials.editBundle.itemsTable.instructionsColumn": "Instructions",
  "billOfMaterials.editBundle.itemsTable.nameColumn": "Item name",
  "billOfMaterials.editBundle.titleAdd": "Add bundle",
  "billOfMaterials.editBundle.titleEdit": "Edit bundle",
  "billOfMaterials.editBundle.titleFieldLabel": "Title",
  "billOfMaterials.editBundleCollection.descriptionFieldLabel": "Description",
  "billOfMaterials.editBundleCollection.titleAdd": "Create new bundle collection",
  "billOfMaterials.editBundleCollection.titleEdit": "Edit bundle collection",
  "billOfMaterials.editBundleCollection.titleFieldLabel": "Title",
  "billOfMaterials.editCollection": "Edit collection",
  "billOfMaterials.editVersion": "Edit version",
  "billOfMaterials.editVersion.titleAdd": "Add version",
  "billOfMaterials.editVersion.titleEdit": "Edit version",
  "billOfMaterials.grossMarginTwo": "Gross Margin 2",
  "billOfMaterials.grossMarginTwo.info": "Shows the margin after the hardware costs are subtracted from the net revenue (ex. VAT). This margin should be above 50% and is calculated as:\n\nGross margin 2 = (net revenue - hardware costs) ÷ net revenue.\n\nWhere:\n- net revenue = design cost (ex. VAT) + subsidies = {designCost} + {subsidies}",
  "billOfMaterials.installationKit.AikItems": "AIK ERP ID(s)",
  "billOfMaterials.installationKit.itemNotUsedElsewhere": "This item is not used in any template bundle",
  "billOfMaterials.installationKit.setAikItems": "Set AIK ERP ID(s)",
  "billOfMaterials.interchangeability": "Interchangeability",
  "billOfMaterials.itemCatalogue.duplicates.description": "The selected items contain duplicate ERP IDs, possibly due to different versions of the same item. Please verify if this is intentional, or deselect the items you don't want and continue.",
  "billOfMaterials.itemCatalogue.filter.category": "Category",
  "billOfMaterials.itemCatalogue.filter.search": "Search",
  "billOfMaterials.itemCatalogue.select": "Select",
  "billOfMaterials.itemCatalogue.subtitle": "ERP catalogue",
  "billOfMaterials.itemCatalogue.table.archived": "This item has been removed from the ERP and can no longer be ordered. Use a replacement item.",
  "billOfMaterials.itemCatalogue.table.category": "Category",
  "billOfMaterials.itemCatalogue.table.costPerUnit": "Cost per unit",
  "billOfMaterials.itemCatalogue.table.description": "Description",
  "billOfMaterials.itemCatalogue.table.details": "Details",
  "billOfMaterials.itemCatalogue.table.erpId": "ERP ID",
  "billOfMaterials.itemCatalogue.table.partOfBundles": "Part of bundles:",
  "billOfMaterials.itemCatalogue.table.supplier": "Supplier",
  "billOfMaterials.itemCatalogue.table.version": "Version",
  "billOfMaterials.itemCatalogue.title": "Add item to bundle",
  "billOfMaterials.itemDetails": "Item details",
  "billOfMaterials.itemRelations": "Item relations",
  "billOfMaterials.labels.addNew": "Add new label",
  "billOfMaterials.labels.newLabel": "New label",
  "billOfMaterials.labels.selectLabel": "Select label",
  "billOfMaterials.mandatoryBundle": "Mandatory bundle collection",
  "billOfMaterials.mandatoryBundleTooltip": "Select if this bundle collection is required for designers. If not marked mandatory, designers can choose to skip selecting a bundle from this collection.",
  "billOfMaterials.miscellaneousItems.addCustomItem": "Add custom item",
  "billOfMaterials.moveDown": "Move down",
  "billOfMaterials.moveUp": "Move up",
  "billOfMaterials.quantity": "Quantity",
  "billOfMaterials.removeItemsFromEquivalencyGroup.success": "Successfully removed equivalence groups from selected items",
  "billOfMaterials.setInterchangeable.confirmDescription": "Selected items will be marked as interchangeable and combined into a single row during procurement if they appear together.",
  "billOfMaterials.setInterchangeable.confirmTitle": "Set selected items as interchangeable?",
  "billOfMaterials.setLabel": "Set label",
  "billOfMaterials.showLess": "Show less",
  "billOfMaterials.showMore": "Show more",
  "billOfMaterials.unit": "Unit",
  "billOfMaterials.vanStock.duplicateError": "This combination of stock type and regions already exists in bundle \"{duplicateBundleName}\"",
  "billOfMaterials.vanStock.regions": "Regions",
  "billOfMaterials.vanStock.selectAllRegions": "Select All Regions",
  "billOfMaterials.vanStock.selectRegions": "Select regions",
  "billOfMaterials.vanStock.stockType": "Stock Type",
  "billOfMaterials.versions": "Versions",
  "billOfMaterialsDesignPage.addMiscellaneousItems": "Add miscellaneous items",
  "billOfMaterialsDesignPage.addMiscellaneousItemsDescription": "Add items that are not in any bundle but are needed for this job.",
  "billOfMaterialsDesignPage.Items": "Items",
  "billOfMaterialsDesignPage.notification.bomLocked": "Locked BoM",
  "billOfMaterialsDesignPage.notification.locking": "Locking bill of materials...",
  "billOfMaterialsDesignPage.notification.locking.error": "Error locking bill of materials",
  "billOfMaterialsDesignPage.notification.locking.success": "Successfully locked bill of materials",
  "billOfMaterialsDesignPage.notification.markingAsReady": "Marking as ready for procurement...",
  "billOfMaterialsDesignPage.notification.markingAsReady.error": "Error marking as ready for procurement",
  "billOfMaterialsDesignPage.notification.markingAsReady.success": "Marked as ready for procurement",
  "billOfMaterialsDesignPage.notification.saving": "Saving bill of materials...",
  "billOfMaterialsDesignPage.notification.saving.error": "Error saving bill of materials",
  "billOfMaterialsDesignPage.notification.saving.success": "Successfully saved bill of materials",
  "billOfMaterialsDesignPage.notification.unlocking": "Unlocking bill of materials...",
  "billOfMaterialsDesignPage.notification.unlocking.error": "Error unlocking bill of materials",
  "billOfMaterialsDesignPage.notification.unlocking.success": "Successfully unlocked bill of materials",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady": "Unmarking as ready for procurement...",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.error": "Error unmarking as ready for procurement",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.success": "Unmarked as ready for procurement",
  "billOfMaterialsDesignPage.readyForProcurement": "This was marked as ready for procurement at {timestamp}.",
  "billOfMaterialsDesignPage.saveAndLock.button": "Save & lock",
  "billOfMaterialsDesignPage.saveAndMarkAsReady": "Save & mark as ready for procurement",
  "billOfMaterialsDesignPage.status.readyForProcurement": "Ready for procurement",
  "billOfMaterialsDesignPage.title": "Bill of Materials",
  "billOfMaterialsDesignPage.unlock.button": "Unlock Bill of Materials",
  "billOfMaterialsDesignPage.unmarkAsReady": "Unmark as ready for procurement",
  "booking.button.schedule.survey": "Schedule a new survey",
  "booking.no.survey.scheduled": "No surveys added yet…",
  "bookingTool.body.completedOn": "Completed on",
  "bookingTool.body.dateAndTime": "{date}, {startTime}-{endTime}",
  "bookingTool.body.loading": "Loading...",
  "bookingTool.body.noTimeSlotsAvailable": "No time slots available, select another day.",
  "bookingTool.body.scheduledForDateAndTime": "Scheduled for",
  "bookingTool.body.scheduledForTimeWindow": "Scheduled between",
  "bookingTool.body.surveyorName": "Surveyor: ",
  "bookingTool.body.surveyUnscheduled": "Unscheduled",
  "bookingTool.body.unsavedChanges": "Unsaved changes!",
  "bookingTool.button.bookJob": "Create home survey",
  "bookingTool.button.goToHouseDataTab": "Go to house data tab",
  "bookingTool.button.hideScheduler": "Hide scheduler",
  "bookingTool.button.openMap": "Open map",
  "bookingTool.button.salesVisitMap": "Sales visit map",
  "bookingTool.button.scheduleSurvey": "Reschedule the existing survey",
  "bookingTool.button.unschedule": "Unschedule",
  "bookingTool.checkbox.battery": "Battery",
  "bookingTool.checkbox.photovoltaic": "Photovoltaic",
  "bookingTool.checkbox.solarThermal": "Solar thermal",
  "bookingTool.deactivatedUser": "Deactivated user",
  "bookingTool.error.jobSchedulingFailure": "An error occured when trying to schedule the job.",
  "bookingTool.error.magicplanAccountNeeded": "Magicplan account needed",
  "bookingTool.error.noAddress": "Unable to find address for energy solution. An address is needed to schedule surveys. Please add the address in the house data tab.",
  "bookingTool.error.noResourceSelected": "No resource selected",
  "bookingTool.error.noTimeSlotSelected": "No time slot selected",
  "bookingTool.error.resourceNotAvailable": "Resource not available for this combination of skills and roles",
  "bookingTool.label.assignSpecificPerson": "Assign specific person?",
  "bookingTool.label.dateAndTime": "Date and time",
  "bookingTool.label.installer": "Installer",
  "bookingTool.label.magicPlan": "Magicplan",
  "bookingTool.label.mobileLink": "Mobile",
  "bookingTool.label.notes": "Notes:",
  "bookingTool.label.salesSurvey": "Sales survey",
  "bookingTool.label.salesSurveyor": "Sales surveyor",
  "bookingTool.label.skills": "Additional energy solutions to survey for.",
  "bookingTool.label.surveyBookings": "Sales/Technical Survey",
  "bookingTool.label.surveyDuration": "Survey duration",
  "bookingTool.label.surveyorType": "Surveyor type",
  "bookingTool.label.surveyPDF": "Survey PDF:",
  "bookingTool.label.technicalSurvey": "Technical survey",
  "bookingTool.label.technicalSurveyor": "Technical surveyor",
  "bookingTool.label.timeSlotsAvailable": "Time slots available: ",
  "bookingTool.label.unknown": "Unknown",
  "bookingTool.label.videoSalesMeeting": "Video Sales Meeting",
  "bookingTool.label.webLink": "Web",
  "bookingTool.placeholder.selectPerson": "Select person",
  "bookingTool.popup.unscheduleDescription": "This unschedules the booking time and removes the assigned resource.",
  "bookingTool.popup.unscheduleHeader": "Unschedule booking",
  "bookingTool.radio.both": "Both",
  "bookingTool.radio.duration": "{hours, plural, =0{} one{# hour} other{# hours}} {minutes, plural, =0{} one{# minute} other{# minutes}}",
  "bookingTool.radio.salesSurveyor": "Sales surveyor",
  "bookingTool.radio.technicalSurveyor": "Technical surveyor",
  "bookingTool.skillsChip.BATTERY": "Battery",
  "bookingTool.skillsChip.HEAT_PUMP": "Heat pump",
  "bookingTool.skillsChip.PHOTOVOLTAIC": "Photovoltaic",
  "bookingTool.skillsChip.SOLAR_THERMAL": "Solar thermal",
  "bookingTool.surveyStatus.CANCELLED": "Cancelled",
  "bookingTool.surveyStatus.FINISHED": "Finished",
  "bookingTool.surveyStatus.IN_PROGRESS": "In progress",
  "bookingTool.surveyStatus.NOT_SCHEDULED": "Not scheduled",
  "bookingTool.surveyStatus.READY": "Ready",
  "bookingTool.surveyStatus.SCHEDULED": "Scheduled",
  "bookingTool.surveyStatus.UNKNOWN": "Unknown",
  "bookingTool.tab.setTimeLater": "Set time later",
  "bookingTool.tab.specificTime": "Specific time",
  "bookingTool.tab.timeWindow": "Time window",
  "bookingTool.timeWindow.afternoon": "Afternoon",
  "bookingTool.timeWindow.morning": "Morning",
  "bookingTool.title.homeSurveys": "Home surveys",
  "bookingTool.title.noAddress": "No address",
  "bookingTool.title.salesSurvey": "Sales survey",
  "bookingTool.title.scheduledBookings": "Home visits",
  "bookingTool.title.scheduleSuccess": "Survey scheduled successfully!",
  "bookingTool.title.summary": "Summary",
  "bookingTool.title.technicalSurvey": "Technical survey",
  "bookingTool.title.videoSalesBooking": "Video sales booking",
  "common.aira.nothing": "Remove this translation.",
  "common.country.DE": "Germany",
  "common.country.GB": "United Kingdom",
  "common.country.GB.SCT": "Scotland",
  "common.country.IT": "Italy",
  "common.deprecated": "deprecated",
  "common.error.contactDevelopmentTeam": "If this error persists, please contact the development team and supply the following information:",
  "common.error.errorDownloadingSignedQuote": "An error occurred while trying to download the signed quote",
  "common.error.installationProjectNotFound.title": "Installation project not found",
  "common.error.missing": "Missing",
  "common.error.pageNotFound.description": "Nothing is available at this address.",
  "common.error.pageNotFound.title": "Page not found",
  "common.error.retry": "Retry",
  "common.error.schedulingNotSupported": "Scheduling is not currently supported for region: {region}",
  "common.error.unableToFindEnergySolution": "Unable to find energy solution",
  "common.error.unknown": "Unknown error.",
  "common.file.browse.text": "Select from files",
  "common.file.drag&drop.text": "Drag & drop here or",
  "common.label.addItems": "Add items",
  "common.label.address": "Address",
  "common.label.address.google": "Address (Google)",
  "common.label.address.loqate": "Address (Loqate)",
  "common.label.beta": "Beta",
  "common.label.cancel": "Cancel",
  "common.label.catalogueRadiator": "Catalogue radiator",
  "common.label.clear": "Clear",
  "common.label.comment": "Comment",
  "common.label.confirm": "Confirm",
  "common.label.confirmed": "Confirmed",
  "common.label.continue": "Continue",
  "common.label.copyText": "Copy",
  "common.label.copyURL": "Copy URL",
  "common.label.date": "Date",
  "common.label.delete": "Delete",
  "common.label.description": "Description",
  "common.label.discard": "Discard",
  "common.label.download": "Download",
  "common.label.downloadPDF": "Download PDF",
  "common.label.edit": "Edit",
  "common.label.emailAddress": "Email address",
  "common.label.fabric": "Fabric",
  "common.label.hlcDisplayName": "Display Name",
  "common.label.items": "Items",
  "common.label.label": "Label",
  "common.label.latest": "Latest",
  "common.label.load": "Load",
  "common.label.loggedInAsEmail": "You are logged in as {email}.",
  "common.label.measurement.area": "Area",
  "common.label.measurement.height": "Height",
  "common.label.measurement.heightmm": "Height (mm)",
  "common.label.measurement.length": "Length",
  "common.label.measurement.lengthmm": "Length (mm)",
  "common.label.measurement.width": "Width",
  "common.label.measurement.widthmm": "Length (mm)",
  "common.label.name": "Name",
  "common.label.next": "Next",
  "common.label.none": "None",
  "common.label.order": "Order",
  "common.label.phoneNumber": "Phone number",
  "common.label.preliminary": "Preliminary",
  "common.label.radiators": "Radiators",
  "common.label.reload": "Reload",
  "common.label.room": "Room",
  "common.label.save": "Save",
  "common.label.sendEmail": "Send email",
  "common.label.set": "Set",
  "common.label.surveyor": "Surveyor",
  "common.label.type": "Type",
  "common.label.unknown": "Unknown",
  "common.link.arViews": "AR views",
  "common.link.assistant": "Assistant",
  "common.link.back": "Back",
  "common.link.baselineCalculator": "Baseline Calculator",
  "common.link.booking": "Scheduling",
  "common.link.contact": "Create lead",
  "common.link.designTools": "Design tools",
  "common.link.heatDesign": "Heat design",
  "common.link.heatPumpConfig": "Aira Heat Pump configuration",
  "common.link.houseData": "House data",
  "common.link.installation": "Installation",
  "common.link.installationHandover": "Handover",
  "common.link.installationReport": "Report",
  "common.link.installationReview": "Booking / review",
  "common.link.invoicing": "invoicing",
  "common.link.next": "Next",
  "common.link.orderSummary": "Product summary ›",
  "common.link.print": "Print",
  "common.link.productSummary": "Product summary",
  "common.link.quotation": "Quotation",
  "common.link.quote": "Quote",
  "common.link.salesSurvey": "Sales survey",
  "common.link.service": "Service",
  "common.link.survey": "Survey",
  "common.link.technicalSurvey": "Technical survey",
  "common.link.videoCall": "Video call",
  "common.link.videoSalesBooking": "Video sales booking",
  "common.max": "Max",
  "common.min": "Min",
  "common.minutes": "Minutes",
  "common.months": "months",
  "common.no": "No",
  "common.note.label": "Note",
  "common.notify.accessDenied": "Access denied",
  "common.notify.appealAccessDenied": "Please reach out to the development team if you believe you should have access.",
  "common.notify.copySuccess": "Copied to clipboard",
  "common.notify.error": "Something went wrong!",
  "common.notify.invitationSent": "Invitation sent",
  "common.notify.loading": "Loading...",
  "common.notify.loadingContent": "Loading content...",
  "common.notify.loadSuccess": "Loaded successfully",
  "common.notify.saveSuccess": "Saved successfully",
  "common.notify.success": "Success, contact is created",
  "common.notify.unsupportedArea": "Address not supported, select region",
  "common.pageOfTotal": "Page {currentPage} of {pageTotal}",
  "common.select.upload-file": "Click to select a file",
  "common.test.demo": "hello tech demo forum! ",
  "common.unlock": "Unlock",
  "common.years": "years",
  "common.yes": "Yes",
  "configuration.product.confirm": "Confirm",
  "configuration.product.description": "Confirm updates to {numberOfProducts} products in {country}.",
  "configuration.product.title": "Product configuration",
  "contact.facility.createNew": "Create new",
  "contact.facility.placeholder": "Choose a facility...",
  "contact.houseData.cannotBeReused": "cannot be reused",
  "contact.houseData.facility": "your current facility",
  "contact.houseUnitSource.custom": "Custom...",
  "contact.houseUnitSource.guestHouse": "Guest house",
  "contact.houseUnitSource.leftWing": "Left wing",
  "contact.houseUnitSource.mainHouse": "Main house",
  "contact.houseUnitSource.rightWing": "Right wing",
  "contact.houseUnitSource.standard": "Standard",
  "contact.label.addHouseData": "Add consumption details",
  "contact.label.country": "Country",
  "contact.label.customerEmail": "Customer email",
  "contact.label.customerInformation": "Customer information",
  "contact.label.duplicateEmail": "This customer already exists. Are you sure you want to proceed?",
  "contact.label.emailAddress": "Email address",
  "contact.label.external.duplicateEmail": "This lead already exists in our system, due to that there is not possibility to recreate that. Please reach out to your regional contact to move forward.",
  "contact.label.facility": "Facility",
  "contact.label.firstName": "First name",
  "contact.label.hideHouseData": "Hide consumption details",
  "contact.label.houseData": "House data",
  "contact.label.lastName": "Last name",
  "contact.label.leadSource": "Lead source",
  "contact.label.number": "Number",
  "contact.label.region": "Region/Hub",
  "contact.label.required": "This field is mandatory",
  "contact.label.submit": "Submit",
  "contact.leadSource.canvasing": "Canvasing",
  "contact.leadSource.other": "Other",
  "contact.leadSource.referral": "Referral",
  "contact.leadSource.required": "Required",
  "contact.notification.addressAndFacilityUsed": "This address and facility have already been used by another customer and cannot be reused.",
  "contact.notification.addressMultipleBuildings": "An address can have multiple buildings.",
  "contact.notification.addressUsed": "This address is already used by another customer.",
  "contact.notification.canBeReused": "can be reused",
  "contact.notification.cannotBeReused": "can be reused",
  "contact.notification.enterValidEmail": "Enter a valid email address",
  "contact.notification.facilityAlreadyUsed": "Facility is already used",
  "contact.notification.facilityCanBeReused": "This facility already has a project. Please check the link below to reuse it.",
  "contact.notification.facilityDescription": "Pick a facility from the dropdown or add a new one",
  "contact.notification.facilityInfo": "Some addresses include multiple units. Selecting a facility ensures accurate service.",
  "contact.notification.isAddressContainer": "The selected address contains multiple addresses. Please select the correct one.",
  "contact.notification.unsupportedArea": "Address not supported, select region",
  "contact.notification.writeFacilityName": "Write your facility name or choose one from the list to reuse",
  "contact.placeholder.newFacilityName": "Enter a new facility name",
  "dashboard.button.addLead": "Add lead",
  "dashboard.button.contact": "Create lead",
  "dashboard.button.installationProject": "Installation project",
  "dashboard.button.quotation": "Quotation",
  "dashboard.button.salesVisitMap": "Sales visit map",
  "dashboard.customerDetails": "Customer details",
  "dashboard.filter.accepted": "Accepted",
  "dashboard.filter.all": "All",
  "dashboard.filter.notAccepted": "Not accepted yet",
  "dashboard.filter.openForModification": "Open for modification",
  "dashboard.filter.priceLocked": "Price locked",
  "dashboard.filter.productsLocked": "Products locked",
  "dashboard.filters.show": "Show",
  "dashboard.noOngoingWork": "No ongoing work",
  "dashboard.noOngoingWorkAccordingToFilters": "No ongoing work according to selected filters.",
  "dashboard.solutionStatus.CANCELLED": "Cancelled",
  "dashboard.solutionStatus.OPEN_FOR_MODIFICATION": "Open for modification",
  "dashboard.solutionStatus.openForModification": "Open for modification",
  "dashboard.solutionStatus.ORDERED": "Ordered",
  "dashboard.solutionStatus.PRICE_LOCKED": "Price locked",
  "dashboard.solutionStatus.priceLocked": "Price locked",
  "dashboard.solutionStatus.PRODUCTS_LOCKED": "Products locked",
  "dashboard.solutionStatus.productsLocked": "Products locked",
  "dashboard.solutionStatus.READY_FOR_ORDER": "Ready for order",
  "dashboard.solutionStatus.readyForOrder": "Ready for order",
  "dashboard.status": "Status",
  "dashboard.title.overview": "Overview",
  "error.hlc.product.selection.incompatibleTankSize.description": "With the 12kW outdoor unit selected, only a 250 litre domestic hot water tank can be used.",
  "error.hlc.product.selection.incompatibleTankSize.heading": "Incompatible Tank Size",
  "error.hlc.unlock.invalidState": "Solutions with state <b>{state}</b> cannot be unlocked. \n\nChanging the solution's state in the Quotation Page may allow unlocking, but may also cause side-effects such as order adjustment or customer re-approval.",
  "error.support.card.askHelpText": "Ask for help in Microsoft Teams",
  "error.support.card.description": "An error occurred when saving your project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "error.support.card.description.lock": "An error occurred when locking your project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "error.support.card.description.save": "An error occurred when saving your project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "error.support.card.description.unlock": "An error occurred when unlocking your project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "error.support.card.hlc.locked.description": "The project cannot be saved because it is locked. Please unlock the project by choosing 'unlock products' and try again.\n\nIf this doesn't work, please create a support ticket in the “Design Engineering Community” in Microsoft Teams.",
  "error.support.card.hlc.locked.heading": "Cannot save a locked project",
  "error.support.card.hlc.product.selection.description": "Product changes cannot be saved, this could be due to the downgrade of the product. Everything up to this point is saved, you can manually go into Quotation Tool to properly downgrade and change the price before proceeding in the heat loss calculation. \n\nPlease try the above first, and if this doesn't work, please create a support ticket in the “Design Engineering Community” in Microsoft Teams.",
  "error.support.card.hlc.product.selection.heading": "Failed to save product changes",
  "error.support.card.hlc.sendDesignReviewEmail.description": "An error occurred when trying to send an email to the customer. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "error.support.card.hlc.sendDesignReviewEmail.heading": "Failed to send email",
  "error.support.card.hlc.sendDesignReviewEmail.wrongState.description": "This solution is not in the correct state for the customer design approval email to be sent. For example, the quote must have been sent to and approved by the customer first. You can check the state of this solution on the quotation page. \n\nOnce the solution is in the correct state, you can try sending the customer design approval email again.",
  "error.support.card.subTitle": "Copy this information and paste in the support ticket",
  "error.support.card.title": "Project failed to save",
  "error.support.card.title.lock": "Project failed to lock",
  "error.support.card.title.save": "Project failed to save",
  "error.support.card.title.unlock": "Project failed to unlock",
  "floorPlans.error.noDwellingInProject": "No dwelling found in the project",
  "floorPlans.error.noFloorsInProject": "No floors found for project",
  "floorPlans.symbols.bufferTank": "Buffer tank",
  "floorPlans.symbols.cylinder": "Cylinder",
  "floorPlans.symbols.expansionVessel": "Expansion vessel",
  "floorPlans.symbols.indoorUnit": "Indoor unit",
  "floorPlans.symbols.outdoorUnit": "Outdoor unit",
  "floorPlans.symbols.thermostat": "Thermostat",
  "floorPlans.title.floorPlan": "Floor Plan",
  "form-redirect.manual-redirect.title": "Redirect not working?",
  "form-redirect.status.aerospace.failure": "Failed to load Aerospace data. Please try again. If the problem persists, please contact support.",
  "form-redirect.status.aerospace.header": "Loading Aerospace data...",
  "form-redirect.status.aerospace.success": "Successfully loaded Aerospace data.",
  "form-redirect.status.magicplan.check.failure": "Error when checking Magicplan project. Please try again. If the problem persists, please contact support.",
  "form-redirect.status.magicplan.check.header": "Checking Magicplan project...",
  "form-redirect.status.magicplan.check.incorrect": "This Magicplan project might not be configured correctly.",
  "form-redirect.status.magicplan.check.success": "Magicplan project looks good. Redirecting to the new Magicplan project...",
  "form-redirect.status.magicplan.create.failure": "Error creating Magicplan project. Please try again. If the problem persists, please contact support.",
  "form-redirect.status.magicplan.create.header": "Creating Magicplan project...",
  "form-redirect.status.magicplan.create.notice": "Error creating Magicplan project.\n\nThe surveyor assigned to this survey does not have a Magicplan account. Please request a Magicplan account through Aira Breeze → Digital workplace support → Software License Request and try again when they have an account.",
  "form-redirect.status.magicplan.create.success": "Created a new Magicplan project. Redirecting to the new Magicplan project...",
  "form-redirect.status.magicplan.failure": "Error when finding Magicplan project. Please try again. If the problem persists, please contact support.",
  "form-redirect.status.magicplan.header": "Finding Magicplan project...",
  "form-redirect.status.magicplan.not-found": "No Magicplan project found for this Aerospace project.",
  "form-redirect.status.magicplan.success": "A Magicplan project is already linked to this Aerospace project. Redirecting to Magicplan...",
  "form-redirect.status.magicplan.user-mismatch.advice": "To be able to do a technical survey, you need to be logged in to Magicplan with the same user that is assigned to the Magicplan project.",
  "form-redirect.status.magicplan.user-mismatch.aerospace-user": "But you are logged in to Aerospace as:",
  "form-redirect.status.magicplan.user-mismatch.alternative": "Alternatively, if you know what you are doing, you can open the Magicplan project without reassigning it on:",
  "form-redirect.status.magicplan.user-mismatch.magicplan-user": "The Magicplan project is currently assigned to:",
  "form-redirect.status.magicplan.user-mismatch.reassign.failure": "Failed to reassign Magicplan project. Please try again. If this problem persists, please contact support.",
  "form-redirect.status.magicplan.user-mismatch.reassign.success": "Magicplan project successfully reassigned. Redirecting to new Magicplan project...",
  "form-redirect.title": "Magicplan Checker",
  "handoverSolution.tabs.externalLinks": "Extra documents",
  "handoverSolution.tabs.floorPlan": "Floor plan",
  "handoverSolution.tabs.hardware": "Hardware",
  "handoverSolution.tabs.overview": "Overview",
  "handoverSolution.tabs.radiators": "Radiators",
  "handoverSolution.tabs.schematics": "Schematics",
  "heatDesign.additionalLossTypes.exposedLocation": "Exposed location",
  "heatDesign.additionalLossTypes.highCeiling": "High ceiling",
  "heatDesign.additionalLossTypes.intermittentHeating": "Intermittent heating",
  "heatDesign.additionalLossTypes.thermalBridging": "Thermal bridging",
  "heatDesign.adjacentKind.heated": "Heated",
  "heatDesign.adjacentKind.outside": "Outside",
  "heatDesign.adjacentKind.room": "Room",
  "heatDesign.adjacentKind.soil": "Against soil",
  "heatDesign.adjacentKind.solidFloor": "Solid floor",
  "heatDesign.adjacentKind.suspendedFloor": "Suspended floor",
  "heatDesign.adjacentKind.unheated": "Unheated",
  "heatDesign.billOfMaterial.summary.miscellaneousAddition": "Miscellaneous addition",
  "heatDesign.billOfMaterials.heatDesignSummary.accessoriesTitle": "Radiator accessories",
  "heatDesign.billOfMaterials.heatDesignSummary.appliancesTitle": "Aira appliances",
  "heatDesign.billOfMaterials.heatDesignSummary.customRadiatorTooltip": "This radiator is custom and must be manually procured, as it cannot be ordered through the ERP.",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model\n": "Model",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model": "Model",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.quantity": "Qty",
  "heatDesign.billOfMaterials.heatDesignSummary.summary": "Appliances and radiators selected during Product Selection & Emitter Sizing. Radiators are auto-added to the bill of materials; appliances are added via bundles.",
  "heatDesign.billOfMaterials.heatDesignSummary.title": "From Heat Design",
  "heatDesign.billOfMaterials.heatDesignSummary.updatedWarning": "Heat design has been updated, please double-check the bill of materials",
  "heatDesign.billOfMaterials.miscellaneousItemsTable.customItemTooltip": "This item is custom and must be manually procured, as it cannot be ordered through the ERP.",
  "heatDesign.billOfMaterials.notLockedWarning": "Heat design is not locked",
  "heatDesign.billOfMaterials.summary.cost": "Cost ({symbol})",
  "heatDesign.billOfMaterials.summary.designedPrice": "Designed price",
  "heatDesign.billOfMaterials.summary.itemDeletedFromErp": "This item has been removed from the ERP and can no longer be ordered. Notify your Head of Design to update the bundle collections.",
  "heatDesign.billOfMaterials.summary.itemsPerUnit": "Items per unit",
  "heatDesign.billOfMaterials.summary.prefilledFromHeatDesign": "Pre-filled from Aira Heat Design",
  "heatDesign.billOfMaterials.summary.salesPrice": "Quoted price",
  "heatDesign.cancelRadiatorEdit.detail": "Do you want to discard your changes?",
  "heatDesign.cancelRadiatorEdit.title": "Discard changes",
  "heatDesign.climate.data.error": "There was an issue loading the climate data for your project. \n\nThis can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "heatDesign.climate.data.error.title": "Project failed to load - Unable to fetch climate data",
  "heatDesign.common.erpId": "ERP ID",
  "heatDesign.customerAcceptance.sendEmailConfirmation": "Are you sure you want to request customer approval via email?",
  "heatDesign.customerReport.button": "Copy URL to Final Spec",
  "heatDesign.customerReport.calculationResults.flowTemperature": "The flow temperature of water through your <b>radiators</b>.",
  "heatDesign.customerReport.calculationResults.heatLossColdestDay": "Your home's heat loss and heat pump output at the lowest expected temperature at your property (<b>{temp} ⁰C</b>).",
  "heatDesign.customerReport.calculationResults.showerTime": "Your blissfully <b>hot shower time</b>. If all the hot water has been used, it will take <b>{reheatTime}</b> minutes to reheat the tank.",
  "heatDesign.customerReport.calculationResults.title": "Our calculation results for your home",
  "heatDesign.customerReport.calculationResults.ufhFlowTemperature": "The flow temperature of water through your <b>underfloor heating</b>.",
  "heatDesign.customerReport.comment.label": "Comments regarding the installation",
  "heatDesign.customerReport.comment.placeholder": "e.g. reasons for the pump location, requirements for installing indoor units, unique aspects of the installation and house, and key considerations to take into account during installation...",
  "heatDesign.customerReport.comment.template.buttonTitle": "Use template",
  "heatDesign.customerReport.comment.template.text": "",
  "heatDesign.customerReport.floorPlans.disclaimer": "Note: Some items may not have been added to the floor plan.",
  "heatDesign.customerReport.floorPlans.title": "Floor plans",
  "heatDesign.customerReport.instructions": "This summary can be downloaded or copied as a sharable link and sent to the customer as an agreement of what will be installed.",
  "heatDesign.customerReport.instructions.description": "Below are the results of the heat loss calculations and your product selections. You can download this information or copy the URL of the Final Spec document to send to the customer. This will serve as an agreement on what will be installed and where, helping to avoid unnecessary changes during installation.",
  "heatDesign.customerReport.instructions.title": "This summary can be downloaded or copied as a sharable link and sent to the customer as an agreement of what will be installed.",
  "heatDesign.customerReport.newRadiators.text": "Please use the automatically grouped list below to fill in your radiators in the Product selection on the left. Make sure to identify any <b>incorrectly grouped radiators</b>.",
  "heatDesign.customerReport.newRadiators.title": "New radiators to be installed",
  "heatDesign.customerReport.outdoorUnits": "Outdoor {count, plural, one{unit} other{units}}",
  "heatDesign.customerReport.preview.description": "The preview below shows what the customer will see and receive via email when the design is complete and ready for installation. You can modify the comment in the field above to provide a more detailed description of the system design for the customer. New comments will only visible in the preview after saving.\n\nYou can also click on the button to copy a link to the System Design page, which you can send to the customer if needed.",
  "heatDesign.customerReport.preview.embed.title": "Preview of customer design report",
  "heatDesign.customerReport.preview.refresh.button": "Refresh preview",
  "heatDesign.customerReport.preview.refresh.description": "You have unsaved changes. To preview or download the updated customer report, first save your changes using the 'Save design' button below.",
  "heatDesign.customerReport.preview.title": "Preview of what will be sent to the customer",
  "heatDesign.customerReport.preview.wrongState": "This project is not in the correct state to show a preview of the customer report. Please check that the prices have been locked and that the customer has accepted the quote.",
  "heatDesign.customerReport.subtitle": "System Design",
  "heatDesign.customerReport.title": "Customer Design Report",
  "heatDesign.customerReport.upcomingChanges.description": "Your system design covers everything from the specifications of your indoor and outdoor units to the placement of your radiators. Don’t be afraid to dig into the details and let us know if you have any questions.",
  "heatDesign.customerReport.upcomingChanges.title": "What are we going to do at your home?",
  "heatDesign.customRadiator": "Custom radiator",
  "heatDesign.deleteRadiator.detail": "Do you want to delete the radiator?",
  "heatDesign.deleteRadiator.title": "Delete radiator",
  "heatDesign.deleteUnderfloorHeating.detail": "Are you sure you want to delete the underfloor heating?",
  "heatDesign.deleteUnderfloorHeating.title": "Are you sure?",
  "heatDesign.development.resetProject": "Reset project to Magicplan",
  "heatDesign.discardChanges.detail": "Do you want to discard your changes?",
  "heatDesign.discardChanges.title": "Discard changes",
  "heatDesign.dwellingDefaultsModal.description": "These values will be the default values of the <b>house</b> if not specified for <b>floors</b> or <b>rooms</b> directly.",
  "heatDesign.dwellingDefaultsModal.title": "House default values",
  "heatDesign.dwellingValidationModal.title": "Invalid house data",
  "heatDesign.emitterList.emitterType.radiator": "Radiators",
  "heatDesign.emitterList.emitterType.underfloor": "Underfloor heating",
  "heatDesign.emitterList.noEmitters": "No enabled water-based emitters found for this house",
  "heatDesign.emitterList.summary.flowNeededForRadiators": "Flow needed for radiators",
  "heatDesign.emitterList.summary.flowNeededForUnderfloorHeating": "Flow needed for underfloor heating",
  "heatDesign.emitterList.summary.radiatorFlowTemperature": "Radiator flow temperature",
  "heatDesign.emitterList.summary.radiatorReturnTemperature": "Actual radiator return temperature",
  "heatDesign.emitterList.summary.totalFlowNeeded": "Total flow needed",
  "heatDesign.emitterList.summary.totalFlowNeededForHouse": "Total flow needed for the house",
  "heatDesign.emitterList.summary.underfloorHeatingFlowTemperature": "Underfloor heating flow temperature",
  "heatDesign.emitterList.summary.underfloorHeatingReturnTemperature": "Underfloor heating return temperature",
  "heatDesign.emitterList.underfloorHeatingDisclaimer": "Underfloor heating circuits are fixed at: <strong>Flow of 45 °C, Return of 35 °C</strong> and an <strong>Emitter ΔT of 10 °C.</strong>",
  "heatDesign.emitterReport.calculationResults.circuitDeltaT": "Circuit flow/return ΔT",
  "heatDesign.emitterReport.calculationResults.emitterAverageTemperature": "Emitter average temperature",
  "heatDesign.emitterReport.calculationResults.flowTemperature": "Flow temperature of circuit",
  "heatDesign.emitterReport.calculationResults.returnTemperature": "Return temperature of circuit",
  "heatDesign.emitterReport.tab.title": "Emitter Report",
  "heatDesign.emitterReport.title": "Emitter Report",
  "heatDesign.emitterReport.upcomingChanges.description": "The list below defines in what emitters will be present in the customers home after installation. The table defines what rooms the emitters exist or will be installed in and their specifications, eventual adjustments & output.",
  "heatDesign.emittersValidationModal.body": "{count, plural, =1 {A room has} other {Some rooms have}} missing or invalid radiator data. Click on the {count, plural, =1 {button} other {buttons}} below to open that room's radiator details and fix the problem. The problems will be indicated with orange highlighting.",
  "heatDesign.emittersValidationModal.title": "Invalid radiator data",
  "heatDesign.error.application.description": "An error occurred while using the tool. This can happen for a number of reasons and requires a developer to take a look at the error.\n\nPlease create a support ticket in the \"Design Engineering Community\" in Microsoft Teams.",
  "heatDesign.error.application.title": "Unable to load project - Application error",
  "heatDesign.error.cannotRetrieveMagicplanProject": "An error occurred when trying to retrieve the Magicplan project for this house. Please make sure the Magicplan survey has been submitted, then try again.",
  "heatDesign.error.checkMagicplanProjectNotDeleted": "Please check that the Magicplan project has not been deleted.",
  "heatDesign.error.dwellingAddress": "House address:",
  "heatDesign.error.magicplan.errorRetrievingProject": "Unable to load magicplan project - Errors when retrieving project",
  "heatDesign.error.noFormFound": "Found no technical survey associated with this project.",
  "heatDesign.error.noHouseData.description": "This can happen for a number of reasons and requires a developer to take a look at the error.\n\nPlease create a support ticket in the \"Design Engineering Community\" in Microsoft Teams.",
  "heatDesign.error.noHouseData.details": "Project failed to load - Error while loading house data",
  "heatDesign.error.noHouseData.title": "Error loading house data",
  "heatDesign.error.noMagicplanSubmission": "The Magicplan project for this customer has not yet been submitted by the technical surveyor. When this has been done, it will be possible to begin the heat design.",
  "heatDesign.error.noMagicplanSubmission.howTo": "See our FAQ for <linkToFAQ>how to submit a Magicplan project</linkToFAQ>.",
  "heatDesign.error.noMagicplanSubmission.latestSurvey": "Latest technical survey",
  "heatDesign.error.noMagicplanSubmission.title": "Waiting for Magicplan submission",
  "heatDesign.error.noRoomsOnFloor": "This floor contains no rooms.",
  "heatDesign.error.unableToLoadMagicplanProject": "Unable to load Magicplan project",
  "heatDesign.existingRadiator": "Existing radiator",
  "heatDesign.floorDefaultsModal.description": "These values will be the default values for this <b>floor</b>, overriding <b>house</b> defaults but not values specified in <b>rooms</b>.",
  "heatDesign.floorDefaultsModal.generalSection.title": "General",
  "heatDesign.floorDefaultsModal.soilInput.label": "External wall surface in contact with soil",
  "heatDesign.floorDefaultsModal.soilInput.tooltip": "For the parts of the wall in contact with soil, we use the average outside temperature as the opposing temperature, and not the Outside Design Temperature (ODT).",
  "heatDesign.floorDefaultsModal.title": "Floor default values",
  "heatDesign.floorOverviewValidationModal.body": "{count, plural, =1 {A room has} other {Some rooms have}} missing or invalid data. Click on the {count, plural, =1 {button} other {buttons}} below to open that room's details and fix the problem. The problems will be indicated with orange highlighting.",
  "heatDesign.floorOverviewValidationModal.invalidFloors": "The following {count, plural, =1 {floor has} other {# floors have}} invalid default values:",
  "heatDesign.floorOverviewValidationModal.invalidRooms": "The following {count, plural, =1 {room has} other {# rooms have}} missing or invalid data:",
  "heatDesign.flowRate": "Flow rate",
  "heatDesign.helper.floorPlan": "Click on a room below to configure it",
  "heatDesign.helpModal.faq.body": "Please check out the FAQ (opens in a new window) and see if we already have an answer to your question. This FAQ is updated with the most common questions continuously.",
  "heatDesign.helpModal.faq.button": "Frequently Asked Questions",
  "heatDesign.helpModal.faq.title": "Do you have questions or need help?",
  "heatDesign.helpModal.request.body": "If you can’t find your answer in the FAQ or when searching the Microsoft Teams channel, you’re very welcome to ask our team in the Microsoft Teams channel. We always have a developer on-call during work hours and will respond as fast as we can.\n\nPlease describe your issue as thoroughly as possible for us to have an easier time to pick up and look at your question.",
  "heatDesign.helpModal.request.title": "Personal help",
  "heatDesign.isCustomTooltip": "Use the custom radiator option to enter details manually. Only choose this if no suitable radiator is available in the database, as it will require manual handling during BoM creation and procurement.",
  "heatDesign.keep": "Keep",
  "heatDesign.lock.confirmation": "Do you want to lock the design? If it's the first time locking the design this will create a \"Customer Design Review\" ticket in Hubspot, which once completed will automatically send an e-mail to the customer with the Design Report.",
  "heatDesign.lock.confirmation.designReview": "Do you want to lock the design?",
  "heatDesign.lock.confirmation.sendDesignReviewEmail": "Request customer approval via email",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.accepted": "<strong>Status:</strong> The customer approved this project at {timestamp}.",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.not-ready": "<strong>Status:</strong> The next step is for the customer to <linkToFinalSpec>approve the design</linkToFinalSpec>.",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.ready": "<strong>Status:</strong> Waiting for customer approval. An e-mail was sent at {timestamp}.",
  "heatDesign.magicplan.advanced": "Advanced",
  "heatDesign.magicplan.heading": "Magicplan",
  "heatDesign.magicplan.links.mobile": "Mobile",
  "heatDesign.magicplan.links.title": "Links to this Magicplan project:",
  "heatDesign.magicplan.links.web": "Desktop",
  "heatDesign.magicplan.merge.buttonLabel": "Refresh project",
  "heatDesign.magicplan.merge.buttonLabel.loading": "Refreshing...",
  "heatDesign.magicplan.merge.description": "If there are changes in the Magicplan project that you want to reflect in your heat design project, you can refresh it without resetting all your data.",
  "heatDesign.magicplan.merge.error.card.description": "An error occurred while trying to refresh the project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "heatDesign.magicplan.merge.error.card.description.duplicates": "Unfortunately this specific project can not be refreshed right due to a known bug affecting some older projects. You can choose to reset this project, after which the merge functionality will work again.",
  "heatDesign.magicplan.merge.error.card.title": "Failed to refresh project",
  "heatDesign.magicplan.merge.locked": "You cannot refresh a locked project.",
  "heatDesign.magicplan.merge.modal.body": "The system will compare the heat design model in Aerospace with the scan in Magicplan to identify any changes. If differences are detected, the system will adjust the project accordingly.\n\nPlease note that this process will <b>not fully reset your design</b>, and only alter items that are changed.\n\nItems with default values, such as walls, windows, and doors, will not be explicitly marked as added. However, elements without default values, such as existing radiators, will be flagged as missing if they are not present.",
  "heatDesign.magicplan.merge.modal.title": "Are you sure you want to update the heat design model with new Magicplan data?",
  "heatDesign.magicplan.merge.success": "Project successfully refreshed with latest Magicplan changes. This page will reload in a few seconds...",
  "heatDesign.magicplan.merge.unsaved": "You have unsaved changes. Please save these changes before trying to refresh the project.",
  "heatDesign.magicplan.reassign.buttonLabel": "Reassign project to me",
  "heatDesign.magicplan.reassign.buttonLabel.loading": "Reassigning...",
  "heatDesign.magicplan.reassign.description": "You can assign the project to yourself to be able to edit the scan in the Magicplan app.",
  "heatDesign.magicplan.reassign.error.card.description": "An error occurred while trying to reassign the project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "heatDesign.magicplan.reassign.error.card.title": "Failed to reassign project",
  "heatDesign.magicplan.reassign.success": "Magicplan project successfully reassigned",
  "heatDesign.magicplan.reimport.buttonLabel": "Reset heat design project",
  "heatDesign.magicplan.reimport.buttonLabel.loading": "Resetting...",
  "heatDesign.magicplan.reimport.description": "You can reset your heat design calculations if you want to start over from scratch.",
  "heatDesign.magicplan.reimport.error.card.description": "An error occurred while trying to reset the project. This can happen for various reasons and requires help from a developer. Please create a support ticket in the \"Design Engineer Community\" on Microsoft Teams.",
  "heatDesign.magicplan.reimport.error.card.title": "Failed to reset project",
  "heatDesign.magicplan.reimport.locked": "You cannot reset a locked project.",
  "heatDesign.magicplan.reimport.modal.body": "Resetting the project will remove all of the choices and selections you have made in the heat design project and make you start over from scratch. This means that it will <b>completely reset your heat loss calculations</b>.\n\nResetting also means that the latest Magicplan updates to the project will be imported to the heat design.\n\nThis choice is irreversible. Are you sure?",
  "heatDesign.magicplan.reimport.modal.title": "Do you want to reset the heat design project?",
  "heatDesign.magicplan.reimport.success": "Heat design project successfully reset. This page will reload in a few seconds...",
  "heatDesign.newEmitterDeltaT": "New E ΔT",
  "heatDesign.newRadiator": "New radiator",
  "heatDesign.newRadiator.NewRadiators": "New Radiators",
  "heatDesign.notify.designReviewEmailSent": "Email sent to customer",
  "heatDesign.notify.lockingProject": "Locking design...",
  "heatDesign.notify.projectLocked": "Design locked",
  "heatDesign.notify.projectUnlocked": "Design unlocked",
  "heatDesign.notify.sendingDesignReviewEmail": "Sending email to customer...",
  "heatDesign.notify.unlockingProject": "Unlocking design...",
  "heatDesign.procurement.aik": "AIK",
  "heatDesign.procurement.aikItemError": "This item is not specified in the BoM. Could there be a similar item in the BoM that can be marked as interchangeable with this item?",
  "heatDesign.procurement.bom": "BoM",
  "heatDesign.procurement.customItemError": "This is a custom item and must be handled manually—either by matching it with a database item or ordering it separately. Please specify the solution in the comments.",
  "heatDesign.procurement.deleteEquivalenceGroup.description": "Are you sure you want to remove this item?",
  "heatDesign.procurement.deleteEquivalenceGroup.title": "Remove item",
  "heatDesign.procurement.duplicateItemError": "This item, or one of its equivalents, shares ERP ID with another in this view—possibly a different version. Please verify that this is intended before ordering.",
  "heatDesign.procurement.itemName": "Item name",
  "heatDesign.procurement.quantity": "Qty",
  "heatDesign.procurement.quantity.tooltip": "This column shows the amount that will be sent to the ERP. It's calculated by taking the amount from the BoM and subtracting any items from the ‘AIK’ or ‘Van Stock’.",
  "heatDesign.procurement.removedFromErpError": "This item has been removed from the ERP and can no longer be ordered.",
  "heatDesign.procurement.vanStock": "VS",
  "heatDesign.productSelection.heatDetails.calculatedTotalHeatLoss": "Total heat loss (kW)",
  "heatDesign.productSelection.heatDetails.chosenFlowTemp": "Flow temperature (°C)",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp": "ODT (Adjusted) (°C)",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp.tooltip": "The adjusted <b>Outdoor Design Temperature (ODT)</b> is the one that was set at the start of the project based on the location and parameters of the house.",
  "heatDesign.productSelection.heatDetails.outdoorUnit.bivalencePoint.heatOutput.without.electric.tooltip": "This is the maximum heat output the selected heat pump can produce at the <b>bivalence point</b> and selected flow temperature <b>without help</b> from any electrical backup measures.",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.with.electric.tooltip": "This is the maximum heat output the selected heat pump can produce at the <b>Outdoor Design Temperature</b> and selected flow temperature <b>with help</b> from electrical backup measures.",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.without.electric.tooltip": "This is the maximum heat output the selected heat pump can produce at the <b>Outdoor Design Temperature</b> and selected flow temperature <b>without help</b> from any electrical backup measures.",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.temperature": "Temperature (°C)",
  "heatDesign.productSelection.heatDetails.title": "House details: Heat",
  "heatDesign.productSelection.heatPumpPackageSelection": "Outdoor & indoor unit details",
  "heatDesign.productSelection.heatPumpPackageSelection.designLocked": "The design is locked and you need to unlock the design to change products.",
  "heatDesign.productSelection.heatPumpPackageSelection.productsLocked": "The products need to be unlocked to be adjusted.",
  "heatDesign.productSelection.indoorUnitDetails.cylinderReheatTime": "Reheat time to {temperature} °C",
  "heatDesign.productSelection.indoorUnitDetails.houseWaterSection.title": "House water parameters",
  "heatDesign.productSelection.indoorUnitDetails.title": "Selected indoor unit performance",
  "heatDesign.productSelection.noNewRadiators": "No new radiators",
  "heatDesign.productSelection.noPerformanceData": "No performance data for heat pump outdoor unit with SKU {sku}",
  "heatDesign.productSelection.outdoorUnitDetails.bivalencePoint": "Bivalence point (°C)",
  "heatDesign.productSelection.outdoorUnitDetails.bivalenceSection.title": "Performance at Bivalence point",
  "heatDesign.productSelection.outdoorUnitDetails.heatOutput.with.electric": "Heat output <b>with</b> electric heater (kW)",
  "heatDesign.productSelection.outdoorUnitDetails.houseHeatingSection.title": "House heating parameters",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutput": "Heat output <b>without</b> electric heating (kW)",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithElectric": "Heat output <b>with</b> electric heating (kW)",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithoutElectric": "Heat output <b>without</b> electric heating (kW)",
  "heatDesign.productSelection.outdoorUnitDetails.performanceODTSection.title": "Performance at ODT",
  "heatDesign.productSelection.outdoorUnitDetails.scop": "SCOP",
  "heatDesign.productSelection.outdoorUnitDetails.scop.description": "SCOP is based on the ODT and the flow temperature and <b>should not be under 3</b>.",
  "heatDesign.productSelection.outdoorUnitDetails.title": "Selected outdoor unit performance",
  "heatDesign.productSelection.radiators.showRadiatorsButton": "Show radiators in project",
  "heatDesign.productSelection.radiators.title": "House details: New radiators",
  "heatDesign.productSelection.radiators.tooltip": "These are all radiators that are to be installed in the property.\n\nAira groups radiators based on their cost or largest dimension (width/length or height): \n - Standard: Up to 1 599 mm \n - Large: Anything above 1 600 mm, and also K3/Type 33 of any size.\n - Design: Towel radiators or customer requests out of the ordinary Aira catalog.",
  "heatDesign.productSelection.radiators.total": "Total amount of new radiators: {total}",
  "heatDesign.productSelection.radiatorsList.groupHeader": "Radiator ({group})",
  "heatDesign.productSelection.radiatorsList.groupHeader.v2": "{group}: {numberOfRadiators}",
  "heatDesign.productSelection.radiatorsList.noRadiators": "No radiators in this project",
  "heatDesign.productSelection.radiatorsList.title": "All radiators in the project",
  "heatDesign.productSelection.showerTime": "<b>Hot shower time.</b> It will take {reheatTime} minutes to reheat the tank from cold.",
  "heatDesign.productSelection.unlockProducts.error.body": "An error occurred when trying to unlock products. Received the following error:\n\n<strong>{error}</strong>\n\nPlease make sure the solution is in a state where it can be unlocked and try again. If the problem persists, please contact support.",
  "heatDesign.productSelection.unlockProducts.error.title": "Failed to unlock products",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity": "Minimum recommended DHW tank capacity (L)",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity.tooltip": "The minimum required Domestic Hot Water (DHW) tank capacity in litres is calculated as whichever is the greatest of: \n(number of bedrooms + 1) * 45, or\n(number of occupants) * 45",
  "heatDesign.productSelection.waterUsage.numBathrooms": "Number of Bathrooms",
  "heatDesign.productSelection.waterUsage.numBedrooms": "Number of Bedrooms",
  "heatDesign.productSelection.waterUsage.numOccupants": "Number of Occupants",
  "heatDesign.propertyDetails.ACPHDefault": "ACPH Default",
  "heatDesign.propertyDetails.AddressTemperatureData": "Climate data",
  "heatDesign.propertyDetails.altitude.label": "Altitude of postal code (m)",
  "heatDesign.propertyDetails.altitude.tooltip": "The altitude is based of the postal code as a whole, so take care if this specific property is higher up or lower than others in the area.",
  "heatDesign.propertyDetails.bathrooms": "Bathrooms",
  "heatDesign.propertyDetails.bedrooms": "Bedrooms",
  "heatDesign.propertyDetails.climateZone": "Climate zone",
  "heatDesign.propertyDetails.DegreeDays": "Degree days",
  "heatDesign.propertyDetails.ExternalTempForLocation": "Outdoor Design Temperature (ODT)",
  "heatDesign.propertyDetails.floors": "Floors",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation": "Is the house in an exposed location?",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.description": "Is the house prone to windy conditions? If checked, will add -1 °C to the temperature adjustment",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.disabled": "This option has been deprecated and will be removed in the future. Please use the standard ventilation method to set the exposure of the house",
  "heatDesign.propertyDetails.MeanAirTemp": "Mean air temperature",
  "heatDesign.propertyDetails.noStreetView": "No Street View imagery found near the location.",
  "heatDesign.propertyDetails.NumberOfBathrooms": "Number of Bathrooms",
  "heatDesign.propertyDetails.NumberOfBathrooms.tooltip": "You can change the amount of bathrooms by changing the “Room type” to \"Bathroom\" when going into Room Details on the next screen.",
  "heatDesign.propertyDetails.NumberOfBedrooms": "Number of Bedrooms",
  "heatDesign.propertyDetails.NumberOfBedrooms.tooltip": "You can change the amount of bedrooms by changing the “Room type” variable when going into Room Details on the next screen.",
  "heatDesign.propertyDetails.NumberOfFloors": "Number of Floors",
  "heatDesign.propertyDetails.NumberOfOccupants": "Number of Occupants",
  "heatDesign.propertyDetails.occupants": "Occupants",
  "heatDesign.propertyDetails.OutsideTemp": "Adjusted ODT",
  "heatDesign.propertyDetails.temperatureCompensated": "Temperature adjustment",
  "heatDesign.propertyDetails.TemperatureCompensation": "Outdoor Design Temperature adjustment",
  "heatDesign.propertyDetails.TemperatureCompensation.description": "You can adjust the external temperature further if it's located at a high altitude, etc",
  "heatDesign.propertyDetails.title": "Property Details",
  "heatDesign.propertyDetails.updateUValuesModal.cancelButton": "Keep existing U-values",
  "heatDesign.propertyDetails.updateUValuesModal.confirmButton": "Apply changes to U-values",
  "heatDesign.propertyDetails.updateUValuesModal.currentUValueHeader": "Current U-value",
  "heatDesign.propertyDetails.updateUValuesModal.helper": "You have updated the construction year. Would you like to update the following U-values for this house?",
  "heatDesign.propertyDetails.updateUValuesModal.suggestedUValueHeader": "Suggested U-value",
  "heatDesign.propertyDetails.updateUValuesModal.title": "Update dwelling U-values",
  "heatDesign.propertyDetails.YearBuilt": "Year Built",
  "heatDesign.propertyOverview.ventilation.acphDefaultsTooltip": "Configure the default ventilation settings for the project according to regional or European minimum (EN12831:2017) standards, or override them with a custom whole-house ACPH if required.",
  "heatDesign.propertyOverview.ventilation.airPermeabilityTooltip": "Air permeability reflects a building’s airtightness and can be measured or taken from standards. If unmeasured \n\nEN12831:2017 suggests 12 m³/(m²·h) for all houses.\n\nCIBSE instead suggest a minimum ventilation rate in L/s based on bedrooms/occupants that needs to be converted to m³/(m²·h) using the house’s floor area and volume.",
  "heatDesign.propertyOverview.ventilation.calculationMethodTooltip": "EN12831:2017 defines two methods for estimating ventilation heat loss: the Simplified model (Eq. 13, p.34) and the Standard method (Eq. 16, p.34).\n\nThe Simple method is the method that the the Aira HLC tool have traditionally used which relies on regional defaults based on build year (or EN12831 minimum defaults), but without the  adjustment for the proportion of fresh air vs. air from adjacent rooms as stated in EN12831. \n\nThe Standard method is a more accurate method which compares calculations that factors in air permeability, façade exposure, and other parameters, applying the worst-case outcome for a more accurate result.",
  "heatDesign.propertyOverview.ventilation.exposureTooltip": "Set the house’s wind exposure: Shielded (e.g., city center or forest), Moderate (some surrounding buildings/trees), or Exposed (open area).",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bathroom": "Bathroom covers the following room types: Bathroom, Half Bathroom, Primary Bathroom",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bedroom": "Bedrooms covers the following room types: Bedroom, Children Bedroom, Master Bedroom, Primary Bedroom",
  "heatDesign.propertyOverview.ventilation.roomTooltip.diningRoom": "Dining room covers the following room types: Dining Room",
  "heatDesign.propertyOverview.ventilation.roomTooltip.dressingRoom": "Dressing room covers the following room types: Closet",
  "heatDesign.propertyOverview.ventilation.roomTooltip.gamesRoom": "Games room covers the following room types: Den, Music Room, Playroom",
  "heatDesign.propertyOverview.ventilation.roomTooltip.hall": "Hall covers the following room types: Hall, Hallway, Corridor",
  "heatDesign.propertyOverview.ventilation.roomTooltip.kitchen": "Kitchen covers the following room types: Kitchen, Workshop, Kitchenette, Cafeteria",
  "heatDesign.propertyOverview.ventilation.roomTooltip.landing": "Landing covers the following room types: Stairway",
  "heatDesign.propertyOverview.ventilation.roomTooltip.livingRoom": "Living room covers the following room types: Living Room, Reception, Waiting Room, Vestibule",
  "heatDesign.propertyOverview.ventilation.roomTooltip.loungeRoom": "Lounge covers the following room types: Lounge",
  "heatDesign.propertyOverview.ventilation.roomTooltip.other": "Other covers the following room types: Attic, Loft, Balcony, Deck, Furnace Room, Hatched Room, Other, Outbuilding, Patio, Porch, Open Space, Photocopy Room, Elevators, Unknown, Unfinished Basement, Garage",
  "heatDesign.propertyOverview.ventilation.roomTooltip.storeRoom": "Store room covers the following room types: Cellar, Storage, Maintenance Room",
  "heatDesign.propertyOverview.ventilation.roomTooltip.study": "Study covers the following room types: Study, Private Office, Shared Office, Meeting Room, Conference Room, Training Room, Archives, Lab",
  "heatDesign.propertyOverview.ventilation.roomTooltip.toilet": "Toilet covers the following room types: Toilet",
  "heatDesign.propertyOverview.ventilation.roomTooltip.utilityRoom": "Utility room covers the following room types: Laundry Room, Server Room",
  "heatDesign.radiator.FlowReturnDeltaT": "Flow/Return DeltaT",
  "heatDesign.radiator.FlowTemperature": "Flow Temperature",
  "heatDesign.radiator.heatOutput.totals.meanWaterAirTransferInvalid": "Your choice of flow temperature and deltaT gives a mean water temperature in the radiators that is lower than the design temperature of the hottest room ({highestRoomTemp} °C). Are you sure?",
  "heatDesign.radiator.heatOutput.totals.sum": "Total sum:",
  "heatDesign.radiator.heatOutput.totals.title": "House Totals",
  "heatDesign.radiator.MeanWaterTemperature": "Mean Water Temperature",
  "heatDesign.radiator.ReturnTemperature": "Return Temperature",
  "heatDesign.radiatorModal.browse": "Browse radiator",
  "heatDesign.radiatorModal.catalogue.brand": "Brand",
  "heatDesign.radiatorModal.catalogue.category": "Category",
  "heatDesign.radiatorModal.catalogue.tab.radiators": "Radiator catalogue",
  "heatDesign.radiatorModal.catalogue.title": "Aira catalogue",
  "heatDesign.radiatorModal.catalogue.type": "Type",
  "heatDesign.radiatorModal.discard": "Discard",
  "heatDesign.radiatorModal.emitterPanel.description": "Emitter description",
  "heatDesign.radiatorModal.emitterPanel.title.new": "New radiator",
  "heatDesign.radiatorModal.emitterPanel.title.replace": "To be replaced",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.label": "Individual emitter adjustment",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.tooltip": "This will give you manual control over the <b>ΔT</b> for every emitter, enabling you to precisely adjust the flow rate (hydronic balancing).",
  "heatDesign.radiatorModal.heatBalance.title": "Room heat balance",
  "heatDesign.radiatorModal.heatBalance.tooltip": "The room heat balance is the difference between the output of all new & kept emitters in the room and the room's heat loss.",
  "heatDesign.radiatorModal.noRadiators": "No radiators found in catalogue",
  "heatDesign.radiatorModal.removedFromErp": "This radiator is no longer available in ERP. Please choose an alternative, if it has not already been procured.",
  "heatDesign.radiatorModal.select": "Select",
  "heatDesign.radiatorModal.shared.all": "All",
  "heatDesign.radiatorModal.shared.calculatedOutput.dynamic": "Calculated output (W)  @ MW-AT ΔT {deltaT} °C",
  "heatDesign.radiatorModal.shared.calculatedOutput.static": "Calculated output (W)",
  "heatDesign.radiatorModal.shared.catalog": "Catalogue",
  "heatDesign.radiatorModal.shared.columns": "Columns",
  "heatDesign.radiatorModal.shared.height": "Height (mm)",
  "heatDesign.radiatorModal.shared.image": "Image",
  "heatDesign.radiatorModal.shared.length": "Length (mm)",
  "heatDesign.radiatorModal.shared.material": "Material",
  "heatDesign.radiatorModal.shared.model": "Name/model",
  "heatDesign.radiatorModal.shared.search": "Search...",
  "heatDesign.radiatorModal.shared.type": "Type",
  "heatDesign.radiatorModal.table.details": "Details",
  "heatDesign.radiatorModal.table.factor": "Factor",
  "heatDesign.radiatorModal.table.nominalOutput.detailed": "Nominal output (W)  @ MW-AT ΔT {deltaT} °C (75/65/20)",
  "heatDesign.radiatorModal.table.nominalOutput.generic": "Nominal output (W)",
  "heatDesign.radiatorModal.table.weight": "Weight (kg)",
  "heatDesign.radiatorModal.title.add": "Add new radiator",
  "heatDesign.radiatorModal.title.replace": "Replace radiator",
  "heatDesign.radiatorRenderer.AddRadiator": "Add radiator",
  "heatDesign.radiatorRenderer.AddRadiatorDetail": "Do you want to add an existing \nradiator or a new one?",
  "heatDesign.radiatorRenderer.AddUnderfloorHeating": "Add Underfloor Heating",
  "heatDesign.radiatorRenderer.DeltaT": "Temperature difference between radiator and room (ΔT) (⁰C)",
  "heatDesign.radiatorRenderer.emitterTempAndRoomDiff": "Temperature difference between emitters & room (MW-AT ΔT)",
  "heatDesign.radiatorRenderer.footer.HeatOutput": "Heat output of kept & new emitters",
  "heatDesign.radiatorRenderer.footer.ReturnTemp": "Actual return",
  "heatDesign.radiatorRenderer.footer.TotalRoomHeatLoss": "Total room heat loss",
  "heatDesign.radiatorRenderer.footer.WattDifference": "Watt difference between emitter & room temperature",
  "heatDesign.radiatorRenderer.header.DeltaT": "Temperature difference between flow and return (Emitter ΔT)",
  "heatDesign.radiatorRenderer.header.FlowTemp": "Flow temperature of circuit",
  "heatDesign.radiatorRenderer.header.MeanRadiatorTemp": "Emitter average temperature",
  "heatDesign.radiatorRenderer.header.ReturnTemp": "Return temperature of circuit",
  "heatDesign.radiatorRenderer.RemoveRadiator": "Delete radiator",
  "heatDesign.radiatorRenderer.RoomTemp": "Room temperature",
  "heatDesign.radiators.calculatedOutputTooltip": "The calculated output represents the radiator’s heat output in this room at the given flow rate.\n\nTemperature Difference (ΔT): ΔT = (Average water temperature in the radiator) − (Room temperature)\n\nDifferential Factor (DF): DF = (ΔT ÷ Manufacturer reference temperature)¹·³\n\nCalculated Output (CO): CO = Manufacturer output at reference temperature × DF",
  "heatDesign.radiators.conversionFactor": "Conversion factor",
  "heatDesign.radiators.externalVendorId": "External vendor ID",
  "heatDesign.radiators.manufacturerId": "Manufacturer ID",
  "heatDesign.radiators.waterContent": "Water content (L)",
  "heatDesign.radiatorSizeCategory.design": "Design",
  "heatDesign.radiatorSizeCategory.large": "Large",
  "heatDesign.radiatorSizeCategory.standard": "Standard",
  "heatDesign.radiatorsOverview.emitterList.title": "Emitter list",
  "heatDesign.radiatorsOverview.floorOverview.title": "Floor overview",
  "heatDesign.radiatorTable.addExisting": "Add Existing",
  "heatDesign.radiatorTable.addNew": "Add New",
  "heatDesign.radiatorTable.calculated.output": "Calculated output (W)",
  "heatDesign.radiatorTable.description": "Emitter description",
  "heatDesign.radiatorTable.ExistingRadiators": "Existing Radiators",
  "heatDesign.radiatorTable.flowRateAdjustment.cannotAdjustElectricRadiators": "Electric radiators can't be adjusted here.",
  "heatDesign.radiatorTable.flowRateAdjustment.flowReturnTempLabel": "Individual Emitter ΔT {temperature}",
  "heatDesign.radiatorTable.flowRateAdjustment.label": "-/+ Emitter ΔT adjustment (⁰C)",
  "heatDesign.radiatorTable.meanWaterTemp": "Calculated output @ ΔT {meanWaterTemp} °C (W)",
  "heatDesign.radiatorTable.meanWaterTemp.tooltip": "The calculated output represents the radiator’s heat output in this room at the given flow rate.\n\nTemperature Difference (ΔT):  \nΔT = (Average water temperature in the radiator) − (Room temperature)\n\nDifferential Factor (DF): \nDF = (ΔT ÷ Manufacturer reference temperature)¹·³\n\nCalculated Output (CO):  CO = Manufacturer output at reference temperature × DF",
  "heatDesign.radiatorTable.meanWaterTempPerRadiator": "@ MW-AT ΔT {meanWaterTemp} °C",
  "heatDesign.radiatorTable.nominalOutput.deltaT": "ΔT (⁰C)",
  "heatDesign.radiatorTable.nominalOutput.title": "Nominal output",
  "heatDesign.radiatorTable.nominalOutput.watts": "Watts (W)",
  "heatDesign.radiatorTable.replacedBy": "Replaced by {id}",
  "heatDesign.radiatorTable.replaces": "Replaces {id}",
  "heatDesign.radiatorTable.temperatureAdjustment.title": "Emitter temperature adjustment",
  "heatDesign.radiatorTable.TotalOutputOfAllEnabledEmittersWatts": "Total output of all enabled emitters (Watts)",
  "heatDesign.radiatorTable.underfloorHeating.matchRoomHeatLossLabel": "Match room heat loss",
  "heatDesign.radiatorTable.underfloorHeating.output": "Output (W)",
  "heatDesign.radiatorTable.underfloorHeating.tooltip": "The output of underfloor heating defaults to the room's total heat loss and is then kept in sync if the room’s heat loss changes.  \n\nIf you have technical data on the underfloor heating, you can override the output to remove the sync.",
  "heatDesign.radiatorTable.Use": "Enabled",
  "heatDesign.replaceRadiator": "Replace",
  "heatDesign.report.clause.effectiveAirChangesPerHour": "Effective ACPH reflects the ventilation heat loss calculated using the EN12831 2017 zone level method and may differ from the initial reference defaults.",
  "heatDesign.report.dwellingData.climateData.adjustedExternalTemp": "Outside design temperature used for calculations (°C)",
  "heatDesign.report.dwellingData.climateData.degreeDays": "Degree days",
  "heatDesign.report.dwellingData.climateData.meanAirTemp": "Mean air temperature (°C)",
  "heatDesign.report.dwellingData.climateData.outsideTemp": "Outside design temperature (°C)",
  "heatDesign.report.dwellingData.climateData.temperatureAdjustmentExposedLocation": "Temperature adjustment for exposed location (°C)",
  "heatDesign.report.dwellingData.climateData.temperatureCompensation": "Further temperature adjustment for location (°C)",
  "heatDesign.report.dwellingData.climateData.title": "Climate Data",
  "heatDesign.report.dwellingData.energyDemand.dailyHotWaterEnergyDemand": "Daily hot water energy demand (kWh)",
  "heatDesign.report.dwellingData.energyDemand.fabricEnergyDemand": "Annual fabric energy demand (kWh)",
  "heatDesign.report.dwellingData.energyDemand.hotWaterEnergyDemand": "Annual hot water energy demand (kWh)",
  "heatDesign.report.dwellingData.energyDemand.title": "Energy Demand",
  "heatDesign.report.dwellingData.energyDemand.totalEnergyDemand": "Annual total energy demand (kWh)",
  "heatDesign.report.dwellingData.energyDemand.ventilationEnergyDemand": "Annual ventilation energy demand (kWh)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowDeltaT": "Suggested flow ΔT (°C)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowTemp": "Suggested flow temperature (°C)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.title": "Flow Temperatures & Emitters",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.totalHeatEmittance": "Total output of heat emitters (kW)",
  "heatDesign.report.dwellingData.generalInfo.address": "Address",
  "heatDesign.report.dwellingData.generalInfo.customerName": "Customer name",
  "heatDesign.report.dwellingData.generalInfo.numBedrooms": "Number of bedrooms",
  "heatDesign.report.dwellingData.generalInfo.numFloors": "Number of floors",
  "heatDesign.report.dwellingData.generalInfo.numOccupants": "Number of occupants",
  "heatDesign.report.dwellingData.generalInfo.title": "General Info",
  "heatDesign.report.dwellingData.generalInfo.totalHousingArea": "Total housing area (m²)",
  "heatDesign.report.dwellingData.generalInfo.yearBuilt": "Year built",
  "heatDesign.report.dwellingData.heatLoss.fabricHeatLoss": "Fabric Heat Loss (kW)",
  "heatDesign.report.dwellingData.heatLoss.fabricTitle": "Fabric",
  "heatDesign.report.dwellingData.heatLoss.title": "Heat Loss",
  "heatDesign.report.dwellingData.heatLoss.totalHeatLoss": "Total Heat Loss (kW)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphCustom": "{value} ACPH (Custom housewide)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphDefaultsUsed": "ACPH defaults used",
  "heatDesign.report.dwellingData.heatLoss.ventilation.airPermeability": "Air permeability (m³/(h·m²))",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposedFacadesFactor": "Exposed facades factor",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposureCoefficient": "Exposure coefficient",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method": "Method of calculation",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.simple": "Simple (EN12831)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.standard": "Standard (EN12831)",
  "heatDesign.report.dwellingData.heatLoss.ventilationHeatLoss": "Ventilation Heat Loss (kW)",
  "heatDesign.report.dwellingData.heatLoss.ventilationTitle": "Ventilation",
  "heatDesign.report.dwellingData.indoorUnitDetails.reheatTime": "Cylinder reheat time to {temperature} °C (minutes)",
  "heatDesign.report.dwellingData.indoorUnitDetails.selectedIndoorUnit": "Selected indoor unit",
  "heatDesign.report.dwellingData.indoorUnitDetails.title": "Indoor unit details",
  "heatDesign.report.dwellingData.outdoorUnitDetails.bivalencePoint": "Bivalence point (°C)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.capacity": "Selected outdoor unit capacity (kW)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.maxHeatOutput": "Max heat output without electric heating (kW)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.scop": "SCOP",
  "heatDesign.report.dwellingData.outdoorUnitDetails.selectedOutdoorUnit": "Selected outdoor unit",
  "heatDesign.report.dwellingData.outdoorUnitDetails.title": "Outdoor unit details",
  "heatDesign.report.dwellingData.title": "House Data",
  "heatDesign.report.externalWallAdditionalSurfaceArea": "Heat loss calculations for External Walls add an additional {additionalAreaPercentage}% surface area compared to its internal side according to EN 12831 (2017).",
  "heatDesign.report.floorDetails.generalInfo.numberOfRooms": "Number of rooms",
  "heatDesign.report.floorDetails.generalInfo.title": "General Info",
  "heatDesign.report.floorDetails.generalInfo.totalArea": "Total area (m²)",
  "heatDesign.report.floorDetails.generalInfo.totalHeatEmittance": "Total output of heat emitters (kW)",
  "heatDesign.report.floorDetails.generalInfo.totalHeatLoss": "Total heat loss (kW)",
  "heatDesign.report.floorDetails.rooms.title": "Rooms",
  "heatDesign.report.instructions": "The following technical report contains all of the heat loss calculation results and can be downloaded and sent to the proper authorities who require it to finalise the installation.",
  "heatDesign.report.instructions.description": "The following technical report contains all of the heat loss calculation results and can be downloaded and sent to the proper authorities who require it to finalise the installation.",
  "heatDesign.report.instructions.title": "The technical report contains the full results from the heat loss calculations.",
  "heatDesign.report.pdf.footer.generatedAt": "Report generated on {dateTime}",
  "heatDesign.report.pdf.footer.generatedAtBy": "Report generated by {author} on {dateTime}",
  "heatDesign.report.pdf.locked": "Report generated at {generatedAt}",
  "heatDesign.report.pdf.noReports": "No locked reports found.",
  "heatDesign.report.pdf.preview": "Preview",
  "heatDesign.report.pdf.select": "Select report",
  "heatDesign.report.roomDetails.avgRoomHeight": "Avg room height (m)",
  "heatDesign.report.roomDetails.designRoomTemp": "Design room temperature (°C)",
  "heatDesign.report.roomDetails.emitterDetails.calculatedOutput": "Calculated output @ ΔT {deltaT}⁰C (W)",
  "heatDesign.report.roomDetails.emitterDetails.description": "Description [height x length]",
  "heatDesign.report.roomDetails.emitterDetails.missingEmitters": "There are no emitters in this room",
  "heatDesign.report.roomDetails.emitterDetails.nominalOutput": "Nominal output",
  "heatDesign.report.roomDetails.emitterDetails.note": "The Flow/Return ΔT has been adjusted individually for this emitter to achieve a specific output.",
  "heatDesign.report.roomDetails.emitterDetails.status": "Status",
  "heatDesign.report.roomDetails.emitterDetails.title": "Radiators",
  "heatDesign.report.roomDetails.emitterDetails.type": "Type",
  "heatDesign.report.roomDetails.fabricDetails.area": "Area (m²)",
  "heatDesign.report.roomDetails.fabricDetails.heatLoss": "Heat loss (W)",
  "heatDesign.report.roomDetails.fabricDetails.opposingTemperature": "Opposing temperature (°C)",
  "heatDesign.report.roomDetails.fabricDetails.surface.doors": "External door",
  "heatDesign.report.roomDetails.fabricDetails.surface.externalWalls": "External wall",
  "heatDesign.report.roomDetails.fabricDetails.surface.floors": "Floor",
  "heatDesign.report.roomDetails.fabricDetails.surface.internalWalls": "Internal wall",
  "heatDesign.report.roomDetails.fabricDetails.surface.partyWalls": "Party wall",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofGlazings": "Roof glazing",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofsOrCeilings": "Roof",
  "heatDesign.report.roomDetails.fabricDetails.surface.windows": "Window",
  "heatDesign.report.roomDetails.fabricDetails.surfaceType": "Fabric",
  "heatDesign.report.roomDetails.fabricDetails.title": "Fabric details",
  "heatDesign.report.roomDetails.fabricDetails.uValue": "U-value | Material",
  "heatDesign.report.roomDetails.fabricHeatLoss": "Fabric heat loss (W)",
  "heatDesign.report.roomDetails.heatFlux": "Heat flux (W/m²)",
  "heatDesign.report.roomDetails.otherEmitters.title": "Other emitters",
  "heatDesign.report.roomDetails.roomArea": "Room area (m²)",
  "heatDesign.report.roomDetails.roomType": "Room type",
  "heatDesign.report.roomDetails.total": "Total",
  "heatDesign.report.roomDetails.totalHeatEmittance": "Total output of heat emitters (W)",
  "heatDesign.report.roomDetails.totalHeatLoss": "Total heat loss (W)",
  "heatDesign.report.roomDetails.underfloorHeating.output": "Output (W)",
  "heatDesign.report.roomDetails.underfloorHeating.title": "Underfloor heating",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour": "Air changes per hour (ACPH)",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.simple": "Based on EN12831 2017 simple calculation method.",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.standard": "Based on EN12831 2017 zone level calculation method.",
  "heatDesign.report.roomDetails.ventilationDetails.deltaT": "ΔT (°C)",
  "heatDesign.report.roomDetails.ventilationDetails.externalEnvelopeArea": "Area of external surfaces (m2)",
  "heatDesign.report.roomDetails.ventilationDetails.heatLoss": "Heat loss (W)",
  "heatDesign.report.roomDetails.ventilationDetails.title": "Ventilation details",
  "heatDesign.report.roomDetails.ventilationDetails.totalVolume": "Volume (m³)",
  "heatDesign.report.roomDetails.ventilationHeatLoss": "Ventilation heat loss (W)",
  "heatDesign.report.thermalBridgingIncludedInHeatLoss": "All heat loss calculations add {thermalBridgingAdjustment} to all U-values to account for thermal bridging according to EN 12831 (2017).",
  "heatDesign.reportInstructions.emitterReport.description": "It defines in what rooms the emitters exist or will be installed in, specifications about them, eventual adjustments made to them and their output.",
  "heatDesign.reportInstructions.emitterReport.title": "These are all the emitters that will be used at the customers property",
  "heatDesign.resultSnapshotBanner.description": "Values in the background might differ, if the tools calculation algorithms have been changed.",
  "heatDesign.resultSnapshotBanner.designReviewState.accepted": "Customer approved at {timestamp}",
  "heatDesign.resultSnapshotBanner.designReviewState.not-ready": "Customer approval email not sent",
  "heatDesign.resultSnapshotBanner.designReviewState.ready": "Waiting for customer approval. An e-mail was sent at {timestamp}",
  "heatDesign.resultSnapshotBanner.flowTemperature": "Flow Temperature",
  "heatDesign.resultSnapshotBanner.houseHeatLoss": "House Heat Loss",
  "heatDesign.resultSnapshotBanner.lockedAt": "Locked at {lockedAtDate}",
  "heatDesign.resultSnapshotBanner.title": "The design is locked. To be able to save edits to the project, you must unlock the design.",
  "heatDesign.resultSnapshotBanner.totalEmitterOutput": "Total Emitter Output",
  "heatDesign.returnTemperatureTooltip": "The return value might differ slightly from the initial setup if you have made individual emitter adjustments",
  "heatDesign.RoofGlazingsRenderer.addRoofGlazing": "Add roof glazing",
  "heatDesign.RoofGlazingsRenderer.UVAlueAngleCompensation": "U-values will have a 0.5 addition to account for extra heat loss through roof glazings compared to windows.",
  "heatDesign.room.averageCeilingHeight": "Average ceiling height",
  "heatDesign.room.avgAirChangesPerHour": "Average Air Changes Per Hour",
  "heatDesign.room.avgAirChangesPerHour.disabledByPulseTest": "Disabled if air permeability is measured",
  "heatDesign.room.avgAirChangesPerHour.helperText.custom": "Based on custom default value",
  "heatDesign.room.avgAirChangesPerHour.helperText.standardized": "Based on \"{standard}\" defaults",
  "heatDesign.room.avgAirChangesPerHourHelperText": "Based on room type & characteristics.",
  "heatDesign.room.avgAirChangesPerHourSuffix": "ACPH",
  "heatDesign.room.belowFloorLabel.heatedRoom": "Heated room (no heat loss)",
  "heatDesign.room.belowFloorLabel.solidFloor": "Soil, e.g. concrete slab ({temp} °C)",
  "heatDesign.room.belowFloorLabel.suspendedFloor": "Outside air, e.g. a suspended floor ({temp} °C)",
  "heatDesign.room.belowFloorLabel.unheatedRoom": "Unheated room ({temp}°C)",
  "heatDesign.room.designRoomTemp": "Design Room Temperature",
  "heatDesign.room.designRoomUnheated": "Using local average annual temperature of {temperature} °C.",
  "heatDesign.room.heatedRoom": "Heated room",
  "heatDesign.room.heatedRoom.tooltip": "Disabling this option will mark the room as unheated, and its heat loss won't be calculated. Other rooms will treat this space as having the local annual average temperature, which is determined by the property's address.",
  "heatDesign.room.openFlue": "Is there an open flue?",
  "heatDesign.room.openFlue.disabledByPulseTest": "Disabled if air permeability is measured",
  "heatDesign.room.referenceAirChangesPerHour": "Reference ACPH",
  "heatDesign.room.referenceAirChangesPerHourTooltip": "The ACPH can be used in different ways depending on your chosen calculation method according to EN12831 2017.\n    \n    The <strong>simple method</strong> will use the chosen ACPH default or overridden data directly (<strong>without</strong> the fresh air adjustment factor) to calculate the ventilation heat loss.\n    \n    The <strong>standard method</strong> will compare the simple method (<strong>with</strong> the fresh air adjustment factor) versus calculations that factors in air permeability, façade exposure, and other parameters, and use the worst-case scenario as basis for the ventilation heat loss",
  "heatDesign.room.roomName": "Room name",
  "heatDesign.room.roomType": "Room type",
  "heatDesign.room.tempOfSpaceAbove": "Temperature of space above",
  "heatDesign.room.typeOfSpaceAbove": "Type of space above",
  "heatDesign.room.whatIsBelowTheFloor": "What is below the floor?",
  "heatDesign.roomDetails.belowFloor.tooltip": "These options determine the opposing temperature for floor heat loss calculations:\n\n<b>Soil:</b> For houses with solid ground as their foundation, such as concrete slab, we use the Average Outside Temperature for the location.\n\n<b>Suspended floors:</b> For houses with air filling the void between the ground and the flooring of the house. Uses the Adjusted Outside Design Temperature.\n\n<b>Heated room:</b> We assume no temperature difference.\n\n<b>Unheated room:</b> We use the Average Outside Temperature for the location.",
  "heatDesign.roomEditor.internalDoorsAndWindowsNotIncluded": "Doors and windows attached to this wall type are not included in heat loss calculation.",
  "heatDesign.roomOutput.helperText": "Cells highlighted in red contain values below the expected range. Please review them carefully for accuracy.",
  "heatDesign.roomRenderer.selectASurface": "Click on items in the sketch or one of the buttons above to view/edit their properties",
  "heatDesign.roomSurfaceTypes.ceilings": "Ceilings",
  "heatDesign.roomSurfaceTypes.doors": "External doors",
  "heatDesign.roomSurfaceTypes.externalDoors": "External doors",
  "heatDesign.roomSurfaceTypes.externalWalls": "External walls",
  "heatDesign.roomSurfaceTypes.externalWindows": "External windows",
  "heatDesign.roomSurfaceTypes.floors": "Floors",
  "heatDesign.roomSurfaceTypes.foundation": "Foundation",
  "heatDesign.roomSurfaceTypes.intermediateFloors": "Intermediate Floors",
  "heatDesign.roomSurfaceTypes.internalWalls": "Internal walls",
  "heatDesign.roomSurfaceTypes.partyWalls": "Party walls",
  "heatDesign.roomSurfaceTypes.roof": "Roof",
  "heatDesign.roomSurfaceTypes.roofGlazings": "Roof glazings",
  "heatDesign.roomSurfaceTypes.roofs": "Roofs",
  "heatDesign.roomSurfaceTypes.roofsOrCeilings": "Roofs or ceilings",
  "heatDesign.roomSurfaceTypes.windows": "Windows",
  "heatDesign.roomType.bathroom": "Bathroom",
  "heatDesign.roomType.bedroom": "Bedroom",
  "heatDesign.roomType.hallway": "Hallway",
  "heatDesign.roomType.kitchen": "Kitchen",
  "heatDesign.roomType.livingRoom": "Living room",
  "heatDesign.roomType.other": "Other",
  "heatDesign.roomValidationModal.title": "Invalid room data",
  "heatDesign.sidebar.title": "Defaults",
  "heatDesign.surfaces.door": "Door",
  "heatDesign.surfaces.doors": "Door",
  "heatDesign.surfaces.externalDoor": "External door",
  "heatDesign.surfaces.externalDoors": "External door",
  "heatDesign.surfaces.externalWall": "External wall",
  "heatDesign.surfaces.externalWalls": "External wall",
  "heatDesign.surfaces.externalWindow": "External window",
  "heatDesign.surfaces.externalWindows": "External window",
  "heatDesign.surfaces.floor": "Floor",
  "heatDesign.surfaces.floors": "Floor",
  "heatDesign.surfaces.foundation": "Foundation",
  "heatDesign.surfaces.intermediateFloors": "Intermediate Floors",
  "heatDesign.surfaces.internalWall": "Internal wall",
  "heatDesign.surfaces.internalWalls": "Internal wall",
  "heatDesign.surfaces.partyWall": "Party wall",
  "heatDesign.surfaces.partyWalls": "Party wall",
  "heatDesign.surfaces.roof": "Roof",
  "heatDesign.surfaces.roofGlazing": "Roof glazing",
  "heatDesign.surfaces.roofGlazings": "Roof glazing",
  "heatDesign.surfaces.roofsOrCeilings": "Roof",
  "heatDesign.surfaces.wall": "Wall",
  "heatDesign.surfaces.window": "Window",
  "heatDesign.surfaces.windows": "Window",
  "heatDesign.surveyForms.download.notSubmitted": "No reports submitted",
  "heatDesign.surveyForms.download.section.description": "Download the survey PDF generated during the project’s on-site scan.",
  "heatDesign.tableHeaders.annualAdditionalEnergyDemand": "Annual additional energy demand",
  "heatDesign.tableHeaders.annualFabricEnergyDemand": "Annual fabric energy demand",
  "heatDesign.tableHeaders.annualHeatingEnergyDemand": "Annual heating energy demand",
  "heatDesign.tableHeaders.annualVentilationEnergyDemand": "Annual ventilation energy demand",
  "heatDesign.tableHeaders.annualWaterEnergyDemand": "Annual water energy demand",
  "heatDesign.tableHeaders.ceilingHeight": "Ceiling height (m)",
  "heatDesign.tableHeaders.dailyWaterEnergyDemand": "Daily water energy demand",
  "heatDesign.tableHeaders.effectiveAirChangesPerHour": "Effective ACPH",
  "heatDesign.tableHeaders.emitterDeltaT": "Emitter ΔT (°C)",
  "heatDesign.tableHeaders.emitterDescription": "Emitter description",
  "heatDesign.tableHeaders.energyDemand": "Energy demand",
  "heatDesign.tableHeaders.existing-new": "Existing/New",
  "heatDesign.tableHeaders.floorArea": "Floor area (m²)",
  "heatDesign.tableHeaders.flowRate": "Flow rate (l/h)",
  "heatDesign.tableHeaders.heatLossSummary": "Heat loss summary",
  "heatDesign.tableHeaders.HouseAcph": "House ACPH",
  "heatDesign.tableHeaders.OutputWatt": "Output (W)",
  "heatDesign.tableHeaders.roomHeatLoss": "Heat Loss",
  "heatDesign.tableHeaders.roomName": "Room name",
  "heatDesign.tableHeaders.TotalAdditionalHeatLoss": "Total additional heat loss",
  "heatDesign.tableHeaders.totalAnnualEnergyDemand": "Total annual energy demand",
  "heatDesign.tableHeaders.TotalFabricHeatLoss": "Total fabric heat loss",
  "heatDesign.tableHeaders.TotalHeatLoss": "Total heat loss",
  "heatDesign.tableHeaders.TotalVentilationHeatLoss": "Total ventilation heat loss",
  "heatDesign.tableHeaders.value": "Value",
  "heatDesign.tableHeaders.ventilationHeatLoss": "Ventilation heat loss",
  "heatDesign.tableHeaders.wattPerSqM": "Heat flux",
  "heatDesign.technicalReport.title": "Technical Report",
  "heatDesign.title.dropdownAlternativeSelect": "Alternatively, select an element from the dropdown",
  "heatDesign.title.dwellingDefaultValues": "House default values",
  "heatDesign.title.floorLevelDefaultValues": "Floor default values",
  "heatDesign.title.floorOverview": "Room details",
  "heatDesign.title.heatLossOverview": "Heat loss results",
  "heatDesign.title.heatPumpConfig": "Heat Pump Config",
  "heatDesign.title.productSelection": "Product selection",
  "heatDesign.title.propertyDetails": "Property overview",
  "heatDesign.title.pulseTestAirPermeability": "Pulse test air permeability",
  "heatDesign.title.radiatorsOverview": "Emitter details",
  "heatDesign.title.resultsExport": "Results & reports",
  "heatDesign.title.selectASurface": "Click on an element to change it",
  "heatDesign.title.surfaceEditor": "{surfaceType} Editor",
  "heatDesign.title.totalArea": "Total area",
  "heatDesign.title.totalVolume": "Total volume",
  "heatDesign.underfloorHeating.editTitle": "Existing underfloor heating",
  "heatDesign.underFloorHeating.UnderfloorHeating": "Underfloor Heating",
  "heatDesign.unlock.confirmation": "<strong>Warning:</strong> Are you sure you want to unlock the design? If there have been significant changes to how the Design tool works, you might be prompted for more input before being able to lock again.",
  "heatDesign.usingDefaultUValue.dwelling": "Using <strong>house</strong> default U-value",
  "heatDesign.usingDefaultUValue.floorLevel": "Using <strong>floor level</strong> default U-value",
  "heatDesign.usingDefaultUValue.floorLevelAbove": "Using \"floor\" U-value from <strong>floor level above</strong>",
  "heatDesign.uValues.addCustomUValue": "Add Custom U-value",
  "heatDesign.uValues.addCustomUValue.save": "Save & use",
  "heatDesign.uValues.name": "{surfaceType} Value",
  "heatDesign.uValues.placeholder": "Pick a U-value",
  "heatDesign.uValues.placeholder.noFabricTypeInProject": "No {fabricType} on this floor",
  "heatDesign.uValues.placeholder.setAtDwelling": "This U-value is set at the house level",
  "heatDesign.uValues.uValue": "U-value",
  "heatDesign.uValues.uValues": "U-values",
  "heatDesign.uValues.valueLabel": "Value",
  "heatDesign.uValues.worseThanBuildingRegulations": "Beware that the values below are worse than the building regulations for houses built this year.",
  "heatDesign.uValues.worseThanBuildingRegulations.v2": "Beware that the values below are worse than the building regulations for {constructionYear}, when this house was built.",
  "heatDesign.validationModal.customValue.missingOrInvalid": "You must enter a value above zero and a name for the U-value.",
  "heatDesign.validationModal.missingOrInvalid": "Missing or invalid {property}",
  "heatDesign.ventilation.acphDefaults": "ACPH defaults",
  "heatDesign.ventilation.acphDefaultsDisabledPulseTest": "EN12831 minimum ACPH is used for pulse tests.",
  "heatDesign.ventilation.airPermeability": "Air permeability",
  "heatDesign.ventilation.calculationMethod": "Calculation method",
  "heatDesign.ventilation.exposureOfHouse": "Exposure of house",
  "heatDesign.ventilation.houseWideAcph": "Housewide ACPH",
  "heatDesign.ventilation.houseWideAcphTooltip": "Setting a housewide ACPH will apply it across all rooms. The ACPH in specific rooms can later be adjusted individually.",
  "heatDesign.ventilation.roomType": "Room type",
  "heatDesign.ventilation.ventilation": "Ventilation",
  "heatDesign.wallsRenderer.adjoiningRoom.heated": "Heated",
  "heatDesign.wallsRenderer.adjoiningRoom.roomNameLabel": "Room",
  "heatDesign.wallsRenderer.adjoiningRoom.tempLabel": "Temperature",
  "heatDesign.wallsRenderer.adjoiningRoom.title": "Adjoining room",
  "heatDesign.wallsRenderer.adjoiningRoom.unheated": "Unheated",
  "heatDesign.wallsRenderer.area": "Area",
  "heatDesign.wallsRenderer.area.tooltip": "This is the complete area of the wall <strong>including</strong> eventual windows and doors. When calculating the heat loss calculations, we subtract the area of said windows and doors from the wall to get the correct heat loss through the wall.",
  "heatDesign.wallsRenderer.length": "Length",
  "heatDesign.wallsRenderer.soilPercentage.label": "External wall surface in contact with soil",
  "heatDesign.wallsRenderer.soilPercentage.resetToDefaults": "Reset to floor defaults",
  "heatDesign.wallsRenderer.soilPercentage.tooltip": "For the parts of the wall in contact with soil, we use the average outside temperature as the opposing temperature, and not the Outside Design Temperature (ODT).",
  "heatDesign.waterReheatTime": "The maximum time needed to reheat your tank from cold. However, your heat pump is running continuously and reheating water as you use it.",
  "heatPumpConfig.advancedSettings": "Advanced heat pump settings",
  "heatPumpConfig.advancedSettings.cooling.title": "Cooling",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating": "How much additional electric heating do you want to allow?",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.0kwHeating3kwBackup": "0 kW heating,{br}3 kW backup",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.3kwHeating3kwBackup": "3 kW heating,{br}3 kW backup",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.6kwHeating3kwBackup": "6 kW heating,{br}3 kW backup",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.bridgeWarning": "Note that if the indoor unit is connected with only one phase, the installer must remember to bridge the heating elements to activate them. Not doing this will cause alarms.",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.tooltip": "A 3 kW on the same phase as the outdoor unit is always added to avoid hardware failure during subzero temperatures.",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold": "Outdoor temperature threshold for <strong>cooling</strong> (°C)",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold.tooltip": "When the average outdoor temperature over 24 hours is <strong>below</strong> this value, cooling is <strong>blocked</strong>. This value must be 3 °C above the threshold for heating. Default: 22.0",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor": "Energy balance to start <strong>compressor</strong> (degree minutes) ",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor.tooltip": "The heat pump pump keeps track of how \"far behind\" it is the target temperature, a concept called energy balance. Once the balance is <strong>below this threshold</strong>, the <strong>compressor</strong> kicks in. By changing this number, you can control how often the heat pump starts and stops.{br}{br}Default: -30",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating": "Energy balance to start <strong>immersion heater</strong> (degree minutes)",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating.tooltip": "The heat pump pump keeps track of how \"far behind\" it is the target temperature, a concept called energy balance. Once the balance is <strong>below this threshold</strong>, the <strong>immersion heater</strong> is allowed to kick in. By changing this number, you can control how often the immersion heater is used.{br}{br}Default: -600",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold": "Outdoor temperature threshold for <strong>heating</strong> (°C)",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold.tooltip": "When the average outdoor temperature over 24 hours is <strong>above</strong> this value, heating is <strong>blocked</strong>. This means circulation pumps are also blocked. Default: 17.0",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold": "Outdoor temperature threshold for <strong>immersion heating</strong> (°C)",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold.tooltip": "When the average outdoor temperature over 24 hours is <strong>above</strong> this value, immersion heater is <strong>blocked</strong>. Default: 6.0",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater": "Priority time for <strong>domestic hot water</strong> (minutes)",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater.tooltip": "For how long the system prioritize heating up domestic hot water until switching to heating the house. Max: 180, Default: 30",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating": "Priority time for <strong>heating house</strong> (minutes)",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating.tooltip": "For how long the system prioritize heating the house until switching to domestic hot water. Max: 180, Default: 15",
  "heatPumpConfig.advancedSettings.heating.title": "Heating",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp": "Cooling supply temperatures for <strong>Zone {zone}</strong> (°C)",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp.tooltip": "This threshold is to protect the system from too low or high temperatures that can harm pipes or other hardware.{br}Default: 7.0 & 20.0",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp": "<strong>Minimum</strong> heating supply temperature for <strong>Zone {zone}</strong> (°C)",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp.tooltip": "This threshold can be adjusted to have some \"basic heating\" in colder areas such as a basement or floor heating. Default: 10.0",
  "heatPumpConfig.advancedSettingsDescription": "Only edit the values below <strong>if you know what you are doing</strong> and if there is a specific need for this customer.",
  "heatPumpConfig.domesticHotWater.tankSizeMismatch": "Note: The chosen domestic hot water tank size does not match the domestic hot water tank size selected during product selection <strong>{selectedDomesticHotWaterTankSize}</strong>. You may proceed if this is intentional.",
  "heatPumpConfig.general.dhw100": "100 litres",
  "heatPumpConfig.general.dhw150": "150 litres",
  "heatPumpConfig.general.dhw200": "200 litres",
  "heatPumpConfig.general.dhw250": "250 litres",
  "heatPumpConfig.general.dhw300": "300 litres",
  "heatPumpConfig.general.dhwNone": "None",
  "heatPumpConfig.general.domesticHotWaterTankSize": "Domestic hot water tank size (DHW)",
  "heatPumpConfig.general.domesticHotWaterTankSize.outdoorCapacityConstraintViolation": "The combination found in the {productSelectionPage} of a <strong>{indoorUnit}</strong> indoor unit with a <strong>{selectedOutdoorCapacity}</strong> outdoor unit is not allowed. The <strong>{indoorUnit}</strong> indoor unit can only be used with the <strong>{allowedOutdoorUnits}</strong> outdoor unit(s). Please go back and change this before continuing with the configuration",
  "heatPumpConfig.general.electricityMeterType": "Type of power meter",
  "heatPumpConfig.general.electricityMeterTypeDisclaimer": "<b>Note:</b> The power meter is ordered separately and is available in both a 1-phase and 3-phase version. Make sure you select the same type here that is being ordered in the ERP.",
  "heatPumpConfig.general.et112": "1-Phase",
  "heatPumpConfig.general.et340": "3-Phase",
  "heatPumpConfig.general.hotWaterTemperature": "Water temperature: {degrees} °C",
  "heatPumpConfig.general.title": "General parameters",
  "heatPumpConfig.indoorUnitType.hydrobox": "Compact",
  "heatPumpConfig.indoorUnitType.mismatch": "Note: The chosen indoor unit type does not match the indoor unit type selected during product selection <strong>{indoorUnitType}</strong>. You may proceed if this is intentional.",
  "heatPumpConfig.indoorUnitType.title": "All-in-one or Compact solution?",
  "heatPumpConfig.indoorUnitType.tooltip": "What will be installed at the customers property? An All-in-one unit where all indoor units are grouped up, or a Compact solution where the water tank is separate from the rest.",
  "heatPumpConfig.indoorUnitType.unitower": "All-in-one",
  "heatPumpConfig.lastUpdatedAt": "Heat pump parameters were stored at {updatedAt}",
  "heatPumpConfig.neverSaved": "This heat pump configuration has never been saved.",
  "heatPumpConfig.notAiraBrand.description": "The heat pump associated with this solution is not an Aira brand unit. Please verify compatibility or select an Aira heat pump in the product selection.",
  "heatPumpConfig.notAiraBrand.title": "Non-Aira heat pump detected",
  "heatPumpConfig.outdoorUnitCapacity.capacity12kw": "12 kW",
  "heatPumpConfig.outdoorUnitCapacity.capacity6kw": "6 kW",
  "heatPumpConfig.outdoorUnitCapacity.capacity8kw": "8 kW",
  "heatPumpConfig.outdoorUnitCapacity.capacityMismatch": "Note: The chosen outdoor unit does not match the outdoor unit selected during product selection <strong>{selectedOutdoorUnitCapacity}</strong>. You may proceed if this is intentional.",
  "heatPumpConfig.outdoorUnitCapacity.title": "Outdoor unit capacity",
  "heatPumpConfig.outdoorUnitCapacity.tooltip": "The water temperature can be adjusted later in the Aira app.",
  "heatPumpConfig.output": "Output",
  "heatPumpConfig.saveButtonTitle": "Save & export",
  "heatPumpConfig.saveButtonTitle.saving": "Saving...",
  "heatPumpConfig.silent.mode": "Should the unit run in silent mode?",
  "heatPumpConfig.silent.mode.tooltip": "This mode limits the compressor (to 67 Hz) and fan speed (to 620 rpm) during the night (between 22-06) and lowers the noise to the following levels:\nAira 6 Gen1: 57,5 dB(A)\nAira 8 Gen1: 58,8 dB(A)\nAira 12 Gen1: 63,4 dB(A)",
  "heatPumpConfig.title": "Aira Heat Pump configuration",
  "heatPumpConfig.zone.coolingCurveTitle": "Cooling curve for zone {zone}",
  "heatPumpConfig.zone.emitterType.radiator": "Radiator",
  "heatPumpConfig.zone.emitterType.title": "Emitter type",
  "heatPumpConfig.zone.emitterType.underfloor": "Underfloor",
  "heatPumpConfig.zone.features.both": "Both",
  "heatPumpConfig.zone.features.cooling": "Cooling",
  "heatPumpConfig.zone.features.heating": "Heating",
  "heatPumpConfig.zone.features.title": "Features",
  "heatPumpConfig.zone.flowTemperature.subtitle": "Flow temperature at ODT (limit is 5 °C lower than max for chosen emitter). During the heat design process, this was set at <b>{heatDesignFlowTemperature} °C</b>.",
  "heatPumpConfig.zone.flowTemperature.title": "Flow temperature (°C)",
  "heatPumpConfig.zone.flowTemperature.tooltip": "Underfloor (45 °C): Max allowed initial value is 40 °C.\nRadiator (70 °C): Max allowed initial value is 65 °C.\nThe reason we impose this limit here is that we want both the customer and our optimisation algorithms to be able to increase the temperature up to 5 degrees if the house is too cold. ",
  "heatPumpConfig.zone.flowTemperature.warning": "Flow temperature cannot be greater than Zone 1 flow temperature",
  "heatPumpConfig.zone.graph.coolingCurveInvalid": "Invalid curve. Verify your inputs so that the curve is always decreasing and within limits.",
  "heatPumpConfig.zone.graph.flowTemperature": "Target flow temperature (°C)",
  "heatPumpConfig.zone.graph.heatingCurveInvalid": "Invalid curve. Verify your inputs so that the curve is always increasing and within limits.",
  "heatPumpConfig.zone.graph.odtFlowPointLabel": "ODT/Flow",
  "heatPumpConfig.zone.graph.outdoorDesignTemperature": "Outside temperature (°C)",
  "heatPumpConfig.zone.heatingCurveTitle": "Heating curve for zone {zone}",
  "heatPumpConfig.zone.outdoorDesignTemperature.subtitle": "Design temperature at install location (ODT). During the heat design process, this was set at <b>{heatDesignOdt} °C</b>.",
  "heatPumpConfig.zone.outdoorDesignTemperature.title": "Outdoor design temperature (°C)",
  "heatPumpConfig.zone.temperaturePlaceholder": "Temperature in (°C)",
  "heatPumpConfig.zone.thermostatType.helperText": "Thermostat options for Zone 2 are limited based on the thermostat options chosen for Zone 1.",
  "heatPumpConfig.zone.thermostatType.title": "Type of thermostat",
  "heatPumpConfig.zone.thermostatType.wired": "Wired",
  "heatPumpConfig.zone.thermostatType.wireless": "Wireless",
  "heatPumpConfig.zone.title": "Zone {zone}",
  "heatPumpConfig.zonesConfiguration.diagram.altText": "Diagram of the heat pump zone configuration",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.description": "<b>1 zone - no mixing valve:</b> The most common use case. Can be used even if the house has both radiators and underfloor heating, provided the underfloor heating has an independent controller or temperature limiter.",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.label": "1 zone - no mixing valve",
  "heatPumpConfig.zonesConfiguration.title": "Configuration",
  "heatPumpConfig.zonesConfiguration.tooltip": "For Aira heat pumps, \"zones\" are defined as the area over which we have direct control. Underfloor heating or radiators with a TRV themselves are considered a \"sub-zone\" that Aira cannot control and would not count as a \"zone\". A system with 1 zone and UFH would only count as 1 \"zone\".",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.description": "<b>2 zones - mixing valve in Zone 2:</b> Zone 1 will have the same temperature as the buffer tank and Zone 2 can be mixed to a lower temperature. Typical use cases: radiators in Zone 1 and underfloor heating without a separate manifold in Zone 2, or zones that heat different floors to different temperatures.",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.label": "2 zones - mixing valve in Zone 2",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.description": "<b>2 zones - mixing valves in both zones:</b> Useful when either zone could call for the highest temperature. The zone with the highest temperature demand will set the buffer temperature. Another use case is having heating in one zone and cooling in the other zone.",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.label": "2 zones - mixing valves in both zones",
  "hlc.label.save": "Save design",
  "hlc.label.save.and.lock": "Save & lock design",
  "hlc.label.unlock.design": "Unlock design",
  "hlc.unsavedChanges.modal.description": "Please note that you have unsaved changes. If you want to go to the next page, please save the changes first.",
  "hlc.unsavedChanges.modal.tittle": "Unsaved Changes Noticed",
  "houseData.addressChangeConfirmation.title": "Are you sure that the address in ERP is updated?",
  "houseData.addressChangeModal.distanceBetweenOldAndNewAddress": "Distance between old and new address:",
  "houseData.addressChangeModal.ensureSurveysAndInstallationsAreAssignedCorrectly": "Ensure scheduled surveys and installations are assigned correctly.",
  "houseData.addressChangeModal.timeBetweenOldAndNewAddress": "Time between old and new address:",
  "houseData.addressChangeModal.updateAddressInErp": "I have updated the address in the ERP",
  "houseData.addressChangeModal.urgent": "Urgent: Please update the address in the ERP system immediately.",
  "houseData.addressChangeModal.warning": "Warning - address change detected",
  "houseData.fuelType.GAS": "Gas",
  "houseData.fuelType.LIQUID_GAS": "Liquid gas",
  "houseData.fuelType.OIL": "Oil",
  "houseData.houseType.APARTMENT": "Apartment",
  "houseData.houseType.BUNGALOW": "Bungalow",
  "houseData.houseType.DETACHED": "Detached",
  "houseData.houseType.SEMI_DETACHED": "Semi-detached",
  "houseData.houseType.TERRACED": "Terraced",
  "houseData.label.bedroomCount": "Number of bedrooms",
  "houseData.label.fuelConsumption": "Fuel consumption",
  "houseData.label.houseSize": "House size",
  "houseData.label.houseType": "House type",
  "houseData.label.housingUnits": "Housing units",
  "houseData.label.numberOfPanels": "Number of panels",
  "houseData.label.postalCode": "Postal code: ",
  "houseData.notification.isAddressContainer": "The selected address contains multiple addresses. Please select the correct one.",
  "houseData.title.consumptionEstimate": "Consumption estimate",
  "houseData.title.solarPanelsInstalled": "Solar panels installed",
  "infobar.airaZeroSignedAt": "Customer invited to Aira Zero, at ",
  "infoBar.contactId": "Contact ID",
  "infoBar.downloadSignedQuote": "Download accepted quote",
  "infoBar.emailAddress": "Email address",
  "infoBar.errorDownloadingSignedQuote": "An error occurred while trying to download the signed quote",
  "infoBar.expiryDate": "Quote expiration date",
  "infoBar.mobileNumber": "Mobile number",
  "infoBar.phoneNumber": "Phone number",
  "infoBar.previewQuote": "Preview quote",
  "infoBar.reference": "Order Reference",
  "infoBar.resendTariffEmail": "Resend Aira Zero invite",
  "infoBar.sendTariffEmail": "Invite to Aira Zero",
  "infoBar.showSignableQuote": "Present quote",
  "infoBar.signing.cancel": "Cancel",
  "infoBar.signing.content": "Select preferred payment method",
  "infoBar.signing.optionInstalments": "Financing",
  "infoBar.signing.optionInstalmentsFinancing": "Financing",
  "infoBar.signing.optionInvoice": "Pay in full",
  "infoBar.signing.title": "Present a quote",
  "infobar.tariff.modal.description": "This sends an email to the customer with a link to sign up for Aira Zero.\n\nNote: All customers are sent an automated email to join Aira Zero on the day the installation is completed.",
  "infoBar.tariff.modal.title": "Invite customer to Aira Zero?",
  "infoBar.viewAcceptedQuote": "Accepted quote",
  "infoBar.viewFinalOrderSummary": "Final order summary",
  "infoBar.viewOrderSummary": "Current order summary",
  "installationBooking.emptyState.noInstallation": "You can book installation jobs here when the job is ready for order.",
  "installationBooking.errors.hours": "Total man hours must be a positive value or 0",
  "installationBooking.fields.hours": "Total man hours",
  "installationBooking.fields.jobsPerDay": "Max jobs per day and person",
  "installationBooking.fields.jobsPerDayCaption": "Use 0 to not set a limit",
  "installationBooking.main.bookButton": "Create jobs in Skedulo",
  "installationBooking.main.description": "These values will be pre-filled from the results of the Installation complexity tool.",
  "installationBooking.main.descriptionfrombaseline": "These values are pre-filled from the results of the Installation complexity tool.",
  "installationBooking.main.title": "Book installation",
  "installationBooking.moreOptions": "More options",
  "installationBooking.notify.booking": "Booking installation jobs...",
  "installationBooking.notify.bookingCreated": "Installation job created",
  "installationBooking.roles.electrician": "Electrician",
  "installationBooking.roles.installer": "Clean Energy Technician team",
  "installationBooking.roles.landscaper": "Landscaper",
  "installationHandover.addUrl": "Add Url\n",
  "installationHandover.appButtonTitle": "Go to Aira app to configure heat pump",
  "installationHandover.externalLinks.deviationsTracker": "Deviations tracker",
  "installationHandover.externalLinks.fillInInstallationReport": "Fill in installation report",
  "installationHandover.externalLinks.heatPumpConfiguration": "Heat pump configuration",
  "installationHandover.extraDocuments.downloadPDF": "Download Magicplan PDF",
  "installationHandover.extraDocuments.openMagicplanApp": "Open Magicplan app",
  "installationHandover.extraDocuments.openMagicplanWebsite": "Open Magicplan website",
  "installationHandover.extraDocuments.sharepointFolder": "Sharepoint folder",
  "installationHandover.floorPlans.disclaimer": "Not all objects listed may be in the floor plans. Only objects added during the survey are shown.",
  "installationHandover.hardware.common.unspecified": "Unspecified",
  "installationHandover.hardware.electrical.airaFuseBoxLocationPhotos": "Aira fuse box location photos",
  "installationHandover.hardware.electrical.cableCrossSection": "Cable cross section [mm²]",
  "installationHandover.hardware.electrical.cableRunsDrawnOnFloorPlan": "Cable runs drawn on floor plan",
  "installationHandover.hardware.electrical.comment": "Comment",
  "installationHandover.hardware.electrical.earthingArrangement": "Earthing arrangement",
  "installationHandover.hardware.electrical.earthingArrangementCustomerSubstation": "Customer substation",
  "installationHandover.hardware.electrical.earthingArrangementTnCs": "TN-C-S",
  "installationHandover.hardware.electrical.earthingArrangementTnS": "TN-S",
  "installationHandover.hardware.electrical.earthingArrangementTt": "TT",
  "installationHandover.hardware.electrical.electricityBillPhotos": "Electricity bill photos",
  "installationHandover.hardware.electrical.electricityNetworkOperator": "Electricity network operator",
  "installationHandover.hardware.electrical.fuseBoardPhotos": "Fuse board photos",
  "installationHandover.hardware.electrical.isIncomingThreePhase": "Is the incoming power cable 3-phase?",
  "installationHandover.hardware.electrical.loopedService": "Looped service",
  "installationHandover.hardware.electrical.mainFuseRating": "Main fuse rating [Amps]",
  "installationHandover.hardware.electrical.maxDemandTest": "Max demand test",
  "installationHandover.hardware.electrical.meterBoxPhotos": "Meter box photos",
  "installationHandover.hardware.electrical.mpanNumber": "MPAN number",
  "installationHandover.hardware.electrical.phases": "Phases",
  "installationHandover.hardware.electrical.singlePhase": "Single phase",
  "installationHandover.hardware.electrical.spaceForNewCables": "Space for new cables",
  "installationHandover.hardware.electrical.threePhase": "Three phase",
  "installationHandover.hardware.existingHeatSource.a2aHp": "A2A HP",
  "installationHandover.hardware.existingHeatSource.a2wHp": "A2W HP",
  "installationHandover.hardware.existingHeatSource.additionalHotWaterTankPhotos": "Picture of additional hot water tank (if present)",
  "installationHandover.hardware.existingHeatSource.boilerType": "Boiler type?",
  "installationHandover.hardware.existingHeatSource.boilerTypeCombi": "Combi",
  "installationHandover.hardware.existingHeatSource.boilerTypeRegular": "Regular",
  "installationHandover.hardware.existingHeatSource.boilerTypeSystem": "System",
  "installationHandover.hardware.existingHeatSource.coldWaterPressureBar": "Pressure of cold water (bar)",
  "installationHandover.hardware.existingHeatSource.comments": "Comments",
  "installationHandover.hardware.existingHeatSource.connectionType": "How is the connection to the heating circuits made?",
  "installationHandover.hardware.existingHeatSource.connectionTypeMetal": "Metal",
  "installationHandover.hardware.existingHeatSource.connectionTypePlastic": "Plastic",
  "installationHandover.hardware.existingHeatSource.consumptionKwh": "Consumption [kWh]",
  "installationHandover.hardware.existingHeatSource.consumptionLitres": "Consumption [litres]",
  "installationHandover.hardware.existingHeatSource.coveringType": "What do we cover the vent with?",
  "installationHandover.hardware.existingHeatSource.eaHp": "EA HP",
  "installationHandover.hardware.existingHeatSource.electricHeaters": "Electric heaters",
  "installationHandover.hardware.existingHeatSource.electricityConsumptionMeasurements": "Electricity consumption measurements (kWh)",
  "installationHandover.hardware.existingHeatSource.existingHeatingSourcePhotos": "Photos of existing heating source and cylinders",
  "installationHandover.hardware.existingHeatSource.flowTemperatureCelsius": "What flow temperature can be seen?",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypeNeedsBrickingUp": "Needs bricking up",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypePlastic": "Plastic",
  "installationHandover.hardware.existingHeatSource.flueNeedsCovering": "Is there a flue that needs covering up?",
  "installationHandover.hardware.existingHeatSource.fluePhotos": "Photo of flue with surrounding wall",
  "installationHandover.hardware.existingHeatSource.gasBoiler": "Gas boiler",
  "installationHandover.hardware.existingHeatSource.gasBoilerTechnicalDataPhotos": "Attach picture of gas boiler technical data",
  "installationHandover.hardware.existingHeatSource.gasConsumptionMeasurements": "Gas consumption measurements (kWh)",
  "installationHandover.hardware.existingHeatSource.gasMeterNumber": "Make a note of the gas meter number",
  "installationHandover.hardware.existingHeatSource.gasMeterPhotos": "Photo of the gas meter",
  "installationHandover.hardware.existingHeatSource.gasNetworkOperator": "Who is the gas supply network operator? (Network operator is not necessarily the supplier!)",
  "installationHandover.hardware.existingHeatSource.gsHp": "GS HP",
  "installationHandover.hardware.existingHeatSource.hasSeparateCirculationPump": "Is there a separate circulation pump",
  "installationHandover.hardware.existingHeatSource.heatRegulation": "Heat regulation",
  "installationHandover.hardware.existingHeatSource.heatRegulationFixedTemperature": "Fixed temperature",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightLowering": "Night lowering",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightOff": "Night off",
  "installationHandover.hardware.existingHeatSource.heatRegulationTurnOnWhenPresent": "Turn on when present",
  "installationHandover.hardware.existingHeatSource.heatSourceType": "Current heating source",
  "installationHandover.hardware.existingHeatSource.hotWaterProduction": "Hot water is produced via",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionExistingHeatSource": "Existing heat source",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherIntegrationDesired": "Other integration desired",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherNoIntegrationDesired": "Other no integration desired",
  "installationHandover.hardware.existingHeatSource.isCondensingBoiler": "Is it a condensing boiler?",
  "installationHandover.hardware.existingHeatSource.lpgBoiler": "LPG boiler",
  "installationHandover.hardware.existingHeatSource.numberOfHeatingZones": "Number of heating zones",
  "installationHandover.hardware.existingHeatSource.oilBurner": "Oil burner",
  "installationHandover.hardware.existingHeatSource.oilConsumptionMeasurements": "Oil Consumption measurements (litres)",
  "installationHandover.hardware.existingHeatSource.oilLeftLitres": "How much oil is left in the tanks?",
  "installationHandover.hardware.existingHeatSource.otherComponentsPhotos": "Photos of other components (zone valves, manifolds)",
  "installationHandover.hardware.existingHeatSource.pellets": "Pellets",
  "installationHandover.hardware.existingHeatSource.pipeSizeToDhw": "Current pipe size to DHW",
  "installationHandover.hardware.existingHeatSource.pipeSizeToExistingBoiler": "Current pipe size to existing boiler",
  "installationHandover.hardware.existingHeatSource.removeExistingHeatingSource": "Should we remove the existing heating source?",
  "installationHandover.hardware.existingHeatSource.showerPumpNeedsReplacement": "Is there an existing shower pump that needs removing and piping up?",
  "installationHandover.hardware.existingHeatSource.solarThermal": "Solar thermal",
  "installationHandover.hardware.existingHeatSource.underfloorHeating": "Does the house have water underfloor heating?",
  "installationHandover.hardware.existingHeatSource.underfloorHeatingManifoldPhotos": "Photos of underfloor heating manifolds?",
  "installationHandover.hardware.existingHeatSource.ventingType": "Is it open vented?",
  "installationHandover.hardware.existingHeatSource.ventingTypeOpenVented": "Open vented",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnknown": "Unknown",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnvented": "Unvented",
  "installationHandover.hardware.existingHeatSource.year": "Year",
  "installationHandover.hardware.existingHeatSource.yearOfManufacture": "Estimated manufacturing year of current heating system",
  "installationHandover.hardware.existingHeatSource.yesRemoveExistingHeatSource": "Yes",
  "installationHandover.hardware.existingHeatSource.yesUnderfloorHeating": "Yes",
  "installationHandover.hardware.indoorInstallation.allInOneUnitFits": "Will an All-in-one/Unitower fit?",
  "installationHandover.hardware.indoorInstallation.asbestosRisk": "Is there any asbestos in the property at risk of disturbance?",
  "installationHandover.hardware.indoorInstallation.bufferTankSize": "Max buffer tank size that would fit?",
  "installationHandover.hardware.indoorInstallation.bufferTankSize100L": "100L",
  "installationHandover.hardware.indoorInstallation.bufferTankSize45L": "45L",
  "installationHandover.hardware.indoorInstallation.chipBoardFloorType": "Chip board",
  "installationHandover.hardware.indoorInstallation.coldWaterFlowRate": "Flow rate of cold water (litres / minute)",
  "installationHandover.hardware.indoorInstallation.comments": "Comments (Potential obstacles on the way, steps, narrow door etc)",
  "installationHandover.hardware.indoorInstallation.componentsShouldFitWhenTilted": "(Something about measuring distance when tilted, e.g. in a staircase)",
  "installationHandover.hardware.indoorInstallation.crampedInstallationLocation": "Cramped location for installers? E.g. only one person at a time can work.",
  "installationHandover.hardware.indoorInstallation.distanceBetweenTundishAndTermination": "Distance between tundish and termination (D2) [m]",
  "installationHandover.hardware.indoorInstallation.distanceOfNewPrimaryPipeworkNeeded": "Distance of new primary pipework needed indoors [m] (perhaps existing pipework can be re-used)",
  "installationHandover.hardware.indoorInstallation.drainPipePresent": "Is there a drain pipe present?",
  "installationHandover.hardware.indoorInstallation.drinkingWaterFilter": "Is there a drinking water filter",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupply": "How is the house supplied with drinking water?",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyNetwork": "Drinking water network",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyOwnWell": "Own well",
  "installationHandover.hardware.indoorInstallation.emergencyDischargePipeReusable": "Is there an emergency discharge pipe we can re-use?",
  "installationHandover.hardware.indoorInstallation.floorBoardFloorType": "Floor board",
  "installationHandover.hardware.indoorInstallation.floorType": "What type of floor?",
  "installationHandover.hardware.indoorInstallation.hydraulicUnitFits": "Will a Hydraulic unit (770x440x350) fit?",
  "installationHandover.hardware.indoorInstallation.indoorPrimaryPipingRoutePhotos": "Photos of indoor primaries route",
  "installationHandover.hardware.indoorInstallation.installationLocationPhotos": "Photos of installation location, including path to it",
  "installationHandover.hardware.indoorInstallation.largestAllInOneSizeThatFits": "Largest all-in-one unit that will fit",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits": "Largest DHW tank (cylinder) that will fit?",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits150L": "150L (955x595 mm)",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits200L": "200L (1265x595 mm)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsLarge": "Large (1880x600x600)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsSmall": "Small (1100x500x500)",
  "installationHandover.hardware.indoorInstallation.minimumPathHeight": "Minimum height of the path to installation location [m]",
  "installationHandover.hardware.indoorInstallation.minimumPathWidth": "Minimum width of the path to installation location [m]",
  "installationHandover.hardware.indoorInstallation.pipeLayoutDrawn": "Have you drawn the pipe layout from Outdoor unit to Indoor unit?",
  "installationHandover.hardware.indoorInstallation.plasticDuctOnPipesNeeded": "Does the customer want a plastic duct on pipes going from indoor unit to wall?",
  "installationHandover.hardware.indoorInstallation.primaryRunsUnderneathFloor": "Will we have to install primary runs underneath existing floor?",
  "installationHandover.hardware.indoorInstallation.wifiReachesInstallationLocation": "Have you verified that the customer's WiFi reaches the installation location?",
  "installationHandover.hardware.outdoorInstallation.comments": "Comments",
  "installationHandover.hardware.outdoorInstallation.condensationPipedToDrain": "Piped to drain",
  "installationHandover.hardware.outdoorInstallation.condensationSoakAway": "Soak away",
  "installationHandover.hardware.outdoorInstallation.condensationSolution": "How do we deal with condensation water?",
  "installationHandover.hardware.outdoorInstallation.distance": "Distance (m)",
  "installationHandover.hardware.outdoorInstallation.distanceFromOutdoorUnitToWall": "Distance from outdoor unit to wall (m)",
  "installationHandover.hardware.outdoorInstallation.distanceOfTrunkingRequired": "Distance of trunking required [m]",
  "installationHandover.hardware.outdoorInstallation.extraResourcesNeeded": "Extra resources needed (apart from the 2 man installation team) to place the Outdoor Unit",
  "installationHandover.hardware.outdoorInstallation.holePlacement": "Hole placement",
  "installationHandover.hardware.outdoorInstallation.holePlacementAboveGround": "Above ground",
  "installationHandover.hardware.outdoorInstallation.holePlacementBelowGround": "Below ground",
  "installationHandover.hardware.outdoorInstallation.holePlacementHighAboveGround": "High above ground (ladder required)",
  "installationHandover.hardware.outdoorInstallation.isGroundworkRequired": "Is groundwork required? Example if ground is not level.",
  "installationHandover.hardware.outdoorInstallation.isOutdoorPipingBelowGroundNeeded": "Is outdoor piping below ground needed?",
  "installationHandover.hardware.outdoorInstallation.isOutdoorTrunkingRequired": "Is outdoor trunking required?",
  "installationHandover.hardware.outdoorInstallation.minimumDistanceToClosestOpening": "Minimum distance to closest opening (meters)",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnGround": "Ground",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnWall": "Wall mount",
  "installationHandover.hardware.outdoorInstallation.obstacleElectricalSocket": "Electrical socket",
  "installationHandover.hardware.outdoorInstallation.obstacleOutsideTab": "Outside tab",
  "installationHandover.hardware.outdoorInstallation.obstacles": "Obstacles",
  "installationHandover.hardware.outdoorInstallation.obstacleSoilPipe": "Soil pipe",
  "installationHandover.hardware.outdoorInstallation.obstaclesToRemove": "Obstacles to remove",
  "installationHandover.hardware.outdoorInstallation.otherCondensationSolution": "Other condensation solution",
  "installationHandover.hardware.outdoorInstallation.outdoorPrimaryPipingRoutePhotos": "Outdoor primary piping route photos",
  "installationHandover.hardware.outdoorInstallation.outdoorUnitMounting": "Outdoor unit mounting",
  "installationHandover.hardware.outdoorInstallation.proposedInstallationLocationPhotos": "Proposed installation location photos",
  "installationHandover.hardware.outdoorInstallation.shinglesRequired": "Is shingles required? (not needed if existing ground is pourous)",
  "installationHandover.hardware.outdoorInstallation.trenchDigger": "Who will dig the trench?",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerAira": "Aira",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerCustomer": "Customer",
  "installationHandover.hardware.outdoorInstallation.trunkingColour": "Colour of trunking",
  "installationHandover.hardware.outdoorInstallation.trunkingColourBlack": "Black",
  "installationHandover.hardware.outdoorInstallation.trunkingColourWhite": "White",
  "installationHandover.hardware.outdoorInstallation.wallMaterial": "Wall material for Primary Runs",
  "installationHandover.hardware.outdoorInstallation.wallMaterialBrick": "Brick",
  "installationHandover.hardware.outdoorInstallation.wallMaterialConcrete": "Concrete",
  "installationHandover.hardware.outdoorInstallation.wallMaterialHardStone": "Hard stone",
  "installationHandover.hardware.outdoorInstallation.wallMaterialNaturalStone": "Natural stone",
  "installationHandover.hardware.outdoorInstallation.wallMaterialPerforatedBrick": "Perforated brick",
  "installationHandover.hardware.outdoorInstallation.wallMaterialReinforcedConcrete": "Reinforced concrete",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSoftStone": "Soft stone",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSolidBrick": "Solid brick",
  "installationHandover.hardware.outdoorInstallation.wallMaterialWood": "Wood",
  "installationHandover.hardware.outdoorInstallation.wallThickness": "Wall thickness (for primary run piping)",
  "installationHandover.hardware.outdoorInstallation.yesObstaclesToRemove": "Yes",
  "installationHandover.hardware.outdoorInstallation.yesOutdoorPipingBelowGroundNeeded": "Yes",
  "installationHandover.hardware.title.electrical": "Electrical",
  "installationHandover.hardware.title.existingHeatSource": "Existing heat source",
  "installationHandover.hardware.title.indoorInstallation": "Indoor installation",
  "installationHandover.hardware.title.outdoorInstallation": "Outdoor installation",
  "installationHandover.heatPumpConfigAvailable": "Heat pump configuration parameters are available for this installation.",
  "installationHandover.noInstallation": "No installation",
  "installationHandover.objectDetailsPanel.buttons.info": "Info",
  "installationHandover.objectDetailsPanel.buttons.photos": "Photos",
  "installationHandover.objectDetailsPanel.radiator": "Radiator",
  "installationHandover.pasteLink": "Paste link",
  "installationHandover.projectOveriew.title.mainTitle": "Project Overview",
  "installationHandover.projectOveriew.title.product": "Product",
  "installationHandover.projectOveriew.title.quantity": "Quantity",
  "installationHandover.projectOverview.people": "People",
  "installationHandover.radiator": "Radiator",
  "installationHandover.radiatorInfoPanel.comment": "Comment:",
  "installationHandover.radiatorInfoPanel.deltaT": "DeltaT:",
  "installationHandover.radiatorInfoPanel.electricRadiator": "Electric Radiator",
  "installationHandover.radiatorInfoPanel.enabled": "Enabled?:",
  "installationHandover.radiatorInfoPanel.height": "Height:",
  "installationHandover.radiatorInfoPanel.inRoom": "In Room:",
  "installationHandover.radiatorInfoPanel.outputWatts": "Output[W]:",
  "installationHandover.radiatorInfoPanel.radiatorType": "Radiator Type:",
  "installationHandover.radiatorInfoPanel.toBeInstalled": "To Be installed?:",
  "installationHandover.radiatorInfoPanel.waterRadiator": "Water Radiator",
  "installationHandover.radiatorInfoPanel.width": "Length:",
  "installationHandover.radiators.existingRadiators": "Existing radiators",
  "installationHandover.radiators.newRadiators": "New radiators to be installed",
  "installationHandover.radiators.noExisitingRadiatorsInRoom": "There are no existing radiators in this room",
  "installationHandover.radiators.noNewRadiatorsInRoom": "There are no new radiators to be installed in this room",
  "installationHandover.radiators.noRadiatorsInRoom": "There are no radiators in this room",
  "installationHandover.radiators.noRadiatorsToRemoveInRoom": "There are no radiators to be removed from this room",
  "installationHandover.radiators.removedRadiators": "Removed radiators",
  "installationHandover.roomInfoPanel.existingRadiators": "Existing radiators",
  "installationHandover.sharePointUrlError": "⛔ Something went wrong check the url and try again.",
  "installationHandover.sharePointUrlSuccess": "✅ Sharepoint url updated successfully.",
  "installationHandover.title": "Installation handover",
  "installationHandover.titles.mainTitle": "Installation Handover",
  "installationPlanning.addResourcesToJob.availableResources": "Available Resources",
  "installationPlanning.addResourcesToJob.currentResources": "Current Resources",
  "installationPlanning.addResourcesToJob.noAvailableResources": "No available resources to add",
  "installationPlanning.addResourcesToJob.noResources": "No resources assigned to this job",
  "installationPlanning.addResourcesToJob.title": "People",
  "installationPlanning.addResourcesToTeam.availableResources": "Available Resources",
  "installationPlanning.addResourcesToTeam.defaultResources": "Default Resources",
  "installationPlanning.addResourcesToTeam.noAvailableResources": "No available resources to add",
  "installationPlanning.addResourcesToTeam.noResources": "No resources assigned to this team",
  "installationPlanning.assignResourceToSegment.conflictingSegments": "{resourceName} is already assigned to the following customers on this day:",
  "installationPlanning.assignResourceToSegment.conflictingServiceVisits": "{resourceName} is assigned to the following service visits on this day:",
  "installationPlanning.assignResourceToSegment.unavailable": "",
  "installationPlanning.changeLog.title": "Latest updates to the planner tool",
  "installationPlanning.dateRangePicker.endDate": "End Date",
  "installationPlanning.dateRangePicker.selectDateRange": "Select date range to show",
  "installationPlanning.dateRangePicker.startDate": "Start Date",
  "installationPlanning.dispatchJobs.description": "Click a job with assignees to dispatch it or dispatch all below.",
  "installationPlanning.dispatchJobs.dispatchAll": "Dispatch all",
  "installationPlanning.dispatchJobs.dispatchSelected": "Dispatch selected",
  "installationPlanning.dispatchJobs.failedToDispatchJobs": "Failed to dispatch the following jobs: ",
  "installationPlanning.dispatchJobs.successfullyDispatchedJobs": "Jobs dispatched successfully",
  "installationPlanning.dispatchJobs.title": "Dispatch mode",
  "installationPlanning.highlightFlexibleProjects": "Flexible projects",
  "installationPlanning.highlightProjects": "Highlight projects within ",
  "installationPlanning.highlightProjectsWithin": "Highlight projects within ",
  "installationPlanning.incompleteHours.description": "This will add {days} days and {hours} hours to the job",
  "installationPlanning.incompleteHours.errors.failedToAddHours": "Failed to add hours to the following roles: ",
  "installationPlanning.incompleteHours.save": "Save",
  "installationPlanning.incompleteHours.success": "All hours have been added successfully.",
  "installationPlanning.incompleteHours.title": "Incomplete Hours",
  "installationPlanning.inSalesRecoveryProjects": "In sales recovery projects",
  "installationPlanning.jobBar.segmentOverviewModal.baselineManHours": "Baseline man-hours:",
  "installationPlanning.jobBar.segmentOverviewModal.expectedTravelTime": "Expected travel time:",
  "installationPlanning.jobBar.segmentOverviewModal.extraTimeNeeded": "Extra time needed after accounting for travel time:",
  "installationPlanning.jobBar.segmentOverviewModal.scheduledManHours": "Total scheduled man-hours:",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_FINANCE_NOT_COMPLETED": "Customer finance not completed",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_ILLNESS": "Customer illness",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_RESCHEDULED": "Customer rescheduled",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_SCOPE_CHANGED": "Customer scope changed",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_HEAT_DESIGN_NOT_COMPLETED": "Heat design not completed",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_INSTALMENT_CAPACITY": "Installation capacity",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_DISTRIBUTION_NETWORK_OPERATOR": "Permissions distribution network operator",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_PLANNING_APPLICATION": "Permissions planning application",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_SUBSIDY": "Permissions subsidy",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_MATERIAL_UNAVAILABLE": "Product supply material unavailable",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_RUSH_ORDER": "Product supply rush order",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_TECHNICAL_SURVEY_NOT_COMPLETED": "Technical survey not completed",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_CUSTOMER_TRADES": "Third party customer trades",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_DISTRIBUTION_NETWORK_OPERATOR": "Third party distribution network operator",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_SUBCONTRACTOR": "Third party subcontractor",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNCATEGORIZED": "Uncategorized",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNSPECIFIED": "Unspecified",
  "installationPlanning.jobRescheduledReasonGroup.Customer": "Customer",
  "installationPlanning.jobRescheduledReasonGroup.Heat Design": "Heat Design",
  "installationPlanning.jobRescheduledReasonGroup.Installment Capacity": "Installation Capacity",
  "installationPlanning.jobRescheduledReasonGroup.Permissions": "Permissions",
  "installationPlanning.jobRescheduledReasonGroup.Product Supply": "Product Supply",
  "installationPlanning.jobRescheduledReasonGroup.Technical Survey": "Technical Survey",
  "installationPlanning.jobRescheduledReasonGroup.Third Party": "Third Party",
  "installationPlanning.jobRescheduledReasonGroup.Uncategorized": "Uncategorized",
  "installationPlanning.newBaselinesModal.apply": "Apply",
  "installationPlanning.newBaselinesModal.currentDuration": "Current Duration:",
  "installationPlanning.newBaselinesModal.discard": "Discard",
  "installationPlanning.newBaselinesModal.newDuration": "New Duration:",
  "installationPlanning.newBaselinesModal.title": "New Baselines",
  "installationPlanning.noLongerResource": "This resource is no longer available",
  "installationPlanning.notificationCenter.newBaselinesAvailable": "New baselines available",
  "installationPlanning.notificationCenter.plannerToolUpdates": "New planner tool updates",
  "installationPlanning.notificationCenter.projectsWithUnappliedBaseline": "New baseline for {nrOfProjects} projects",
  "installationPlanning.notificationCenter.title": "Notifications",
  "installationPlanning.notify.updatedJobTeam": "Successfully updated the team assigned to the job.",
  "installationPlanning.notify.updatedJobTeamError": "Failed to update the team assigned to the job.",
  "installationPlanning.notify.updateWorkSegmentsError": "Failed to update the job times.",
  "installationPlanning.notify.updateWorkSegmentsSuccess": "Successfully updated the job times.",
  "installationPlanning.onHoldProjects": "On-hold projects",
  "installationPlanning.overlappingSegments.explanation": "With your recent change, the following resources now have overlapping segments. Please adjust them to ensure no overlaps. The affected segments are listed below.",
  "installationPlanning.overlappingSegments.title": "Overlapping Segments",
  "installationPlanning.projectSidebar.bufferTank": "Buffer tank",
  "installationPlanning.projectSidebar.heatPumpIndoorUnit": "Indoor unit",
  "installationPlanning.projectSidebar.heatPumpOutdoorUnit": "Outdoor unit",
  "installationPlanning.projectSidebar.installationPackageSize": "Installation package size",
  "installationPlanning.projectSidebar.manufacturer": "Manufacturer",
  "installationPlanning.projectSidebar.openTicketsInHubspot": "Open tickets in hubspot",
  "installationPlanning.projectSidebar.radiators": "Radiators",
  "installationPlanning.projectSidebar.solution": "ESID number",
  "installationPlanning.removeResourceUnavailability.button.cancel": "Cancel",
  "installationPlanning.removeResourceUnavailability.button.confirm": "Confirm",
  "installationPlanning.removeResourceUnavailability.errors": "Failed to delete some resource unavailability",
  "installationPlanning.removeResourceUnavailability.success": "Resource unavailability have been deleted successfully.",
  "installationPlanning.removeResourceUnavailability.title": "Remove Resource Unavailability?",
  "installationPlanning.saveJobChangesToChangeAssignees": "Save job changes to change assignees",
  "installationPlanning.showFilters": "Filters",
  "installationPlanning.stage.installation": "Installation",
  "installationPlanning.stage.new": "New",
  "installationPlanning.stage.postInstallation": "Post Installation",
  "installationPlanning.stage.preInstallation": "Pre Installation",
  "installationPlanning.stage.technicalDesign": "Awaiting Technical design",
  "installationPlanning.stage.technicalSurvey": "Technical survey scheduled",
  "installationPlanning.suggestionsMap.showProjectsWithNoDate": "Show projects with no date",
  "installationPlanning.team.clickToManageResources": "Click to manage team resources",
  "installationPlanning.topBar.refreshButton.error": "Error refreshing",
  "installationPlanning.topBar.refreshButton.success": "The latest data has been fetched",
  "installationPlanning.topBar.refreshButton.tooltip": "Get the latest data from the server",
  "installationPlanning.unutilization.addAbsence": "Add absence",
  "installationPlanning.unutilization.addAbsenceError": "Failed to add absence",
  "installationPlanning.unutilization.addAbsenceSuccess": "Absence added successfully",
  "installationPlanning.unutilization.endDayDuration": "End day duration",
  "installationPlanning.unutilization.firstDayDuration": "First day duration",
  "installationPlanning.unutilization.fromTo": "From - To",
  "installationPlanning.unutilization.lentOutRegion": "Region lent out to",
  "installationPlanning.unutilization.otherReasonHelperText": "Please provide the other reason",
  "installationPlanning.unutilization.reason": "Reason",
  "installationPlanning.unutilization.resourceAlreadyInActive": "Resource is already unavailabile/ununitilized for {date}",
  "installationPlanning.viewProjectInAerospace": "View project in Aerospace",
  "installationPlanning.viewProjectInHubspot": "View project in Hubspot",
  "installationReport.button.sign": "Sign",
  "installationReport.commissioningDate.label": "Commissioning finalised {date}",
  "installationReport.commissioningIncomplete.label": "Commissioning incomplete",
  "installationReport.confirmation.journey.financingApplication": "Financing application",
  "installationReport.confirmation.journey.header": "Your Aira Journey",
  "installationReport.confirmation.journey.homeEnergyAssessment": "Home energy assessment",
  "installationReport.confirmation.journey.installation": "Installation",
  "installationReport.confirmation.journey.reviewAndAcceptQuote": "Review & accept quote",
  "installationReport.confirmation.journey.technicalSurveyAndDesign": "Technical survey & design",
  "installationReport.confirmation.link.installationReport": "Go to installation report",
  "installationReport.confirmation.nextSteps.accepted.body": "We have sent an email with a copy of the installation report.",
  "installationReport.confirmation.nextSteps.accepted.heading": "Your next steps",
  "installationReport.confirmation.nextSteps.rejected.body": "We will contact you soon about completing your installation.",
  "installationReport.confirmation.nextSteps.rejected.heading": "What happens next?",
  "installationReport.confirmation.title": "Thank you!",
  "installationReport.customer.title": "Customer",
  "installationReport.customerChecklistIntro.shareScreen.body": "To complete this form, the customer must be present. Guide them through it, and remember that only they can give the final sign off.",
  "installationReport.customerChecklistIntro.shareScreen.heading": "Share your screen",
  "installationReport.customerChecklistIntro.title": "Customer onboarding",
  "installationReport.customerChecklistIntro.wantToKnowMore.body": "You can return to the technical sections if the customer wants details about their system or installation.",
  "installationReport.customerChecklistIntro.wantToKnowMore.heading": "Want to know more?",
  "installationReport.incomplete.title": "Incomplete installation report",
  "installationReport.instructions.checklists.body": "A set of technical questions about the system and its installation. You can complete these on your own.",
  "installationReport.instructions.checklists.heading": "Installation & system checklists",
  "installationReport.instructions.intro": "Complete these checklists after the heat pump installation has been completed. There are two parts:",
  "installationReport.instructions.onboarding.body": "Onboard the customer on how to use their heat pump. For this part, the customer needs to be present.",
  "installationReport.instructions.onboarding.heading": "Customer onboarding",
  "installationReport.instructions.title": "Installation complete",
  "installationReport.products.bufferTank": "Buffer tank",
  "installationReport.products.indoorUnit": "Indoor unit",
  "installationReport.products.outdoorUnit": "Outdoor unit",
  "installationReport.products.radiators": "Radiators",
  "installationReport.products.solutionId": "ESID number",
  "installationReport.products.title": "Products installed",
  "installationReport.question.validation.greaterThan": "Value must be greater than {minValue}",
  "installationReport.question.validation.greaterThanAndLessThan": "Value must be greater than {minValue} and less than {maxValue}",
  "installationReport.question.validation.lessThan": "Value must be less than {maxValue}",
  "installationReport.reportSelector.label": "Report submitted",
  "installationReport.signature.customer.title": "Customer signature",
  "installationReport.signature.installer.title": "Installer signature",
  "installationReport.signatureModal.installationComplete.label": "By signing this document, you are confirming that your installation has been completed.",
  "installationReport.signatureModal.installationIncomplete.label": "By signing this document, you are confirming that you have reported all outstanding work/materials needed to complete your installation.",
  "installationReport.startPage.installers.title": "Installers",
  "installationReport.startPage.locked.body": "An accepted report has already been submitted for this customer.",
  "installationReport.startPage.locked.title": "Locked",
  "installationReport.startPage.start": "Start",
  "installationReport.textInput.placeholder": "Text",
  "installationReport.title": "Aira Commissioning Report - Heat Pump Installation",
  "installationReview.button.approveActualDuration": "Approve actual duration",
  "installationReview.button.cleanEnergyTechnician": "Clean Energy Technician",
  "installationReview.button.deleteAll": "Delete all",
  "installationReview.button.deleteJob": "Delete job",
  "installationReview.button.electrician": "Electrician",
  "installationReview.button.landscaper": "Landscaper",
  "installationReview.button.unschedule": "Unschedule",
  "installationReview.columnHeaders.actualEnd": "Actual end",
  "installationReview.columnHeaders.actualManHours": "Actual man-hours",
  "installationReview.columnHeaders.actualStart": "Actual start",
  "installationReview.columnHeaders.addTravelTime": "Add travel time",
  "installationReview.columnHeaders.day": "Day",
  "installationReview.columnHeaders.duration": "Actual Duration",
  "installationReview.columnHeaders.expectedDuration": "Expected duration",
  "installationReview.columnHeaders.expectedManHours": "Expected man-hours",
  "installationReview.columnHeaders.expectedStart": "Expected start",
  "installationReview.columnHeaders.resources": "Resources",
  "installationReview.columnHeaders.scheduledEnd": "Scheduled end",
  "installationReview.columnHeaders.scheduledManHours": "Scheduled man-hours",
  "installationReview.columnHeaders.scheduledStart": "Scheduled start",
  "installationReview.columnHeaders.startTravel": "Start travel",
  "installationReview.columnHeaders.status": "Status",
  "installationReview.errors.failedToDelete": "Failed to delete the installation.",
  "installationReview.errors.failedToDeleteJobs": "Failed to delete the following roles: ",
  "installationReview.errors.failedToPublish": "Failed to publish review.",
  "installationReview.errors.failedToUnschedule": "Failed to unschedule the installation.",
  "installationReview.errors.failedToUnscheduleJobs": "Failed to unschedule the following roles: ",
  "installationReview.errors.notYetScheduled": "Installation jobs have not yet been scheduled.",
  "installationReview.errors.unfinishedJob": "All jobs must be finished!",
  "installationReview.jobSegmentStatus.FINISHED": "Finished",
  "installationReview.jobSegmentStatus.IN_PROGRESS": "In progress",
  "installationReview.jobSegmentStatus.NOT_STARTED": "Not started",
  "installationReview.labels.defaultResourceCount": "Default resource count",
  "installationReview.labels.linkToSchedulingTool": "Open recurring schedule in Skedulo web",
  "installationReview.labels.reviewedAt": "Reviewed on {date} at {time}.",
  "installationReview.labels.totalActualDuration": "Actual duration",
  "installationReview.labels.totalActualManHours": "Total actual man-hours",
  "installationReview.labels.totalExpectedDuration": "Expected duration",
  "installationReview.labels.totalExpectedManHours": "Total expected man-hours",
  "installationReview.modals.addJobHours.button.cancel": "Cancel",
  "installationReview.modals.addJobHours.button.confirm": "Confirm",
  "installationReview.modals.addJobHours.helperText": "Please enter a valid number of hours",
  "installationReview.modals.addJobHours.title": "Add job hours",
  "installationReview.modals.confirmDeletion.button.cancel": "Cancel",
  "installationReview.modals.confirmDeletion.button.delete": "Delete",
  "installationReview.modals.confirmDeletion.label.reasonCategory": "Reason for deletion",
  "installationReview.modals.confirmDeletion.label.reasonDescription": "Description",
  "installationReview.modals.confirmDeletion.title": "Delete job",
  "installationReview.modals.confirmDeletionAll.title": "Delete all jobs",
  "installationReview.modals.confirmUnschedule.button.cancel": "Cancel",
  "installationReview.modals.confirmUnschedule.button.unschedule": "Unschedule",
  "installationReview.modals.confirmUnschedule.label.reasonCategory": "Reason for unscheduling",
  "installationReview.modals.confirmUnschedule.label.reasonDescription": "Description",
  "installationReview.modals.confirmUnschedule.title": "Unschedule all jobs",
  "installationReview.modals.removeJobHours.button.cancel": "Cancel",
  "installationReview.modals.removeJobHours.button.remove": "Remove",
  "installationReview.modals.removeJobHours.title": "Do you want to remove job hours?",
  "installationReview.notify.addHoursSuccess": "Extra hours added to the job",
  "installationReview.notify.deletionSuccess": "The installation has been deleted.",
  "installationReview.notify.removeHoursSuccess": "Hours removed from the job",
  "installationReview.notify.unscheduleSuccess": "The installation has been unscheduled.",
  "installationReview.role.ELECTRICIAN": "Electrician team",
  "installationReview.role.INSTALLER": "Clean Energy Technician team",
  "installationReview.role.LANDSCAPER": "Landscaper team",
  "installationReview.role.UNKNOWN": "Untagged installation",
  "installationReview.tableContent.noResources": "Unassigned",
  "installationReview.tableContent.resourceCount": "{count} resources",
  "installationReview.title": "Installation jobs overview",
  "installationReview.values.duration": "{hours}h {minutes}m",
  "installationReview.values.expectedManHoursPerResource": "{hours}h {minutes}m each for {count} resources",
  "installationReview.values.totalExpectedDuration": "{totalHours}h {totalMinutes}m ({hours}h {minutes}m for {count} resources)",
  "invoice.finalInvoice": "Final invoice",
  "invoice.loading.error": "Error loading invoices:",
  "invoice.pastDue": "Past due",
  "invoice.prepayment": "Prepayment",
  "invoice.total.remainingToPay": "Remaining to pay",
  "invoice.total.value": "System total",
  "invoiceRefund.amount": "Amount",
  "invoiceRefund.amountSummary": "Refunding: {amount} ({percent}% of total)",
  "invoiceRefund.confirm": "Process Refund",
  "invoiceRefund.description": "Please specify refund details for invoice: {invoiceId}",
  "invoiceRefund.error.amountRequired": "Refund amount must be greater than zero",
  "invoiceRefund.error.exceedsTotal": "Refund amount cannot exceed the invoice total",
  "invoiceRefund.error.invalidAmount": "Please enter a valid refund amount",
  "invoiceRefund.error.reasonRequired": "Please provide a reason for the refund",
  "invoiceRefund.fullRefund": "Full Refund",
  "invoiceRefund.partialRefund": "Partial Refund",
  "invoiceRefund.processing": "Processing...",
  "invoiceRefund.reason": "Reason for Refund",
  "invoiceRefund.title": "Refund Invoice",
  "invoiceRefund.totalAmount": "Invoice Total: {amount}",
  "invoiceRefund.warning": "Warning: Refunds are final and cannot be reversed. The customer will be notified once the refund is processed.",
  "invoiceSatus.draft": "Draft",
  "invoiceSatus.open": "Open",
  "invoiceSatus.uncollectable": "Uncollectable",
  "invoicesTable.amountHeader": "Amount",
  "invoicesTable.dateCreatedHeader": "Date created",
  "invoicesTable.descriptionHeader": "Description",
  "invoicesTable.dueDateHeader": "Due date",
  "invoicesTable.emptyState": "No invoices yet",
  "invoicesTable.invoiceNumberHeader": "Invoice number",
  "invoicesTable.noPaymentReceived": "No payment received yet.",
  "invoicesTable.paymentId": "ID",
  "invoicesTable.paymentReceived": "Payment received",
  "invoicesTable.paymentSummary": "Payment Summary",
  "invoicesTable.previewInvoice": "Preview invoice",
  "invoicesTable.refundButton": "Refund",
  "invoicesTable.statusHeader": "Status",
  "invoicesTable.title": "Invoices",
  "invoiceStatus.cancelled": "Cancelled",
  "invoiceStatus.creditNote": "Credit note",
  "invoiceStatus.draft": "Draft",
  "invoiceStatus.Error": "Error",
  "invoiceStatus.error": "Error",
  "invoiceStatus.open": "Open",
  "invoiceStatus.paid": "Paid",
  "invoiceStatus.partiallyPaid": "Partially Paid",
  "invoiceStatus.partiallyRefunded": "Partially refunded",
  "invoiceStatus.pastDue": "Past Due",
  "invoiceStatus.refunded": "Refunded",
  "invoiceStatus.uncollectable": "Uncollectable",
  "invoiceStatus.unknown": "Unknown",
  "invoicing.createInvoice": "Create final invoice",
  "invoicing.erp.customerId": "ERP customer ID: ",
  "invoicing.erp.projectId": "ERP project ID: ",
  "invoicing.error.fetchingInvoices": "Error fetching invoices",
  "invoicing.title": "Invoicing",
  "localized.string": "Localized <strong>string</strong>",
  "numberInputHelperText.canBeDecimal": "This field only allows non-negative numbers.",
  "numberInputHelperText.canBeInteger": "This field only allows non-negative integers.",
  "numberInputHelperText.canBeNegative": "This field only allows integers.",
  "numberInputHelperText.canBeNegativeAndDecimal": "This field only allows numbers.",
  "ongoingInstallations.actualStartDate": "Actual start date",
  "ongoingInstallations.commissioningDate": "Commissioning date",
  "ongoingInstallations.completed": "Completed",
  "ongoingInstallations.customers": "Customers",
  "ongoingInstallations.daysUntilInstallation": "Days until installation",
  "ongoingInstallations.filters.country": "Country",
  "ongoingInstallations.filters.from": "From",
  "ongoingInstallations.filters.region": "Region",
  "ongoingInstallations.filters.to": "To",
  "ongoingInstallations.hoursForCompletion": "Hours for completion",
  "ongoingInstallations.installationComplete": "Installation complete",
  "ongoingInstallations.installationCompleteDate": "Installation complete date",
  "ongoingInstallations.installationIncomplete": "Installation incomplete",
  "ongoingInstallations.installationIncompleteDate": "Installation incomplete date",
  "ongoingInstallations.jobsOverview": "Jobs overview",
  "ongoingInstallations.link": "Link",
  "ongoingInstallations.noStartDate": "No start date",
  "ongoingInstallations.plannedStartDate": "Planned start date",
  "ongoingInstallations.projectStage": "Project Stage",
  "ongoingInstallations.regions": "Regions",
  "ongoingInstallations.reworkCompletionHours": "Rework completion hours",
  "ongoingInstallations.status": "Status",
  "ongoingInstallations.status.completed": "Completed",
  "ongoingInstallations.status.installation": "Installation",
  "ongoingInstallations.status.invoice": "Invoice",
  "ongoingInstallations.status.postInstallation": "Post-installation",
  "ongoingInstallations.status.preInstallation": "Pre-installation",
  "ongoingInstallations.status.unknown": "Unknown",
  "ongoingInstallations.teamLead": "Team lead",
  "ongoingInstallations.title": "Ongoing Installations",
  "outdoorUnit.add.cascading.button": "Add additional outdoor unit",
  "outdoorUnit.add.cascading.description": "Only add additional units for cascading solutions",
  "procurement.actions.bomDeprecated": "The heat design has been updated since the BOM was created. Please review and re-save the <link>BOM</link>.",
  "procurement.actions.bomNotReadyForProcurement": "The BOM is not ready for procurement.",
  "procurement.actions.designReviewNotAccepted": "Design review has not been approved by the customer.",
  "procurement.actions.errorPrefix": "Error: ",
  "procurement.actions.installationDateRequired": "Installation date is required",
  "procurement.actions.installationKitRequired": "AIK is required for ordering",
  "procurement.actions.markedAsOrdered": "Marked as ordered",
  "procurement.actions.markingAsOrdered": "Marking as ordered...",
  "procurement.actions.markOrderFailed": "Failed to mark as ordered",
  "procurement.actions.ordered": "Order sent to ERP at {time}",
  "procurement.actions.orderedSuccess": "Order sent to ERP",
  "procurement.actions.orderFailed": "Order failed",
  "procurement.actions.orderFailedToast": "Failed to order items",
  "procurement.actions.ordering": "Ordering items…",
  "procurement.actions.preliminaryDateConfirmDescription": "The installation date is only preliminary and may change. Are you sure you want to proceed with ordering?",
  "procurement.actions.preliminaryDateConfirmTitle": "Installation date is preliminary",
  "procurement.actions.procurementCommentMissing": "Procurement comment is missing",
  "procurement.actions.reorderConfirmDescription": "You are about to reorder. This will only work if all SOs and POs are removed from the ERP project. Do you want to proceed?",
  "procurement.actions.reorderConfirmTitle": "Previously ordered at {datetime}",
  "procurement.actions.saveAndOrder": "Save & Order",
  "procurement.actions.saved": "Procurement saved",
  "procurement.actions.saveFailed": "Save failed",
  "procurement.actions.saveFailedToast": "Failed to save procurement",
  "procurement.actions.saving": "Saving procurement…",
  "procurement.actions.solutionNotReadyForOrder": "The <link>solution's status</link> is not \"Ready For Order\".",
  "procurement.actions.vanStockBundleRequired": "At least one van stock must be selected.",
  "procurement.comment.label": "Comments regarding order",
  "procurement.comment.placeholder": "Please write any relevant comments for the planner, like PO/SO numbers, delivery times etc.",
  "procurement.description": "This is where the items will be sourced once the order for the installation project is placed. The BoM created by the Design Engineer will subtract the selected version of the Aira Installation Kit and Van Stock to form the final order.",
  "procurement.label.installationDate": "Installation date",
  "procurement.label.selectInstallationKitVersion": "Select Aira Installation Kit (AIK)",
  "procurement.label.selectInstallationKitVersion.tooltip": "Select what AIK to order for the installation. The containing items will appear in the 'AIK' column and be automatically deducted from overlapping BoM items.",
  "procurement.label.selectVanStockBundles": "Select van stocks",
  "procurement.label.selectVanStockBundles.tooltip": "Select the van stock(s) available for the installation. These will appear in the 'VS' column and be automatically deducted from overlapping BoM items.",
  "procurement.label.selectVanStockBundlesPlaceholder": "Select at least one van stock",
  "procurement.status.ordered": "Ordered at {date}",
  "procurement.title": "Procurement",
  "procurement.warning.bomBundleOutdated": "One or more bundles in the BOM are outdated. It would be advisable to review them.",
  "procurement.warning.installationDatePassed": "Installation date has passed",
  "product-price-adjustment.adjust-price": "Increase price?",
  "product-price-adjustment.excluding-vat": "Excl. VAT",
  "product-price-adjustment.handled-products": "Products previously changed and handled",
  "product-price-adjustment.handled-products-note": "This section displays products that have been previously changed.",
  "product-price-adjustment.initial-quote": "Initial quote",
  "product-price-adjustment.note": "If pricing has changed from the initial quote, include an explanation in the comment field in the Customer Design Report to avoid surprising the customer.",
  "product-price-adjustment.products-with-price-increase": "Products with possible price increase",
  "product-price-adjustment.products-with-price-increase-note": "This section displays new or changed products that cost more than the original quote.",
  "product-price-adjustment.products-with-price-reduction": "Products with price reduction",
  "product-price-adjustment.products-with-price-reduction-note": "This section displays removed or changed products that cost less than the original quote.",
  "product-price-adjustment.reverted-products": "Products reverted to original quote",
  "product-price-adjustment.reverted-products-note": "This section displays products that have reverted back to the ones in the quote.",
  "product-price-adjustment.revised-products": "Revised products",
  "product-price-adjustment.subtitle": "Some products have changed from the initial quote. Confirm which are covered by Aira’s price guarantee and which require extra charges. Updated pricing will appear in the System Design sent to the customer for approval after heat design is complete.",
  "product-price-adjustment.title": "Confirm pricing of products",
  "product-price-adjustment.updated-price": "Updated price",
  "productSummary.comparison.added": "Added",
  "productSummary.comparison.displayOriginalOffer": "Display quote comparison",
  "productSummary.comparison.hideOriginalOffer": "Hide quote comparison",
  "productSummary.comparison.removed": "Removed",
  "productSummary.comparison.unchanged": "Unchanged",
  "productSummary.comparison.updated": "Updated",
  "productSummary.discountsTable.discountExVat": "Promotion ex VAT",
  "productSummary.discountsTable.discountIncVat": "Promotion inc VAT",
  "productSummary.discountsTable.discounts": "Promotions",
  "productSummary.discountsTable.vatPercentage": "VAT%",
  "productSummary.invoice.alreadySent": "sent to customer",
  "productSummary.invoice.error.unknown": "Failed to send invoice, error unknown",
  "productSummary.invoice.final.label": "Final invoice",
  "productSummary.invoice.final.send": "Final invoice",
  "productSummary.invoice.final.send.popup.infoCard.title": "Send final invoice",
  "productSummary.invoice.final.send.popup.readyTitle": "Send final invoice",
  "productSummary.invoice.invoice": "Invoice",
  "productSummary.invoice.paid": "paid by customer",
  "productSummary.invoice.partial.label": "",
  "productSummary.invoice.partial.notPaid": "Partial invoice not paid",
  "productSummary.invoice.partial.send": "Partial invoice",
  "productSummary.invoice.send": "Send invoice",
  "productSummary.invoice.send.loading": "Sending invoice",
  "productSummary.invoice.send.popup.description": "Installation complete, send an invoice to the customer.",
  "productSummary.invoice.send.popup.infoCard.text": "· The installation is complete\n· There are no changes that affect the invoice amount",
  "productSummary.invoice.send.popup.infoCard.title": "Before you send the invoice, check that:",
  "productSummary.invoice.send.popup.readyTitle": "Ready to send the invoice?",
  "productSummary.invoice.send.popup.title": "Send invoice",
  "productSummary.invoice.send.tooltip": "Invoicing is available when:\n· Quote is accepted\n· Payment method is invoice\n· Products are locked\n· Installation is completed",
  "productSummary.invoice.sent": "sent to customer",
  "productSummary.productsTable.finalOrderSummaryInformation": "Final order summary",
  "productSummary.productsTable.itemNumber": "WBS / Item number",
  "productSummary.productsTable.offerSummaryInformation": "Quote",
  "productSummary.productsTable.orderSummaryInformation": "Current order summary",
  "productSummary.productsTable.PAYMENT_TYPE_INSTALMENTS": "Instalments",
  "productSummary.productsTable.PAYMENT_TYPE_INVOICE": "Invoice",
  "productSummary.productsTable.paymentMethod": "Payment method",
  "productSummary.productsTable.priceExVat": "Price ex VAT",
  "productSummary.productsTable.priceIncVat": "Price inc VAT",
  "productSummary.productsTable.quantity": "Quantity",
  "productSummary.productsTable.region": "Region",
  "productSummary.productsTable.vatPercentage": "VAT%",
  "productSummary.subsidyTable.subsidy": "Subsidy",
  "productSummary.subsidyTable.subsidyExVat": "Subsidy ex VAT",
  "productSummary.subsidyTable.subsidyIncVat": "Subsidy inc VAT",
  "productSummary.subsidyTable.ukSubsidyTitle": "BUS grant",
  "productSummary.subsidyTable.vatPercentage": "VAT%",
  "productSummary.totalPriceTable.effectiveInterestRate": "Effective interest rate",
  "productSummary.totalPriceTable.interestRate": "Interest rate",
  "productSummary.totalPriceTable.monthlyCost": "Monthly cost",
  "productSummary.totalPriceTable.totalPrice": "Total price",
  "productSummary.totalPriceTable.totalPriceExVat": "Total price ex VAT",
  "productSummary.totalPriceTable.totalPriceIncVat": "Total price inc VAT",
  "quotation.action.AWAITING_DESIGN": "Awaiting design",
  "quotation.action.COMPLETE_DESIGN": "Complete design",
  "quotation.action.COMPLETE_SURVEY": "Complete survey",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "Approve design",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "Cancel",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_DOCUMENTATION_AND_APPLICATIONS": "Complete documentation and applications",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "Complete technical data collection",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "Lock quote",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "Lock products",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "Reopen",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "Send quote",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "Financing secured",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "Mark as ordered",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "Reopen",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "Unlock price and products",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "Unlock quote",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "Unlock products",
  "quotation.action.error.aborted": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.cancelled": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.dataLoss": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.deadlineExceeded": "Email is still being sent, refresh the page to see the status update",
  "quotation.action.error.failedPrecondition": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.internal": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.invalidArgument": "Enter a valid format",
  "quotation.action.error.notFound": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.outOfRange": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.permissionDenied": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.resourceExhausted": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.unauthenticated": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.unavailable": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.unimplemented": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.error.unknownError": "Unknown error, if the error persists contact the Acquisition Tech team using the teams channel",
  "quotation.action.INSTALLED": "Installed",
  "quotation.action.lockingNotAllowed": "Please instead use \"Lock Design\" within the Heat Design tool",
  "quotation.action.markAsOrderedNotAllowed": "Please instead use \"Save and Order\" within the procurement tool",
  "quotation.action.REQUEST_SURVEY": "Request extended survey (manual scheduling in Skedulo)",
  "quotation.button.add": "Add custom promotion",
  "quotation.button.resetProducts": "Reset product selection",
  "quotation.button.update": "Update",
  "quotation.button.viewGuide": "View guide",
  "quotation.errorCard.checkDiscount.text": "Promotion of {discountAmount} applied.\n\nIf there are significant changes to the product selection – consider unlocking the price and create a new quote to reflect the changes in the price.",
  "quotation.errorCard.checkDiscount.title": "Check the discount",
  "quotation.errorCard.paymentType": "Select agreed payment method below",
  "quotation.errorCard.paymentTypeDescription": "If the customer is approved for Financing, select ‘Instalments’. If the customer wants to pay upfront instead, please select ‘Invoice’.  \n\nAlternatively, ‘Cancel’ the energy solution if required.",
  "quotation.label.action": "Action",
  "quotation.label.amountIncVat": "Amount inc VAT",
  "quotation.label.battery": "Battery",
  "quotation.label.bufferTank": "Buffer tank",
  "quotation.label.compatibilityGroup": "Brand",
  "quotation.label.defaultTaxRateDescription": "Standard VAT percentage used",
  "quotation.label.digging": "Digging",
  "quotation.label.disableFinancing": "Remove option to pay monthly",
  "quotation.label.displayPricing": "Display pricing (Individual prices exclude VAT)",
  "quotation.label.displayTaxRateOverride": "Set a custom VAT percentage",
  "quotation.label.editDefaultTaxRateDescription": "This solution uses the standard VAT percentage. To change it, use the toggle below to show a dropdown and select a different percentage.",
  "quotation.label.editOverrideTaxRateDescription": "To use the standard VAT percentage, clear the dropdown below and save the solution.",
  "quotation.label.electricalBonding": "Electrical bonding",
  "quotation.label.enableFinancing": "Add option to pay monthly",
  "quotation.label.evCharger": "EV Charger",
  "quotation.label.financingTerm": "Financing term",
  "quotation.label.heatingCircuits": "Heating circuits",
  "quotation.label.heatingRoom": "",
  "quotation.label.heatingSystem": "Heating system",
  "quotation.label.heatPump": "Outdoor unit",
  "quotation.label.heatPumpIndoor": "Indoor unit",
  "quotation.label.hidePricing": "Hide pricing",
  "quotation.label.hideTaxRateOverride": "Disable custom VAT percentage",
  "quotation.label.hotWaterExpansionVessel": "8L hot water expansion vessel",
  "quotation.label.indoorUnitOutdoorUnitDistance": "Distance between IO & OU",
  "quotation.label.installationKit": "Installation kits",
  "quotation.label.installationPackage": "Installation bundle",
  "quotation.label.isolatorFitting": "Isolator fitting",
  "quotation.label.joinery": "Joinery",
  "quotation.label.liftingEquipment": "Lifting equipment",
  "quotation.label.manifold": "Manifold",
  "quotation.label.mcs-estimate": "MCS estimate",
  "quotation.label.meterBoxChange": "Meter box change",
  "quotation.label.oilTankRemoval": "Oil tank removal",
  "quotation.label.oilTransport": "",
  "quotation.label.oilWallRemoval": "",
  "quotation.label.outdoorMounting": "Stand / Outdoor mounting",
  "quotation.label.outdoorUnitPipeConnection": "Pipe connection to outdoor unit",
  "quotation.label.outdoorUnitWall": "Outdoor wall for freestanding unit",
  "quotation.label.paymentMethod": "Payment method",
  "quotation.label.piping": "Underground piping",
  "quotation.label.predefinedDiscounts": "{numberOfDiscounts} pre-defined promotions selected",
  "quotation.label.qty": "Qty",
  "quotation.label.quantity": "Quantity",
  "quotation.label.radiatorControl": "Digital radiator control",
  "quotation.label.rePiping": "Re-piping",
  "quotation.label.scaffolding": "Scaffolding",
  "quotation.label.solar": "Solar system",
  "quotation.label.status": "status",
  "quotation.label.stoneWallDrilling": "Stone wall drilling",
  "quotation.label.systemDesign": "System design",
  "quotation.label.systemFlush": "Radiator system flush",
  "quotation.label.taxRateOverride": "VAT percentage",
  "quotation.label.technicalSurvey": "Technical survey",
  "quotation.label.temperatureZones": "Multiple temperature zones",
  "quotation.label.totalCost": "Total price incl VAT",
  "quotation.label.totalCostExVat": "Total price ex VAT",
  "quotation.label.underfloorHeatingCommission": "Underfloor heating commissioning",
  "quotation.label.uploadMCS": "Upload MCS estimate",
  "quotation.linkToFinancialPortal": "Customer validation",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "Design approved",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "Cancelled",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "Technical data collection completed",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "Quote locked",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "Products locked",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "Reopened",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "Quote sent",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "Financing secured",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "Ordered",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "Quote Reopened",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "Quote unlocked",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "Quote unlocked",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "Products unlocked",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "Approving design ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "Cancelling quote ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "Completing technical data collection ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "Locking price ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "Locking products ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "Reopening quote ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "Sending quote ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "Setting financing secured ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "Marking as ordered ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "Reopening Quote ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "Unlocking price and products ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "Unlocking price ...",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "Unlocking products ...",
  "quotation.notify.performingAction": "Loading ...",
  "quotation.notify.sendingQuote": "Sending quote ...",
  "quotation.notify.unsavedChanges": "You have unsaved changes that may affect the total cost and estimated savings.",
  "quotation.popup.cancel.confirm": "Confirm",
  "quotation.popup.cancel.content": "This will close any existing quote, do you want to perform this action?",
  "quotation.popup.financing.confirm": "Confirm approval",
  "quotation.popup.financing.content": "To enable option to pay monthly, you need approval from Christoph",
  "quotation.popup.financing.title": "Confirm approval of pay monthly option",
  "quotation.popup.resendQuote.confirm": "Re-send quote",
  "quotation.popup.resendQuote.content": "This will re-send the quote to the customer, are you sure that you want to perform this action?",
  "quotation.popup.resetProducts.confirm": "Continue",
  "quotation.popup.resetProducts.content": "Use this to add the latest product bundles to an existing deal. If you continue, product selection will reset to the latest available bundles.",
  "quotation.popup.saveBeforeAction.confirm": "Continue",
  "quotation.popup.saveBeforeAction.content": "You have unsaved changes. These will not be applied if you continue.",
  "quotation.popup.unlockPrice.confirm": "Confirm",
  "quotation.popup.unlockPrice.content": "To make changes, unlock the quote. Any changes may affect the price once it is locked and a new quote is created.",
  "quotation.popup.unlockPrice.title": "Quote locked",
  "quotation.progress.designAcceptedAt": "Design approved",
  "quotation.progress.documentationAndApplicationsCompletedAt": "Documentation and Applications",
  "quotation.progress.financingSecuredAt": "Financing secured",
  "quotation.progress.priceLockedAt": "Quote locked",
  "quotation.progress.productsLockedAt": "Products locked",
  "quotation.progress.quoteAcceptedAt": "Quote accepted",
  "quotation.progress.quoteSentAt": "Quote sent",
  "quotation.progress.techFeePaidAt": "Technical survey fee paid",
  "quotation.progress.technicalDataCollectionCompletedAt": "Technical data collection completed",
  "quotation.status.CANCELLED": "Cancelled",
  "quotation.status.cancelled": "Cancelled",
  "quotation.status.expired": "Quote expired {date}",
  "quotation.status.installed": "Installed",
  "quotation.status.missing": "Not available",
  "quotation.status.OPEN_FOR_MODIFICATION": "Open for modification",
  "quotation.status.openForModification": "Open for modification",
  "quotation.status.ORDERED": "Ordered",
  "quotation.status.ordered": "Ordered",
  "quotation.status.PRICE_LOCKED": "Quote locked",
  "quotation.status.priceLocked": "Quote locked",
  "quotation.status.PRODUCTS_LOCKED": "Products locked",
  "quotation.status.productsLocked": "Products locked",
  "quotation.status.quoteExpirationAddition": " (quote expires {date})",
  "quotation.status.READY_FOR_ORDER": "Ready for order",
  "quotation.status.readyForOrder": "Ready for order",
  "quotation.table.description": "Description",
  "quotation.table.price": "Price",
  "quotation.table.quantity": "Quantity",
  "quotation.title.addons": "Radiator system upgrades, planning permission & EPC",
  "quotation.title.batteryPackage": "Battery package",
  "quotation.title.discounts": "Promotions",
  "quotation.title.evChargerPackage": "EV charger",
  "quotation.title.heatPumpPackage": "Heat pump package",
  "quotation.title.installationAddons": "Installation add-ons",
  "quotation.title.insulation": "Insulation",
  "quotation.title.miscellaneous": "Miscellaneous",
  "quotation.title.packages": "Packages",
  "quotation.title.progress": "Energy solution progress",
  "quotation.title.quotation": "Quotation",
  "quotation.title.radiators": "Radiator system upgrades",
  "quotation.title.region": "Region",
  "quotation.title.solarPackage": "Solar panels and Battery storage",
  "quotation.title.solarPanels": "Solar panels",
  "quotation.title.subsidy": "Subsidy",
  "quotation.title.subsidyAndPromotions": "Subsidy and Promotions",
  "quotation.title.taxRate": "VAT percentage",
  "quotation.title.total": "Total",
  "quotation.warning.goToHeatDesign": "Go to the \"heat design\" tool to make product changes on an accepted quote.",
  "quotation.warning.unlockToChange": "Unlock the quote to make changes to the products.",
  "quotations.paymentMethod.INSTALMENTS": "Instalments",
  "quotations.paymentMethod.INVOICE": "Invoice",
  "quotations.paymentMethod.popup.p": "Change how the customer will pay. The new method will be shown on their quote.{br}{br}If you’re changing from ‘Invoice’ to ‘Instalments’, initiate the financing application for the customer.",
  "quotations.paymentMethod.SPLIT_INVOICE": "Split invoice (2-rate)",
  "quotations.paymentMethod.UNDECIDED": "Undecided",
  "region.button.installation": "Installation planning",
  "region.button.resources": "Resources",
  "region.button.salesSurvey": "Sales & survey planning",
  "serviceBookingTool.body.resourceName": "Resource",
  "serviceBookingTool.button.bookMaintenance": "Schedule service job",
  "serviceBookingTool.button.bookServiceJob": "Book service job",
  "serviceBookingTool.button.schedule.maintenance": "Schedule a maintenance",
  "serviceBookingTool.emptyState.noInstallation": "Service visits can be booked once the solution is ready for installation. ",
  "serviceBookingTool.error.cannotBookServiceJob": "The service job cannot be booked until the installation is completed.",
  "serviceBookingTool.error.maintenanceSchedulingFailure": "An error occurred when trying to schedule the maintenance.",
  "serviceBookingTool.error.partialAddress": "Something is wrong the address, please check it out!",
  "serviceBookingTool.jobStatus.JOB_STATUS_CANCELLED": "Cancelled",
  "serviceBookingTool.jobStatus.JOB_STATUS_FINISHED": "Finished",
  "serviceBookingTool.jobStatus.JOB_STATUS_IN_PROGRESS": "In progress",
  "serviceBookingTool.jobStatus.JOB_STATUS_NOT_SCHEDULED": "Not scheduled",
  "serviceBookingTool.jobStatus.JOB_STATUS_READY": "Ready",
  "serviceBookingTool.jobStatus.JOB_STATUS_SCHEDULED": "Scheduled",
  "serviceBookingTool.jobStatus.JOB_STATUS_UNSPECIFIED": "Unknown",
  "serviceBookingTool.jobStatus.UNRECOGNIZED": "Unknown",
  "serviceBookingTool.jobType.installThermostat": "Service - Thermostat",
  "serviceBookingTool.jobType.JOB_TYPE_ELECTRICAL_MAINTENANCE": "Service - Electrical",
  "serviceBookingTool.jobType.JOB_TYPE_HEAT_PUMP_MAINTENANCE": "Service - Heat pump",
  "serviceBookingTool.jobType.JOB_TYPE_THERMOSTAT_MAINTENANCE": "Service - Thermostat",
  "serviceBookingTool.jobType.serviceElectricalSystem": "Service - Electrical",
  "serviceBookingTool.jobType.serviceHeatPump": "Service - Heat pump",
  "serviceBookingTool.jobType.serviceJobType": "Service job type",
  "serviceBookingTool.label.duration": "Duration",
  "serviceBookingTool.no.maintenance.scheduled": "No maintenance added yet…",
  "serviceBookingTool.popup.unscheduleDescription": "This unschedules the booking time and removes any assigned resource.",
  "serviceBookingTool.popup.unscheduleHeader": "Unschedule service visit",
  "serviceBookingTool.popup.unscheduleReason.customerCancelled": "Customer Cancelled",
  "serviceBookingTool.popup.unscheduleReason.customerNoShow": "Customer No Show",
  "serviceBookingTool.popup.unscheduleReason.description": "Description",
  "serviceBookingTool.popup.unscheduleReason.noLongerNeeded": "No Longer Needed",
  "serviceBookingTool.popup.unscheduleReason.uncategorized": "Uncategorized",
  "serviceBookingTool.title.homeService": "Home Service",
  "surveysPlanning.suggestionsMap.cleanEnergyExperts": "Clean Energy Experts",
  "surveysPlanning.suggestionsMap.filterByResource": "Filter by resource",
  "surveysPlanning.suggestionsMap.findOnMap": "Find on map",
  "surveysPlanning.suggestionsMap.showVisitsWithNoAssignedResources": "Show visits with no assigned",
  "surveysPlanning.suggestionsMap.showVisitsWithNoDate": "Show visits with no date",
  "surveysPlanning.suggestionsMap.technicalSurveyors": "Technical Surveyors",
  "upload.max-file-size": "The estimate should be in PDF format and generated from the",
  "upload.mcs.description": "To generate an estimate from the TCO, take a screenshot of the ‘MCS output’ tab and upload it here. Ensure the ‘MCS input’ tab is completed beforehand.\nThe newest file you upload will be linked from FAQs on the customer quote.",
  "upload.mcs.link.text": "MCS estimate calculation sheet."
};
