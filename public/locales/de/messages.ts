// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = {
  "addressLookup.input.error": "",
  "addressLookup.toggle.default": "",
  "addressLookup.toggle.description": "",
  "addressLookup.toggle.other": "",
  "arViews.button.openOneDrive": "",
  "arViews.button.openVaillantApp": "",
  "arViews.button.unit.100L_AIO": "",
  "arViews.button.unit.100L_buffer": "",
  "arViews.button.unit.12kW_outdoor": "",
  "arViews.button.unit.150L_cylinder_slimline": "",
  "arViews.button.unit.200L_cylinder": "",
  "arViews.button.unit.250L_AIO": "",
  "arViews.button.unit.250L_cylinder": "",
  "arViews.button.unit.300L_cylinder": "",
  "arViews.button.unit.40L_buffer": "",
  "arViews.button.unit.6kW_outdoor": "",
  "arViews.button.unit.8kW_outdoor": "",
  "arViews.button.unit.compact_hydrobox": "",
  "arViews.button.unit.thermostat": "",
  "arViews.text.arDescription": "",
  "arViews.text.howToAltDescription": "",
  "arViews.text.howToDescription": "",
  "arViews.text.vaillantHowToDescription": "",
  "arViews.title.airaHP": "",
  "arViews.title.arViews": "",
  "arViews.title.bufferTanks": "",
  "arViews.title.cylinders": "",
  "arViews.title.indoorUnits": "",
  "arViews.title.outdoorUnits": "",
  "arViews.title.thermostat": "",
  "arViews.title.vaillantHP": "",
  "baselineCalc.boolean.false": "",
  "baselineCalc.boolean.true": "",
  "baselineCalc.label.complexityElectricalWorks": "",
  "baselineCalc.label.complexityExistingUnit": "",
  "baselineCalc.label.complexityIndoorPlumbing": "",
  "baselineCalc.label.complexityOutdoorPlumbing": "",
  "baselineCalc.label.indoorAndOutdoorDistance": "",
  "baselineCalc.label.lengthElectricCableDuct": "",
  "baselineCalc.label.modelVersion": "",
  "baselineCalc.label.numberThermostats": "",
  "baselineCalc.label.radiators": "",
  "baselineCalc.label.thermostaticValves": "",
  "baselineCalc.label.wallsThickness": "",
  "baselineCalc.notify.error": "",
  "baselineCalc.notify.saving": "",
  "baselineCalc.notify.success": "",
  "baselineCalc.table.macroActivity": "",
  "baselineCalc.table.microActivity": "",
  "baselineCalc.table.totalManHours": "",
  "baselineCalc.text.electricianHours": "",
  "baselineCalc.text.installationHours": "",
  "baselineCalc.text.noModel": "",
  "baselineCalc.text.singleSaveWarning": "",
  "baselineCalc.text.totalManHours": "",
  "baselineCalc.title.byActivity": "",
  "baselineCalc.title.byRole": "",
  "baselineCalc.title.calculateInstallationBaseline": "",
  "baselineCalc.title.inputs": "",
  "baselineCalc.title.output": "",
  "baselineCalc.title.scenarioDefinition": "",
  "baselineCalc.title.summary": "",
  "billOfMaterials.addBundle": "",
  "billOfMaterials.addBundleCollection": "",
  "billOfMaterials.addItemsToEquivalencyGroup.success": "",
  "billOfMaterials.addItemsToEquivalencyGroupTooltip": "",
  "billOfMaterials.addVersion": "",
  "billOfMaterials.advanced": "",
  "billOfMaterials.advancedSearch": "",
  "billOfMaterials.advancedSearchTooltip": "",
  "billOfMaterials.bulkSetLabelTooltip": "",
  "billOfMaterials.bundleCollections": "",
  "billOfMaterials.bundles": "",
  "billOfMaterials.bundleSelect.description": "",
  "billOfMaterials.bundleSelect.error": "",
  "billOfMaterials.bundleSelect.loading": "",
  "billOfMaterials.bundleSelect.outdatedBundle": "",
  "billOfMaterials.bundleSelect.title": "",
  "billOfMaterials.clearInterchangeable.confirmDescription": "",
  "billOfMaterials.clearInterchangeable.confirmTitle": "",
  "billOfMaterials.confirmCloseEdit.description": "",
  "billOfMaterials.confirmCloseEdit.title": "",
  "billOfMaterials.confirmDeleteBundle.description": "",
  "billOfMaterials.confirmDeleteBundle.title": "",
  "billOfMaterials.confirmDeleteCollection.description": "",
  "billOfMaterials.confirmDeleteCollection.title": "",
  "billOfMaterials.confirmDeleteItem.description": "",
  "billOfMaterials.confirmDeleteItem.title": "",
  "billOfMaterials.copyBundle": "",
  "billOfMaterials.copyVersion": "",
  "billOfMaterials.costOfAllItems": "",
  "billOfMaterials.costOfAllItems.info": "",
  "billOfMaterials.deleteBundle": "",
  "billOfMaterials.deleteCollection": "",
  "billOfMaterials.deleteItem": "",
  "billOfMaterials.deleteItemEquivalence.confirm.description": "",
  "billOfMaterials.deleteItemEquivalence.confirm.title": "",
  "billOfMaterials.deleteVersion": "",
  "billOfMaterials.design.hlc.itemNotFoundInERP": "",
  "billOfMaterials.editBundle": "",
  "billOfMaterials.editBundle.browseItems": "",
  "billOfMaterials.editBundle.descriptionFieldLabel": "",
  "billOfMaterials.editBundle.itemsInBundle": "",
  "billOfMaterials.editBundle.itemsInInstallationKit": "",
  "billOfMaterials.editBundle.itemsInInstallationKitTooltip": "",
  "billOfMaterials.editBundle.itemsInVersion": "",
  "billOfMaterials.editBundle.itemsTable.amountColumn": "",
  "billOfMaterials.editBundle.itemsTable.amountColumn.infoBox": "",
  "billOfMaterials.editBundle.itemsTable.descritionColumn": "",
  "billOfMaterials.editBundle.itemsTable.erpId": "",
  "billOfMaterials.editBundle.itemsTable.instructionsColumn": "",
  "billOfMaterials.editBundle.itemsTable.nameColumn": "",
  "billOfMaterials.editBundle.titleAdd": "",
  "billOfMaterials.editBundle.titleEdit": "",
  "billOfMaterials.editBundle.titleFieldLabel": "",
  "billOfMaterials.editBundleCollection.descriptionFieldLabel": "",
  "billOfMaterials.editBundleCollection.titleAdd": "",
  "billOfMaterials.editBundleCollection.titleEdit": "",
  "billOfMaterials.editBundleCollection.titleFieldLabel": "",
  "billOfMaterials.editCollection": "",
  "billOfMaterials.editVersion": "",
  "billOfMaterials.editVersion.titleAdd": "",
  "billOfMaterials.editVersion.titleEdit": "",
  "billOfMaterials.grossMarginTwo": "",
  "billOfMaterials.grossMarginTwo.info": "",
  "billOfMaterials.installationKit.AikItems": "",
  "billOfMaterials.installationKit.itemNotUsedElsewhere": "",
  "billOfMaterials.installationKit.setAikItems": "",
  "billOfMaterials.interchangeability": "",
  "billOfMaterials.itemCatalogue.duplicates.description": "",
  "billOfMaterials.itemCatalogue.filter.category": "",
  "billOfMaterials.itemCatalogue.filter.search": "",
  "billOfMaterials.itemCatalogue.select": "",
  "billOfMaterials.itemCatalogue.subtitle": "",
  "billOfMaterials.itemCatalogue.table.archived": "",
  "billOfMaterials.itemCatalogue.table.category": "",
  "billOfMaterials.itemCatalogue.table.costPerUnit": "",
  "billOfMaterials.itemCatalogue.table.description": "",
  "billOfMaterials.itemCatalogue.table.details": "",
  "billOfMaterials.itemCatalogue.table.erpId": "",
  "billOfMaterials.itemCatalogue.table.partOfBundles": "",
  "billOfMaterials.itemCatalogue.table.supplier": "",
  "billOfMaterials.itemCatalogue.table.version": "",
  "billOfMaterials.itemCatalogue.title": "",
  "billOfMaterials.itemDetails": "",
  "billOfMaterials.itemRelations": "",
  "billOfMaterials.labels.addNew": "",
  "billOfMaterials.labels.newLabel": "",
  "billOfMaterials.labels.selectLabel": "",
  "billOfMaterials.mandatoryBundle": "",
  "billOfMaterials.mandatoryBundleTooltip": "",
  "billOfMaterials.miscellaneousItems.addCustomItem": "",
  "billOfMaterials.moveDown": "",
  "billOfMaterials.moveUp": "",
  "billOfMaterials.quantity": "",
  "billOfMaterials.removeItemsFromEquivalencyGroup.success": "",
  "billOfMaterials.setInterchangeable.confirmDescription": "",
  "billOfMaterials.setInterchangeable.confirmTitle": "",
  "billOfMaterials.setLabel": "",
  "billOfMaterials.showLess": "",
  "billOfMaterials.showMore": "",
  "billOfMaterials.unit": "",
  "billOfMaterials.vanStock.duplicateError": "",
  "billOfMaterials.vanStock.regions": "",
  "billOfMaterials.vanStock.selectAllRegions": "",
  "billOfMaterials.vanStock.selectRegions": "",
  "billOfMaterials.vanStock.stockType": "",
  "billOfMaterials.versions": "",
  "billOfMaterialsDesignPage.addMiscellaneousItems": "",
  "billOfMaterialsDesignPage.addMiscellaneousItemsDescription": "",
  "billOfMaterialsDesignPage.Items": "",
  "billOfMaterialsDesignPage.notification.bomLocked": "",
  "billOfMaterialsDesignPage.notification.locking": "",
  "billOfMaterialsDesignPage.notification.locking.error": "",
  "billOfMaterialsDesignPage.notification.locking.success": "",
  "billOfMaterialsDesignPage.notification.markingAsReady": "",
  "billOfMaterialsDesignPage.notification.markingAsReady.error": "",
  "billOfMaterialsDesignPage.notification.markingAsReady.success": "",
  "billOfMaterialsDesignPage.notification.saving": "",
  "billOfMaterialsDesignPage.notification.saving.error": "",
  "billOfMaterialsDesignPage.notification.saving.success": "",
  "billOfMaterialsDesignPage.notification.unlocking": "",
  "billOfMaterialsDesignPage.notification.unlocking.error": "",
  "billOfMaterialsDesignPage.notification.unlocking.success": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.error": "",
  "billOfMaterialsDesignPage.notification.unmarkingAsReady.success": "",
  "billOfMaterialsDesignPage.readyForProcurement": "",
  "billOfMaterialsDesignPage.saveAndLock.button": "",
  "billOfMaterialsDesignPage.saveAndMarkAsReady": "",
  "billOfMaterialsDesignPage.status.readyForProcurement": "",
  "billOfMaterialsDesignPage.title": "",
  "billOfMaterialsDesignPage.unlock.button": "",
  "billOfMaterialsDesignPage.unmarkAsReady": "",
  "booking.button.schedule.survey": "",
  "booking.no.survey.scheduled": "",
  "bookingTool.body.completedOn": "",
  "bookingTool.body.dateAndTime": "",
  "bookingTool.body.loading": "",
  "bookingTool.body.noTimeSlotsAvailable": "",
  "bookingTool.body.scheduledForDateAndTime": "",
  "bookingTool.body.scheduledForTimeWindow": "",
  "bookingTool.body.surveyorName": "",
  "bookingTool.body.surveyUnscheduled": "",
  "bookingTool.body.unsavedChanges": "",
  "bookingTool.button.bookJob": "",
  "bookingTool.button.goToHouseDataTab": "",
  "bookingTool.button.hideScheduler": "",
  "bookingTool.button.openMap": "",
  "bookingTool.button.salesVisitMap": "",
  "bookingTool.button.scheduleSurvey": "",
  "bookingTool.button.unschedule": "",
  "bookingTool.checkbox.battery": "",
  "bookingTool.checkbox.photovoltaic": "",
  "bookingTool.checkbox.solarThermal": "",
  "bookingTool.deactivatedUser": "",
  "bookingTool.error.jobSchedulingFailure": "",
  "bookingTool.error.magicplanAccountNeeded": "",
  "bookingTool.error.noAddress": "",
  "bookingTool.error.noResourceSelected": "",
  "bookingTool.error.noTimeSlotSelected": "",
  "bookingTool.error.resourceNotAvailable": "",
  "bookingTool.label.assignSpecificPerson": "",
  "bookingTool.label.dateAndTime": "",
  "bookingTool.label.installer": "",
  "bookingTool.label.magicPlan": "",
  "bookingTool.label.mobileLink": "",
  "bookingTool.label.notes": "",
  "bookingTool.label.salesSurvey": "",
  "bookingTool.label.salesSurveyor": "",
  "bookingTool.label.skills": "",
  "bookingTool.label.surveyBookings": "",
  "bookingTool.label.surveyDuration": "",
  "bookingTool.label.surveyorType": "",
  "bookingTool.label.surveyPDF": "",
  "bookingTool.label.technicalSurvey": "",
  "bookingTool.label.technicalSurveyor": "",
  "bookingTool.label.timeSlotsAvailable": "",
  "bookingTool.label.unknown": "",
  "bookingTool.label.videoSalesMeeting": "",
  "bookingTool.label.webLink": "",
  "bookingTool.placeholder.selectPerson": "",
  "bookingTool.popup.unscheduleDescription": "",
  "bookingTool.popup.unscheduleHeader": "",
  "bookingTool.radio.both": "",
  "bookingTool.radio.duration": "",
  "bookingTool.radio.salesSurveyor": "",
  "bookingTool.radio.technicalSurveyor": "",
  "bookingTool.skillsChip.BATTERY": "",
  "bookingTool.skillsChip.HEAT_PUMP": "",
  "bookingTool.skillsChip.PHOTOVOLTAIC": "",
  "bookingTool.skillsChip.SOLAR_THERMAL": "",
  "bookingTool.surveyStatus.CANCELLED": "",
  "bookingTool.surveyStatus.FINISHED": "",
  "bookingTool.surveyStatus.IN_PROGRESS": "",
  "bookingTool.surveyStatus.NOT_SCHEDULED": "",
  "bookingTool.surveyStatus.READY": "",
  "bookingTool.surveyStatus.SCHEDULED": "",
  "bookingTool.surveyStatus.UNKNOWN": "",
  "bookingTool.tab.setTimeLater": "",
  "bookingTool.tab.specificTime": "",
  "bookingTool.tab.timeWindow": "",
  "bookingTool.timeWindow.afternoon": "",
  "bookingTool.timeWindow.morning": "",
  "bookingTool.title.homeSurveys": "",
  "bookingTool.title.noAddress": "",
  "bookingTool.title.salesSurvey": "",
  "bookingTool.title.scheduledBookings": "",
  "bookingTool.title.scheduleSuccess": "",
  "bookingTool.title.summary": "",
  "bookingTool.title.technicalSurvey": "",
  "bookingTool.title.videoSalesBooking": "",
  "common.aira.nothing": "",
  "common.country.DE": "Deutschland",
  "common.country.GB": "Großbritannien",
  "common.country.GB.SCT": "Schottland",
  "common.country.IT": "Italien",
  "common.deprecated": "",
  "common.error.contactDevelopmentTeam": "",
  "common.error.errorDownloadingSignedQuote": "",
  "common.error.installationProjectNotFound.title": "",
  "common.error.missing": "Fehlt",
  "common.error.pageNotFound.description": "",
  "common.error.pageNotFound.title": "",
  "common.error.retry": "",
  "common.error.schedulingNotSupported": "",
  "common.error.unableToFindEnergySolution": "",
  "common.error.unknown": "",
  "common.file.browse.text": "",
  "common.file.drag&drop.text": "",
  "common.label.addItems": "",
  "common.label.address": "",
  "common.label.address.google": "",
  "common.label.address.loqate": "",
  "common.label.beta": "",
  "common.label.cancel": "",
  "common.label.catalogueRadiator": "",
  "common.label.clear": "",
  "common.label.comment": "",
  "common.label.confirm": "",
  "common.label.confirmed": "",
  "common.label.continue": "",
  "common.label.copyText": "",
  "common.label.copyURL": "",
  "common.label.date": "",
  "common.label.delete": "",
  "common.label.description": "",
  "common.label.discard": "",
  "common.label.download": "",
  "common.label.downloadPDF": "PDF Herunterladen",
  "common.label.edit": "",
  "common.label.emailAddress": "",
  "common.label.fabric": "",
  "common.label.hlcDisplayName": "",
  "common.label.items": "",
  "common.label.label": "",
  "common.label.latest": "",
  "common.label.load": "",
  "common.label.loggedInAsEmail": "",
  "common.label.measurement.area": "",
  "common.label.measurement.height": "",
  "common.label.measurement.heightmm": "",
  "common.label.measurement.length": "",
  "common.label.measurement.lengthmm": "",
  "common.label.measurement.width": "",
  "common.label.measurement.widthmm": "",
  "common.label.name": "",
  "common.label.next": "Weiter",
  "common.label.none": "",
  "common.label.order": "",
  "common.label.phoneNumber": "",
  "common.label.preliminary": "",
  "common.label.radiators": "",
  "common.label.reload": "",
  "common.label.room": "Raum",
  "common.label.save": "",
  "common.label.sendEmail": "",
  "common.label.set": "",
  "common.label.surveyor": "",
  "common.label.type": "",
  "common.label.unknown": "",
  "common.link.arViews": "",
  "common.link.assistant": "",
  "common.link.back": "",
  "common.link.baselineCalculator": "",
  "common.link.booking": "",
  "common.link.contact": "",
  "common.link.designTools": "",
  "common.link.heatDesign": "",
  "common.link.heatPumpConfig": "",
  "common.link.houseData": "",
  "common.link.installation": "",
  "common.link.installationHandover": "",
  "common.link.installationReport": "Bericht",
  "common.link.installationReview": "",
  "common.link.invoicing": "",
  "common.link.next": "",
  "common.link.orderSummary": "",
  "common.link.print": "",
  "common.link.productSummary": "",
  "common.link.quotation": "",
  "common.link.quote": "",
  "common.link.salesSurvey": "",
  "common.link.service": "",
  "common.link.survey": "",
  "common.link.technicalSurvey": "",
  "common.link.videoCall": "",
  "common.link.videoSalesBooking": "",
  "common.max": "",
  "common.min": "",
  "common.minutes": "",
  "common.months": "",
  "common.no": "",
  "common.note.label": "",
  "common.notify.accessDenied": "",
  "common.notify.appealAccessDenied": "",
  "common.notify.copySuccess": "",
  "common.notify.error": "",
  "common.notify.invitationSent": "",
  "common.notify.loading": "",
  "common.notify.loadingContent": "",
  "common.notify.loadSuccess": "",
  "common.notify.saveSuccess": "",
  "common.notify.success": "",
  "common.notify.unsupportedArea": "",
  "common.pageOfTotal": "Seite {currentPage} von {pageTotal}",
  "common.select.upload-file": "",
  "common.test.demo": "",
  "common.unlock": "",
  "common.years": "",
  "common.yes": "",
  "configuration.product.confirm": "",
  "configuration.product.description": "",
  "configuration.product.title": "",
  "contact.facility.createNew": "",
  "contact.facility.placeholder": "",
  "contact.houseData.cannotBeReused": "",
  "contact.houseData.facility": "",
  "contact.houseUnitSource.custom": "",
  "contact.houseUnitSource.guestHouse": "",
  "contact.houseUnitSource.leftWing": "",
  "contact.houseUnitSource.mainHouse": "",
  "contact.houseUnitSource.rightWing": "",
  "contact.houseUnitSource.standard": "",
  "contact.label.addHouseData": "",
  "contact.label.country": "",
  "contact.label.customerEmail": "",
  "contact.label.customerInformation": "",
  "contact.label.duplicateEmail": "",
  "contact.label.emailAddress": "",
  "contact.label.external.duplicateEmail": "",
  "contact.label.facility": "",
  "contact.label.firstName": "",
  "contact.label.hideHouseData": "",
  "contact.label.houseData": "",
  "contact.label.lastName": "",
  "contact.label.leadSource": "",
  "contact.label.number": "",
  "contact.label.region": "",
  "contact.label.required": "",
  "contact.label.submit": "",
  "contact.leadSource.canvasing": "",
  "contact.leadSource.other": "",
  "contact.leadSource.referral": "",
  "contact.leadSource.required": "",
  "contact.notification.addressAndFacilityUsed": "",
  "contact.notification.addressMultipleBuildings": "",
  "contact.notification.addressUsed": "",
  "contact.notification.canBeReused": "",
  "contact.notification.cannotBeReused": "",
  "contact.notification.enterValidEmail": "",
  "contact.notification.facilityAlreadyUsed": "",
  "contact.notification.facilityCanBeReused": "",
  "contact.notification.facilityDescription": "",
  "contact.notification.facilityInfo": "",
  "contact.notification.isAddressContainer": "",
  "contact.notification.unsupportedArea": "",
  "contact.notification.writeFacilityName": "",
  "contact.placeholder.newFacilityName": "",
  "dashboard.button.addLead": "",
  "dashboard.button.contact": "",
  "dashboard.button.installationProject": "",
  "dashboard.button.quotation": "",
  "dashboard.button.salesVisitMap": "",
  "dashboard.customerDetails": "",
  "dashboard.filter.accepted": "",
  "dashboard.filter.all": "",
  "dashboard.filter.notAccepted": "",
  "dashboard.filter.openForModification": "",
  "dashboard.filter.priceLocked": "",
  "dashboard.filter.productsLocked": "",
  "dashboard.filters.show": "",
  "dashboard.noOngoingWork": "",
  "dashboard.noOngoingWorkAccordingToFilters": "",
  "dashboard.solutionStatus.CANCELLED": "",
  "dashboard.solutionStatus.OPEN_FOR_MODIFICATION": "",
  "dashboard.solutionStatus.openForModification": "",
  "dashboard.solutionStatus.ORDERED": "",
  "dashboard.solutionStatus.PRICE_LOCKED": "",
  "dashboard.solutionStatus.priceLocked": "",
  "dashboard.solutionStatus.PRODUCTS_LOCKED": "",
  "dashboard.solutionStatus.productsLocked": "",
  "dashboard.solutionStatus.READY_FOR_ORDER": "",
  "dashboard.solutionStatus.readyForOrder": "",
  "dashboard.status": "",
  "dashboard.title.overview": "",
  "error.hlc.product.selection.incompatibleTankSize.description": "",
  "error.hlc.product.selection.incompatibleTankSize.heading": "",
  "error.hlc.unlock.invalidState": "",
  "error.support.card.askHelpText": "",
  "error.support.card.description": "",
  "error.support.card.description.lock": "",
  "error.support.card.description.save": "",
  "error.support.card.description.unlock": "",
  "error.support.card.hlc.locked.description": "",
  "error.support.card.hlc.locked.heading": "",
  "error.support.card.hlc.product.selection.description": "",
  "error.support.card.hlc.product.selection.heading": "",
  "error.support.card.hlc.sendDesignReviewEmail.description": "",
  "error.support.card.hlc.sendDesignReviewEmail.heading": "",
  "error.support.card.hlc.sendDesignReviewEmail.wrongState.description": "",
  "error.support.card.subTitle": "",
  "error.support.card.title": "",
  "error.support.card.title.lock": "",
  "error.support.card.title.save": "",
  "error.support.card.title.unlock": "",
  "floorPlans.error.noDwellingInProject": "",
  "floorPlans.error.noFloorsInProject": "",
  "floorPlans.symbols.bufferTank": "Pufferspeicher",
  "floorPlans.symbols.cylinder": "",
  "floorPlans.symbols.expansionVessel": "",
  "floorPlans.symbols.indoorUnit": "Inneneinheit",
  "floorPlans.symbols.outdoorUnit": "Außeneinheit",
  "floorPlans.symbols.thermostat": "Thermostat",
  "floorPlans.title.floorPlan": "",
  "form-redirect.manual-redirect.title": "",
  "form-redirect.status.aerospace.failure": "",
  "form-redirect.status.aerospace.header": "",
  "form-redirect.status.aerospace.success": "",
  "form-redirect.status.magicplan.check.failure": "",
  "form-redirect.status.magicplan.check.header": "",
  "form-redirect.status.magicplan.check.incorrect": "",
  "form-redirect.status.magicplan.check.success": "",
  "form-redirect.status.magicplan.create.failure": "",
  "form-redirect.status.magicplan.create.header": "",
  "form-redirect.status.magicplan.create.notice": "",
  "form-redirect.status.magicplan.create.success": "",
  "form-redirect.status.magicplan.failure": "",
  "form-redirect.status.magicplan.header": "",
  "form-redirect.status.magicplan.not-found": "",
  "form-redirect.status.magicplan.success": "",
  "form-redirect.status.magicplan.user-mismatch.advice": "",
  "form-redirect.status.magicplan.user-mismatch.aerospace-user": "",
  "form-redirect.status.magicplan.user-mismatch.alternative": "",
  "form-redirect.status.magicplan.user-mismatch.magicplan-user": "",
  "form-redirect.status.magicplan.user-mismatch.reassign.failure": "",
  "form-redirect.status.magicplan.user-mismatch.reassign.success": "",
  "form-redirect.title": "",
  "handoverSolution.tabs.externalLinks": "Andere Dokumente",
  "handoverSolution.tabs.floorPlan": "Grundriss",
  "handoverSolution.tabs.hardware": "Hardware",
  "handoverSolution.tabs.overview": "Übersicht",
  "handoverSolution.tabs.radiators": "Heizkörper",
  "handoverSolution.tabs.schematics": "Schema",
  "heatDesign.additionalLossTypes.exposedLocation": "",
  "heatDesign.additionalLossTypes.highCeiling": "",
  "heatDesign.additionalLossTypes.intermittentHeating": "",
  "heatDesign.additionalLossTypes.thermalBridging": "",
  "heatDesign.adjacentKind.heated": "Heizen",
  "heatDesign.adjacentKind.outside": "Draußen",
  "heatDesign.adjacentKind.room": "Raum",
  "heatDesign.adjacentKind.soil": "Gegen Boden",
  "heatDesign.adjacentKind.solidFloor": "Fester Boden",
  "heatDesign.adjacentKind.suspendedFloor": "Abgehängter Boden",
  "heatDesign.adjacentKind.unheated": "Ungeheizt",
  "heatDesign.billOfMaterial.summary.miscellaneousAddition": "",
  "heatDesign.billOfMaterials.heatDesignSummary.accessoriesTitle": "",
  "heatDesign.billOfMaterials.heatDesignSummary.appliancesTitle": "",
  "heatDesign.billOfMaterials.heatDesignSummary.customRadiatorTooltip": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model\n": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model": "",
  "heatDesign.billOfMaterials.heatDesignSummary.radiatorList.quantity": "",
  "heatDesign.billOfMaterials.heatDesignSummary.summary": "",
  "heatDesign.billOfMaterials.heatDesignSummary.title": "",
  "heatDesign.billOfMaterials.heatDesignSummary.updatedWarning": "",
  "heatDesign.billOfMaterials.miscellaneousItemsTable.customItemTooltip": "",
  "heatDesign.billOfMaterials.notLockedWarning": "",
  "heatDesign.billOfMaterials.summary.cost": "",
  "heatDesign.billOfMaterials.summary.designedPrice": "",
  "heatDesign.billOfMaterials.summary.itemDeletedFromErp": "",
  "heatDesign.billOfMaterials.summary.itemsPerUnit": "",
  "heatDesign.billOfMaterials.summary.prefilledFromHeatDesign": "",
  "heatDesign.billOfMaterials.summary.salesPrice": "",
  "heatDesign.cancelRadiatorEdit.detail": "",
  "heatDesign.cancelRadiatorEdit.title": "",
  "heatDesign.climate.data.error": "",
  "heatDesign.climate.data.error.title": "",
  "heatDesign.common.erpId": "",
  "heatDesign.customerAcceptance.sendEmailConfirmation": "",
  "heatDesign.customerReport.button": "",
  "heatDesign.customerReport.calculationResults.flowTemperature": "Das ist die Vorlauftemperatur für Ihre <b>Heizkörper</b>.",
  "heatDesign.customerReport.calculationResults.heatLossColdestDay": "Wärmeverlust des Hauses und Heizleistung bei der niedrigsten erwarteten Temperatur auf Ihrem Grundstück (<b>{temp} ⁰C</b>).",
  "heatDesign.customerReport.calculationResults.showerTime": "Das ist die voraussichtliche Zeitspanne, in der Sie Warmwasser zapfen können. Falls das komplette Warmwasser verbraucht sein sollte, dauert das Wiederaufheizen ca. <b>{reheatTime}</b> min.",
  "heatDesign.customerReport.calculationResults.title": "Die Berechnungsergebnisse für Ihr Zuhause",
  "heatDesign.customerReport.calculationResults.ufhFlowTemperature": "Das ist die Vorlauftemperatur für Ihre <b>Fußbodenheizung</b>.",
  "heatDesign.customerReport.comment.label": "Besonderheiten bei Ihrer Installation",
  "heatDesign.customerReport.comment.placeholder": "z.B. Warum wurde der Standort der Pumpe gewählt, was wird für die Installation von Innengeräten benötigt, was ist das Besondere an dieser Anlage und diesem Haus, was muss beachtet werden...",
  "heatDesign.customerReport.comment.template.buttonTitle": "",
  "heatDesign.customerReport.comment.template.text": "",
  "heatDesign.customerReport.floorPlans.disclaimer": "Hinweis: Es kann sein, dass einige Elemente nicht in den Grundriss aufgenommen wurden.",
  "heatDesign.customerReport.floorPlans.title": "Grundrisse",
  "heatDesign.customerReport.instructions": "Diese Zusammenfassung kann dem Kunden als Planungsgrundlage zur Verfügung gestellt werden.",
  "heatDesign.customerReport.instructions.description": "Diese Zusammenfassung enthält das Ergebnis der Heizlast Berechnung Ihres Zuhauses sowie die von Ihnen gewählten Komponenten. Dieser Bericht zeigt was wo installiert werden wird und dient als Übereinkunft zwischen Ihnen und Aira, um Mißverständnisse während der Ausführung zu vermeiden.",
  "heatDesign.customerReport.instructions.title": "Diese Zusammenfassung kann dem Kunden als Planungsgrundlage zur Verfügung gestellt werden.",
  "heatDesign.customerReport.newRadiators.text": "",
  "heatDesign.customerReport.newRadiators.title": "Heizkörper die zum Tausch vorgesehen sind",
  "heatDesign.customerReport.outdoorUnits": "{count, plural, one{Außeneinheit} other{Außeneinheiten}}",
  "heatDesign.customerReport.preview.description": "",
  "heatDesign.customerReport.preview.embed.title": "",
  "heatDesign.customerReport.preview.refresh.button": "",
  "heatDesign.customerReport.preview.refresh.description": "",
  "heatDesign.customerReport.preview.title": "",
  "heatDesign.customerReport.preview.wrongState": "",
  "heatDesign.customerReport.subtitle": "Technische Design",
  "heatDesign.customerReport.title": "Kunden Bericht",
  "heatDesign.customerReport.upcomingChanges.description": "Das Technische Design umfasst alles von den Spezifikationen Ihrer Innen- und Außeneinheiten bis hin zur Platzierung Ihrer Heizkörper. Zögern Sie nicht, die Details zu prüfen, und lassen Sie uns wissen, wenn Sie Fragen haben.",
  "heatDesign.customerReport.upcomingChanges.title": "Was wir bei Ihnen durchführen werden",
  "heatDesign.customRadiator": "",
  "heatDesign.deleteRadiator.detail": "",
  "heatDesign.deleteRadiator.title": "",
  "heatDesign.deleteUnderfloorHeating.detail": "",
  "heatDesign.deleteUnderfloorHeating.title": "",
  "heatDesign.development.resetProject": "",
  "heatDesign.discardChanges.detail": "",
  "heatDesign.discardChanges.title": "",
  "heatDesign.dwellingDefaultsModal.description": "",
  "heatDesign.dwellingDefaultsModal.title": "",
  "heatDesign.dwellingValidationModal.title": "",
  "heatDesign.emitterList.emitterType.radiator": "",
  "heatDesign.emitterList.emitterType.underfloor": "",
  "heatDesign.emitterList.noEmitters": "",
  "heatDesign.emitterList.summary.flowNeededForRadiators": "",
  "heatDesign.emitterList.summary.flowNeededForUnderfloorHeating": "",
  "heatDesign.emitterList.summary.radiatorFlowTemperature": "",
  "heatDesign.emitterList.summary.radiatorReturnTemperature": "",
  "heatDesign.emitterList.summary.totalFlowNeeded": "",
  "heatDesign.emitterList.summary.totalFlowNeededForHouse": "",
  "heatDesign.emitterList.summary.underfloorHeatingFlowTemperature": "",
  "heatDesign.emitterList.summary.underfloorHeatingReturnTemperature": "",
  "heatDesign.emitterList.underfloorHeatingDisclaimer": "",
  "heatDesign.emitterReport.calculationResults.circuitDeltaT": "",
  "heatDesign.emitterReport.calculationResults.emitterAverageTemperature": "",
  "heatDesign.emitterReport.calculationResults.flowTemperature": "",
  "heatDesign.emitterReport.calculationResults.returnTemperature": "",
  "heatDesign.emitterReport.tab.title": "",
  "heatDesign.emitterReport.title": "",
  "heatDesign.emitterReport.upcomingChanges.description": "",
  "heatDesign.emittersValidationModal.body": "",
  "heatDesign.emittersValidationModal.title": "",
  "heatDesign.error.application.description": "",
  "heatDesign.error.application.title": "",
  "heatDesign.error.cannotRetrieveMagicplanProject": "",
  "heatDesign.error.checkMagicplanProjectNotDeleted": "",
  "heatDesign.error.dwellingAddress": "",
  "heatDesign.error.magicplan.errorRetrievingProject": "",
  "heatDesign.error.noFormFound": "",
  "heatDesign.error.noHouseData.description": "",
  "heatDesign.error.noHouseData.details": "",
  "heatDesign.error.noHouseData.title": "",
  "heatDesign.error.noMagicplanSubmission": "",
  "heatDesign.error.noMagicplanSubmission.howTo": "",
  "heatDesign.error.noMagicplanSubmission.latestSurvey": "",
  "heatDesign.error.noMagicplanSubmission.title": "",
  "heatDesign.error.noRoomsOnFloor": "",
  "heatDesign.error.unableToLoadMagicplanProject": "",
  "heatDesign.existingRadiator": "",
  "heatDesign.floorDefaultsModal.description": "",
  "heatDesign.floorDefaultsModal.generalSection.title": "",
  "heatDesign.floorDefaultsModal.soilInput.label": "",
  "heatDesign.floorDefaultsModal.soilInput.tooltip": "",
  "heatDesign.floorDefaultsModal.title": "",
  "heatDesign.floorOverviewValidationModal.body": "",
  "heatDesign.floorOverviewValidationModal.invalidFloors": "",
  "heatDesign.floorOverviewValidationModal.invalidRooms": "",
  "heatDesign.flowRate": "",
  "heatDesign.helper.floorPlan": "",
  "heatDesign.helpModal.faq.body": "",
  "heatDesign.helpModal.faq.button": "",
  "heatDesign.helpModal.faq.title": "",
  "heatDesign.helpModal.request.body": "",
  "heatDesign.helpModal.request.title": "",
  "heatDesign.isCustomTooltip": "",
  "heatDesign.keep": "",
  "heatDesign.lock.confirmation": "Do you want to lock the design? If it's the first time locking the design this will send an e-mail to the customer with the Design Report.",
  "heatDesign.lock.confirmation.designReview": "Do you want to lock the design?",
  "heatDesign.lock.confirmation.sendDesignReviewEmail": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.accepted": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.not-ready": "",
  "heatDesign.lock.confirmation.sendDesignReviewEmail.status.ready": "",
  "heatDesign.magicplan.advanced": "",
  "heatDesign.magicplan.heading": "",
  "heatDesign.magicplan.links.mobile": "",
  "heatDesign.magicplan.links.title": "",
  "heatDesign.magicplan.links.web": "",
  "heatDesign.magicplan.merge.buttonLabel": "",
  "heatDesign.magicplan.merge.buttonLabel.loading": "",
  "heatDesign.magicplan.merge.description": "",
  "heatDesign.magicplan.merge.error.card.description": "",
  "heatDesign.magicplan.merge.error.card.description.duplicates": "",
  "heatDesign.magicplan.merge.error.card.title": "",
  "heatDesign.magicplan.merge.locked": "",
  "heatDesign.magicplan.merge.modal.body": "",
  "heatDesign.magicplan.merge.modal.title": "",
  "heatDesign.magicplan.merge.success": "",
  "heatDesign.magicplan.merge.unsaved": "",
  "heatDesign.magicplan.reassign.buttonLabel": "",
  "heatDesign.magicplan.reassign.buttonLabel.loading": "",
  "heatDesign.magicplan.reassign.description": "",
  "heatDesign.magicplan.reassign.error.card.description": "",
  "heatDesign.magicplan.reassign.error.card.title": "",
  "heatDesign.magicplan.reassign.success": "",
  "heatDesign.magicplan.reimport.buttonLabel": "",
  "heatDesign.magicplan.reimport.buttonLabel.loading": "",
  "heatDesign.magicplan.reimport.description": "",
  "heatDesign.magicplan.reimport.error.card.description": "",
  "heatDesign.magicplan.reimport.error.card.title": "",
  "heatDesign.magicplan.reimport.locked": "",
  "heatDesign.magicplan.reimport.modal.body": "",
  "heatDesign.magicplan.reimport.modal.title": "",
  "heatDesign.magicplan.reimport.success": "",
  "heatDesign.newEmitterDeltaT": "",
  "heatDesign.newRadiator": "",
  "heatDesign.newRadiator.NewRadiators": "",
  "heatDesign.notify.designReviewEmailSent": "",
  "heatDesign.notify.lockingProject": "",
  "heatDesign.notify.projectLocked": "",
  "heatDesign.notify.projectUnlocked": "",
  "heatDesign.notify.sendingDesignReviewEmail": "",
  "heatDesign.notify.unlockingProject": "",
  "heatDesign.procurement.aik": "",
  "heatDesign.procurement.aikItemError": "",
  "heatDesign.procurement.bom": "",
  "heatDesign.procurement.customItemError": "",
  "heatDesign.procurement.deleteEquivalenceGroup.description": "",
  "heatDesign.procurement.deleteEquivalenceGroup.title": "",
  "heatDesign.procurement.duplicateItemError": "",
  "heatDesign.procurement.itemName": "",
  "heatDesign.procurement.quantity": "",
  "heatDesign.procurement.quantity.tooltip": "",
  "heatDesign.procurement.removedFromErpError": "",
  "heatDesign.procurement.vanStock": "",
  "heatDesign.productSelection.heatDetails.calculatedTotalHeatLoss": "",
  "heatDesign.productSelection.heatDetails.chosenFlowTemp": "",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp": "",
  "heatDesign.productSelection.heatDetails.outdoorDesignTemp.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.bivalencePoint.heatOutput.without.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.with.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.without.electric.tooltip": "",
  "heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.temperature": "",
  "heatDesign.productSelection.heatDetails.title": "",
  "heatDesign.productSelection.heatPumpPackageSelection": "",
  "heatDesign.productSelection.heatPumpPackageSelection.designLocked": "",
  "heatDesign.productSelection.heatPumpPackageSelection.productsLocked": "",
  "heatDesign.productSelection.indoorUnitDetails.cylinderReheatTime": "",
  "heatDesign.productSelection.indoorUnitDetails.houseWaterSection.title": "",
  "heatDesign.productSelection.indoorUnitDetails.title": "",
  "heatDesign.productSelection.noNewRadiators": "",
  "heatDesign.productSelection.noPerformanceData": "",
  "heatDesign.productSelection.outdoorUnitDetails.bivalencePoint": "",
  "heatDesign.productSelection.outdoorUnitDetails.bivalenceSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.heatOutput.with.electric": "",
  "heatDesign.productSelection.outdoorUnitDetails.houseHeatingSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutput": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithElectric": "",
  "heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithoutElectric": "",
  "heatDesign.productSelection.outdoorUnitDetails.performanceODTSection.title": "",
  "heatDesign.productSelection.outdoorUnitDetails.scop": "",
  "heatDesign.productSelection.outdoorUnitDetails.scop.description": "",
  "heatDesign.productSelection.outdoorUnitDetails.title": "",
  "heatDesign.productSelection.radiators.showRadiatorsButton": "",
  "heatDesign.productSelection.radiators.title": "",
  "heatDesign.productSelection.radiators.tooltip": "",
  "heatDesign.productSelection.radiators.total": "",
  "heatDesign.productSelection.radiatorsList.groupHeader": "",
  "heatDesign.productSelection.radiatorsList.groupHeader.v2": "",
  "heatDesign.productSelection.radiatorsList.noRadiators": "",
  "heatDesign.productSelection.radiatorsList.title": "",
  "heatDesign.productSelection.showerTime": "",
  "heatDesign.productSelection.unlockProducts.error.body": "",
  "heatDesign.productSelection.unlockProducts.error.title": "",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity": "",
  "heatDesign.productSelection.waterUsage.domesticHotWaterCapacity.tooltip": "",
  "heatDesign.productSelection.waterUsage.numBathrooms": "",
  "heatDesign.productSelection.waterUsage.numBedrooms": "",
  "heatDesign.productSelection.waterUsage.numOccupants": "",
  "heatDesign.propertyDetails.ACPHDefault": "",
  "heatDesign.propertyDetails.AddressTemperatureData": "",
  "heatDesign.propertyDetails.altitude.label": "",
  "heatDesign.propertyDetails.altitude.tooltip": "",
  "heatDesign.propertyDetails.bathrooms": "",
  "heatDesign.propertyDetails.bedrooms": "",
  "heatDesign.propertyDetails.climateZone": "",
  "heatDesign.propertyDetails.DegreeDays": "",
  "heatDesign.propertyDetails.ExternalTempForLocation": "",
  "heatDesign.propertyDetails.floors": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.description": "",
  "heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.disabled": "",
  "heatDesign.propertyDetails.MeanAirTemp": "",
  "heatDesign.propertyDetails.noStreetView": "",
  "heatDesign.propertyDetails.NumberOfBathrooms": "",
  "heatDesign.propertyDetails.NumberOfBathrooms.tooltip": "",
  "heatDesign.propertyDetails.NumberOfBedrooms": "",
  "heatDesign.propertyDetails.NumberOfBedrooms.tooltip": "",
  "heatDesign.propertyDetails.NumberOfFloors": "",
  "heatDesign.propertyDetails.NumberOfOccupants": "",
  "heatDesign.propertyDetails.occupants": "",
  "heatDesign.propertyDetails.OutsideTemp": "",
  "heatDesign.propertyDetails.temperatureCompensated": "",
  "heatDesign.propertyDetails.TemperatureCompensation": "",
  "heatDesign.propertyDetails.TemperatureCompensation.description": "",
  "heatDesign.propertyDetails.title": "",
  "heatDesign.propertyDetails.updateUValuesModal.cancelButton": "",
  "heatDesign.propertyDetails.updateUValuesModal.confirmButton": "",
  "heatDesign.propertyDetails.updateUValuesModal.currentUValueHeader": "",
  "heatDesign.propertyDetails.updateUValuesModal.helper": "",
  "heatDesign.propertyDetails.updateUValuesModal.suggestedUValueHeader": "",
  "heatDesign.propertyDetails.updateUValuesModal.title": "",
  "heatDesign.propertyDetails.YearBuilt": "",
  "heatDesign.propertyOverview.ventilation.acphDefaultsTooltip": "",
  "heatDesign.propertyOverview.ventilation.airPermeabilityTooltip": "",
  "heatDesign.propertyOverview.ventilation.calculationMethodTooltip": "",
  "heatDesign.propertyOverview.ventilation.exposureTooltip": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bathroom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.bedroom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.diningRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.dressingRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.gamesRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.hall": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.kitchen": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.landing": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.livingRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.loungeRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.other": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.storeRoom": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.study": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.toilet": "",
  "heatDesign.propertyOverview.ventilation.roomTooltip.utilityRoom": "",
  "heatDesign.radiator.FlowReturnDeltaT": "",
  "heatDesign.radiator.FlowTemperature": "",
  "heatDesign.radiator.heatOutput.totals.meanWaterAirTransferInvalid": "",
  "heatDesign.radiator.heatOutput.totals.sum": "",
  "heatDesign.radiator.heatOutput.totals.title": "",
  "heatDesign.radiator.MeanWaterTemperature": "",
  "heatDesign.radiator.ReturnTemperature": "",
  "heatDesign.radiatorModal.browse": "",
  "heatDesign.radiatorModal.catalogue.brand": "",
  "heatDesign.radiatorModal.catalogue.category": "",
  "heatDesign.radiatorModal.catalogue.tab.radiators": "",
  "heatDesign.radiatorModal.catalogue.title": "",
  "heatDesign.radiatorModal.catalogue.type": "",
  "heatDesign.radiatorModal.discard": "",
  "heatDesign.radiatorModal.emitterPanel.description": "",
  "heatDesign.radiatorModal.emitterPanel.title.new": "",
  "heatDesign.radiatorModal.emitterPanel.title.replace": "",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.label": "",
  "heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.tooltip": "",
  "heatDesign.radiatorModal.heatBalance.title": "",
  "heatDesign.radiatorModal.heatBalance.tooltip": "",
  "heatDesign.radiatorModal.noRadiators": "",
  "heatDesign.radiatorModal.removedFromErp": "",
  "heatDesign.radiatorModal.select": "",
  "heatDesign.radiatorModal.shared.all": "",
  "heatDesign.radiatorModal.shared.calculatedOutput.dynamic": "",
  "heatDesign.radiatorModal.shared.calculatedOutput.static": "",
  "heatDesign.radiatorModal.shared.catalog": "",
  "heatDesign.radiatorModal.shared.columns": "",
  "heatDesign.radiatorModal.shared.height": "",
  "heatDesign.radiatorModal.shared.image": "",
  "heatDesign.radiatorModal.shared.length": "",
  "heatDesign.radiatorModal.shared.material": "",
  "heatDesign.radiatorModal.shared.model": "",
  "heatDesign.radiatorModal.shared.search": "",
  "heatDesign.radiatorModal.shared.type": "",
  "heatDesign.radiatorModal.table.details": "",
  "heatDesign.radiatorModal.table.factor": "",
  "heatDesign.radiatorModal.table.nominalOutput.detailed": "",
  "heatDesign.radiatorModal.table.nominalOutput.generic": "",
  "heatDesign.radiatorModal.table.weight": "",
  "heatDesign.radiatorModal.title.add": "",
  "heatDesign.radiatorModal.title.replace": "",
  "heatDesign.radiatorRenderer.AddRadiator": "",
  "heatDesign.radiatorRenderer.AddRadiatorDetail": "",
  "heatDesign.radiatorRenderer.AddUnderfloorHeating": "",
  "heatDesign.radiatorRenderer.DeltaT": "",
  "heatDesign.radiatorRenderer.emitterTempAndRoomDiff": "",
  "heatDesign.radiatorRenderer.footer.HeatOutput": "",
  "heatDesign.radiatorRenderer.footer.ReturnTemp": "",
  "heatDesign.radiatorRenderer.footer.TotalRoomHeatLoss": "",
  "heatDesign.radiatorRenderer.footer.WattDifference": "",
  "heatDesign.radiatorRenderer.header.DeltaT": "",
  "heatDesign.radiatorRenderer.header.FlowTemp": "",
  "heatDesign.radiatorRenderer.header.MeanRadiatorTemp": "",
  "heatDesign.radiatorRenderer.header.ReturnTemp": "",
  "heatDesign.radiatorRenderer.RemoveRadiator": "",
  "heatDesign.radiatorRenderer.RoomTemp": "",
  "heatDesign.radiators.calculatedOutputTooltip": "",
  "heatDesign.radiators.conversionFactor": "",
  "heatDesign.radiators.externalVendorId": "",
  "heatDesign.radiators.manufacturerId": "",
  "heatDesign.radiators.waterContent": "",
  "heatDesign.radiatorSizeCategory.design": "",
  "heatDesign.radiatorSizeCategory.large": "",
  "heatDesign.radiatorSizeCategory.standard": "",
  "heatDesign.radiatorsOverview.emitterList.title": "",
  "heatDesign.radiatorsOverview.floorOverview.title": "",
  "heatDesign.radiatorTable.addExisting": "",
  "heatDesign.radiatorTable.addNew": "",
  "heatDesign.radiatorTable.calculated.output": "",
  "heatDesign.radiatorTable.description": "",
  "heatDesign.radiatorTable.ExistingRadiators": "",
  "heatDesign.radiatorTable.flowRateAdjustment.cannotAdjustElectricRadiators": "",
  "heatDesign.radiatorTable.flowRateAdjustment.flowReturnTempLabel": "",
  "heatDesign.radiatorTable.flowRateAdjustment.label": "",
  "heatDesign.radiatorTable.meanWaterTemp": "",
  "heatDesign.radiatorTable.meanWaterTemp.tooltip": "",
  "heatDesign.radiatorTable.meanWaterTempPerRadiator": "",
  "heatDesign.radiatorTable.nominalOutput.deltaT": "ΔT (⁰C)",
  "heatDesign.radiatorTable.nominalOutput.title": "",
  "heatDesign.radiatorTable.nominalOutput.watts": "",
  "heatDesign.radiatorTable.replacedBy": "",
  "heatDesign.radiatorTable.replaces": "",
  "heatDesign.radiatorTable.temperatureAdjustment.title": "",
  "heatDesign.radiatorTable.TotalOutputOfAllEnabledEmittersWatts": "",
  "heatDesign.radiatorTable.underfloorHeating.matchRoomHeatLossLabel": "",
  "heatDesign.radiatorTable.underfloorHeating.output": "",
  "heatDesign.radiatorTable.underfloorHeating.tooltip": "",
  "heatDesign.radiatorTable.Use": "",
  "heatDesign.replaceRadiator": "",
  "heatDesign.report.clause.effectiveAirChangesPerHour": "",
  "heatDesign.report.dwellingData.climateData.adjustedExternalTemp": "Berechnete Außentemperatur (°C)",
  "heatDesign.report.dwellingData.climateData.degreeDays": "Gradtage",
  "heatDesign.report.dwellingData.climateData.meanAirTemp": "Mittlere Lufttemperatur (°C)",
  "heatDesign.report.dwellingData.climateData.outsideTemp": "Außentemperatur (°C)",
  "heatDesign.report.dwellingData.climateData.temperatureAdjustmentExposedLocation": "Temperaturkorrektur für freistehende Lage (°C)",
  "heatDesign.report.dwellingData.climateData.temperatureCompensation": "Weitere Temperaturkorrektur für den Standort (°C)",
  "heatDesign.report.dwellingData.climateData.title": "Klimadaten",
  "heatDesign.report.dwellingData.energyDemand.dailyHotWaterEnergyDemand": "Täglicher Energiebedarf für Warmwasser (kWh)",
  "heatDesign.report.dwellingData.energyDemand.fabricEnergyDemand": "Jährlicher Energiebedarf der Gebäudehülle (kWh)",
  "heatDesign.report.dwellingData.energyDemand.hotWaterEnergyDemand": "Jährlicher Energiebedarf für Warmwasser (kWh)",
  "heatDesign.report.dwellingData.energyDemand.title": "Energiebedarf",
  "heatDesign.report.dwellingData.energyDemand.totalEnergyDemand": "Jährlicher Gesamtenergiebedarf (kWh)",
  "heatDesign.report.dwellingData.energyDemand.ventilationEnergyDemand": "Jährlicher Energiebedarf für Lüftung (kWh)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowDeltaT": "Empfohlenes Vorlauf-ΔT (°C)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.suggestedFlowTemp": "Empfohlene Vorlauftemperatur (°C)",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.title": "Vorlauftemperaturen & Heizkörper",
  "heatDesign.report.dwellingData.flowTemperatureAndEmitters.totalHeatEmittance": "Gesamte Heizleistung der Heizkörper (kW)",
  "heatDesign.report.dwellingData.generalInfo.address": "Adresse",
  "heatDesign.report.dwellingData.generalInfo.customerName": "Kundenname",
  "heatDesign.report.dwellingData.generalInfo.numBedrooms": "Anzahl der Schlafzimmer",
  "heatDesign.report.dwellingData.generalInfo.numFloors": "Etagen",
  "heatDesign.report.dwellingData.generalInfo.numOccupants": "Bewohner",
  "heatDesign.report.dwellingData.generalInfo.title": "Allgemeine Informationen",
  "heatDesign.report.dwellingData.generalInfo.totalHousingArea": "Gesamtfläche (m²)",
  "heatDesign.report.dwellingData.generalInfo.yearBuilt": "Baujahr",
  "heatDesign.report.dwellingData.heatLoss.fabricHeatLoss": "Wärmeverlust durch die Gebäudehülle (kW)",
  "heatDesign.report.dwellingData.heatLoss.fabricTitle": "Gebäudehülle",
  "heatDesign.report.dwellingData.heatLoss.title": "Wärmeverlust",
  "heatDesign.report.dwellingData.heatLoss.totalHeatLoss": "Gesamter Wärmeverlust (kW)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphCustom": "{value} ACPH (Benutzerdefiniert für das gesamte Haus)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.acphDefaultsUsed": "ACPH-Standardeinstellungen verwendet",
  "heatDesign.report.dwellingData.heatLoss.ventilation.airPermeability": "Luftdurchlässigkeit (m³/(h·m²))",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposedFacadesFactor": "Faktor der freiliegenden Fassaden",
  "heatDesign.report.dwellingData.heatLoss.ventilation.exposureCoefficient": "Expositionskoeffizient",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method": "Berechnungsmethode",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.simple": "Einfach (EN12831)",
  "heatDesign.report.dwellingData.heatLoss.ventilation.method.standard": "Norm (EN12831)",
  "heatDesign.report.dwellingData.heatLoss.ventilationHeatLoss": "Wärmeverlust durch Lüftung (kW)",
  "heatDesign.report.dwellingData.heatLoss.ventilationTitle": "Lüftung",
  "heatDesign.report.dwellingData.indoorUnitDetails.reheatTime": "Boiler-Aufheizzeit auf {temperature} °C (Minuten)",
  "heatDesign.report.dwellingData.indoorUnitDetails.selectedIndoorUnit": "Ausgewählte Inneneinheit",
  "heatDesign.report.dwellingData.indoorUnitDetails.title": "Details zur Inneneinheit",
  "heatDesign.report.dwellingData.outdoorUnitDetails.bivalencePoint": "Bivalenzpunkt (°C)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.capacity": "Kapazität der ausgewählten Außeneinheit (kW)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.maxHeatOutput": "Maximale Heizleistung ohne elektrische Zusatzheizung (kW)",
  "heatDesign.report.dwellingData.outdoorUnitDetails.scop": "SCOP",
  "heatDesign.report.dwellingData.outdoorUnitDetails.selectedOutdoorUnit": "Ausgewählte Außeneinheit",
  "heatDesign.report.dwellingData.outdoorUnitDetails.title": "Details zur Außeneinheit",
  "heatDesign.report.dwellingData.title": "Gebäudedaten",
  "heatDesign.report.externalWallAdditionalSurfaceArea": "Wärmeverlustberechnungen für Außenwände berücksichtigen gemäß EN 12831 eine zusätzliche Fläche von {additionalAreaPercentage}% im Vergleich zur Innenseite.",
  "heatDesign.report.floorDetails.generalInfo.numberOfRooms": "Anzahl der Räume",
  "heatDesign.report.floorDetails.generalInfo.title": "Allgemeine Informationen",
  "heatDesign.report.floorDetails.generalInfo.totalArea": "Gesamtfläche (m²)",
  "heatDesign.report.floorDetails.generalInfo.totalHeatEmittance": "Gesamte Heizleistung der Heizkörper (kW)",
  "heatDesign.report.floorDetails.generalInfo.totalHeatLoss": "Gesamter Wärmeverlust (kW)",
  "heatDesign.report.floorDetails.rooms.title": "Räume",
  "heatDesign.report.instructions": "Der folgende technische Bericht enthält alle Ergebnisse der Wärmeverlustberechnung und kann heruntergeladen und an die zuständigen Behörden zur Finalisierung der Installation gesendet werden.",
  "heatDesign.report.instructions.description": "Die folgende technische Dokumentation enthält sämtliche Ergebnisse der Wärmeverlustberechnung und kann heruntergeladen und an die zuständigen Behörden übermittelt werden, die sie zur Finalisierung der Installation benötigen.",
  "heatDesign.report.instructions.title": "Der technische Bericht enthält alle Ergebnisse der Wärmeverlustberechnungen.",
  "heatDesign.report.pdf.footer.generatedAt": "Bericht erstellt am {dateTime}",
  "heatDesign.report.pdf.footer.generatedAtBy": "Bericht erstellt von {author} am {dateTime}",
  "heatDesign.report.pdf.locked": "Bericht erstellt am {generatedAt}",
  "heatDesign.report.pdf.noReports": "Keine gesperrten Berichte gefunden.",
  "heatDesign.report.pdf.preview": "Vorschau",
  "heatDesign.report.pdf.select": "Bericht auswählen",
  "heatDesign.report.roomDetails.avgRoomHeight": "Durchschnittliche Raumhöhe (m)",
  "heatDesign.report.roomDetails.designRoomTemp": "Auslegungstemperatur des Raums (°C)",
  "heatDesign.report.roomDetails.emitterDetails.calculatedOutput": "Berechnete Leistung bei ΔT {deltaT}°C (W)",
  "heatDesign.report.roomDetails.emitterDetails.description": "Bezeichnung [Höhe x Breite]",
  "heatDesign.report.roomDetails.emitterDetails.missingEmitters": "In diesem Raum sind keine Heizkörper vorhanden",
  "heatDesign.report.roomDetails.emitterDetails.nominalOutput": "Nennleistung",
  "heatDesign.report.roomDetails.emitterDetails.note": "Die Vor-/Rücklauf-ΔT wurde für diesen Heizkörper individuell angepasst, um eine bestimmte Leistung zu erreichen.",
  "heatDesign.report.roomDetails.emitterDetails.status": "Status",
  "heatDesign.report.roomDetails.emitterDetails.title": "Heizkörper",
  "heatDesign.report.roomDetails.emitterDetails.type": "Typ",
  "heatDesign.report.roomDetails.fabricDetails.area": "Fläche (m²)",
  "heatDesign.report.roomDetails.fabricDetails.heatLoss": "Wärmeverlust (W)",
  "heatDesign.report.roomDetails.fabricDetails.opposingTemperature": "Gegentemperatur (°C)",
  "heatDesign.report.roomDetails.fabricDetails.surface.doors": "Außentür",
  "heatDesign.report.roomDetails.fabricDetails.surface.externalWalls": "Außenwand",
  "heatDesign.report.roomDetails.fabricDetails.surface.floors": "Boden",
  "heatDesign.report.roomDetails.fabricDetails.surface.internalWalls": "Innenwand",
  "heatDesign.report.roomDetails.fabricDetails.surface.partyWalls": "Trennwand",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofGlazings": "Dachverglasung",
  "heatDesign.report.roomDetails.fabricDetails.surface.roofsOrCeilings": "Dach",
  "heatDesign.report.roomDetails.fabricDetails.surface.windows": "Fenster",
  "heatDesign.report.roomDetails.fabricDetails.surfaceType": "Bauteil",
  "heatDesign.report.roomDetails.fabricDetails.title": "Details zur Gebäudehülle",
  "heatDesign.report.roomDetails.fabricDetails.uValue": "U-Wert | Material",
  "heatDesign.report.roomDetails.fabricHeatLoss": "Wärmeverlust durch Gebäudehülle (W)",
  "heatDesign.report.roomDetails.heatFlux": "Wärmestromdichte (W/m²)",
  "heatDesign.report.roomDetails.otherEmitters.title": "Weitere Heizkörper",
  "heatDesign.report.roomDetails.roomArea": "Raumfläche (m²)",
  "heatDesign.report.roomDetails.roomType": "Raumtyp",
  "heatDesign.report.roomDetails.total": "Gesamt",
  "heatDesign.report.roomDetails.totalHeatEmittance": "Gesamte Heizleistung der Heizkörper (W)",
  "heatDesign.report.roomDetails.totalHeatLoss": "Gesamter Wärmeverlust (W)",
  "heatDesign.report.roomDetails.underfloorHeating.output": "Leistung (W)",
  "heatDesign.report.roomDetails.underfloorHeating.title": "Fußbodenheizung",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour": "Luftwechsel pro Stunde (ACPH)",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.simple": "Basierend auf der einfachen Berechnungsmethode gemäß EN12831 2017.",
  "heatDesign.report.roomDetails.ventilationDetails.avgAirChangesPerHour.standard": "Basierend auf der Zonenberechnungsmethode gemäß EN12831 2017.",
  "heatDesign.report.roomDetails.ventilationDetails.deltaT": "ΔT (°C)",
  "heatDesign.report.roomDetails.ventilationDetails.externalEnvelopeArea": "Fläche der Außenflächen (m²)",
  "heatDesign.report.roomDetails.ventilationDetails.heatLoss": "Wärmeverlust (W)",
  "heatDesign.report.roomDetails.ventilationDetails.title": "Lüftungsdetails",
  "heatDesign.report.roomDetails.ventilationDetails.totalVolume": "Volumen (m³)",
  "heatDesign.report.roomDetails.ventilationHeatLoss": "Wärmeverlust durch Lüftung (W)",
  "heatDesign.report.thermalBridgingIncludedInHeatLoss": "Alle Wärmeverlustberechnungen berücksichtigen {thermalBridgingAdjustment} bei den U-Werten gemäß EN 12831.",
  "heatDesign.reportInstructions.emitterReport.description": "Es wird angegeben, in welchen Räumen sich die Heizkörper befinden oder installiert werden sollen, ihre Spezifikationen, eventuelle Anpassungen und ihre Leistung.",
  "heatDesign.reportInstructions.emitterReport.title": "Dies sind alle Heizkörper, die auf dem Grundstück des Kunden verwendet werden",
  "heatDesign.resultSnapshotBanner.description": "",
  "heatDesign.resultSnapshotBanner.designReviewState.accepted": "",
  "heatDesign.resultSnapshotBanner.designReviewState.not-ready": "",
  "heatDesign.resultSnapshotBanner.designReviewState.ready": "",
  "heatDesign.resultSnapshotBanner.flowTemperature": "",
  "heatDesign.resultSnapshotBanner.houseHeatLoss": "",
  "heatDesign.resultSnapshotBanner.lockedAt": "",
  "heatDesign.resultSnapshotBanner.title": "",
  "heatDesign.resultSnapshotBanner.totalEmitterOutput": "",
  "heatDesign.returnTemperatureTooltip": "",
  "heatDesign.RoofGlazingsRenderer.addRoofGlazing": "",
  "heatDesign.RoofGlazingsRenderer.UVAlueAngleCompensation": "",
  "heatDesign.room.averageCeilingHeight": "",
  "heatDesign.room.avgAirChangesPerHour": "",
  "heatDesign.room.avgAirChangesPerHour.disabledByPulseTest": "",
  "heatDesign.room.avgAirChangesPerHour.helperText.custom": "",
  "heatDesign.room.avgAirChangesPerHour.helperText.standardized": "",
  "heatDesign.room.avgAirChangesPerHourHelperText": "",
  "heatDesign.room.avgAirChangesPerHourSuffix": "",
  "heatDesign.room.belowFloorLabel.heatedRoom": "",
  "heatDesign.room.belowFloorLabel.solidFloor": "",
  "heatDesign.room.belowFloorLabel.suspendedFloor": "",
  "heatDesign.room.belowFloorLabel.unheatedRoom": "",
  "heatDesign.room.designRoomTemp": "",
  "heatDesign.room.designRoomUnheated": "",
  "heatDesign.room.heatedRoom": "",
  "heatDesign.room.heatedRoom.tooltip": "",
  "heatDesign.room.openFlue": "",
  "heatDesign.room.openFlue.disabledByPulseTest": "",
  "heatDesign.room.referenceAirChangesPerHour": "",
  "heatDesign.room.referenceAirChangesPerHourTooltip": "",
  "heatDesign.room.roomName": "",
  "heatDesign.room.roomType": "",
  "heatDesign.room.tempOfSpaceAbove": "",
  "heatDesign.room.typeOfSpaceAbove": "",
  "heatDesign.room.whatIsBelowTheFloor": "",
  "heatDesign.roomDetails.belowFloor.tooltip": "",
  "heatDesign.roomEditor.internalDoorsAndWindowsNotIncluded": "",
  "heatDesign.roomOutput.helperText": "",
  "heatDesign.roomRenderer.selectASurface": "",
  "heatDesign.roomSurfaceTypes.ceilings": "",
  "heatDesign.roomSurfaceTypes.doors": "",
  "heatDesign.roomSurfaceTypes.externalDoors": "",
  "heatDesign.roomSurfaceTypes.externalWalls": "",
  "heatDesign.roomSurfaceTypes.externalWindows": "",
  "heatDesign.roomSurfaceTypes.floors": "",
  "heatDesign.roomSurfaceTypes.foundation": "",
  "heatDesign.roomSurfaceTypes.intermediateFloors": "",
  "heatDesign.roomSurfaceTypes.internalWalls": "",
  "heatDesign.roomSurfaceTypes.partyWalls": "",
  "heatDesign.roomSurfaceTypes.roof": "",
  "heatDesign.roomSurfaceTypes.roofGlazings": "",
  "heatDesign.roomSurfaceTypes.roofs": "",
  "heatDesign.roomSurfaceTypes.roofsOrCeilings": "",
  "heatDesign.roomSurfaceTypes.windows": "",
  "heatDesign.roomType.bathroom": "",
  "heatDesign.roomType.bedroom": "",
  "heatDesign.roomType.hallway": "",
  "heatDesign.roomType.kitchen": "",
  "heatDesign.roomType.livingRoom": "",
  "heatDesign.roomType.other": "",
  "heatDesign.roomValidationModal.title": "",
  "heatDesign.sidebar.title": "",
  "heatDesign.surfaces.door": "",
  "heatDesign.surfaces.doors": "",
  "heatDesign.surfaces.externalDoor": "",
  "heatDesign.surfaces.externalDoors": "",
  "heatDesign.surfaces.externalWall": "",
  "heatDesign.surfaces.externalWalls": "",
  "heatDesign.surfaces.externalWindow": "",
  "heatDesign.surfaces.externalWindows": "",
  "heatDesign.surfaces.floor": "",
  "heatDesign.surfaces.floors": "",
  "heatDesign.surfaces.foundation": "",
  "heatDesign.surfaces.intermediateFloors": "",
  "heatDesign.surfaces.internalWall": "",
  "heatDesign.surfaces.internalWalls": "",
  "heatDesign.surfaces.partyWall": "",
  "heatDesign.surfaces.partyWalls": "",
  "heatDesign.surfaces.roof": "",
  "heatDesign.surfaces.roofGlazing": "",
  "heatDesign.surfaces.roofGlazings": "",
  "heatDesign.surfaces.roofsOrCeilings": "",
  "heatDesign.surfaces.wall": "",
  "heatDesign.surfaces.window": "",
  "heatDesign.surfaces.windows": "",
  "heatDesign.surveyForms.download.notSubmitted": "",
  "heatDesign.surveyForms.download.section.description": "",
  "heatDesign.tableHeaders.annualAdditionalEnergyDemand": "",
  "heatDesign.tableHeaders.annualFabricEnergyDemand": "",
  "heatDesign.tableHeaders.annualHeatingEnergyDemand": "",
  "heatDesign.tableHeaders.annualVentilationEnergyDemand": "",
  "heatDesign.tableHeaders.annualWaterEnergyDemand": "",
  "heatDesign.tableHeaders.ceilingHeight": "",
  "heatDesign.tableHeaders.dailyWaterEnergyDemand": "",
  "heatDesign.tableHeaders.effectiveAirChangesPerHour": "",
  "heatDesign.tableHeaders.emitterDeltaT": "",
  "heatDesign.tableHeaders.emitterDescription": "",
  "heatDesign.tableHeaders.energyDemand": "",
  "heatDesign.tableHeaders.existing-new": "",
  "heatDesign.tableHeaders.floorArea": "",
  "heatDesign.tableHeaders.flowRate": "",
  "heatDesign.tableHeaders.heatLossSummary": "",
  "heatDesign.tableHeaders.HouseAcph": "",
  "heatDesign.tableHeaders.OutputWatt": "",
  "heatDesign.tableHeaders.roomHeatLoss": "",
  "heatDesign.tableHeaders.roomName": "",
  "heatDesign.tableHeaders.TotalAdditionalHeatLoss": "",
  "heatDesign.tableHeaders.totalAnnualEnergyDemand": "",
  "heatDesign.tableHeaders.TotalFabricHeatLoss": "",
  "heatDesign.tableHeaders.TotalHeatLoss": "",
  "heatDesign.tableHeaders.TotalVentilationHeatLoss": "",
  "heatDesign.tableHeaders.value": "",
  "heatDesign.tableHeaders.ventilationHeatLoss": "",
  "heatDesign.tableHeaders.wattPerSqM": "",
  "heatDesign.technicalReport.title": "Technik Bericht",
  "heatDesign.title.dropdownAlternativeSelect": "",
  "heatDesign.title.dwellingDefaultValues": "",
  "heatDesign.title.floorLevelDefaultValues": "",
  "heatDesign.title.floorOverview": "Grundriss Übersicht",
  "heatDesign.title.heatLossOverview": "Heizlast Berechnung",
  "heatDesign.title.heatPumpConfig": "Wärmepumpe Konfiguration",
  "heatDesign.title.productSelection": "Produkt Auswahl",
  "heatDesign.title.propertyDetails": "Gebäude Daten",
  "heatDesign.title.pulseTestAirPermeability": "",
  "heatDesign.title.radiatorsOverview": "Heizkörper Auswahl",
  "heatDesign.title.resultsExport": "Zusammenfassung und Übersicht",
  "heatDesign.title.selectASurface": "",
  "heatDesign.title.surfaceEditor": "",
  "heatDesign.title.totalArea": "",
  "heatDesign.title.totalVolume": "",
  "heatDesign.underfloorHeating.editTitle": "",
  "heatDesign.underFloorHeating.UnderfloorHeating": "",
  "heatDesign.unlock.confirmation": "",
  "heatDesign.usingDefaultUValue.dwelling": "",
  "heatDesign.usingDefaultUValue.floorLevel": "",
  "heatDesign.usingDefaultUValue.floorLevelAbove": "",
  "heatDesign.uValues.addCustomUValue": "",
  "heatDesign.uValues.addCustomUValue.save": "",
  "heatDesign.uValues.name": "",
  "heatDesign.uValues.placeholder": "",
  "heatDesign.uValues.placeholder.noFabricTypeInProject": "",
  "heatDesign.uValues.placeholder.setAtDwelling": "",
  "heatDesign.uValues.uValue": "",
  "heatDesign.uValues.uValues": "",
  "heatDesign.uValues.valueLabel": "",
  "heatDesign.uValues.worseThanBuildingRegulations": "",
  "heatDesign.uValues.worseThanBuildingRegulations.v2": "",
  "heatDesign.validationModal.customValue.missingOrInvalid": "",
  "heatDesign.validationModal.missingOrInvalid": "",
  "heatDesign.ventilation.acphDefaults": "",
  "heatDesign.ventilation.acphDefaultsDisabledPulseTest": "",
  "heatDesign.ventilation.airPermeability": "",
  "heatDesign.ventilation.calculationMethod": "",
  "heatDesign.ventilation.exposureOfHouse": "",
  "heatDesign.ventilation.houseWideAcph": "",
  "heatDesign.ventilation.houseWideAcphTooltip": "",
  "heatDesign.ventilation.roomType": "",
  "heatDesign.ventilation.ventilation": "",
  "heatDesign.wallsRenderer.adjoiningRoom.heated": "",
  "heatDesign.wallsRenderer.adjoiningRoom.roomNameLabel": "",
  "heatDesign.wallsRenderer.adjoiningRoom.tempLabel": "",
  "heatDesign.wallsRenderer.adjoiningRoom.title": "",
  "heatDesign.wallsRenderer.adjoiningRoom.unheated": "",
  "heatDesign.wallsRenderer.area": "",
  "heatDesign.wallsRenderer.area.tooltip": "",
  "heatDesign.wallsRenderer.length": "",
  "heatDesign.wallsRenderer.soilPercentage.label": "",
  "heatDesign.wallsRenderer.soilPercentage.resetToDefaults": "",
  "heatDesign.wallsRenderer.soilPercentage.tooltip": "",
  "heatDesign.waterReheatTime": "",
  "heatPumpConfig.advancedSettings": "",
  "heatPumpConfig.advancedSettings.cooling.title": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.0kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.3kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.6kwHeating3kwBackup": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.bridgeWarning": "",
  "heatPumpConfig.advancedSettings.heating.additionalElectricHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold": "",
  "heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceCompressor.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating": "",
  "heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold": "",
  "heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold": "",
  "heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating": "",
  "heatPumpConfig.advancedSettings.heating.priorityTimeHeating.tooltip": "",
  "heatPumpConfig.advancedSettings.heating.title": "",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp": "",
  "heatPumpConfig.advancedSettings.zone.coolSupplyTemp.tooltip": "",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp": "",
  "heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp.tooltip": "",
  "heatPumpConfig.advancedSettingsDescription": "",
  "heatPumpConfig.domesticHotWater.tankSizeMismatch": "",
  "heatPumpConfig.general.dhw100": "",
  "heatPumpConfig.general.dhw150": "",
  "heatPumpConfig.general.dhw200": "",
  "heatPumpConfig.general.dhw250": "",
  "heatPumpConfig.general.dhw300": "",
  "heatPumpConfig.general.dhwNone": "",
  "heatPumpConfig.general.domesticHotWaterTankSize": "",
  "heatPumpConfig.general.domesticHotWaterTankSize.outdoorCapacityConstraintViolation": "",
  "heatPumpConfig.general.electricityMeterType": "",
  "heatPumpConfig.general.electricityMeterTypeDisclaimer": "",
  "heatPumpConfig.general.et112": "",
  "heatPumpConfig.general.et340": "",
  "heatPumpConfig.general.hotWaterTemperature": "",
  "heatPumpConfig.general.title": "",
  "heatPumpConfig.indoorUnitType.hydrobox": "",
  "heatPumpConfig.indoorUnitType.mismatch": "",
  "heatPumpConfig.indoorUnitType.title": "",
  "heatPumpConfig.indoorUnitType.tooltip": "",
  "heatPumpConfig.indoorUnitType.unitower": "",
  "heatPumpConfig.lastUpdatedAt": "",
  "heatPumpConfig.neverSaved": "",
  "heatPumpConfig.notAiraBrand.description": "",
  "heatPumpConfig.notAiraBrand.title": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity12kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity6kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacity8kw": "",
  "heatPumpConfig.outdoorUnitCapacity.capacityMismatch": "",
  "heatPumpConfig.outdoorUnitCapacity.title": "",
  "heatPumpConfig.outdoorUnitCapacity.tooltip": "",
  "heatPumpConfig.output": "",
  "heatPumpConfig.saveButtonTitle": "",
  "heatPumpConfig.saveButtonTitle.saving": "",
  "heatPumpConfig.silent.mode": "",
  "heatPumpConfig.silent.mode.tooltip": "",
  "heatPumpConfig.title": "",
  "heatPumpConfig.zone.coolingCurveTitle": "",
  "heatPumpConfig.zone.emitterType.radiator": "",
  "heatPumpConfig.zone.emitterType.title": "",
  "heatPumpConfig.zone.emitterType.underfloor": "",
  "heatPumpConfig.zone.features.both": "",
  "heatPumpConfig.zone.features.cooling": "",
  "heatPumpConfig.zone.features.heating": "",
  "heatPumpConfig.zone.features.title": "",
  "heatPumpConfig.zone.flowTemperature.subtitle": "",
  "heatPumpConfig.zone.flowTemperature.title": "",
  "heatPumpConfig.zone.flowTemperature.tooltip": "",
  "heatPumpConfig.zone.flowTemperature.warning": "",
  "heatPumpConfig.zone.graph.coolingCurveInvalid": "",
  "heatPumpConfig.zone.graph.flowTemperature": "",
  "heatPumpConfig.zone.graph.heatingCurveInvalid": "",
  "heatPumpConfig.zone.graph.odtFlowPointLabel": "",
  "heatPumpConfig.zone.graph.outdoorDesignTemperature": "",
  "heatPumpConfig.zone.heatingCurveTitle": "",
  "heatPumpConfig.zone.outdoorDesignTemperature.subtitle": "",
  "heatPumpConfig.zone.outdoorDesignTemperature.title": "",
  "heatPumpConfig.zone.temperaturePlaceholder": "",
  "heatPumpConfig.zone.thermostatType.helperText": "",
  "heatPumpConfig.zone.thermostatType.title": "",
  "heatPumpConfig.zone.thermostatType.wired": "",
  "heatPumpConfig.zone.thermostatType.wireless": "",
  "heatPumpConfig.zone.title": "",
  "heatPumpConfig.zonesConfiguration.diagram.altText": "",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.description": "",
  "heatPumpConfig.zonesConfiguration.oneZoneNoMixingValve.label": "",
  "heatPumpConfig.zonesConfiguration.title": "",
  "heatPumpConfig.zonesConfiguration.tooltip": "",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.description": "",
  "heatPumpConfig.zonesConfiguration.twoZonesOneMixingValve.label": "",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.description": "",
  "heatPumpConfig.zonesConfiguration.twoZonesTwoMixingValves.label": "",
  "hlc.label.save": "",
  "hlc.label.save.and.lock": "",
  "hlc.label.unlock.design": "",
  "hlc.unsavedChanges.modal.description": "",
  "hlc.unsavedChanges.modal.tittle": "",
  "houseData.addressChangeConfirmation.title": "",
  "houseData.addressChangeModal.distanceBetweenOldAndNewAddress": "",
  "houseData.addressChangeModal.ensureSurveysAndInstallationsAreAssignedCorrectly": "",
  "houseData.addressChangeModal.timeBetweenOldAndNewAddress": "",
  "houseData.addressChangeModal.updateAddressInErp": "",
  "houseData.addressChangeModal.urgent": "",
  "houseData.addressChangeModal.warning": "",
  "houseData.fuelType.GAS": "",
  "houseData.fuelType.LIQUID_GAS": "",
  "houseData.fuelType.OIL": "",
  "houseData.houseType.APARTMENT": "",
  "houseData.houseType.BUNGALOW": "",
  "houseData.houseType.DETACHED": "",
  "houseData.houseType.SEMI_DETACHED": "",
  "houseData.houseType.TERRACED": "",
  "houseData.label.bedroomCount": "",
  "houseData.label.fuelConsumption": "",
  "houseData.label.houseSize": "",
  "houseData.label.houseType": "",
  "houseData.label.housingUnits": "",
  "houseData.label.numberOfPanels": "",
  "houseData.label.postalCode": "",
  "houseData.notification.isAddressContainer": "",
  "houseData.title.consumptionEstimate": "",
  "houseData.title.solarPanelsInstalled": "",
  "infobar.airaZeroSignedAt": "",
  "infoBar.contactId": "",
  "infoBar.downloadSignedQuote": "",
  "infoBar.emailAddress": "",
  "infoBar.errorDownloadingSignedQuote": "",
  "infoBar.expiryDate": "",
  "infoBar.mobileNumber": "",
  "infoBar.phoneNumber": "",
  "infoBar.previewQuote": "",
  "infoBar.reference": "",
  "infoBar.resendTariffEmail": "",
  "infoBar.sendTariffEmail": "",
  "infoBar.showSignableQuote": "",
  "infoBar.signing.cancel": "",
  "infoBar.signing.content": "",
  "infoBar.signing.optionInstalments": "Monatlicher Plan",
  "infoBar.signing.optionInstalmentsFinancing": "",
  "infoBar.signing.optionInvoice": "Festpreis",
  "infoBar.signing.title": "",
  "infobar.tariff.modal.description": "",
  "infoBar.tariff.modal.title": "",
  "infoBar.viewAcceptedQuote": "",
  "infoBar.viewFinalOrderSummary": "",
  "infoBar.viewOrderSummary": "",
  "installationBooking.emptyState.noInstallation": "",
  "installationBooking.errors.hours": "",
  "installationBooking.fields.hours": "",
  "installationBooking.fields.jobsPerDay": "",
  "installationBooking.fields.jobsPerDayCaption": "",
  "installationBooking.main.bookButton": "",
  "installationBooking.main.description": "",
  "installationBooking.main.descriptionfrombaseline": "",
  "installationBooking.main.title": "",
  "installationBooking.moreOptions": "",
  "installationBooking.notify.booking": "",
  "installationBooking.notify.bookingCreated": "",
  "installationBooking.roles.electrician": "",
  "installationBooking.roles.installer": "",
  "installationBooking.roles.landscaper": "",
  "installationHandover.addUrl": "Add Url\n",
  "installationHandover.appButtonTitle": "Gehen Sie weiter zu der Aira App um diese Wärmepumpe zu konfigurieren",
  "installationHandover.externalLinks.deviationsTracker": "Deviations tracker",
  "installationHandover.externalLinks.fillInInstallationReport": "Installationsbericht ausfüllen",
  "installationHandover.externalLinks.heatPumpConfiguration": "Konfiguration der Wärmepumpe",
  "installationHandover.extraDocuments.downloadPDF": "Magicplan PDF herunterladen",
  "installationHandover.extraDocuments.openMagicplanApp": "Magicplan App öffnen",
  "installationHandover.extraDocuments.openMagicplanWebsite": "Magicplan Webseite öffnen",
  "installationHandover.extraDocuments.sharepointFolder": "Sharepoint Mappe",
  "installationHandover.floorPlans.disclaimer": "Bitte beachten Sie dass, manche Elemente des Systems villleicht nicht gelisted sind. Nur diejenige die in der Technische Untesuchung hintelegt wurden, werden hier gezeigt.",
  "installationHandover.hardware.common.unspecified": "Nicht spezifiziert",
  "installationHandover.hardware.electrical.airaFuseBoxLocationPhotos": "Foto der vorgeschlagenen Position des Aira-Sicherungskastens",
  "installationHandover.hardware.electrical.cableCrossSection": "",
  "installationHandover.hardware.electrical.cableRunsDrawnOnFloorPlan": "Hast du den Verlauf der Stromkabel von Sicherungskasten zur Innen- und Außeneinheit in Magicplan eingezeichnet?",
  "installationHandover.hardware.electrical.comment": "Kommentare",
  "installationHandover.hardware.electrical.earthingArrangement": "",
  "installationHandover.hardware.electrical.earthingArrangementCustomerSubstation": "",
  "installationHandover.hardware.electrical.earthingArrangementTnCs": "",
  "installationHandover.hardware.electrical.earthingArrangementTnS": "",
  "installationHandover.hardware.electrical.earthingArrangementTt": "",
  "installationHandover.hardware.electrical.electricityBillPhotos": "",
  "installationHandover.hardware.electrical.electricityNetworkOperator": "Wer ist der Netzbetreiber der Stromversorgung? (Netzbetreiber ist nicht unbedingt der Lieferant!)",
  "installationHandover.hardware.electrical.fuseBoardPhotos": "Foto der vorhandenen Verteilungs-/Sicherungstafel mit Angabe der Sicherungswerte",
  "installationHandover.hardware.electrical.isIncomingThreePhase": "Ist das eingehende Stromkabel 3-Phasig?",
  "installationHandover.hardware.electrical.loopedService": "",
  "installationHandover.hardware.electrical.mainFuseRating": "Nennwert der Hauptsicherung (Ampere)",
  "installationHandover.hardware.electrical.maxDemandTest": "",
  "installationHandover.hardware.electrical.meterBoxPhotos": "Fotos von Stromzählerbox (inkl. aller Isolatoren)",
  "installationHandover.hardware.electrical.mpanNumber": "",
  "installationHandover.hardware.electrical.phases": "Phasen",
  "installationHandover.hardware.electrical.singlePhase": "1",
  "installationHandover.hardware.electrical.spaceForNewCables": "Ist im Kabelrohr zwischen Zählerkasten und Schaltschrank Platz für neue Kabel?",
  "installationHandover.hardware.electrical.threePhase": "3",
  "installationHandover.hardware.existingHeatSource.a2aHp": "Luftwärmepumpe",
  "installationHandover.hardware.existingHeatSource.a2wHp": "Luft-Wasser Wärmepumpe",
  "installationHandover.hardware.existingHeatSource.additionalHotWaterTankPhotos": "Foto von weiterem Warmwassertank (wenn vorhanden)",
  "installationHandover.hardware.existingHeatSource.boilerType": "Typ ",
  "installationHandover.hardware.existingHeatSource.boilerTypeCombi": "Heizungsbereitung mit Warmwasserbereitung im Durchflusssystem",
  "installationHandover.hardware.existingHeatSource.boilerTypeRegular": "Wärmeerzeugung mit externen Trinkwasserspeicher und Ausdehungsgefäß",
  "installationHandover.hardware.existingHeatSource.boilerTypeSystem": "Wandgerät mit Internen Ausdehnungsfäß und Heizungspumpe",
  "installationHandover.hardware.existingHeatSource.coldWaterPressureBar": "Kaltwasserdruck (bar)",
  "installationHandover.hardware.existingHeatSource.comments": "Bemerkungen",
  "installationHandover.hardware.existingHeatSource.connectionType": "Wie erfolgt die Anbindung zu den Heizkreisen?",
  "installationHandover.hardware.existingHeatSource.connectionTypeMetal": "Metall",
  "installationHandover.hardware.existingHeatSource.connectionTypePlastic": "Kunststoff",
  "installationHandover.hardware.existingHeatSource.consumptionKwh": "Verbrauch [KWH]",
  "installationHandover.hardware.existingHeatSource.consumptionLitres": "Verbrauch [Liter]",
  "installationHandover.hardware.existingHeatSource.coveringType": "Womit schließen wir die Öffnung?",
  "installationHandover.hardware.existingHeatSource.eaHp": "",
  "installationHandover.hardware.existingHeatSource.electricHeaters": "Nachtspeicheröfen",
  "installationHandover.hardware.existingHeatSource.electricityConsumptionMeasurements": "Stromverbrauchsmessungen (kWh)",
  "installationHandover.hardware.existingHeatSource.existingHeatingSourcePhotos": "Dokumentiere die aktuelle Heizungsanlage",
  "installationHandover.hardware.existingHeatSource.flowTemperatureCelsius": "Welche Vorlauftemperatur ist zu erkennen?",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypeNeedsBrickingUp": "Mauerarbeiten notwendig",
  "installationHandover.hardware.existingHeatSource.flueCoveringTypePlastic": "Plastik-Verkleidung",
  "installationHandover.hardware.existingHeatSource.flueNeedsCovering": "Gibt es einen Schornstein, der abgedeckt werden muss",
  "installationHandover.hardware.existingHeatSource.fluePhotos": "Foto von Schornstein mit anliegenden Wänden",
  "installationHandover.hardware.existingHeatSource.gasBoiler": "Erdgas Heizgerät",
  "installationHandover.hardware.existingHeatSource.gasBoilerTechnicalDataPhotos": "Dokumentiere das Typenschild der Heizung",
  "installationHandover.hardware.existingHeatSource.gasConsumptionMeasurements": "Gasverbrauchsmessungen (kWh)",
  "installationHandover.hardware.existingHeatSource.gasMeterNumber": "Notiere die Nummer vom Gaszähler",
  "installationHandover.hardware.existingHeatSource.gasMeterPhotos": "Foto vom Gaszähler",
  "installationHandover.hardware.existingHeatSource.gasNetworkOperator": "Wer ist der Netzbetreiber der Gasversorgung? (Netzbetreiber ist nicht unbedingt der Lieferant!)",
  "installationHandover.hardware.existingHeatSource.gsHp": "Erdwärmepumpe",
  "installationHandover.hardware.existingHeatSource.hasSeparateCirculationPump": "Ist eine Zirkulationspumpe vorhanden ",
  "installationHandover.hardware.existingHeatSource.heatRegulation": "",
  "installationHandover.hardware.existingHeatSource.heatRegulationFixedTemperature": "",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightLowering": "",
  "installationHandover.hardware.existingHeatSource.heatRegulationNightOff": "",
  "installationHandover.hardware.existingHeatSource.heatRegulationTurnOnWhenPresent": "",
  "installationHandover.hardware.existingHeatSource.heatSourceType": "Derzeitige Wärmeerzeugung ? ",
  "installationHandover.hardware.existingHeatSource.hotWaterProduction": "Die Herstellung von Warmwasser erfolgt über",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionExistingHeatSource": "Vorhandene Wärmequelle",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherIntegrationDesired": "Anderes System (Einbindung gewünscht)",
  "installationHandover.hardware.existingHeatSource.hotWaterProductionOtherNoIntegrationDesired": "Anderes System (keine Einbindung gewünscht)",
  "installationHandover.hardware.existingHeatSource.isCondensingBoiler": "Ist es ein Brennwert Gerät?",
  "installationHandover.hardware.existingHeatSource.lpgBoiler": "Flüssiggas Heizgerät",
  "installationHandover.hardware.existingHeatSource.numberOfHeatingZones": "Anzahl der Heizkreise ",
  "installationHandover.hardware.existingHeatSource.oilBurner": "Öl- Heizung",
  "installationHandover.hardware.existingHeatSource.oilConsumptionMeasurements": "Ölverbrauchsmessungen (Liter)",
  "installationHandover.hardware.existingHeatSource.oilLeftLitres": "Wie viel Öl befindet sich noch in den Tanks?",
  "installationHandover.hardware.existingHeatSource.otherComponentsPhotos": "Fotos von anderen Komponenten ",
  "installationHandover.hardware.existingHeatSource.pellets": "Pellet-Heizung",
  "installationHandover.hardware.existingHeatSource.pipeSizeToDhw": "Größe der Rohrleitung zum Warmwasser",
  "installationHandover.hardware.existingHeatSource.pipeSizeToExistingBoiler": "Größe der Rohrleitung zum vorhandenen Kessel",
  "installationHandover.hardware.existingHeatSource.removeExistingHeatingSource": "Sollten wir die bestehende Heizung entsorgen?",
  "installationHandover.hardware.existingHeatSource.showerPumpNeedsReplacement": "Existiert eine Duschpumpe, die beseitigt und verrohrt werden muss? ",
  "installationHandover.hardware.existingHeatSource.solarThermal": "Solarthermie",
  "installationHandover.hardware.existingHeatSource.underfloorHeating": "Hat das Haus eine Fußbodenheizung?",
  "installationHandover.hardware.existingHeatSource.underfloorHeatingManifoldPhotos": "Fotos von Heizkreisverteilern?",
  "installationHandover.hardware.existingHeatSource.ventingType": "Ist es offen belüftet?",
  "installationHandover.hardware.existingHeatSource.ventingTypeOpenVented": "Offen belüftet",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnknown": "Unbekannt (Bilder machen)",
  "installationHandover.hardware.existingHeatSource.ventingTypeUnvented": "Unbelüftet",
  "installationHandover.hardware.existingHeatSource.year": "Jahr",
  "installationHandover.hardware.existingHeatSource.yearOfManufacture": "Baujahr der Wärmeerzeugung ",
  "installationHandover.hardware.existingHeatSource.yesRemoveExistingHeatSource": "Falls ja",
  "installationHandover.hardware.existingHeatSource.yesUnderfloorHeating": "Falls ja",
  "installationHandover.hardware.indoorInstallation.allInOneUnitFits": "Passt ein All-in-one/Unitower?",
  "installationHandover.hardware.indoorInstallation.asbestosRisk": "Gibt es gefährliche Asbestrisiken im Objekt?",
  "installationHandover.hardware.indoorInstallation.bufferTankSize": "Maximaler Durchmesser des Pufferspeichers",
  "installationHandover.hardware.indoorInstallation.bufferTankSize100L": "100l (940x570 mm)",
  "installationHandover.hardware.indoorInstallation.bufferTankSize45L": "45l (880x370 mm)",
  "installationHandover.hardware.indoorInstallation.chipBoardFloorType": "Spahnplatten",
  "installationHandover.hardware.indoorInstallation.coldWaterFlowRate": "Durchflussmenge von kaltem Wasser (Liter / Minute)",
  "installationHandover.hardware.indoorInstallation.comments": "Kommentare (mögliche Hindernisse auf dem Weg, Stufen, enge Türen usw.)",
  "installationHandover.hardware.indoorInstallation.componentsShouldFitWhenTilted": "Prüfe das Kippmaß unserer Komponenten mit der Raumhöhe",
  "installationHandover.hardware.indoorInstallation.crampedInstallationLocation": "Eingeschränkte Bewegungsfreiheit für Installer? z.B. nur eine Person kann gleichzeitig arbeiten",
  "installationHandover.hardware.indoorInstallation.distanceBetweenTundishAndTermination": "Abstand zwischen Verteiler und Anschluss (D2)",
  "installationHandover.hardware.indoorInstallation.distanceOfNewPrimaryPipeworkNeeded": "Distanz der benötigten neuen innenliegenden Leitungen (vielleicht können bestehende Leitungen verwendet werden)",
  "installationHandover.hardware.indoorInstallation.drainPipePresent": "Ist ein Abflussrohr vorhanden?",
  "installationHandover.hardware.indoorInstallation.drinkingWaterFilter": "Ist ein Trinkwasserfilter vorhanden ",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupply": "Wie wird das Haus mit Trinkwasser versorgt?",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyNetwork": "Trinkwassernetz",
  "installationHandover.hardware.indoorInstallation.drinkingWaterSupplyOwnWell": "Eigener Brunnen",
  "installationHandover.hardware.indoorInstallation.emergencyDischargePipeReusable": "Gibt es eine wiederverwendbare Notfall-Entlastungsleitung?",
  "installationHandover.hardware.indoorInstallation.floorBoardFloorType": "Bodenplatten",
  "installationHandover.hardware.indoorInstallation.floorType": "Welche Art von Boden?",
  "installationHandover.hardware.indoorInstallation.hydraulicUnitFits": "Passt ein Hydraulikaggregat (770x440x350)?",
  "installationHandover.hardware.indoorInstallation.indoorPrimaryPipingRoutePhotos": "Fotos der Route der Installer (Linie von Lieferort bis Aufstellort in Foto einzeichnen)",
  "installationHandover.hardware.indoorInstallation.installationLocationPhotos": "Übersicht über den Installationsort, einschließlich des Weges dorthin",
  "installationHandover.hardware.indoorInstallation.largestAllInOneSizeThatFits": "Größte All-in-One-Einheit, die passt",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits": "Größe des Warmwasserspeichers",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits150L": "150l (955x595)",
  "installationHandover.hardware.indoorInstallation.largestCylinderSizeThatFits200L": "200l (1265x595)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsLarge": "Groß (1880x600x600)",
  "installationHandover.hardware.indoorInstallation.largestSizeThatFitsSmall": "Klein (1100x500x500)",
  "installationHandover.hardware.indoorInstallation.minimumPathHeight": "Min. Bereich in m Breite ",
  "installationHandover.hardware.indoorInstallation.minimumPathWidth": "Min. Bereich in m Höhe ",
  "installationHandover.hardware.indoorInstallation.pipeLayoutDrawn": "",
  "installationHandover.hardware.indoorInstallation.plasticDuctOnPipesNeeded": "Möchte der Kunde eine Plastikabdeckung für Rohre die von der Inneneinheit in die Wand führen?",
  "installationHandover.hardware.indoorInstallation.primaryRunsUnderneathFloor": "Werden wir Leitungen unter bestehendem Boden verlegen müssen?",
  "installationHandover.hardware.indoorInstallation.wifiReachesInstallationLocation": "Hast du sichergestellt, dass das W-Lan des Kunden bis zum Installationsort reicht?",
  "installationHandover.hardware.outdoorInstallation.comments": "Kommentare",
  "installationHandover.hardware.outdoorInstallation.condensationPipedToDrain": "Ablaufrohr",
  "installationHandover.hardware.outdoorInstallation.condensationSoakAway": "Sickergrube",
  "installationHandover.hardware.outdoorInstallation.condensationSolution": "Wie gehen wir mit Kondenswasser um?",
  "installationHandover.hardware.outdoorInstallation.distance": "Distanz?",
  "installationHandover.hardware.outdoorInstallation.distanceFromOutdoorUnitToWall": "Distanz von Außeneinheit zur Hauswand",
  "installationHandover.hardware.outdoorInstallation.distanceOfTrunkingRequired": "Länge der Leitungen notwendig [m]",
  "installationHandover.hardware.outdoorInstallation.extraResourcesNeeded": "Sind weitere Ressourcen für die Aufstellung der Außeneinheit notwendig (über Team von 2 Installern hinaus)",
  "installationHandover.hardware.outdoorInstallation.holePlacement": "Position der Rohröffnung für Leitung",
  "installationHandover.hardware.outdoorInstallation.holePlacementAboveGround": "Oberirdisch",
  "installationHandover.hardware.outdoorInstallation.holePlacementBelowGround": "Unterirdisch",
  "installationHandover.hardware.outdoorInstallation.holePlacementHighAboveGround": "Oberirdisch höher gelegen (Leiter notwendig)",
  "installationHandover.hardware.outdoorInstallation.isGroundworkRequired": "Ist Bodenarbeit notwendig? Z.b. falls Boden nicht eben ist. ",
  "installationHandover.hardware.outdoorInstallation.isOutdoorPipingBelowGroundNeeded": "Unterirdische Rohrleitung?",
  "installationHandover.hardware.outdoorInstallation.isOutdoorTrunkingRequired": "Außenkanäle notwendig?",
  "installationHandover.hardware.outdoorInstallation.minimumDistanceToClosestOpening": "Kuerzeste Distanz von nähesten Fenster, Tür, Steckdose und Ventilation",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnGround": "Boden",
  "installationHandover.hardware.outdoorInstallation.mountOutdoorUnitOnWall": "Wandhalterung",
  "installationHandover.hardware.outdoorInstallation.obstacleElectricalSocket": "Elektroanschluss",
  "installationHandover.hardware.outdoorInstallation.obstacleOutsideTab": "Gartenwasserhahn",
  "installationHandover.hardware.outdoorInstallation.obstacles": "Welche Hindernisse",
  "installationHandover.hardware.outdoorInstallation.obstacleSoilPipe": "Abflussrohr",
  "installationHandover.hardware.outdoorInstallation.obstaclesToRemove": "Gibt es Hindernisse, die für die Installation der Außeneinheit beseitigt werden müssen?",
  "installationHandover.hardware.outdoorInstallation.otherCondensationSolution": "Andere lösung",
  "installationHandover.hardware.outdoorInstallation.outdoorPrimaryPipingRoutePhotos": "Photos der Rohrleitungs-Route (Tipp: Zeichne Linien in das Foto ein) ",
  "installationHandover.hardware.outdoorInstallation.outdoorUnitMounting": "Anbringung der Außeneinheit",
  "installationHandover.hardware.outdoorInstallation.proposedInstallationLocationPhotos": "Fotos von den Aufstellbedigungen und der Lage (links, rechts, von vorn)",
  "installationHandover.hardware.outdoorInstallation.shinglesRequired": "Sind Schindeln notwendig? (Nicht notwendig, wenn existierender Boden porös ist)",
  "installationHandover.hardware.outdoorInstallation.trenchDigger": "Wer wird den Graben ausheben?",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerAira": "Aira",
  "installationHandover.hardware.outdoorInstallation.trenchDiggerCustomer": "Kunde",
  "installationHandover.hardware.outdoorInstallation.trunkingColour": "Farbe der Leitungen",
  "installationHandover.hardware.outdoorInstallation.trunkingColourBlack": "Schwarz",
  "installationHandover.hardware.outdoorInstallation.trunkingColourWhite": "Weiß",
  "installationHandover.hardware.outdoorInstallation.wallMaterial": "Wandmaterial der Rohrleitung",
  "installationHandover.hardware.outdoorInstallation.wallMaterialBrick": "Ziegelmauer",
  "installationHandover.hardware.outdoorInstallation.wallMaterialConcrete": "Betonmauer",
  "installationHandover.hardware.outdoorInstallation.wallMaterialHardStone": "harter Stein (z.B. Granit)",
  "installationHandover.hardware.outdoorInstallation.wallMaterialNaturalStone": "Natursteinmauer",
  "installationHandover.hardware.outdoorInstallation.wallMaterialPerforatedBrick": "Lochstein",
  "installationHandover.hardware.outdoorInstallation.wallMaterialReinforcedConcrete": "Verstärkter Beton",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSoftStone": "weicher Stein (z.B. Kalkstein)",
  "installationHandover.hardware.outdoorInstallation.wallMaterialSolidBrick": "Stein",
  "installationHandover.hardware.outdoorInstallation.wallMaterialWood": "Holz",
  "installationHandover.hardware.outdoorInstallation.wallThickness": "Wandstärke der Rohrleitung",
  "installationHandover.hardware.outdoorInstallation.yesObstaclesToRemove": "Falls ja",
  "installationHandover.hardware.outdoorInstallation.yesOutdoorPipingBelowGroundNeeded": "Falls ja",
  "installationHandover.hardware.title.electrical": "Elektrisch",
  "installationHandover.hardware.title.existingHeatSource": "Vorhandene Wärmequelle",
  "installationHandover.hardware.title.indoorInstallation": "Innenraum Aufstellungsort",
  "installationHandover.hardware.title.outdoorInstallation": "Position der Outdoor-Unit",
  "installationHandover.heatPumpConfigAvailable": "Es gibt verfügbare Paramater für die Konfiguration der Wärmepumpe dieser Anlage.",
  "installationHandover.noInstallation": "Keine Installation",
  "installationHandover.objectDetailsPanel.buttons.info": "Information",
  "installationHandover.objectDetailsPanel.buttons.photos": "Bilder",
  "installationHandover.objectDetailsPanel.radiator": "Heizkörper",
  "installationHandover.pasteLink": "Paste link",
  "installationHandover.projectOveriew.title.mainTitle": "Projektübersicht",
  "installationHandover.projectOveriew.title.product": "Produkt",
  "installationHandover.projectOveriew.title.quantity": "Anzahl",
  "installationHandover.projectOverview.people": "Beteiligte Mitarbeiter",
  "installationHandover.radiator": "Heizkörper",
  "installationHandover.radiatorInfoPanel.comment": "Anmerkung:",
  "installationHandover.radiatorInfoPanel.deltaT": "DeltaT:",
  "installationHandover.radiatorInfoPanel.electricRadiator": "Elektrischer Heizkörper",
  "installationHandover.radiatorInfoPanel.enabled": "In betrieb?",
  "installationHandover.radiatorInfoPanel.height": "Höhe:",
  "installationHandover.radiatorInfoPanel.inRoom": "Im Raum:",
  "installationHandover.radiatorInfoPanel.outputWatts": "Leistung [W]:",
  "installationHandover.radiatorInfoPanel.radiatorType": "Heizkörpertyp:",
  "installationHandover.radiatorInfoPanel.toBeInstalled": "Muss installiert werden?",
  "installationHandover.radiatorInfoPanel.waterRadiator": "Wasserheizkörper",
  "installationHandover.radiatorInfoPanel.width": "Breite:",
  "installationHandover.radiators.existingRadiators": "Befintliche Heizkörper",
  "installationHandover.radiators.newRadiators": "Neue Heizkörper, die installiert werden müssen",
  "installationHandover.radiators.noExisitingRadiatorsInRoom": "Es gibt keine Heizkörper in diesem Raum",
  "installationHandover.radiators.noNewRadiatorsInRoom": "Es gibt keine neue Heizkörper für diesen Raum",
  "installationHandover.radiators.noRadiatorsInRoom": "Es gibt keine Heizkörper in diesem Raum",
  "installationHandover.radiators.noRadiatorsToRemoveInRoom": "Es gibt keine Heizkörper, die aus diesem Raum entfernt werden müssen",
  "installationHandover.radiators.removedRadiators": "Enfernte Heizkörper",
  "installationHandover.roomInfoPanel.existingRadiators": "Befintliche Heizkörper",
  "installationHandover.sharePointUrlError": "⛔ Something went wrong check the url and try again.",
  "installationHandover.sharePointUrlSuccess": "✅ Sharepoint url updated successfully.",
  "installationHandover.title": "Installationsübergabe",
  "installationHandover.titles.mainTitle": "Installationsübergabe",
  "installationPlanning.addResourcesToJob.availableResources": "",
  "installationPlanning.addResourcesToJob.currentResources": "",
  "installationPlanning.addResourcesToJob.noAvailableResources": "",
  "installationPlanning.addResourcesToJob.noResources": "",
  "installationPlanning.addResourcesToJob.title": "",
  "installationPlanning.addResourcesToTeam.availableResources": "",
  "installationPlanning.addResourcesToTeam.defaultResources": "",
  "installationPlanning.addResourcesToTeam.noAvailableResources": "",
  "installationPlanning.addResourcesToTeam.noResources": "",
  "installationPlanning.assignResourceToSegment.conflictingSegments": "",
  "installationPlanning.assignResourceToSegment.conflictingServiceVisits": "",
  "installationPlanning.assignResourceToSegment.unavailable": "",
  "installationPlanning.changeLog.title": "",
  "installationPlanning.dateRangePicker.endDate": "",
  "installationPlanning.dateRangePicker.selectDateRange": "",
  "installationPlanning.dateRangePicker.startDate": "",
  "installationPlanning.dispatchJobs.description": "",
  "installationPlanning.dispatchJobs.dispatchAll": "",
  "installationPlanning.dispatchJobs.dispatchSelected": "",
  "installationPlanning.dispatchJobs.failedToDispatchJobs": "",
  "installationPlanning.dispatchJobs.successfullyDispatchedJobs": "",
  "installationPlanning.dispatchJobs.title": "",
  "installationPlanning.highlightFlexibleProjects": "",
  "installationPlanning.highlightProjects": "",
  "installationPlanning.highlightProjectsWithin": "",
  "installationPlanning.incompleteHours.description": "",
  "installationPlanning.incompleteHours.errors.failedToAddHours": "",
  "installationPlanning.incompleteHours.save": "",
  "installationPlanning.incompleteHours.success": "",
  "installationPlanning.incompleteHours.title": "",
  "installationPlanning.inSalesRecoveryProjects": "",
  "installationPlanning.jobBar.segmentOverviewModal.baselineManHours": "",
  "installationPlanning.jobBar.segmentOverviewModal.expectedTravelTime": "",
  "installationPlanning.jobBar.segmentOverviewModal.extraTimeNeeded": "",
  "installationPlanning.jobBar.segmentOverviewModal.scheduledManHours": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_FINANCE_NOT_COMPLETED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_ILLNESS": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_RESCHEDULED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_CUSTOMER_SCOPE_CHANGED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_HEAT_DESIGN_NOT_COMPLETED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_INSTALMENT_CAPACITY": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_DISTRIBUTION_NETWORK_OPERATOR": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_PLANNING_APPLICATION": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PERMISSIONS_SUBSIDY": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_MATERIAL_UNAVAILABLE": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_PRODUCT_SUPPLY_RUSH_ORDER": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_TECHNICAL_SURVEY_NOT_COMPLETED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_CUSTOMER_TRADES": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_DISTRIBUTION_NETWORK_OPERATOR": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_THIRD_PARTY_SUBCONTRACTOR": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNCATEGORIZED": "",
  "installationPlanning.jobRescheduledReason.REASON_CATEGORY_UNSPECIFIED": "",
  "installationPlanning.jobRescheduledReasonGroup.Customer": "",
  "installationPlanning.jobRescheduledReasonGroup.Heat Design": "",
  "installationPlanning.jobRescheduledReasonGroup.Installment Capacity": "",
  "installationPlanning.jobRescheduledReasonGroup.Permissions": "",
  "installationPlanning.jobRescheduledReasonGroup.Product Supply": "",
  "installationPlanning.jobRescheduledReasonGroup.Technical Survey": "",
  "installationPlanning.jobRescheduledReasonGroup.Third Party": "",
  "installationPlanning.jobRescheduledReasonGroup.Uncategorized": "",
  "installationPlanning.newBaselinesModal.apply": "",
  "installationPlanning.newBaselinesModal.currentDuration": "",
  "installationPlanning.newBaselinesModal.discard": "",
  "installationPlanning.newBaselinesModal.newDuration": "",
  "installationPlanning.newBaselinesModal.title": "",
  "installationPlanning.noLongerResource": "",
  "installationPlanning.notificationCenter.newBaselinesAvailable": "",
  "installationPlanning.notificationCenter.plannerToolUpdates": "",
  "installationPlanning.notificationCenter.projectsWithUnappliedBaseline": "",
  "installationPlanning.notificationCenter.title": "",
  "installationPlanning.notify.updatedJobTeam": "",
  "installationPlanning.notify.updatedJobTeamError": "",
  "installationPlanning.notify.updateWorkSegmentsError": "",
  "installationPlanning.notify.updateWorkSegmentsSuccess": "",
  "installationPlanning.onHoldProjects": "",
  "installationPlanning.overlappingSegments.explanation": "",
  "installationPlanning.overlappingSegments.title": "",
  "installationPlanning.projectSidebar.bufferTank": "",
  "installationPlanning.projectSidebar.heatPumpIndoorUnit": "",
  "installationPlanning.projectSidebar.heatPumpOutdoorUnit": "",
  "installationPlanning.projectSidebar.installationPackageSize": "",
  "installationPlanning.projectSidebar.manufacturer": "",
  "installationPlanning.projectSidebar.openTicketsInHubspot": "",
  "installationPlanning.projectSidebar.radiators": "",
  "installationPlanning.projectSidebar.solution": "",
  "installationPlanning.removeResourceUnavailability.button.cancel": "",
  "installationPlanning.removeResourceUnavailability.button.confirm": "",
  "installationPlanning.removeResourceUnavailability.errors": "",
  "installationPlanning.removeResourceUnavailability.success": "",
  "installationPlanning.removeResourceUnavailability.title": "",
  "installationPlanning.saveJobChangesToChangeAssignees": "",
  "installationPlanning.showFilters": "",
  "installationPlanning.stage.installation": "",
  "installationPlanning.stage.new": "",
  "installationPlanning.stage.postInstallation": "",
  "installationPlanning.stage.preInstallation": "",
  "installationPlanning.stage.technicalDesign": "",
  "installationPlanning.stage.technicalSurvey": "",
  "installationPlanning.suggestionsMap.showProjectsWithNoDate": "",
  "installationPlanning.team.clickToManageResources": "",
  "installationPlanning.topBar.refreshButton.error": "",
  "installationPlanning.topBar.refreshButton.success": "",
  "installationPlanning.topBar.refreshButton.tooltip": "",
  "installationPlanning.unutilization.addAbsence": "",
  "installationPlanning.unutilization.addAbsenceError": "",
  "installationPlanning.unutilization.addAbsenceSuccess": "",
  "installationPlanning.unutilization.endDayDuration": "",
  "installationPlanning.unutilization.firstDayDuration": "",
  "installationPlanning.unutilization.fromTo": "",
  "installationPlanning.unutilization.lentOutRegion": "",
  "installationPlanning.unutilization.otherReasonHelperText": "",
  "installationPlanning.unutilization.reason": "",
  "installationPlanning.unutilization.resourceAlreadyInActive": "",
  "installationPlanning.viewProjectInAerospace": "",
  "installationPlanning.viewProjectInHubspot": "",
  "installationReport.button.sign": "Unterschreiben",
  "installationReport.commissioningDate.label": "Inbetriebnahme abgeschlossen {date}",
  "installationReport.commissioningIncomplete.label": "Inbetriebnahme unvollständig",
  "installationReport.confirmation.journey.financingApplication": "Antrag auf Finanzierung",
  "installationReport.confirmation.journey.header": "Ihre Aira Reise",
  "installationReport.confirmation.journey.homeEnergyAssessment": "Bewertung der Hausenergie",
  "installationReport.confirmation.journey.installation": "Installation",
  "installationReport.confirmation.journey.reviewAndAcceptQuote": "Angebot prüfen und annehmen",
  "installationReport.confirmation.journey.technicalSurveyAndDesign": "Technische Vermessung und Entwurf",
  "installationReport.confirmation.link.installationReport": "Zum Installationsbericht",
  "installationReport.confirmation.nextSteps.accepted.body": "Wir haben eine E-Mail mit einer Kopie des Installationsberichts geschickt.",
  "installationReport.confirmation.nextSteps.accepted.heading": "Ihre nächsten Schritte",
  "installationReport.confirmation.nextSteps.rejected.body": "Wir werden uns in Kürze mit Ihnen in Verbindung setzen, um Ihre Installation abzuschließen.",
  "installationReport.confirmation.nextSteps.rejected.heading": "Wie geht es jetzt weiter?",
  "installationReport.confirmation.title": "Vielen Dank!",
  "installationReport.customer.title": "Kunde",
  "installationReport.customerChecklistIntro.shareScreen.body": "Um dieses Formular auszufüllen, muss der Kunde anwesend sein. Führen Sie ihn durch das Formular und denken Sie daran, dass nur er es endgültig abzeichnen kann.",
  "installationReport.customerChecklistIntro.shareScreen.heading": "Teilen Sie Ihren Bildschirm",
  "installationReport.customerChecklistIntro.title": "Kundeneinführung",
  "installationReport.customerChecklistIntro.wantToKnowMore.body": "Sie können zu den technischen Abschnitten zurückkehren, wenn der Kunde Einzelheiten zu seinem System oder seiner Installation wünscht.",
  "installationReport.customerChecklistIntro.wantToKnowMore.heading": "Möchten Sie mehr erfahren?",
  "installationReport.incomplete.title": "Unvollständiger Installationsbericht",
  "installationReport.instructions.checklists.body": "Eine Reihe technischer Fragen über das System und dessen Installation. Sie können diese selbst beantworten.",
  "installationReport.instructions.checklists.heading": "Installations- und Systemchecklisten",
  "installationReport.instructions.intro": "Füllen Sie diese Checklisten aus, nachdem die Installation der Wärmepumpe abgeschlossen ist. Es gibt zwei Teile:",
  "installationReport.instructions.onboarding.body": "Informieren Sie den Kunden über die Nutzung seiner Wärmepumpe. Für diesen Teil muss der Kunde anwesend sein.",
  "installationReport.instructions.onboarding.heading": "Kunden-Onboarding",
  "installationReport.instructions.title": "Installation abgeschlossen",
  "installationReport.products.bufferTank": "Puffer­speicher",
  "installationReport.products.indoorUnit": "Innen­einheit",
  "installationReport.products.outdoorUnit": "Außen­einheit",
  "installationReport.products.radiators": "Heizkörper",
  "installationReport.products.solutionId": "ESID nummer",
  "installationReport.products.title": "Installierte Produkte",
  "installationReport.question.validation.greaterThan": "Wert muss größer sein als {minValue}",
  "installationReport.question.validation.greaterThanAndLessThan": "Wert muss größer als {minValue} und kleiner als {maxValue} sein",
  "installationReport.question.validation.lessThan": "Wert muss kleiner sein als {maxValue}",
  "installationReport.reportSelector.label": "Bericht vorgelegt",
  "installationReport.signature.customer.title": "Unterschrift des Kunden",
  "installationReport.signature.installer.title": "Unterschrift des Installateurs",
  "installationReport.signatureModal.installationComplete.label": "Mit der Unterzeichnung dieses Dokuments bestätigen Sie, dass Ihre Installation abgeschlossen ist.",
  "installationReport.signatureModal.installationIncomplete.label": "Mit der Unterzeichnung dieses Dokuments bestätigen Sie, dass Sie alle ausstehenden Arbeiten/Materialien gemeldet haben, die für den Abschluss Ihrer Installation erforderlich sind.",
  "installationReport.startPage.installers.title": "Installateure",
  "installationReport.startPage.locked.body": "Für diesen Kunden wurde bereits ein finales Protokoll eingereicht.",
  "installationReport.startPage.locked.title": "Abgeschlossen",
  "installationReport.startPage.start": "Start",
  "installationReport.textInput.placeholder": "Text",
  "installationReport.title": "Aira Wärmepumpen Installations Inbetriebnahme Bericht",
  "installationReview.button.approveActualDuration": "",
  "installationReview.button.cleanEnergyTechnician": "",
  "installationReview.button.deleteAll": "",
  "installationReview.button.deleteJob": "",
  "installationReview.button.electrician": "",
  "installationReview.button.landscaper": "",
  "installationReview.button.unschedule": "",
  "installationReview.columnHeaders.actualEnd": "",
  "installationReview.columnHeaders.actualManHours": "",
  "installationReview.columnHeaders.actualStart": "",
  "installationReview.columnHeaders.addTravelTime": "",
  "installationReview.columnHeaders.day": "",
  "installationReview.columnHeaders.duration": "",
  "installationReview.columnHeaders.expectedDuration": "",
  "installationReview.columnHeaders.expectedManHours": "",
  "installationReview.columnHeaders.expectedStart": "",
  "installationReview.columnHeaders.resources": "",
  "installationReview.columnHeaders.scheduledEnd": "",
  "installationReview.columnHeaders.scheduledManHours": "",
  "installationReview.columnHeaders.scheduledStart": "",
  "installationReview.columnHeaders.startTravel": "",
  "installationReview.columnHeaders.status": "",
  "installationReview.errors.failedToDelete": "",
  "installationReview.errors.failedToDeleteJobs": "",
  "installationReview.errors.failedToPublish": "",
  "installationReview.errors.failedToUnschedule": "",
  "installationReview.errors.failedToUnscheduleJobs": "",
  "installationReview.errors.notYetScheduled": "",
  "installationReview.errors.unfinishedJob": "",
  "installationReview.jobSegmentStatus.FINISHED": "",
  "installationReview.jobSegmentStatus.IN_PROGRESS": "",
  "installationReview.jobSegmentStatus.NOT_STARTED": "",
  "installationReview.labels.defaultResourceCount": "",
  "installationReview.labels.linkToSchedulingTool": "",
  "installationReview.labels.reviewedAt": "",
  "installationReview.labels.totalActualDuration": "",
  "installationReview.labels.totalActualManHours": "",
  "installationReview.labels.totalExpectedDuration": "",
  "installationReview.labels.totalExpectedManHours": "",
  "installationReview.modals.addJobHours.button.cancel": "",
  "installationReview.modals.addJobHours.button.confirm": "",
  "installationReview.modals.addJobHours.helperText": "",
  "installationReview.modals.addJobHours.title": "",
  "installationReview.modals.confirmDeletion.button.cancel": "",
  "installationReview.modals.confirmDeletion.button.delete": "",
  "installationReview.modals.confirmDeletion.label.reasonCategory": "",
  "installationReview.modals.confirmDeletion.label.reasonDescription": "",
  "installationReview.modals.confirmDeletion.title": "",
  "installationReview.modals.confirmDeletionAll.title": "",
  "installationReview.modals.confirmUnschedule.button.cancel": "",
  "installationReview.modals.confirmUnschedule.button.unschedule": "",
  "installationReview.modals.confirmUnschedule.label.reasonCategory": "",
  "installationReview.modals.confirmUnschedule.label.reasonDescription": "",
  "installationReview.modals.confirmUnschedule.title": "",
  "installationReview.modals.removeJobHours.button.cancel": "",
  "installationReview.modals.removeJobHours.button.remove": "",
  "installationReview.modals.removeJobHours.title": "",
  "installationReview.notify.addHoursSuccess": "",
  "installationReview.notify.deletionSuccess": "",
  "installationReview.notify.removeHoursSuccess": "",
  "installationReview.notify.unscheduleSuccess": "",
  "installationReview.role.ELECTRICIAN": "",
  "installationReview.role.INSTALLER": "",
  "installationReview.role.LANDSCAPER": "",
  "installationReview.role.UNKNOWN": "",
  "installationReview.tableContent.noResources": "",
  "installationReview.tableContent.resourceCount": "",
  "installationReview.title": "",
  "installationReview.values.duration": "",
  "installationReview.values.expectedManHoursPerResource": "",
  "installationReview.values.totalExpectedDuration": "",
  "invoice.finalInvoice": "",
  "invoice.loading.error": "",
  "invoice.pastDue": "",
  "invoice.prepayment": "",
  "invoice.total.remainingToPay": "",
  "invoice.total.value": "",
  "invoiceRefund.amount": "",
  "invoiceRefund.amountSummary": "",
  "invoiceRefund.confirm": "",
  "invoiceRefund.description": "",
  "invoiceRefund.error.amountRequired": "",
  "invoiceRefund.error.exceedsTotal": "",
  "invoiceRefund.error.invalidAmount": "",
  "invoiceRefund.error.reasonRequired": "",
  "invoiceRefund.fullRefund": "",
  "invoiceRefund.partialRefund": "",
  "invoiceRefund.processing": "",
  "invoiceRefund.reason": "",
  "invoiceRefund.title": "",
  "invoiceRefund.totalAmount": "",
  "invoiceRefund.warning": "",
  "invoiceSatus.draft": "",
  "invoiceSatus.open": "",
  "invoiceSatus.uncollectable": "",
  "invoicesTable.amountHeader": "",
  "invoicesTable.dateCreatedHeader": "",
  "invoicesTable.descriptionHeader": "",
  "invoicesTable.dueDateHeader": "",
  "invoicesTable.emptyState": "",
  "invoicesTable.invoiceNumberHeader": "",
  "invoicesTable.noPaymentReceived": "",
  "invoicesTable.paymentId": "",
  "invoicesTable.paymentReceived": "",
  "invoicesTable.paymentSummary": "",
  "invoicesTable.previewInvoice": "",
  "invoicesTable.refundButton": "",
  "invoicesTable.statusHeader": "",
  "invoicesTable.title": "",
  "invoiceStatus.cancelled": "",
  "invoiceStatus.creditNote": "",
  "invoiceStatus.draft": "",
  "invoiceStatus.Error": "",
  "invoiceStatus.error": "",
  "invoiceStatus.open": "",
  "invoiceStatus.paid": "",
  "invoiceStatus.partiallyPaid": "",
  "invoiceStatus.partiallyRefunded": "",
  "invoiceStatus.pastDue": "",
  "invoiceStatus.refunded": "",
  "invoiceStatus.uncollectable": "",
  "invoiceStatus.unknown": "",
  "invoicing.createInvoice": "",
  "invoicing.erp.customerId": "",
  "invoicing.erp.projectId": "",
  "invoicing.error.fetchingInvoices": "",
  "invoicing.title": "",
  "localized.string": "",
  "numberInputHelperText.canBeDecimal": "",
  "numberInputHelperText.canBeInteger": "",
  "numberInputHelperText.canBeNegative": "",
  "numberInputHelperText.canBeNegativeAndDecimal": "",
  "ongoingInstallations.actualStartDate": "",
  "ongoingInstallations.commissioningDate": "",
  "ongoingInstallations.completed": "",
  "ongoingInstallations.customers": "",
  "ongoingInstallations.daysUntilInstallation": "",
  "ongoingInstallations.filters.country": "",
  "ongoingInstallations.filters.from": "",
  "ongoingInstallations.filters.region": "",
  "ongoingInstallations.filters.to": "",
  "ongoingInstallations.hoursForCompletion": "",
  "ongoingInstallations.installationComplete": "",
  "ongoingInstallations.installationCompleteDate": "",
  "ongoingInstallations.installationIncomplete": "",
  "ongoingInstallations.installationIncompleteDate": "",
  "ongoingInstallations.jobsOverview": "",
  "ongoingInstallations.link": "",
  "ongoingInstallations.noStartDate": "",
  "ongoingInstallations.plannedStartDate": "",
  "ongoingInstallations.projectStage": "",
  "ongoingInstallations.regions": "",
  "ongoingInstallations.reworkCompletionHours": "",
  "ongoingInstallations.status": "",
  "ongoingInstallations.status.completed": "",
  "ongoingInstallations.status.installation": "",
  "ongoingInstallations.status.invoice": "",
  "ongoingInstallations.status.postInstallation": "",
  "ongoingInstallations.status.preInstallation": "",
  "ongoingInstallations.status.unknown": "",
  "ongoingInstallations.teamLead": "",
  "ongoingInstallations.title": "",
  "outdoorUnit.add.cascading.button": "",
  "outdoorUnit.add.cascading.description": "",
  "procurement.actions.bomDeprecated": "",
  "procurement.actions.bomNotReadyForProcurement": "",
  "procurement.actions.designReviewNotAccepted": "",
  "procurement.actions.errorPrefix": "",
  "procurement.actions.installationDateRequired": "",
  "procurement.actions.installationKitRequired": "",
  "procurement.actions.markedAsOrdered": "",
  "procurement.actions.markingAsOrdered": "",
  "procurement.actions.markOrderFailed": "",
  "procurement.actions.ordered": "",
  "procurement.actions.orderedSuccess": "",
  "procurement.actions.orderFailed": "",
  "procurement.actions.orderFailedToast": "",
  "procurement.actions.ordering": "",
  "procurement.actions.preliminaryDateConfirmDescription": "",
  "procurement.actions.preliminaryDateConfirmTitle": "",
  "procurement.actions.procurementCommentMissing": "",
  "procurement.actions.reorderConfirmDescription": "",
  "procurement.actions.reorderConfirmTitle": "",
  "procurement.actions.saveAndOrder": "",
  "procurement.actions.saved": "",
  "procurement.actions.saveFailed": "",
  "procurement.actions.saveFailedToast": "",
  "procurement.actions.saving": "",
  "procurement.actions.solutionNotReadyForOrder": "",
  "procurement.actions.vanStockBundleRequired": "",
  "procurement.comment.label": "",
  "procurement.comment.placeholder": "",
  "procurement.description": "",
  "procurement.label.installationDate": "",
  "procurement.label.selectInstallationKitVersion": "",
  "procurement.label.selectInstallationKitVersion.tooltip": "",
  "procurement.label.selectVanStockBundles": "",
  "procurement.label.selectVanStockBundles.tooltip": "",
  "procurement.label.selectVanStockBundlesPlaceholder": "",
  "procurement.status.ordered": "",
  "procurement.title": "",
  "procurement.warning.bomBundleOutdated": "",
  "procurement.warning.installationDatePassed": "",
  "product-price-adjustment.adjust-price": "",
  "product-price-adjustment.excluding-vat": "",
  "product-price-adjustment.handled-products": "",
  "product-price-adjustment.handled-products-note": "",
  "product-price-adjustment.initial-quote": "",
  "product-price-adjustment.note": "",
  "product-price-adjustment.products-with-price-increase": "",
  "product-price-adjustment.products-with-price-increase-note": "",
  "product-price-adjustment.products-with-price-reduction": "",
  "product-price-adjustment.products-with-price-reduction-note": "",
  "product-price-adjustment.reverted-products": "",
  "product-price-adjustment.reverted-products-note": "",
  "product-price-adjustment.revised-products": "",
  "product-price-adjustment.subtitle": "",
  "product-price-adjustment.title": "",
  "product-price-adjustment.updated-price": "",
  "productSummary.comparison.added": "",
  "productSummary.comparison.displayOriginalOffer": "",
  "productSummary.comparison.hideOriginalOffer": "",
  "productSummary.comparison.removed": "",
  "productSummary.comparison.unchanged": "",
  "productSummary.comparison.updated": "",
  "productSummary.discountsTable.discountExVat": "",
  "productSummary.discountsTable.discountIncVat": "",
  "productSummary.discountsTable.discounts": "",
  "productSummary.discountsTable.vatPercentage": "",
  "productSummary.invoice.alreadySent": "",
  "productSummary.invoice.error.unknown": "",
  "productSummary.invoice.final.label": "",
  "productSummary.invoice.final.send": "",
  "productSummary.invoice.final.send.popup.infoCard.title": "",
  "productSummary.invoice.final.send.popup.readyTitle": "",
  "productSummary.invoice.invoice": "",
  "productSummary.invoice.paid": "",
  "productSummary.invoice.partial.label": "",
  "productSummary.invoice.partial.notPaid": "",
  "productSummary.invoice.partial.send": "",
  "productSummary.invoice.send": "",
  "productSummary.invoice.send.loading": "",
  "productSummary.invoice.send.popup.description": "",
  "productSummary.invoice.send.popup.infoCard.text": "",
  "productSummary.invoice.send.popup.infoCard.title": "",
  "productSummary.invoice.send.popup.readyTitle": "",
  "productSummary.invoice.send.popup.title": "",
  "productSummary.invoice.send.tooltip": "",
  "productSummary.invoice.sent": "",
  "productSummary.productsTable.finalOrderSummaryInformation": "",
  "productSummary.productsTable.itemNumber": "",
  "productSummary.productsTable.offerSummaryInformation": "",
  "productSummary.productsTable.orderSummaryInformation": "",
  "productSummary.productsTable.PAYMENT_TYPE_INSTALMENTS": "",
  "productSummary.productsTable.PAYMENT_TYPE_INVOICE": "",
  "productSummary.productsTable.paymentMethod": "",
  "productSummary.productsTable.priceExVat": "",
  "productSummary.productsTable.priceIncVat": "",
  "productSummary.productsTable.quantity": "",
  "productSummary.productsTable.region": "",
  "productSummary.productsTable.vatPercentage": "",
  "productSummary.subsidyTable.subsidy": "",
  "productSummary.subsidyTable.subsidyExVat": "",
  "productSummary.subsidyTable.subsidyIncVat": "",
  "productSummary.subsidyTable.ukSubsidyTitle": "",
  "productSummary.subsidyTable.vatPercentage": "",
  "productSummary.totalPriceTable.effectiveInterestRate": "",
  "productSummary.totalPriceTable.interestRate": "",
  "productSummary.totalPriceTable.monthlyCost": "",
  "productSummary.totalPriceTable.totalPrice": "",
  "productSummary.totalPriceTable.totalPriceExVat": "",
  "productSummary.totalPriceTable.totalPriceIncVat": "",
  "quotation.action.AWAITING_DESIGN": "",
  "quotation.action.COMPLETE_DESIGN": "",
  "quotation.action.COMPLETE_SURVEY": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_DOCUMENTATION_AND_APPLICATIONS": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.action.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.action.error.aborted": "",
  "quotation.action.error.cancelled": "",
  "quotation.action.error.dataLoss": "",
  "quotation.action.error.deadlineExceeded": "",
  "quotation.action.error.failedPrecondition": "",
  "quotation.action.error.internal": "",
  "quotation.action.error.invalidArgument": "",
  "quotation.action.error.notFound": "",
  "quotation.action.error.outOfRange": "",
  "quotation.action.error.permissionDenied": "",
  "quotation.action.error.resourceExhausted": "",
  "quotation.action.error.unauthenticated": "",
  "quotation.action.error.unavailable": "",
  "quotation.action.error.unimplemented": "",
  "quotation.action.error.unknownError": "",
  "quotation.action.INSTALLED": "",
  "quotation.action.lockingNotAllowed": "",
  "quotation.action.markAsOrderedNotAllowed": "",
  "quotation.action.REQUEST_SURVEY": "",
  "quotation.button.add": "",
  "quotation.button.resetProducts": "",
  "quotation.button.update": "",
  "quotation.button.viewGuide": "",
  "quotation.errorCard.checkDiscount.text": "",
  "quotation.errorCard.checkDiscount.title": "",
  "quotation.errorCard.paymentType": "",
  "quotation.errorCard.paymentTypeDescription": "",
  "quotation.label.action": "",
  "quotation.label.amountIncVat": "",
  "quotation.label.battery": "",
  "quotation.label.bufferTank": "",
  "quotation.label.compatibilityGroup": "",
  "quotation.label.defaultTaxRateDescription": "",
  "quotation.label.digging": "Grabungen (Gala)",
  "quotation.label.disableFinancing": "",
  "quotation.label.displayPricing": "",
  "quotation.label.displayTaxRateOverride": "",
  "quotation.label.editDefaultTaxRateDescription": "",
  "quotation.label.editOverrideTaxRateDescription": "",
  "quotation.label.electricalBonding": "",
  "quotation.label.enableFinancing": "",
  "quotation.label.evCharger": "",
  "quotation.label.financingTerm": "",
  "quotation.label.heatingCircuits": "",
  "quotation.label.heatingRoom": "Heizraum verlegen",
  "quotation.label.heatingSystem": "",
  "quotation.label.heatPump": "",
  "quotation.label.heatPumpIndoor": "",
  "quotation.label.hidePricing": "",
  "quotation.label.hideTaxRateOverride": "",
  "quotation.label.hotWaterExpansionVessel": "",
  "quotation.label.indoorUnitOutdoorUnitDistance": "",
  "quotation.label.installationKit": "",
  "quotation.label.installationPackage": "",
  "quotation.label.isolatorFitting": "",
  "quotation.label.joinery": "",
  "quotation.label.liftingEquipment": "",
  "quotation.label.manifold": "Erneuerung Fußbodenheizkreisverteiler",
  "quotation.label.mcs-estimate": "",
  "quotation.label.meterBoxChange": "Erneuerung Zählerschrank",
  "quotation.label.oilTankRemoval": "Entsorgung Öltank",
  "quotation.label.oilTransport": "Öltransport",
  "quotation.label.oilWallRemoval": "Rückbau Ölmauer",
  "quotation.label.outdoorMounting": "",
  "quotation.label.outdoorUnitPipeConnection": "",
  "quotation.label.outdoorUnitWall": "Schutzwand für freistehende Außeneinheit",
  "quotation.label.paymentMethod": "",
  "quotation.label.piping": "",
  "quotation.label.predefinedDiscounts": "",
  "quotation.label.qty": "",
  "quotation.label.quantity": "",
  "quotation.label.radiatorControl": "Digitale Heizkörpersteuerung (Einrohrsystem)",
  "quotation.label.rePiping": "",
  "quotation.label.scaffolding": "",
  "quotation.label.solar": "",
  "quotation.label.status": "",
  "quotation.label.stoneWallDrilling": "",
  "quotation.label.systemDesign": "",
  "quotation.label.systemFlush": "",
  "quotation.label.taxRateOverride": "",
  "quotation.label.technicalSurvey": "",
  "quotation.label.temperatureZones": "",
  "quotation.label.totalCost": "",
  "quotation.label.totalCostExVat": "",
  "quotation.label.underfloorHeatingCommission": "",
  "quotation.label.uploadMCS": "",
  "quotation.linkToFinancialPortal": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_APPROVE_DESIGN": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_CANCEL": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_REOPEN": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE": "",
  "quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS": "",
  "quotation.notify.performingAction": "",
  "quotation.notify.sendingQuote": "",
  "quotation.notify.unsavedChanges": "",
  "quotation.popup.cancel.confirm": "",
  "quotation.popup.cancel.content": "",
  "quotation.popup.financing.confirm": "",
  "quotation.popup.financing.content": "",
  "quotation.popup.financing.title": "",
  "quotation.popup.resendQuote.confirm": "",
  "quotation.popup.resendQuote.content": "",
  "quotation.popup.resetProducts.confirm": "",
  "quotation.popup.resetProducts.content": "",
  "quotation.popup.saveBeforeAction.confirm": "",
  "quotation.popup.saveBeforeAction.content": "",
  "quotation.popup.unlockPrice.confirm": "",
  "quotation.popup.unlockPrice.content": "",
  "quotation.popup.unlockPrice.title": "",
  "quotation.progress.designAcceptedAt": "",
  "quotation.progress.documentationAndApplicationsCompletedAt": "",
  "quotation.progress.financingSecuredAt": "",
  "quotation.progress.priceLockedAt": "",
  "quotation.progress.productsLockedAt": "",
  "quotation.progress.quoteAcceptedAt": "",
  "quotation.progress.quoteSentAt": "",
  "quotation.progress.techFeePaidAt": "",
  "quotation.progress.technicalDataCollectionCompletedAt": "",
  "quotation.status.CANCELLED": "",
  "quotation.status.cancelled": "",
  "quotation.status.expired": "",
  "quotation.status.installed": "",
  "quotation.status.missing": "",
  "quotation.status.OPEN_FOR_MODIFICATION": "",
  "quotation.status.openForModification": "",
  "quotation.status.ORDERED": "",
  "quotation.status.ordered": "",
  "quotation.status.PRICE_LOCKED": "",
  "quotation.status.priceLocked": "",
  "quotation.status.PRODUCTS_LOCKED": "",
  "quotation.status.productsLocked": "",
  "quotation.status.quoteExpirationAddition": "",
  "quotation.status.READY_FOR_ORDER": "",
  "quotation.status.readyForOrder": "",
  "quotation.table.description": "",
  "quotation.table.price": "",
  "quotation.table.quantity": "",
  "quotation.title.addons": "",
  "quotation.title.batteryPackage": "",
  "quotation.title.discounts": "",
  "quotation.title.evChargerPackage": "",
  "quotation.title.heatPumpPackage": "",
  "quotation.title.installationAddons": "",
  "quotation.title.insulation": "",
  "quotation.title.miscellaneous": "",
  "quotation.title.packages": "",
  "quotation.title.progress": "",
  "quotation.title.quotation": "",
  "quotation.title.radiators": "",
  "quotation.title.region": "",
  "quotation.title.solarPackage": "",
  "quotation.title.solarPanels": "",
  "quotation.title.subsidy": "",
  "quotation.title.subsidyAndPromotions": "",
  "quotation.title.taxRate": "",
  "quotation.title.total": "",
  "quotation.warning.goToHeatDesign": "",
  "quotation.warning.unlockToChange": "",
  "quotations.paymentMethod.INSTALMENTS": "Ratenkauf",
  "quotations.paymentMethod.INVOICE": "Vollzahlung",
  "quotations.paymentMethod.popup.p": "",
  "quotations.paymentMethod.SPLIT_INVOICE": "2-Raten",
  "quotations.paymentMethod.UNDECIDED": "",
  "region.button.installation": "",
  "region.button.resources": "",
  "region.button.salesSurvey": "",
  "serviceBookingTool.body.resourceName": "",
  "serviceBookingTool.button.bookMaintenance": "",
  "serviceBookingTool.button.bookServiceJob": "",
  "serviceBookingTool.button.schedule.maintenance": "",
  "serviceBookingTool.emptyState.noInstallation": "",
  "serviceBookingTool.error.cannotBookServiceJob": "",
  "serviceBookingTool.error.maintenanceSchedulingFailure": "",
  "serviceBookingTool.error.partialAddress": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_CANCELLED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_FINISHED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_IN_PROGRESS": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_NOT_SCHEDULED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_READY": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_SCHEDULED": "",
  "serviceBookingTool.jobStatus.JOB_STATUS_UNSPECIFIED": "",
  "serviceBookingTool.jobStatus.UNRECOGNIZED": "",
  "serviceBookingTool.jobType.installThermostat": "",
  "serviceBookingTool.jobType.JOB_TYPE_ELECTRICAL_MAINTENANCE": "",
  "serviceBookingTool.jobType.JOB_TYPE_HEAT_PUMP_MAINTENANCE": "",
  "serviceBookingTool.jobType.JOB_TYPE_THERMOSTAT_MAINTENANCE": "",
  "serviceBookingTool.jobType.serviceElectricalSystem": "",
  "serviceBookingTool.jobType.serviceHeatPump": "",
  "serviceBookingTool.jobType.serviceJobType": "",
  "serviceBookingTool.label.duration": "",
  "serviceBookingTool.no.maintenance.scheduled": "",
  "serviceBookingTool.popup.unscheduleDescription": "",
  "serviceBookingTool.popup.unscheduleHeader": "",
  "serviceBookingTool.popup.unscheduleReason.customerCancelled": "",
  "serviceBookingTool.popup.unscheduleReason.customerNoShow": "",
  "serviceBookingTool.popup.unscheduleReason.description": "",
  "serviceBookingTool.popup.unscheduleReason.noLongerNeeded": "",
  "serviceBookingTool.popup.unscheduleReason.uncategorized": "",
  "serviceBookingTool.title.homeService": "",
  "surveysPlanning.suggestionsMap.cleanEnergyExperts": "",
  "surveysPlanning.suggestionsMap.filterByResource": "",
  "surveysPlanning.suggestionsMap.findOnMap": "",
  "surveysPlanning.suggestionsMap.showVisitsWithNoAssignedResources": "",
  "surveysPlanning.suggestionsMap.showVisitsWithNoDate": "",
  "surveysPlanning.suggestionsMap.technicalSurveyors": "",
  "upload.max-file-size": "",
  "upload.mcs.description": "",
  "upload.mcs.link.text": ""
};
