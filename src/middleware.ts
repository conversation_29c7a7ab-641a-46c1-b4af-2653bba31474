import { createTRPCClient, httpBatchLink, loggerLink } from '@trpc/client';
import { env } from 'env.mjs';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { Environments } from 'server/api/environments';
import { AppRouter } from 'server/api/root';
import { Session } from 'server/auth';
import { getLocalePrefix, localeHeuristic } from 'utils/locale';
import { transformer } from 'utils/transformer';

// For now to avoid limiting access for users that should have access, we only deny external installers.
const NON_LEADER_INSTALLER_GROUPS = [{ name: 'GROUP_EXTERNAL_ALL_INSTALLER', value: 19 }];
const INSTALLER_ALLOWED_PATHS_RE: RegExp =
  /\/access-denied|\/solution\/[^/]+\/(installation-handover|customer-acceptance-form|installation-report)/;
const AEROSPACE_PRODUCT_MANAGEMENT_GROUPS = [{ name: 'GROUP_AEROSPACE_PRODUCT_MANAGEMENT', value: 21 }];

export async function middleware(request: NextRequest) {
  try {
    console.log('Middleware started');
    const trpcUrl = 'http://localhost:3000/api/trpc';

    const trpcClient = createTRPCClient<AppRouter>({
      links: [
        loggerLink({
          enabled: (opts) =>
            env.ENVIRONMENT === Environments.LOCAL || (opts.direction === 'down' && opts.result instanceof Error),
        }),
        httpBatchLink({
          url: trpcUrl,
          headers() {
            return {
              'x-amzn-oidc-accesstoken': request.headers.get('x-amzn-oidc-accesstoken') as string,
              'x-amzn-oidc-data': request.headers.get('x-amzn-oidc-data') as string,
              'x-amzn-trace-id': request.headers.get('x-amzn-trace-id') as string,
            };
          },
          transformer,
        }),
      ],
    });

    let currentUser: Session['user'];
    try {
      currentUser = await trpcClient.AiraBackend.whoAmI.query();
      console.log('Current user id:', currentUser?.userId?.value);
    } catch (error) {
      console.error('Error querying tRPC endpoint:', error);
      return NextResponse.error();
    }

    const localeHeuristicResult = localeHeuristic(request, currentUser);
    if (localeHeuristicResult?.type === 'redirect') {
      return NextResponse.redirect(
        new URL(`/${localeHeuristicResult.locale}${request.nextUrl.pathname}${request.nextUrl.search}`, request.url),
      );
    }

    const isInstaller = currentUser?.groupMemberships?.some((group) =>
      NON_LEADER_INSTALLER_GROUPS.some((installerGroup) => installerGroup.value === group.group),
    );
    if (isInstaller && !INSTALLER_ALLOWED_PATHS_RE.test(request.nextUrl.pathname)) {
      console.log('Redirecting to access-denied');
      return NextResponse.redirect(new URL(`${getLocalePrefix(request)}/access-denied`, request.url));
    }

    const canManageAerospaceProducts =
      currentUser?.groupMemberships?.some((group) =>
        AEROSPACE_PRODUCT_MANAGEMENT_GROUPS.some(
          (productManagementGroup) => productManagementGroup.value === group.group,
        ),
      ) || process.env.ALL_AEROSPACE_PAGES === 'true';
    if (!canManageAerospaceProducts && request.nextUrl.pathname.endsWith('/configuration/products')) {
      if (!request.nextUrl.pathname.endsWith('/access-denied')) {
        console.log('Redirecting to access-denied');
        return NextResponse.redirect(new URL(`${getLocalePrefix(request)}/access-denied`, request.url));
      }
    }

    console.log('Middleware completed');

    return NextResponse.next();
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.error();
  }
}

export const config = {
  matcher: ['/', '/((?!api|fonts|_next/static|_next/image).*)'],
};
