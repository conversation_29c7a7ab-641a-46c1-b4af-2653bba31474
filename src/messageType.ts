import { messages } from '../public/locales/en-gb/messages';

// Here, we use global augmentation to restrict the type of our id keys to the keys of our messages object.
// https://www.typescriptlang.org/docs/handbook/declaration-merging.html#global-augmentation

export type MessageKey = keyof typeof messages;

declare global {
  // We need to use namespace here to perform the global augmentation.
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace FormatjsIntl {
    interface Message {
      ids: MessageKey;
    }
  }
}
