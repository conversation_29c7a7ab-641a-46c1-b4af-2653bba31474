import React from 'react';
import { fireEvent, screen } from '@testing-library/react';

import { IntlProvider } from 'react-intl';
import { setupServer } from 'msw/node';
import { vi } from 'vitest';
import {
  chooseInUValueInput,
  filterDifficultRooms,
  getMuiSliderValue,
  goToNextHeatDesignStep,
  mockedSlider,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  setMuiSliderValue,
  trpcMsw,
} from 'tests/utils/testUtils';
import HeatDesign from 'components/heat-design/HeatDesign';
import { userEvent } from '@testing-library/user-event';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { AdjacentKind } from 'components/heat-design/stores/types';
import { mockGetServerEnvironment, mockGetTechnicalSpecifications_empty } from 'tests/utils/mockedTrpcCalls';
import { GetHeatPumpParametersResponse } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.v1';
import untypedAsaHouse from './asa_house_response.json';
import { installationGroundwork, products, solution } from './fixtures/asaTestData';

/**
 * This test file is for general integration tests of the Heat Design tool.
 */

// Test setup and lifecycle
const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
mockRouterForHeatDesign();
const getAsaHouse = (): ProtoHeatDesign => {
  const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
  const filtered: ProtoHeatDesign = filterDifficultRooms(asaRawHouse);
  return {
    ...filtered,
    ...sampleDefaultDwellingUValueDefaults,
  };
};

let currentHeatDesign = getAsaHouse();

const server = setupServer(
  mockGetServerEnvironment(),
  mockGetTechnicalSpecifications_empty(),
  trpcMsw.AiraBackend.getGrpcEnergySolution.query(() => ({ solution })),
  trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => ({ currentSolution: solution, lastQuotedSolution: solution })),
  trpcMsw.AiraBackend.getProducts.query(() => products),
  trpcMsw.AiraBackend.getGroundworkForSolution.query(() => installationGroundwork),
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: currentHeatDesign,
    isLocked: false,
    result: undefined,
    updatedAt: new Date(),
    events: [],
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => []),
  trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() => ({
    parameters: null as unknown as GetHeatPumpParametersResponse,
  })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => ({ radiators: [] })),
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: currentHeatDesign,
    isLocked: false,
    result: undefined,
    updatedAt: new Date(),
    events: [],
  })),
  trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(async (input) => {
    currentHeatDesign = input.input.heatDesign;
    return {};
  }),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [],
  })),
  trpcMsw.InstallationGroundwork.sendHeatPumpParameters.mutation(() => ({})),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators: [] })),
);

// Helpers
vi.mock('@mui/material/Slider', () => ({
  __esModule: true,
  default: (props: any) => mockedSlider(props),
}));

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

async function setupBathroom() {
  // Go into the Bathroom overview
  await userEvent.click(screen.getByText('1st Floor'));
  await userEvent.click(screen.getByTestId(`heat-design-room-Bathroom-area`));

  // Set Design temp, and validate it
  expect(screen.getByTestId('heat-loss-room-editor-design-temp-is-heated-checkbox')).toBeChecked();
  const slider = await screen.findByTestId('heat-loss-room-editor-design-temp-slider');
  const input = slider.querySelector('input');
  expect(input).toBeVisible();
  fireEvent.change(input!, {
    target: { value: '18' },
  });
  fireEvent.mouseUp(input!);
  expect(input).toHaveValue('18');

  // Set Air Change Per Hour
  fireEvent.change(screen.getByLabelText('heatDesign.room.avgAirChangesPerHour'), { target: { value: '2' } });

  // closes down the room modal
  await userEvent.click(screen.getByRole('button', { name: 'Close this dialog' }));
}

async function setupUnheatedBedroom3() {
  // Go into the Bedroom overview
  await userEvent.click(screen.getByText('1st Floor'));
  await userEvent.click(screen.getByTestId(`heat-design-room-Bedroom_3-area`));

  // Check room is heated, then set it to unheated
  expect(screen.getByTestId('heat-loss-room-editor-design-temp-is-heated-checkbox')).toBeChecked();
  await userEvent.click(screen.getByTestId('heat-loss-room-editor-design-temp-is-heated-checkbox'));
  expect(screen.getByTestId('heat-loss-room-editor-design-temp-is-heated-checkbox')).not.toBeChecked();

  expect(screen.getByTestId('heat-loss-room-editor-design-temp-unheated-label')).toBeVisible();

  // closes down the room modal
  await userEvent.click(screen.getByRole('button', { name: 'Close this dialog' }));
}

// Test lifecycle

beforeAll(() => server.listen());
beforeEach(async () => {
  currentHeatDesign = getAsaHouse();
  renderWithProviders(heatDesignComponent());
  await reload();
});
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

vi.setConfig({ testTimeout: 60_000 });

test('Verify the heat loss from different room components', async () => {
  expect(
    await screen.findByText('heatDesign.title.propertyDetails', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();

  expect(screen.getByTestId('heat-design-first-page-outside-temp')).toHaveTextContent('-3.2'); // In heat engineer it is set to 3.31, closest I can get to is 3.2

  goToNextHeatDesignStep();

  // Testing floors list and areas
  const groundFloor = screen.getByTestId('floor-overview-button-Ground Floor');
  expect(await screen.findByText('Ground Floor')).toBeInTheDocument();
  expect((await groundFloor).firstChild?.textContent).toMatch('Ground Floor');
  const groundFloorAreaLabel = screen.getByTestId('Ground Floor-area-label');
  expect((await groundFloorAreaLabel).textContent).toBe('67.21 m²');
  const firstFloor = screen.findByTestId('floor-overview-button-1st Floor');
  expect(await screen.findByText('1st Floor')).toBeInTheDocument();
  const firstFloorAreaLabel = screen.getByTestId('1st Floor-area-label');

  expect((await firstFloor).firstChild?.textContent).toBe('1st Floor');
  expect((await firstFloorAreaLabel).textContent).toBe('39.13 m²');

  await setupBathroom();

  // go to the next page
  goToNextHeatDesignStep();

  expect(
    await screen.findByText('heatDesign.title.heatLossOverview', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();

  // Select the first floor
  await userEvent.click(screen.getByText('1st Floor'));

  /*
    The expectations below are based on the calculations from Heat Engineer and are mainly the more simplistic cases.
    They do differ somewhat because we are not able, as of now, to get the exact same outdoor temp, but it is very close.
   */

  // Validations for Bathroom 1

  expect(Number(screen.getByTestId('heat-design-Bathroom-output-externalWalls').textContent)).toBeCloseTo(41);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-internalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-partyWalls').textContent)).toBeCloseTo(21);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-windows').textContent)).toBeCloseTo(73);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-floors').textContent)).toBeCloseTo(39);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-roofsOrCeilings').textContent)).toBeCloseTo(19);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-ventilationHeatLoss').textContent)).toBeCloseTo(130);

  // Validations for Bedroom 2
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-externalWalls').textContent)).toBeCloseTo(142);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-internalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-partyWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-windows').textContent)).toBeCloseTo(156);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-floors').textContent)).toBeCloseTo(104);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-roofsOrCeilings').textContent)).toBeCloseTo(50);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-ventilationHeatLoss').textContent)).toBeCloseTo(174);

  // Validations for Bedroom 3
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-externalWalls').textContent)).toBeCloseTo(56);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-internalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-partyWalls').textContent)).toBeCloseTo(24);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-windows').textContent)).toBeCloseTo(85);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-floors').textContent)).toBeCloseTo(57);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-roofsOrCeilings').textContent)).toBeCloseTo(27);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-ventilationHeatLoss').textContent)).toBeCloseTo(96);
});

test('Verify unheated rooms are treated correctly', async () => {
  expect(
    await screen.findByText('heatDesign.title.propertyDetails', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();

  expect(screen.getByTestId('heat-design-first-page-outside-temp')).toHaveTextContent('-3.2'); // In heat engineer it is set to 3.31, closest I can get to is 3.2

  goToNextHeatDesignStep();

  expect(await screen.findByText('Ground Floor')).toBeInTheDocument();

  await setupBathroom();
  await setupUnheatedBedroom3();

  // go to the next page
  goToNextHeatDesignStep();

  expect(
    await screen.findByText('heatDesign.title.heatLossOverview', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();

  // Select the first floor
  await userEvent.click(screen.getByText('1st Floor'));

  // Validations for Bathroom 1
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-externalWalls').textContent)).toBeCloseTo(41);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-internalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-partyWalls').textContent)).toBeCloseTo(21);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-windows').textContent)).toBeCloseTo(73);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-floors').textContent)).toBeCloseTo(39);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-roofsOrCeilings').textContent)).toBeCloseTo(19);
  expect(Number(screen.getByTestId('heat-design-Bathroom-output-ventilationHeatLoss').textContent)).toBeCloseTo(130);

  // Validations for Bedroom 2
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-externalWalls').textContent)).toBeCloseTo(142);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-internalWalls').textContent)).toBeCloseTo(100); // Now loses heat to the unheated room
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-partyWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-windows').textContent)).toBeCloseTo(156);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-floors').textContent)).toBeCloseTo(104);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-roofsOrCeilings').textContent)).toBeCloseTo(50);
  expect(Number(screen.getByTestId('heat-design-Bedroom 2-output-ventilationHeatLoss').textContent)).toBeCloseTo(174);

  // Validations for Bedroom 3
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-externalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-internalWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-partyWalls').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-doors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-windows').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-floors').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-roofGlazings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-roofsOrCeilings').textContent)).toBeCloseTo(0);
  expect(Number(screen.getByTestId('heat-design-Bedroom 3-output-ventilationHeatLoss').textContent)).toBeCloseTo(0);
});

test('Verify that you can create roof glazings and area is calculated correctly', async () => {
  goToNextHeatDesignStep();

  // Go to the bathroom
  await userEvent.click(await screen.findByText('1st Floor'));
  await userEvent.click(await screen.findByTestId(`heat-design-room-Bathroom-area`));

  // Add a 3x4 roof glazing
  await userEvent.click(await screen.findByText('heatDesign.roomSurfaceTypes.roofGlazings'));
  await userEvent.click(await screen.findByText('heatDesign.RoofGlazingsRenderer.addRoofGlazing'));

  await chooseInUValueInput('roofGlazings', 'Double Glazing Low-E Glass, Metal frame', 2.8);
  await userEvent.type(await screen.findByLabelText('common.label.measurement.length'), '4');
  await userEvent.type(await screen.findByLabelText('common.label.measurement.width'), '3');

  // It should show the calculated area
  expect(((await screen.findByLabelText('common.label.measurement.area')) as HTMLInputElement).value).toEqual('12');
});

test('Verify that switching wall types updates the default U-Value', async () => {
  goToNextHeatDesignStep();

  // Go to the bathroom
  await userEvent.click(await screen.findByText('1st Floor'));
  await userEvent.click(await screen.findByTestId(`heat-design-room-Bathroom-area`));

  // Click on a wall to select it
  const wallId = 'd272d0a0-1f37-4612-b151-7f4333f01170';
  await userEvent.click(await screen.findByTestId(`wall-${wallId}`));
  expect(await screen.findByText('heatDesign.title.surfaceEditor')).toBeInTheDocument();

  // Verify the default U-Value
  expect(screen.getByTestId(`heat-design-uvalue-modal-input-internalWalls`)).toHaveValue(
    '1.76 | Plaster, Brick 102.5mm, Plaster',
  );

  // Change the wall's type
  const select = screen.getByLabelText('common.label.type');
  await userEvent.selectOptions(select, 'Party Wall');

  // Verify that the U-Value has changed
  expect(screen.getByTestId(`heat-design-uvalue-modal-input-partyWalls`)).toHaveValue('0.50 | Party wall 1940+');
});

test('Verify reloading without saving resets data', async () => {
  // This test mostly exists to verify that we are testing the right thing in "Verify saving and loading works"

  // Make some edits
  expect((screen.getByLabelText('heatDesign.propertyDetails.YearBuilt') as HTMLInputElement).value).toBe('1969');
  await userEvent.click(screen.getByTestId('edit-construction-year'));
  fireEvent.change(screen.getByLabelText('heatDesign.propertyDetails.YearBuilt'), { target: { value: '1972' } });
  await userEvent.click(screen.getByTestId('save-construction-year'));

  expect(await screen.findByLabelText('heatDesign.propertyDetails.YearBuilt')).toHaveValue(1972);

  // Reload
  renderWithProviders(heatDesignComponent());
  await reload();

  // Expect edits to be gone
  expect(await screen.findByLabelText('heatDesign.propertyDetails.YearBuilt')).toHaveValue(1969);
});

test('Verify saving and loading works', async () => {
  // Make some global edits
  expect((screen.getByLabelText('heatDesign.propertyDetails.YearBuilt') as HTMLInputElement).value).toBe('1969');
  await userEvent.click(screen.getByTestId('edit-construction-year'));
  fireEvent.change(screen.getByLabelText('heatDesign.propertyDetails.YearBuilt'), { target: { value: '1972' } });
  await userEvent.click(screen.getByTestId('save-construction-year'));
  await userEvent.click(
    screen.getByRole('button', { name: 'heatDesign.propertyDetails.updateUValuesModal.cancelButton' }),
  );

  fireEvent.change(screen.getByLabelText('heatDesign.propertyDetails.TemperatureCompensation'), {
    target: { value: '-2' },
  });

  // Make some per-room edits
  goToNextHeatDesignStep();

  // Check that the "Living Room" has the right design temp
  await userEvent.click(screen.getByTestId('heat-design-room-Living_Room-area'));
  await userEvent.click(screen.getByText('heatDesign.surfaces.roof'));
  fireEvent.change(screen.getByTestId('heat-design-room-typeOfSpaceAbove'), { target: { value: AdjacentKind.Heated } });
  fireEvent.change(screen.getByTestId('heat-design-room-tempOfSpaceAbove'), { target: { value: 18 } });
  expect(screen.getByTestId('heat-design-room-tempOfSpaceAbove')).toHaveValue(18);
  fireEvent.change(screen.getByTestId('heat-design-room-typeOfSpaceAbove'), {
    target: { value: AdjacentKind.Outside },
  });
  await userEvent.click(screen.getByRole('button', { name: 'Close this dialog' }));

  // Change things on the first floor
  await userEvent.click(screen.getByText('1st Floor'));
  await userEvent.click(screen.getByTestId(`heat-design-room-Bathroom-area`));

  await userEvent.clear(screen.getByLabelText('heatDesign.room.roomName'));
  await userEvent.type(screen.getByLabelText('heatDesign.room.roomName'), 'Fancy Bathroom');

  expect(await getMuiSliderValue('heat-loss-room-editor-design-temp-slider')).toEqual('22');
  await setMuiSliderValue('heat-loss-room-editor-design-temp-slider', 18);

  await userEvent.click(screen.getByText('heatDesign.surfaces.roof'));
  await chooseInUValueInput('roofsOrCeilings', 'Intermediate Floor Timber with insulation', 0.32);
  fireEvent.change(screen.getByTestId('heat-design-room-typeOfSpaceAbove'), { target: { value: AdjacentKind.Heated } });

  await userEvent.click(screen.getByText('heatDesign.surfaces.floor'));
  await chooseInUValueInput('floors', 'Intermediate Floor Timber without insulation', 1.73);
  fireEvent.change(screen.getByTestId('heat-design-room-belowFloor'), {
    target: { value: AdjacentKind.SuspendedFloor },
  });

  await userEvent.click(await screen.findByText('heatDesign.roomSurfaceTypes.roofGlazings'));
  await userEvent.click(await screen.findByText('heatDesign.RoofGlazingsRenderer.addRoofGlazing'));

  await chooseInUValueInput('roofGlazings', 'Double Glazing Low-E Glass, Metal frame', 2.8);
  await userEvent.type(await screen.findByLabelText('common.label.measurement.length'), '10');
  await userEvent.type(await screen.findByLabelText('common.label.measurement.width'), '4');
  await userEvent.click(screen.getByTestId('save-roof-glazing'));

  // Close down the room modal
  await userEvent.click(screen.getByRole('button', { name: 'Close this dialog' }));
  await userEvent.click(screen.getByText('common.link.back'));

  // Save
  await userEvent.click(screen.getByText('hlc.label.save *'));

  await reload();

  // Expect the edited values to be there
  expect(((await screen.findByLabelText('heatDesign.propertyDetails.YearBuilt')) as HTMLInputElement).value).toBe(
    '1972',
  );
  expect(
    ((await screen.findByLabelText('heatDesign.propertyDetails.TemperatureCompensation')) as HTMLInputElement).value,
  ).toBe('-2');
  goToNextHeatDesignStep();
  await userEvent.click(screen.getByText('1st Floor'));
  await userEvent.click(screen.getByTestId(`heat-design-room-Fancy_Bathroom-area`));
  expect(((await screen.findByLabelText('heatDesign.room.roomName')) as HTMLInputElement).value).toBe('Fancy Bathroom');
  expect(await getMuiSliderValue('heat-loss-room-editor-design-temp-slider')).toEqual('18');
  await userEvent.click(screen.getByText('heatDesign.surfaces.roof'));
  expect(screen.getByTestId('heat-design-uvalue-modal-input-roofsOrCeilings')).toHaveValue(
    '0.32 | Intermediate Floor Timber with insulation',
  );
  expect(screen.getByTestId('heat-design-room-typeOfSpaceAbove')).toHaveValue(AdjacentKind.Heated);
  await userEvent.click(screen.getByText('heatDesign.surfaces.floor'));
  expect(screen.getByTestId('heat-design-uvalue-modal-input-floors')).toHaveValue(
    '1.73 | Intermediate Floor Timber without insulation',
  );
  expect(screen.getByTestId('heat-design-room-belowFloor')).toHaveValue(AdjacentKind.SuspendedFloor);

  await userEvent.click(await screen.findByText('heatDesign.roomSurfaceTypes.roofGlazings'));
  expect(((await screen.findByLabelText('common.label.measurement.area')) as HTMLInputElement).value).toEqual('40');
});
test('Verify air changes per hour input field', async () => {
  expect(
    await screen.findByText('heatDesign.title.propertyDetails', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();

  goToNextHeatDesignStep();

  expect(await screen.findByText('Ground Floor')).toBeInTheDocument();
  await userEvent.click(screen.getByTestId('heat-design-room-Living_Room-area'));
  const avgAirChangesPerHourInput = screen.getByLabelText('heatDesign.room.avgAirChangesPerHour');

  expect(avgAirChangesPerHourInput).toHaveValue(1.5); // default value
  const helperText = screen.getByText('heatDesign.room.avgAirChangesPerHour.helperText.standardized');
  expect(helperText).toBeInTheDocument();
  expect(screen.queryByTestId('CloseOutlinedIcon')).not.toBeInTheDocument();

  fireEvent.change(avgAirChangesPerHourInput, { target: { value: 3 } });
  expect(avgAirChangesPerHourInput).toHaveValue(3);
  expect(screen.queryByText('heatDesign.room.avgAirChangesPerHour.helperText.standardized')).not.toBeInTheDocument();
  const updatedCloseIcon = screen.getByTestId('CloseOutlinedIcon');
  expect(updatedCloseIcon).toBeInTheDocument();
});

test('verify that changing the room type updates the heating characteristics', async () => {
  expect(
    await screen.findByText('heatDesign.title.propertyDetails', {
      selector: 'h1',
    }),
  ).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  expect(await screen.findByText('Ground Floor')).toBeInTheDocument();

  // Go into the Bathroom overview
  await userEvent.click(screen.getByText('1st Floor'));
  await userEvent.click(screen.getByTestId(`heat-design-room-Bathroom-area`));
  expect(screen.getByLabelText('heatDesign.room.roomName')).toHaveValue('Bathroom');

  // Check the default ACPH
  expect(screen.getByLabelText('heatDesign.room.avgAirChangesPerHour')).toHaveValue(3);
  expect(screen.getByText('heatDesign.room.avgAirChangesPerHour.helperText.standardized')).toBeVisible();

  // Change the room type to "Bedroom", which has a lower ACPH in the UK
  const input = document.querySelector('#roomType');
  await userEvent.click(input!);
  let [option] = Array.from(document.querySelectorAll(`[data-value=${'Bedroom'}]`));
  await userEvent.click(option!);

  // Verify the updated ACPH
  expect(screen.getByLabelText('heatDesign.room.avgAirChangesPerHour')).toHaveValue(1);
  expect(screen.getByText('heatDesign.room.avgAirChangesPerHour.helperText.standardized')).toBeVisible();

  // Now override the ACPH and change the room type again to verify that the override goes away
  fireEvent.change(screen.getByLabelText('heatDesign.room.avgAirChangesPerHour'), { target: { value: '10' } });

  await userEvent.click(input!);
  [option] = Array.from(document.querySelectorAll(`[data-value=${'Bathroom'}]`));
  await userEvent.click(option!);

  expect(screen.getByLabelText('heatDesign.room.avgAirChangesPerHour')).toHaveValue(3);
  expect(screen.getByText('heatDesign.room.avgAirChangesPerHour.helperText.standardized')).toBeVisible();
});
