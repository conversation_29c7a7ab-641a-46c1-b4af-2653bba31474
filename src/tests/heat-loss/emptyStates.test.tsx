import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { screen } from '@testing-library/react';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { setupServer } from 'msw/node';
import { IntlProvider } from 'react-intl';
import { mockGetServerEnvironment, mockGetTechnicalSpecifications_empty, mocks } from 'tests/utils/mockedTrpcCalls';
import {
  goToNextHeatDesignStep,
  mockHeatDesignRoom,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  trpcMsw,
} from 'tests/utils/testUtils';
import { solution as asaSolution } from './fixtures/asaTestData';

const DWELLING_ID = { value: 'c9a14c64-c230-4755-9c49-050425668d18' };
const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
mockRouterForHeatDesign();
let heatDesign = ProtoHeatDesign.create({});
const server = setupServer(
  mockGetServerEnvironment(),
  mockGetTechnicalSpecifications_empty(),
  mocks.getGrpcEnergySolution.asa,
  trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => ({
    currentSolution: asaSolution,
    lastQuotedSolution: asaSolution,
  })),
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.getRoles.empty,
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign,
    isLocked: false,
    result: undefined,
    updatedAt: new Date(),
    events: [],
  })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [],
  })),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => ({ reports: [] })),
  trpcMsw.HeatLossCalculator.renderPreview.mutation(async () => ({ pdf: new Uint8Array([1, 2, 3, 4]) })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators: [] })),
);

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

async function renderAndReloadDesign(newHeatDesign: ProtoHeatDesign) {
  heatDesign = ProtoHeatDesign.create({ ...newHeatDesign, ...sampleDefaultDwellingUValueDefaults });
  renderWithProviders(heatDesignComponent());
  await reload();
}

// Test lifecycle

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

test('all floors have no rooms', async () => {
  const groundFloorName = 'Ground Floor';

  renderAndReloadDesign(
    ProtoHeatDesign.create({
      dwelling: {
        id: DWELLING_ID,
        constructionYear: 1969,
        numberOfResidents: 2,
        floors: [
          {
            id: { value: '5a377d7a-9854-4639-9f0f-e8a7dbaa9ed6' },
            name: groundFloorName,
            rooms: [],
          },
        ],
      },
    }),
  );

  // We should be able to step through the whole project without errors
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  expect(await screen.findByText(groundFloorName)).toBeInTheDocument();
  expect(await screen.findByText('heatDesign.error.noRoomsOnFloor')).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();
});

test('one floor with no rooms, another floor with rooms', async () => {
  const groundFloorName = 'Ground Floor';
  const firstFloorName = 'First Floor';

  renderAndReloadDesign(
    ProtoHeatDesign.create({
      dwelling: {
        id: DWELLING_ID,
        constructionYear: 1969,
        numberOfResidents: 2,
        floors: [
          {
            id: { value: '803761eb-8072-4d47-946c-23a30fd273c4' },
            name: groundFloorName,
            rooms: [],
          },
          {
            id: { value: 'e3cf2547-f418-4285-a21d-9280f17e77d0' },
            name: firstFloorName,
            rooms: [mockHeatDesignRoom('51d8041e-c985-4dc6-a0a0-0fb07003a545')],
          },
        ],
      },
    }),
  );

  // We should be able to step through the whole project without errors
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  expect(await screen.findByText(groundFloorName)).toBeInTheDocument();
  expect(await screen.findByText(firstFloorName)).toBeInTheDocument();
  expect(await screen.findByText('heatDesign.error.noRoomsOnFloor')).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  expect(await screen.findByText(groundFloorName)).toBeInTheDocument();
  expect(await screen.findByText(firstFloorName)).toBeInTheDocument();
  expect(await screen.findByText('heatDesign.error.noRoomsOnFloor')).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();
});
