import { Box, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { useIntl } from 'react-intl';
import { useEffect, useState } from 'react';
import { DurationForRoleInput } from './DurationForRoleInput';
import { getTeamSizeForRole, InstallationBookingRole, installationBookingRoles } from './queries/useGetManHours';

interface InstallationBookingProps {
  installationProjectId: string;
  isBasedOnBaseline: boolean;
  initialManHours: { [key in InstallationBookingRole]: number | undefined | null };
  onBookedInstallation: () => void;
  closeModal?: () => void;
  missingRoles?: string[];
}

export default function InstallationBooking({
  installationProjectId,
  isBasedOnBaseline,
  initialManHours,
  onBookedInstallation,
  missingRoles,
  closeModal,
}: InstallationBookingProps) {
  const intl = useIntl();
  const [manHours, setManHours] = useState(initialManHours);

  useEffect(() => {
    setManHours(initialManHours);
  }, [initialManHours]);

  const allFieldsAreValid = Object.values(manHours).every(
    (hours) => (hours !== undefined && hours !== null && hours >= 0) || hours === undefined,
  );
  const someFieldIsSet = Object.values(manHours).some((hours) => hours !== undefined && hours !== null && hours > 0);
  const enabled = allFieldsAreValid && someFieldIsSet;

  const { mutateAsync: createHeatPumpInstallationJob } =
    api.InstallationProject.createHeatPumpInstallationJob.useMutation({
      onMutate() {
        toast.loading(intl.formatMessage({ id: 'installationBooking.notify.booking' }));
      },
      onSuccess() {
        onBookedInstallation();
        setTimeout(() => {
          toast.dismiss();
          toast.success(
            intl.formatMessage({
              id: 'installationBooking.notify.bookingCreated',
              defaultMessage: 'Installation job created',
            }),
          );
          closeModal?.();
        }, 5000);
      },
      onError() {
        toast.dismiss();
        toast.error(intl.formatMessage({ id: 'common.notify.error' }));
      },
    });

  const handleBookInstallation = async () => {
    await createHeatPumpInstallationJob({
      installationProjectId,
      hoursForRole: installationBookingRoles
        .filter((role) => manHours[role] !== undefined && manHours[role] !== null)
        .map((role) => ({
          role,
          hours: manHours[role]! / getTeamSizeForRole(role),
        })),
    });
  };

  const setManHoursForRole = (role: InstallationBookingRole, hours: number | null) => {
    setManHours((prev: { [key in InstallationBookingRole]: number | undefined | null }) => ({
      ...prev,
      [role]: hours,
    }));
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'space-between',
        gap: '16px',
        maxWidth: '400px',
        width: '100%',
      }}
    >
      <Typography variant="headline1" component="h1">
        {intl.formatMessage({ id: 'installationBooking.main.title' })}
      </Typography>
      <Typography variant="body2" mb={2}>
        {isBasedOnBaseline
          ? intl.formatMessage({ id: 'installationBooking.main.descriptionfrombaseline' })
          : intl.formatMessage({ id: 'installationBooking.main.description' })}
      </Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'space-between',
          gap: 2,
          width: '100%',
        }}
      >
        {manHours.INSTALLER !== undefined && (!missingRoles || missingRoles.includes('INSTALLER')) && (
          <DurationForRoleInput
            workRole="INSTALLER"
            title={intl.formatMessage({ id: 'installationBooking.roles.installer' })}
            hours={manHours.INSTALLER}
            setHours={(hours) => setManHoursForRole('INSTALLER', hours)}
          />
        )}
        {manHours.ELECTRICIAN !== undefined && (!missingRoles || missingRoles.includes('ELECTRICIAN')) && (
          <DurationForRoleInput
            workRole="ELECTRICIAN"
            title={intl.formatMessage({ id: 'installationBooking.roles.electrician' })}
            hours={manHours.ELECTRICIAN}
            setHours={(hours) => setManHoursForRole('ELECTRICIAN', hours)}
          />
        )}
        {manHours.LANDSCAPER !== undefined && (!missingRoles || missingRoles.includes('LANDSCAPER')) && (
          <DurationForRoleInput
            workRole="LANDSCAPER"
            title={intl.formatMessage({ id: 'installationBooking.roles.landscaper' })}
            hours={manHours.LANDSCAPER}
            setHours={(hours) => setManHoursForRole('LANDSCAPER', hours)}
          />
        )}
      </Box>
      <Button variant="contained" disabled={!enabled} fullWidth sx={{ marginTop: 4 }} onClick={handleBookInstallation}>
        {intl.formatMessage({ id: 'installationBooking.main.bookButton' })}
      </Button>
    </Box>
  );
}
