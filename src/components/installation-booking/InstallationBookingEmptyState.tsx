import { Card, createTheme } from '@mui/material';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { foregroundThemes } from '@ui/theme/componentsThemes';
import { theme } from '@ui/theme/theme';

interface InstallationBookingEmptyStateProps {
  description: string;
}

export default function InstallationBookingEmptyState({ description }: InstallationBookingEmptyStateProps) {
  const solutionTheme = createTheme(theme, foregroundThemes.dark);

  return (
    <AiraThemeProvider theme={solutionTheme}>
      <Card
        sx={{
          overflowX: 'visible',
          overflowY: 'auto',
          height: '100%',
          padding: '20px 0px',
          maxWidth: '100%',
          display: 'flex',
          flex: '1 1 auto',
          flexDirection: 'column',
        }}
      >
        {description}
      </Card>
    </AiraThemeProvider>
  );
}
