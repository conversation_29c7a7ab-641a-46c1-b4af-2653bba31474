import { NumericFormField } from '@ui/components/NumericFormField/NumericFormField';
import { Box, Typography } from '@mui/material';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { durationDescription } from './durationFormatting';
import { InstallationBookingRole } from './queries/useGetManHours';

export interface DurationForRoleInputProps {
  workRole: InstallationBookingRole;
  title: string;
  hours: number | null;
  setHours: (value: number | null) => void;
}

export function DurationForRoleInput({ workRole, title, hours, setHours }: DurationForRoleInputProps) {
  const intl = useIntl();

  const [valid, setValid] = useState<boolean>(true);

  const handleChangedValue = (val: number | null) => {
    setHours(val);
    if (val !== null && val >= 0) {
      setValid(true);
    } else {
      setValid(false);
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
      <Typography variant="headline3" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <Box>
        <NumericFormField
          name={`${workRole.toLocaleLowerCase}_hours`}
          label={intl.formatMessage({ id: 'installationBooking.fields.hours' })}
          value={hours}
          onChange={handleChangedValue}
          fullWidth
          size="small"
        />
      </Box>
      <Box sx={{ width: '100%', mt: 1 }}>
        {valid && hours !== null ? (
          <Typography variant="body2">{durationDescription(workRole, hours)}</Typography>
        ) : (
          <Typography color="error" variant="body2">
            {intl.formatMessage({ id: 'installationBooking.errors.hours' })}
          </Typography>
        )}
      </Box>
    </Box>
  );
}
