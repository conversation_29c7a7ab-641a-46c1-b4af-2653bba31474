import { InstallationBooking<PERSON><PERSON>, getTeamSizeForRole } from './queries/useGetManHours';

// This assumption needs to be aligned with the backend, where we split the hours into a sequence of jobs
export const WORK_HOURS_PER_DAY = 8;

// TODO: These things should be translated, but since we are currently reorganizing how that works, we'll leave it for now

function fullDayJobs(days: number) {
  if (days === 1) {
    return `one full day job (of ${WORK_HOURS_PER_DAY} hours)`;
  }
  return `${days} full day jobs (of ${WORK_HOURS_PER_DAY} hours each)`;
}

function partialDayJob(hours: number) {
  if (hours === 1) {
    return `one job of ${hours} hour`;
  }
  return `one job of ${hours} hours`;
}

function forTeamSize(teamSize: number) {
  if (teamSize === 1) {
    return 'for one person';
  }
  if (teamSize === 2) {
    return 'for two people';
  }
  return `for ${teamSize} people`;
}

export function durationDescription(role: InstallationBookingRole, manHours: number) {
  const teamSize = getTeamSizeForRole(role);
  const totalHours = manHours / teamSize;
  const fullDays = Math.floor(totalHours / WORK_HOURS_PER_DAY);
  const partialDayHours = totalHours % WORK_HOURS_PER_DAY;

  if (fullDays > 0 && partialDayHours > 0) {
    return `Creates ${fullDayJobs(fullDays)} and ${partialDayJob(partialDayHours)} ${forTeamSize(teamSize)}.`;
  }
  if (fullDays > 0) {
    return `Creates ${fullDayJobs(fullDays)} ${forTeamSize(teamSize)}.`;
  }
  if (partialDayHours > 0) {
    return `Creates ${partialDayJob(partialDayHours)} ${forTeamSize(teamSize)}.`;
  }
  return null;
}
