import { ResourceType } from 'components/installation-planning/types/planningTypes';
import { api } from 'utils/api';

export type InstallationBookingRole = 'INSTALLER' | 'ELECTRICIAN' | 'LANDSCAPER';

export const installationBookingRoles: ResourceType[] = [
  ResourceType.INSTALLER,
  ResourceType.ELECTRICIAN,
  ResourceType.LANDSCAPER,
];

export function getTeamSizeForRole(role: InstallationBookingRole): number {
  switch (role) {
    case 'INSTALLER':
      return 2;
    case 'ELECTRICIAN':
      return 1;
    case 'LANDSCAPER':
      return 1;
    default:
      return 1;
  }
}

function createManHoursForRole({ countryCode }: { countryCode: string }): {
  [key in InstallationBookingRole]: number | undefined | null;
} {
  return {
    INSTALLER: 0,
    ELECTRICIAN: 0,
    LANDSCAPER: countryCode !== 'GB' ? 0 : undefined,
  };
}

export const useGetManHours = ({
  installationGroundworkId,
  countryCode,
}: {
  installationGroundworkId: string;
  countryCode: string;
}) => {
  const manHoursForAllRoles = createManHoursForRole({ countryCode });
  const result = api.ManHours.getManHours.useQuery(
    {
      installationGroundworkId,
    },
    {
      enabled: !!installationGroundworkId && !!countryCode,
      select: ({ data }) => {
        if (data !== null && data.roleDurations) {
          for (const role of installationBookingRoles) {
            const durationInMinutes = data.roleDurations
              .filter((r) => {
                // Map gRPC installationRole to our role strings
                if (role === 'INSTALLER') {
                  return r.installationRole?.role?.$case === 'plumber';
                } else if (role === 'ELECTRICIAN') {
                  return r.installationRole?.role?.$case === 'electrician';
                } else if (role === 'LANDSCAPER') {
                  return r.installationRole?.role?.$case === 'landscaper';
                }
                return false;
              })
              .reduce((accumulator, currentValue) => {
                // Convert Duration to minutes
                const durationInSeconds = currentValue.duration?.seconds || 0;
                return accumulator + Math.ceil(durationInSeconds / 60);
              }, 0);
            const durationInHours = Math.ceil(durationInMinutes / 60);
            manHoursForAllRoles[role] = durationInHours;
          }
          return {
            manHoursForAllRoles,
            isBasedOnBaseline: true,
          };
        }
        return {
          manHoursForAllRoles,
          isBasedOnBaseline: false,
        };
      },
    },
  );
  if (result.isError) {
    return {
      ...result,
      data: {
        manHoursForAllRoles,
        isBasedOnBaseline: false,
      },
    };
  } else {
    return result;
  }
};
