import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/com/aira/acquisition/contract/identity/v2/model';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Box, Typography, IconButton, Stack } from '@mui/material';
import ResourceImage from 'components/installation-planning/table/ResourceImage';
import { differenceInSeconds } from 'date-fns';
import { Fragment } from 'react';
import { FormattedMessage } from 'react-intl';
import { Duration } from './Duration';
import { useState } from 'react';
import { api } from 'utils/api';
import CircularProgress from '@mui/material/CircularProgress';
import { format } from 'date-fns';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { useRouter } from 'next/router';
import { enGB, de, it } from 'date-fns/locale';
import { Modal } from '@ui/components/Modal/Modal';
import CheckInOutTimeEdit from './CheckInOutTimeEdit';
import { tz } from '@date-fns/tz';
import { useRegionContext } from 'context/RegionContext';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { toast } from 'react-hot-toast';
import { surface } from 'ui/theme/colors';

const LOCALES = { 'en-gb': enGB, de: de, it: it };

export default function SegmentResourceSection({
  jobId,
  workSegment,
  workSegments,
  assignedResourcesMap,
  refetchInstallation,
}: {
  jobId: string;
  workSegment: InstallationProjectJob_WorkSegment;
  workSegments: InstallationProjectJob_WorkSegment[];
  assignedResourcesMap: Map<string, UserIdentity>;
  refetchInstallation: () => void;
}) {
  const router = useRouter();
  const { locale } = router;
  const chosenLocale = locale || 'en-GB';
  // Local state for editable timecards
  const { timeZone } = useRegionContext();

  // Mutation for updating timecards
  const { mutate: updateTimeCards, isPending } = api.InstallationProject.updateWorkSegmentTimeCards.useMutation({});

  // Track which picker is open: { [index_field]: true }
  const [editing, setEditing] = useState<{ [key: string]: boolean }>({});
  const [clearConfirmation, setClearConfirmation] = useState<{
    index: number;
    field: 'startedAt' | 'endedAt' | 'enRouteAt';
  } | null>(null);

  // Handler for picker change
  const handleTimeChange = (
    index: number,
    field: 'startedAt' | 'endedAt' | 'enRouteAt',
    value: Date | null,
    userId: string,
  ) => {
    updateTimeCards(
      {
        jobId,
        workSegments: [
          ...workSegments.map((ws) => {
            if (ws.sequenceNumber === workSegment.sequenceNumber) {
              return {
                startTime: workSegment.startTime,
                duration: workSegment.duration,
                teamId: workSegment.teamId?.value,
                timeCard: {
                  resourceUserId: userId,
                  startedAt: field === 'startedAt' ? value : undefined,
                  endedAt: field === 'endedAt' ? value : undefined,
                  enRouteAt: field === 'enRouteAt' ? value : undefined,
                },
              };
            } else {
              return {
                startTime: ws.startTime,
                duration: ws.duration,
                teamId: ws.teamId?.value,
              };
            }
          }),
        ],
      },
      {
        onSuccess: () => {
          setEditing((prev) => ({ ...prev, [`${index}_${field}`]: false }));
          setClearConfirmation(null);
          refetchInstallation();
        },

        onError: () => {
          toast.error('Failed to update time card');
        },
      },
    );
  };

  // Handler to open picker
  const openPicker = (index: number, field: 'startedAt' | 'endedAt' | 'enRouteAt') => {
    setEditing((prev) => ({ ...prev, [`${index}_${field}`]: true }));
  };
  // Handler to close picker
  const closePicker = (index: number, field: 'startedAt' | 'endedAt' | 'enRouteAt') => {
    setEditing((prev) => ({ ...prev, [`${index}_${field}`]: false }));
  };

  const openClearConfirmation = (index: number, field: 'startedAt' | 'endedAt' | 'enRouteAt') => {
    setClearConfirmation({ index, field });
  };

  const clearTimeCard = (index: number, field: 'startedAt' | 'endedAt' | 'enRouteAt', userId: string) => {
    handleTimeChange(index, field, null, userId);
  };

  const localeForDatePicker = LOCALES[chosenLocale as keyof typeof LOCALES] || enGB;

  return (
    <>
      {workSegment.timeCards.length > 0 && (
        <Box
          sx={{
            display: 'flex',
            padding: '24px 24px 16px 24px',
            flexDirection: 'column',
            alignItems: 'flex-start',
            borderRadius: '0px 0px 22px 22px',
            alignSelf: 'stretch',
            backgroundColor: '#22222608',
          }}
        >
          <div
            style={{
              display: 'grid',
              width: '1166px',
              height: `124px + ${workSegment.timeCards.length * 48}px`,
              rowGap: '4px',
              columnGap: '4px',
              gridTemplateRows: '48px 72px',
              gridTemplateColumns: '46px 160px 240px 240px 240px 220px',
            }}
          >
            <div
              style={{
                display: 'flex',
                padding: '16px 8px',
                alignItems: 'center',
                gap: '24px',
                flex: '1 0 0',
                alignSelf: 'stretch',
                gridRow: '1 / span 1',
                gridColumn: '2 / span 1',
              }}
            >
              <Typography variant="body1Emphasis">
                <FormattedMessage id="installationReview.columnHeaders.resources" defaultMessage="Resources" />
              </Typography>
            </div>

            <div
              style={{
                display: 'flex',
                paddingBottom: '8px',
                alignItems: 'flex-end',
                gap: '8px',
                flex: '1 0 0',
                alignSelf: 'stretch',
                gridRow: '1 / span 1',
                gridColumn: '3 / span 1',
              }}
            >
              <Typography variant="body1Emphasis">
                <FormattedMessage id="installationReview.columnHeaders.startTravel" defaultMessage="Start travel" />
              </Typography>
            </div>

            <div
              style={{
                display: 'flex',
                paddingBottom: '8px',
                alignItems: 'flex-end',
                gap: '8px',
                flex: '1 0 0',
                alignSelf: 'stretch',
                gridRow: '1 / span 1',
                gridColumn: '4 / span 1',
              }}
            >
              <Typography variant="body1Emphasis">
                <FormattedMessage id="installationReview.columnHeaders.actualStart" defaultMessage="Actual start" />
              </Typography>
            </div>

            <div
              style={{
                display: 'flex',
                paddingBottom: '8px',
                alignItems: 'flex-end',
                gap: '8px',
                flex: '1 0 0',
                alignSelf: 'stretch',
                gridRow: '1 / span 1',
                gridColumn: '5 / span 1',
              }}
            >
              <Typography variant="body1Emphasis">
                <FormattedMessage id="installationReview.columnHeaders.actualEnd" defaultMessage="Actual end" />
              </Typography>
            </div>

            <div
              style={{
                display: 'flex',
                paddingBottom: '8px',
                alignItems: 'flex-end',
                gap: '8px',
                flex: '1 0 0',
                alignSelf: 'stretch',
                gridRow: '1 / span 1',
                gridColumn: '6 / span 1',
              }}
            >
              <Typography variant="body1Emphasis">
                <FormattedMessage
                  id="installationReview.columnHeaders.actualManHours"
                  defaultMessage="Actual man-hours"
                />
              </Typography>
            </div>
            {workSegment.timeCards.map((timeCard, timeCardIndex) => (
              <Fragment key={workSegment.sequenceNumber + (timeCard.resourceUserId?.value ?? '')}>
                <div
                  style={{
                    display: 'flex',
                    padding: '16px 8px',
                    alignItems: 'center',
                    gap: '24px',
                    flex: '1 0 0',
                    alignSelf: 'stretch',
                    gridRow: `${timeCardIndex + 2} / span 1`,
                    gridColumn: '1 / span 2',
                  }}
                >
                  <Box>
                    {timeCard.resourceUserId?.value &&
                      assignedResourcesMap.get(timeCard.resourceUserId?.value) !== undefined && (
                        <ResourceImage
                          resource={assignedResourcesMap.get(timeCard.resourceUserId?.value) as UserIdentity}
                          size="medium"
                        />
                      )}
                  </Box>
                  <Typography variant="body1">
                    {`${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.firstName} ${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.lastName}`}
                  </Typography>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    flex: '1 0 0',
                    alignSelf: 'stretch',
                    gridRow: `${timeCardIndex + 2} / span 1`,
                    gridColumn: '3 / span 1',
                  }}
                >
                  {editing[`${timeCardIndex}_enRouteAt`] ? (
                    <Modal
                      isModalOpen={true}
                      handleClose={() => closePicker(timeCardIndex, 'enRouteAt')}
                      height="fit-content"
                      width="fit-content"
                      sx={{
                        p: 0,
                      }}
                    >
                      <CheckInOutTimeEdit
                        initialValue={workSegment.timeCards[timeCardIndex]?.enRouteAt ?? null}
                        resourceName={`${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.firstName} ${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.lastName}`}
                        onAccept={(date) => {
                          if (!timeCard.resourceUserId?.value) {
                            toast.error('Please select a resource');
                            return;
                          }
                          handleTimeChange(timeCardIndex, 'enRouteAt', date, timeCard.resourceUserId.value);
                          setEditing((prev) => ({ ...prev, [`${timeCardIndex}_enRouteAt`]: false }));
                        }}
                        onClose={() => closePicker(timeCardIndex, 'enRouteAt')}
                        disabled={isPending}
                        locale={localeForDatePicker}
                        label="Start travel"
                      />
                    </Modal>
                  ) : workSegment.timeCards[timeCardIndex]?.enRouteAt ? (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRight: `1px solid ${surface[200]}`,
                        paddingRight: '13px',
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => openPicker(timeCardIndex, 'enRouteAt')}
                      >
                        {format(workSegment.timeCards[timeCardIndex].enRouteAt, 'yyyy-MM-dd HH:mm', {
                          in: tz(timeZone),
                        })}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => openPicker(timeCardIndex, 'enRouteAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                          marginLeft: '3px',
                        }}
                      >
                        <CalendarOutlinedIcon height={16} width={16} color="#222226" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => openClearConfirmation(timeCardIndex, 'enRouteAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                        }}
                      >
                        <BinOutlinedIcon height={16} width={16} color="#222226" />
                      </IconButton>
                    </div>
                  ) : (
                    <>
                      <Typography
                        variant="body1"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => openPicker(timeCardIndex, 'enRouteAt')}
                      >
                        <FormattedMessage
                          id="installationReview.columnHeaders.addTravelTime"
                          defaultMessage="Add travel time"
                        />
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => openPicker(timeCardIndex, 'enRouteAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                        }}
                      >
                        <CalendarOutlinedIcon height={16} width={16} color="#222226" />
                      </IconButton>
                    </>
                  )}
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    flex: '1 0 0',
                    alignSelf: 'stretch',
                    gridRow: `${timeCardIndex + 2} / span 1`,
                    gridColumn: '4 / span 1',
                  }}
                >
                  {editing[`${timeCardIndex}_startedAt`] ? (
                    <Modal
                      isModalOpen={true}
                      handleClose={() => closePicker(timeCardIndex, 'startedAt')}
                      height="fit-content"
                      width="fit-content"
                      sx={{
                        p: 0,
                      }}
                    >
                      <CheckInOutTimeEdit
                        initialValue={workSegment.timeCards[timeCardIndex]?.startedAt ?? null}
                        resourceName={`${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.firstName} ${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.lastName}`}
                        onAccept={(date) => {
                          if (!timeCard.resourceUserId?.value) {
                            toast.error('Please select a resource');
                            return;
                          }
                          handleTimeChange(timeCardIndex, 'startedAt', date, timeCard.resourceUserId.value);
                          setEditing((prev) => ({ ...prev, [`${timeCardIndex}_startedAt`]: false }));
                        }}
                        onClose={() => closePicker(timeCardIndex, 'startedAt')}
                        disabled={isPending}
                        locale={localeForDatePicker}
                        label="Actual start"
                      />
                    </Modal>
                  ) : workSegment.timeCards[timeCardIndex]?.startedAt ? (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRight: `1px solid ${surface[200]}`,
                        paddingRight: '13px',
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => openPicker(timeCardIndex, 'startedAt')}
                      >
                        {format(workSegment.timeCards[timeCardIndex].startedAt, 'yyyy-MM-dd HH:mm', {
                          in: tz(timeZone),
                        })}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => openPicker(timeCardIndex, 'startedAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                          marginLeft: '3px',
                        }}
                      >
                        <CalendarOutlinedIcon height={16} width={16} color="#222226" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => openClearConfirmation(timeCardIndex, 'startedAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                          marginLeft: '3px',
                        }}
                      >
                        <BinOutlinedIcon height={16} width={16} color="#222226" />
                      </IconButton>
                    </div>
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => openPicker(timeCardIndex, 'startedAt')}
                      disabled={isPending}
                      sx={{
                        background: '#22222608',
                        borderRadius: '50px',
                        height: '40px',
                        width: '40px',
                      }}
                    >
                      <CalendarOutlinedIcon height={16} width={16} color="#222226" />
                    </IconButton>
                  )}
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    flex: '1 0 0',
                    alignSelf: 'stretch',
                    gridRow: `${timeCardIndex + 2} / span 1`,
                    gridColumn: '5 / span 1',
                  }}
                >
                  {editing[`${timeCardIndex}_endedAt`] ? (
                    <Modal
                      isModalOpen={true}
                      handleClose={() => closePicker(timeCardIndex, 'endedAt')}
                      height="fit-content"
                      width="fit-content"
                      sx={{
                        p: 0,
                      }}
                    >
                      <CheckInOutTimeEdit
                        initialValue={workSegment.timeCards[timeCardIndex]?.endedAt ?? null}
                        resourceName={`${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.firstName} ${assignedResourcesMap.get(timeCard.resourceUserId?.value ?? '')?.lastName}`}
                        onAccept={(date) => {
                          if (!timeCard.resourceUserId?.value) {
                            toast.error('Please select a resource');
                            return;
                          }
                          handleTimeChange(timeCardIndex, 'endedAt', date, timeCard.resourceUserId.value);
                          setEditing((prev) => ({ ...prev, [`${timeCardIndex}_endedAt`]: false }));
                        }}
                        onClose={() => closePicker(timeCardIndex, 'endedAt')}
                        disabled={isPending}
                        locale={localeForDatePicker}
                        label="Actual end"
                      />
                    </Modal>
                  ) : workSegment.timeCards[timeCardIndex]?.endedAt ? (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRight: `1px solid ${surface[200]}`,
                        paddingRight: '13px',
                      }}
                    >
                      <Typography
                        variant="body1"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => openPicker(timeCardIndex, 'endedAt')}
                      >
                        {format(workSegment.timeCards[timeCardIndex].endedAt!, 'yyyy-MM-dd HH:mm', {
                          in: tz(timeZone),
                        })}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => openPicker(timeCardIndex, 'endedAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                        }}
                      >
                        <CalendarOutlinedIcon height={16} width={16} color="#222226 " />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => openClearConfirmation(timeCardIndex, 'endedAt')}
                        disabled={isPending}
                        sx={{
                          background: '#22222608',
                          borderRadius: '50px',
                          height: '40px',
                          width: '40px',
                        }}
                      >
                        <BinOutlinedIcon height={16} width={16} color="#222226 " />
                      </IconButton>
                    </div>
                  ) : (
                    <IconButton
                      size="small"
                      onClick={() => openPicker(timeCardIndex, 'endedAt')}
                      disabled={isPending}
                      sx={{
                        background: '#22222608',
                        borderRadius: '50px',
                        height: '40px',
                        width: '40px',
                      }}
                    >
                      <CalendarOutlinedIcon height={16} width={16} color="#222226" />
                    </IconButton>
                  )}
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                    gap: '8px',
                    flex: '1 0 0',
                    alignSelf: 'stretch',
                    gridRow: `${timeCardIndex + 2} / span 1`,
                    gridColumn: '6 / span 1',
                  }}
                >
                  {isPending ? (
                    <CircularProgress size={16} sx={{ ml: 1 }} />
                  ) : (
                    <Duration
                      seconds={
                        workSegment.timeCards[timeCardIndex]?.endedAt && workSegment.timeCards[timeCardIndex]?.startedAt
                          ? differenceInSeconds(
                              workSegment.timeCards[timeCardIndex]!.endedAt!,
                              workSegment.timeCards[timeCardIndex]!.startedAt!,
                            )
                          : 0
                      }
                    />
                  )}
                </div>
              </Fragment>
            ))}
          </div>
        </Box>
      )}
      {clearConfirmation && (
        <Modal isModalOpen={true} handleClose={() => {}} height="fit-content" width="400px">
          <Stack direction="column" gap={2}>
            <Typography variant="body1">Are you sure you want to clear this time card?</Typography>
            <Stack direction="row" gap={2} width="100%">
              <Button variant="outlined" onClick={() => setClearConfirmation(null)} sx={{ width: '100%' }}>
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={() => {
                  if (!workSegment.timeCards[clearConfirmation.index]?.resourceUserId?.value) {
                    toast.error('Please select a resource');
                    return;
                  }
                  clearTimeCard(
                    clearConfirmation.index,
                    clearConfirmation.field,
                    workSegment.timeCards[clearConfirmation.index]!.resourceUserId!.value,
                  );
                }}
                sx={{ width: '100%' }}
              >
                Clear
              </Button>
            </Stack>
          </Stack>
        </Modal>
      )}
    </>
  );
}
