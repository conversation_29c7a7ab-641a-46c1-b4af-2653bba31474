import { Stack, Typography, useMediaQuery } from '@mui/material';
import { useMemo, useState } from 'react';
import { api } from 'utils/api';
import { FormattedMessage, useIntl } from 'react-intl';
import { Button } from '@ui/components/Button/Button';
import toast from 'react-hot-toast';
import {
  InstallationProjectActivity_JobCancelled_ReasonCategory as CancellationReasonCategory,
  InstallationProjectJob_JobResourceRole,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import {
  InstallationProject,
  InstallationProjectJob,
  InstallationProjectJob_JobStatus,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { reverseResourceMapping } from 'components/installation-planning/types/planningTypes';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import JobSection from './JobSection';
import { sortInstallationJobsWithInstallerFirst } from './helpers';
import GenericReasonModal from './GenericReasonModal';
import { InstallationBookingWrapper } from 'pages/solution/[solution]/installation';

const recreateReason = {
  key: 'recreate',
  category: CancellationReasonCategory.REASON_CATEGORY_RECREATE,
  label: 'Recreate',
};

const cancellationReasons = [
  {
    key: 'customer-cancellation',
    category: CancellationReasonCategory.REASON_CATEGORY_CUSTOMER_CANCELLATION,
    label: 'Customer cancellation (Deal Lost)',
  },
  {
    key: 'customer-no-show',
    category: CancellationReasonCategory.REASON_CATEGORY_CUSTOMER_NO_SHOW,
    label: 'Customer no show (Reschedule)',
  },
  {
    key: 'customer-reschedule',
    category: CancellationReasonCategory.REASON_CATEGORY_CUSTOMER_RESCHEDULE,
    label: 'Customer reschedule (Reschedule)',
  },
  {
    key: 'aira-cancellation',
    category: CancellationReasonCategory.REASON_CATEGORY_AIRA_CANCELLATION,
    label: 'Aira cancellation (Reschedule)',
  },
  recreateReason,
  {
    key: 'other',
    category: CancellationReasonCategory.REASON_CATEGORY_UNCATEGORIZED,
    label: 'Other',
  },
] as const;

export function InstallationReview({
  installationProject,
  refetchInstallation,
  countryCode,
}: {
  installationProject: InstallationProject & { assignedResourcesDetails: UserIdentity[] };
  refetchInstallation: () => void;
  countryCode: string;
}) {
  const { formatMessage } = useIntl();
  const [showDeletionModal, setShowDeletionModal] = useState(false);
  const isMobile = useMediaQuery('(max-width: 900px)');
  const [currentTab, setCurrentTab] = useState<InstallationProjectJob_JobResourceRole>(
    InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER,
  );
  const sortedJobs = sortInstallationJobsWithInstallerFirst(installationProject.jobs ?? []);
  const [selectedJobs, setSelectedJobs] = useState<InstallationProjectJob[]>([]);

  const { mutateAsync: deleteJobAsync, isPending: isDeletingJobs } =
    api.InstallationProject.deleteInstallationProjectJob.useMutation();

  const status = installationProject.jobs.map((job) => job.status);
  const areAllFinishedOrInProgress = status.every((s) =>
    [
      InstallationProjectJob_JobStatus.JOB_STATUS_FINISHED,
      InstallationProjectJob_JobStatus.JOB_STATUS_IN_PROGRESS,
    ].includes(s),
  );

  const assignedResourcesMap = useMemo(() => {
    const map = new Map<string, UserIdentity>();
    installationProject.assignedResourcesDetails.forEach((resource) => {
      map.set(resource.userId?.value ?? '', resource);
    });
    return map;
  }, [installationProject.assignedResourcesDetails]);

  const handleOpenDeletionModal = (jobs: InstallationProjectJob[]) => {
    setSelectedJobs(jobs);
    setShowDeletionModal(true);
  };

  const handleDeletionConfirmation = async (reason: {
    category: CancellationReasonCategory;
    description: string | null;
  }) => {
    const deletionPromises = selectedJobs.map(
      (job) =>
        deleteJobAsync({ jobId: job.id?.value ?? '', reason })
          .then(() => null) // No need for specific success tracking in this array
          .catch(() => reverseResourceMapping[job.requiredRole as keyof typeof reverseResourceMapping]), // Return job role if it fails
    );

    // Run all deletions in parallel
    const results = await Promise.allSettled(deletionPromises);

    // Track failures by role
    const failedRoles: string[] = [];
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        if (sortedJobs[index]) {
          failedRoles.push(
            reverseResourceMapping[sortedJobs[index]!.requiredRole as keyof typeof reverseResourceMapping],
          ); // Add the role of the failed job to the list
        }
      }
    });

    // Handle results
    if (failedRoles.length > 0) {
      // Show error toast with information about failed deletions
      toast.error(
        <>
          <FormattedMessage
            id="installationReview.errors.failedToDeleteJobs"
            defaultMessage="Failed to delete the following roles: "
          />
          {failedRoles.join(', ')}
        </>,
        { position: 'bottom-center' },
      );
    } else {
      refetchInstallation();
      // All deletions succeeded
      setShowDeletionModal(false);
      toast(
        <FormattedMessage
          id="installationReview.notify.deletionSuccess"
          defaultMessage="All installations have been deleted successfully."
        />,
      );
    }
  };

  const jobsForSelectedRole = sortedJobs.filter((job) => job.requiredRole === currentTab);

  return (
    <Stack mx={isMobile ? 0 : 'auto'} width="100%" maxWidth="1200px">
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        mb="48px"
        width="100%"
        gap="24px"
        sx={{ flexWrap: 'wrap' }}
      >
        <Typography variant="headline2">
          <FormattedMessage id="installationReview.title" />
        </Typography>

        {sortedJobs.length > 0 && (
          <>
            <Stack direction="row" gap="24px">
              <Button
                size="small"
                variant="outlined"
                onClick={() => handleOpenDeletionModal(sortedJobs)}
                sx={{
                  padding: '8px 32px',
                }}
              >
                <FormattedMessage id="installationReview.button.deleteAll" defaultMessage="Delete all" />
              </Button>
            </Stack>
          </>
        )}
      </Stack>
      <Stack direction="row" gap="24px">
        <Button
          variant={
            currentTab === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER ? 'contained' : 'outlined'
          }
          onClick={() => setCurrentTab(InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER)}
          size="small"
        >
          <FormattedMessage
            id="installationReview.button.cleanEnergyTechnician"
            defaultMessage="Clean Energy Technician"
          />
        </Button>
        <Button
          variant={
            currentTab === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_ELECTRICIAN
              ? 'contained'
              : 'outlined'
          }
          onClick={() => setCurrentTab(InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_ELECTRICIAN)}
          size="small"
        >
          <FormattedMessage id="installationReview.button.electrician" defaultMessage="Electrician" />
        </Button>
        {countryCode === 'DE' && (
          <Button
            variant={
              currentTab === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_LANDSCAPER
                ? 'contained'
                : 'outlined'
            }
            onClick={() => setCurrentTab(InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_LANDSCAPER)}
            size="small"
          >
            <FormattedMessage id="installationReview.button.landscaper" defaultMessage="Landscaper" />
          </Button>
        )}
      </Stack>
      <Stack alignItems="center" width="100%">
        {jobsForSelectedRole.length === 0 ? (
          <Stack
            sx={{
              mt: '80px',
            }}
          >
            <InstallationBookingWrapper
              installationGroundworkId={installationProject.installationGroundworkId?.value ?? ''}
              installationProjectId={installationProject.id?.value ?? ''}
              onBookedInstallation={refetchInstallation}
              countryCode={countryCode}
              missingRoles={[reverseResourceMapping[currentTab as keyof typeof reverseResourceMapping]]}
            />
          </Stack>
        ) : (
          jobsForSelectedRole.map((job) => (
            <JobSection
              key={job.id?.value}
              installationJob={job}
              refetchInstallation={refetchInstallation}
              assignedResourcesMap={assignedResourcesMap}
              jobDurationEstimates={installationProject.jobDurationEstimates}
              handleShowDeletionModal={(job) => handleOpenDeletionModal([job])}
            />
          ))
        )}
      </Stack>

      <GenericReasonModal
        id="confirm-deletion"
        isModalOpen={showDeletionModal}
        isInProgress={isDeletingJobs}
        reasons={!areAllFinishedOrInProgress ? cancellationReasons : [recreateReason]}
        onClose={() => setShowDeletionModal(false)}
        onConfirm={handleDeletionConfirmation}
        title={
          sortedJobs.length === selectedJobs.length && selectedJobs.length !== 1
            ? formatMessage({
                id: 'installationReview.modals.confirmDeletionAll.title',
                defaultMessage: 'Delete all jobs',
              })
            : formatMessage({
                id: 'installationReview.modals.confirmDeletion.title',
                defaultMessage: 'Delete job',
              })
        }
        confirmLabel={formatMessage({
          id: 'installationReview.modals.confirmDeletion.button.delete',
          defaultMessage: 'Delete',
        })}
        cancelLabel={formatMessage({
          id: 'installationReview.modals.confirmDeletion.button.cancel',
          defaultMessage: 'Cancel',
        })}
        reasonCategoryLabel={formatMessage({
          id: 'installationReview.modals.confirmDeletion.label.reasonCategory',
          defaultMessage: 'Reason for deletion',
        })}
        reasonDescriptionLabel={formatMessage({
          id: 'installationReview.modals.confirmDeletion.label.reasonDescription',
          defaultMessage: 'Description',
        })}
      />
    </Stack>
  );
}
