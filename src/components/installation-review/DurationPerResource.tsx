import { TypographyProps } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { Typography } from '@mui/material';
import { normaliseDuration } from './helpers';

export function DurationPerResource({
  estimateInSeconds,
  resourceCount,
  ...props
}: {
  estimateInSeconds: number | undefined;
  resourceCount: number;
} & TypographyProps) {
  const normalisedDuration = normaliseDuration((estimateInSeconds ?? 0) / resourceCount);
  return (
    <Typography variant="body1" {...props}>
      <FormattedMessage
        id="installationReview.values.expectedManHoursPerResource"
        values={{
          hours: normalisedDuration.hours,
          minutes: normalisedDuration.minutes,
          count: resourceCount,
        }}
        defaultMessage="{hours}h {minutes}m each for {count} resources"
      />
    </Typography>
  );
}
