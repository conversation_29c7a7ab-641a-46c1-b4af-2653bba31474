import { InstallationProjectJob_WorkSegmentStatus } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { installationProjectJob_WorkSegmentStatusToJSON } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { FormattedMessage } from 'react-intl';
import { Typography } from '@mui/material';
import { Box } from '@mui/material';
import { Stack } from '@mui/material';
import { MessageKey } from 'messageType';

export function statusColor(status: string) {
  switch (status) {
    case 'FINISHED':
      return '#358267';
    case 'IN_PROGRESS':
      return '#FFAF51';
    default:
      return '#D9D9D9';
  }
}

export default function WorkSegmentStatusText({ status }: { status: InstallationProjectJob_WorkSegmentStatus }) {
  const statusText = installationProjectJob_WorkSegmentStatusToJSON(status).split('WORK_SEGMENT_STATUS_')[1] ?? '';
  return (
    <Stack
      direction="row"
      gap="8px"
      alignItems="center"
      sx={{ background: '#22222608', padding: '8px 16px', borderRadius: '50px', width: 'fit-content' }}
    >
      <Box
        sx={{
          borderRadius: '5px',
          backgroundColor: statusColor(statusText),
          width: '12px',
          height: '12px',
        }}
      />
      <Typography variant="body2">
        <FormattedMessage id={`installationReview.jobSegmentStatus.${statusText}` as MessageKey} />
      </Typography>
    </Stack>
  );
}
