import { <PERSON><PERSON><PERSON>, <PERSON>ack, MenuItem, SelectChangeEvent, CircularProgress, TextField, InputLabel } from '@mui/material';
import { useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { Select } from '@ui/components/Select/Select';

export default function GenericReasonModal<ReasonCat, ReasonKey extends string>({
  isModalOpen,
  isInProgress,
  reasons,
  onClose,
  onConfirm,
  id,
  title,
  confirmLabel,
  cancelLabel,
  reasonCategoryLabel,
  reasonDescriptionLabel,
}: {
  isModalOpen: boolean;
  isInProgress: boolean;
  reasons: readonly { key: ReasonKey; category: ReasonCat; label: string }[];
  onClose: () => void;
  onConfirm: (reason: { category: ReasonCat; description: string | null }) => void;
  id: string;
  title: string;
  confirmLabel: string;
  cancelLabel: string;
  reasonCategoryLabel: string;
  reasonDescriptionLabel: string;
}) {
  const [reasonCategory, setReasonCategory] = useState<ReasonKey>(reasons[0]!.key);
  const [description, setDescription] = useState<string | null>(null);
  const handleConfirmClicked = () => {
    const reason = reasons.find((r) => r.key === reasonCategory);
    if (!reason) {
      return;
    }
    onConfirm({
      category: reason.category,
      description,
    });
  };
  const handleChangeReasonCategory = (value: ReasonKey) => {
    setReasonCategory(value);
    if (value !== 'other') setDescription(null);
  };
  const descriptionInputId = `${id}-reason-description`;
  return (
    <Modal isModalOpen={isModalOpen} handleClose={onClose} height="auto" width="auto">
      <Stack justifyContent="space-between" height="100%" gap="32px">
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="body1Emphasis">{title}</Typography>
        </Stack>
        <Select
          label={reasonCategoryLabel}
          name="reason"
          value={reasonCategory}
          onChange={(event: SelectChangeEvent) => handleChangeReasonCategory(event.target.value as ReasonKey)}
        >
          {reasons.map((option) => (
            <MenuItem key={option.key} value={option.key}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {reasonCategory === 'other' && (
          <Stack>
            <InputLabel htmlFor={descriptionInputId}>
              <Typography variant="inputLabel">{reasonDescriptionLabel}</Typography>
            </InputLabel>
            <TextField
              multiline
              id={descriptionInputId}
              rows={2}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </Stack>
        )}
        <Stack direction="row" justifyContent="flex-start" gap="24px">
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              padding: '8px 32px',
            }}
          >
            {cancelLabel}
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmClicked}
            sx={{
              padding: '8px 32px',
            }}
          >
            {isInProgress ? <CircularProgress color="secondary" size={24} /> : confirmLabel}
          </Button>
        </Stack>
      </Stack>
    </Modal>
  );
}
