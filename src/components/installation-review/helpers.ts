import { InstallationProjectJob } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

export function sortInstallationJobsWithInstallerFirst(
  array: readonly InstallationProjectJob[],
): InstallationProjectJob[] {
  return [...array].sort((a, b) => {
    const aRole = a.requiredRole;
    const bRole = b.requiredRole;
    // sort by required role in ascending order to get installer first, then electrician, then landscaper
    if (aRole === bRole) {
      return 0;
    }
    return aRole < bRole ? -1 : 1;
  });
}

export function minDate(dates: Date[]): Date | null {
  const minimum = Math.min(...dates.map((time) => +time));
  if (!Number.isFinite(minimum)) {
    return null;
  }
  return new Date(minimum);
}

export function maxDate(dates: Date[]): Date | null {
  const maximum = Math.max(...dates.map((time) => +time));
  if (!Number.isFinite(maximum)) {
    return null;
  }
  return new Date(maximum);
}

export function normaliseDuration(seconds: number) {
  return {
    hours: Math.floor((seconds ?? 0) / 3600),
    minutes: Math.floor(((seconds ?? 0) % 3600) / 60),
  };
}
