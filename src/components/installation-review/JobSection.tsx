import { FormattedMessage } from 'react-intl';
import { useState } from 'react';
import { Box, CircularProgress, IconButton, Stack, Typography } from '@mui/material';
import toast from 'react-hot-toast';
import { LinkIcon } from '@ui/components/Icons/LinkIcon/LinkIcon';
import { differenceInSeconds } from 'date-fns';
import { grey } from '@ui/theme/colors';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { api } from 'utils/api';
import { parseErrorMessage } from 'utils/helpers';
import { getTeamSizeForRole } from 'components/installation-booking/queries/useGetManHours';
import {
  InstallationProjectJob,
  InstallationProjectJob_JobResourceRole,
  InstallationProjectJobDurationEstimate,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { reverseResourceMapping } from 'components/installation-planning/types/planningTypes';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import RemoveJobHoursModal from './RemoveJobHoursModal';
import AddJobHoursModal from './AddJobHoursModal';
import SegmentResourceSection from './SegmentResourceSection';
import { Duration } from './Duration';
import { DurationPerResource } from './DurationPerResource';
import WorkSegmentStatusText from './WorkSegmentStatusText';
import { Time } from './Time';

export default function JobSection({
  installationJob,
  refetchInstallation,
  assignedResourcesMap,
  jobDurationEstimates,
  handleShowDeletionModal,
}: {
  installationJob: InstallationProjectJob;
  refetchInstallation: () => void;
  assignedResourcesMap: Map<string, UserIdentity>;
  jobDurationEstimates: InstallationProjectJobDurationEstimate[];
  handleShowDeletionModal: (job: InstallationProjectJob) => void;
}) {
  const [showRemoveJobHoursModal, setShowRemoveJobHoursModal] = useState(false);
  const [showAddJobHoursModal, setShowAddJobHoursModal] = useState(false);
  const jobDurationEstimate = jobDurationEstimates.find((estimate) => estimate.jobType === installationJob.type);

  const notify = (operationType: 'addition' | 'removal') =>
    operationType === 'addition'
      ? toast(
          <FormattedMessage
            id="installationReview.notify.addHoursSuccess"
            defaultMessage="Extra hours added to the job"
          />,
        )
      : toast(
          <FormattedMessage
            id="installationReview.notify.removeHoursSuccess"
            defaultMessage="Hours removed from the job"
          />,
        );

  const { mutate: addJobHours, isPending: isAddingJobHours } =
    api.InstallationProject.extendInstallationProjectJob.useMutation({
      onSuccess() {
        setShowAddJobHoursModal(false);
        notify('addition');
        refetchInstallation();
      },
      onError(error) {
        toast.error(parseErrorMessage(error), { position: 'bottom-center' });
      },
    });

  const { mutate: removeJobHours, isPending: isRemovingJobHours } =
    api.InstallationProject.deleteInstallationProjectJobHours.useMutation({
      onSuccess() {
        setShowRemoveJobHoursModal(false);
        notify('removal');
        refetchInstallation();
      },
      onError(error) {
        toast.error(parseErrorMessage(error), { position: 'bottom-center' });
      },
    });

  const totalDurationInSeconds = installationJob.workSegments
    .flatMap((workSegment) => workSegment.timeCards)
    .filter((timeCard) => timeCard.endedAt && timeCard.startedAt)
    .map((timeCard) => differenceInSeconds(new Date(timeCard.endedAt!), new Date(timeCard.startedAt!)))
    .reduce((accumulator, currentValue) => accumulator + currentValue, 0);

  const handleRemoveJobHoursConfirmation = () => {
    removeJobHours({
      jobId: installationJob.id?.value ?? '',
    });
  };

  const handleAddJobHours = (jobHours: number) => {
    addJobHours({
      jobId: installationJob.id?.value ?? '',
      durationInHours:
        jobHours /
        getTeamSizeForRole(reverseResourceMapping[installationJob.requiredRole as keyof typeof reverseResourceMapping]),
    });
  };

  const getDefaultResourceCount = () => {
    if (installationJob.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER) {
      return 2;
    }
    return 1;
  };

  return (
    <Box paddingBlock="60px" width="100%" maxWidth="1300px">
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
        <Box display="flex" gap={3} alignItems="center">
          <Typography variant="headline3">
            <FormattedMessage
              id={`installationReview.role.${reverseResourceMapping[installationJob.requiredRole as keyof typeof reverseResourceMapping] ?? 'UNKNOWN'}`}
            />
          </Typography>
          <Button
            size="small"
            variant="rounded"
            onClick={() => window.open(installationJob.webLink ?? '', '_blank')}
            startIcon={<LinkIcon />}
          >
            Skedulo
          </Button>
        </Box>
        <Stack direction="row" gap="16px" alignItems="center">
          <Button
            size="small"
            variant="rounded"
            onClick={() => handleShowDeletionModal(installationJob)}
            endIcon={<BinOutlinedIcon height={18} width={18} color="#000" />}
          >
            <FormattedMessage id="installationReview.button.deleteJob" defaultMessage="Delete job" />
          </Button>
        </Stack>
      </Stack>
      <Box display="grid" gridTemplateColumns="auto 1fr" columnGap="20px">
        <Typography variant="body1Emphasis">
          <FormattedMessage
            id="installationReview.labels.defaultResourceCount"
            defaultMessage="Default resource count"
          />
          :
        </Typography>
        <Typography variant="body1">{getDefaultResourceCount()}</Typography>
        <Typography variant="body1Emphasis">
          <FormattedMessage
            id="installationReview.labels.totalExpectedManHours"
            defaultMessage="Total expected man-hours"
          />
          :
        </Typography>
        <Duration seconds={jobDurationEstimate?.duration?.seconds} variant="body1" />
        <Typography variant="body1Emphasis">
          <div />
        </Typography>
        <DurationPerResource
          estimateInSeconds={jobDurationEstimate?.duration?.seconds}
          resourceCount={getDefaultResourceCount()}
        />
        <Typography variant="body1Emphasis">
          <FormattedMessage
            id="installationReview.labels.totalActualManHours"
            defaultMessage="Total actual man-hours"
          />
          :
        </Typography>
        <Duration variant="body1" pr="24px" seconds={totalDurationInSeconds} />
      </Box>

      <Stack gap="24px" sx={{ mt: '32px' }}>
        {installationJob.workSegments.map((workSegment, index) => {
          return (
            <div
              key={`${installationJob.id?.value}-${index}`}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                gap: '4px',
                alignSelf: 'stretch',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  padding: '24px 24px 16px 24px',
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderRadius: '22px 22px 0px 0px',
                  alignSelf: 'stretch',
                  backgroundColor: '#22222608',
                }}
              >
                <div
                  style={{
                    display: 'grid',
                    width: '1166px',
                    height: '124px',
                    rowGap: '4px',
                    columnGap: '4px',
                    gridTemplateRows: '48px 72px',
                    gridTemplateColumns: '46px 160px 240px 240px 240px 140px 140px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      paddingBottom: '8px',
                      alignItems: 'flex-end',
                      gap: '24px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '1 / span 1',
                      gridColumn: '2 / span 1',
                    }}
                  >
                    <Typography variant="body1Emphasis">
                      <FormattedMessage id="installationReview.columnHeaders.day" />
                      {` ${index + 1}`}
                    </Typography>
                  </div>
                  <div
                    style={{
                      display: 'inline-flex',
                      padding: '16px 102px 16px 0px',
                      alignItems: 'center',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '2 / span 1',
                      gridColumn: '2 / span 1',
                    }}
                  >
                    <WorkSegmentStatusText status={workSegment.status} />
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      paddingBottom: '8px',
                      alignItems: 'flex-end',
                      gap: '8px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '1 / span 1',
                      gridColumn: '4 / span 1',
                    }}
                  >
                    <Typography variant="body1Emphasis">
                      <FormattedMessage id="installationReview.columnHeaders.expectedStart" />
                    </Typography>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '16px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '2 / span 1',
                      gridColumn: '4 / span 1',
                    }}
                  >
                    {workSegment.startTime ? <Time time={new Date(workSegment.startTime)} /> : <>&mdash;</>}
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      paddingBottom: '8px',
                      alignItems: 'flex-end',
                      gap: '8px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '1 / span 1',
                      gridColumn: '5 / span 1',
                    }}
                  >
                    <Typography variant="body1Emphasis">
                      <FormattedMessage
                        id="installationReview.columnHeaders.expectedManHours"
                        defaultMessage="Expected man-hours"
                      />
                    </Typography>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'flex-start',
                      gap: '8px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '2 / span 1',
                      gridColumn: '5 / span 1',
                    }}
                  >
                    <Duration
                      seconds={
                        workSegment.duration?.seconds ? workSegment.duration.seconds * getDefaultResourceCount() : 0
                      }
                    />
                  </div>

                  <div
                    style={{
                      display: 'flex',
                      paddingBottom: '8px',
                      alignItems: 'flex-end',
                      gap: '8px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '1 / span 1',
                      gridColumn: '6 / span 1',
                    }}
                  >
                    <Typography variant="body1Emphasis">
                      <FormattedMessage
                        id="installationReview.columnHeaders.actualManHours"
                        defaultMessage="Actual man-hours"
                      />
                    </Typography>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'flex-start',
                      gap: '8px',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '2 / span 1',
                      gridColumn: '6 / span 1',
                    }}
                  >
                    <Duration
                      seconds={workSegment.timeCards
                        ?.map(({ startedAt, endedAt }) =>
                          endedAt && startedAt ? differenceInSeconds(new Date(endedAt), new Date(startedAt)) : 0,
                        )
                        .reduce((acc, cur) => acc + cur, 0)}
                    />
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      padding: '16px 0px',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-start',
                      flex: '1 0 0',
                      alignSelf: 'stretch',
                      gridRow: '2 / span 1',
                      gridColumn: '7 / span 1',
                    }}
                  >
                    <IconButton
                      onClick={() => setShowRemoveJobHoursModal(true)}
                      sx={{
                        backgroundColor: '#22222608',
                        borderRadius: '50%',
                        height: '48px',
                        width: '48px',
                      }}
                    >
                      <BinOutlinedIcon color="#222226" />
                    </IconButton>
                  </div>
                </div>
              </Box>
              <SegmentResourceSection
                jobId={installationJob.id?.value ?? ''}
                workSegments={installationJob.workSegments}
                workSegment={workSegment}
                assignedResourcesMap={assignedResourcesMap}
                refetchInstallation={refetchInstallation}
              />
            </div>
          );
        })}
      </Stack>

      <Stack justifyContent="center" alignItems="center" width="100%" height="100%" mt={3}>
        <IconButton
          sx={{
            backgroundColor: grey[150],
            borderRadius: '50%',
            height: '48px',
            width: '48px',
          }}
          onClick={() => setShowAddJobHoursModal(true)}
        >
          {isAddingJobHours ? <CircularProgress size="18px" /> : <AddOutlinedIcon color="#222226" />}
        </IconButton>
      </Stack>

      <AddJobHoursModal
        onClose={() => setShowAddJobHoursModal(false)}
        isModalOpen={showAddJobHoursModal}
        handleAddJobHours={handleAddJobHours}
        isAddingJobHours={isAddingJobHours}
        role={reverseResourceMapping[installationJob.requiredRole as keyof typeof reverseResourceMapping]}
      />
      <RemoveJobHoursModal
        onClose={() => setShowRemoveJobHoursModal(false)}
        isModalOpen={showRemoveJobHoursModal}
        handleRemoveJobHoursConfirmation={handleRemoveJobHoursConfirmation}
        isRemovingJobHours={isRemovingJobHours}
      />
    </Box>
  );
}
