import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuItem, Stack, Typography } from '@mui/material';
import { useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { Select } from '@ui/components/Select/Select';
import { format, getHours, getMinutes, setHours, setMinutes } from 'date-fns';
import { tz, TZDate } from '@date-fns/tz';
import { DateCalendar, LocalizationProvider } from '@mui/x-date-pickers';
import { Locale } from 'date-fns';
import { useRegionContext } from 'context/RegionContext';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { TextField } from '@ui/components/TextField/TextField';

export default function CheckInOutTimeEdit({
  initialValue,
  onAccept,
  onClose,
  disabled,
  locale,
  label,
  resourceName,
}: {
  initialValue: Date | null;
  onAccept: (date: Date | null) => void;
  onClose: () => void;
  disabled: boolean;
  locale: Locale;
  label: string;
  resourceName: string;
}) {
  const { timeZone } = useRegionContext();
  const [selectedDate, setSelectedDate] = useState(initialValue ? new TZDate(initialValue, timeZone) : new Date());
  const [selectedHour, setSelectedHour] = useState(
    getHours(initialValue ?? new Date(), { in: tz(timeZone) })
      .toString()
      .padStart(2, '0'),
  );
  const [selectedMinute, setSelectedMinute] = useState(
    getMinutes(initialValue ?? new Date(), { in: tz(timeZone) })
      .toString()
      .padStart(2, '0'),
  );
  const chosenDate = setMinutes(
    setHours(new TZDate(selectedDate, timeZone), parseInt(selectedHour)),
    parseInt(selectedMinute),
  );

  const hourOptions = Array.from({ length: 24 }, (_, i) => ({
    value: i.toString().padStart(2, '0'),
    label: i.toString().padStart(2, '0'),
  }));
  const minuteOptions = Array.from({ length: 60 }, (_, i) => ({
    value: i.toString().padStart(2, '0'),
    label: i.toString().padStart(2, '0'),
  }));

  const handleSelectDate = (date: Date) => {
    setSelectedDate(date);
  };

  const handleSelectHour = (hour: string) => {
    setSelectedHour(hour);
  };

  const handleSelectMinute = (minute: string) => {
    setSelectedMinute(minute);
  };

  const handleConfirmClicked = () => {
    onAccept(chosenDate);
    onClose();
  };

  return (
    <Stack sx={{ position: 'relative', p: '42px', pt: '24px', gap: 2 }}>
      <Stack direction="row" gap={1} justifyContent="space-between">
        <Stack direction="row" gap={1}>
          <Typography variant="headline3">{label}</Typography>
        </Stack>
        <IconButton onClick={onClose} sx={{ padding: 0 }}>
          <CrossOutlinedIcon height={24} width={24} />
        </IconButton>
      </Stack>

      <Stack direction="row" gap={1}>
        <Typography variant="body1Emphasis">{resourceName}</Typography>
      </Stack>
      <Stack>
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={locale}>
          <DateCalendar
            defaultValue={selectedDate}
            onChange={(date) => {
              if (date) {
                handleSelectDate(date);
              }
            }}
          />
        </LocalizationProvider>

        <Select
          label="Hour"
          name="hour"
          value={selectedHour}
          size="small"
          renderValue={(value) => {
            const selectedOption = hourOptions.find((option) => option.value === value);
            return `${selectedOption?.label}`;
          }}
          onChange={(e) => handleSelectHour(e.target.value)}
          MenuProps={{
            PaperProps: {
              style: {
                zIndex: 3500, // higher than the parent component's z-index
              },
            },
            disablePortal: true, // Ensures the menu renders inside the DOM hierarchy
          }}
          sx={{
            mb: 1,
          }}
        >
          {hourOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>

        <Select
          label="Minute"
          name="minute"
          value={selectedMinute}
          size="small"
          renderValue={(value) => {
            const selectedOption = minuteOptions.find((option) => option.value === value);
            return `${selectedOption?.label}`;
          }}
          onChange={(e) => handleSelectMinute(e.target.value)}
        >
          {minuteOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>
      </Stack>

      <TextField
        label="Time"
        name="time"
        value={format(chosenDate, 'yyyy-MM-dd HH:mm', {
          in: tz(timeZone),
        })}
        onChange={() => {}}
        sx={{ mt: 2 }}
        disabled
      />

      <Button variant="contained" onClick={handleConfirmClicked} disabled={disabled} sx={{ mt: 2 }}>
        Confirm
      </Button>
    </Stack>
  );
}
