import { FormattedMessage } from 'react-intl';
import { CircularProgress, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';

export default function RemoveJobHoursModal({
  onClose,
  isModalOpen,
  handleRemoveJobHoursConfirmation,
  isRemovingJobHours,
}: {
  onClose: () => void;
  isModalOpen: boolean;
  handleRemoveJobHoursConfirmation: () => void;
  isRemovingJobHours: boolean;
}) {
  return (
    <Modal
      isModalOpen={isModalOpen}
      handleClose={onClose}
      aria-labelledby="remove-job-hours-modal-title"
      aria-describedby="remove-job-hours-modal-description"
      width="auto"
      height="auto"
    >
      <Stack justifyContent="space-between" height="100%" gap="32px">
        <Typography variant="body1Emphasis">
          <FormattedMessage
            id="installationReview.modals.removeJobHours.title"
            defaultMessage="Do you want to remove job hours?"
          />
        </Typography>

        <Stack direction="row" gap="24px" justifyContent="space-between">
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              padding: '8px 32px',
            }}
          >
            <FormattedMessage id="installationReview.modals.removeJobHours.button.cancel" defaultMessage="Cancel" />
          </Button>
          <Button
            variant="contained"
            onClick={handleRemoveJobHoursConfirmation}
            sx={{
              padding: '8px 32px',
            }}
          >
            {isRemovingJobHours ? (
              <CircularProgress color="secondary" size={24} />
            ) : (
              <FormattedMessage id="installationReview.modals.removeJobHours.button.remove" defaultMessage="Remove" />
            )}
          </Button>
        </Stack>
      </Stack>
    </Modal>
  );
}
