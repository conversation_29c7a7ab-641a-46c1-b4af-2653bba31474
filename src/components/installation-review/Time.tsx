import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';

export function Time({ time, estimate }: { time: Date; estimate?: boolean }) {
  const dateString = time.toLocaleDateString(undefined, {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    minute: 'numeric',
    hour: 'numeric',
  });
  return (
    <Stack>
      <Typography variant="body1" sx={{ fontStyle: estimate ? 'italic' : 'normal' }}>
        {dateString}
      </Typography>
    </Stack>
  );
}
