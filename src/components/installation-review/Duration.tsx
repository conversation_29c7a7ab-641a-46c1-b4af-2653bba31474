import { TypographyProps } from '@mui/material';
import { normaliseDuration } from './helpers';
import { FormattedMessage } from 'react-intl';
import { Typography } from '@mui/material';

export function Duration({ seconds, ...props }: { seconds?: number } & TypographyProps) {
  const normalisedDuration = normaliseDuration(seconds ?? 0);
  return (
    <Typography variant="body2" {...props}>
      <FormattedMessage
        id="installationReview.values.duration"
        values={{
          hours: normalisedDuration.hours,
          minutes: normalisedDuration.minutes,
        }}
      />
    </Typography>
  );
}
