import { FormattedMessage, useIntl } from 'react-intl';
import { useState } from 'react';
import { CircularProgress, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { DurationForRoleInput } from 'components/installation-booking/DurationForRoleInput';
import { InstallationBookingRole } from 'components/installation-booking/queries/useGetManHours';

export default function AddJobHoursModal({
  onClose,
  isModalOpen,
  handleAddJobHours,
  isAddingJobHours,
  role,
}: {
  onClose: () => void;
  isModalOpen: boolean;
  handleAddJobHours: (jobHours: number) => void;
  isAddingJobHours?: boolean;
  role: InstallationBookingRole;
}) {
  const { formatMessage } = useIntl();
  const [manHours, setManHours] = useState<number | null>(0);
  const [showHelperText, setShowHelperText] = useState(false);

  const handleSetManHours = (hours: number | null) => {
    if (hours === null) {
      setManHours(null);
    } else if (hours > 0) {
      setManHours(hours);
      setShowHelperText(false);
    }
  };
  const inputTitle = () => {
    switch (role) {
      case 'INSTALLER':
        return formatMessage({ id: 'installationBooking.roles.installer' });
      case 'ELECTRICIAN':
        return formatMessage({ id: 'installationBooking.roles.electrician' });
      case 'LANDSCAPER':
        return formatMessage({ id: 'installationBooking.roles.landscaper' });
      default:
        return '';
    }
  };
  return (
    <Modal
      isModalOpen={isModalOpen}
      handleClose={onClose}
      aria-labelledby="add-job-hours-modal-title"
      aria-describedby="add-job-hours-modal-description"
      width="auto"
      height="auto"
    >
      <Stack justifyContent="space-between" gap="32px" height="100%">
        <Typography variant="body1Emphasis">
          <FormattedMessage id="installationReview.modals.addJobHours.title" defaultMessage="Add job hours" />
        </Typography>
        <DurationForRoleInput
          workRole={role}
          title={inputTitle()}
          hours={manHours}
          setHours={(hours) => handleSetManHours(hours)}
        />
        {showHelperText && (
          <Typography variant="body2" color="error">
            <FormattedMessage
              id="installationReview.modals.addJobHours.helperText"
              defaultMessage="Please enter a valid number of hours"
            />
          </Typography>
        )}

        <Stack direction="row" gap="24px" justifyContent="space-between">
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              padding: '8px 32px',
            }}
          >
            <FormattedMessage id="installationReview.modals.addJobHours.button.cancel" defaultMessage="Cancel" />
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (!manHours || manHours <= 0) {
                setShowHelperText(true);
                return;
              }
              handleAddJobHours(manHours);
            }}
            sx={{
              padding: '8px 32px',
            }}
          >
            {isAddingJobHours ? (
              <CircularProgress color="secondary" size={24} />
            ) : (
              <FormattedMessage id="installationReview.modals.addJobHours.button.confirm" defaultMessage="Confirm" />
            )}
          </Button>
        </Stack>
      </Stack>
    </Modal>
  );
}
