import { useEffect } from 'react';
import { useEstimatorDerivedValuesStore } from './stores/EstimatorDerivedValuesStore';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';
import { degreeDayHash } from './degreeDayHash';

function DerivedTemperatureValues({ postalCode }: { postalCode: string }) {
  const {
    adjustmentForAltitude,
    outsideTemp,
    outsideTempWithoutAltitudeAdj,
    insideOutsideDeltaT,
    degreeDays,
    meanAirTemp,
    setAdjustmentForAltitude,
    setOutsideTemp,
    setOutsideTempWithoutAltitudeAdj,
    setInsideOutsideDeltaT,
    setDegreeDays,
    setMeanAirTemp,
  } = useEstimatorDerivedValuesStore();

  const avgIndoorTemp = useEstimatorInputsStore((state) => state.avgIndoorTemp);
  const heightAboveSeaLevel = useEstimatorInputsStore((state) => state.heightAboveSeaLevel);

  useEffect(() => {
    function isDegreeDayHashKey(key: string): key is Extract<keyof typeof degreeDayHash, string> {
      return Object.keys(degreeDayHash).includes(key);
    }
    function toTwoDecimalPlaces(num: number): number {
      return Math.round(num * 100) / 100;
    }
    function getDegreeAdjustmentForAltitude(altitude: number): number {
      return toTwoDecimalPlaces(altitude * (-0.3 / 50));
    }
    if (postalCode === undefined) return;
    const postalCodeHashKey = postalCode.substring(0, 2);
    if (postalCodeHashKey === '' || postalCodeHashKey === undefined) return;
    if (!isDegreeDayHashKey(postalCodeHashKey)) return;
    if (!degreeDayHash) return;
    const degreeDayHashValue = degreeDayHash[postalCodeHashKey];
    if (degreeDayHashValue === undefined) return;
    const newDegreeDayValue = degreeDayHashValue.degreeDays;
    const newOutsideTempWithoutAltitudeAdj = degreeDayHashValue.outsideDesignTemp;
    const newAdjustmentForAltitude = getDegreeAdjustmentForAltitude(heightAboveSeaLevel);
    const newOutsideTemp = toTwoDecimalPlaces(outsideTempWithoutAltitudeAdj + newAdjustmentForAltitude);
    setDegreeDays(newDegreeDayValue);
    setOutsideTempWithoutAltitudeAdj(newOutsideTempWithoutAltitudeAdj);
    setOutsideTemp(newOutsideTemp);
    setMeanAirTemp(degreeDayHashValue.meanAirTemp);
    setAdjustmentForAltitude(newAdjustmentForAltitude);
    setInsideOutsideDeltaT(avgIndoorTemp - newOutsideTemp);
  }, [
    postalCode,
    setDegreeDays,
    heightAboveSeaLevel,
    setOutsideTempWithoutAltitudeAdj,
    setOutsideTemp,
    outsideTempWithoutAltitudeAdj,
    setAdjustmentForAltitude,
    setInsideOutsideDeltaT,
    avgIndoorTemp,
    setMeanAirTemp,
  ]);

  return (
    <table>
      <tr>
        <td>External temp for location</td>
        <td>{outsideTempWithoutAltitudeAdj}</td>
      </tr>
      <tr>
        <td>Adjustment for altitude</td>
        <td>{adjustmentForAltitude}</td>
      </tr>
      <tr>
        <td>Outside temp</td>
        <td>{outsideTemp}</td>
      </tr>
      <tr>
        <td>Inside outside delta T</td>
        <td>{insideOutsideDeltaT}</td>
      </tr>
      <tr>
        <td>Degree days</td>
        <td>{degreeDays}</td>
      </tr>
      <tr>
        <td>Mean air temp</td>
        <td>{meanAirTemp}</td>
      </tr>
    </table>
  );
}

export default DerivedTemperatureValues;
