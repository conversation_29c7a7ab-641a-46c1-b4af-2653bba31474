import React from 'react';
import { Box, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { useEstimatorHeatLossStore } from './stores/EstimatorHeatLossStore';
import { useEstimatorEnergyDemandStore } from './stores/EstimatorEnergyDemandStore';
import { toTwoDecimalPlaces } from './utils/helpers';

function EstimationResult() {
  const { totalHeatLoss } = useEstimatorHeatLossStore();

  const { totalEnergyDemand } = useEstimatorEnergyDemandStore();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'space-between',
        justifyContent: 'center',
      }}
    >
      <Card
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'center',
          width: '50%',
          margin: '20px',
          background: '#fff',
          borderRadius: '8px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Typography variant="h6" component="div">
            Heat Loss
          </Typography>
          <Typography variant="headline1" component="div">
            {toTwoDecimalPlaces(totalHeatLoss / 1000)} kW
          </Typography>
        </Box>
      </Card>
      <Card
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'center',
          width: '50%',
          margin: '20px',
          background: '#fff',
          borderRadius: '8px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'flex-start',
            justifyContent: 'center',
            width: '100%',
          }}
        >
          <Typography variant="h6" component="div">
            Energy Demand
          </Typography>
          <Typography variant="headline1" component="div">
            {toTwoDecimalPlaces(totalEnergyDemand / 1000)} kWh
          </Typography>
        </Box>
      </Card>
    </Box>
  );
}

export default EstimationResult;
