import { getHotWaterPerDay } from 'utils/calculations';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';

function DerivedHeatWaterTankValue() {
  const numBedrooms = useEstimatorInputsStore((state) => state.numBedrooms);
  const numOccupants = useEstimatorInputsStore((state) => state.numOccupants);
  const heatWaterTankSize = getHotWaterPerDay({ numberOfResidents: numOccupants, numBedrooms });

  return (
    <table>
      <tr>
        <td>Heat water tank size (L)</td>
        <td>{`${heatWaterTankSize}`}</td>
      </tr>
    </table>
  );
}

export default DerivedHeatWaterTankValue;
