import { useEffect } from 'react';
import { useEstimatorDerivedValuesStore } from './stores/EstimatorDerivedValuesStore';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';

function DerivedSizeValues() {
  const { totalFloorArea, dwellingVolume, setTotalFloorArea, setDwellingVolume } = useEstimatorDerivedValuesStore();
  const avgStoreyHeight = useEstimatorInputsStore((state) => state.avgStoreyHeight);
  const numFloors = useEstimatorInputsStore((state) => state.numFloors);
  const avgGroundFloorArea = useEstimatorInputsStore((state) => state.avgGroundFloorArea);

  useEffect(() => {
    setTotalFloorArea(avgGroundFloorArea * numFloors);
    setDwellingVolume(avgStoreyHeight * numFloors * avgGroundFloorArea);
  }, [avgStoreyHeight, numFloors, avgGroundFloorArea, setTotalFloorArea, setDwellingVolume]);

  return (
    <table>
      <tr>
        <td>Total Floor Area</td>
        <td>{totalFloorArea}</td>
      </tr>
      <tr>
        <td>Dwelling Volume</td>
        <td>{dwellingVolume}</td>
      </tr>
    </table>
  );
}

export default DerivedSizeValues;
