import React, { useEffect } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import {
  getDoorHeatLoss,
  getFloorHeatLoss,
  getMainBuildingHeatLoss,
  getRoofGlazingHeatLoss,
  getRoofHeatLoss,
  getVentilationHeatLoss,
  getWallHeatLoss,
  getWindowHeatLoss,
  useEstimatorHeatLossStore,
} from './stores/EstimatorHeatLossStore';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';
import { useEstimatorDerivedValuesStore } from './stores/EstimatorDerivedValuesStore';
import { toTwoDecimalPlaces } from './utils/helpers';

function EstimationResultHeatLoss() {
  const {
    roofHeatLoss,
    wallHeatLoss,
    floorHeatLoss,
    doorHeatLoss,
    windowHeatLoss,
    roofGlazingHeatLoss,
    ventilationHeatLoss,
    totalHeatLoss,
    mainBuildingHeatLoss,
    extensionHeat<PERSON>oss,
    setR<PERSON><PERSON><PERSON><PERSON>oss,
    set<PERSON>all<PERSON>eat<PERSON>oss,
    set<PERSON>loor<PERSON>eatLoss,
    setD<PERSON><PERSON>eatLoss,
    setWindowHeatLoss,
    setRoofGlazingHeatLoss,
    setVentilationHeatLoss,
    setTotalHeatLoss,
    setMainBuildingHeatLoss,
  } = useEstimatorHeatLossStore();

  const roofUValue = useEstimatorInputsStore((state) => state.uValueRoofCeiling);
  const roofGlazingUValue = useEstimatorInputsStore((state) => state.uValueRoofCeilingGlazing);
  const wallUValue = useEstimatorInputsStore((state) => state.uValueExternalWalls);
  const floorUValue = useEstimatorInputsStore((state) => state.uValueGroundFloor);
  const doorUValue = useEstimatorInputsStore((state) => state.uValueExternalDoors);
  const windowUValue = useEstimatorInputsStore((state) => state.uValueWindows);
  const avgGroundFloorArea = useEstimatorInputsStore((state) => state.avgGroundFloorArea);
  const insideOutsideDeltaT = useEstimatorDerivedValuesStore((state) => state.insideOutsideDeltaT);
  const glazingToRoofRatio = useEstimatorInputsStore((state) => state.roofGlazingToRoofRatio);
  const avgStoreyHeight = useEstimatorInputsStore((state) => state.avgStoreyHeight);
  const glazingToWallRatio = useEstimatorInputsStore((state) => state.glazingToWallRatio);
  const numFloors = useEstimatorInputsStore((state) => state.numFloors);
  const numExternalDoors = useEstimatorInputsStore((state) => state.numExternalDoors);
  const avgNumAirChangesPerHour = useEstimatorInputsStore((state) => state.avgNumAirChangesPerHour);
  const dwellingVolume = useEstimatorDerivedValuesStore((state) => state.dwellingVolume);
  const avgIndoorTemp = useEstimatorInputsStore((state) => state.avgIndoorTemp);
  const meanAirTemp = useEstimatorDerivedValuesStore((state) => state.meanAirTemp);
  const floorType = useEstimatorInputsStore((state) => state.floorType);

  useEffect(() => {
    const newRoofHeatLoss = getRoofHeatLoss({
      avgGroundFloorArea,
      roofUValue,
      glazingToRoofRatio,
      temperatureDifference: insideOutsideDeltaT,
    });

    const newRoofGlazingHeatLoss = getRoofGlazingHeatLoss({
      roofGlazingUValue,
      avgGroundFloorArea,
      glazingToRoofRatio,
      temperatureDifference: insideOutsideDeltaT,
    });

    const newWallHeatLoss = getWallHeatLoss({
      avgGroundFloorArea,
      wallUValue,
      temperatureDifference: insideOutsideDeltaT,
      avgStoreyHeight,
      glazingToWallRatio,
      numFloors,
      numExternalDoors,
    });

    const newFloorHeatLoss = getFloorHeatLoss({
      avgGroundFloorArea,
      floorUValue,
      temperatureDifference: floorType === 'Solid' ? avgIndoorTemp - meanAirTemp : insideOutsideDeltaT,
    });

    const newDoorHeatLoss = getDoorHeatLoss({
      numExternalDoors,
      doorUValue,
      temperatureDifference: insideOutsideDeltaT,
    });

    const newWindowHeatLoss = getWindowHeatLoss({
      avgGroundFloorArea,
      windowUValue,
      temperatureDifference: insideOutsideDeltaT,
      glazingToWallRatio,
      avgStoreyHeight,
      numFloors,
      numExternalDoors,
    });

    const newVentilationHeatLoss = getVentilationHeatLoss({
      dwellingVolume,
      avgNumAirChangesPerHour,
      temperatureDifference: insideOutsideDeltaT,
    });

    const newMainBuildingHeatLoss = getMainBuildingHeatLoss({
      roofHeatLoss: newRoofHeatLoss,
      roofGlazingHeatLoss: newRoofGlazingHeatLoss,
      wallHeatLoss: newWallHeatLoss,
      floorHeatLoss: newFloorHeatLoss,
      doorHeatLoss: newDoorHeatLoss,
      windowHeatLoss: newWindowHeatLoss,
      ventilationHeatLoss: newVentilationHeatLoss,
    });

    setMainBuildingHeatLoss(newMainBuildingHeatLoss);
    setTotalHeatLoss(newMainBuildingHeatLoss); // TODO: add extensions
    setVentilationHeatLoss(newVentilationHeatLoss);
    setRoofHeatLoss(newRoofHeatLoss);
    setRoofGlazingHeatLoss(newRoofGlazingHeatLoss);
    setFloorHeatLoss(newFloorHeatLoss);
    setDoorHeatLoss(newDoorHeatLoss);
    setWindowHeatLoss(newWindowHeatLoss);
    setWallHeatLoss(newWallHeatLoss);
  }, [
    roofUValue,
    glazingToRoofRatio,
    insideOutsideDeltaT,
    setRoofHeatLoss,
    avgGroundFloorArea,
    roofGlazingUValue,
    wallUValue,
    avgStoreyHeight,
    glazingToWallRatio,
    numFloors,
    numExternalDoors,
    floorUValue,
    doorUValue,
    windowUValue,
    setRoofGlazingHeatLoss,
    setFloorHeatLoss,
    setDoorHeatLoss,
    setWindowHeatLoss,
    setWallHeatLoss,
    dwellingVolume,
    avgNumAirChangesPerHour,
    setMainBuildingHeatLoss,
    setVentilationHeatLoss,
    setTotalHeatLoss,
    meanAirTemp,
    avgIndoorTemp,
    floorType,
  ]);

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Category</TableCell>
            <TableCell align="right">Value</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>Roof heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(roofHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Roof glazing heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(roofGlazingHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Wall heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(wallHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Floor heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(floorHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Door heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(doorHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Window heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(windowHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Ventilation heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(ventilationHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Total heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(totalHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Main Building heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(mainBuildingHeatLoss)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Extension Heat loss</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(extensionHeatLoss)}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default EstimationResultHeatLoss;
