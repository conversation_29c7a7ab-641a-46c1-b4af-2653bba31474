import { Box, MenuItem, Radio, RadioGroup, Stack, Typography } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import React from 'react';
import { useIntl } from 'react-intl';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { heatLossEstimatorValues } from './heatLossEstimatorValues';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';

function PropertyDetails() {
  const {
    houseType,
    yearBuilt,
    numOccupants,
    numBedrooms,
    occupantsPerBedroom,
    numBathrooms,
    avgIndoorTemp,
    numFloors,
    avgStoreyHeight,
    avgGroundFloorArea,
    avgNumAirChangesPerHour,
    heightAboveSeaLevel,
    isDwellingInExposedLocation,
    setHouseType,
    setYearBuilt,
    setNumOccupants,
    setNumBedrooms,
    setOccupantsPerBedroom,
    setNumBathrooms,
    setAvgIndoorTemp,
    setNumFloors,
    setAvgStoreyHeight,
    setAvgGroundFloorArea,
    setHeightAboveSeaLevel,
    setIsDwellingInExposedLocation,
    setAvgNumAirChangesPerHour,
  } = useEstimatorInputsStore();
  const intl = useIntl();
  return (
    <Stack>
      <Typography variant="headline3" sx={{ mt: 4 }}>
        {intl.formatMessage({ id: 'heatDesign.propertyDetails.title' })}
      </Typography>
      <Box
        pt={3}
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, 340px)',
          gridGap: '1rem',
          justifyContent: 'space-between',
        }}
      >
        <Box>
          <Select
            name="houseType"
            label="House Type"
            value={houseType}
            onChange={(e) => setHouseType(e.target.value)}
            size="small"
          >
            {heatLossEstimatorValues.propertyType.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <NumericTextField
            name="yearBuilt"
            label="Year Built"
            value={yearBuilt}
            onChange={(val) => setYearBuilt(val)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <TextField
            name="numOccupants"
            type="number"
            label="Number of Occupants"
            value={numOccupants}
            onChange={(e: { target: { value: string } }) => setNumOccupants(e.target.value)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <TextField
            name="numBedrooms"
            type="number"
            label="Number of Bedrooms"
            value={numBedrooms}
            onChange={(e: { target: { value: string } }) => setNumBedrooms(e.target.value)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <NumericTextField
            name="occupantsPerBedroom"
            label="Occupants Per Bedroom"
            value={occupantsPerBedroom}
            onChange={(val) => setOccupantsPerBedroom(val)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <NumericTextField
            name="numBathrooms"
            label="Number of Bathrooms"
            value={numBathrooms}
            onChange={(val) => setNumBathrooms(val)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <Select
            name="averageIndoorTemp"
            label="Average Indoor Temperature"
            value={avgIndoorTemp.toString()}
            onChange={(e: { target: { value: string } }) => setAvgIndoorTemp(parseInt(e.target.value, 10))}
            size="small"
          >
            {heatLossEstimatorValues.averageIndoorTemp.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <NumericTextField
            name="numFloors"
            label="Number of Floors"
            value={numFloors}
            onChange={(val) => setNumFloors(val)}
            size="small"
            fullWidth
          />
        </Box>
        <Box>
          <NumericTextField
            name="averageStoryHeight"
            label="Average Story Height"
            value={avgStoreyHeight}
            onChange={(val) => setAvgStoreyHeight(val)}
            size="small"
            fullWidth
            suffix="m"
          />
        </Box>
        <Box>
          <NumericTextField
            name="averageGroundFloorArea"
            label="Average Ground Floor Area"
            value={avgGroundFloorArea}
            onChange={(val) => setAvgGroundFloorArea(val)}
            size="small"
            suffix="m²"
            fullWidth
          />
        </Box>
        <Box>
          <NumericTextField
            name="averageNumAirChangesPerHour"
            label="Average Number of Air Changes Per Hour"
            value={avgNumAirChangesPerHour}
            onChange={(val) => setAvgNumAirChangesPerHour(val)}
            size="small"
            fullWidth
            type="number"
          />
        </Box>
        <Box>
          <NumericTextField
            name="heightAboveSeaLevel"
            label="Height Above Sea Level"
            value={heightAboveSeaLevel}
            onChange={(val) => setHeightAboveSeaLevel(val)}
            size="small"
            fullWidth
            suffix="m"
            type="number"
          />
        </Box>
        <Box>
          <RadioGroup
            name="isDwellingInExposedLocation"
            value={isDwellingInExposedLocation.toString()}
            onChange={(e) => setIsDwellingInExposedLocation(e.target.value === 'true')}
            row
          >
            <Typography variant="inputLabel" sx={{ fontSize: '15px' }}>
              Is the dwelling in an exposed location?
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'row' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Typography variant="body1Emphasis">No</Typography>
                <Radio value="false" />
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Typography variant="body1Emphasis">Yes</Typography>
                <Radio value="true" />
              </Box>
            </Box>
          </RadioGroup>
        </Box>
      </Box>
    </Stack>
  );
}

export default PropertyDetails;
