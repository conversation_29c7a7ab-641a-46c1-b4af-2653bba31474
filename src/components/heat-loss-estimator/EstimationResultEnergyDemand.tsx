import React, { useEffect } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { getHotWaterEnergyDemand } from 'utils/calculations';
import {
  getDoorEnergyDemand,
  getFloorEnergyDemand,
  getMainBuildingEnergyDemand,
  getRoofGlazingEnergyDemand,
  getRoofEnergyDemand,
  getVentilationEnergyDemand,
  getWallEnergyDemand,
  getWindowEnergyDemand,
  useEstimatorEnergyDemandStore,
} from './stores/EstimatorEnergyDemandStore';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';
import { useEstimatorDerivedValuesStore } from './stores/EstimatorDerivedValuesStore';
import { toTwoDecimalPlaces } from './utils/helpers';

function EstimationResultEnergyDemand() {
  const {
    roofEnergyDemand,
    wallEnergyDemand,
    floorEnergyDemand,
    doorEnergyDemand,
    windowEnergyDemand,
    roofGlazingEnergyDemand,
    ventilationEnergyDemand,
    totalEnergyDemand,
    mainBuildingEnergyDemand,
    extensionEnergyDemand,
    setRoofEnergyDemand,
    setWallEnergyDemand,
    setFloorEnergyDemand,
    setDoorEnergyDemand,
    setWindowEnergyDemand,
    setRoofGlazingEnergyDemand,
    setVentilationEnergyDemand,
    setTotalEnergyDemand,
    setMainBuildingEnergyDemand,
  } = useEstimatorEnergyDemandStore();

  const roofUValue = useEstimatorInputsStore((state) => state.uValueRoofCeiling);
  const roofGlazingUValue = useEstimatorInputsStore((state) => state.uValueRoofCeilingGlazing);
  const wallUValue = useEstimatorInputsStore((state) => state.uValueExternalWalls);
  const floorUValue = useEstimatorInputsStore((state) => state.uValueGroundFloor);
  const doorUValue = useEstimatorInputsStore((state) => state.uValueExternalDoors);
  const windowUValue = useEstimatorInputsStore((state) => state.uValueWindows);
  const avgGroundFloorArea = useEstimatorInputsStore((state) => state.avgGroundFloorArea);
  const numOccupants = useEstimatorInputsStore((state) => state.numOccupants);
  const numBedrooms = useEstimatorInputsStore((state) => state.numBedrooms);
  const insideOutsideDeltaT = useEstimatorDerivedValuesStore((state) => state.insideOutsideDeltaT);
  const glazingToRoofRatio = useEstimatorInputsStore((state) => state.roofGlazingToRoofRatio);
  const avgStoreyHeight = useEstimatorInputsStore((state) => state.avgStoreyHeight);
  const glazingToWallRatio = useEstimatorInputsStore((state) => state.glazingToWallRatio);
  const numFloors = useEstimatorInputsStore((state) => state.numFloors);
  const numExternalDoors = useEstimatorInputsStore((state) => state.numExternalDoors);
  const avgNumAirChangesPerHour = useEstimatorInputsStore((state) => state.avgNumAirChangesPerHour);
  const dwellingVolume = useEstimatorDerivedValuesStore((state) => state.dwellingVolume);
  const degreeDays = useEstimatorDerivedValuesStore((state) => state.degreeDays);

  useEffect(() => {
    const newRoofEnergyDemand = getRoofEnergyDemand({
      avgGroundFloorArea,
      roofUValue,
      glazingToRoofRatio,
      degreeDays,
    });

    const newRoofGlazingEnergyDemand = getRoofGlazingEnergyDemand({
      roofGlazingUValue,
      avgGroundFloorArea,
      glazingToRoofRatio,
      degreeDays,
    });

    const newWallEnergyDemand = getWallEnergyDemand({
      avgGroundFloorArea,
      wallUValue,
      degreeDays,
      avgStoreyHeight,
      glazingToWallRatio,
      numFloors,
      numExternalDoors,
    });

    const newFloorEnergyDemand = getFloorEnergyDemand({
      avgGroundFloorArea,
      floorUValue,
      degreeDays,
    });

    const newDoorEnergyDemand = getDoorEnergyDemand({
      numExternalDoors,
      doorUValue,
      degreeDays,
    });

    const newWindowEnergyDemand = getWindowEnergyDemand({
      avgGroundFloorArea,
      windowUValue,
      degreeDays,
      glazingToWallRatio,
      avgStoreyHeight,
      numFloors,
      numExternalDoors,
    });

    const newVentilationEnergyDemand = getVentilationEnergyDemand({
      dwellingVolume,
      avgNumAirChangesPerHour,
      degreeDays,
    });

    const newMainBuildingEnergyDemand = getMainBuildingEnergyDemand({
      roofEnergyDemand: newRoofEnergyDemand,
      roofGlazingEnergyDemand: newRoofGlazingEnergyDemand,
      wallEnergyDemand: newWallEnergyDemand,
      floorEnergyDemand: newFloorEnergyDemand,
      doorEnergyDemand: newDoorEnergyDemand,
      windowEnergyDemand: newWindowEnergyDemand,
      ventilationEnergyDemand: newVentilationEnergyDemand,
      hotWaterEnergyDemand: getHotWaterEnergyDemand({ numberOfResidents: numOccupants, numBedrooms }),
    });

    setMainBuildingEnergyDemand(newMainBuildingEnergyDemand);
    setTotalEnergyDemand(newMainBuildingEnergyDemand); // TODO: add extensions
    setVentilationEnergyDemand(newVentilationEnergyDemand);
    setRoofEnergyDemand(newRoofEnergyDemand);
    setRoofGlazingEnergyDemand(newRoofGlazingEnergyDemand);
    setFloorEnergyDemand(newFloorEnergyDemand);
    setDoorEnergyDemand(newDoorEnergyDemand);
    setWindowEnergyDemand(newWindowEnergyDemand);
    setWallEnergyDemand(newWallEnergyDemand);
  }, [
    roofUValue,
    glazingToRoofRatio,
    insideOutsideDeltaT,
    setRoofEnergyDemand,
    avgGroundFloorArea,
    roofGlazingUValue,
    wallUValue,
    avgStoreyHeight,
    glazingToWallRatio,
    numFloors,
    numExternalDoors,
    floorUValue,
    doorUValue,
    windowUValue,
    setRoofGlazingEnergyDemand,
    setFloorEnergyDemand,
    setDoorEnergyDemand,
    setWindowEnergyDemand,
    setWallEnergyDemand,
    dwellingVolume,
    avgNumAirChangesPerHour,
    setMainBuildingEnergyDemand,
    setVentilationEnergyDemand,
    setTotalEnergyDemand,
    degreeDays,
    numBedrooms,
    numOccupants,
  ]);

  return (
    <TableContainer>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Category</TableCell>
            <TableCell align="right">Value</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>Roof Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(roofEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Roof glazing Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(roofGlazingEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Wall Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(wallEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Floor Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(floorEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Door Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(doorEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Window Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(windowEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Ventilation Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(ventilationEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Total Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(totalEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Main Building Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(mainBuildingEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Extension Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(extensionEnergyDemand)}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Total Energy Demand</TableCell>
            <TableCell align="right">{toTwoDecimalPlaces(totalEnergyDemand)}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default EstimationResultEnergyDemand;
