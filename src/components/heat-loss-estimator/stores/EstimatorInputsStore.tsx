import { create } from 'zustand';

type EstimatorInputStore = {
  glazingToWallRatio: number;
  roofGlazingToRoofRatio: number;
  numExternalDoors: number;
  houseType: string;
  yearBuilt: number;
  numOccupants: string;
  numBedrooms: string;
  occupantsPerBedroom: number;
  numBathrooms: number;
  avgIndoorTemp: number;
  numFloors: number;
  avgStoreyHeight: number;
  avgGroundFloorArea: number;
  heightAboveSeaLevel: number;
  isDwellingInExposedLocation: boolean;
  avgNumAirChangesPerHour: number;
  uValueRoofCeilingGlazing: number;
  uValueRoofCeiling: number;
  aboveRoof: string;
  uValueWindows: number;
  uValueExternalWalls: number;
  uValueExternalDoors: number;
  floorType: string;
  uValueGroundFloor: number;
  setGlazingToWallRatio: (glazingToWallRatio: number) => void;
  setRoofGlazingToRoofRatio: (roofGlazingToRoofRatio: number) => void;
  setNumExternalDoors: (numExternalDoors: number) => void;
  setHouseType: (houseType: string) => void;
  setYearBuilt: (yearBuilt: number) => void;
  setNumBedrooms: (numBedrooms: string) => void;
  setOccupantsPerBedroom: (occupantsPerBedroom: number) => void;
  setNumOccupants: (numOccupants: string) => void;
  setNumBathrooms: (numBathrooms: number) => void;
  setAvgIndoorTemp: (avgIndoorTemp: number) => void;
  setNumFloors: (numFloors: number) => void;
  setAvgStoreyHeight: (avgStoreyHeight: number) => void;
  setAvgGroundFloorArea: (avgGroundFloorArea: number) => void;
  setHeightAboveSeaLevel: (heightAboveSeaLevel: number) => void;
  setIsDwellingInExposedLocation: (isDwellingInExposedLocation: boolean) => void;
  setAvgNumAirChangesPerHour: (avgNumAirChangesPerHour: number) => void;
  setUValueRoofCeilingGlazing: (uValueRoofCeilingGlazing: number) => void;
  setUValueRoofCeiling: (uValueRoofCeiling: number) => void;
  setAboveRoof: (aboveRoof: string) => void;
  setUValueWindows: (uValueWindows: number) => void;
  setUValueExternalWalls: (uValueExternalWalls: number) => void;
  setUValueExternalDoors: (uValueExternalDoors: number) => void;
  setFloorType: (floorType: string) => void;
  setUValueGroundFloor: (uValueGroundFloor: number) => void;
};
export const useEstimatorInputsStore = create<EstimatorInputStore>((set) => ({
  glazingToWallRatio: 21,
  roofGlazingToRoofRatio: 0,
  numExternalDoors: 0,
  houseType: '',
  yearBuilt: 0,
  numOccupants: '0',
  numBedrooms: '0',
  occupantsPerBedroom: 0,
  numBathrooms: 0,
  avgIndoorTemp: 0,
  numFloors: 1,
  avgStoreyHeight: 3,
  avgGroundFloorArea: 0,
  heightAboveSeaLevel: 0,
  isDwellingInExposedLocation: false,
  avgNumAirChangesPerHour: 0.0,
  uValueRoofCeilingGlazing: 0,
  uValueRoofCeiling: 0,
  aboveRoof: '',
  uValueWindows: 0,
  uValueExternalWalls: 0,
  uValueExternalDoors: 0,
  floorType: '',
  uValueGroundFloor: 0,
  setGlazingToWallRatio: (glazingToWallRatio: number) => set({ glazingToWallRatio }),
  setRoofGlazingToRoofRatio: (roofGlazingToRoofRatio: number) => set({ roofGlazingToRoofRatio }),
  setNumExternalDoors: (numExternalDoors: number) => set({ numExternalDoors }),
  setHouseType: (houseType: string) => set({ houseType }),
  setYearBuilt: (yearBuilt: number) => set({ yearBuilt }),
  setNumOccupants: (numOccupants: string) => set({ numOccupants }),
  setNumBedrooms: (numBedrooms: string) => set({ numBedrooms }),
  setOccupantsPerBedroom: (occupantsPerBedroom: number) => set({ occupantsPerBedroom }),
  setNumBathrooms: (numBathrooms: number) => set({ numBathrooms }),
  setAvgIndoorTemp: (avgIndoorTemp: number) => set({ avgIndoorTemp }),
  setNumFloors: (numFloors: number) => set({ numFloors }),
  setAvgStoreyHeight: (avgStoreyHeight: number) => set({ avgStoreyHeight }),
  setAvgGroundFloorArea: (avgGroundFloorArea: number) => set({ avgGroundFloorArea }),
  setHeightAboveSeaLevel: (heightAboveSeaLevel: number) => set({ heightAboveSeaLevel }),
  setIsDwellingInExposedLocation: (isDwellingInExposedLocation: boolean) => set({ isDwellingInExposedLocation }),
  setAvgNumAirChangesPerHour: (avgNumAirChangesPerHour: number) => set({ avgNumAirChangesPerHour }),
  setUValueRoofCeilingGlazing: (uValueRoofCeilingGlazing: number) => set({ uValueRoofCeilingGlazing }),
  setUValueRoofCeiling: (uValueRoofCeiling: number) => set({ uValueRoofCeiling }),
  setAboveRoof: (aboveRoof: string) => set({ aboveRoof }),
  setUValueWindows: (uValueWindows: number) => set({ uValueWindows }),
  setUValueExternalWalls: (uValueExternalWalls: number) => set({ uValueExternalWalls }),
  setUValueExternalDoors: (uValueExternalDoors: number) => set({ uValueExternalDoors }),
  setFloorType: (floorType: string) => set({ floorType }),
  setUValueGroundFloor: (uValueGroundFloor: number) => set({ uValueGroundFloor }),
}));
