import { create } from 'zustand';

type EstimatorEnergyDemandStore = {
  roofEnergyDemand: number;
  wallEnergyDemand: number;
  floorEnergyDemand: number;
  doorEnergyDemand: number;
  windowEnergyDemand: number;
  roofGlazingEnergyDemand: number;
  ventilationEnergyDemand: number;
  mainBuildingEnergyDemand: number;
  extensionEnergyDemand: number;
  totalEnergyDemand: number;
  hotWaterEnergyDemand: number;
  heatingEnergyDemand: number;
  averageWM2: number;
  mainBuildingWM2: number;
  extensionWM2: number;
  setRoofEnergyDemand: (roofEnergyDemand: number) => void;
  setWallEnergyDemand: (wallEnergyDemand: number) => void;
  setFloorEnergyDemand: (floorEnergyDemand: number) => void;
  setDoorEnergyDemand: (doorEnergyDemand: number) => void;
  setWindowEnergyDemand: (windowEnergyDemand: number) => void;
  setRoofGlazingEnergyDemand: (roofGlazingEnergyDemand: number) => void;
  setVentilationEnergyDemand: (ventilationEnergyDemand: number) => void;
  setMainBuildingEnergyDemand: (mainBuildingEnergyDemand: number) => void;
  setExtensionEnergyDemand: (extensionEnergyDemand: number) => void;
  setTotalEnergyDemand: (totalEnergyDemand: number) => void;
  setHotWaterEnergyDemand: (hotWaterEnergyDemand: number) => void;
  setHeatingEnergyDemand: (heatingEnergyDemand: number) => void;
  setAverageWM2: (averageWM2: number) => void;
  setMainBuildingWM2: (mainBuildingWM2: number) => void;
  setExtensionWM2: (extensionWM2: number) => void;
};

const standardDoorArea = 1.98;
const airChangeFactor = 0.33;

const getRoofEnergyDemand = ({
  avgGroundFloorArea,
  glazingToRoofRatio,
  roofUValue,
  degreeDays,
}: {
  avgGroundFloorArea: number;
  glazingToRoofRatio: number;
  roofUValue: number;
  degreeDays: number;
}) => (avgGroundFloorArea - avgGroundFloorArea * (glazingToRoofRatio / 100)) * roofUValue * degreeDays * (24 / 1000);

const getRoofGlazingEnergyDemand = ({
  avgGroundFloorArea,
  glazingToRoofRatio,
  roofGlazingUValue,
  degreeDays,
}: {
  avgGroundFloorArea: number;
  glazingToRoofRatio: number;
  roofGlazingUValue: number;
  degreeDays: number;
}) => {
  const roofGlazingArea = avgGroundFloorArea * glazingToRoofRatio;
  return roofGlazingArea * roofGlazingUValue * degreeDays * (24 / 1000);
};

const getWallEnergyDemand = ({
  avgGroundFloorArea,
  avgStoreyHeight,
  numFloors,
  glazingToWallRatio,
  numExternalDoors,
  wallUValue,
  degreeDays,
}: {
  avgGroundFloorArea: number;
  avgStoreyHeight: number;
  numFloors: number;
  glazingToWallRatio: number;
  numExternalDoors: number;
  wallUValue: number;
  degreeDays: number;
}) => {
  const floorPerimeter = Math.sqrt(avgGroundFloorArea) * 4;
  const totalDoorArea = numExternalDoors * standardDoorArea;
  const totalWallArea = floorPerimeter * avgStoreyHeight * numFloors;
  const wallAreaMinusGlazingAndDoors = totalWallArea * ((100 - glazingToWallRatio) / 100) - totalDoorArea;
  return wallAreaMinusGlazingAndDoors * wallUValue * degreeDays * (24 / 1000);
};

const getFloorEnergyDemand = ({
  avgGroundFloorArea,
  floorUValue,
  degreeDays,
}: {
  avgGroundFloorArea: number;
  floorUValue: number;
  degreeDays: number;
}) => avgGroundFloorArea * floorUValue * degreeDays * (24 / 1000);

const getDoorEnergyDemand = ({
  numExternalDoors,
  doorUValue,
  degreeDays,
}: {
  numExternalDoors: number;
  doorUValue: number;
  degreeDays: number;
}) => numExternalDoors * standardDoorArea * doorUValue * degreeDays * (24 / 1000);

const getWindowEnergyDemand = ({
  avgGroundFloorArea,
  avgStoreyHeight,
  numFloors,
  glazingToWallRatio,
  numExternalDoors,
  windowUValue,
  degreeDays,
}: {
  avgGroundFloorArea: number;
  avgStoreyHeight: number;
  numFloors: number;
  glazingToWallRatio: number;
  numExternalDoors: number;
  windowUValue: number;
  degreeDays: number;
}) => {
  const totalDoorArea = numExternalDoors * standardDoorArea;
  const totalWallArea = avgGroundFloorArea * avgStoreyHeight * numFloors;
  const totalWindowArea = (totalWallArea - totalDoorArea) * (glazingToWallRatio / 100);
  return totalWindowArea * windowUValue * degreeDays * (24 / 1000);
};

const getVentilationEnergyDemand = ({
  dwellingVolume,
  avgNumAirChangesPerHour,
  degreeDays,
}: {
  dwellingVolume: number;
  avgNumAirChangesPerHour: number;
  degreeDays: number;
}) => dwellingVolume * avgNumAirChangesPerHour * airChangeFactor * degreeDays * (24 / 1000);

const getMainBuildingEnergyDemand = ({
  wallEnergyDemand,
  roofEnergyDemand,
  floorEnergyDemand,
  doorEnergyDemand,
  windowEnergyDemand,
  roofGlazingEnergyDemand,
  ventilationEnergyDemand,
  hotWaterEnergyDemand,
}: {
  wallEnergyDemand: number;
  roofEnergyDemand: number;
  floorEnergyDemand: number;
  doorEnergyDemand: number;
  windowEnergyDemand: number;
  roofGlazingEnergyDemand: number;
  ventilationEnergyDemand: number;
  hotWaterEnergyDemand: number;
}) =>
  hotWaterEnergyDemand +
  wallEnergyDemand +
  roofEnergyDemand +
  floorEnergyDemand +
  doorEnergyDemand +
  windowEnergyDemand +
  roofGlazingEnergyDemand +
  ventilationEnergyDemand;

const getTotalEnergyDemand = ({
  mainBuildingEnergyDemand,
  extensionEnergyDemand,
}: {
  mainBuildingEnergyDemand: number;
  extensionEnergyDemand: number;
}) => mainBuildingEnergyDemand + extensionEnergyDemand;

export const useEstimatorEnergyDemandStore = create<EstimatorEnergyDemandStore>((set) => ({
  roofEnergyDemand: 0,
  wallEnergyDemand: 0,
  floorEnergyDemand: 0,
  doorEnergyDemand: 0,
  windowEnergyDemand: 0,
  roofGlazingEnergyDemand: 0,
  ventilationEnergyDemand: 0,
  totalEnergyDemand: 0,
  mainBuildingEnergyDemand: 0,
  extensionEnergyDemand: 0,
  hotWaterEnergyDemand: 0,
  heatingEnergyDemand: 0,
  averageWM2: 0,
  mainBuildingWM2: 0,
  extensionWM2: 0,
  setRoofEnergyDemand: (roofEnergyDemand: number) => set({ roofEnergyDemand }),
  setWallEnergyDemand: (wallEnergyDemand: number) => set({ wallEnergyDemand }),
  setFloorEnergyDemand: (floorEnergyDemand: number) => set({ floorEnergyDemand }),
  setDoorEnergyDemand: (doorEnergyDemand: number) => set({ doorEnergyDemand }),
  setWindowEnergyDemand: (windowEnergyDemand: number) => set({ windowEnergyDemand }),
  setRoofGlazingEnergyDemand: (roofGlazingEnergyDemand: number) => set({ roofGlazingEnergyDemand }),
  setVentilationEnergyDemand: (ventilationEnergyDemand: number) => set({ ventilationEnergyDemand }),
  setTotalEnergyDemand: (totalEnergyDemand: number) => set({ totalEnergyDemand }),
  setMainBuildingEnergyDemand: (mainBuildingEnergyDemand: number) => set({ mainBuildingEnergyDemand }),
  setExtensionEnergyDemand: (extensionEnergyDemand: number) => set({ extensionEnergyDemand }),
  setHotWaterEnergyDemand: (hotWaterEnergyDemand: number) => set({ hotWaterEnergyDemand }),
  setHeatingEnergyDemand: (heatingEnergyDemand: number) => set({ heatingEnergyDemand }),
  setAverageWM2: (averageWM2: number) => set({ averageWM2 }),
  setMainBuildingWM2: (mainBuildingWM2: number) => set({ mainBuildingWM2 }),
  setExtensionWM2: (extensionWM2: number) => set({ extensionWM2 }),
}));

export {
  getRoofEnergyDemand,
  getWallEnergyDemand,
  getFloorEnergyDemand,
  getDoorEnergyDemand,
  getWindowEnergyDemand,
  getRoofGlazingEnergyDemand,
  getVentilationEnergyDemand,
  getMainBuildingEnergyDemand,
  getTotalEnergyDemand,
};
