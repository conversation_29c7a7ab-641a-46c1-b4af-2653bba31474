import { create } from 'zustand';

type EstimatorDerivedValuesStore = {
  totalFloorArea: number;
  dwellingVolume: number;
  outsideTemp: number;
  outsideTempWithoutAltitudeAdj: number;
  insideOutsideDeltaT: number;
  degreeDays: number;
  adjustmentForAltitude: number;
  meanAirTemp: number;
  setTotalFloorArea: (totalFloorArea: number) => void;
  setDwellingVolume: (dwellingVolume: number) => void;
  setOutsideTemp: (outsideTemp: number) => void;
  setOutsideTempWithoutAltitudeAdj: (outsideTempWithoutAltitudeAdj: number) => void;
  setInsideOutsideDeltaT: (indoorOutdoorDeltaT: number) => void;
  setDegreeDays: (degreeDays: number) => void;
  setAdjustmentForAltitude: (adjustmentForAltitude: number) => void;
  setMeanAirTemp: (meanAirTemp: number) => void;
};

export const useEstimatorDerivedValuesStore = create<EstimatorDerivedValuesStore>((set) => ({
  totalFloorArea: 0,
  dwellingVolume: 0,
  outsideTemp: 0,
  outsideTempWithoutAltitudeAdj: 0,
  insideOutsideDeltaT: 0,
  degreeDays: 0,
  adjustmentForAltitude: 0,
  meanAirTemp: 0,
  setTotalFloorArea: (totalFloorArea: number) => set({ totalFloorArea }),
  setDwellingVolume: (dwellingVolume: number) => set({ dwellingVolume }),
  setOutsideTemp: (outsideTemp: number) => set({ outsideTemp }),
  setOutsideTempWithoutAltitudeAdj: (outsideTempWithoutAltitudeAdj: number) => set({ outsideTempWithoutAltitudeAdj }),
  setInsideOutsideDeltaT: (insideOutsideDeltaT: number) => set({ insideOutsideDeltaT }),
  setDegreeDays: (degreeDays: number) => set({ degreeDays }),
  setAdjustmentForAltitude: (adjustmentForAltitude: number) => set({ adjustmentForAltitude }),
  setMeanAirTemp: (meanAirTemp: number) => set({ meanAirTemp }),
}));
