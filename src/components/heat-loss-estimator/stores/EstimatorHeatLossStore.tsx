import { create } from 'zustand';

type EstimatorHeatLossStore = {
  roofHeatLoss: number;
  wallHeatLoss: number;
  floorHeatLoss: number;
  doorHeatLoss: number;
  windowHeatLoss: number;
  roofGlazingHeatLoss: number;
  ventilationHeatLoss: number;
  totalHeatLoss: number;
  mainBuildingHeatLoss: number;
  extensionHeatLoss: number;
  setRoofHeatLoss: (roofHeatLoss: number) => void;
  setWallHeatLoss: (wallHeatLoss: number) => void;
  setFloorHeatLoss: (floorHeatLoss: number) => void;
  setDoorHeatLoss: (doorHeatLoss: number) => void;
  setWindowHeatLoss: (windowHeatLoss: number) => void;
  setRoofGlazingHeatLoss: (roofGlazingHeatLoss: number) => void;
  setVentilationHeatLoss: (ventilationHeatLoss: number) => void;
  setTotalHeatLoss: (totalHeatLoss: number) => void;
  setMainBuildingHeatLoss: (mainBuildingHeatLoss: number) => void;
  setExtensionHeatLoss: (extensionHeatLoss: number) => void;
};

const standardDoorArea = 1.98;
const airChangeFactor = 0.33;

const getRoofHeatLoss = ({
  avgGroundFloorArea,
  glazingToRoofRatio,
  roofUValue,
  temperatureDifference,
}: {
  avgGroundFloorArea: number;
  glazingToRoofRatio: number;
  roofUValue: number;
  temperatureDifference: number;
}) => (avgGroundFloorArea - avgGroundFloorArea * (glazingToRoofRatio / 100)) * roofUValue * temperatureDifference;

const getRoofGlazingHeatLoss = ({
  avgGroundFloorArea,
  glazingToRoofRatio,
  roofGlazingUValue,
  temperatureDifference,
}: {
  avgGroundFloorArea: number;
  glazingToRoofRatio: number;
  roofGlazingUValue: number;
  temperatureDifference: number;
}) => {
  const roofGlazingArea = avgGroundFloorArea * (glazingToRoofRatio / 100);
  return roofGlazingArea * roofGlazingUValue * temperatureDifference;
};

const getWallHeatLoss = ({
  avgGroundFloorArea,
  avgStoreyHeight,
  numFloors,
  glazingToWallRatio,
  numExternalDoors,
  wallUValue,
  temperatureDifference,
}: {
  avgGroundFloorArea: number;
  avgStoreyHeight: number;
  numFloors: number;
  glazingToWallRatio: number;
  numExternalDoors: number;
  wallUValue: number;
  temperatureDifference: number;
}) => {
  const totalDoorArea = numExternalDoors * standardDoorArea;
  const floorPerimeter = Math.sqrt(avgGroundFloorArea) * 4;
  const totalWallArea = floorPerimeter * avgStoreyHeight * numFloors;
  const wallAreaMinusGlazingAndDoors = totalWallArea * ((100 - glazingToWallRatio) / 100) - totalDoorArea;
  return wallAreaMinusGlazingAndDoors * wallUValue * temperatureDifference;
};

const getFloorHeatLoss = ({
  avgGroundFloorArea,
  floorUValue,
  temperatureDifference,
}: {
  avgGroundFloorArea: number;
  floorUValue: number;
  temperatureDifference: number;
}) => avgGroundFloorArea * floorUValue * temperatureDifference;

const getDoorHeatLoss = ({
  numExternalDoors,
  doorUValue,
  temperatureDifference,
}: {
  numExternalDoors: number;
  doorUValue: number;
  temperatureDifference: number;
}) => numExternalDoors * standardDoorArea * doorUValue * temperatureDifference;

const getWindowHeatLoss = ({
  avgGroundFloorArea,
  avgStoreyHeight,
  numFloors,
  glazingToWallRatio,
  numExternalDoors,
  windowUValue,
  temperatureDifference,
}: {
  avgGroundFloorArea: number;
  avgStoreyHeight: number;
  numFloors: number;
  glazingToWallRatio: number;
  numExternalDoors: number;
  windowUValue: number;
  temperatureDifference: number;
}) => {
  const totalDoorArea = numExternalDoors * standardDoorArea;
  const totalWallArea = avgGroundFloorArea * avgStoreyHeight * numFloors;
  const totalWindowArea = (totalWallArea - totalDoorArea) * (glazingToWallRatio / 100);
  return totalWindowArea * windowUValue * temperatureDifference;
};

const getVentilationHeatLoss = ({
  dwellingVolume,
  avgNumAirChangesPerHour,
  temperatureDifference,
}: {
  dwellingVolume: number;
  avgNumAirChangesPerHour: number;
  temperatureDifference: number;
}) => dwellingVolume * avgNumAirChangesPerHour * airChangeFactor * temperatureDifference;

const getMainBuildingHeatLoss = ({
  wallHeatLoss,
  roofHeatLoss,
  floorHeatLoss,
  doorHeatLoss,
  windowHeatLoss,
  roofGlazingHeatLoss,
  ventilationHeatLoss,
}: {
  wallHeatLoss: number;
  roofHeatLoss: number;
  floorHeatLoss: number;
  doorHeatLoss: number;
  windowHeatLoss: number;
  roofGlazingHeatLoss: number;
  ventilationHeatLoss: number;
}) =>
  wallHeatLoss +
  roofHeatLoss +
  floorHeatLoss +
  doorHeatLoss +
  windowHeatLoss +
  roofGlazingHeatLoss +
  ventilationHeatLoss;

const getTotalHeatLoss = ({
  mainBuildingHeatLoss,
  extensionHeatLoss,
}: {
  mainBuildingHeatLoss: number;
  extensionHeatLoss: number;
}) => mainBuildingHeatLoss + extensionHeatLoss;

export const useEstimatorHeatLossStore = create<EstimatorHeatLossStore>((set) => ({
  roofHeatLoss: 0,
  wallHeatLoss: 0,
  floorHeatLoss: 0,
  doorHeatLoss: 0,
  windowHeatLoss: 0,
  roofGlazingHeatLoss: 0,
  ventilationHeatLoss: 0,
  totalHeatLoss: 0,
  mainBuildingHeatLoss: 0,
  extensionHeatLoss: 0,
  setRoofHeatLoss: (roofHeatLoss: number) => set({ roofHeatLoss }),
  setWallHeatLoss: (wallHeatLoss: number) => set({ wallHeatLoss }),
  setFloorHeatLoss: (floorHeatLoss: number) => set({ floorHeatLoss }),
  setDoorHeatLoss: (doorHeatLoss: number) => set({ doorHeatLoss }),
  setWindowHeatLoss: (windowHeatLoss: number) => set({ windowHeatLoss }),
  setRoofGlazingHeatLoss: (roofGlazingHeatLoss: number) => set({ roofGlazingHeatLoss }),
  setVentilationHeatLoss: (ventilationHeatLoss: number) => set({ ventilationHeatLoss }),
  setTotalHeatLoss: (totalHeatLoss: number) => set({ totalHeatLoss }),
  setMainBuildingHeatLoss: (mainBuildingHeatLoss: number) => set({ mainBuildingHeatLoss }),
  setExtensionHeatLoss: (extensionHeatLoss: number) => set({ extensionHeatLoss }),
}));

export {
  getRoofHeatLoss,
  getWallHeatLoss,
  getFloorHeatLoss,
  getDoorHeatLoss,
  getWindowHeatLoss,
  getRoofGlazingHeatLoss,
  getVentilationHeatLoss,
  getMainBuildingHeatLoss,
  getTotalHeatLoss,
};
