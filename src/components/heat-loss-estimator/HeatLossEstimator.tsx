import { Card } from '@ui/components/Card/Card';
import { TextField } from '@ui/components/TextField/TextField';
import { Stack, Typography, useMediaQuery } from '@mui/material';
import { useIntl } from 'react-intl';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { api } from '../../utils/api';
import UValues from './UValues';
import DwellingOpenings from './DwellingOpenings';
import PropertyDetails from './PropertyDetails';
import DerivedSizeValues from './DerivedSizeValues';
import DerivedTemperatureValues from './DerivedTemperatureValues';
import EstimationResultHeatLoss from './EstimationResultHeatLoss';
import EstimationResultEnergyDemand from './EstimationResultEnergyDemand';
import EstimationResult from './EstimationResult';
import DerivedHeatWaterTankValue from './DerivedHeatWaterTankValue';

export default function HeatLossEstimator() {
  const intl = useIntl();
  const solutionId = useEnergySolutionId();
  const { data: groundwork, isLoading: isLoadingGroundwork } = api.AiraBackend.getGroundworkForSolution.useQuery(
    { solutionId: solutionId! },
    { enabled: !!solutionId },
  );

  const isMobile = useMediaQuery('(max-width: 700px)');

  if (isLoadingGroundwork) return <div>Loading house data...</div>;

  if (!groundwork) {
    return <div>Error loading house data.</div>;
  }

  if (groundwork.location?.$case !== 'exactAddress') {
    return <div>Groundwork missing an exact adress.</div>;
  }

  const { exactAddress } = groundwork.location;

  return (
    <Card
      sx={{
        overflowX: 'visible',
        overflowY: 'auto',
        height: '100vh',
        padding: '20px 0 40px 0',
        maxWidth: isMobile ? '100vw' : '100%',
        display: 'flex',
        flex: '1 1 auto',
        flexDirection: 'column',
      }}
    >
      <Stack sx={{ width: isMobile ? '100vw' : '700px', maxWidth: '100%', alignSelf: 'center' }} spacing={3}>
        <Stack spacing={2}>
          <Typography variant="headline2">{intl.formatMessage({ id: 'common.label.address' })}</Typography>
          <TextField name="address" value={exactAddress.formattedAddress} onChange={() => {}} fullWidth disabled />
          <PropertyDetails />
          <DerivedSizeValues />
          <DerivedHeatWaterTankValue />
          <DerivedTemperatureValues postalCode={exactAddress.postalCode} />
          <UValues />
          <DwellingOpenings />
          <EstimationResult />
          <EstimationResultHeatLoss />
          <EstimationResultEnergyDemand />
        </Stack>
      </Stack>
    </Card>
  );
}
