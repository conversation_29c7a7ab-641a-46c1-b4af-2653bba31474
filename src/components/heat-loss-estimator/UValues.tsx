import { Box, MenuItem, Stack, Typography } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import React from 'react';
import { heatLossEstimatorValues } from './heatLossEstimatorValues';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';

function UValues() {
  const {
    uValueRoofCeilingGlazing,
    uValueRoofCeiling,
    aboveRoof,
    uValueWindows,
    uValueExternalWalls,
    uValueExternalDoors,
    floorType,
    uValueGroundFloor,
    setUValueRoofCeilingGlazing,
    setUValueRoofCeiling,
    setAboveRoof,
    setUValueWindows,
    setUValueExternalWalls,
    setUValueExternalDoors,
    setFloorType,
    setUValueGroundFloor,
  } = useEstimatorInputsStore();

  return (
    <Stack>
      <Typography variant="headline3" sx={{ mt: 4 }}>
        Main Building
      </Typography>

      <Box
        pt={3}
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, 340px)',
          gridGap: '1rem',
          justifyContent: 'space-between',
        }}
      >
        <Box>
          <Select
            name="uValueRoofCeilingGlazing"
            label="U Value Roof/Ceiling Glazing (W/m2K)"
            value={uValueRoofCeilingGlazing.toString()}
            onChange={(e) => setUValueRoofCeilingGlazing(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValuesWindows.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="uValueRoofCeiling"
            label="U Value Roof/Ceiling (W/m2K)"
            value={uValueRoofCeiling.toString()}
            onChange={(e) => setUValueRoofCeiling(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValuesRoof.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="aboveRoof"
            label="What is above Roof?"
            value={aboveRoof}
            onChange={(e) => setAboveRoof(e.target.value)}
            size="small"
          >
            {heatLossEstimatorValues.aboveRoof.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="uValueWindows"
            label="U Value Windows (W/m2K)"
            value={uValueWindows.toString()}
            onChange={(e) => setUValueWindows(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValuesWindows.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="uValueExternalWalls"
            label="U Value External Walls (W/m2K)"
            value={uValueExternalWalls.toString()}
            onChange={(e) => setUValueExternalWalls(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValuesExternalWall.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="uValueExternalDoors"
            label="U Value External Doors (W/m2K)"
            value={uValueExternalDoors.toString()}
            onChange={(e) => setUValueExternalDoors(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValuesDoors.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="floorType"
            label="Floor Type"
            value={floorType}
            onChange={(e) => setFloorType(e.target.value)}
            size="small"
          >
            {heatLossEstimatorValues.floorType.map((option) => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </Box>
        <Box>
          <Select
            name="uValueGroundFloor"
            label="U Value Ground Floor (W/m2K)"
            value={uValueGroundFloor.toString()}
            onChange={(e) => setUValueGroundFloor(parseFloat(e.target.value))}
            size="small"
          >
            {heatLossEstimatorValues.uValueFloor.map((option) => (
              <MenuItem key={option.name} value={option.value}>
                {option.display()}
              </MenuItem>
            ))}
          </Select>
        </Box>
      </Box>
    </Stack>
  );
}

export default UValues;
