import { Box, Stack, Typography } from '@mui/material';
import React from 'react';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { useEstimatorInputsStore } from './stores/EstimatorInputsStore';

function DwellingOpenings() {
  const {
    glazingToWallRatio,
    numExternalDoors,
    roofGlazingToRoofRatio,
    setGlazingToWallRatio,
    setNumExternalDoors,
    setRoofGlazingToRoofRatio,
  } = useEstimatorInputsStore();

  return (
    <Stack>
      <Typography variant="headline3" sx={{ mt: 4 }}>
        Dwelling Openings
      </Typography>
      <Box
        pt={3}
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, 340px)',
          gridGap: '1rem',
          justifyContent: 'space-between',
        }}
      >
        <Box>
          <NumericTextField
            name="glazingToWallRatio"
            label="Glazing to Wall Ratio (%)"
            value={glazingToWallRatio}
            onChange={(val) => setGlazingToWallRatio(val)}
            suffix="%"
            fullWidth
            size="small"
          />
        </Box>
        <Box>
          <NumericTextField
            name="numExternalDoors"
            label="Number of External Doors"
            value={numExternalDoors}
            type="number"
            onChange={(val) => setNumExternalDoors(val)}
            fullWidth
            size="small"
          />
        </Box>
        <Box>
          <NumericTextField
            name="roofGlazingToRoofRatio"
            label="Roof Glazing to Roof Ratio (%)"
            value={roofGlazingToRoofRatio}
            type="number"
            onChange={(val) => setRoofGlazingToRoofRatio(val)}
            suffix="%"
            fullWidth
            size="small"
          />
        </Box>
      </Box>
    </Stack>
  );
}

export default DwellingOpenings;
