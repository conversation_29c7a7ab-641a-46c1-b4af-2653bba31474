import { create } from 'zustand';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';

interface BundleCollectionState {
  editingCollectionIds: Set<string>;
  loadingCollectionIds: Set<string>;
  deletingCollectionIds: Set<string>;
  editingBundleIds: Set<string>;
  expandedBundleIds: Set<string>;
  loadingBundleIds: Set<string>;
  placeholderCollection: BundleCollection | undefined;
  placeholderBundles: Record<string, Bundle | undefined>; // Track placeholder bundles per collection
  movingCollectionIds: string[];

  // Actions
  setIsEditing: (collectionId: string, isEditing: boolean) => void;
  setIsLoading: (collectionId: string, isLoading: boolean) => void;
  setIsDeleting: (collectionId: string, isDeleting: boolean) => void;
  setIsBundleEditing: (bundleId: string, isEditing: boolean) => void;
  setIsBundleExpanded: (bundleId: string, isExpanded: boolean) => void;
  setIsBundleLoading: (bundleId: string, isLoading: boolean) => void;
  setPlaceholderCollection: (collection: BundleCollection | undefined) => void;
  setPlaceholderBundle: (collectionId: string, bundle: Bundle | undefined) => void;
  setMovingCollectionIds: (collectionIds: string[]) => void;
}

export const useBundleCollectionStore = create<BundleCollectionState>((set) => ({
  editingCollectionIds: new Set(),
  loadingCollectionIds: new Set(),
  deletingCollectionIds: new Set(),
  editingBundleIds: new Set(),
  expandedBundleIds: new Set(),
  loadingBundleIds: new Set(),
  placeholderCollection: undefined,
  placeholderBundles: {},
  movingCollectionIds: [],

  setIsEditing: (collectionId, isEditing) =>
    set((state) => {
      const newSet = new Set(state.editingCollectionIds);
      if (isEditing) {
        newSet.add(collectionId);
      } else {
        newSet.delete(collectionId);
      }
      return { editingCollectionIds: newSet };
    }),

  setIsLoading: (collectionId, isLoading) =>
    set((state) => {
      const newSet = new Set(state.loadingCollectionIds);
      if (isLoading) {
        newSet.add(collectionId);
      } else {
        newSet.delete(collectionId);
      }
      return { loadingCollectionIds: newSet };
    }),

  setIsDeleting: (collectionId, isDeleting) =>
    set((state) => {
      const newSet = new Set(state.deletingCollectionIds);
      if (isDeleting) {
        newSet.add(collectionId);
      } else {
        newSet.delete(collectionId);
      }
      return { deletingCollectionIds: newSet };
    }),

  setIsBundleEditing: (bundleId, isEditing) =>
    set((state) => {
      const newSet = new Set(state.editingBundleIds);
      if (isEditing) {
        newSet.add(bundleId);
      } else {
        newSet.delete(bundleId);
      }
      return { editingBundleIds: newSet };
    }),

  setIsBundleExpanded: (bundleId, isExpanded) =>
    set((state) => {
      const newSet = new Set(state.expandedBundleIds);
      if (isExpanded) {
        newSet.add(bundleId);
      } else {
        newSet.delete(bundleId);
      }
      return { expandedBundleIds: newSet };
    }),

  setIsBundleLoading: (bundleId, isLoading) =>
    set((state) => {
      const newSet = new Set(state.loadingBundleIds);
      if (isLoading) {
        newSet.add(bundleId);
      } else {
        newSet.delete(bundleId);
      }
      return { loadingBundleIds: newSet };
    }),

  setPlaceholderCollection: (collection) => set({ placeholderCollection: collection }),

  setPlaceholderBundle: (collectionId, bundle) =>
    set((state) => ({
      placeholderBundles: {
        ...state.placeholderBundles,
        [collectionId]: bundle,
      },
    })),

  setMovingCollectionIds: (collectionIds) => set({ movingCollectionIds: collectionIds }),
}));
