import React, { useEffect, useMemo, useState } from 'react';
import { Box, Stack } from '@mui/system';
import { Badge, Typography } from '@mui/material';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { Grid4AddOutlinedIcon } from '@ui/components/StandardIcons/Grid4AddOutlinedIcon';
import { TabButton, TabButtonsWrapper, TabPanel, TabPanelsWrapper } from '@ui/components/Tabs/Tabs';
import { VanOutlinedIcon } from '@ui/components/StandardIcons/VanOutlinedIcon';
import { beige, grey } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { PackageIcon } from '@ui/components/Icons/PackageIcon/PackageIcon';
import { StaticBundleCollectionCard } from './components/bundle-collection/StaticBundleCollectionCard';
import { api } from '../../utils/api';
import { BillOfMaterialsModals } from './BillOfMaterialsModals';
import { Identifier, STATIC_IDENTIFIERS } from './types';
import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { TemplateBundleCollectionCard } from './components/bundle-collection/TemplateBundleCollectionCard';
import { useBundleCollectionStore } from './stores/bundleCollectionStore';
import { Country } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';
import HeatPumpLoader from '../loaders/HeatPumpLoader';
import { ANIMATION_DURATION } from './constants/constants';
import { useCountry } from '../../hooks/useCountry';
import { ItemRelations } from './components/item-relations/ItemRelations';
import { ItemRelationsProvider } from './components/item-relations/ItemRelationsContext';
import { useErpItemsWithLabels } from '../../hooks/useErpItemsWithLabels';
import { AccountTreeIcon } from '@ui/components/Icons/AccountTreeIcon/AccountTreeIcon';
import { MessageKey } from 'messageType';
import { getCountryCodeFromCountry } from 'utils/marketConfigurations';
import { getTemplateBundleCollections } from 'components/heat-design/bill-of-materials/utils';

const BUNDLE_COLLECTION_ICONS: Record<Identifier, React.ReactNode> = {
  vanStock: <VanOutlinedIcon />,
  installationKit: <PackageIcon />,
  template: null,
};

function createPlaceholderBundleCollection(country: Country): BundleCollection {
  return {
    id: undefined,
    title: '',
    description: '',
    bundles: [],
    mandatory: true,
    details: {
      details: {
        $case: 'template',
        template: {
          displayOrder: 0,
        },
      },
    },
    country,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function BillOfMaterials() {
  const [activeTab, setActiveTab] = useState<string>('BundleCollections');
  const {
    placeholderCollection,
    setIsEditing,
    setIsLoading,
    setIsDeleting,
    setPlaceholderCollection,
    movingCollectionIds,
    setMovingCollectionIds,
  } = useBundleCollectionStore();
  const country = useCountry();
  const countryCode = getCountryCodeFromCountry(country);

  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const createBundleCollection = api.BillOfMaterials.createBundleCollection.useMutation();
  const updateBundleCollection = api.BillOfMaterials.updateBundleCollection.useMutation();
  const removeBundleCollection = api.BillOfMaterials.removeBundleCollection.useMutation();

  // Use the shared hook to get ERP items with labels and bundle information
  const { isLoading: areErpItemsPending } = useErpItemsWithLabels();

  const apiUtils = api.useUtils();
  const staticBundleCollections = useMemo(() => {
    if (bundleCollectionQuery.data) {
      return bundleCollectionQuery.data.bundleCollections
        .filter(
          (collection) =>
            !!collection.details?.details?.$case && STATIC_IDENTIFIERS.includes(collection.details.details?.$case),
        )
        .map((collection) => ({
          ...collection,
          icon: BUNDLE_COLLECTION_ICONS[collection.details?.details?.$case as Identifier],
        }));
    }
    return [];
  }, [bundleCollectionQuery.data]);

  const templateBundleCollections = useMemo(() => {
    return getTemplateBundleCollections(bundleCollectionQuery.data);
  }, [bundleCollectionQuery.data]);

  // We run this effect when templateBundleCollections changes to reset animations
  useEffect(() => {
    // Skip effect execution if there are no movingCollectionIds
    if (movingCollectionIds.length === 0) return;

    movingCollectionIds.forEach((collectionId) => {
      const card = document.querySelector(`#template-collection-wrapper-${collectionId}`) as HTMLElement;
      if (card) {
        card.style.transition = 'transform 0s';
        card.style.transform = `translateY(0px)`;
      }
    });
    setMovingCollectionIds([]);
  }, [movingCollectionIds, templateBundleCollections, setMovingCollectionIds]);

  function onAddBundleCollection() {
    setPlaceholderCollection(createPlaceholderBundleCollection(country));
    setIsEditing('new', true);
  }

  const handleCreateCollection = async (newCollection: BundleCollection) => {
    setIsLoading('new', true);

    try {
      let displayOrder = 1;
      if (templateBundleCollections.length > 0) {
        const maxOrder = templateBundleCollections.reduce((max, collection) => {
          const order =
            collection.details?.details?.$case === 'template'
              ? collection.details.details.template.displayOrder || 0
              : 0;
          return Math.max(max, order);
        }, 0);
        displayOrder = maxOrder + 1;
      }

      await createBundleCollection.mutateAsync({
        country,
        title: newCollection.title,
        description: newCollection.description,
        mandatory: newCollection.mandatory,
        details: {
          details: {
            $case: 'template',
            template: {
              displayOrder,
            },
          },
        },
      });

      await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    } finally {
      setIsLoading('new', false);
      setPlaceholderCollection(undefined);
    }
  };

  const handleUpdateCollection = async (updatedCollection: BundleCollection) => {
    const collectionId = updatedCollection.id?.value;
    if (!collectionId) return;

    setIsLoading(collectionId, true);

    try {
      await updateBundleCollection.mutateAsync({
        bundleCollectionId: collectionId,
        bundleCollection: {
          country,
          title: updatedCollection.title,
          description: updatedCollection.description,
          mandatory: updatedCollection.mandatory,
          details: updatedCollection.details,
        },
      });

      await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    } finally {
      setIsLoading(collectionId, false);
    }
  };

  const handleDeleteCollection = (collectionId: string) => async () => {
    setIsDeleting(collectionId, true);

    try {
      await removeBundleCollection.mutateAsync({
        bundleCollectionId: collectionId,
      });

      await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    } finally {
      setIsDeleting(collectionId, false);
    }
  };

  const handleMoveCollection = (collection: BundleCollection, direction: 'up' | 'down') => async () => {
    const collectionId = collection.id?.value;
    if (!collectionId) return;
    const currentOrder =
      collection.details?.details?.$case === 'template' ? collection.details.details.template.displayOrder || 0 : 0;

    const newOrder = direction === 'up' ? currentOrder - 1 : currentOrder + 1;
    const targetCollection = templateBundleCollections.find(
      (c) => c.details?.details?.$case === 'template' && c.details.details.template.displayOrder === newOrder,
    );

    if (!targetCollection || !targetCollection.id?.value) return;

    const targetCard = document.querySelector(
      `#template-collection-wrapper-${targetCollection.id.value}`,
    ) as HTMLElement;
    const movedCard = document.querySelector(`#template-collection-wrapper-${collectionId}`) as HTMLElement;

    if (!targetCard || !movedCard) return;

    try {
      setMovingCollectionIds([collectionId, targetCollection.id.value]);

      const targetBbox = targetCard.getBoundingClientRect();
      const movedBbox = movedCard.getBoundingClientRect();
      const gap = direction === 'up' ? movedBbox.top - targetBbox.bottom : targetBbox.top - movedBbox.bottom;

      const targetYTranslate = movedCard.offsetHeight + gap;
      const movedYTranslate = targetCard.offsetHeight + gap;
      targetCard.style.transition = `transform ${ANIMATION_DURATION}ms`;
      movedCard.style.transition = `transform ${ANIMATION_DURATION}ms`;
      targetCard.style.transform = `translateY(${direction === 'up' ? targetYTranslate : -targetYTranslate}px)`;
      movedCard.style.transform = `translateY(${direction === 'up' ? -movedYTranslate : movedYTranslate}px)`;

      // Wait for animations and updates to complete
      await Promise.all([
        new Promise((resolve) => setTimeout(resolve, ANIMATION_DURATION)),
        updateBundleCollection.mutateAsync({
          bundleCollectionId: targetCollection.id.value,
          bundleCollection: {
            country,
            title: targetCollection.title,
            description: targetCollection.description,
            mandatory: targetCollection.mandatory,
            details: {
              ...targetCollection.details,
              details: {
                $case: 'template',
                template: {
                  displayOrder: currentOrder,
                },
              },
            },
          },
        }),
        updateBundleCollection.mutateAsync({
          bundleCollectionId: collectionId,
          bundleCollection: {
            country,
            title: collection.title,
            description: collection.description,
            mandatory: collection.mandatory,
            details: {
              ...collection.details,
              details: {
                $case: 'template',
                template: {
                  displayOrder: newOrder,
                },
              },
            },
          },
        }),
      ]);

      // Invalidate after all operations are complete
      await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    } catch (error) {
      console.error('Error moving collection:', error);
    }
  };

  return (
    <>
      {areErpItemsPending ? (
        <HeatPumpLoader />
      ) : (
        <Stack gap={2} data-testid="bill-of-materials-page">
          <Stack gap={1}>
            <Stack direction="row" alignItems="center" gap={2}>
              <Typography variant="headline2">Admin: Bill of Materials</Typography>
              <Badge
                sx={{
                  backgroundColor: grey[200],
                  px: 1,
                  borderRadius: 1,
                  height: '2rem',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <FormattedMessage id={`common.country.${countryCode}` as MessageKey} />
              </Badge>
            </Stack>
            <Typography variant="body1">
              This is the panel where the countries&#39; different bundles will be configured.
            </Typography>
          </Stack>
          <Box>
            <TabButtonsWrapper gap={1}>
              <TabButton
                isSelected={activeTab === 'BundleCollections'}
                onClick={() => setActiveTab('BundleCollections')}
              >
                <Stack gap={1} alignItems="center" direction="row">
                  <Grid4AddOutlinedIcon />
                  <Typography variant="body2">
                    <FormattedMessage id="billOfMaterials.bundleCollections" />
                  </Typography>
                </Stack>
              </TabButton>
              <TabButton isSelected={activeTab === 'ItemRelations'} onClick={() => setActiveTab('ItemRelations')}>
                <Stack gap={1} alignItems="center" direction="row">
                  <AccountTreeIcon />
                  <Typography variant="body2">
                    <FormattedMessage id="billOfMaterials.itemRelations" />
                  </Typography>
                </Stack>
              </TabButton>
            </TabButtonsWrapper>
            <TabPanelsWrapper sx={{ borderRadius: '0 24px 24px 24px' }}>
              <TabPanel isSelected={activeTab === 'BundleCollections'}>
                <Stack
                  sx={{
                    backgroundColor: beige[100],
                    marginBottom: '48px',
                    paddingBottom: '48px',
                    borderBottom: '2px solid #2222261F',
                  }}
                  gap={2}
                  data-testid="static-bundle-collections"
                >
                  {staticBundleCollections.map((collection) => (
                    <StaticBundleCollectionCard
                      key={collection?.id?.value}
                      bundleCollection={collection}
                      icon={collection.icon}
                    />
                  ))}
                </Stack>
                <Stack sx={{ backgroundColor: beige[100] }} gap={2} data-testid="template-bundle-collections">
                  <Stack sx={{ height: '40px' }} direction="row" alignItems="center" justifyContent="space-between">
                    <Typography variant="headline3">
                      <FormattedMessage id="billOfMaterials.bundleCollections" />
                    </Typography>
                    <Button
                      onClick={() => onAddBundleCollection()}
                      sx={{ borderRadius: '16px', height: '100%' }}
                      variant="contained"
                      color="inherit"
                      data-testid="add-bundle-collection-button"
                    >
                      <Stack direction="row" alignItems="center" justifyContent="space-between" gap={1}>
                        <AddOutlinedIcon />
                        <Typography variant="body2Emphasis">
                          <FormattedMessage id="billOfMaterials.addBundleCollection" />
                        </Typography>
                      </Stack>
                    </Button>
                  </Stack>
                  {placeholderCollection && (
                    <TemplateBundleCollectionCard
                      bundleCollection={placeholderCollection}
                      onCreate={handleCreateCollection}
                      onUpdate={async () => {}}
                      onDelete={async () => {}}
                      onMoveUp={async () => {}}
                      onMoveDown={async () => {}}
                      onClose={() => setPlaceholderCollection(undefined)}
                      isFirst={true}
                      isLast={true}
                    />
                  )}
                  {templateBundleCollections.map((collection, index) => {
                    const collectionId = collection.id?.value;
                    if (!collectionId) return null;

                    return (
                      <Stack
                        direction="row"
                        gap={2}
                        key={collectionId}
                        sx={{
                          position: 'relative',
                          '.MuiStack-root[data-testid^="template-collection-wrapper-"]': {
                            transition: `all ${ANIMATION_DURATION}ms ease-in-out`,
                            width: '100%',
                          },
                        }}
                      >
                        <TemplateBundleCollectionCard
                          bundleCollection={collection}
                          onUpdate={handleUpdateCollection}
                          onDelete={handleDeleteCollection(collectionId)}
                          onMoveUp={handleMoveCollection(collection, 'up')}
                          onMoveDown={handleMoveCollection(collection, 'down')}
                          isFirst={index === 0}
                          isLast={index === templateBundleCollections.length - 1}
                        />
                      </Stack>
                    );
                  })}
                </Stack>
              </TabPanel>
              <TabPanel isSelected={activeTab === 'ItemRelations'}>
                <ItemRelationsProvider>
                  <ItemRelations />
                </ItemRelationsProvider>
              </TabPanel>
            </TabPanelsWrapper>
          </Box>
          <BillOfMaterialsModals />
        </Stack>
      )}
    </>
  );
}
