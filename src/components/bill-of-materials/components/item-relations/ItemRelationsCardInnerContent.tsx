import { memo, useState } from 'react';
import { Box, CircularProgress, IconButton, Stack, Typography } from '@mui/material';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { surface } from '@ui/theme/colors';
import { getFormattedItemPrice } from '../../utils';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import { Item } from '../../types';
import { api } from '../../../../utils/api';
import { useIntl } from 'react-intl';
import { useRouter } from 'next/router';
import { COLUMN_WIDTHS } from './ItemRelationsTable';

export type ItemRelationsCardInnerContentProps = {
  equivalentItems: Item[];
};

function ItemRelationsCardInnerContentComponent({ equivalentItems }: ItemRelationsCardInnerContentProps) {
  const [loadingItemDeletions, setLoadingItemDeletions] = useState<Set<string>>(new Set());
  const [deleteAnchorEl, setDeleteAnchorEl] = useState<HTMLElement | null>(null);
  const [itemToDelete, setItemToDelete] = useState<Item | null>(null);
  const removeItemsFromEquivalenceGroup = api.BillOfMaterials.removeItemsFromEquivalenceGroup.useMutation();
  const utils = api.useUtils();
  const { formatMessage } = useIntl();
  const { locale } = useRouter();

  const splitColumnWidths = COLUMN_WIDTHS.split(' ');
  const mainColumnWidths = (splitColumnWidths[0] ? [splitColumnWidths[0], '1fr'] : ['1fr']).join(' ');
  const innerColumnWidths = splitColumnWidths.slice(1).join(' ');

  const clearEquivalenceGroup = async () => {
    if (!itemToDelete) {
      return;
    }
    setDeleteAnchorEl(null);
    setLoadingItemDeletions((prev) => {
      const newSet = new Set(prev);
      newSet.add(itemToDelete.id.value);
      return newSet;
    });
    await removeItemsFromEquivalenceGroup.mutateAsync({
      itemIds: [itemToDelete.id.value],
    });
    await utils.BillOfMaterials.getItems.invalidate();
    setItemToDelete(null);
    setLoadingItemDeletions((prev) => {
      const newSet = new Set(prev);
      newSet.delete(itemToDelete.id.value);
      return newSet;
    });
  };

  return (
    <Stack onClick={(e) => e.stopPropagation()} gap={1} data-testid="item-relations-card-inner-content-container">
      {equivalentItems.map((item) => {
        return (
          <Box
            data-testid="equivalent-item-card"
            sx={{
              gridTemplateColumns: mainColumnWidths,
              width: '100%',
              display: 'grid',
            }}
            key={item.id.value}
          >
            <Stack alignItems="center" justifyContent="center">
              <IconButton
                data-testid="delete-item-from-equivalency-group-button"
                onClick={(e) => {
                  setDeleteAnchorEl(e.target as HTMLButtonElement);
                  setItemToDelete(item);
                  e.stopPropagation();
                }}
                sx={{ padding: '8px' }}
              >
                {loadingItemDeletions.has(item.id.value) ? <CircularProgress size={20} /> : <BinOutlinedIcon />}
              </IconButton>
            </Stack>

            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: innerColumnWidths,
                alignItems: 'center',
                borderRadius: '16px',
                marginRight: '8px',
                backgroundColor: surface[100],
              }}
            >
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.erpId}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.category}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.description}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.quantity}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.unit}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {getFormattedItemPrice(item, locale)}
              </Typography>
              <Typography sx={{ padding: '0 8px' }} variant="body2">
                {item.label?.label}
              </Typography>
              {/*The empty div here to fill the last column so layout aligns with ERP card*/}
              <div></div>
            </Box>
          </Box>
        );
      })}
      <ConfirmationPopover
        anchorEl={deleteAnchorEl}
        open={!!deleteAnchorEl}
        onClose={() => setDeleteAnchorEl(null)}
        title={formatMessage({
          id: 'billOfMaterials.clearInterchangeable.confirmTitle',
        })}
        description={formatMessage({
          id: 'billOfMaterials.clearInterchangeable.confirmTitle',
        })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={() => clearEquivalenceGroup()}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        testId="delete-bundle-confirmation-popover"
      />
    </Stack>
  );
}

export const ItemRelationsCardInnerContent = memo(ItemRelationsCardInnerContentComponent);
