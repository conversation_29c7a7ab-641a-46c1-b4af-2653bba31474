import { createContext, Dispatch, ReactNode, SetStateAction, useContext, useMemo, useState } from 'react';
import { Item } from '../../types';
import { createSearchIndex, emptyItemFilter, filterItems, ItemFilter } from './item-filter';
import { useErpItemsWithLabels } from 'hooks/useErpItemsWithLabels';

type ContextProviderProps = {
  setItemFilter: Dispatch<SetStateAction<ItemFilter>>;
  itemFilter: ItemFilter;
  filteredItems: Item[];
  selectedItemIds: Set<string>;
  setSelectedItemIds: Dispatch<SetStateAction<Set<string>>>;
};

export const ItemRelationsContext = createContext<ContextProviderProps>({
  setItemFilter: () => undefined,
  itemFilter: emptyItemFilter(),
  filteredItems: [],
  selectedItemIds: new Set(),
  setSelectedItemIds: () => undefined,
});

export function ItemRelationsProvider({ children, selectedIds }: { children: ReactNode; selectedIds?: Set<string> }) {
  const [itemFilter, setItemFilter] = useState<ItemFilter>(emptyItemFilter());
  const [selectedItemIds, setSelectedItemIds] = useState<Set<string>>(selectedIds ?? new Set());
  const { items: erpItems } = useErpItemsWithLabels();
  const searchIndex = useMemo(() => {
    const activeItems = erpItems.filter((item) => !item.archivedAt);
    return createSearchIndex(activeItems);
  }, [erpItems]);

  const context = useMemo(
    () => ({
      selectedItemIds,
      setSelectedItemIds,
      itemFilter,
      setItemFilter,
      filteredItems: filterItems(itemFilter, searchIndex),
    }),
    [selectedItemIds, itemFilter, searchIndex],
  );

  return <ItemRelationsContext.Provider value={context}>{children}</ItemRelationsContext.Provider>;
}

export function useItemRelationsContext() {
  const context = useContext(ItemRelationsContext);
  return context;
}
