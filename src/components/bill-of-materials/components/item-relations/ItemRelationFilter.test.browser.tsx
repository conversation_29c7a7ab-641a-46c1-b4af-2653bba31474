import { cleanup, screen } from '@testing-library/react';
import { afterEach } from 'vitest';
import { billOfMaterialsTestSetup } from '../../test-utils';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { ItemRelationsProvider } from './ItemRelationsContext';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { ItemRelationsFilter } from './ItemRelationsFilter';

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

function renderItemRelationFilters(options?: { selectedIds?: Set<string> }) {
  renderWithProviders(
    <AiraThemeProvider theme={heatDesignTheme}>
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ItemRelationsProvider selectedIds={options?.selectedIds ?? new Set()}>
          <ItemRelationsFilter />
        </ItemRelationsProvider>
      </IntlProvider>
    </AiraThemeProvider>,
  );
}

describe('ItemRelationActions', () => {
  it('should render component', async () => {
    renderItemRelationFilters();
    const container = await screen.findByTestId('item-relations-filter-container');
    expect(container).toBeInTheDocument();
  });
  it('should render the label selector and the query input', async () => {
    renderItemRelationFilters();
    const labelSelector = await screen.findByTestId('item-relations-label-filter');
    const queryInput = await screen.findByTestId('item-relations-search');
    expect(labelSelector).toBeInTheDocument();
    expect(queryInput).toBeInTheDocument();
  });
});
