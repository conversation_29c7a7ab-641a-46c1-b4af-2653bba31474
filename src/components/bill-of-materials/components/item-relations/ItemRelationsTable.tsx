import { useItemRelationsContext } from './ItemRelationsContext';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { Item } from '../../types';
import { ErpItemCard } from '../common/ErpItemCard';
import { useCallback, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { IconButton, Stack, Typography } from '@mui/material';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import { beige } from '@ui/theme/colors';
import { keyIsNotNullish } from '../../../../utils/isNotNullish';
import { ItemRelationsCardInnerContent } from './ItemRelationsCardInnerContent';

export const COLUMN_WIDTHS = '50px 100px 150px 1fr 100px 100px 150px 150px 100px';

export type ItemRelationsTableProps = {
  items: Item[];
  isSelectionLimited?: boolean;
  // Used to differentiate between main and actions table for testing purposes.
  tableTestId: 'main' | 'actions';
};

export function ItemRelationsTable({ items, isSelectionLimited, tableTestId }: ItemRelationsTableProps) {
  const { setSelectedItemIds, selectedItemIds } = useItemRelationsContext();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const onRowClick = (item: Item, hasWarning?: boolean) => {
    if (!item.id) {
      return;
    }
    if (selectedItemIds.has(item.id.value)) {
      selectedItemIds.delete(item.id.value);
    } else {
      if (hasWarning) {
        return;
      }
      selectedItemIds.add(item.id.value);
    }
    setSelectedItemIds(new Set(selectedItemIds));
  };

  const itemsByEquivalenceGroup = useMemo(() => {
    const itemsWithEquivalenceGroups = items.filter(keyIsNotNullish('equivalenceGroupId'));
    return Object.groupBy(itemsWithEquivalenceGroups, (i) => i.equivalenceGroupId.value);
  }, [items]);

  const rowElement = ({ item }: { item: Item }) => {
    const isSelected = selectedItemIds.has(item.id.value);
    const equivalenceGroupId = item.equivalenceGroupId?.value;
    const equivalentItems = ((equivalenceGroupId ? itemsByEquivalenceGroup[equivalenceGroupId] : []) ?? []).filter(
      (i) => i.id.value !== item.id.value,
    );

    const onExpandedChange = (item: Item) => {
      return (value: boolean) => {
        setExpandedItems((prev) => {
          const newSet = new Set(prev);
          if (value) {
            newSet.add(item.id.value);
          } else {
            newSet.delete(item.id.value);
          }
          return newSet;
        });
      };
    };

    return (
      <ErpItemCard
        sx={{
          '.MuiAccordionDetails-root': {
            padding: '8px 0',
          },
        }}
        item={item}
        columnWidths={COLUMN_WIDTHS}
        isSelected={isSelected}
        onRowClick={onRowClick}
        isSelectionLimited={isSelectionLimited}
        isExpanded={expandedItems.has(item.id.value)}
        setIsExpanded={onExpandedChange(item)}
      >
        <ItemRelationsCardInnerContent equivalentItems={equivalentItems} />
      </ErpItemCard>
    );
  };

  const headerTypographyGetter = useCallback(
    (text: MessageDescriptor['id']) => (
      <Typography fontWeight="500" variant="body2">
        <FormattedMessage id={text} />
      </Typography>
    ),
    [],
  );
  const columns = useMemo<ColumnDef<Item>[]>(
    () => [
      {
        header: () => (
          <IconButton
            data-testid="select-all-button"
            sx={{
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              padding: 0,
            }}
            disabled={items.length === 0}
            onClick={() => {
              if (selectedItemIds.size === items.length) {
                setSelectedItemIds(new Set());
              } else {
                setSelectedItemIds(new Set(items.map((item) => item.id.value)));
              }
            }}
          >
            <CheckCircleFilledIcon />
          </IconButton>
        ),
        id: 'isSelected',
        enableSorting: false,
        accessorKey: 'isSelected',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.erpId'),
        accessorKey: 'erpId',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.category'),
        accessorKey: 'category',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.description'),
        accessorKey: 'description',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.quantity'),
        accessorKey: 'amount',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.unit'),
        accessorKey: 'unit',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.costPerUnit'),
        accessorKey: 'minorPrice',
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.label'),
        accessorKey: 'labelId',
      },
      {
        id: 'expand',
        header: '',
        accessorKey: '',
        enableSorting: false,
      },
    ],
    [headerTypographyGetter, selectedItemIds, setSelectedItemIds, items],
  );
  return (
    <Stack
      data-testid={`item-relations-table-${tableTestId}`}
      sx={{ position: 'relative', flex: '1', maxHeight: '800px', height: '100%' }}
    >
      <TanstackTable
        styles={{
          columnWidths: COLUMN_WIDTHS,
          bodyColumnWidths: '1fr',
          tableContainer: () => ({
            padding: '0 8px',
            height: '100%',
            minHeight: '800px',
          }),
          tableHeadRow: () => ({
            backgroundColor: beige[100],
          }),
          tableBodyRow: () => ({
            paddingBottom: '8px',
          }),
          tableHeadCell: () => ({
            border: 'none',
          }),
        }}
        columns={columns}
        customRowRenderer={(item) => rowElement({ item })}
        data={items}
        virtualizationOptions={{
          measureElement:
            typeof window !== 'undefined' && navigator.userAgent.indexOf('Firefox') === -1
              ? (element) => element?.getBoundingClientRect().height
              : undefined,
          estimateSize: () => 53,
        }}
      />
    </Stack>
  );
}
