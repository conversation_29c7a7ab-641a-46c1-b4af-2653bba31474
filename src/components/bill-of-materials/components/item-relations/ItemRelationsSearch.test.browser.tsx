import { cleanup, fireEvent, screen, waitFor } from '@testing-library/react';
import { afterEach } from 'vitest';
import { billOfMaterialsTestSetup, fillAdvancedSearch } from '../../test-utils';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { ItemRelationsProvider, useItemRelationsContext } from './ItemRelationsContext';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { ItemRelationsSearch } from './ItemRelationsSearch';
import { useState } from 'react';
import userEvent from '@testing-library/user-event';
import { SearchQuery } from './item-filter';

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

function ItemRelationsSearchWrapper() {
  const [query, setQuery] = useState<SearchQuery>({ $case: 'by-free-text', query: '' });
  const { filteredItems } = useItemRelationsContext();
  // We need to wait for the initialisation of the items until we can render the search
  // Otherwise we get flaky tests (the filteredItems starts as an empty array and then gets populated)
  if (!filteredItems.length) {
    return null;
  }
  return <ItemRelationsSearch query={query} setQuery={setQuery} />;
}

async function renderItemRelationSearch(options?: { selectedIds?: Set<string> }) {
  renderWithProviders(
    <AiraThemeProvider theme={heatDesignTheme}>
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ItemRelationsProvider selectedIds={options?.selectedIds ?? new Set()}>
          <ItemRelationsSearchWrapper />
        </ItemRelationsProvider>
      </IntlProvider>
    </AiraThemeProvider>,
  );
  await waitFor(() => {
    expect(screen.getByTestId('item-relations-search')).toBeInTheDocument();
  });
}

describe('ItemRelationsSearch', () => {
  it('should render component', async () => {
    await renderItemRelationSearch();
    const container = await screen.findByTestId('item-relations-search');
    expect(container).toBeInTheDocument();
  });
  it('should be possible to input a basic query', async () => {
    await renderItemRelationSearch();
    const textField = await screen.findByTestId('item-relations-search-input-input');
    await userEvent.click(textField);
    expect(textField.querySelector('input')).toHaveFocus();
    await userEvent.type(textField, 'test query');
    expect(textField.querySelector('input')).toHaveValue('test query');
  });
  it('should be possible to search erpItems with advanced search', async () => {
    await renderItemRelationSearch();
    await fillAdvancedSearch('ERP001\nERP002\nERP999');
    const successResults = await screen.findAllByTestId('advanced-search-result-icon-check');
    const failureResults = await screen.findAllByTestId('advanced-search-result-icon-cross');
    expect(successResults.length).toBe(2); // Assuming ERP001 and ERP002 exist in the mocked data
    expect(failureResults.length).toBe(1); // ERP999 does not exist in the mocked data
  });
  it('should cancel advanced search with outside click', async () => {
    await renderItemRelationSearch();
    fireEvent.click(await screen.findByTestId('advanced-search-label'));
    const textField = await screen.findByTestId('item-relations-advanced-search-input');
    await waitFor(() => {
      expect(textField).toBeVisible();
    });
    fireEvent.click(document.body); // Click outside to close the advanced search
    await new Promise((resolve) => setTimeout(resolve, 300)); // Wait for animation to complete
    expect(textField).not.toBeVisible();
  });
});
