import { IconButton, InputAdornment, MenuItem, Stack, Typography } from '@mui/material';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { Select } from '@ui/components/Select/Select';
import { grey } from '@ui/theme/colors';
import useItemLabels from 'components/bill-of-materials/hooks/useItemLabels';
import { useCountry } from 'hooks/useCountry';
import { memo, useMemo } from 'react';
import { useIntl } from 'react-intl';

interface Props {
  selectedLabelIds: Set<string>;
  setSelectedLabelIds: (ids: Set<string>) => void;
}

const MemoizedCheckbox = memo(Checkbox, (prevProps, nextProps) => prevProps.checked === nextProps.checked);

export function ItemRelationsLabelFilter({ selectedLabelIds, setSelectedLabelIds }: Props) {
  const country = useCountry();
  const { labels } = useItemLabels({ countries: [country] });
  const { formatMessage } = useIntl();

  const selectedLabelNames = useMemo(() => {
    const allLabels = new Set<string>();
    labels.forEach((label) => {
      if (label.id?.value && selectedLabelIds.has(label.id.value)) {
        allLabels.add(label.label);
      }
    });
    return Array.from(allLabels).join(', ');
  }, [labels, selectedLabelIds]);

  return (
    <Select
      sx={{
        '.MuiSelect-select': { padding: '8px' },
        width: '200px',
      }}
      data-testid="item-relations-label-filter"
      multiple
      onChange={(event) => {
        setSelectedLabelIds(
          new Set(Array.isArray(event.target.value) ? event.target.value : event.target.value.split(',')),
        );
      }}
      renderValue={() => (
        <Typography noWrap whiteSpace="nowrap">
          {selectedLabelNames}
        </Typography>
      )}
      value={Array.from(selectedLabelIds)}
      label={formatMessage({ id: 'billOfMaterials.labels.selectLabel' })}
      name="Label"
      error={false}
      endAdornment={
        selectedLabelIds.size > 0 && (
          <InputAdornment sx={{ marginRight: '10px' }} position="end">
            <IconButton
              onClick={() => {
                setSelectedLabelIds(new Set());
              }}
            >
              <CloseIcon height={14} width={14} color={grey[900]} />
            </IconButton>
          </InputAdornment>
        )
      }
    >
      {labels.map(
        (label) =>
          label.id?.value && (
            <MenuItem key={label.id.value} value={label.id.value} data-testid={`label-select-option-${label.label}`}>
              <Stack direction="row">
                <MemoizedCheckbox
                  labelSx={{
                    '> .MuiBox-root': {
                      width: '20px',
                      height: '20px',
                      marginRight: '4px',
                    },
                  }}
                  label={label.label}
                  checked={selectedLabelIds.has(label.id.value)}
                  onChange={() => {}}
                />
              </Stack>
            </MenuItem>
          ),
      )}
    </Select>
  );
}
