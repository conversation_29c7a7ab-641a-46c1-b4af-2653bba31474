import { cleanup, fireEvent, screen, waitFor, within } from '@testing-library/react';
import { afterEach } from 'vitest';
import { billOfMaterialsTestSetup, EQUIVALENCE_GROUP_ID } from '../../test-utils';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { ItemRelationsProvider } from './ItemRelationsContext';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { ItemRelationsCardInnerContent } from './ItemRelationsCardInnerContent';
import userEvent from '@testing-library/user-event';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

function ItemRelationsCardInnerContentWrapper() {
  const { erpItems } = useErpItems();
  const items = Object.values(erpItems).filter((item) => item.equivalenceGroupId?.value === EQUIVALENCE_GROUP_ID.value);
  if (!items) {
    return <div>No item with equivalency group found</div>;
  }
  return <ItemRelationsCardInnerContent equivalentItems={items} />;
}

function renderItemRelationsCardInnerContent() {
  renderWithProviders(
    <AiraThemeProvider theme={heatDesignTheme}>
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ItemRelationsProvider>
          <ItemRelationsCardInnerContentWrapper />
        </ItemRelationsProvider>
      </IntlProvider>
    </AiraThemeProvider>,
  );
}

describe('ItemRelationActions', () => {
  it('should render component', async () => {
    renderItemRelationsCardInnerContent();
    const container = await screen.findByTestId('item-relations-card-inner-content-container');
    expect(container).toBeInTheDocument();
  });
  it('should remove item from equivalence group when hitting delete button', async () => {
    renderItemRelationsCardInnerContent();
    const items = await screen.findAllByTestId('equivalent-item-card');
    const originalItemLength = items.length;
    const deleteButton = await within(items[0]!).findByTestId('delete-item-from-equivalency-group-button');
    await userEvent.click(deleteButton);
    const confirmButton = await screen.findByTestId('confirm-delete-bundle-confirmation-popover');
    fireEvent.click(confirmButton);
    await waitFor(async () => {
      const items = await screen.findAllByTestId('equivalent-item-card');
      expect(items.length).toEqual(originalItemLength - 1);
    });
  });
});
