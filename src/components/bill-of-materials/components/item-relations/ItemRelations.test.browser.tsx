import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import {
  billOfMaterialsTestSetup,
  EQUIVALENCE_GROUP_ID,
  ERP_ID1,
  ERP_ID2,
  fillAdvancedSearch,
  LABEL_ID2,
} from '../../test-utils';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { ItemRelationsProvider } from './ItemRelationsContext';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { ItemRelations } from './ItemRelations';
import userEvent from '@testing-library/user-event';

const backendState = billOfMaterialsTestSetup();

function renderItemRelations() {
  renderWithProviders(
    <AiraThemeProvider theme={heatDesignTheme}>
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ItemRelationsProvider>
          <ItemRelations />
        </ItemRelationsProvider>
      </IntlProvider>
    </AiraThemeProvider>,
  );
}

async function selectItem(itemId: string) {
  const itemRelationsTable = await screen.findByTestId('item-relations-table-main');
  const row = await within(itemRelationsTable).findByTestId(`erp-item-card-${itemId}`);
  await userEvent.click(row);
}

describe('ItemRelations', () => {
  it('should render component', async () => {
    renderItemRelations();
    const container = await screen.findByTestId('item-relations-container');
    expect(container).toBeInTheDocument();
  });
  it('should render child components', async () => {
    renderItemRelations();
    const filter = await screen.findByTestId('item-relations-filter-container');
    const mainTable = await screen.findByTestId('item-relations-table-main');
    const actionsTable = await screen.findByTestId('item-relations-table-actions');
    const actions = await screen.findByTestId('item-relations-actions-container');
    expect(filter).toBeInTheDocument();
    expect(mainTable).toBeInTheDocument();
    expect(actionsTable).toBeInTheDocument();
    expect(actions).toBeInTheDocument();
  });
  it('should filter table items based on selected label', async () => {
    renderItemRelations();
    let rows = await screen.findAllByTestId(/table-body-row/);
    const labelSelect = (await screen.findByTestId('item-relations-label-filter')).querySelector('.MuiSelect-select');
    await userEvent.click(labelSelect!);
    const labelOptions = await screen.findAllByTestId(/label-select-option/);
    expect(labelOptions.length).toBeGreaterThan(0);
    fireEvent.click(labelOptions[0]!);
    rows = await screen.findAllByTestId(/table-body-row/);
    expect(rows.length).toEqual(1);
  });
  it('should filter table items based on search query', async () => {
    renderItemRelations();
    let rows = await screen.findAllByTestId(/table-body-row/);
    const searchInput = (await screen.findByTestId('item-relations-search-input')).querySelector('input');
    fireEvent.change(searchInput!, { target: { value: 'Test Erp Item 1' } });
    await waitFor(async () => {
      rows = await screen.findAllByTestId(/table-body-row/);
      expect(rows.length).toEqual(1);
    });
  });
  it('should filter table items based on advanced search', async () => {
    renderItemRelations();
    let rows = await screen.findAllByTestId(/table-body-row/);
    await fillAdvancedSearch('ERP001\nERP002\nERP999');
    await waitFor(async () => {
      rows = await screen.findAllByTestId(/table-body-row/);
      expect(rows.length).toEqual(3);
    });
  });
  it('should bulk-set label of selected items', async () => {
    renderItemRelations();

    await selectItem(ERP_ID1);
    await selectItem(ERP_ID2);

    const expandBtn = await screen.findByTestId('item-relations-actions-expand-button');
    fireEvent.click(expandBtn);
    const actionsContainer = await screen.findByTestId('item-relations-actions-container');

    const bulkLabelSelect = (await within(actionsContainer).findByTestId('label-select')).querySelector(
      '.MuiInputBase-root',
    )!;
    await userEvent.click(bulkLabelSelect);

    // choose the second label (“Label 2”)
    const options = await screen.findAllByTestId('label-select-option');
    const label2Option = options.find((o) => o.textContent === 'Label 2')!;
    await userEvent.click(label2Option);

    await waitFor(async () => {
      const updatedLabels = await within(await screen.findByTestId('item-relations-table-main')).findAllByTestId(
        `item-label-${LABEL_ID2}`,
      );
      expect(updatedLabels).toHaveLength(2);
    });
  });
  it('should bulk-set item equivalency groups of selected items', async () => {
    renderItemRelations();
    await selectItem(ERP_ID1);
    await selectItem(ERP_ID2);

    const actionsContainer = await screen.findByTestId('item-relations-actions-container');
    const equivalencyGroupButton = await within(actionsContainer).findByTestId('set-equivalency-group-button');
    fireEvent.click(equivalencyGroupButton);
    const confirmButton = await screen.findByTestId('confirm-set-interchangeable-popover');
    fireEvent.click(confirmButton);
    // We don't currently display the equivalency group in the UI, so for now just confirming the effects on backend
    await waitFor(async () => {
      const items = backendState.current.getItems().items;
      const item1 = items.find((item) => item.id.value === ERP_ID1);
      const item2 = items.find((item) => item.id.value === ERP_ID2);
      // These items should now have a new equivalency group ID, not the same as before
      const equivalenceGroupId1 = item1?.equivalenceGroupId?.value;
      const equivalenceGroupId2 = item2?.equivalenceGroupId?.value;
      expect(equivalenceGroupId1).toEqual(equivalenceGroupId2);
      expect(equivalenceGroupId1).not.toEqual(EQUIVALENCE_GROUP_ID);
    });
  });
  it('should bulk-clear equivalency groups of selected items', async () => {
    renderItemRelations();
    await selectItem(ERP_ID1);
    await selectItem(ERP_ID2);

    const actionsContainer = await screen.findByTestId('item-relations-actions-container');
    const removeEquivalencyGroupButton = await within(actionsContainer).findByTestId('clear-equivalency-group-button');
    fireEvent.click(removeEquivalencyGroupButton);
    const confirmButton = await screen.findByTestId('confirm-clear-interchangeable-popover');
    fireEvent.click(confirmButton);
    await waitFor(async () => {
      const items = backendState.current.getItems().items;
      const item1 = items.find((item) => item.id.value === ERP_ID1);
      const item2 = items.find((item) => item.id.value === ERP_ID2);

      expect(item1?.equivalenceGroupId).not.toBeDefined();
      expect(item2?.equivalenceGroupId).not.toBeDefined();
    });
  });
});
