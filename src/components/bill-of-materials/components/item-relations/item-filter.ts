import { Item } from 'components/bill-of-materials/types';
import Fuse, { IFuseOptions } from 'fuse.js';

// Model for item filter

export type SearchQuery =
  | {
      $case: 'by-free-text';
      query: string;
    }
  | {
      $case: 'by-erp-ids';
      erpIds: Set<string>;
    };

export type ItemFilter = {
  searchQuery: SearchQuery;
  selectedLabelIds: Set<string>;
};

export function emptyItemFilter(): ItemFilter {
  return {
    searchQuery: { $case: 'by-free-text', query: '' },
    selectedLabelIds: new Set(),
  };
}

// Search engine

export type SearchIndex = {
  items: Item[];
  fuse: Fuse<Item>;
};

const QUERY_FIELDS = ['category', 'supplier', 'supplierId', 'description', 'label.label'];
const ID_FIELDS = ['erpId'];
const fuseOptions: IFuseOptions<Item> = {
  includeScore: true,
  keys: [...QUERY_FIELDS, ...ID_FIELDS],
  ignoreDiacritics: true,
  shouldSort: true,
  threshold: 0.3, // Adjust this number to make the fuzziness more or less sensitive
  useExtendedSearch: true,
};

export function createSearchIndex(items: Item[]): SearchIndex {
  const fuse = new Fuse(items, fuseOptions);
  return { items, fuse };
}

function fuzzySearch(queryString: string, searchIndex: SearchIndex) {
  if (!queryString.trim()) {
    // If the query is empty, return all items
    return searchIndex.items;
  }
  // We either want to hit a fuzzy-search on any of the query fields, OR search ID_FIELDS
  // by them *containing* the query (the single quote ' symbol is used for this)

  const searchQuery = {
    $or: [
      ...QUERY_FIELDS.map((field) => {
        return {
          [field]: queryString,
        };
      }),
      ...ID_FIELDS.map((field) => {
        return {
          [field]: `'${queryString}'`,
        };
      }),
    ],
  };
  return searchIndex.fuse.search(searchQuery).map((result) => result.item);
}

function invalidCase(value: unknown): never {
  throw new Error(`Invalid case: ${JSON.stringify(value)}`);
}

export function filterBySearchQuery(searchQuery: SearchQuery, searchIndex: SearchIndex): Item[] {
  const $case = searchQuery.$case;
  switch ($case) {
    case 'by-free-text':
      return fuzzySearch(searchQuery.query, searchIndex);
    case 'by-erp-ids':
      return Object.values(searchIndex.items).filter((item) => {
        return searchQuery.erpIds.has(item.erpId);
      });
    default:
      return invalidCase($case);
  }
}

function filterByLabelIds(selectedLabelIds: Set<string>, items: Item[]): Item[] {
  if (selectedLabelIds.size === 0) {
    return items;
  }
  return items.filter((item) => {
    return !!item.label?.id?.value && selectedLabelIds.has(item.label.id.value);
  });
}

export function filterItems({ searchQuery, selectedLabelIds }: ItemFilter, searchIndex: SearchIndex): Item[] {
  return filterByLabelIds(selectedLabelIds, filterBySearchQuery(searchQuery, searchIndex));
}
