import { Divide<PERSON>, I<PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material';
import { useItemRelationsContext } from './ItemRelationsContext';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { useEffect, useMemo, useState } from 'react';
import LabelSelect from '../common/LabelSelect';
import { uniq } from 'lodash';
import useItemLabels from '../../hooks/useItemLabels';
import { ItemLabel } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { AccountTreeIcon } from '@ui/components/Icons/AccountTreeIcon/AccountTreeIcon';
import { EmptyOutlinedIcon } from '@ui/components/StandardIcons/EmptyOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { beige, grey, surface } from '@ui/theme/colors';
import { ItemRelationsTable } from './ItemRelationsTable';
import toast from 'react-hot-toast';
import { api } from '../../../../utils/api';
import { isNotNullish } from '../../../../utils/isNotNullish';
import uniqBy from 'lodash/uniqBy';
import { useCountry } from '../../../../hooks/useCountry';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

export function ItemRelationActions() {
  const { selectedItemIds, setSelectedItemIds } = useItemRelationsContext();
  const { erpItems } = useErpItems();
  const { formatMessage } = useIntl();
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedLabel, setSelectedLabel] = useState<string | null>(null);
  const utils = api.useUtils();
  const createItemEquivalenceGroup = api.BillOfMaterials.createItemEquivalenceGroup.useMutation();
  const removeItemsFromEquivalenceGroup = api.BillOfMaterials.removeItemsFromEquivalenceGroup.useMutation();
  const country = useCountry();
  const { labels, createLabel, setLabelForItems } = useItemLabels({ countries: [country] });
  const uniqueLabels = useMemo(() => {
    return uniq([...labels.map((label) => label.label)]);
  }, [labels]);
  const selectedItemsArray = useMemo(() => {
    return [...selectedItemIds].map((id) => erpItems[id]).filter(isNotNullish);
  }, [erpItems, selectedItemIds]);
  useEffect(() => {
    setSelectedLabel(null);
  }, [selectedItemIds]);
  const onLabelSelected = async (label: ItemLabel | string | null) => {
    let labelId: string | undefined = undefined;
    if (typeof label === 'string') {
      labelId = await createLabel(label);
    } else {
      labelId = label?.id?.value;
    }
    await setLabelForItems(
      selectedItemsArray.map((item) => item.id.value),
      labelId,
    );
    utils.BillOfMaterials.getItemLabels.invalidate();
    utils.BillOfMaterials.getItems.invalidate();

    toast.success('Successfully set label for selected items');
  };

  // Confirmation popover state for setting/clearing interchangeability
  const [setAnchorEl, setSetAnchorEl] = useState<HTMLElement | null>(null);
  const [clearAnchorEl, setClearAnchorEl] = useState<HTMLElement | null>(null);
  const isSetPopoverOpen = Boolean(setAnchorEl);
  const isClearPopoverOpen = Boolean(clearAnchorEl);

  async function handleSetInterchangeable() {
    // We need to first get all items in the relevant equivalency groups
    // Since if we set one item equivalent to another, we most likely want to set all items that were set equivalent to
    // THOSE items as equivalent with each other as well
    const equivalencyGroupsInSelectedItems = [
      ...new Set(selectedItemsArray.map((item) => item.equivalenceGroupId?.value).filter(isNotNullish)),
    ];
    const itemsInExistingEquivalenceGroups = equivalencyGroupsInSelectedItems.length
      ? Object.values(erpItems).filter((item) => {
          return item.equivalenceGroupId?.value
            ? equivalencyGroupsInSelectedItems.includes(item.equivalenceGroupId.value)
            : false;
        })
      : [];

    if (itemsInExistingEquivalenceGroups.length) {
      // Remove all items from existing groups
      await removeItemsFromEquivalenceGroup.mutateAsync({
        itemIds: itemsInExistingEquivalenceGroups.map((item) => item.id.value),
      });
    }

    const allItems = uniqBy([...itemsInExistingEquivalenceGroups, ...selectedItemsArray], 'id.value');
    // Add all items to the new equivalency group
    await createItemEquivalenceGroup.mutateAsync({
      itemIds: allItems.map((item) => item.id.value),
    });
    utils.BillOfMaterials.getItems.invalidate();
    toast.success(formatMessage({ id: 'billOfMaterials.addItemsToEquivalencyGroup.success' }));
    setSetAnchorEl(null);
  }

  async function handleClearInterchangeable() {
    await removeItemsFromEquivalenceGroup.mutateAsync({
      itemIds: selectedItemsArray.map((item) => item.id.value),
    });
    utils.BillOfMaterials.getItems.invalidate();
    toast.success(formatMessage({ id: 'billOfMaterials.removeItemsFromEquivalencyGroup.success' }));
    setClearAnchorEl(null);
  }

  return (
    <Stack
      direction="row"
      justifyContent="center"
      sx={{
        opacity: selectedItemIds.size ? 1 : 0,
        width: '100%',
        transform: `translateY(${selectedItemIds.size ? '-100%' : '00px'})`,
        transition: 'opacity 0.2s, transform 0.5s',
        position: 'absolute',
        top: '100%',
        zIndex: 10,
        padding: '0 24px 0 8px',
      }}
      data-testid="item-relations-actions-container"
    >
      <Stack
        direction="column"
        alignItems="center"
        gap={2}
        sx={{
          width: isExpanded ? '100%' : '50%',
          minWidth: '1000px',
          backgroundColor: '#fff',
          borderRadius: '16px 16px 0 0',
          padding: '16px',
          height: isExpanded ? '400px' : '72px',
          boxShadow: '0 12px 36px 0 #00000040',
          boxSizing: 'border-box',
          pointerEvents: 'all',
          overflow: 'hidden',
          transition: 'height 0.3s, width 0.3s',
        }}
      >
        <Stack direction="row" gap={4} alignItems="center" sx={{ height: '40px' }}>
          <Stack
            direction="row"
            alignItems="center"
            gap={1}
            onClick={() => setIsExpanded((prev) => !prev)}
            sx={{ cursor: 'pointer' }}
            data-testid="item-relations-actions-expand-button"
          >
            <Chevron transitionDuration="0.2s" direction={isExpanded ? 'up' : 'down'} height={20} width={20} />
            <Typography
              variant="body2"
              fontWeight={500}
              sx={{ whiteSpace: 'nowrap' }}
              data-testid="selected-items-count"
            >
              {selectedItemIds.size} <FormattedMessage id="common.label.items" />
            </Typography>
          </Stack>
          <Divider flexItem orientation="vertical" />
          <Stack direction="row" alignItems="center" gap={1} sx={{ width: '300px' }}>
            <Typography sx={{ whiteSpace: 'nowrap' }} variant="body2" fontWeight={500}>
              <FormattedMessage id="billOfMaterials.setLabel" />
            </Typography>
            <TooltipAira
              title={formatMessage({
                id: 'billOfMaterials.bulkSetLabelTooltip',
              })}
            />
            <LabelSelect
              selectedLabel={selectedLabel}
              setSelectedLabel={setSelectedLabel}
              onUpdate={onLabelSelected}
              items={uniqueLabels}
            />
          </Stack>
          <Divider flexItem orientation="vertical" />

          <Stack direction="row" alignItems="center" gap={2}>
            <Typography variant="body2" fontWeight={500} sx={{ whiteSpace: 'nowrap' }}>
              <FormattedMessage id="billOfMaterials.interchangeability" />
            </Typography>
            <TooltipAira
              title={formatMessage({
                id: 'billOfMaterials.addItemsToEquivalencyGroupTooltip',
              })}
            />
            <Button
              onClick={(e) => setSetAnchorEl(e.currentTarget)}
              sx={{ height: '100%', borderRadius: '16px', padding: '8px 12px' }}
              variant="contained"
              data-testid="set-equivalency-group-button"
              disabled={selectedItemsArray.length < 2}
            >
              <Stack direction="row" alignItems="center" gap={1}>
                <AccountTreeIcon />
                <FormattedMessage id="common.label.set" />
              </Stack>
            </Button>
            <Button
              sx={{
                backgroundColor: surface[100],
                color: grey[900],
                height: '100%',
                borderRadius: '16px',
                padding: '8px 12px',
                '&:hover': {
                  backgroundColor: surface[200],
                },
              }}
              variant="contained"
              onClick={(e) => setClearAnchorEl(e.currentTarget)}
              disabled={selectedItemsArray.length === 0}
              data-testid="clear-equivalency-group-button"
            >
              <Stack direction="row" alignItems="center" gap={1}>
                <EmptyOutlinedIcon />
                <FormattedMessage id="common.label.clear" />
              </Stack>
            </Button>
          </Stack>
          <Divider flexItem orientation="vertical" />

          <Stack direction="row" alignItems="center" justifyContent="center">
            <IconButton
              data-testid="clear-selection-button"
              onClick={() => {
                setSelectedItemIds(new Set());
                setIsExpanded(false);
              }}
            >
              <CrossOutlinedIcon />
            </IconButton>
          </Stack>
        </Stack>
        <Stack
          direction="column"
          gap={1}
          sx={{
            width: '100%',
            height: '75%',
            flex: '1 0 auto',
            borderRadius: '16px',
            padding: '16px',
            backgroundColor: beige[100],
          }}
        >
          <ItemRelationsTable isSelectionLimited={true} items={selectedItemsArray} tableTestId="actions" />
        </Stack>
      </Stack>

      <ConfirmationPopover
        anchorEl={setAnchorEl}
        open={isSetPopoverOpen}
        onClose={() => setSetAnchorEl(null)}
        title={formatMessage({
          id: 'billOfMaterials.setInterchangeable.confirmTitle',
        })}
        description={formatMessage({
          id: 'billOfMaterials.setInterchangeable.confirmDescription',
        })}
        confirmText={formatMessage({ id: 'common.label.set' })}
        onConfirm={handleSetInterchangeable}
        confirmIcon={<AccountTreeIcon />}
        testId="set-interchangeable-popover"
      />
      <ConfirmationPopover
        anchorEl={clearAnchorEl}
        open={isClearPopoverOpen}
        onClose={() => setClearAnchorEl(null)}
        title={formatMessage({
          id: 'billOfMaterials.clearInterchangeable.confirmTitle',
        })}
        description={formatMessage({
          id: 'billOfMaterials.clearInterchangeable.confirmDescription',
        })}
        confirmText={formatMessage({ id: 'common.label.clear' })}
        onConfirm={handleClearInterchangeable}
        confirmIcon={<EmptyOutlinedIcon />}
        testId="clear-interchangeable-popover"
      />
    </Stack>
  );
}
