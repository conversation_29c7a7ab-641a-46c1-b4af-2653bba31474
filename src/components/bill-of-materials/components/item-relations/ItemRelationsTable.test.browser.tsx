import { cleanup, fireEvent, screen, waitFor, within } from '@testing-library/react';
import { afterEach } from 'vitest';
import { billOfMaterialsTestSetup } from '../../test-utils';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { ItemRelationsProvider } from './ItemRelationsContext';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { ItemRelationsTable } from './ItemRelationsTable';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

function ItemRelationsTableWrapper() {
  const { erpItems } = useErpItems();
  const actualItems = Object.values(erpItems);
  return <ItemRelationsTable items={actualItems} tableTestId="main" />;
}

async function renderItemRelationsTable() {
  renderWithProviders(
    <AiraThemeProvider theme={heatDesignTheme}>
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ItemRelationsProvider>
          <ItemRelationsTableWrapper />
        </ItemRelationsProvider>
      </IntlProvider>
    </AiraThemeProvider>,
  );
  await waitFor(async () => {
    const rows = await screen.findAllByTestId(/erp-item-card/);
    expect(rows.length).toBeGreaterThan(0);
  });
}

describe('ItemRelationsTable', () => {
  it('should render component', async () => {
    renderItemRelationsTable();
    const container = await screen.findByTestId('item-relations-table-main');
    expect(container).toBeInTheDocument();
  });
  it('should select items by clicking on them', async () => {
    await renderItemRelationsTable();
    const rows = await screen.findAllByTestId(/erp-item-card/);
    expect(rows[0]).not.toHaveClass('selected');
    fireEvent.click(rows[0]!);
    expect(rows[0]).toHaveClass('selected');
  });
  // NOTE: Disabling this test because it's flaky and I have not been able to figure out why. SAD-1974.
  it('should select all items by clicking on the header', async () => {
    await renderItemRelationsTable();
    const selectAllButton = await screen.findByTestId('select-all-button');
    await waitFor(async () => {
      expect(selectAllButton).not.toHaveClass('disabled');
    });
    fireEvent.click(selectAllButton);
    const rows = await screen.findAllByTestId(/erp-item-card/);
    expect(rows.length).toBeGreaterThan(0);
    await waitFor(() => {
      rows.forEach((row) => {
        expect(row).toHaveClass('selected');
      });
    });
  });
  it('should expand erp item cards by clicking on the details button', async () => {
    await renderItemRelationsTable();
    const rows = await screen.findAllByTestId(/erp-item-card/);
    const firstRow = rows[0];
    expect(within(firstRow!).queryByTestId('item-details-panel')).not.toBeInTheDocument();

    const expandDetailsButton = await within(firstRow!).findByTestId('expand-details-button');
    fireEvent.click(expandDetailsButton);
    expect(await within(firstRow!).findByTestId('item-details-panel')).toBeVisible();
  });
});
