import {
  createSearchIndex,
  filterBySearchQuery,
  filterItems,
  ItemFilter,
  SearchIndex,
  SearchQuery,
} from './item-filter';
import { Item } from 'components/bill-of-materials/types';
import { Country } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';

function createItem({
  id,
  category,
  supplier,
  description,
  label,
  erpId,
}: {
  id: string;
  category: string;
  supplier: string;
  description: string;
  label: string;
  erpId: string;
}): Item {
  return {
    id: { value: id },
    category: category,
    supplier: supplier,
    supplierId: 'supplier-id',
    description: description,
    label: { id: { value: label }, label: label },
    erpId: erpId,
    country: Country.COUNTRY_GB,
    currency: 'USD',
    updatedAt: new Date(),
    validationErrors: [],
    includedInBundles: [],
  };
}

const ITEM_1 = createItem({
  id: 'ITEM-1',
  category: 'Some Category',
  supplier: 'Some Supplier',
  description: 'Some Description',
  label: 'Some Label',
  erpId: 'ERP-ID-1',
});
const ITEM_2 = createItem({
  id: 'ITEM-2',
  category: 'Other Category',
  supplier: 'Other Supplier',
  description: 'Other Description',
  label: 'Other Label',
  erpId: 'ERP-ID-2',
});
const ITEM_3 = createItem({
  id: 'ITEM-3',
  category: 'Banana',
  supplier: 'Apple',
  description: 'Orange',
  label: 'Some Label',
  erpId: 'ERP-ID-3',
});

const items: Item[] = [ITEM_1, ITEM_2, ITEM_3];

describe('filterBySearchQuery', () => {
  it('returns no results when free text not in data', () => {
    const searchQuery: SearchQuery = {
      $case: 'by-free-text',
      query: 'not in the data',
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterBySearchQuery(searchQuery, searchIndex);
    expect(result).toEqual([]);
  });

  it('returns results when free text matches', () => {
    const searchQuery: SearchQuery = {
      $case: 'by-free-text',
      query: 'Category',
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterBySearchQuery(searchQuery, searchIndex);
    expect(result).toEqual([ITEM_1, ITEM_2]);
  });

  it('returns everything when free text is blank', () => {
    const searchQuery: SearchQuery = {
      $case: 'by-free-text',
      query: '',
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterBySearchQuery(searchQuery, searchIndex);
    expect(result).toEqual(items);
  });

  it('filters by ERP id', () => {
    const searchQuery: SearchQuery = {
      $case: 'by-erp-ids',
      erpIds: new Set(['ERP-ID-2']),
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterBySearchQuery(searchQuery, searchIndex);
    expect(result).toEqual([ITEM_2]);
  });

  it('returns no results when ERP id not in data', () => {
    const searchQuery: SearchQuery = {
      $case: 'by-erp-ids',
      erpIds: new Set(['non-existent-id']),
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterBySearchQuery(searchQuery, searchIndex);
    expect(result).toEqual([]);
  });

  it('filters by labels if set', () => {
    const filter: ItemFilter = {
      searchQuery: {
        $case: 'by-free-text',
        query: '',
      },
      selectedLabelIds: new Set(['Some Label']),
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterItems(filter, searchIndex);
    expect(result).toEqual([ITEM_1, ITEM_3]);
  });

  it('filters by both query and labels', () => {
    const filter: ItemFilter = {
      searchQuery: {
        $case: 'by-free-text',
        query: 'Category',
      },
      selectedLabelIds: new Set(['Some Label']),
    };
    const searchIndex: SearchIndex = createSearchIndex(items);
    const result = filterItems(filter, searchIndex);
    expect(result).toEqual([ITEM_1]);
  });
});
