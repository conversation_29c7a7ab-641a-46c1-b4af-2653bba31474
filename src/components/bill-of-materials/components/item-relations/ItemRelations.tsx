import { Stack } from '@mui/material';
import { ItemRelationsFilter } from './ItemRelationsFilter';
import { ItemRelationsTable } from './ItemRelationsTable';
import { ItemRelationActions } from './ItemRelationActions';
import { useLayoutEffect, useRef, useState } from 'react';
import { useItemRelationsContext } from './ItemRelationsContext';

export function ItemRelations() {
  const { filteredItems } = useItemRelationsContext();
  const containerRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({
    left: 0,
    width: 0,
  });

  useLayoutEffect(() => {
    const container = containerRef.current;
    if (!container) {
      return;
    }
    const measure = () => {
      const rect = container.getBoundingClientRect();
      // The hardcoded values are to adjust for paddings - the tanstack table has
      // inbuilt paddings and we have to take those into account.
      // We could calculate them dynamically, but this is simpler and works well enough.
      setPosition({
        left: rect.left - 16,
        width: rect.width + 48,
      });
    };

    // Initial measurement
    measure();
    const resizeObserver = new ResizeObserver(() => {
      measure();
    });
    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
    };
  }, [containerRef]);
  return (
    <Stack gap={2} sx={{ position: 'relative' }} ref={containerRef} data-testid="item-relations-container">
      <ItemRelationsFilter />
      <ItemRelationsTable items={filteredItems} tableTestId="main" />
      <Stack
        sx={{
          left: position.left,
          width: position.width,
          position: 'fixed',
          height: '100vh',
          top: 0,
          pointerEvents: 'none',
          zIndex: 11,
        }}
      >
        <ItemRelationActions />
      </Stack>
    </Stack>
  );
}
