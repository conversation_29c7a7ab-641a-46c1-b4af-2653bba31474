import { I<PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useElementBbox } from '../../../../hooks/useElementBbox';
import { Button } from '@ui/components/Button/Button';
import { MagnifyingGlassOutlinedIcon } from '@ui/components/StandardIcons/MagnifyingGlassOutlinedIcon';
import { TextField } from '@ui/components/TextField/TextField';
import { CheckOutlinedIcon } from '@ui/components/StandardIcons/CheckOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import { SearchQuery } from './item-filter';
import { debounce } from 'lodash';
import { useItemRelationsContext } from './ItemRelationsContext';

export type ItemRelationsSearchProps = {
  setQuery: (searchQuery: SearchQuery) => void;
  query: SearchQuery;
};

export function ItemRelationsSearch({ setQuery, query }: ItemRelationsSearchProps) {
  const { formatMessage } = useIntl();
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isAdvancedSearchResultVisible, setIsAdvancedSearchResultVisible] = useState(false);
  const [containerElement, setContainerElement] = useState<HTMLDivElement | undefined>(undefined);
  const [titleElement, setTitleElement] = useState<HTMLDivElement | undefined>(undefined);
  const [advancedSearchQuery, setAdvancedSearchQuery] = useState<string>('');

  const { filteredItems } = useItemRelationsContext();

  const containerBbox = useElementBbox({ element: containerElement });
  const titleElementBbox = useElementBbox({ element: titleElement });
  const translateX = isExpanded ? containerBbox.width - titleElementBbox.width - 16 : 8;

  const onAdvancedSearchChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setAdvancedSearchQuery(event.target.value);
  };

  const handleContainerChange = useCallback(
    (node: HTMLDivElement) => {
      setContainerElement(node);
    },
    [setContainerElement],
  );

  const handleTitleChange = useCallback(
    (node: HTMLDivElement) => {
      setTitleElement(node);
    },
    [setTitleElement],
  );

  const doAdvancedSearch = () => {
    const ids = advancedSearchQuery
      .split('\n')
      .map((id) => id.trim())
      .filter(Boolean);

    setQuery({ $case: 'by-erp-ids', erpIds: new Set(ids) });
    setIsAdvancedSearchResultVisible(true);
  };
  const isAdvancedSearchActive = query.$case === 'by-erp-ids';
  const getAdvancedSearchResultIcon = (itemId: string) => {
    return isAdvancedSearchActive && filteredItems.some((item) => item.erpId === itemId) ? (
      <CheckOutlinedIcon data-testid="advanced-search-result-icon-check" />
    ) : (
      <CrossOutlinedIcon data-testid="advanced-search-result-icon-cross" />
    );
  };

  const debouncedUpdateFreeTextQuery = useMemo(() => {
    const updateFreeTextQuery = (query: string) => {
      setQuery({ $case: 'by-free-text', query });
    };
    return debounce(updateFreeTextQuery, 300);
  }, [setQuery]);

  useEffect(() => {
    debouncedUpdateFreeTextQuery(searchQuery);
  }, [debouncedUpdateFreeTextQuery, searchQuery]);

  return (
    <Stack sx={{ position: 'relative', width: '400px', height: '100%' }} data-testid="item-relations-search">
      <Typography variant="body2" fontWeight={500} sx={{ marginBottom: '8px', height: '17px' }}>
        <FormattedMessage id="billOfMaterials.itemCatalogue.filter.search" />
      </Typography>
      <Stack
        direction="row"
        alignItems="center"
        ref={handleContainerChange}
        sx={{ width: '100%', height: '40px', position: 'relative' }}
      >
        <Stack
          sx={{
            height: '100%',
            width: '100%',
            position: isExpanded ? 'absolute' : 'relative',
            left: 0,
            top: 0,
            opacity: isExpanded ? 0 : 1,
            pointerEvents: isExpanded ? 'none' : 'all',
            cursor: 'pointer',
          }}
          onClick={(e) => {
            if (isAdvancedSearchActive && !isExpanded) {
              e.preventDefault();
              e.stopPropagation();
              setIsExpanded(true);
            }
          }}
        >
          <TextField
            containerProps={{ width: '100%' }}
            sx={{
              input: { padding: '8px' },
              width: '100%',
              pointerEvents: isAdvancedSearchActive ? 'none' : 'all',
            }}
            data-testid="item-relations-search-input"
            value={isAdvancedSearchActive ? '' : searchQuery}
            onChange={(event) => setSearchQuery(event.target.value)}
            name="Search"
            placeholder={
              isAdvancedSearchActive
                ? `<${formatMessage({ id: 'billOfMaterials.advancedSearch' })}>`
                : formatMessage({ id: 'billOfMaterials.itemCatalogue.filter.search' })
            }
          />
        </Stack>
        <ClickAwayListener onClickAway={() => setIsExpanded(false)}>
          <Stack
            gap={2}
            sx={{
              height: isExpanded ? '400px' : '40px',
              width: '100%',
              position: 'absolute',
              left: 0,
              top: 0,
              padding: isExpanded ? '16px' : '0 8px',
              pointerEvents: isExpanded ? 'all' : 'none',
              transition: 'height 0.3s, width 0.3s, backgroundColor 0.3s, box-shadow 0.3s, padding 0.3s',
              borderRadius: '16px',
              backgroundColor: isExpanded ? '#fff' : 'transparent',
              zIndex: 11,
              boxShadow: isExpanded ? '0 12px 36px 0 #00000040' : 'none',
            }}
          >
            <Stack sx={{ flex: '0 0 40px' }} direction="row-reverse" alignItems="center">
              <Stack
                ref={handleTitleChange}
                direction="row"
                alignItems="center"
                gap={1}
                sx={{
                  pointerEvents: 'all',
                  position: 'absolute',
                  right: 0,
                  transform: `translateX(${-translateX}px)`,
                  transition: 'transform 0.2s',
                  cursor: 'pointer',
                }}
              >
                <Typography onClick={() => setIsExpanded((prev) => !prev)} data-testid="advanced-search-label">
                  <FormattedMessage id={isExpanded ? 'billOfMaterials.advanced' : 'billOfMaterials.advancedSearch'} />
                </Typography>
                {isAdvancedSearchActive && !isExpanded && (
                  <IconButton
                    onClick={() => {
                      setQuery({ $case: 'by-free-text', query: '' });
                    }}
                  >
                    <CrossOutlinedIcon />
                  </IconButton>
                )}
              </Stack>

              <Button
                data-testid="advanced-search-accept-button"
                sx={{
                  height: '100%',
                  width: '100px',
                  opacity: isExpanded ? '1' : '0',
                  transition: 'opacity 0.2s',
                  pointerEvents: isExpanded ? 'all' : 'none',
                }}
                onClick={() => {
                  doAdvancedSearch();
                }}
              >
                <Stack direction="row" alignItems="center" gap={1}>
                  <MagnifyingGlassOutlinedIcon />
                  <Typography color="#fff" variant="body2">
                    <FormattedMessage id="billOfMaterials.itemCatalogue.filter.search" />
                  </Typography>
                </Stack>
              </Button>
            </Stack>
            <Stack direction="column" sx={{ opacity: isExpanded ? 1 : 0, transition: 'opacity 0.3s' }}>
              <Typography variant="body2">
                <FormattedMessage id="billOfMaterials.advancedSearchTooltip" />
              </Typography>
            </Stack>
            <Stack
              sx={{ opacity: isExpanded ? 1 : 0, transition: 'opacity 0.3s', position: 'relative' }}
              onMouseDown={() => {
                setIsAdvancedSearchResultVisible(false);
              }}
            >
              <TextField
                type="textarea"
                sx={{
                  height: '200px',
                  boxSizing: 'border-box',
                  position: 'relative',
                  textarea: { height: '100% !important', opacity: isAdvancedSearchResultVisible ? 0 : 1 },
                }}
                data-testid="item-relations-advanced-search-input"
                value={advancedSearchQuery}
                onChange={(event) => onAdvancedSearchChange(event as ChangeEvent<HTMLTextAreaElement>)}
                name="advanced-search"
              />
              <Stack
                direction="column"
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  zIndex: 1,
                  padding: '16px',
                  pointerEvents: 'none',
                  opacity: isAdvancedSearchResultVisible ? 1 : 0,
                }}
              >
                {(query.$case === 'by-erp-ids' ? Array.from(query.erpIds) : []).map((itemId) => {
                  return (
                    <Stack
                      key={itemId}
                      direction="row"
                      alignItems="center"
                      gap={1}
                      data-testid="advanced-search-result-item"
                    >
                      {getAdvancedSearchResultIcon(itemId)}
                      {itemId}
                    </Stack>
                  );
                })}
              </Stack>
            </Stack>
          </Stack>
        </ClickAwayListener>
      </Stack>
    </Stack>
  );
}
