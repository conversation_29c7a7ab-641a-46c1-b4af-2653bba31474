import { cleanup, fireEvent, screen } from '@testing-library/react';
import { ItemRelationActions } from './ItemRelationActions';
import { afterEach } from 'vitest';
import { billOfMaterialsTestSetup } from '../../test-utils';
import { ItemRelationsProvider } from './ItemRelationsContext';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

function renderItemRelationActions(options?: { selectedIds?: Set<string> }) {
  renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <AiraThemeProvider theme={heatDesignTheme}>
        <ItemRelationsProvider selectedIds={options?.selectedIds ?? new Set()}>
          <ItemRelationActions />
        </ItemRelationsProvider>
      </AiraThemeProvider>
    </IntlProvider>,
  );
}

describe('ItemRelationActions', () => {
  it('should render component', async () => {
    renderItemRelationActions();
    const container = await screen.findByTestId('item-relations-actions-container');
    expect(container).toBeInTheDocument();
  });

  it('should be hidden if no items are selected', async () => {
    renderItemRelationActions();
    const container = await screen.findByTestId('item-relations-actions-container');
    expect(container).not.toBeVisible();
  });
  it('should show actions when items are selected', async () => {
    renderItemRelationActions({
      selectedIds: new Set(['475f5a0b-fc2a-4a45-930e-755c4781c851', '4abe96cf-5ce5-47fd-b0f7-6bd8f9ed29b2']),
    });
    const actionsContainer = await screen.findByTestId('item-relations-actions-container');
    expect(actionsContainer).toBeVisible();
  });
  it('should expand and show items table when clicking on expand button', async () => {
    renderItemRelationActions({
      selectedIds: new Set(['475f5a0b-fc2a-4a45-930e-755c4781c851', '4abe96cf-5ce5-47fd-b0f7-6bd8f9ed29b2']),
    });
    const container = await screen.findByTestId('item-relations-actions-container');
    expect(container).toBeVisible();
    const expandButton = screen.getByTestId('item-relations-actions-expand-button');
    expect(expandButton).toBeInTheDocument();
    expandButton.click();
    const table = await screen.findByTestId('item-relations-table-actions');
    expect(table).toBeVisible();
    const rowsInTable = await screen.findAllByTestId(/table-body-row/);
    expect(rowsInTable.length).toEqual(2);
  });
  it('should clear selected items when clicking on clear selection button', async () => {
    renderItemRelationActions({
      selectedIds: new Set(['475f5a0b-fc2a-4a45-930e-755c4781c851', '4abe96cf-5ce5-47fd-b0f7-6bd8f9ed29b2']),
    });
    const container = await screen.findByTestId('item-relations-actions-container');
    const itemCount = await screen.findByTestId('selected-items-count');
    expect(container).toBeVisible();
    expect(itemCount.innerHTML).toContain('2 common.label.items');
    const clearSelectionButton = await screen.findByTestId('clear-selection-button');
    fireEvent.click(clearSelectionButton);
    await new Promise((resolve) => setTimeout(resolve, 500)); // Wait for container to animate out
    expect(container).not.toBeVisible();
    expect(itemCount.innerHTML).toContain('0 common.label.items');
  });
});
