import { Stack } from '@mui/material';
import { useItemRelationsContext } from './ItemRelationsContext';
import { ItemRelationsSearch } from './ItemRelationsSearch';
import { ItemRelationsLabelFilter } from './ItemRelationsLabelFilter';
import { useCallback } from 'react';
import { SearchQuery } from './item-filter';

export function ItemRelationsFilter() {
  const { itemFilter, setItemFilter } = useItemRelationsContext();
  const { selectedLabelIds, searchQuery } = itemFilter;

  const setSelectedLabelIds = useCallback(
    (selectedLabelIds: Set<string>) => {
      setItemFilter((prev) => ({ ...prev, selectedLabelIds }));
    },
    [setItemFilter],
  );
  const setSearchQuery = useCallback(
    (searchQuery: SearchQuery) => {
      setItemFilter((prev) => ({ ...prev, searchQuery }));
    },
    [setItemFilter],
  );

  return (
    <Stack direction="row" gap={2} alignItems="center" data-testid="item-relations-filter-container">
      <ItemRelationsLabelFilter selectedLabelIds={selectedLabelIds} setSelectedLabelIds={setSelectedLabelIds} />
      <ItemRelationsSearch query={searchQuery} setQuery={setSearchQuery} />
    </Stack>
  );
}
