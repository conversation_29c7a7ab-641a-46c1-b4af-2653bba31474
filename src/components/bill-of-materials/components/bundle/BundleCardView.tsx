import { Box, CircularProgress, Stack, Tooltip, Typography } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { ContentCopyIcon } from '@ui/components/Icons/ContentCopyIcon/ContentCopyIcon';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { useEffect, useRef, useState } from 'react';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { MessageDescriptor, useIntl } from 'react-intl';
import { api } from '../../../../utils/api';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import {
  bundleItemsToBundleItemRequest,
  isVanStockBundle,
  isInstallationKitBundle,
  isStaticBundleCollection,
} from '../../utils';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { BundleItemsTable } from './BundleItemsTable';
import { useBundleCollectionStore } from '../../stores/bundleCollectionStore';
import { ANIMATION_DURATION } from '../../constants/constants';
import { VanStockBundleDetailsView } from './VanStockBundleDetailsView';
import { InstallationKitBundleDetails } from './InstallationKitBundleDetails';

export type BundleCardViewProps = {
  bundle: Bundle;
  setIsEditing: (isEditing: boolean) => void;
  bundleCollection: BundleCollection;
  setIsExpanded: (isExpanded: boolean) => void;
};

export function BundleCardView({ bundle, setIsEditing, bundleCollection, setIsExpanded }: BundleCardViewProps) {
  const removeBundle = api.BillOfMaterials.removeBundle.useMutation();
  const createBundle = api.BillOfMaterials.createBundle.useMutation();
  const addBundleItems = api.BillOfMaterials.addBundleItems.useMutation();
  const apiUtils = api.useUtils();
  const { formatMessage } = useIntl();
  const { expandedBundleIds } = useBundleCollectionStore();
  const descriptionRef = useRef<HTMLSpanElement>(null);
  const [deleteAnchorEl, setDeleteAnchorEl] = useState<HTMLElement | null>(null);
  const deletePopoverOpen = Boolean(deleteAnchorEl);
  const [isCopying, setIsCopying] = useState(false);
  const isExpanded = bundle.id?.value && expandedBundleIds.has(bundle.id.value);
  const [isFullyClosed, setIsFullyClosed] = useState(!isExpanded);
  const onBundleRemove = async () => {
    const bundleId = bundle.id?.value;
    if (!bundleId) {
      return;
    }
    await removeBundle.mutateAsync({
      bundleId,
    });
    apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    setDeleteAnchorEl(null);
  };

  const handleCopyClick = async (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
    const bundleCollectionId = bundle.bundleCollectionId?.value;
    if (!bundleCollectionId) {
      return;
    }

    setIsCopying(true);
    try {
      // Create a new bundle with the same properties
      const result = await createBundle.mutateAsync({
        bundleCollectionId,
        title: `Copy of ${bundle.title}`,
        description: bundle.description,
        details: bundle.details,
      });

      // Add the same items to the new bundle
      if (result.bundleId?.value && bundle.items.length > 0) {
        await addBundleItems.mutateAsync(bundleItemsToBundleItemRequest(bundle.items, result.bundleId.value));
      }

      // Refresh the list of bundles
      await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    } finally {
      setIsCopying(false);
    }
  };

  const bundleEditLabel: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.editVersion'
    : 'billOfMaterials.editBundle';

  const bundleCopyLabel: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.copyVersion'
    : 'billOfMaterials.copyBundle';

  const bundleDeleteLabel: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.deleteVersion'
    : 'billOfMaterials.deleteBundle';

  const handleDeleteClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
    setDeleteAnchorEl(event.currentTarget);
  };

  const handleDeleteCancel = () => {
    setDeleteAnchorEl(null);
  };

  useEffect(() => {
    if (isExpanded) {
      setIsFullyClosed(false);
    } else {
      setTimeout(() => {
        setIsFullyClosed(!isExpanded);
      }, ANIMATION_DURATION);
    }
  }, [isExpanded]);

  return (
    <Stack
      data-testid="bundle-card-view"
      gap={1}
      sx={{
        padding: '16px',
        top: 0,
        left: 0,
        transition: `background-color ${ANIMATION_DURATION}ms`,
        backgroundColor: !isExpanded ? 'transparent' : beige[100],
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: '16px',
        height: isExpanded ? 'auto' : '60px',
        ...(isExpanded
          ? {
              '&:hover': {
                cursor: 'pointer',
              },
              '.actions': {
                width: '150px',
                opacity: 1,
              },
            }
          : {
              '&:hover': {
                backgroundColor: isFullyClosed ? '#2222260f' : 'transparent',
                cursor: 'pointer',
                '.actions': {
                  width: '150px',
                  opacity: 1,
                },
              },
            }),
      }}
    >
      <Stack
        sx={{ width: '100%', height: '100%' }}
        alignItems="flex-start"
        direction="row"
        onClick={() => bundle.id?.value && setIsExpanded(!isExpanded)}
      >
        <Box
          sx={{
            flex: '0 0 25%',
            height: '100%',
            display: 'flex',
            alignItems: 'flex-start',
          }}
        >
          <Typography
            variant="body1"
            sx={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: isExpanded ? 'unset' : 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxHeight: isExpanded ? 'none' : '3rem',
            }}
            fontWeight={500}
            data-testid="bundle-card-title"
          >
            {bundle.title}
          </Typography>
        </Box>
        <Box
          sx={{
            flex: '1',
            height: '100%',
            display: 'flex',
            alignItems: isExpanded ? 'flex-start' : 'center',
          }}
        >
          <Typography
            variant="body2"
            sx={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: isExpanded ? 'unset' : 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              maxHeight: isExpanded ? 'none' : '3rem',
            }}
            ref={descriptionRef}
          >
            {bundle.description}
          </Typography>
        </Box>
        <Stack
          className="actions"
          direction="row"
          sx={{ width: '150px', alignSelf: 'flex-start' }}
          alignItems="center"
          justifyContent="flex-end"
        >
          <Stack
            className="actions"
            direction="row"
            justifyContent="flex-end"
            sx={{
              transition: 'opacity 0.3s',
              width: '150px',
              overflow: 'hidden',
              opacity: deletePopoverOpen ? 1 : 0,
            }}
          >
            <Tooltip title={FormatForTooltip(formatMessage({ id: bundleEditLabel }))}>
              <Box
                data-testid="edit-bundle-button"
                onClick={() => setIsEditing(true)}
                sx={{
                  borderRadius: '50%',
                  '&:hover': { backgroundColor: grey[200] },
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  cursor: 'pointer',
                  height: '35px',
                  width: '35px',
                  transition: `background-color ${ANIMATION_DURATION}ms`,
                }}
              >
                <PenOutlinedIcon width="24px" height="24px" />
              </Box>
            </Tooltip>
            <Tooltip title={FormatForTooltip(formatMessage({ id: bundleCopyLabel }))}>
              <Box
                data-testid="copy-bundle-button"
                onClick={handleCopyClick}
                sx={{
                  borderRadius: '50%',
                  '&:hover': { backgroundColor: grey[200] },
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  cursor: 'pointer',
                  height: '35px',
                  width: '35px',
                  transition: `background-color ${ANIMATION_DURATION}ms`,
                  pointerEvents: isCopying ? 'none' : 'auto',
                }}
              >
                {isCopying ? <CircularProgress size={18} /> : <ContentCopyIcon width="18px" height="18px" />}
              </Box>
            </Tooltip>
            <Tooltip title={FormatForTooltip(formatMessage({ id: bundleDeleteLabel }))}>
              <Box
                data-testid="delete-bundle-button"
                onClick={handleDeleteClick}
                sx={{
                  borderRadius: '50%',
                  '&:hover': { backgroundColor: grey[200] },
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  cursor: 'pointer',
                  height: '35px',
                  width: '35px',
                  transition: `background-color ${ANIMATION_DURATION}ms`,
                  pointerEvents: removeBundle.isPending ? 'none' : 'auto',
                }}
              >
                {removeBundle.isPending ? <CircularProgress /> : <BinOutlinedIcon width="22px" height="22px" />}
              </Box>
            </Tooltip>
            <Box
              data-testid="expand-button"
              sx={{
                borderRadius: '50%',
                '&:hover': { backgroundColor: grey[200] },
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                cursor: 'pointer',
                height: '35px',
                width: '35px',
                transition: `background-color ${ANIMATION_DURATION}ms`,
              }}
            >
              <Chevron width={20} height={20} direction={isExpanded ? 'up' : 'down'} transitionDuration="200ms" />
            </Box>
          </Stack>
        </Stack>
      </Stack>
      {isExpanded && (
        <>
          {isVanStockBundle(bundle) && <VanStockBundleDetailsView bundle={bundle} />}
          {isInstallationKitBundle(bundle) && <InstallationKitBundleDetails bundle={bundle} isEditing={false} />}
          <BundleItemsTable
            bundleCollection={bundleCollection}
            bundle={bundle}
            tableBackgroundColor={beige[100]}
            disableCostOfAllItems={isInstallationKitBundle(bundle)}
          />
        </>
      )}
      <ConfirmationPopover
        anchorEl={deleteAnchorEl}
        open={deletePopoverOpen}
        onClose={handleDeleteCancel}
        title={formatMessage({
          id: 'billOfMaterials.confirmDeleteBundle.title',
        })}
        description={formatMessage({
          id: 'billOfMaterials.confirmDeleteBundle.description',
        })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={onBundleRemove}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        testId="delete-bundle-confirmation-popover"
      />
    </Stack>
  );
}
