import { Box, CircularProgress, I<PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material';
import { brandYellow, grey } from '@ui/theme/colors';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { TextField as AiraTextField } from '@ui/components/TextField/TextField';
import { useMemo, useState } from 'react';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import {
  Bundle,
  BundleCollection,
  BundleItem,
  ItemLabel,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import useItemLabels from 'components/bill-of-materials/hooks/useItemLabels';
import { api } from 'utils/api';
import { BundleItemsTable } from './BundleItemsTable';
import { INSTALLATION_KIT_IDENTIFIER, Item, VAN_STOCK_IDENTIFIER } from '../../types';
import { VanStockBundleDetailsEdit } from './VanStockBundleDetailsEdit';
import {
  ensureInstallationKitBundle,
  ensureVanStockBundle,
  isInstallationKitBundleCollection,
  isStaticBundleCollection,
  isVanStockBundleCollection,
} from '../../utils';
import { InstallationKitBundleDetails } from './InstallationKitBundleDetails';
import { useValidateBundle } from '../../hooks/useValidateBundle';
import { useCountry } from '../../../../hooks/useCountry';

export type UpdateBundleData = {
  updatedBundle: Bundle;
  updatedBundleItems: BundleItem[];
  addedBundleItems: BundleItem[];
  removedBundleItems: BundleItem[];
};

export type BundleCardEditProps = {
  bundle: Bundle;
  bundleCollection: BundleCollection;
  setIsEditing: (value: boolean) => void;
  onUpdate: (data: UpdateBundleData) => Promise<void>;
  onCreate: (newBundle: Bundle) => Promise<void>;
};

// Just helper types to aid with readability of addedLabels/updatedLabels records
type Label = string;
type ErpItemId = string;

export function BundleCardEdit({ bundle, bundleCollection, setIsEditing, onCreate, onUpdate }: BundleCardEditProps) {
  const apiUtils = api.useUtils();
  const country = useCountry();
  const { setLabelForItems, createLabel } = useItemLabels({ countries: [country] });
  const [editableBundle, setEditableBundle] = useState<Bundle>(cloneDeep(bundle));
  const [originalBundle, setOriginalBundle] = useState<Bundle>(cloneDeep(bundle));
  const [addedLabels, setAddedLabels] = useState<Record<ErpItemId, Label>>({});
  const [updatedLabels, setUpdatedLabels] = useState<Record<ErpItemId, ItemLabel | undefined>>({});
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  const isDirty = useMemo(() => {
    return !isEqual(originalBundle, editableBundle) || !isEmpty(addedLabels) || !isEmpty(updatedLabels);
  }, [originalBundle, editableBundle, updatedLabels, addedLabels]);

  const { formatMessage } = useIntl();
  const [closeAnchorEl, setCloseAnchorEl] = useState<HTMLElement | null>(null);
  const closePopoverOpen = Boolean(closeAnchorEl) && isDirty;

  function onBundleChange<T extends keyof Bundle>(data: Record<T, Bundle[T]>) {
    const newEditableBundle = { ...editableBundle, ...data };
    setEditableBundle(newEditableBundle);
  }

  // Use the new validation hook
  const { isValid: hasRequiredFields } = useValidateBundle(editableBundle, bundleCollection);

  // Button should be enabled only if form is dirty AND all validations pass
  const isSaveEnabled = useMemo(
    () => isDirty && hasRequiredFields && !isUpdating,
    [isDirty, hasRequiredFields, isUpdating],
  );

  async function processLabelOperations(): Promise<Promise<unknown>[]> {
    const promises: Promise<unknown>[] = [];

    // Process new labels to be created and assigned
    if (!isEmpty(addedLabels)) {
      const addedLabelsGroupedByLabel = Object.keys(addedLabels).reduce(
        (acc, key) => {
          const label = addedLabels[key];
          if (!label) {
            return acc;
          }
          acc[label] = acc[label] ? [...acc[label], key] : [key];
          return acc;
        },
        {} as Record<Label, ErpItemId[]>,
      );

      const createLabelAndAssign = Object.entries(addedLabelsGroupedByLabel).map(async ([label, itemIds]) => {
        const createdLabel = await createLabel(label);
        if (createdLabel) {
          return setLabelForItems(itemIds, createdLabel);
        } else {
          return Promise.resolve();
        }
      });

      promises.push(...createLabelAndAssign);
    }

    // Process existing labels to be updated or removed
    if (!isEmpty(updatedLabels)) {
      const updatedLabelsGroupedByLabel = Object.keys(updatedLabels).reduce(
        (acc, key) => {
          const itemLabelId = updatedLabels[key]?.id?.value ?? 'undefined';
          acc[itemLabelId] = acc[itemLabelId] ? [...acc[itemLabelId], key] : [key];
          return acc;
        },
        {} as Record<Label, ErpItemId[]>,
      );

      const updateAssignments = Object.entries(updatedLabelsGroupedByLabel).map(([labelId, itemIds]) =>
        setLabelForItems(itemIds, labelId !== 'undefined' ? labelId : undefined),
      );

      promises.push(...updateAssignments);
    }

    return promises;
  }

  async function onBundleSave() {
    // If validation doesn't pass, don't proceed with save
    if (!hasRequiredFields) {
      return;
    }
    setIsUpdating(true);

    if (bundle.id?.value) {
      // Get items that are in the new edited bundle but not in the old one
      const addedBundleItems = editableBundle.items.filter(
        (newItem) => !bundle.items.find((oldItem) => oldItem.itemId?.value === newItem.itemId?.value),
      );
      // And vice versa, get items that were in the old bundle items but not in the new one
      const removedBundleItems = bundle.items.filter(
        (oldItem) => !editableBundle.items.find((newItem) => newItem.itemId?.value === oldItem.itemId?.value),
      );
      // And finally get items that are in both, but are not equal to each other (so therefore have updated)
      const updatedBundleItems = editableBundle.items.reduce((acc, item) => {
        const originalItem = bundle.items.find((i) => i.itemId?.value === item.itemId?.value);
        if (originalItem && !isEqual(originalItem, item)) {
          acc.push(item);
        }
        return acc;
      }, [] as BundleItem[]);

      // Process all label operations
      const promises = await processLabelOperations();
      await Promise.all(promises);

      // Update bundles
      await onUpdate({
        updatedBundle: editableBundle,
        updatedBundleItems,
        addedBundleItems,
        removedBundleItems,
      });
    } else {
      const promises = await processLabelOperations();
      await Promise.all(promises);

      await onCreate(editableBundle);
    }

    setOriginalBundle(cloneDeep(editableBundle));
    apiUtils.BillOfMaterials.getItems.invalidate();
    apiUtils.BillOfMaterials.getItemLabels.invalidate();
    setIsUpdating(false);
  }

  const handleCloseClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (isDirty) {
      setCloseAnchorEl(event.currentTarget);
    } else {
      setIsEditing(false);
    }
  };

  const handleCloseCancel = () => {
    setCloseAnchorEl(null);
  };

  const handleCloseConfirm = () => {
    setCloseAnchorEl(null);
    setIsEditing(false);
  };

  const handleLabelUpdate = (label: ItemLabel | string | null, erpItem: Item) => {
    // If the label is null, it means the user has removed the label from the item
    // Therefore we need to either remove it form the added labels, or explicitly set it to undefined for the updated labels
    // so it can be removed in the db
    if (!label) {
      const currentAddedLabels = { ...addedLabels };
      const currentUpdatedLabels = { ...updatedLabels };
      delete currentAddedLabels[erpItem.id.value];
      setAddedLabels(currentAddedLabels);
      setUpdatedLabels({
        ...currentUpdatedLabels,
        [erpItem.id.value]: undefined,
      });
    } else if (typeof label === 'string') {
      // If label is of type string, that means it's a new label
      setAddedLabels({
        ...addedLabels,
        [erpItem.id.value]: label,
      });
    } else {
      setUpdatedLabels({
        ...updatedLabels,
        [erpItem.id.value]: label,
      });
    }
  };

  const onBundleItemsUpdate = (bundleItems: BundleItem[]) => {
    setEditableBundle({
      ...editableBundle,
      items: bundleItems,
    });
  };

  const bundleEditTitle: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.editVersion.titleEdit'
    : 'billOfMaterials.editBundle.titleEdit';

  const bundleAddTitle: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.editVersion.titleAdd'
    : 'billOfMaterials.editBundle.titleAdd';

  return (
    <Stack
      data-testid="bundle-card-edit"
      gap={2}
      sx={{
        padding: '16px',
        width: '100%',
        top: 0,
        left: 0,
        backgroundColor: 'white',
      }}
    >
      <Stack sx={{ width: '100%' }} alignItems="center" direction="row" justifyContent="space-between">
        <Box sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
          <Typography variant="headline2">
            <FormattedMessage id={bundle.id?.value ? bundleEditTitle : bundleAddTitle} />
          </Typography>
        </Box>

        <Stack alignItems="center" gap={2} direction="row">
          <IconButton
            onClick={() => {
              onBundleSave();
            }}
            disabled={!isSaveEnabled}
            sx={{
              width: '40px',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '50%',
              backgroundColor: 'rgba(0, 0, 0, 0.03)',
              pointerEvents: isSaveEnabled ? 'auto' : 'none',
              cursor: isSaveEnabled ? 'pointer' : 'default',
              opacity: isSaveEnabled ? '1' : '0.5',
              background: isSaveEnabled ? brandYellow[400] : grey[200],
              '&:hover': { backgroundColor: isSaveEnabled ? brandYellow[500] : 'transparent' },
              transition: 'background-color 0.2s',
            }}
            data-testid="save-bundle-button"
          >
            {isUpdating ? (
              <CircularProgress sx={{ width: '100% !important', height: '100% !important' }} />
            ) : (
              <CheckIconThin />
            )}
          </IconButton>
          <IconButton
            sx={{
              width: '40px',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '8px',
              borderRadius: '50%',
              color: 'black',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              '&:hover': { backgroundColor: grey[300] },
              transition: 'background-color 0.2s',
            }}
            data-testid="close-edit-bundle-button"
            onClick={handleCloseClick}
          >
            <CrossOutlinedIcon color="black" />
          </IconButton>
        </Stack>
      </Stack>
      <Stack direction="row" gap={2}>
        <AiraTextField
          containerProps={{ sx: { flex: '2' } }}
          onChange={(value) => {
            onBundleChange({ title: value.target.value });
          }}
          label={formatMessage({ id: 'billOfMaterials.editBundle.titleFieldLabel' })}
          name="title"
          data-testid="bundle-title"
          variant="standard"
          value={editableBundle.title}
          error={!editableBundle.title}
          required
        />
        <AiraTextField
          containerProps={{ sx: { flex: '3' } }}
          onChange={(value) => {
            onBundleChange({ description: value.target.value });
          }}
          required
          label={formatMessage({ id: 'billOfMaterials.editBundle.descriptionFieldLabel' })}
          name="description"
          value={editableBundle.description}
          error={!editableBundle.description}
        />
      </Stack>
      {isVanStockBundleCollection(bundleCollection) && (
        <VanStockBundleDetailsEdit
          bundle={ensureVanStockBundle(editableBundle)}
          onChange={(value) => {
            onBundleChange({
              details: {
                details: {
                  $case: VAN_STOCK_IDENTIFIER,
                  vanStock: value,
                },
              },
            });
          }}
          bundleCollection={bundleCollection}
        />
      )}{' '}
      {isInstallationKitBundleCollection(bundleCollection) && (
        <InstallationKitBundleDetails
          bundle={ensureInstallationKitBundle(editableBundle)}
          isEditing={true}
          onChange={(value) => {
            onBundleChange({
              details: {
                details: {
                  $case: INSTALLATION_KIT_IDENTIFIER,
                  installationKit: value,
                },
              },
            });
          }}
        />
      )}
      <BundleItemsTable
        bundleCollection={bundleCollection}
        onItemLabelUpdate={handleLabelUpdate}
        onItemsUpdate={onBundleItemsUpdate}
        bundle={editableBundle}
        tableBackgroundColor="white"
      />
      <ConfirmationPopover
        anchorEl={closeAnchorEl}
        open={closePopoverOpen}
        onClose={handleCloseCancel}
        title={formatMessage({
          id: 'billOfMaterials.confirmCloseEdit.title',
        })}
        description={formatMessage({
          id: 'billOfMaterials.confirmCloseEdit.description',
        })}
        confirmText={formatMessage({ id: 'common.label.discard' })}
        onConfirm={handleCloseConfirm}
        confirmIcon={<CrossOutlinedIcon width={20} height={20} />}
        testId="close-edit-confirmation-popover"
      />
    </Stack>
  );
}
