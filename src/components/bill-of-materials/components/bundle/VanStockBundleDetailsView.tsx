import { Box, Chip, Stack, Typography } from '@mui/material';
import { VanStockType } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { FormattedMessage } from 'react-intl';
import { useRegions } from 'components/bill-of-materials/hooks/useRegions';
import { useMemo } from 'react';
import { ValidStockType, VanStockBundle } from '../../types';
import { grey } from '@ui/theme/colors';
import { UUID } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/contract/common/v1/uuid';
import { getValidVanStockTypes, getVanStockTypeLabel } from 'components/bill-of-materials/utils';

export interface VanStockBundleDetailsViewProps {
  bundle: VanStockBundle;
}

/**
 * Displays the selected regions and stock type for a van stock bundle
 */
export function VanStockBundleDetailsView({ bundle }: VanStockBundleDetailsViewProps) {
  const { regions, isPending } = useRegions();
  const vanStock = bundle.details.details.vanStock;

  // Find selected regions by matching region IDs with those in the bundle
  const selectedRegions = useMemo(() => {
    return regions
      .filter((r) => {
        const regionId = r.id?.value;
        if (!regionId) return false;

        return vanStock.regions.some((br: UUID) => br.value === regionId);
      })
      .map((r) => ({
        id: r.id!.value!,
        name: r.name,
      }));
  }, [vanStock, regions]);

  if (isPending) {
    return (
      <Box>
        <Typography variant="body2" color={grey[600]}>
          Loading...
        </Typography>
      </Box>
    );
  }

  const stockType = vanStock.stockType;
  const validStockTypes = getValidVanStockTypes();
  const isValidStockType = (stockType: VanStockType): stockType is ValidStockType => {
    return validStockTypes.includes(stockType as ValidStockType);
  };

  return (
    <Stack width="100%" gap={2}>
      <Stack direction="row" gap={2}>
        <Stack gap={1}>
          <Typography variant="body2" fontWeight={500}>
            <FormattedMessage id="common.label.type" />
          </Typography>
          {isValidStockType(stockType) ? (
            <Chip label={getVanStockTypeLabel(stockType)} size="small" />
          ) : (
            <Typography variant="body2" color={grey[600]}>
              <FormattedMessage id="common.label.none" />
            </Typography>
          )}
        </Stack>
        <Stack sx={{ flex: 1 }} gap={1}>
          <Typography variant="body2" fontWeight={500}>
            <FormattedMessage id="billOfMaterials.vanStock.regions" />
          </Typography>
          <Stack direction="row" gap={1} sx={{ flexWrap: 'wrap' }}>
            {selectedRegions.map((region) => (
              <Chip key={region.id} label={region.name} size="small" />
            ))}
            {selectedRegions.length === 0 && (
              <Typography variant="body2" color={grey[600]}>
                <FormattedMessage id="common.label.none" />
              </Typography>
            )}
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  );
}
