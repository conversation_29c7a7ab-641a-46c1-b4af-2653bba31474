import { Divider, Stack, Tooltip, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { InstallationKitBundle, Item } from 'components/bill-of-materials/types';
import { useBillOfMaterialsStore } from 'components/bill-of-materials/BillOfMaterialsStore';
import { InstallationKitBundle as ProtoInstallationKitBundleDetails } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { useMemo } from 'react';
import { Card } from '@ui/components/Card/Card';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { FormattedMessage, useIntl } from 'react-intl';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { beige, grey } from '@ui/theme/colors';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { BILL_OF_MATERIALS_MODALS } from 'components/bill-of-materials/BillOfMaterialsModals';
import { MoneyDropOutlinedIcon } from '@ui/components/StandardIcons/MoneyDropOutlinedIcon';
import { getFormattedItemPrice, getItemMinorPricePerUnit } from 'components/bill-of-materials/utils';
import { useRouter } from 'next/router';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

function KitItemCard({
  children,
  isEditing,
  isError,
  key,
}: Readonly<{
  children: React.ReactNode;
  key?: string;
  isEditing?: boolean;
  isError?: boolean;
}>) {
  let backgroundColor: string | undefined;
  if (!isError) {
    backgroundColor = isEditing ? '#f5f5f5' : beige[150];
  } else {
    backgroundColor = '#fff';
  }

  return (
    <Card
      key={key}
      size="xsmall"
      sx={{
        position: 'relative',
        backgroundColor,
        borderRadius: 2,
        transition: isEditing ? 'all 0.2s ease' : undefined,
        flexGrow: 1,
        flexBasis: 'auto',
        ...(isEditing && !isError
          ? {
              '&:hover': {
                backgroundColor: '#e0e0e0',
                '.actions': {
                  opacity: 1,
                },
              },
            }
          : {}),
        ...(isError && {
          borderColor: 'warning.main',
          borderWidth: 1,
          borderStyle: 'solid',
        }),
      }}
    >
      {children}
    </Card>
  );
}

export type InstallationKitBundleDetailsProps = {
  bundle: InstallationKitBundle;
  isEditing?: boolean;
  onChange?: (data: ProtoInstallationKitBundleDetails) => void;
};

/**
 * Component for displaying and editing InstallationKit bundle details
 * In edit mode: Allows picking AIK items that will be represented by this bundle
 * In view mode: Displays the selected items for an installation kit bundle
 */
export function InstallationKitBundleDetails({
  bundle,
  isEditing = false,
  onChange,
}: Readonly<InstallationKitBundleDetailsProps>) {
  const { formatMessage } = useIntl();
  const { erpItems } = useErpItems();
  const { locale } = useRouter();
  const { openModal } = useBillOfMaterialsStore();
  const installationKit = bundle.details.details.installationKit;

  // Map itemIds to actual items for display
  const selectedItems = useMemo(() => {
    return installationKit.itemIds
      .map((id) => {
        const item = Object.values(erpItems).find((item) => item.id?.value === id);
        return item;
      })
      .filter((item): item is Item => !!item); // Filter out any undefined items
  }, [installationKit.itemIds, erpItems]);

  // Open the item picker modal - only available in edit mode
  const handleOpenItemPicker = () => {
    if (!isEditing || !onChange) return;

    openModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE, {
      preSelectedItemIds: new Set(installationKit.itemIds),
      title: formatMessage({ id: 'billOfMaterials.installationKit.setAikItems' }),
      onClose: (selectedErpItems) => {
        // Extract just the IDs from the selected items
        const itemIds = selectedErpItems.map((item) => item.id?.value).filter(Boolean);
        onChange({
          itemIds,
        });
      },
    });
  };

  // Remove an item from the selection - only available in edit mode
  const handleRemoveItem = (itemId: string) => {
    if (!isEditing || !onChange) return;

    const newItemIds = installationKit.itemIds.filter((id) => id !== itemId);
    onChange({
      itemIds: newItemIds,
    });
  };

  const costOfAllItems = useMemo(() => {
    const totalCost = selectedItems.reduce((sum, item) => {
      return sum + getItemMinorPricePerUnit(item);
    }, 0);
    const currency = selectedItems[0]?.currency ?? 'EUR';
    return getFormattedItemPrice(
      {
        minorPrice: totalCost,
        currency,
      },
      locale,
    );
  }, [selectedItems, locale]);

  return (
    <Stack gap={isEditing ? 1 : 2} width={isEditing ? undefined : '100%'}>
      <Stack direction="row" justifyContent="space-between">
        <Stack direction="row">
          <Typography variant="headline4" fontWeight={500}>
            <FormattedMessage id="billOfMaterials.installationKit.AikItems" />
          </Typography>
          <Divider sx={{ margin: '0 16px' }} color={grey[900]} flexItem orientation="vertical" />
          <Stack direction="row" gap={1}>
            <MoneyDropOutlinedIcon />
            <Typography sx={{ display: 'flex', alignItems: 'center' }} fontWeight={500} variant="body2">
              <FormattedMessage id="billOfMaterials.costOfAllItems" />
            </Typography>
            <Typography sx={{ display: 'flex', alignItems: 'center' }} variant="body1">
              {costOfAllItems}
            </Typography>
          </Stack>
        </Stack>
        {isEditing && (
          <Button
            data-testid="browse-items-button"
            onClick={handleOpenItemPicker}
            sx={{
              borderRadius: '16px',
              backgroundColor: '#22222608',
              height: '100%',
              '&:hover': { backgroundColor: '#22222616' },
            }}
          >
            <Stack direction="row" alignItems="center" gap={1}>
              <AddOutlinedIcon color="#000" />
              <Typography variant="body1Emphasis">
                <FormattedMessage id="billOfMaterials.installationKit.setAikItems" />
              </Typography>
            </Stack>
          </Button>
        )}
      </Stack>
      {selectedItems.length === 0 ? (
        <KitItemCard isError>
          <Typography variant="body2" color={isEditing ? 'warning.main' : 'grey[600]'}>
            <FormattedMessage id="common.label.none" defaultMessage="None" />
          </Typography>
        </KitItemCard>
      ) : (
        <Stack direction="row" flexWrap="wrap" gap={1}>
          {selectedItems
            .filter((item) => !!item)
            .map((item) => (
              <KitItemCard key={item.id?.value} isEditing={isEditing}>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Typography variant="body2Emphasis">{item.description}</Typography>
                  <Typography variant="body2">{item.erpId}</Typography>
                  {isEditing && (
                    <Stack
                      className="actions"
                      direction="row"
                      gap={1}
                      alignItems="center"
                      sx={{ opacity: 0, transition: 'opacity 0.3s ease' }}
                    >
                      <Tooltip title={FormatForTooltip(formatMessage({ id: 'billOfMaterials.deleteItem' }))}>
                        <Stack
                          onClick={() => handleRemoveItem(item.id?.value)}
                          sx={{
                            borderRadius: '50%',
                            '&:hover': { backgroundColor: grey[200] },
                            justifyContent: 'center',
                            alignItems: 'center',
                            cursor: 'pointer',
                          }}
                        >
                          <BinOutlinedIcon width="20px" height="20px" />
                        </Stack>
                      </Tooltip>
                    </Stack>
                  )}
                </Stack>
              </KitItemCard>
            ))}
        </Stack>
      )}
    </Stack>
  );
}
