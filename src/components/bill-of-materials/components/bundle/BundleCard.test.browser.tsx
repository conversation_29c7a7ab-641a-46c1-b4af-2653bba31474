import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import {
  AIK1_ID,
  AIK_BUNDLE_ID,
  AIK_ERP_ID,
  billOfMaterialsTestSetup,
  createTestBundleCollections,
  VAN_STOCK_1_BUNDLE_ID,
} from '../../test-utils';
import { BillOfMaterials } from '../../BillOfMaterials';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';
import userEvent from '@testing-library/user-event';

billOfMaterialsTestSetup();

describe('BundleCard', () => {
  beforeEach(async () => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <AiraThemeProvider theme={heatDesignTheme}>
          <BillOfMaterials />
        </AiraThemeProvider>
      </IntlProvider>,
    );

    await waitFor(() => {
      screen.getAllByTestId(/static-bundle-collection-card-container/);
    });
    const vanStockBundleCollection = createTestBundleCollections()[0];
    const card = screen.getByTestId(`static-bundle-collection-card-container-${vanStockBundleCollection!.id!.value}`);
    fireEvent.click(within(card).getByTestId(`bundle-collection-card-accordion-toggle`));
  });

  it('should render the van stock bundle card', () => {
    expect(screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`)).toBeInTheDocument();
  });

  it('should render the bundle in non-editing mode initially', () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    expect(within(bundleCard).getByTestId('bundle-card-view')).toBeVisible();
    expect(within(bundleCard).queryByTestId('bundle-card-edit')).not.toBeInTheDocument();
  });

  it('should open edit-mode when pressing edit button', async () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    fireEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    expect(await within(bundleCard).findByTestId('bundle-card-edit')).toBeVisible();
  });

  it('should close edit-mode when pressing cancel button', async () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    fireEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    expect(await within(bundleCard).findByTestId('bundle-card-edit')).toBeVisible();
    fireEvent.click(within(bundleCard).getByTestId('close-edit-bundle-button'));
    expect(await within(bundleCard).findByTestId('bundle-card-view')).toBeVisible();
    expect(within(bundleCard).queryByTestId('bundle-card-edit')).not.toBeInTheDocument();
  });

  it('should not be possible to save bundle without changes', async () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    fireEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    expect(await within(bundleCard).findByTestId('save-bundle-button')).toBeDisabled();
  });

  it('should be possible to save bundle with changes', async () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    fireEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    const titleInput = (await within(bundleCard).findByTestId('bundle-title-input')).querySelector('input');
    expect(titleInput).toBeInTheDocument();
    fireEvent.change(titleInput!, { target: { value: 'New Bundle Title' } });
    fireEvent.click(within(bundleCard).getByTestId('save-bundle-button'));
    expect(within(bundleCard).queryByTestId('bundle-card-edit')).toBeInTheDocument();
  });

  it('should open item-catalogue when pressing browse items button', async () => {
    const bundleCard = screen.getByTestId(`bundle-card-${VAN_STOCK_1_BUNDLE_ID}`);
    await userEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    await waitFor(() => {
      expect(within(bundleCard).getByTestId('browse-items-button')).toBeInTheDocument();
    });
    fireEvent.click(within(bundleCard).getByTestId('browse-items-button'));
    expect(screen.getByTestId('item-catalogue-modal-container')).toBeVisible();
  });

  it('should show warning for unused AIK items', async () => {
    // Open AIK bundle collection
    const card = screen.getByTestId(`static-bundle-collection-card-container-${AIK1_ID}`);
    fireEvent.click(within(card).getByTestId(`bundle-collection-card-accordion-toggle`));

    const bundleCard = screen.getByTestId(`bundle-card-${AIK_BUNDLE_ID}`);
    fireEvent.click(within(bundleCard).getByTestId('edit-bundle-button'));
    expect(await within(bundleCard).findByTestId('bundle-card-edit')).toBeVisible();
    const warningTooltip = within(bundleCard).getByTestId(`item-warning-tooltip-${AIK_ERP_ID}`);
    fireEvent.mouseOver(warningTooltip);
    expect(await screen.findByText('billOfMaterials.installationKit.itemNotUsedElsewhere')).toBeInTheDocument();
  });
});
