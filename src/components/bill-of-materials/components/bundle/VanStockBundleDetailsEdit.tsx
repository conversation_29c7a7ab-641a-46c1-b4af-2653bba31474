import { Stack, InputLabel, Typography, Checkbox, MenuItem } from '@mui/material';
import MuiAutocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { Select } from '@ui/components/Select/Select';
import { VanStockBundle, VanStockBundleCollection, VanStockRegion } from 'components/bill-of-materials/types';
import {
  VanStockBundle as VanStockBundleDetails,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { useRegions } from 'components/bill-of-materials/hooks/useRegions';
import { useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useValidateVanStockBundle } from 'components/bill-of-materials/hooks/useValidateVanStockBundle';
import { FormHelperText } from '@ui/components/FormHelperText/FormHelperText';
import { UUID } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/contract/common/v1/uuid';
import { getValidVanStockTypes, getVanStockTypeLabel } from 'components/bill-of-materials/utils';

export type VanStockBundleDetailsProps = {
  onChange: (data: VanStockBundleDetails) => void;
  bundle: VanStockBundle;
  bundleCollection: VanStockBundleCollection;
};

export function VanStockBundleDetailsEdit({
  onChange,
  bundle,
  bundleCollection,
}: Readonly<VanStockBundleDetailsProps>) {
  const { regions, isPending } = useRegions();
  const intl = useIntl();
  const vanStock = bundle.details.details.vanStock;
  const currentStockType = vanStock.stockType;

  // Filter regions to ensure they all have valid IDs
  const regionOptions: VanStockRegion[] = useMemo(
    () => regions.filter((r) => r.id?.value).map((r) => ({ id: r.id!, name: r.name })),
    [regions],
  );

  // Find selected regions by matching region IDs with those in the bundle
  const selectedRegions: VanStockRegion[] = useMemo(
    () =>
      regions
        .filter((r) => vanStock.regions.some((br: UUID) => br.value === r.id?.value))
        .map((r) => ({
          id: r.id!,
          name: r.name,
        })),
    [regions, vanStock],
  );

  // Validate that the stock type and regions combination is unique
  const { isVanStockUnique = true, errorMessage } = useValidateVanStockBundle(bundleCollection, bundle);

  // Function to handle selecting all regions
  const handleSelectAllRegions = () => {
    onChange({
      regions: regionOptions.map((r) => ({ value: r.id!.value })),
      stockType: currentStockType,
    });
  };

  // Check if all regions are selected
  const allRegionsSelected = regionOptions.length > 0 && selectedRegions.length === regionOptions.length;

  // Create update handler that validates before calling onChange
  const handleUpdate = (stockType: VanStockType, regions: VanStockRegion[]) => {
    onChange({
      regions: regions.filter((r) => r.id?.value).map((r) => ({ value: r.id!.value })),
      stockType: stockType,
    });
  };

  return (
    <Stack>
      <Stack direction="row" gap={2}>
        <Stack sx={{ flex: 2 }}>
          <Select
            label={intl.formatMessage({
              id: 'common.label.type',
            })}
            name="stockType"
            value={currentStockType === VanStockType.VAN_STOCK_TYPE_UNSPECIFIED ? '' : currentStockType}
            onChange={(e) => {
              const selectedType = Object.values(VanStockType).find((type) => type === e.target.value) as VanStockType;
              handleUpdate(selectedType, selectedRegions);
            }}
            required
            error={!currentStockType || !isVanStockUnique}
            fullWidth
          >
            {getValidVanStockTypes().map((type) => (
              <MenuItem key={type} value={type}>
                {getVanStockTypeLabel(type)}
              </MenuItem>
            ))}
          </Select>
        </Stack>
        <Stack sx={{ flex: 3 }}>
          <InputLabel>
            <Typography variant="inputLabel">
              <FormattedMessage id="billOfMaterials.vanStock.regions" />
            </Typography>
          </InputLabel>
          <MuiAutocomplete
            multiple
            disablePortal
            options={regionOptions}
            value={selectedRegions}
            loading={isPending}
            loadingText="Loading..."
            getOptionLabel={(option) => option.name}
            isOptionEqualToValue={(option, value) =>
              Boolean(option.id?.value && value.id?.value && option.id.value === value.id.value)
            }
            onChange={(_, newValue) => {
              handleUpdate(currentStockType, newValue);
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                placeholder={
                  selectedRegions.length === 0
                    ? intl.formatMessage({
                        id: 'billOfMaterials.vanStock.selectRegions',
                      })
                    : ''
                }
                error={(!isPending && !selectedRegions.length) || !isVanStockUnique}
                fullWidth
              />
            )}
            renderOption={(props, option, { selected }) => (
              <li {...props}>
                <Checkbox checked={selected} />
                {option.name}
              </li>
            )}
            slotProps={{
              listbox: {
                style: { maxHeight: 250, overflow: 'auto' },
                component: (props) => (
                  <ul {...props}>
                    <li
                      onClick={handleSelectAllRegions}
                      style={{
                        padding: '8px 16px',
                        display: 'flex',
                        alignItems: 'center',
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        cursor: 'pointer',
                      }}
                    >
                      <Checkbox checked={allRegionsSelected} />
                      <Typography>
                        <FormattedMessage id="billOfMaterials.vanStock.selectAllRegions" />
                      </Typography>
                    </li>
                    {props.children}
                  </ul>
                ),
              },
            }}
          />
        </Stack>
      </Stack>

      {!isVanStockUnique && <FormHelperText helperText={errorMessage ?? ''} error />}
    </Stack>
  );
}
