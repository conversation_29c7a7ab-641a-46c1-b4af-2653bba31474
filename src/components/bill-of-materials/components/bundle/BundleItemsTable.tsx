import { Divider, Stack, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { ErpTableItem, Item } from '../../types';
import { useMemo } from 'react';
import {
  compareTimestamps,
  getFormattedItemPrice,
  getItemMinorPricePerUnit,
  getErpItemWarningsForContext,
  isInstallationKitBundleCollection,
  isStaticBundleCollection,
  isVanStockBundleCollection,
} from '../../utils';
import {
  Bundle,
  BundleCollection,
  BundleItem,
  ItemLabel,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { useBillOfMaterialsStore } from '../../BillOfMaterialsStore';
import { ListIcon } from '@ui/components/StandardIcons/ListIcon';
import { ErpItem } from '../../../../server/api/routers/billOfMaterials';
import { MoneyDropOutlinedIcon } from '@ui/components/StandardIcons/MoneyDropOutlinedIcon';
import { useNextLocale } from '../../hooks/useNextLocale';
import { Button } from '@ui/components/Button/Button';
import { DEFAULT_QUANTITY_FOR_NEW_ITEM } from '../../constants/constants';
import ItemDetailsPanel from '../common/ItemDetailsPanel';
import { BILL_OF_MATERIALS_MODALS } from 'components/bill-of-materials/BillOfMaterialsModals';
import { ItemsTable } from '../common/ItemsTable';
import { useValidateBundle } from '../../hooks/useValidateBundle';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

function getTitleForBundleCollection(bundleCollection: BundleCollection): React.ReactNode {
  if (isVanStockBundleCollection(bundleCollection)) {
    return <FormattedMessage id="billOfMaterials.editBundle.itemsInVersion" />;
  } else if (isInstallationKitBundleCollection(bundleCollection)) {
    return (
      <>
        <FormattedMessage id="billOfMaterials.editBundle.itemsInInstallationKit" />
        <TooltipAira title={<FormattedMessage id="billOfMaterials.editBundle.itemsInInstallationKitTooltip" />} />
      </>
    );
  }
  return <FormattedMessage id="billOfMaterials.editBundle.itemsInBundle" />;
}

export type BundleItemsTableProps = {
  bundle: Bundle;
  bundleCollection: BundleCollection;
  onItemsUpdate?: (items: BundleItem[]) => void;
  onItemLabelUpdate?: (label: ItemLabel | string | null, item: Item) => void;
  tableBackgroundColor?: string;
  disableCostOfAllItems?: boolean;
};

export function BundleItemsTable({
  bundle,
  onItemsUpdate,
  bundleCollection,
  onItemLabelUpdate,
  tableBackgroundColor,
  disableCostOfAllItems = false,
}: Readonly<BundleItemsTableProps>) {
  const { formatMessage } = useIntl();
  const { openModal } = useBillOfMaterialsStore();
  const { erpItems, isLoading: areErpItemsPending } = useErpItems();
  const nextLocale = useNextLocale();
  const isStaticBC = isStaticBundleCollection(bundleCollection);
  const itemTableTitle = getTitleForBundleCollection(bundleCollection);
  const bundleValidation = useValidateBundle(bundle, bundleCollection);
  const handleDeleteItem = (itemId: string, itemType: 'erp' | 'custom') => {
    if (onItemsUpdate && itemType === 'erp') {
      const updatedItems = bundle.items.filter((bundleItem) => bundleItem.itemId?.value !== itemId);
      onItemsUpdate(updatedItems);
    }
  };

  const handleQuantityChange = (itemId: string, newQuantity: number | undefined, itemType: 'erp' | 'custom') => {
    if (onItemsUpdate && itemType === 'erp') {
      const updatedItems = bundle.items.map((bundleItem) => {
        if (bundleItem.itemId?.value === itemId) {
          return {
            ...bundleItem,
            defaultQuantity: newQuantity,
          };
        }
        return bundleItem;
      });
      onItemsUpdate(updatedItems);
    }
  };

  const handleInstructionsChange = (itemId: string, newInstructions: string, itemType: 'erp' | 'custom') => {
    if (onItemsUpdate && itemType === 'erp') {
      const updatedItems = bundle.items.map((bundleItem) => {
        if (bundleItem.itemId?.value === itemId) {
          return {
            ...bundleItem,
            instructions: newInstructions,
          };
        }
        return bundleItem;
      });
      onItemsUpdate(updatedItems);
    }
  };

  const handleLabelUpdate = (itemId: string, label: ItemLabel | string | null, itemType: 'erp' | 'custom') => {
    if (itemType === 'erp') {
      const erpItem = erpItems[itemId];
      if (erpItem && onItemLabelUpdate) {
        onItemLabelUpdate(label, erpItem);
      }
    }
  };

  // Prepare items with ERP data
  const sortedItems = useMemo(() => {
    return bundle.items
      .toSorted((a, b) => {
        const timestampComparison = compareTimestamps(a.createdAt, b.createdAt);

        // If timestamps are equal, sort alphabetically by description
        if (timestampComparison === 0) {
          const descriptionA = erpItems[a.itemId?.value ?? '']?.description ?? '';
          const descriptionB = erpItems[b.itemId?.value ?? '']?.description ?? '';
          return descriptionA.localeCompare(descriptionB);
        }

        return timestampComparison;
      })
      .map((item) => {
        const itemId = item.itemId?.value;
        if (!itemId || !erpItems[itemId]) return null;

        const erpItem = erpItems[itemId];

        return {
          type: 'erp',
          itemId: itemId,
          quantity: item.defaultQuantity,
          instructions: item.instructions,
          erpItem: erpItem,
        } as ErpTableItem;
      })
      .filter((item): item is ErpTableItem => item !== null);
  }, [bundle.items, erpItems]);

  // Calculate cost of all items
  const costOfAllItems = useMemo(() => {
    const bundleItemsWithErpData = bundle.items
      .filter((item) => !!item.itemId?.value)
      .map((item) => ({
        ...item,
        ...erpItems[item.itemId!.value],
      }));
    const currency = bundleItemsWithErpData.find((item) => !!item.currency)?.currency ?? 'EUR';
    const totalCost = bundleItemsWithErpData.reduce((acc, item) => {
      const pricePerUnit = getItemMinorPricePerUnit(item);
      const defaultQuantity = item.defaultQuantity ?? 0;
      return acc + pricePerUnit * defaultQuantity;
    }, 0);
    return getFormattedItemPrice(
      {
        minorPrice: totalCost,
        currency,
      },
      nextLocale,
    );
  }, [bundle, erpItems, nextLocale]);

  const onItemCatalogueClose = (selectedItems: ErpItem[]) => {
    if (!onItemsUpdate) {
      return;
    }
    // Set defaults for new items
    const items = selectedItems.map((item) => {
      // Check if the item is already in the bundle
      const existingBundleItem = bundle.items.find((bundleItem) => bundleItem.itemId?.value === item.id?.value);

      if (existingBundleItem) {
        return existingBundleItem;
      }

      return {
        itemId: item.id,
        bundleId: bundle.id,
        editable: true,
        defaultQuantity: DEFAULT_QUANTITY_FOR_NEW_ITEM,
        instructions: undefined,
        createdAt: new Date(),
      } satisfies BundleItem;
    });
    onItemsUpdate(items);
  };

  return (
    <Stack sx={{ width: '100%' }} gap={1} marginBottom={2} borderRadius={2}>
      <Stack direction="row" sx={{ height: '40px' }} justifyContent="space-between" alignItems="center">
        <Stack direction="row">
          <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }} fontWeight={500} variant="headline4">
            {itemTableTitle}
          </Typography>
          {!disableCostOfAllItems && (
            <>
              <Divider sx={{ margin: '0 16px' }} color={grey[900]} flexItem orientation="vertical" />
              <Stack direction="row" gap={1}>
                <MoneyDropOutlinedIcon />
                <Typography sx={{ display: 'flex', alignItems: 'center' }} fontWeight={500} variant="body2">
                  <FormattedMessage id="billOfMaterials.costOfAllItems" />
                </Typography>
                <Typography sx={{ display: 'flex', alignItems: 'center' }} variant="body1">
                  {costOfAllItems}
                </Typography>
              </Stack>
            </>
          )}
        </Stack>

        {onItemsUpdate && (
          <Button
            data-testid="browse-items-button"
            onClick={() =>
              openModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE, {
                preSelectedItemIds: new Set(
                  bundle.items.map((item) => item.itemId?.value).filter((id?: string): id is string => !!id),
                ),
                onClose: onItemCatalogueClose,
              })
            }
            sx={{
              borderRadius: '16px',
              backgroundColor: '#22222608',
              height: '100%',
              '&:hover': { backgroundColor: '#22222616' },
            }}
          >
            <Stack justifyContent="space-between" direction="row" alignItems="center" gap={1}>
              <ListIcon color="#000" />
              <Typography variant="body1Emphasis">
                <FormattedMessage id="common.label.addItems" />
              </Typography>
            </Stack>
          </Button>
        )}
      </Stack>

      <ItemsTable
        items={sortedItems}
        isLoading={areErpItemsPending}
        quantityTooltipMessage={formatMessage({ id: 'billOfMaterials.editBundle.itemsTable.amountColumn.infoBox' })}
        extraWarningsForItem={(row, erpItem) => {
          if (row.type !== 'erp' || !erpItem) return [];
          return getErpItemWarningsForContext(erpItem, { bundle }).map((descriptor) =>
            formatMessage({ id: descriptor.id, defaultMessage: descriptor.defaultMessage }, descriptor.values),
          );
        }}
        renderDetailsPanel={(item) => {
          const erpItem = erpItems[item.itemId];
          if (item.type !== 'erp' || !erpItem) return null;

          // Exclude the current bundle from the included bundles array
          const filtered = erpItem.includedInBundles?.filter((b) => b.id !== bundle.id!.value) ?? [];
          const itemForPanel = { ...erpItem, includedInBundles: filtered };

          return <ItemDetailsPanel item={itemForPanel} showCostPerUnit={!isStaticBC} />;
        }}
        showCostPerUnit={isStaticBC}
        {...(onItemsUpdate && {
          onDeleteItem: handleDeleteItem,
          onQuantityChange: handleQuantityChange,
          onInstructionsChange: handleInstructionsChange,
        })}
        {...(onItemLabelUpdate && {
          onItemLabelUpdate: handleLabelUpdate,
        })}
        errors={bundleValidation.validationErrors.items}
        tableBackgroundColor={tableBackgroundColor}
      />
    </Stack>
  );
}
