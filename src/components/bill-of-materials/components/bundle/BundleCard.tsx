import { CircularProgress, Stack } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { BundleCardEdit, UpdateBundleData } from './BundleCardEdit';
import { BundleCardView } from './BundleCardView';
import { beige } from '@ui/theme/colors';
import { useBundleCollectionStore } from '../../stores/bundleCollectionStore';
import { ANIMATION_DURATION } from '../../constants/constants';

export type BundleProps = {
  bundle: Bundle;
  bundleCollection: BundleCollection;
  onUpdate?: (data: UpdateBundleData) => Promise<void>;
  onCreate?: (bundle: Bundle) => Promise<void>;
  isLoading?: boolean;
  isEditing: boolean;
  setIsEditing: (bundleId: string, isEditing: boolean) => void;
};

export function BundleCard({
  bundle,
  bundleCollection,
  onUpdate,
  onCreate,
  isLoading,
  setIsEditing,
  isEditing,
}: BundleProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const DEFAULT_HEIGHT = 'auto';
  const [currentHeight, setCurrentHeight] = useState<number | 'auto'>('auto');
  const [transition, setTransition] = useState<string>('none');
  const { expandedBundleIds, setIsBundleExpanded } = useBundleCollectionStore();
  const isExpanded = bundle.id?.value && expandedBundleIds.has(bundle.id.value);
  useEffect(() => {
    setCurrentHeight(contentRef.current?.scrollHeight ?? DEFAULT_HEIGHT);
    if (isEditing || isExpanded) {
      // This implements a two-phase height transition:
      // 1. First, set explicit pixel height for smooth animation
      // 2. After animation completes, switch to 'auto' height to accommodate dynamic content changes
      // Without this approach, any content changes inside the expanded card, such as
      // expanding an item for details, would not trigger height adjustments
      setTimeout(() => {
        setCurrentHeight(DEFAULT_HEIGHT);
      }, ANIMATION_DURATION);
    }
  }, [isEditing, isExpanded]);

  useEffect(() => {
    if (currentHeight === 'auto') {
      setTransition('none');
    } else {
      setTransition(`height ${ANIMATION_DURATION}ms`);
    }
  }, [currentHeight]);

  const onBundleUpdate = async (data: UpdateBundleData) => {
    await onUpdate?.(data);
  };

  const onBundleCreate = async (newBundle: Bundle) => {
    await onCreate?.(newBundle);
  };

  const updateIsExpanded = (value: boolean) => {
    const id = bundle.id?.value;

    if (id) {
      setCurrentHeight(contentRef.current?.scrollHeight ?? DEFAULT_HEIGHT);
      setIsBundleExpanded(id, value);
    }
  };

  const updateIsEditing = (value: boolean) => {
    setCurrentHeight(contentRef.current?.scrollHeight ?? DEFAULT_HEIGHT);
    setIsEditing(bundle.id?.value ?? 'new', value);
  };
  const bundleId = bundle.id?.value ?? 'new';

  return (
    <Stack
      data-testid={`bundle-card-${bundleId}`}
      sx={{
        width: '100%',
        height: currentHeight,
        overflow: 'hidden',
        position: 'relative',
        transition: transition,
        backgroundColor: isEditing ? '#FFFFFF' : beige[100],
        borderRadius: '16px',
        boxShadow: isEditing ? '0px 8px 36px rgba(0, 0, 0, 0.25)' : 'none',
      }}
    >
      {isLoading && (
        <Stack
          sx={{
            zIndex: 1,
            position: 'absolute',
            width: '100%',
            height: '100%',
          }}
          alignItems="center"
          justifyContent="center"
        >
          <CircularProgress />
        </Stack>
      )}
      <Stack ref={contentRef}>
        {isEditing ? (
          <BundleCardEdit
            bundleCollection={bundleCollection}
            bundle={bundle}
            onUpdate={onBundleUpdate}
            onCreate={onBundleCreate}
            setIsEditing={updateIsEditing}
          />
        ) : (
          <BundleCardView
            setIsExpanded={updateIsExpanded}
            bundleCollection={bundleCollection}
            bundle={bundle}
            setIsEditing={updateIsEditing}
          />
        )}
      </Stack>
    </Stack>
  );
}
