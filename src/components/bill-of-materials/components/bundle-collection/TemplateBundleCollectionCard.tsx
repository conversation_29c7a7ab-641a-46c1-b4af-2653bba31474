import { AccordionDetails, AccordionSummary, Box, CircularProgress, Stack, Typography } from '@mui/material';
import { useRef, useState } from 'react';
import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { TemplateBundleCollectionCardEdit } from './TemplateBundleCollectionCardEdit';
import { TemplateBundleCollectionCardView } from './TemplateBundleCollectionCardView';
import { StyledAccordion } from './styles';
import { BundleList } from './BundleList';
import { MoveButtons } from './MoveButtons';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { useBundleCollectionStore } from '../../stores/bundleCollectionStore';

export type TemplateBundleCollectionCardProps = {
  bundleCollection: BundleCollection;
  onUpdate: (updatedCollection: BundleCollection) => Promise<void>;
  onCreate?: (newCollection: BundleCollection) => Promise<void>;
  onDelete: () => Promise<void>;
  onMoveUp: () => Promise<void>;
  onMoveDown: () => Promise<void>;
  onClose?: () => void; // Added callback for when a new collection is discarded
  isFirst: boolean;
  isLast: boolean;
};

export function TemplateBundleCollectionCard({
  bundleCollection,
  onUpdate,
  onCreate,
  onDelete,
  onMoveUp,
  onMoveDown,
  onClose,
  isFirst,
  isLast,
}: TemplateBundleCollectionCardProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const { editingCollectionIds, loadingCollectionIds, deletingCollectionIds, movingCollectionIds, setIsEditing } =
    useBundleCollectionStore();
  const collectionId = bundleCollection.id?.value ?? 'new';
  const isEditing = editingCollectionIds.has(collectionId);
  const isLoading = loadingCollectionIds.has(collectionId);
  const isDeleting = deletingCollectionIds.has(collectionId);
  const isMoving = movingCollectionIds.includes(collectionId);

  const handleUpdateCollection = async (data: BundleCollection) => {
    await onUpdate(data);
    setIsEditing(collectionId, false);
  };

  return (
    <Stack
      direction="row"
      alignItems="flex-start"
      gap={2}
      id={`template-collection-wrapper-${collectionId}`}
      data-testid={`template-collection-wrapper-${collectionId}`}
      data-collection-id={collectionId}
      ref={cardRef}
      sx={{
        minHeight: isEditing ? undefined : '80px', // Maintain minimum height for non-editing state
        minWidth: 0,
      }}
    >
      <MoveButtons onMoveUp={onMoveUp} onMoveDown={onMoveDown} isFirst={isFirst} isLast={isLast} isMoving={isMoving} />

      {isEditing ? (
        <Stack
          data-testid={`template-collection-${collectionId}`}
          sx={{
            flex: 1,
            minWidth: 0, //prevent overflow horizontally
            width: '100%',
            backgroundColor: '#FFFFFF',
            borderRadius: '16px',
            boxShadow: '0px 8px 36px rgba(0, 0, 0, 0.25)',
            padding: '16px',
            position: 'relative', // Add relative positioning
          }}
        >
          {isLoading && (
            <Stack
              sx={{
                zIndex: 1,
                position: 'absolute',
                width: '100%',
                height: '100%',
              }}
              alignItems="center"
              justifyContent="center"
            >
              <CircularProgress />
            </Stack>
          )}
          <Stack ref={contentRef}>
            <TemplateBundleCollectionCardEdit
              bundleCollection={bundleCollection}
              onUpdate={handleUpdateCollection}
              onCreate={onCreate}
              onClose={onClose}
              setIsEditing={(isEditing: boolean) => {
                setIsEditing(collectionId, isEditing);
              }}
            />
          </Stack>
        </Stack>
      ) : (
        <StyledAccordion
          expanded={isExpanded}
          data-testid={`template-collection-${collectionId}`}
          sx={{
            flex: 1,
            position: 'relative', // Add relative positioning
            minWidth: 0,
          }}
        >
          <AccordionSummary>
            <Stack
              sx={{ width: '100%', alignItems: 'flex-start', minWidth: 0 }}
              gap={2}
              direction="row"
              onClick={() => setIsExpanded((prev) => !prev)}
              data-testid="bundle-collection-card-accordion-toggle"
            >
              <Box flex="0 1 25%">
                <Stack direction="row" gap={1} sx={{ height: '100%' }}>
                  <Chevron
                    style={{ flex: '0 0 auto', height: '100%', width: '20px' }}
                    transitionDuration="0.2s"
                    direction={isExpanded ? 'up' : 'down'}
                    height={24}
                    width={20}
                  />
                  <Typography
                    sx={{
                      height: '100%',
                      display: '-webkit-box',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: isExpanded ? 'unset' : 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxHeight: isExpanded ? 'none' : '35px',
                    }}
                    variant="headline4"
                    fontWeight={500}
                  >
                    {bundleCollection.title}
                  </Typography>
                </Stack>
              </Box>
              <Box flex="1 0 60%">
                <Typography
                  sx={{
                    display: '-webkit-box',
                    WebkitBoxOrient: 'vertical',
                    WebkitLineClamp: isExpanded ? 'unset' : 2,
                    overflow: 'hidden',
                    alignItems: 'flex-start',
                    textOverflow: 'ellipsis',
                    maxHeight: isExpanded ? 'none' : '40px',
                  }}
                  variant="body2"
                >
                  {bundleCollection.description}
                </Typography>
              </Box>
              <TemplateBundleCollectionCardView
                setIsEditing={(isEditing: boolean) => {
                  setIsEditing(collectionId, isEditing);
                }}
                onDelete={onDelete}
                isDeleting={isDeleting}
              />
            </Stack>
          </AccordionSummary>
          <AccordionDetails>
            <Stack gap={2} data-testid="bundle-collection-card-detail-content" pt={2}>
              <BundleList bundleCollection={bundleCollection} />
            </Stack>
          </AccordionDetails>
        </StyledAccordion>
      )}
    </Stack>
  );
}
