import styled from '@emotion/styled';
import { Accordion, Box } from '@mui/material';
import { grey } from '@ui/theme/colors';

export const CircularButton = styled(Box)({
  borderRadius: '50%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  cursor: 'pointer',
  height: '35px',
  width: '35px',
  transition: 'background-color 0.2s, transform 0.15s',
  '&:hover': { backgroundColor: grey[200] },
  '&:active': { transform: 'scale(0.92)' },
});

export const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  transition: 'background-color 0.2s, box-shadow 0.2s',
  borderRadius: '24px !important',
  padding: '24px',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: 'outline 0.2s',
  },
  '.MuiAccordionDetails-root': {
    padding: 0,
  },
  '.MuiAccordionSummary-root': {
    padding: 0,
  },
});
