import { CircularProgress, Stack, Tooltip } from '@mui/material';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { useIntl } from 'react-intl';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import { useState } from 'react';
import { CircularButton } from './styles';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';

export type TemplateBundleCollectionCardViewProps = {
  setIsEditing: (isEditing: boolean) => void;
  onDelete: () => void;
  isDeleting: boolean;
};

export function TemplateBundleCollectionCardView({
  setIsEditing,
  onDelete,
  isDeleting,
}: TemplateBundleCollectionCardViewProps) {
  const { formatMessage } = useIntl();
  const [deleteAnchorEl, setDeleteAnchorEl] = useState<HTMLElement | null>(null);
  const deletePopoverOpen = Boolean(deleteAnchorEl);

  const handleDeleteClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
    setDeleteAnchorEl(event.currentTarget);
  };

  const handleDeleteCancel = () => {
    setDeleteAnchorEl(null);
  };

  const handleDeleteConfirm = () => {
    setDeleteAnchorEl(null);
    onDelete();
  };

  return (
    <Stack
      className="actions"
      direction="row"
      sx={{
        flex: '0 0 auto',
        opacity: 0,
        transition: 'opacity 0.3s',
        height: '100%',
        '.Mui-expanded &, .MuiAccordionSummary-root:hover &': { opacity: 1 },
      }}
      alignItems="flex-start"
    >
      <Tooltip
        title={FormatForTooltip(
          formatMessage({
            id: 'billOfMaterials.editCollection',
          }),
        )}
      >
        <CircularButton
          data-testid="edit-collection-button"
          onClick={(e) => {
            e.stopPropagation();
            setIsEditing(true);
          }}
        >
          <PenOutlinedIcon width="24px" height="24px" />
        </CircularButton>
      </Tooltip>
      <Tooltip
        title={FormatForTooltip(
          formatMessage({
            id: 'billOfMaterials.deleteCollection',
          }),
        )}
      >
        <CircularButton
          data-testid="delete-collection-button"
          onClick={handleDeleteClick}
          sx={{
            pointerEvents: isDeleting ? 'none' : 'auto',
          }}
        >
          {isDeleting ? <CircularProgress size={20} /> : <BinOutlinedIcon width="22px" height="22px" />}
        </CircularButton>
      </Tooltip>

      <ConfirmationPopover
        anchorEl={deleteAnchorEl}
        open={deletePopoverOpen}
        onClose={handleDeleteCancel}
        title={formatMessage({
          id: 'billOfMaterials.confirmDeleteCollection.title',
        })}
        description={formatMessage({
          id: 'billOfMaterials.confirmDeleteCollection.description',
        })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={handleDeleteConfirm}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        testId="delete-collection-confirmation-popover"
      />
    </Stack>
  );
}
