import { Stack, Typography } from '@mui/material';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { BundleCard } from '../bundle/BundleCard';
import { useBundleCollectionStore } from '../../stores/bundleCollectionStore';
import { api } from '../../../../utils/api';
import { UpdateBundleData } from '../bundle/BundleCardEdit';
import { bundleItemsToBundleItemRequest, compareTimestamps, isStaticBundleCollection } from '../../utils';
import { useMemo } from 'react';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { But<PERSON> } from '@ui/components/Button/Button';
import toast from 'react-hot-toast';

interface BundleListProps {
  bundleCollection: BundleCollection;
}

function createPlaceholderBundle(): Bundle {
  return {
    id: undefined,
    title: '',
    description: '',
    items: [],
    bundleCollectionId: undefined,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function BundleList({ bundleCollection }: BundleListProps) {
  const collectionId = bundleCollection.id?.value ?? 'new';
  const {
    editingBundleIds,
    loadingBundleIds,
    setIsBundleEditing,
    placeholderBundles,
    setPlaceholderBundle,
    setIsBundleExpanded,
  } = useBundleCollectionStore();

  const apiUtils = api.useUtils();
  const updateBundle = api.BillOfMaterials.updateBundle.useMutation();
  const updateBundleItem = api.BillOfMaterials.updateBundleItem.useMutation();
  const removeBundleItems = api.BillOfMaterials.removeBundleItems.useMutation();
  const createBundle = api.BillOfMaterials.createBundle.useMutation();
  const addBundleItems = api.BillOfMaterials.addBundleItems.useMutation();

  const sortedBundles = useMemo(
    () => bundleCollection.bundles.toSorted((a, b) => compareTimestamps(a.createdAt, b.createdAt)),
    [bundleCollection.bundles],
  );

  const handleAddBundle = () => {
    setPlaceholderBundle(collectionId, createPlaceholderBundle());
    setIsBundleEditing('new', true);
  };

  const onBundleCreate = async (bundle: Bundle) => {
    const bundleCollectionId = bundleCollection.id?.value;
    if (!bundleCollectionId) return;

    const response = await createBundle.mutateAsync({
      title: bundle.title,
      description: bundle.description,
      bundleCollectionId,
      details: bundle.details,
    });

    if (response.bundleId?.value) {
      if (bundle.items.length > 0) {
        await addBundleItems.mutateAsync(bundleItemsToBundleItemRequest(bundle.items, response.bundleId.value));
      }

      setIsBundleExpanded(response.bundleId.value, true);
    }
    await apiUtils.BillOfMaterials.getBundleCollections.invalidate();

    setPlaceholderBundle(collectionId, undefined);
  };

  const onBundleUpdate = async (data: UpdateBundleData) => {
    const bundleCollectionId = data.updatedBundle.bundleCollectionId?.value;
    const bundleId = data.updatedBundle.id?.value;
    if (!bundleCollectionId || !bundleId) return;

    try {
      await Promise.all([
        updateBundle.mutateAsync({
          bundleCollectionId,
          title: data.updatedBundle.title,
          description: data.updatedBundle.description,
          bundleId,
          details: data.updatedBundle.details,
        }),
        ...data.updatedBundleItems.map((item) => {
          const itemId = item.itemId?.value;
          if (!itemId) return Promise.resolve();
          return updateBundleItem.mutateAsync({
            bundleId,
            itemId,
            defaultQuantity: item.defaultQuantity,
            editable: item.editable,
            instructions: item.instructions,
          });
        }),
        addBundleItems.mutateAsync(bundleItemsToBundleItemRequest(data.addedBundleItems)),
        removeBundleItems.mutateAsync({
          bundleId,
          itemIds: data.removedBundleItems.map((item) => item.itemId?.value).filter((id): id is string => !!id),
        }),
      ]);
      toast.success('Bundle succesfully updated');
    } catch (e) {
      toast.error('There was a problem updating the bundle');
      throw e;
    }

    await apiUtils.BillOfMaterials.getBundleCollections.invalidate();
    setIsBundleEditing(bundleId, false);
    setIsBundleExpanded(bundleId, true);
  };

  const bundleListTitle: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.versions'
    : 'billOfMaterials.bundles';
  const addBundleTitle: MessageDescriptor['id'] = isStaticBundleCollection(bundleCollection)
    ? 'billOfMaterials.addVersion'
    : 'billOfMaterials.addBundle';

  return (
    <Stack gap={2}>
      <Stack sx={{ height: '40px' }} direction="row" justifyContent="space-between" alignItems="end">
        <Typography fontWeight={500} variant="headline4">
          <FormattedMessage id={bundleListTitle} />
        </Typography>
        <Button
          data-testid="add-bundle-button"
          onClick={handleAddBundle}
          sx={{ borderRadius: '16px', height: '100%' }}
          variant="outlined"
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between" gap={1}>
            <AddOutlinedIcon />
            <Typography variant="body2Emphasis">
              <FormattedMessage id={addBundleTitle} />
            </Typography>
          </Stack>
        </Button>
      </Stack>
      <Stack gap={2} flexDirection="column">
        {placeholderBundles[collectionId] && (
          <BundleCard
            onCreate={onBundleCreate}
            setIsEditing={(_, isEditing: boolean) => {
              if (!isEditing) {
                setPlaceholderBundle(collectionId, undefined);
              }
            }}
            bundleCollection={bundleCollection}
            isEditing={editingBundleIds.has('new')}
            isLoading={loadingBundleIds.has('new')}
            key="new"
            bundle={placeholderBundles[collectionId]!}
          />
        )}
        {sortedBundles.map((bundle) => {
          const bundleId = bundle.id?.value;
          if (!bundleId) return null;
          return (
            <BundleCard
              key={bundleId}
              bundleCollection={bundleCollection}
              setIsEditing={(_, isEditing) => setIsBundleEditing(bundleId, isEditing)}
              isEditing={editingBundleIds.has(bundleId)}
              isLoading={loadingBundleIds.has(bundleId)}
              onUpdate={onBundleUpdate}
              bundle={bundle}
            />
          );
        })}
      </Stack>
    </Stack>
  );
}
