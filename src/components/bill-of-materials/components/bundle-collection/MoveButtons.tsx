import { Stack, Tooltip } from '@mui/material';
import { useIntl } from 'react-intl';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { CircularButton } from './styles';
import { useState } from 'react';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { grey } from '@ui/theme/colors';

interface MoveButtonsProps {
  onMoveUp: () => Promise<void>;
  onMoveDown: () => Promise<void>;
  isFirst: boolean;
  isLast: boolean;
  isMoving: boolean;
}

export function MoveButtons({ onMoveUp, onMoveDown, isFirst, isLast, isMoving }: MoveButtonsProps) {
  const { formatMessage } = useIntl();
  const [activeButton, setActiveButton] = useState<'up' | 'down' | null>(null);

  const handleMoveUp = async () => {
    setActiveButton('up');
    await onMoveUp();
    setTimeout(() => setActiveButton(null), 200);
  };

  const handleMoveDown = async () => {
    setActiveButton('down');
    await onMoveDown();
    setTimeout(() => setActiveButton(null), 200);
  };

  return (
    <Stack direction="column" alignItems="center" justifyContent="center" gap={1}>
      <Tooltip
        title={FormatForTooltip(
          formatMessage({
            id: 'billOfMaterials.moveUp',
          }),
        )}
      >
        <CircularButton
          onClick={handleMoveUp}
          sx={{
            opacity: isFirst || isMoving ? 0.5 : 1,
            pointerEvents: isFirst || isMoving ? 'none' : 'auto',
            backgroundColor: activeButton === 'up' ? grey[300] : 'transparent',
            transform: activeButton === 'up' ? 'scale(0.92)' : 'scale(1)',
          }}
          data-testid="move-up-button"
        >
          <Chevron direction="up" height={24} width={24} />
        </CircularButton>
      </Tooltip>
      <Tooltip
        title={FormatForTooltip(
          formatMessage({
            id: 'billOfMaterials.moveDown',
          }),
        )}
      >
        <CircularButton
          onClick={handleMoveDown}
          sx={{
            opacity: isLast || isMoving ? 0.5 : 1,
            pointerEvents: isLast || isMoving ? 'none' : 'auto',
            backgroundColor: activeButton === 'down' ? grey[300] : 'transparent',
            transform: activeButton === 'down' ? 'scale(0.92)' : 'scale(1)',
          }}
          data-testid="move-down-button"
        >
          <Chevron direction="down" height={24} width={24} />
        </CircularButton>
      </Tooltip>
    </Stack>
  );
}
