import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import { beforeEach } from 'vitest';
import { billOfMaterialsTestSetup } from '../../test-utils';
import { BillOfMaterials } from '../../BillOfMaterials';
import { renderWithProviders } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { heatDesignTheme } from '../../../heat-design/HeatDesignTheme';

billOfMaterialsTestSetup();

beforeEach(() => {
  renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <AiraThemeProvider theme={heatDesignTheme}>
        <BillOfMaterials />
      </AiraThemeProvider>
    </IntlProvider>,
  );
});
describe('BundleCollectionCard', () => {
  describe('StaticBundleCollectionCard', () => {
    beforeEach(async () => {
      await waitFor(() => {
        screen.getAllByTestId(/static-bundle-collection-card/);
      });
    });

    it('should render BundleCollectionCard component', () => {
      const cards = screen.getAllByTestId(/static-bundle-collection-card-container/);
      expect(cards.length).toBeGreaterThan(0);
      expect(cards[0]).toBeInTheDocument();
    });

    it('should expand when clicked', () => {
      const cards = screen.getAllByTestId(/static-bundle-collection-card-container/);
      const card = cards[0]!;
      expect(within(card).getByTestId('bundle-collection-card-detail-content')).not.toBeVisible();
      fireEvent.click(within(card).getByTestId('bundle-collection-card-accordion-toggle'));
      expect(within(card).getByTestId('bundle-collection-card-detail-content')).toBeVisible();
    });

    it('should have a list of bundles inside', () => {
      const cards = screen.getAllByTestId(/static-bundle-collection-card-container/);
      const card = cards[0]!;
      expect(within(card).getByTestId('bundle-collection-card-detail-content')).not.toBeVisible();
      fireEvent.click(within(card).getByTestId('bundle-collection-card-accordion-toggle'));
      const bundleCards = within(card).getAllByTestId(/bundle-card/);
      expect(bundleCards.length).toBeGreaterThan(0);
      expect(bundleCards[0]!).toBeVisible();
    });

    it('should open card to add new bundle when clicking on new bundle button', () => {
      const cards = screen.getAllByTestId(/static-bundle-collection-card-container/);
      const card = cards[0]!;
      fireEvent.click(within(card).getByTestId('bundle-collection-card-accordion-toggle'));
      const addBundleButton = within(card).getByTestId('add-bundle-button');
      expect(addBundleButton).toBeVisible();
      fireEvent.click(addBundleButton);
      expect(within(card).getByTestId('bundle-card-new')).toBeVisible();
    });
  });

  describe('TemplateBundleCollectionCard', () => {
    beforeEach(async () => {
      await waitFor(() => {
        screen.getAllByTestId(/template-collection/);
      });
    });

    describe('Create New Collection', () => {
      it('should open new collection form when add button is clicked', async () => {
        const addButton = screen.getByTestId('add-bundle-collection-button');
        fireEvent.click(addButton);
        await waitFor(() => {
          expect(screen.getByTestId('template-collection-new')).toBeVisible();
        });
      });

      it('should show form validation when trying to save empty collection', async () => {
        const addButton = screen.getByTestId('add-bundle-collection-button');
        fireEvent.click(addButton);
        const saveButton = screen.getByTestId('save-bundle-collection-button');

        expect(saveButton).toHaveStyle({ opacity: '0.5' }); // Initially disabled

        const titleInputContainer = screen.getByTestId('bundle-collection-title-input');
        const formInputs = titleInputContainer.querySelectorAll('input');
        if (formInputs.length > 0) {
          fireEvent.change(formInputs[0]!, { target: { value: 'Test Collection' } });
        }

        expect(saveButton).toHaveStyle({ opacity: '0.5' }); // Still disabled

        const descriptionInputContainer = screen.getByTestId('bundle-collection-description-input');
        const descInputs = descriptionInputContainer.querySelectorAll('input');
        if (descInputs.length > 0) {
          fireEvent.change(descInputs[0]!, { target: { value: 'Test Description' } });
        }

        expect(saveButton).toHaveStyle({ opacity: '1' }); // Now enabled
      });

      it('should attempt to close form when cancelling new collection', async () => {
        const addButton = screen.getByTestId('add-bundle-collection-button');
        fireEvent.click(addButton);

        // Fill in form using the correct input selectors
        const titleInputContainer = screen.getByTestId('bundle-collection-title-input');
        const descriptionInputContainer = screen.getByTestId('bundle-collection-description-input');

        // Find the actual input elements
        const titleInputs = titleInputContainer.querySelectorAll('input');
        const descriptionInputs = descriptionInputContainer.querySelectorAll('input');

        if (titleInputs.length > 0 && descriptionInputs.length > 0) {
          fireEvent.change(titleInputs[0]!, { target: { value: 'Test Collection' } });
          fireEvent.change(descriptionInputs[0]!, { target: { value: 'Test Description' } });
        }

        // Cancel
        const closeButton = screen.getByTestId('close-edit-bundle-collection-button');
        fireEvent.click(closeButton);

        // The confirmation popover should be visible now
        const confirmationPopover = screen.getByRole('presentation');
        expect(confirmationPopover).toBeInTheDocument();
      });
    });

    describe('Edit Collection', () => {
      it('should show edit mode when edit button is clicked', () => {
        const collections = screen.getAllByTestId(/template-collection-wrapper/);
        const collection = collections[0]!;
        const editButton = within(collection).getByTestId('edit-collection-button');
        fireEvent.click(editButton);
        expect(within(collection).getByTestId('bundle-collection-title-input')).toBeVisible();
        expect(within(collection).getByTestId('bundle-collection-description-input')).toBeVisible();
      });
    });

    describe('Move Collection', () => {
      it('should show move buttons appropriately based on position', () => {
        const collections = screen.getAllByTestId(/template-collection-wrapper/);

        // First collection - check for down button only
        const firstCollection = collections[0]!;
        expect(within(firstCollection).getByTestId('move-down-button')).toBeVisible();

        // Last collection - check for up button
        const lastCollection = collections[collections.length - 1]!;
        expect(within(lastCollection).getByTestId('move-up-button')).toBeVisible();

        // Middle collections (if any) - both buttons
        if (collections.length > 2) {
          const middleCollection = collections[1]!;
          expect(within(middleCollection).getByTestId('move-up-button')).toBeVisible();
          expect(within(middleCollection).getByTestId('move-down-button')).toBeVisible();
        }
      });
    });

    describe('Delete Collection', () => {
      it('should show delete confirmation when delete button is clicked', async () => {
        const collections = screen.getAllByTestId(/template-collection-wrapper/);
        const collection = collections[0]!;
        const deleteButton = within(collection).getByTestId('delete-collection-button');
        fireEvent.click(deleteButton);

        await waitFor(() => {
          expect(screen.getByTestId('delete-collection-confirmation-popover')).toBeVisible();
        });
      });
    });
  });
});
