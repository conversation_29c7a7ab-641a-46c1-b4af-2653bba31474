import { AccordionDetails, AccordionSummary, Box, Stack, Typography } from '@mui/material';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { useState } from 'react';
import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { BundleList } from './BundleList';
import { StyledAccordion } from './styles';

export type BundleCollectionProps = {
  bundleCollection: BundleCollection;
  icon?: React.ReactNode;
};

export function StaticBundleCollectionCard({ bundleCollection, icon }: BundleCollectionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <StyledAccordion
      expanded={isExpanded}
      data-testid={`static-bundle-collection-card-container-${bundleCollection.id?.value}`}
    >
      <AccordionSummary>
        <Stack
          sx={{ width: '100%' }}
          gap={2}
          alignItems="flex-start"
          direction="row"
          onClick={() => setIsExpanded((prev) => !prev)}
          data-testid="bundle-collection-card-accordion-toggle"
        >
          <Box flex="0 1 25%" sx={{ height: '100%' }}>
            <Stack direction="row" alignItems="flex-start" gap={1}>
              <Chevron
                style={{ width: '24px', height: '24px' }}
                transitionDuration="0.2s"
                direction={isExpanded ? 'up' : 'down'}
                height={24}
                width={20}
              />
              {icon}
              <Typography variant="headline4" sx={{ display: 'flex', alignItems: 'flex-start' }} fontWeight={500}>
                {bundleCollection.title}
              </Typography>
            </Stack>
          </Box>
          <Box flex="1 0 75%" sx={{ display: 'flex', alignItems: 'flex-start', height: '100%' }}>
            <Typography variant="body2">{bundleCollection.description}</Typography>
          </Box>
        </Stack>
      </AccordionSummary>
      <AccordionDetails>
        <Stack gap={2} data-testid="bundle-collection-card-detail-content" pt={2}>
          <BundleList bundleCollection={bundleCollection} />
        </Stack>
      </AccordionDetails>
    </StyledAccordion>
  );
}
