import { Stack, Typography } from '@mui/material';
import { TextField as AiraTextField } from '@ui/components/TextField/TextField';
import { useState } from 'react';
import { cloneDeep, isEqual } from 'lodash';
import { FormattedMessage, useIntl } from 'react-intl';
import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { ConfirmationPopover } from '../common/ConfirmationPopover';
import { CircularButton } from './styles';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { brandYellow, grey } from '@ui/theme/colors';
import { Switch } from '@ui/components/Switch/Switch';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';

export type TemplateBundleCollectionCardEditProps = {
  bundleCollection: BundleCollection;
  setIsEditing: (value: boolean) => void;
  onUpdate?: (updatedCollection: BundleCollection) => void;
  onCreate?: (newCollection: BundleCollection) => void;
  onClose?: () => void;
};

export function TemplateBundleCollectionCardEdit({
  bundleCollection,
  setIsEditing,
  onUpdate,
  onCreate,
  onClose,
}: TemplateBundleCollectionCardEditProps) {
  const [editableCollection, setEditableCollection] = useState<BundleCollection>(cloneDeep(bundleCollection));
  const [originalCollection] = useState<BundleCollection>(cloneDeep(bundleCollection));
  const isDirty = !isEqual(originalCollection, editableCollection);
  const { formatMessage } = useIntl();
  const [closeAnchorEl, setCloseAnchorEl] = useState<HTMLElement | null>(null);
  const closePopoverOpen = Boolean(closeAnchorEl) && isDirty;

  function onCollectionChange<T extends keyof BundleCollection>(data: Record<T, BundleCollection[T]>) {
    setEditableCollection((prev) => ({ ...prev, ...data }));
  }

  function onCollectionSave() {
    if (bundleCollection.id?.value) {
      onUpdate?.(editableCollection);
    } else {
      onCreate?.(editableCollection);
    }
  }

  const close = () => {
    setIsEditing(false);
    onClose?.();
  };

  const handleCloseClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (isDirty) {
      setCloseAnchorEl(event.currentTarget);
    } else {
      close();
    }
  };

  const handleCloseCancel = () => {
    setCloseAnchorEl(null);
  };
  const handleCloseConfirm = () => {
    setCloseAnchorEl(null);
    close();
  };

  const isSaveDisabled = !editableCollection.title || !editableCollection.description;

  return (
    <Stack gap={2} paddingBottom={4} width="100%">
      <Stack sx={{ width: '100%' }} direction="row" justifyContent="space-between">
        <Typography variant="headline2">
          <FormattedMessage
            id={`${
              bundleCollection.id?.value
                ? 'billOfMaterials.editBundleCollection.titleEdit'
                : 'billOfMaterials.editBundleCollection.titleAdd'
            }`}
          />
        </Typography>
        <Stack alignItems="center" gap={2} direction="row">
          <CircularButton
            onClick={() => {
              if (!isSaveDisabled) {
                onCollectionSave();
              }
            }}
            sx={{
              pointerEvents: isDirty && !isSaveDisabled ? 'auto' : 'none',
              opacity: isDirty && !isSaveDisabled ? '1' : '0.5',
              background: isDirty && !isSaveDisabled ? brandYellow[400] : grey[200],
              '&:hover': { backgroundColor: isDirty && !isSaveDisabled ? brandYellow[500] : 'transparent' },
            }}
            data-testid="save-bundle-collection-button"
          >
            <CheckIconThin />
          </CircularButton>
          <CircularButton
            sx={{
              padding: '8px',
              '&:hover': { backgroundColor: grey[300] },
            }}
            data-testid="close-edit-bundle-collection-button"
            onClick={handleCloseClick}
          >
            <CrossOutlinedIcon />
          </CircularButton>
        </Stack>
      </Stack>
      <Stack gap={2} direction="row">
        <AiraTextField
          containerProps={{ sx: { flex: '2' } }}
          onChange={(value) => {
            onCollectionChange({ title: value.target.value });
          }}
          label={formatMessage({
            id: 'billOfMaterials.editBundleCollection.titleFieldLabel',
          })}
          name="title"
          data-testid="bundle-collection-title-input"
          variant="standard"
          value={editableCollection.title}
          error={!editableCollection.title}
          required
        />
        <AiraTextField
          containerProps={{ sx: { flex: '3' } }}
          onChange={(value) => {
            onCollectionChange({ description: value.target.value });
          }}
          label={formatMessage({
            id: 'billOfMaterials.editBundleCollection.descriptionFieldLabel',
          })}
          name="description"
          data-testid="bundle-collection-description-input"
          value={editableCollection.description}
          error={!editableCollection.description}
          required
          multiline
          minRows={2}
          maxRows={4}
        />
        <Stack direction="row" gap={1} alignItems="flex-end">
          <Switch
            label={formatMessage({ id: 'billOfMaterials.mandatoryBundle' })}
            checked={editableCollection.mandatory}
            onChange={() => onCollectionChange({ mandatory: !editableCollection.mandatory })}
          />
          <TooltipAira
            sx={{ alignSelf: 'flex-end' }}
            title={formatMessage({ id: 'billOfMaterials.mandatoryBundleTooltip' })}
          />
        </Stack>
      </Stack>
      <ConfirmationPopover
        anchorEl={closeAnchorEl}
        open={closePopoverOpen}
        onClose={handleCloseCancel}
        title={formatMessage({
          id: 'billOfMaterials.confirmCloseEdit.title',
        })}
        description={formatMessage({
          id: 'billOfMaterials.confirmCloseEdit.description',
        })}
        confirmText={formatMessage({ id: 'common.label.discard' })}
        onConfirm={handleCloseConfirm}
        confirmIcon={<CrossOutlinedIcon width={20} height={20} />}
        testId="close-edit-confirmation-popover"
      />
    </Stack>
  );
}
