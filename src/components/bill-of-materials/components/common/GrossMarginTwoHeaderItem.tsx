import { Divider, Stack, Typography } from '@mui/material';
import { ChartLineUpOutlinedIcon } from '@ui/components/StandardIcons/ChartLineUpOutlinedIcon';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { green, red } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { formatNumberCurrency } from 'utils/helpers';
import { CountryCode, marketConfiguration } from 'utils/marketConfigurations';

interface GrossMarginHeaderItemProps {
  /**
   * Includes: the total cost of any items ordered from ERP, the cost of any AIKs used (not the cost
   * of the items within each AIK, but each AIK itself) and the cost of any consumed van stock. As
   * minor amount i.e. 50173 = £501.73
   */
  hardwareCost: number;
  /**
   * The price after the designer has designed the solution for the customer, making any necessary
   * adjustments e.g. more radiators than quoted or a different outdoor unit. Excluding tax and as
   * minor amount i.e. 1471200 = £14,712.00.
   */
  designedPrice: number;
  /**
   * Any subsidies that were applied to reduce the customer's cost but that Aira would still receive.
   * For example, the boiler upgrade scheme (BUS) in the UK is £7500 at the time of writing - the UK
   * government will give this money to Aira, so we should consider it as income. As a minor amount
   * i.e. 750000 = £7,500. Note: this is not the same as a "discount", which is a price reduction at
   * Aira's expense e.g. a locked price adjustment.
   */
  subsidy: number;
  countryCode: CountryCode;
}

/**
 * Aira has a global target that installation are not to be below 50% for their gross margin 2.
 * This is so that we can cover the designer, administrative and the Swedish HQ costs.
 */
const GROSS_MARGIN_TWO_THRESHOLD = 0.5;

export function GrossMarginTwoHeaderItem({
  hardwareCost,
  designedPrice,
  subsidy,
  countryCode,
}: GrossMarginHeaderItemProps) {
  const grossMarginTwo = (designedPrice + subsidy - hardwareCost) / (designedPrice + subsidy);
  const locale = marketConfiguration[countryCode].locale;
  const currency = marketConfiguration[countryCode].currency;

  return (
    <>
      <Divider flexItem orientation="vertical" />
      <Stack direction="row" gap={2} alignItems="center" data-testid="gross-margin-two">
        <Stack direction="row" gap={1} alignItems="center">
          <ChartLineUpOutlinedIcon />
          <Typography sx={{ display: 'flex', alignItems: 'center' }} variant="body2">
            <FormattedMessage id="billOfMaterials.grossMarginTwo" />
          </Typography>
          <TooltipAira
            title={
              <FormattedMessage
                id="billOfMaterials.grossMarginTwo.info"
                values={{
                  designCost: formatNumberCurrency(designedPrice, countryCode, currency),
                  subsidies: formatNumberCurrency(subsidy, countryCode, currency),
                }}
              />
            }
          />
        </Stack>
        <Typography
          sx={{
            display: 'flex',
            alignItems: 'center',
            color: grossMarginTwo > GROSS_MARGIN_TWO_THRESHOLD ? green[700] : red[600],
          }}
          variant="body2Emphasis"
        >
          {Number.isNaN(grossMarginTwo) || !Number.isFinite(grossMarginTwo)
            ? '- %'
            : grossMarginTwo.toLocaleString(locale, {
                style: 'percent',
                minimumFractionDigits: 1,
                signDisplay: 'exceptZero',
              })}
        </Typography>
      </Stack>
    </>
  );
}
