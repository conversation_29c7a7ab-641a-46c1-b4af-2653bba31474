import { Box, Popover, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { grey } from '@ui/theme/colors';
import { ReactNode } from 'react';
import { useIntl } from 'react-intl';

export interface ConfirmationPopoverProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  title: string;
  description: string;
  confirmText: string;
  onConfirm: () => void;
  confirmIcon?: ReactNode;
  testId?: string;
}

export function ConfirmationPopover({
  anchorEl,
  open,
  onClose,
  title,
  description,
  confirmText,
  onConfirm,
  confirmIcon,
  testId,
}: ConfirmationPopoverProps) {
  const { formatMessage } = useIntl();

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      data-testid={testId}
      slotProps={{
        paper: {
          sx: {
            borderRadius: '16px',
          },
        },
      }}
    >
      <Stack maxWidth="300px" p={2}>
        <Typography variant="headline4" sx={{ mb: 2 }}>
          {title}
        </Typography>
        <Typography variant="body3" sx={{ mb: 2 }}>
          {description}
        </Typography>
        <Stack direction="row" spacing={1} justifyContent="space-between">
          <Button
            variant="contained"
            size="small"
            fullWidth
            onClick={onConfirm}
            data-testid={`confirm-${testId}`}
            sx={{
              backgroundColor: grey[800],
              '&:hover': { backgroundColor: grey[900] },
              borderRadius: '16px',
              gap: 1,
            }}
          >
            {confirmIcon && <Box>{confirmIcon}</Box>}
            <Typography color="white">{confirmText}</Typography>
          </Button>
          <Button
            variant="outlined"
            size="small"
            fullWidth
            onClick={onClose}
            data-testid={`cancel-${testId}`}
            sx={{
              border: 0,
              ':hover': {
                backgroundColor: grey[150],
                border: 0,
              },
              borderRadius: '16px',
            }}
          >
            {formatMessage({ id: 'common.label.cancel' })}
          </Button>
        </Stack>
      </Stack>
    </Popover>
  );
}
