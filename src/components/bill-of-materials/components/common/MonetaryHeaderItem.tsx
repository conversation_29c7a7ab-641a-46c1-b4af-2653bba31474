import { MonetaryAmount } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.money.v1';
import { Divider, Stack, Typography } from '@mui/material';
import React from 'react';
import { FormattedMessage, MessageDescriptor } from 'react-intl';

interface MonetaryHeaderItemProps {
  icon: React.ReactElement;
  label: MessageDescriptor['id'];
  monetaryAmount: MonetaryAmount;
  locale: string;
  testId?: string;
  showDivider?: boolean;
  tooltip?: React.ReactElement;
}

export function MonetaryHeaderItem({
  icon,
  label,
  monetaryAmount,
  locale,
  testId = undefined,
  showDivider = true,
  tooltip,
}: MonetaryHeaderItemProps) {
  return (
    <>
      {showDivider && <Divider flexItem orientation="vertical" />}
      <Stack direction="row" gap={2} alignItems="center" data-testid={testId}>
        <Stack direction="row" gap={1} alignItems="center">
          {icon}
          <Typography sx={{ display: 'flex', alignItems: 'center' }} variant="body2">
            <FormattedMessage id={label} />
          </Typography>
        </Stack>
        <Stack direction="row" gap={1} alignItems="center">
          <Typography
            sx={{
              display: 'flex',
              alignItems: 'center',
            }}
            variant="body2Emphasis"
          >
            {(monetaryAmount.minorAmount / 100).toLocaleString(locale, {
              style: 'currency',
              currency: monetaryAmount.currencyCode,
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            })}
          </Typography>
          {tooltip}
        </Stack>
      </Stack>
    </>
  );
}
