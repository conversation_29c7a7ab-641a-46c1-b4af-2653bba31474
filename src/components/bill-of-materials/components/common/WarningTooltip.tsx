import { Stack, Tooltip, TooltipProps } from '@mui/material';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { WarningIcon } from 'components/heat-design/components/WarningIcon';
import { ReactNode } from 'react';

export type WarningTooltipProps = Omit<TooltipProps, 'children'> & {
  children?: ReactNode;
  'data-testid'?: string;
};

export function WarningTooltip({ title, 'data-testid': dataTestId, ...props }: Readonly<WarningTooltipProps>) {
  return (
    <Tooltip title={FormatForTooltip(title)} {...props}>
      <Stack data-testid={dataTestId}>
        <WarningIcon x={2.5} y={2.5} iconWidth={20} iconHeight={20} canvasWidth={20} canvasHeight={20} />
      </Stack>
    </Tooltip>
  );
}
