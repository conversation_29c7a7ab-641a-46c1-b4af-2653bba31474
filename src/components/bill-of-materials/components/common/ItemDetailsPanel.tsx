import { Box, Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { Item } from '../../types';
import { getFormattedItemPricePerUnit } from '../../utils';
import { useNextLocale } from '../../hooks/useNextLocale';

interface ItemDetailsPanelProps {
  item: Item;
  showCostPerUnit?: boolean;
}

/**
 * ItemDetailsPanel component displays the details of an item, including supplier, version, cost per unit,
 * and bundles it is included in.
 *
 * @param {Item} item - The item object containing details to be displayed.
 * @param {boolean} [showCostPerUnit=false] - Flag to show or hide the cost per unit.
 * @returns {JSX.Element} The rendered ItemDetailsPanel component.
 */
function ItemDetailsPanel({ item, showCostPerUnit = false }: Readonly<ItemDetailsPanelProps>) {
  const nextLocale = useNextLocale();

  return (
    <Box sx={{ padding: '8px 16px 16px 16px' }} data-testid="item-details-panel">
      <Stack style={{ width: '100%' }} gap="16px">
        <Stack direction="row" gap="16px">
          {item.supplier && (
            <Stack
              style={{ flex: '1', padding: '8px', borderRadius: '8px', backgroundColor: '#22222608' }}
              maxWidth="400px"
              direction="row"
              justifyContent="space-between"
            >
              <Typography variant="body2" fontWeight="400">
                <FormattedMessage id="billOfMaterials.itemCatalogue.table.supplier" />
              </Typography>
              <Typography variant="body2" fontWeight="500">
                {item.supplier}
              </Typography>
            </Stack>
          )}
          {item.version && (
            <Stack
              style={{ flex: '1', padding: '8px', borderRadius: '8px', backgroundColor: '#22222608' }}
              maxWidth="300px"
              direction="row"
              justifyContent="space-between"
            >
              <Typography variant="body2" fontWeight="400">
                <FormattedMessage id="billOfMaterials.itemCatalogue.table.version" defaultMessage="Version" />
              </Typography>
              <Typography variant="body2" fontWeight="500">
                {item.version}
              </Typography>
            </Stack>
          )}
          {showCostPerUnit && (
            <Stack
              style={{
                flex: '1',
                padding: '8px',
                borderRadius: '8px',
                backgroundColor: '#22222608',
              }}
              maxWidth="300px"
              direction="row"
              justifyContent="space-between"
            >
              <Typography variant="body2" fontWeight="400">
                <FormattedMessage id="billOfMaterials.itemCatalogue.table.costPerUnit" />
              </Typography>
              <Typography variant="body2" fontWeight="500">
                {getFormattedItemPricePerUnit(item, nextLocale)}
              </Typography>
            </Stack>
          )}
        </Stack>
        {item.includedInBundles && item.includedInBundles.length > 0 && (
          <Stack direction="row" gap="16px">
            <Box style={{ padding: '8px', borderRadius: '8px', backgroundColor: '#22222608' }}>
              <Typography variant="body2" fontWeight="400">
                <FormattedMessage id="billOfMaterials.itemCatalogue.table.partOfBundles" />
              </Typography>
            </Box>
            {item.includedInBundles.map((bundle) => (
              <Box key={bundle.id} style={{ padding: '8px', borderRadius: '8px', backgroundColor: '#53535A' }}>
                <Typography variant="body3" fontWeight="500" color="#fff">
                  {bundle.title}
                </Typography>
              </Box>
            ))}
          </Stack>
        )}
      </Stack>
    </Box>
  );
}

export default ItemDetailsPanel;
