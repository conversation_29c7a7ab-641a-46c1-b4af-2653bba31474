import { Box, Divider, FormControl, MenuItem, Paper, Stack, TextField, Typography } from '@mui/material';
import MuiAutocomplete from '@mui/material/Autocomplete';
import { useState } from 'react';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { FormattedMessage, useIntl } from 'react-intl';
import { AutocompleteInputComponent } from '@ui/components/shared/AutocompleteInputComponent';
import { ItemLabel } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { Button } from '@ui/components/Button/Button';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { grey } from '@ui/theme/colors';
import useItemLabels from '../../hooks/useItemLabels';
import { IAutocompleteOption } from '@ui/components/shared/types';
import { useCountry } from '../../../../hooks/useCountry';

export function formatSelectedValueOptionForInput(
  selectedValue: string | null,
): IAutocompleteOption<string | undefined> | null {
  if (!selectedValue) return null;

  return { label: selectedValue, value: selectedValue };
}

function CreateNewLabel({ children, onClick }: { children?: React.ReactNode; onClick?: () => void }) {
  return (
    <Paper>
      {children}
      <Divider style={{ width: '100%' }} />
      <Button
        color="white"
        fullWidth
        sx={{ justifyContent: 'flex-start', ':hover': { backgroundColor: grey[150] } }}
        onMouseDown={(e) => {
          e.preventDefault();
          onClick?.();
        }}
        data-testid="add-new-label-button"
      >
        <AddOutlinedIcon />
        <FormattedMessage id="billOfMaterials.labels.addNew" tagName="span" />
      </Button>
    </Paper>
  );
}

interface LabelSelectProps {
  onUpdate: (label: ItemLabel | string | null) => void;
  disabled?: boolean;
  error?: boolean;
  items: string[];
  placeholder?: string;
  selectedLabel: string | null;
  setSelectedLabel: (label: string | null) => void;
}

export default function LabelSelect({
  onUpdate,
  items,
  disabled = false,
  error = false,
  placeholder,
  selectedLabel,
  setSelectedLabel,
}: LabelSelectProps) {
  const { formatMessage } = useIntl();
  const [isCreatingNewLabel, setIsCreatingNewLabel] = useState(false);
  const [newLabelText, setNewLabelText] = useState('');
  const country = useCountry();
  const { labels, isPending } = useItemLabels({ countries: [country] });
  const handleCreateNewLabel = () => {
    if (!newLabelText.trim()) {
      setIsCreatingNewLabel(false);
      return;
    }
    try {
      onUpdate(newLabelText);
      setSelectedLabel(newLabelText);
      setIsCreatingNewLabel(false);
      setNewLabelText('');
    } catch (error) {
      console.error('Failed to create label:', error);
    }
  };
  // If still creating a new label, show the text input
  if (isCreatingNewLabel) {
    return (
      <Stack direction="row" spacing={1} alignItems="center" onClick={(e) => e.stopPropagation()}>
        <TextField
          size="small"
          data-testid="new-label-input"
          value={newLabelText}
          error={newLabelText.trim().length === 0}
          onChange={(e) => setNewLabelText(e.target.value)}
          placeholder={formatMessage({ id: 'billOfMaterials.labels.newLabel' })}
          autoFocus
          fullWidth
          onBlur={handleCreateNewLabel}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              handleCreateNewLabel();
            } else if (e.key === 'Escape') {
              setIsCreatingNewLabel(false);
            }
          }}
        />
      </Stack>
    );
  }

  return (
    <FormControl onClick={(e) => e.stopPropagation()} fullWidth error={error} disabled={disabled}>
      <MuiAutocomplete
        data-testid="label-select"
        disabled={disabled}
        options={items}
        getOptionLabel={(option) => option}
        loading={isPending}
        value={selectedLabel}
        onChange={(_, newValue) => {
          setSelectedLabel(newValue);
          onUpdate(labels.find((singleLabel) => singleLabel.label === newValue) ?? newValue);
        }}
        popupIcon={
          !disabled && (
            <Box width={24} height={24} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <ChevronDown height={10} width={20} />
            </Box>
          )
        }
        renderInput={(params) => (
          <AutocompleteInputComponent
            selectedValue={formatSelectedValueOptionForInput(selectedLabel)}
            params={{ ...params, size: 'small' }}
            error={error}
            placeholder={placeholder || formatMessage({ id: 'billOfMaterials.labels.selectLabel' })}
          />
        )}
        PaperComponent={(props) => (
          <CreateNewLabel
            {...props}
            onClick={() => {
              setIsCreatingNewLabel(true);
              setNewLabelText('');
            }}
          />
        )}
        renderOption={(props, option) => {
          return (
            <MenuItem data-testid="label-select-option" {...props} key={props.key}>
              <Typography>{option}</Typography>
            </MenuItem>
          );
        }}
      />
    </FormControl>
  );
}
