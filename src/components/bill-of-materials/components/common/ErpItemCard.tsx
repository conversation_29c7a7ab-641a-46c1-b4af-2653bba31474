import { memo, ReactNode, useEffect, useRef, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Box, Stack, SxProps, Theme, Typography } from '@mui/material';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import ItemWarningToolTip from './ItemWarningToolTip';
import { getFormattedItemPricePerUnit } from '../../utils';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import ItemDetailsPanel from './ItemDetailsPanel';
import styled from '@emotion/styled';
import { useRouter } from 'next/router';
import { Item } from '../../types';

const COLLAPSE_DURATION_MS = 200;
const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  transition: `background-color ${COLLAPSE_DURATION_MS}ms, box-shadow ${COLLAPSE_DURATION_MS}ms`,
  padding: 0,
  borderRadius: '16px !important',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: `outline ${COLLAPSE_DURATION_MS}ms`,
  },
  '.details-controls': {
    opacity: 0,
    transition: `opacity ${COLLAPSE_DURATION_MS}ms`,
  },
  p: {
    wordBreak: 'break-word',
  },
  '&:hover': {
    backgroundColor: '#2222260F',

    '.check-wrapper': {
      outline: '1px solid #ccc',
    },
    '.details-controls': {
      opacity: 1,
    },
  },

  '&.disabled': {
    opacity: 0.7,
    pointerEvents: 'none',
  },
  '&.selected': {
    backgroundColor: '#2222261F',

    '&:hover': {
      '.check-wrapper': {
        outline: '1px solid transparent',
      },
    },
  },
  '&.Mui-expanded': {
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

export type ErpItemCardProps = {
  item: Item;
  isSelected: boolean;
  columnWidths: string;
  onRowClick: (item: Item, hasWarning: boolean) => void;
  sx?: SxProps<Theme>;
  isSelectionLimited?: boolean;
  isDisabled?: boolean;
  children?: ReactNode;
  isExpanded?: boolean;
  setIsExpanded?: (value: boolean) => void;
};

function ErpItemCardComponent({
  item,
  isSelected,
  onRowClick,
  columnWidths,
  isSelectionLimited,
  isDisabled,
  sx,
  children,
  isExpanded,
  setIsExpanded,
}: ErpItemCardProps) {
  const hasWarning = item.archivedAt !== undefined || item.validationErrors.length > 0;
  const [isCardExpanded, setIsCardExpanded] = useState(!!isExpanded);
  const { locale } = useRouter();

  const [shouldRenderChildren, setShouldRenderChildren] = useState(!!isExpanded);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setIsCardExpanded(!!isExpanded);
  }, [isExpanded]);

  const onExpandedChange = (value: boolean) => {
    setShouldRenderChildren(true);
    setIsCardExpanded(value);
    setIsExpanded?.(value);
  };

  // We hide the children when the card isn't expanded due to potential performance issues if the content is large
  // The reason we only do it after a timeout in the useEffect is to make the collapsing still look nice -
  // without this the children would immediately disappear instead of them only disappearing after the collapsing is done
  useEffect(() => {
    if (isCardExpanded) {
      // Cancel any pending unmounting timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    } else {
      // Delay unmounting after animation (~200ms)
      timeoutRef.current = setTimeout(() => {
        setShouldRenderChildren(false);
        timeoutRef.current = null;
      }, COLLAPSE_DURATION_MS);
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isCardExpanded]);
  return (
    <StyledAccordion
      sx={sx}
      className={`${isSelected ? 'selected' : ''} ${isDisabled ? 'disabled' : ''}`}
      expanded={isCardExpanded}
      onClick={() => {
        if (!isSelectionLimited) {
          onRowClick(item, hasWarning);
        }
      }}
      data-testid={'erp-item-card-' + item.id.value}
    >
      <AccordionSummary style={{ padding: '0px', cursor: isSelectionLimited ? 'default' : 'pointer' }}>
        <Box
          width="100%"
          style={{
            display: 'grid',
            wordBreak: 'break-all',
            gridTemplateColumns: columnWidths,
            marginRight: '8px',
          }}
        >
          <Box
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '16px 10px',
            }}
          >
            <Box
              className="check-wrapper"
              style={{
                borderRadius: '50%',
                width: '20px',
                height: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                cursor: 'pointer',
              }}
              onClick={() => {
                if (isSelectionLimited) {
                  onRowClick(item, hasWarning);
                }
              }}
            >
              <CheckCircleFilledIcon
                style={{
                  opacity: isSelected ? 1 : 0,
                  transition: 'opacity 0.2s',
                  position: 'absolute',
                  top: '-2px',
                  left: '-2px',
                  width: '24px',
                  height: '24px',
                }}
              />
            </Box>
          </Box>
          <Stack direction="row" gap={1} alignItems="center" padding="16px 8px">
            {hasWarning && <ItemWarningToolTip item={item} />}
            <Typography variant="body2" align="center">
              {item.erpId}
            </Typography>
          </Stack>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{item.category ?? '-'}</Typography>
          </Box>

          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{item.description}</Typography>
          </Box>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{item.quantity ?? '-'}</Typography>
          </Box>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{item.unit ?? '-'}</Typography>
          </Box>

          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{getFormattedItemPricePerUnit(item, locale)}</Typography>
          </Box>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2" data-testid={`item-label-${item.label?.id?.value}`}>
              {item.label?.label}
            </Typography>
          </Box>
          <Stack
            className="details-controls"
            gap="8px"
            sx={{ padding: '16px 8px', cursor: 'pointer' }}
            alignItems="center"
            direction="row"
            data-testid="expand-details-button"
            justifyContent="flex-end"
            onClick={(e) => {
              e.stopPropagation();
              onExpandedChange(!isCardExpanded);
            }}
          >
            <Chevron transitionDuration="0.2s" direction={isCardExpanded ? 'up' : 'down'} height={20} width={20} />
          </Stack>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        {/*Done for performance reasons - this component is used in lists with tons of items so not rendering the content unless expanded is preferable*/}
        {shouldRenderChildren && (
          <Stack>
            <ItemDetailsPanel item={item} />
            {children}
          </Stack>
        )}
      </AccordionDetails>
    </StyledAccordion>
  );
}

export const ErpItemCard = memo(ErpItemCardComponent);
