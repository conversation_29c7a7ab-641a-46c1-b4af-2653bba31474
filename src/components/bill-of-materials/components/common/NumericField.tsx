import { useEffect, useState } from 'react';
import { Stack } from '@mui/material';
import { ITextField, TextField } from '@ui/components/TextField/TextField';

export interface INumericField extends Omit<ITextField, 'onChange' | 'skipNumericalRangeCheck' | 'onBlur'> {
  /**
   * Current value - can be undefined (no value entered) or a number (including 0)
   */
  value: number | undefined;

  /**
   * Callback when value changes
   */
  onChange: (newValue: number | undefined) => void;

  /**
   * Optional callback when field loses focus
   */
  onBlur?: (newValue: number | undefined) => void;

  /**
   * Optional validation function - return true if valid, false if invalid
   */
  validate?: (value: number | undefined) => boolean;
}

/**
 * A numeric field component that handles undefined values and zero properly
 * with support for validation
 */
export function NumericField({ value, onChange, validate, ...props }: INumericField) {
  // Track input as string to allow for empty state
  const [inputValue, setInputValue] = useState<string | undefined>(value?.toString());

  // Determine error state based on validation function and constraints
  const numValue = inputValue ? Number(inputValue) : undefined;
  const isError = validate ? !validate(numValue) : false;

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newInput = e.target.value;
    setInputValue(newInput);

    // If input is empty, treat as undefined
    if (newInput === '') {
      onChange(undefined);
      return;
    }

    // Try to convert to number
    const numValue = Number(newInput);
    if (!isNaN(numValue)) {
      onChange(numValue);
    }
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Apply any special validation or formatting on blur
    const currentValue = e.target.value === '' ? undefined : Number(e.target.value);

    // Call user's onBlur if provided
    if (props.onBlur) {
      props.onBlur(currentValue);
    }
  };

  // Sync input value if external value changes
  useEffect(() => {
    const newInputValue = value === undefined ? '' : value.toString();
    setInputValue((prev) => {
      if (prev !== newInputValue) {
        return newInputValue;
      } else {
        return prev;
      }
    });
  }, [value, setInputValue]);

  return (
    <Stack direction="column" width={props.fullWidth ? '100%' : 'auto'}>
      <TextField
        type="number"
        {...props}
        skipNumericalRangeCheck
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        error={isError || props.error}
      />
    </Stack>
  );
}
