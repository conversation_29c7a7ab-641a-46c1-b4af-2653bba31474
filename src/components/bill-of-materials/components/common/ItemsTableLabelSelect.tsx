import { memo, useState } from 'react';
import LabelSelect from './LabelSelect';
import { Box, Typography } from '@mui/material';
import { Item, TableItem } from '../../types';
import { ItemLabel } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';

export type ItemsTableLabelSelectProps = {
  item: TableItem;
  erpItem?: Item;
  onLabelUpdate?: (label: string | ItemLabel | null) => void;
  labels: string[];
  isDisabled: boolean;
};

function ItemsTableLabelSelectComponent({
  item,
  erpItem,
  onLabelUpdate,
  labels,
  isDisabled,
}: ItemsTableLabelSelectProps) {
  const [selectedLabel, setSelectedLabel] = useState<string | null>(erpItem?.label?.label ?? null);

  const onLabelChange = (label: string | ItemLabel | null) => {
    setSelectedLabel(typeof label === 'string' ? label : (label?.label ?? null));
    if (onLabelUpdate) {
      onLabelUpdate(label);
    }
  };
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
      {item.type === 'erp' ? (
        onLabelUpdate ? (
          <LabelSelect
            selectedLabel={selectedLabel}
            setSelectedLabel={onLabelChange}
            items={labels}
            onUpdate={onLabelChange}
            disabled={isDisabled}
          />
        ) : (
          <Typography variant="body2">{erpItem?.label?.label ?? '-'}</Typography>
        )
      ) : (
        <Typography variant="body2">-</Typography>
      )}
    </Box>
  );
}

export const ItemsTableLabelSelect = memo(ItemsTableLabelSelectComponent);
