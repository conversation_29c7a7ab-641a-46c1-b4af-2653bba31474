import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  CircularProgress,
  IconButton,
  Stack,
  Tooltip,
  Typography,
  MenuItem,
  InputAdornment,
} from '@mui/material';
import { grey } from '@ui/theme/colors';
import { FormattedMessage, useIntl } from 'react-intl';
import { FormatForTooltip, TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import styled from '@emotion/styled';
import { memo, ReactNode, useCallback, useMemo, useState } from 'react';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { ConfirmationPopover } from './ConfirmationPopover';
import { ItemLabel } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { Item, TableItem } from 'components/bill-of-materials/types';
import { getFormattedItemPricePerUnit, getItemMinorPricePerUnit } from 'components/bill-of-materials/utils';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import useItemLabels from 'components/bill-of-materials/hooks/useItemLabels';
import { TextField as AiraTextField } from '@ui/components/TextField/TextField';
import { MarkerOutlinedIcon } from '@ui/components/StandardIcons/MarkerOutlinedIcon';
import { NumericField } from './NumericField';
import ItemWarningToolTip from './ItemWarningToolTip';
import { StickyNoteIcon } from '@ui/components/StandardIcons/StickyNoteIcon';
import { ColumnDefWithSizing, TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { useRouter } from 'next/router';
import { useCountry } from '../../../../hooks/useCountry';
import { ItemsTableLabelSelect } from './ItemsTableLabelSelect';
import {
  BundleItemError,
  ItemError,
  MiscellaneousItemError,
} from 'components/heat-design/bill-of-materials/validation';
import { useErpItems } from 'hooks/useErpItemsWithLabels';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { uniq } from 'lodash';
import { Select } from '@ui/components/Select/Select';

const MemoizedCheckbox = memo(Checkbox, (prevProps, nextProps) => prevProps.checked === nextProps.checked);

const StyledAccordion = styled(Accordion)({
  backgroundColor: '#22222608',
  transition: 'background-color 0.2s, box-shadow 0.2s',
  padding: 0,
  marginLeft: 1,
  marginRight: 1,
  borderRadius: '16px !important',
  width: '100%',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: 'outline 0.2s',
  },
  '.details-controls': {
    opacity: 0,
    transition: 'opacity 0.2s',
  },
  '&:hover': {
    backgroundColor: '#2222260F',

    '.check-wrapper': {
      outline: '1px solid #ccc',
    },
    '.details-controls': {
      opacity: 1,
    },
  },
  '&.selected': {
    backgroundColor: '#2222261F',

    '&:hover': {
      '.check-wrapper': {
        outline: '1px solid transparent',
      },
    },
  },
  '&.Mui-expanded': {
    backgroundColor: '#fff',
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

export type ItemsTableColumn<T> = {
  id: string;
  flex: string;
  header: ReactNode;
  renderCell: (item: T) => ReactNode;
};

type ItemId = string;
type ItemTableErrors = Record<ItemId, ItemError[]>;
type TableItemWithCostPerUnit = TableItem & { costPerUnit: number };

export type ItemsTableProps = {
  items: TableItem[];
  isLoading?: boolean;
  isDisabled?: boolean;
  renderDetailsPanel?: (item: TableItem) => ReactNode;
  extraWarningsForItem?: (item: TableItem, erpItem?: Item) => string[];
  showCostPerUnit?: boolean; // Show cost per unit instead of instructions
  showInstructionsRow?: boolean; // Show instructions under the item name
  onDeleteItem?: (itemId: string, itemType: TableItem['type']) => void;
  onQuantityChange?: (itemId: string, newQuantity: number | undefined, itemType: TableItem['type']) => void;
  onItemLabelUpdate?: (itemId: string, newLabel: ItemLabel | string | null, itemType: TableItem['type']) => void;
  onInstructionsChange?: (itemId: string, newInstructions: string, itemType: TableItem['type']) => void;
  onNameChange?: (itemId: string, newName: string, itemType: TableItem['type']) => void;
  onCustomItemCostChange?: (itemId: string, newCost?: number) => void;
  quantityTooltipMessage?: string;
  tableBackgroundColor?: string;
  errors?: ItemTableErrors;
};

export function ItemsTable({
  items,
  isLoading = false,
  isDisabled = false,
  renderDetailsPanel,
  extraWarningsForItem,
  showCostPerUnit = false,
  showInstructionsRow = false,
  onDeleteItem,
  onQuantityChange,
  onItemLabelUpdate,
  onInstructionsChange,
  onNameChange,
  onCustomItemCostChange,
  errors = {},
  quantityTooltipMessage,
  tableBackgroundColor = 'inherit',
}: Readonly<ItemsTableProps>) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const { formatMessage } = useIntl();
  const { locale } = useRouter();
  const [newLabels, setNewLabels] = useState<string[]>([]);
  const country = useCountry();
  const { labels } = useItemLabels({
    countries: [country],
  });
  const { erpItems } = useErpItems();
  const [selectedLabelNames, setSelectedLabelNames] = useState<Set<string>>(new Set());

  const [deleteAnchorEl, setDeleteAnchorEl] = useState<{ el: HTMLElement | null; item: TableItem | null }>({
    el: null,
    item: null,
  });
  const deletePopoverOpen = Boolean(deleteAnchorEl.el);

  const handleDeleteRequest = (item: TableItem, event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setDeleteAnchorEl({ el: event.currentTarget, item });
  };

  const handleDeleteCancel = () => {
    setDeleteAnchorEl({ el: null, item: null });
  };

  const handleDeleteConfirm = () => {
    if (deleteAnchorEl.item && onDeleteItem) {
      const itemId = deleteAnchorEl.item?.itemId;
      const itemType = deleteAnchorEl.item?.type;
      if (itemId) {
        onDeleteItem(itemId, itemType);
      }
      setDeleteAnchorEl({ el: null, item: null });
    }
  };

  const handleLabelUpdate = (itemId: string, label: string | ItemLabel | null, itemType: TableItem['type']) => {
    if (typeof label === 'string') {
      setNewLabels([...newLabels, label]);
    }
    if (onItemLabelUpdate) {
      onItemLabelUpdate(itemId, label, itemType);
    }
  };

  const toggleItemExpand = (event: React.MouseEvent<HTMLDivElement>, itemId: string) => {
    event.stopPropagation();
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleItemExpand = (event: React.MouseEvent<HTMLDivElement>, itemId: string) => {
    // Only toggle expansion if the click is directly on the AccordionSummary or its direct children
    // and not on any input elements or interactive controls
    const target = event.target as HTMLElement;
    const isFormElement =
      target.tagName === 'INPUT' ||
      target.tagName === 'BUTTON' ||
      target.tagName === 'SELECT' ||
      target.closest('input') ||
      target.closest('select') ||
      target.closest('.MuiAutocomplete-root');

    if (!isFormElement) {
      toggleItemExpand(event, itemId);
    }
  };

  const uniqueLabels = useMemo(() => {
    return uniq([...labels.map((label) => label.label), ...newLabels]);
  }, [labels, newLabels]);

  /**
   * Creates consistent typography style for table headers
   * @param options.text The translation key for the header text
   * @param options.isCentered Whether to center the text (default: left-aligned)
   * @returns Typography component with consistent styling
   */
  const headerTypographyGetter = useCallback(
    (options: { text: string; isCentered?: boolean }) => {
      return (
        <Typography
          fontWeight="500"
          variant="body2"
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: options.isCentered ? 'center' : 'flex-start',
            color: grey[700],
            padding: '8px 0',
          }}
        >
          {formatMessage({ id: options.text as any })}
        </Typography>
      );
    },
    [formatMessage],
  );

  const usedLabels = useMemo<Set<string>>(() => {
    return items.reduce((acc, item) => {
      if (item.type === 'erp') {
        const erpItem = erpItems[item.itemId];
        if (erpItem?.label?.label) {
          const label = uniqueLabels.find((label) => label === erpItem.label?.label);
          if (label) {
            acc.add(label);
          }
        }
      }
      return acc;
    }, new Set<string>());
  }, [items, erpItems, uniqueLabels]);

  // Filter items by selected label(s) and query if any
  const filteredItemsWithCostPerUnit = useMemo<TableItemWithCostPerUnit[]>(() => {
    const filteredItems = selectedLabelNames.size
      ? items.filter((item) => {
          // Only ERP items can have labels
          if (item.type === 'erp') {
            const erpItem = erpItems[item.itemId];
            const labelName = erpItem?.label?.label;
            return labelName && selectedLabelNames.has(labelName);
          }
          return false;
        })
      : items;

    return filteredItems.map((item) => {
      let costPerUnit = 0;
      if (item.type === 'erp') {
        const erpItem = erpItems[item.itemId];
        if (erpItem?.minorPrice && erpItem?.quantity) {
          costPerUnit = getItemMinorPricePerUnit(erpItem) / 100; // Convert from minor units to major units
        }
      } else if (item.type === 'custom' && item.cost !== undefined) {
        costPerUnit = item.cost;
      }
      return {
        ...item,
        costPerUnit,
      } satisfies TableItemWithCostPerUnit;
    });
  }, [items, selectedLabelNames, erpItems]);

  const columns = useMemo<ColumnDefWithSizing<TableItemWithCostPerUnit>[]>(
    () => [
      {
        accessorKey: 'quantity',
        columnWidth: '80px',
        enableSorting: false,
        header: () => (
          <Stack direction="row" sx={{ justifyContent: 'space-between', alignItems: 'center' }}>
            {headerTypographyGetter({ text: 'billOfMaterials.editBundle.itemsTable.amountColumn' })}
            {quantityTooltipMessage && <TooltipAira title={quantityTooltipMessage} />}
          </Stack>
        ),
      },
      {
        accessorKey: 'name',
        columnWidth: 'minmax(400px, 1fr)',
        sortingFn: (rowA, rowB) => {
          const aValue = getNameOfTableItemForSorting(rowA.original, erpItems);
          const bValue = getNameOfTableItemForSorting(rowB.original, erpItems);
          return aValue.localeCompare(bValue);
        },
        header: () => headerTypographyGetter({ text: 'billOfMaterials.editBundle.itemsTable.nameColumn' }),
      },
      showCostPerUnit
        ? {
            accessorKey: 'costPerUnit',
            columnWidth: '100px',
            enableSorting: true,
            header: () => headerTypographyGetter({ text: 'billOfMaterials.itemCatalogue.table.costPerUnit' }),
            sortingFn: (rowA, rowB) => {
              const aValue = rowA.original.costPerUnit;
              const bValue = rowB.original.costPerUnit;
              return aValue - bValue;
            },
          }
        : {
            accessorKey: 'instructions',
            columnWidth: '450px',
            enableSorting: false,
            header: () => headerTypographyGetter({ text: 'billOfMaterials.editBundle.itemsTable.instructionsColumn' }),
          },
      {
        accessorKey: 'erpId',
        columnWidth: '100px',
        enableSorting: false,
        header: () => headerTypographyGetter({ text: 'billOfMaterials.editBundle.itemsTable.erpId' }),
      },
      {
        accessorKey: 'label',
        columnWidth: '200px',
        enableSorting: false,
        header: () => headerTypographyGetter({ text: 'common.label.label' }),
      },
    ],
    [showCostPerUnit, headerTypographyGetter, quantityTooltipMessage, erpItems],
  );

  // Create a renderItemCell function to handle all cell rendering based on accessorKey
  const renderItemCell = (row: TableItem, accessorKey: string) => {
    const erpItem = erpItems[row.itemId];
    if (row.type === 'erp' && !erpItem) {
      return undefined;
    }

    switch (accessorKey) {
      case 'quantity':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            {onQuantityChange ? (
              <NumericField
                sx={{
                  height: '100%',
                  width: '50px',
                  minHeight: '30px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: 0,
                  input: { textAlign: 'center', height: '100%', padding: 0 },
                }}
                onChange={(value: number | undefined) => {
                  row.quantity = value;
                  onQuantityChange(row.itemId, value, row.type);
                }}
                disabled={isDisabled}
                validate={() => !errors[row.itemId]?.includes(BundleItemError.QUANTITY)}
                name="count"
                value={row.quantity}
              />
            ) : (
              <Typography sx={{ width: '50px' }} textAlign="center" variant="body2">
                {row.quantity}
              </Typography>
            )}
          </Box>
        );

      case 'name': {
        const extraWarnings = extraWarningsForItem?.(row, erpItem) ?? [];
        const hasBaseValidationErrors = Boolean(erpItem && (erpItem.validationErrors.length > 0 || erpItem.archivedAt));
        const hasAnyWarningsOrErrors = hasBaseValidationErrors || extraWarnings.length > 0;
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', padding: '8px 8px' }}>
            {row.type === 'custom' ? (
              <Stack direction="row" gap={1} alignItems="center" justifyContent="space-between" width="100%">
                {onNameChange ? (
                  <AiraTextField
                    containerProps={{ sx: { flex: '1' } }}
                    sx={{
                      input: { padding: 1 },
                    }}
                    onChange={(event) => {
                      const newValue = event.target.value;
                      row.name = newValue;
                      onNameChange(row.itemId, newValue, row.type);
                    }}
                    name="name"
                    value={row.name}
                    variant="standard"
                    disabled={isDisabled}
                    error={errors[row.itemId]?.includes(MiscellaneousItemError.NAME)}
                  />
                ) : (
                  <Typography variant="body2">{row.name}</Typography>
                )}
                <TooltipAira
                  title={formatMessage({
                    id: 'heatDesign.billOfMaterials.miscellaneousItemsTable.customItemTooltip',
                  })}
                >
                  <MarkerOutlinedIcon style={{ color: grey[500] }} height={20} width={20} />
                </TooltipAira>
              </Stack>
            ) : (
              <Stack direction="row" gap={1} alignItems="center">
                {hasAnyWarningsOrErrors && erpItem && (
                  <ItemWarningToolTip item={erpItem} extraWarnings={extraWarnings} />
                )}
                <Stack direction="column" gap={1}>
                  <Typography variant="body2">{erpItem?.description}</Typography>
                  {showInstructionsRow && row.instructions && (
                    <Stack direction="row" gap={1} alignItems="center">
                      <StickyNoteIcon width={18} height={18} />
                      <Typography variant="body2Emphasis">
                        <FormattedMessage id="billOfMaterials.editBundle.itemsTable.instructionsColumn" />
                      </Typography>
                      <Typography variant="body2" color={grey[700]}>
                        {row.instructions}
                      </Typography>
                    </Stack>
                  )}
                </Stack>
              </Stack>
            )}
          </Box>
        );
      }
      case 'instructions':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            {onInstructionsChange && row.type === 'erp' ? (
              <AiraTextField
                containerProps={{ sx: { flex: '1' } }}
                sx={{
                  input: { padding: 1 },
                }}
                onChange={(event) => {
                  const newValue = event.target.value;
                  row.instructions = newValue;
                  onInstructionsChange(row.itemId, newValue, row.type);
                }}
                name="instructions"
                value={row.type === 'erp' ? (row.instructions ?? '') : ''}
                variant="standard"
                disabled={isDisabled}
              />
            ) : (
              <Typography variant="body2">{row.type === 'erp' ? (row.instructions ?? '') : ''}</Typography>
            )}
          </Box>
        );

      case 'costPerUnit':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            {row.type === 'custom' && onCustomItemCostChange ? (
              <AiraTextField
                containerProps={{ sx: { flex: '1' } }}
                sx={{
                  input: { padding: 1 },
                }}
                onChange={(event) => {
                  const newValue = parseFloat(event.target.value);
                  // Ensure newValue is a valid number
                  onCustomItemCostChange(row.itemId, isNaN(newValue) ? undefined : newValue);
                }}
                name="costPerUnit"
                value={row.cost}
                variant="standard"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                disabled={isDisabled}
              />
            ) : (
              <Typography variant="body2">
                {row.type === 'erp' ? getFormattedItemPricePerUnit(erpItem, locale) : '-'}
              </Typography>
            )}
          </Box>
        );

      case 'erpId':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
            <Typography variant="body2">{row.type === 'erp' ? erpItem?.erpId : '-'}</Typography>
          </Box>
        );

      case 'label':
        return (
          <ItemsTableLabelSelect
            item={row}
            erpItem={erpItem}
            labels={uniqueLabels}
            onLabelUpdate={
              onItemLabelUpdate === undefined
                ? undefined
                : (newLabel) => {
                    handleLabelUpdate(row.itemId, newLabel, row.type);
                  }
            }
            isDisabled={isDisabled}
          />
        );

      default:
        return null;
    }
  };

  const customRowRenderer = (row: TableItem) => {
    const itemId = row.itemId;
    const expandedView = renderDetailsPanel?.(row);
    const isExpanded = expandedItems.has(itemId);

    return (
      <StyledAccordion
        key={itemId}
        expanded={isExpanded}
        className={isExpanded ? 'Mui-expanded' : ''}
        sx={{ width: '100%' }}
      >
        <AccordionSummary
          style={{
            padding: '0px',
            borderBottom: isExpanded ? `1px solid #2222261F` : 'none',
            width: '100%',
          }}
          onClick={expandedView ? (e) => handleItemExpand(e, itemId) : undefined}
        >
          <Box
            width="100%"
            style={{
              display: 'grid',
              gridTemplateColumns: columnWidths,
              wordBreak: 'break-all',
            }}
          >
            {renderItemCell(row, 'quantity')}
            {renderItemCell(row, 'name')}
            {showCostPerUnit ? renderItemCell(row, 'costPerUnit') : renderItemCell(row, 'instructions')}
            {renderItemCell(row, 'erpId')}
            {renderItemCell(row, 'label')}

            {/* Actions */}
            <Box sx={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Stack
                className="details-controls"
                direction="row"
                sx={{
                  justifyContent: 'flex-end',
                }}
              >
                {onDeleteItem && (
                  <Tooltip title={FormatForTooltip(formatMessage({ id: 'billOfMaterials.deleteItem' }))}>
                    <Box>
                      <IconButton disabled={isDisabled} onClick={(e) => handleDeleteRequest(row, e)}>
                        <BinOutlinedIcon width="20px" height="20px" />
                      </IconButton>
                    </Box>
                  </Tooltip>
                )}
                {expandedView && (
                  <Tooltip title={FormatForTooltip(formatMessage({ id: 'billOfMaterials.itemDetails' }))}>
                    <Box
                      sx={{
                        borderRadius: '50%',
                        '&:hover': { backgroundColor: grey[200] },
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        cursor: 'pointer',
                        height: '35px',
                        width: '35px',
                        transition: 'background-color 0.2s',
                      }}
                      data-testid={`item-details-button-${itemId}`}
                    >
                      <Stack alignItems="center" direction="row">
                        <Chevron
                          transitionDuration="0.2s"
                          direction={isExpanded ? 'up' : 'down'}
                          height={16}
                          width={16}
                        />
                      </Stack>
                    </Box>
                  </Tooltip>
                )}
              </Stack>
            </Box>
          </Box>
        </AccordionSummary>
        {expandedView && <AccordionDetails>{expandedView}</AccordionDetails>}
      </StyledAccordion>
    );
  };

  const actionsWidths = [
    ...(onDeleteItem ? [50] : []), // Delete button width
    ...(renderDetailsPanel ? [50] : []), // Expand/collapse button width
  ].reduce((acc, width) => acc + width, 0);
  const columnWidths = columns.map((col) => `${col.columnWidth ?? '1fr'}`).join(' ') + ` ${actionsWidths}px`;

  return (
    <Box sx={{ backgroundColor: 'inherit' }}>
      {!onItemLabelUpdate && ( // Because we can't access temporary label updates in this component, label filtering does not function correctly when labels can be updated
        <Stack direction="row" alignItems="center" gap={2} flex={1} padding={1}>
          <Select
            sx={{
              '.MuiSelect-select': { padding: '8px' },
              width: '400px',
            }}
            data-testid="item-relations-label-filter"
            multiple
            onChange={(event) => {
              setSelectedLabelNames(
                new Set(Array.isArray(event.target.value) ? event.target.value : event.target.value.split(',')),
              );
            }}
            renderValue={() => (
              <Typography noWrap whiteSpace="nowrap">
                {Array.from(selectedLabelNames).join(', ')}
              </Typography>
            )}
            value={Array.from(selectedLabelNames)}
            label={formatMessage({ id: 'billOfMaterials.labels.selectLabel' })}
            name="Label"
            error={false}
            endAdornment={
              selectedLabelNames.size > 0 && (
                <InputAdornment sx={{ marginRight: '10px' }} position="end">
                  <IconButton
                    onClick={() => {
                      setSelectedLabelNames(new Set());
                    }}
                  >
                    <CloseIcon height={14} width={14} color={grey[900]} />
                  </IconButton>
                </InputAdornment>
              )
            }
          >
            {Array.from(usedLabels).map((label) => (
              <MenuItem key={label} value={label} data-testid={`label-select-option-${label}`}>
                <Stack direction="row">
                  <MemoizedCheckbox
                    labelSx={{
                      '> .MuiBox-root': {
                        width: '20px',
                        height: '20px',
                        marginRight: '4px',
                      },
                    }}
                    label={label}
                    checked={selectedLabelNames.has(label)}
                    onChange={() => {}}
                  />
                </Stack>
              </MenuItem>
            ))}
          </Select>
        </Stack>
      )}
      {isLoading ? (
        <Stack width="100%" height="100px" alignItems="center" justifyContent="center">
          <CircularProgress />
        </Stack>
      ) : (
        <TanstackTable
          data={filteredItemsWithCostPerUnit}
          columns={columns}
          customRowRenderer={customRowRenderer}
          styles={{
            columnWidths,
            bodyColumnWidths: '1fr',
            tableRoot: () => ({
              backgroundColor: tableBackgroundColor,
            }),
            tableContainer: () => ({
              padding: '0 8px',
              maxHeight: '500px',
            }),
            tableBodyRow: () => ({
              paddingBottom: '8px',
            }),
            tableHead: () => ({
              backgroundColor: 'inherit',
            }),
            tableHeadRow: () => ({
              backgroundColor: 'inherit',
            }),
            tableHeadCell: () => ({
              border: 'none',
            }),
          }}
          initialState={{
            sorting: [
              {
                id: 'name',
                desc: false,
              },
            ],
          }}
        />
      )}
      <ConfirmationPopover
        anchorEl={deleteAnchorEl.el}
        open={deletePopoverOpen}
        onClose={handleDeleteCancel}
        title={formatMessage({ id: 'billOfMaterials.confirmDeleteItem.title' })}
        description={formatMessage({ id: 'billOfMaterials.confirmDeleteItem.description' })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={handleDeleteConfirm}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        testId="delete-item-confirmation-popover"
      />
    </Box>
  );
}

function getNameOfTableItemForSorting(original: TableItemWithCostPerUnit, erpItems: Record<string, Item>) {
  const rowType = original.type;
  switch (rowType) {
    case 'custom':
      return original.name?.toLocaleLowerCase() || '';
    case 'erp':
      const erpItem = erpItems[original.itemId];
      return erpItem?.description || '';
    default:
      const unhandled: never = rowType;
      throw new Error(`Unhandled item type: ${unhandled}`);
  }
}
