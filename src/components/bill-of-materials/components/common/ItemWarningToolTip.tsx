import { Stack, Tooltip } from '@mui/material';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { WarningIcon } from 'components/heat-design/components/WarningIcon';
import { IntlShape, useIntl } from 'react-intl';
import { Item } from 'components/bill-of-materials/types';

export function generateWarningTooltipMessage(
  intl: IntlShape,
  validationErrors?: string[],
  archivedAt?: Date,
  extraWarnings?: string[],
) {
  const warningbulletPoints = [
    ...(archivedAt
      ? [
          intl.formatMessage({
            id: 'billOfMaterials.itemCatalogue.table.archived',
          }),
        ]
      : []),
    ...(validationErrors || []),
    ...(extraWarnings || []),
  ];
  return FormatForTooltip(
    <ul>
      {warningbulletPoints.map((warning) => (
        <li key={warning}>{warning}</li>
      ))}
    </ul>,
  );
}

export default function ItemWarningToolTip({
  item,
  extraWarnings,
}: Readonly<{ item: Item; extraWarnings?: string[] }>) {
  const intl = useIntl();
  return (
    <Tooltip
      title={generateWarningTooltipMessage(intl, item.validationErrors, item.archivedAt, extraWarnings)}
      data-testid={`item-warning-tooltip-${item.id.value}`}
    >
      <Stack>
        <WarningIcon x={2.5} y={2.5} iconWidth={20} iconHeight={20} canvasWidth={20} canvasHeight={20} />
      </Stack>
    </Tooltip>
  );
}
