import { Bundle } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { isInstallationKitBundle } from '../utils';

export function useValidateInstallationKitBundle(bundle: Bundle) {
  if (!isInstallationKitBundle(bundle)) {
    return { hasRequiredFields: undefined };
  }

  const itemIds = bundle.details.details.installationKit.itemIds;
  const hasRequiredFields = itemIds && itemIds.length > 0;
  return { hasRequiredFields };
}
