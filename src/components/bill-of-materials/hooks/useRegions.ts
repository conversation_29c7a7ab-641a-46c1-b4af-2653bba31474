import { api } from 'utils/api';
import { useNextLocale } from './useNextLocale';
import { getCountryFromLocale } from 'utils/marketConfigurations';

/**
 * Hook to fetch regions for a country
 * Returns regions along with a special "All Regions" option
 */
export function useRegions() {
  const locale = useNextLocale();
  const country = getCountryFromLocale(locale);

  const { data: regionsResponse, isPending } = api.AiraBackend.listRegionsGrpc.useQuery(
    {
      country,
      excludeNonSchedulable: false,
    },
    {
      staleTime: 1000 * 60 * 60 * 6, // 6 hours
    },
  );

  // Add "All Regions" option with an empty string as value
  const regions = regionsResponse?.regions || [];

  return {
    regions,
    isPending,
  };
}
