import { useMemo } from 'react';
import {
  Bundle,
  BundleCollection,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { UUID } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/contract/common/v1/uuid';
import { useIntl } from 'react-intl';
import { isVanStockBundleCollection, getVanStockDetails } from '../utils';

/**
 * Checks if two arrays of region UUIDs have any overlapping values
 */
function hasOverlappingRegions(regions1: UUID[] = [], regions2: UUID[] = []): boolean {
  if (regions1.length === 0 || regions2.length === 0) return false;

  // Create a Set of region values from regions1 for efficient lookup
  const region1Values = new Set(regions1.map((r) => r.value));

  // Check if any region from regions2 exists in region1Values
  return regions2.some((r) => region1Values.has(r.value));
}

/**
 * Custom hook to validate that a van stock bundle's stockType and regions combination doesn't
 * overlap with any other bundle in the collection (same stock type with overlapping regions)
 */
export function useValidateVanStockBundle(bundleCollection: BundleCollection, currentBundle: Bundle) {
  const intl = useIntl();
  return useMemo(() => {
    // Skip if not van stock collection
    if (!isVanStockBundleCollection(bundleCollection)) {
      return { isVanStockUnique: undefined };
    }

    // Skip if no details in the current bundle
    const currentVanStock = getVanStockDetails(currentBundle);
    if (!currentVanStock) {
      return { isVanStockUnique: undefined };
    }

    // Skip if no regions or stock type is unspecified
    if (currentVanStock.regions.length === 0 || currentVanStock.stockType === VanStockType.VAN_STOCK_TYPE_UNSPECIFIED) {
      return { isVanStockUnique: undefined };
    }

    // Check for bundles with the same stock type and any overlapping regions
    const conflictingBundle = bundleCollection.bundles.find((bundle) => {
      // Skip the current bundle being edited
      if (bundle.id?.value === currentBundle.id?.value) {
        return false;
      }

      // Check if bundle has van stock details
      const vanStock = getVanStockDetails(bundle);
      if (!vanStock) {
        return false;
      }

      const bundleStockType = vanStock.stockType;
      const bundleRegions = vanStock.regions || [];

      // Return true if the stock types match and there's any region overlap
      return (
        bundleStockType === currentVanStock.stockType && hasOverlappingRegions(bundleRegions, currentVanStock.regions)
      );
    });

    return {
      isVanStockUnique: !conflictingBundle,
      errorMessage: conflictingBundle
        ? intl.formatMessage(
            { id: 'billOfMaterials.vanStock.duplicateError' },
            { duplicateBundleName: conflictingBundle.title },
          )
        : '',
    };
  }, [bundleCollection, currentBundle, intl]);
}
