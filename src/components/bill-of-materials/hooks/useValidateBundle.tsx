import { useMemo } from 'react';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { isInstallationKitBundle, isVanStockBundleCollection } from '../utils';
import { useValidateVanStockBundle } from './useValidateVanStockBundle';
import isNil from 'lodash/isNil';
import isEmpty from 'lodash/isEmpty';
import { useValidateInstallationKitBundle } from './useValidateInstallationKitBundle';
import { BundleItemError } from 'components/heat-design/bill-of-materials/validation';

interface ValidationResult {
  isValid: boolean;
  validationErrors: {
    hasTitleAndDescription: boolean;
    isVanStockUnique?: boolean;
    hasRequiredInstallationKitFields?: boolean;
    items: Record<string, BundleItemError[]>;
  };
}

/**
 * A hook to validate bundle data before saving
 * Returns whether the bundle is valid and specific validation results
 */
export function useValidateBundle(bundle: Bundle, bundleCollection: BundleCollection): ValidationResult {
  // Use existing van stock validation if relevant
  const { isVanStockUnique } = useValidateVanStockBundle(bundleCollection, bundle);

  // Use existing installation kit validation if relevant
  const { hasRequiredFields: hasRequiredInstallationKitFields } = useValidateInstallationKitBundle(bundle);

  // Validate the bundle data
  const validationResult = useMemo(() => {
    // Check if bundle has title and description
    const hasTitleAndDescription = Boolean(bundle.title && bundle.description);

    // Check if all bundle items have valid quantities
    const itemErrors = bundle.items.reduce(
      (acc, item) => {
        const itemId = item.itemId?.value;
        if (itemId && !isNil(item.defaultQuantity) && item.defaultQuantity <= 0) {
          acc[itemId] = [BundleItemError.QUANTITY];
        }
        return acc;
      },
      {} as Record<string, BundleItemError[]>,
    );

    // Check bundle validity based on its type
    const isVanStockValid = isVanStockBundleCollection(bundleCollection) ? isVanStockUnique : true;
    const isInstallationKitValid = isInstallationKitBundle(bundle) ? hasRequiredInstallationKitFields : true;

    // Bundle is valid if all validation checks pass
    const isValid = !!(hasTitleAndDescription && isVanStockValid && isInstallationKitValid && isEmpty(itemErrors));

    return {
      isValid,
      validationErrors: {
        hasTitleAndDescription,
        ...(isVanStockBundleCollection(bundleCollection) && { isVanStockUnique }),
        ...(isInstallationKitBundle(bundle) && { hasRequiredInstallationKitFields }),
        items: itemErrors,
      },
    };
  }, [bundle, bundleCollection, isVanStockUnique, hasRequiredInstallationKitFields]);

  return validationResult;
}
