import { api } from 'utils/api';
import { Country } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';

export type UseItemLabelProps = {
  countries: Country[];
};
export default function useItemLabels(props: UseItemLabelProps) {
  const labelsQuery = api.BillOfMaterials.getItemLabels.useQuery({
    countries: props.countries,
  });
  const createLabelMutation = api.BillOfMaterials.createItemLabel.useMutation();
  const setLabelForItemsMutation = api.BillOfMaterials.setLabelForItems.useMutation();
  const apiUtils = api.useUtils();

  return {
    isPending: labelsQuery.isPending,
    labels: labelsQuery.data?.itemLabels ?? [],
    createLabel: async (labelText: string) => {
      if (!labelText.trim()) {
        return;
      }

      const existingLabel = labelsQuery.data?.itemLabels.find((label) => label.label === labelText.trim());
      if (existingLabel) {
        return existingLabel.id?.value;
      }

      const newLabel = await createLabelMutation.mutateAsync({ label: labelText });
      apiUtils.BillOfMaterials.getItemLabels.invalidate();
      return newLabel.labelId!.value;
    },
    setLabelForItems: async (itemIds: string[], labelId?: string) => {
      if (!itemIds.length) {
        return;
      }

      await setLabelForItemsMutation.mutateAsync({
        itemIds,
        labelId,
      });
      apiUtils.BillOfMaterials.getItemLabels.invalidate();
    },
  };
}
