import {
  Bundle,
  BundleCollection,
  BundleItem,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { Country } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';
import { resetRouterMock } from '@mocks/next/router';
import { cleanup, fireEvent, screen } from '@testing-library/react';
import { EquivalenceGroup, ProcurementItem } from 'components/heat-design/procurement/types';
import { RequestHandler } from 'msw';
import { setupWorker } from 'msw/browser';
import { beforeEach, vi } from 'vitest';
import { mockGetServerEnvironment, mocks } from '../../tests/utils/mockedTrpcCalls';
import { trpcMsw } from '../../tests/utils/testUtils';
import { MockedBackendState } from './mocked-backend-state';
import { Item } from './types';

export const BUNDLE1_ID = 'bundle-1';
export const BUNDLE2_ID = 'bundle-2';
export const AIK_BUNDLE_ID = 'bundle-aik';
export const VAN_STOCK_1_BUNDLE_COLLECTION_ID = 'vanstock-collection-1';
export const VAN_STOCK_1_BUNDLE_ID = 'vanstock-bundle-1';
export const AIK1_ID = 'aik-1';
export const TEMPLATE1_ID = 'template-1';
export const TEMPLATE2_ID = 'template-2';
export const ERP_ID1 = '475f5a0b-fc2a-4a45-930e-755c4781c851';
export const ERP_ID2 = '4abe96cf-5ce5-47fd-b0f7-6bd8f9ed29b2';
export const ERP_ID3 = '7a97dbaf-54a9-497b-90ec-3c0cb23c051b';
export const ERP_ID4 = '19eb094e-e2b7-4a6d-bf38-939c8ad923f2';
export const AIK_ERP_ID = 'd442a9c9-e53d-4a06-831a-827020e6e632';
export const VAN_STOCK_ERP_ID = 'ae64e36b-8e5d-477b-a2d7-c36e034ffcdc';
export const REMOVED_ERP_ITEM_ID = '2fca6c37-b444-4ac4-aff1-f1463f9f4a6c';
export const DUPLICATE_ERP_ID = '4bbb16c5-f7e9-47b8-8063-9cfada8b5cd4';
export const EXTRA_ERP_ID = '06fc73ad-8b1d-44f6-b3a2-91169e09d537';
export const EXTRA_ERP_ITEM = {
  id: { value: EXTRA_ERP_ID },
  erpId: 'ERPExtra',
  country: Country.COUNTRY_DE,
  description: 'Extra Erp Item 1',
  currency: 'EUR',
  category: 'Category Extra',
  supplier: 'Supplier Extra',
  minorPrice: 300,
  quantity: 30,
  unit: 'pcs',
  equivalenceGroupId: undefined,
  validationErrors: [],
  updatedAt: new Date(),
  includedInBundles: [],
};
export const LABEL_ID1 = '44b08739-ecb0-42e3-bb57-0a9918615e8e';
export const LABEL_ID2 = 'e36fd9c3-c676-48bc-97b3-1d42ec87df13';
export const EQUIVALENCE_GROUP_ID = { value: '123e4567-e89b-12d3-a456-426614174000' };
export const REGION_ID = '832f1d71-44ea-4b63-86d3-07c1e1380947';

export function createTestErpItems(): Item[] {
  return [
    {
      id: { value: ERP_ID1 },
      erpId: 'ERP001',
      country: Country.COUNTRY_DE,
      description: 'Test Erp Item 1',
      currency: 'EUR',
      category: 'Category1',
      supplier: 'Supplier1',
      minorPrice: 100,
      quantity: 10,
      unit: 'pcs',
      equivalenceGroupId: EQUIVALENCE_GROUP_ID,
      labelId: { value: LABEL_ID1 },
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: ERP_ID2 },
      erpId: 'ERP002',
      country: Country.COUNTRY_DE,
      description: 'Test Erp Item 2',
      currency: 'EUR',
      category: 'Category2',
      supplier: 'Supplier2',
      minorPrice: 200,
      quantity: 20,
      unit: 'pcs',
      equivalenceGroupId: EQUIVALENCE_GROUP_ID,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: ERP_ID3 },
      erpId: 'ERP003',
      country: Country.COUNTRY_DE,
      description: 'Test Erp Item 3',
      currency: 'EUR',
      category: 'Category3',
      supplier: 'Supplier3',
      minorPrice: 300,
      quantity: 30,
      unit: 'pcs',
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: ERP_ID4 },
      erpId: 'ERP004',
      country: Country.COUNTRY_DE,
      description: 'Test Erp Item 4 - no quantity',
      currency: 'EUR',
      category: 'Category4',
      supplier: 'Supplier4',
      minorPrice: 450,
      quantity: 0,
      unit: 'pcs',
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: DUPLICATE_ERP_ID },
      erpId: 'ERP001',
      country: Country.COUNTRY_DE,
      description: 'Test Erp Item 5',
      currency: 'EUR',
      category: 'Category5',
      supplier: 'Supplier5',
      minorPrice: 300,
      quantity: 30,
      unit: 'pcs',
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    EXTRA_ERP_ITEM,
    {
      id: { value: REMOVED_ERP_ITEM_ID },
      erpId: 'ER404',
      country: Country.COUNTRY_DE,
      description: 'Removed ERP Item',
      currency: 'EUR',
      category: 'Category404',
      supplier: 'Supplier404',
      minorPrice: 300,
      quantity: 30,
      unit: 'pcs',
      archivedAt: new Date(),
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: AIK_ERP_ID },
      erpId: 'ERPAik',
      country: Country.COUNTRY_DE,
      description: 'AIK Erp Item',
      currency: 'EUR',
      category: 'Category4',
      supplier: 'Supplier4',
      minorPrice: 300,
      quantity: 30,
      unit: 'pcs',
      archivedAt: new Date(),
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
    {
      id: { value: VAN_STOCK_ERP_ID },
      erpId: 'ERPVanStock',
      country: Country.COUNTRY_DE,
      description: 'Van Stock Erp Item',
      currency: 'EUR',
      category: 'Category4',
      supplier: 'Supplier4',
      minorPrice: 300,
      quantity: 30,
      unit: 'pcs',
      archivedAt: new Date(),
      equivalenceGroupId: undefined,
      validationErrors: [],
      updatedAt: new Date(),
      includedInBundles: [],
    },
  ];
}

export const testBundleItems1: BundleItem[] = [
  {
    bundleId: { value: BUNDLE1_ID },
    itemId: { value: ERP_ID1 },
    defaultQuantity: 1,
    instructions: 'Test instructions 1',
    editable: true,
    createdAt: new Date(),
  },
  {
    bundleId: { value: BUNDLE1_ID },
    itemId: { value: ERP_ID2 },
    defaultQuantity: 2,
    instructions: 'Test instructions 2',
    editable: true,
    createdAt: new Date(),
  },
];

export const aikBundleItems: BundleItem[] = [
  {
    bundleId: { value: AIK_BUNDLE_ID },
    itemId: { value: AIK_ERP_ID },
    defaultQuantity: 1,
    instructions: 'Test instructions 1',
    editable: true,
    createdAt: new Date(),
  },
];

export const vanStockItems: BundleItem[] = [
  {
    bundleId: { value: VAN_STOCK_1_BUNDLE_ID },
    itemId: { value: VAN_STOCK_ERP_ID },
    defaultQuantity: 1,
    instructions: 'Van Stock item',
    editable: true,
    createdAt: new Date(),
  },
  {
    bundleId: { value: VAN_STOCK_1_BUNDLE_ID },
    itemId: { value: ERP_ID1 },
    defaultQuantity: 1,
    instructions: 'Van Stock ERP item',
    editable: true,
    createdAt: new Date(),
  },
];

export const testBundleItems2: BundleItem[] = [
  {
    bundleId: { value: BUNDLE2_ID },
    itemId: { value: ERP_ID3 },
    defaultQuantity: 3,
    instructions: 'Test instructions 3',
    editable: true,
    createdAt: new Date(),
  },
];

export function createTestBundleCollections(): BundleCollection[] {
  return [
    // Static collection (van stock)
    {
      id: { value: VAN_STOCK_1_BUNDLE_COLLECTION_ID },
      title: 'Van Stock',
      description: 'Van Stock Collection',
      bundles: [
        {
          id: { value: VAN_STOCK_1_BUNDLE_ID },
          title: 'Basic Tools',
          description: 'Basic tools for installation',
          items: vanStockItems,
          details: {
            details: {
              $case: 'vanStock',
              vanStock: {
                stockType: VanStockType.VAN_STOCK_TYPE_ELECTRICAL,
                regions: [
                  {
                    value: REGION_ID,
                  },
                ],
              },
            },
          },
          bundleCollectionId: { value: VAN_STOCK_1_BUNDLE_COLLECTION_ID },
          createdAt: new Date(),
          updatedAt: new Date(),
        } as Bundle,
      ],
      mandatory: true,
      details: {
        details: {
          $case: 'vanStock',
          vanStock: {},
        },
      },
      country: Country.COUNTRY_DE,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: { value: AIK1_ID },
      title: 'AIK',
      description: 'AIK collection',
      bundles: [
        {
          id: { value: AIK_BUNDLE_ID },
          title: 'Basic Tools',
          description: 'Basic tools for installation',
          items: aikBundleItems,
          bundleCollectionId: { value: AIK1_ID },
          details: {
            details: {
              $case: 'installationKit',
              installationKit: {
                itemIds: [],
              },
            },
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: { value: BUNDLE2_ID },
          title: 'Installation materials',
          description: 'Basic materials for installation',
          items: testBundleItems2,
          bundleCollectionId: { value: AIK1_ID },
          details: {
            details: {
              $case: 'installationKit',
              installationKit: {
                itemIds: [],
              },
            },
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      mandatory: true,
      details: {
        details: {
          $case: 'installationKit',
          installationKit: {},
        },
      },
      country: Country.COUNTRY_DE,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    // Template collections
    {
      id: { value: TEMPLATE1_ID },
      title: 'Template Collection 1',
      description: 'First template collection',
      bundles: [],
      mandatory: false,
      details: {
        details: {
          $case: 'template',
          template: {
            displayOrder: 1,
          },
        },
      },
      country: Country.COUNTRY_DE,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: { value: TEMPLATE2_ID },
      title: 'Template Collection 2',
      description: 'Second template collection',
      bundles: [],
      mandatory: false,
      details: {
        details: {
          $case: 'template',
          template: {
            displayOrder: 2,
          },
        },
      },
      country: Country.COUNTRY_DE,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];
}

export const testErpItems = {
  items: createTestErpItems(),
};
export const testBundleCollections = {
  bundleCollections: createTestBundleCollections(),
};

// Matching the useErpItems() hook output
export const testErpItemsAsRecord = convertToItemsRecord(createTestErpItems());

export function convertToEquivalenceGroupsRecord(equivalenceGroups: EquivalenceGroup[]) {
  return equivalenceGroups.reduce((acc: Record<string, EquivalenceGroup>, equivalenceGroup: EquivalenceGroup) => {
    acc[equivalenceGroup.id] = equivalenceGroup;
    return acc;
  }, {});
}

export function convertToItemsRecord(items: Item[]) {
  return items.reduce((acc: Record<string, Item>, item: Item) => {
    acc[item?.id?.value] = item;
    return acc;
  }, {});
}

export function convertToProcurementItemsRecord(procurementItems: ProcurementItem[]) {
  return procurementItems.reduce((acc: Record<string, ProcurementItem>, item: ProcurementItem) => {
    if (item.itemId) {
      acc[item?.itemId] = item;
    }
    return acc;
  }, {});
}

export const itemLabelData = {
  itemLabels: [
    {
      id: { value: LABEL_ID1 },
      label: 'Label 1',
    },
    {
      id: { value: LABEL_ID2 },
      label: 'Label 2',
    },
  ],
};

export function billOfMaterialsTestSetup(additionalHandlers: RequestHandler[] = []) {
  // Setup MSW server

  const backendState = {
    current: new MockedBackendState(),
  };

  const worker = setupWorker(
    mockGetServerEnvironment(),
    mocks.listRegionsRpc.empty,
    trpcMsw.BillOfMaterials.getBundleCollections.query(() => testBundleCollections),
    trpcMsw.BillOfMaterials.getItems.query(() => {
      return backendState.current.getItems();
    }),
    trpcMsw.BillOfMaterials.getItemLabels.query(() => {
      return itemLabelData;
    }),
    trpcMsw.BillOfMaterials.createItemLabel.mutation((_) => ({
      labelId: { value: 'd0ae5198-783d-4076-b58d-6d8673651727' },
    })),
    trpcMsw.BillOfMaterials.setLabelForItems.mutation((input) => {
      return backendState.current.setLabelForItems(input);
    }),
    trpcMsw.BillOfMaterials.updateBundle.mutation((req) => ({
      bundle: {
        ...req.input,
        id: { value: req.input.bundleId },
        bundleCollectionId: { value: req.input.bundleCollectionId },
        items: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })),
    trpcMsw.BillOfMaterials.updateBundleCollection.mutation((req) => ({
      bundleCollection: {
        ...req.input.bundleCollection,
        bundles: [],
        details: req.input.bundleCollection.details || {},
        id: { value: req.input.bundleCollectionId },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })),
    trpcMsw.BillOfMaterials.updateBundleItem.mutation((req) => ({
      bundleItem: {
        ...req.input,
        itemId: { value: req.input.itemId },
        bundleId: { value: req.input.bundleId },
        createdAt: new Date(),
      },
    })),
    trpcMsw.BillOfMaterials.createBundle.mutation((_) => ({
      bundleId: { value: 'new-bundle-id' },
    })),
    trpcMsw.BillOfMaterials.addBundleItems.mutation((req) => ({
      bundleItems: req.input.map((item) => ({
        ...item,
        itemId: { value: item.itemId },
        bundleId: { value: item.bundleId },
        createdAt: new Date(),
      })),
    })),
    trpcMsw.BillOfMaterials.createItemEquivalenceGroup.mutation((input) => {
      return backendState.current.createItemEquivalenceGroup(input);
    }),
    trpcMsw.BillOfMaterials.removeItemsFromEquivalenceGroup.mutation((input) => {
      return backendState.current.removeItemsFromEquivalenceGroup(input);
    }),
    trpcMsw.BillOfMaterials.addItemsToEquivalenceGroup.mutation((input) => {
      return backendState.current.addItemsToEquivalenceGroup(input);
    }),
    trpcMsw.BillOfMaterials.removeBundle.mutation(async () => ({})),
    trpcMsw.BillOfMaterials.removeBundleItems.mutation(async () => ({})),
    trpcMsw.BillOfMaterials.removeBundleCollection.mutation(async () => ({})),
    trpcMsw.BillOfMaterials.createBundleCollection.mutation(() => ({
      bundleCollectionId: { value: 'new-collection-id' },
    })),
    trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => ({ radiators: [] })),
    ...additionalHandlers,
  );

  beforeAll(async () => {
    await worker.start({
      onUnhandledRequest: (req, print) => {
        if (req.url.includes('/fonts/')) {
          return;
        }
        print.warning();
      },
      quiet: true,
    });
  });
  beforeEach(() => {
    backendState.current = new MockedBackendState();
  });
  afterEach(() => {
    worker.resetHandlers();
    cleanup();
    vi.clearAllMocks();
    resetRouterMock();
  });
  afterAll(() => worker.stop());

  return backendState;
}

export async function fillAdvancedSearch(query: string) {
  fireEvent.click(await screen.findByTestId('advanced-search-label'));
  const textArea = (await screen.findByTestId('item-relations-advanced-search-input')).querySelector('textarea');
  fireEvent.change(textArea!, {
    target: { value: query },
  });
  fireEvent.click(await screen.findByTestId('advanced-search-accept-button'));
}
