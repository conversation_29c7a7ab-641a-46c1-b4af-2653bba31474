import { create } from 'zustand';
import { Item } from './types';

export type BillOfMaterialsModals = {
  ITEM_CATALOGUE: {
    preSelectedItemIds?: Set<string>;
    disabledItemIds?: Set<string>;
    onClose: (selectedItems: Item[]) => void;
    title?: string;
  };
};

export type BillOfMaterialsModal = keyof BillOfMaterialsModals;

// Create a union type for an open modal item that ties a modal identifier to its data type.
type OpenModal = {
  [K in BillOfMaterialsModal]: {
    modal: K;
    data: BillOfMaterialsModals[K];
  };
}[BillOfMaterialsModal];

export type BillOfMaterialsStore = {
  // The generic here ties the modal key to the correct data type.
  openModal: <T extends BillOfMaterialsModal>(modal: T, data: BillOfMaterialsModals[T]) => void;
  closeModal: (modal: BillOfMaterialsModal) => void;
  openModals: OpenModal[];
};

export const useBillOfMaterialsStore = create<BillOfMaterialsStore>((set) => ({
  openModals: [],
  erpItems: {},
  areErpItemsPending: false,
  openModal: <T extends BillOfMaterialsModal>(modal: T, data: BillOfMaterialsModals[T]) =>
    set((state) => ({
      openModals: [...state.openModals, { modal, data }],
    })),
  closeModal: (modal: BillOfMaterialsModal) =>
    set((state) => ({
      openModals: state.openModals.filter((m) => m.modal !== modal),
    })),
}));
