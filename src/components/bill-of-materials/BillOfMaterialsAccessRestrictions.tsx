import { ReactNode } from 'react';
import { api } from 'utils/api';
import AccessDenied from '../access/AccessDenied';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { canAccessBillOfMaterials, getRolesForUser } from 'server/api/helpers/roles';
import {
  Country,
  countryFromJSON,
} from '@aira/identity-grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';
import { Session } from 'server/auth';

function getUserIdentityCountry(user: Session['user']): Country | undefined {
  const country = user.country;
  if (country) {
    const countryString = Object.entries(Country).find(([_, value]) => value === country)?.[0];
    return countryString ? countryFromJSON(countryString) : undefined;
  } else {
    return undefined;
  }
}

type Props = {
  children: ReactNode;
};

export default function BillOfMaterialsAccessRestrictions({ children }: Props) {
  const { data: user, isPending } = api.AiraBackend.whoAmI.useQuery();
  if (isPending) {
    return <HeatPumpLoader />;
  }
  if (!user) {
    return <AccessDenied />;
  }
  const roles = getRolesForUser(user.email);
  const country = getUserIdentityCountry(user);
  if (canAccessBillOfMaterials(roles, country)) {
    return children;
  } else return <AccessDenied />;
}
