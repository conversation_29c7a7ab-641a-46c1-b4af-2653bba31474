import { Country } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';
import { Item } from './types';
import { findGroupsOfItemsWithSameErpId } from './utils';

function makeItem({ erpId, id, description }: { erpId: string; id: string; description: string }): Item {
  return {
    country: Country.COUNTRY_GB,
    currency: '￡',
    description,
    erpId,
    id: { value: id },
    updatedAt: undefined,
    validationErrors: [],
    includedInBundles: [],
  };
}

const itemA1: Item = makeItem({
  erpId: 'ERP-1',
  id: 'bb1c36c3-8d4b-43a8-9222-2d50949a7c78',
  description: 'Some pipe',
});
const itemA2: Item = makeItem({
  erpId: 'ERP-1',
  id: '12cb7005-3132-4494-a83b-0fa41e3c27b8',
  description: 'Some other, similar pipe',
});
const itemB1: Item = makeItem({
  erpId: 'ERP-2',
  id: '3c4b7005-3132-4494-a83b-0fa41e3c27b8',
  description: 'A completely different item',
});
const itemC1: Item = makeItem({
  erpId: 'ERP-3',
  id: '77be4701-6baf-4ef4-993c-42f0082df5dc',
  description: 'Some radiator',
});
const itemC2: Item = makeItem({
  erpId: 'ERP-3',
  id: '1823c3ec-6e52-4582-acbf-23143f7ab69b',
  description: 'Some other, similar radiator',
});

describe('Finding duplicate items', () => {
  it('groups those with duplicates and discards the rest', () => {
    const groups = findGroupsOfItemsWithSameErpId([itemA1, itemA2, itemB1]);
    expect(groups).toEqual([
      {
        erpId: 'ERP-1',
        items: [itemA1, itemA2],
      },
    ]);
  });
  it('finds multiple groups of duplicates', () => {
    const groups = findGroupsOfItemsWithSameErpId([itemA1, itemA2, itemB1, itemC1, itemC2]);
    expect(groups).toEqual([
      {
        erpId: 'ERP-1',
        items: [itemA1, itemA2],
      },
      {
        erpId: 'ERP-3',
        items: [itemC1, itemC2],
      },
    ]);
  });
});
