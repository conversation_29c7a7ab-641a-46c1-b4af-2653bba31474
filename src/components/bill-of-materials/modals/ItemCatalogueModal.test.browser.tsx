import ItemCatalogueModal from './ItemCatalogueModal';
import { IntlProvider } from 'react-intl';
import { renderWithProviders } from 'tests/utils/testUtils';
import { billOfMaterialsTestSetup, ERP_ID1, DUPLICATE_ERP_ID } from '../test-utils';
import { messages } from '../../../../public/locales/en-gb/messages';
import { Mock } from 'vitest';
import { fireEvent, within } from '@testing-library/react';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { heatDesignTheme } from 'components/heat-design/HeatDesignTheme';
import { Item } from '../types';
import { fail } from 'assert';

billOfMaterialsTestSetup();

function renderItemCatalogueModal(onClose: Mock<(...args: any[]) => any>) {
  return renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en" messages={messages} onError={() => {}}>
      <AiraThemeProvider theme={heatDesignTheme}>
        <ItemCatalogueModal data={{ preSelectedItemIds: new Set(), onClose }} />
      </AiraThemeProvider>
    </IntlProvider>,
  );
}

describe('ItemCatalogueModal', () => {
  it('should allow choosing an item', async () => {
    const onCloseMock = vi.fn();
    const screen = renderItemCatalogueModal(onCloseMock);

    const container = screen.getByTestId('item-catalogue-modal-container');
    expect(container).toBeVisible();

    // Select an item in the catalogue
    fireEvent.click(await screen.findByTestId(`erp-item-card-${ERP_ID1}`));

    // Click the select button to confirm the selection
    const selectButton = screen.getByRole('button', { name: 'Select' });
    fireEvent.click(selectButton);

    // Now the modal should close, with the selected item passed to onClose
    expect(onCloseMock).toHaveBeenCalledWith([expect.objectContaining({ erpId: 'ERP001' })]);
  });

  it('should show warning when items of same ERP ID are selected, allow confirming', async () => {
    const onCloseMock: Mock<(...args: [Item[]]) => any> = vi.fn();
    const screen = renderItemCatalogueModal(onCloseMock);

    const container = screen.getByTestId('item-catalogue-modal-container');
    expect(container).toBeVisible();

    // Select two items with the same ERP ID
    fireEvent.click(await screen.findByTestId(`erp-item-card-${ERP_ID1}`));
    fireEvent.click(await screen.findByTestId(`erp-item-card-${DUPLICATE_ERP_ID}`));

    // Click the select button to confirm the selection
    const selectButton = screen.getByRole('button', { name: 'Select' });
    fireEvent.click(selectButton);

    // Now the warning modal should appear
    expect(screen.getByTestId('duplicate-erp-groups-modal')).toBeVisible();

    // We confirm the selection of duplicate items
    fireEvent.click(screen.getByTestId('duplicate-erp-groups-modal-confirm-button'));

    const [items] = expectSingleCallToMock(onCloseMock);
    expect(items).toHaveLength(2);
    expect(items.some((item) => item.id.value === ERP_ID1)).toBe(true);
    expect(items.some((item) => item.id.value === DUPLICATE_ERP_ID)).toBe(true);
  });

  it('should show warning when items of same ERP ID are selected, allow deselecting', async () => {
    const onCloseMock: Mock<(...args: [Item[]]) => any> = vi.fn();
    const screen = renderItemCatalogueModal(onCloseMock);

    const container = screen.getByTestId('item-catalogue-modal-container');
    expect(container).toBeVisible();

    // Select two items with the same ERP ID
    fireEvent.click(await screen.findByTestId(`erp-item-card-${ERP_ID1}`));
    fireEvent.click(await screen.findByTestId(`erp-item-card-${DUPLICATE_ERP_ID}`));

    // Click the select button to confirm the selection
    const selectButton = screen.getByRole('button', { name: 'Select' });
    fireEvent.click(selectButton);

    // Now the warning modal should appear
    const duplicatesModal = screen.getByTestId('duplicate-erp-groups-modal');
    expect(duplicatesModal).toBeVisible();

    // We deselect one these duplicate items and confirm
    fireEvent.click(within(duplicatesModal).getByTestId(`erp-item-card-${DUPLICATE_ERP_ID}`));
    fireEvent.click(screen.getByTestId('duplicate-erp-groups-modal-confirm-button'));

    const [items] = expectSingleCallToMock(onCloseMock);
    expect(items).toHaveLength(1);
    expect(items.some((item) => item.id.value === ERP_ID1)).toBe(true);
  });
});

function expectSingleCallToMock<T>(mock: Mock<(...args: [T]) => any>): [T] {
  const calls = mock.mock.calls;
  expect(calls).toHaveLength(1);
  const call = calls[0];
  if (call === undefined) {
    fail();
  }
  return call;
}
