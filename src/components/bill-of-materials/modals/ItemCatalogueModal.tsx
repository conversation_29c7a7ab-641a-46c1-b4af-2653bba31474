import { memo, useCallback, useMemo, useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { MagnifyingGlassOutlinedIcon } from '@ui/components/StandardIcons/MagnifyingGlassOutlinedIcon';
import { Modal } from '@ui/components/Modal/Modal';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { ColumnDef } from '@tanstack/react-table';
import { beige, grey } from '@ui/theme/colors';
import { Box, IconButton, InputAdornment, MenuItem, Stack, Typography } from '@mui/material';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import { BillOfMaterialsModals, useBillOfMaterialsStore } from '../BillOfMaterialsStore';
import { Item } from '../types';
import useItemLabels from '../hooks/useItemLabels';
import { useItemCatalogueSearch } from './useItemCatalogueSearch';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { BILL_OF_MATERIALS_MODALS } from '../BillOfMaterialsModals';
import { ErpItemCard } from '../components/common/ErpItemCard';
import { useCountry } from '../../../hooks/useCountry';
import DuplicateErpGroupsModal, { DuplicateErpWarning } from './DuplicateErpGroupsModal';
import { findGroupsOfItemsWithSameErpId } from '../utils';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

const MemoizedCheckbox = memo(Checkbox, (prevProps, nextProps) => prevProps.checked === nextProps.checked);

type ItemCatalogueModalData = BillOfMaterialsModals['ITEM_CATALOGUE'];

export const COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD = '45px 105px 150px 1fr 100px 100px 150px 150px 100px';

function ItemCatalogueModal({ data }: Readonly<{ data: ItemCatalogueModalData }>) {
  const { preSelectedItemIds = new Set<string>(), disabledItemIds = new Set<string>(), onClose } = data;
  const country = useCountry();
  const { labels } = useItemLabels({ countries: [country] });
  const [selectedItemIds, setSelectedItemIds] = useState(preSelectedItemIds);
  const [showSelectedOnly, setShowSelectedOnly] = useState(false);
  const { formatMessage } = useIntl();
  const { closeModal } = useBillOfMaterialsStore();
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(new Set());
  const [selectedLabelIds, setSelectedLabelIds] = useState<Set<string>>(new Set());
  const [duplicateErpWarning, setDuplicateErpWarning] = useState<DuplicateErpWarning | undefined>(undefined);
  const [selectedItemIdsForFilter, setSelectedItemIdsForFilter] = useState<Set<string>>(new Set(preSelectedItemIds));

  // Need this because select dropdown doesn't work with complicated models (have to separately store label name and label
  // id (since for now label names can be duplicated)).
  const selectedLabelNames = useMemo(() => {
    const allLabels = new Set<string>();
    labels.forEach((label) => {
      if (label.id?.value && selectedLabelIds.has(label.id.value)) {
        allLabels.add(label.label);
      }
    });
    return allLabels;
  }, [labels, selectedLabelIds]);
  const { erpItems } = useErpItems();
  const items = useMemo(() => Object.values(erpItems).filter((item) => !item.archivedAt), [erpItems]);
  const { setQuery, queriedItemIds } = useItemCatalogueSearch(items);
  const filteredItems = useMemo(() => {
    let itemsToFilter = items;
    if (queriedItemIds !== undefined) {
      itemsToFilter =
        queriedItemIds === null
          ? []
          : queriedItemIds
              .map((id) => {
                return erpItems[id];
              })
              .filter((item): item is Item => !!item);
    }

    return itemsToFilter.filter((item) => {
      const isCategoryMatch = selectedCategories.size ? !!item.category && selectedCategories.has(item.category) : true;
      const isLabelMatch = selectedLabelIds.size
        ? !!item.label?.id?.value && selectedLabelIds.has(item.label.id.value)
        : true;
      const isSelectedMatch = showSelectedOnly ? selectedItemIdsForFilter.has(item.id.value) : true;
      return isCategoryMatch && isSelectedMatch && isLabelMatch;
    });
  }, [
    erpItems,
    items,
    queriedItemIds,
    selectedCategories,
    showSelectedOnly,
    selectedLabelIds,
    selectedItemIdsForFilter,
  ]);

  const categories = useMemo(
    () =>
      Array.from(
        new Set(items.map((item) => item.category).filter((item): item is string => item !== undefined)),
        (category) => ({
          value: category,
          label: category,
        }),
      ),
    [items],
  );
  const headerCheckedIconGetter = useCallback(
    () => (
      <Box
        sx={{
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          '&:hover': { opacity: 0.8 },
        }}
        onClick={(e) => {
          e.stopPropagation();
          setShowSelectedOnly(!showSelectedOnly);
          if (showSelectedOnly) {
            setSelectedItemIdsForFilter(new Set(selectedItemIds));
          }
        }}
      >
        <CheckCircleFilledIcon style={{ opacity: showSelectedOnly ? 1 : 0.5 }} />
      </Box>
    ),
    [showSelectedOnly, selectedItemIds],
  );
  const headerTypographyGetter = useCallback(
    (text: MessageDescriptor['id']) => (
      <Typography fontWeight="500" variant="body2">
        <FormattedMessage id={text} />
      </Typography>
    ),
    [],
  );
  const columns = useMemo<ColumnDef<Item>[]>(
    () => [
      {
        header: headerCheckedIconGetter,
        id: 'isSelected',
        enableSorting: false,
        accessorFn: (row) => selectedItemIds.has(row.id.value),
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.erpId'),
        accessorKey: 'erpId',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.category'),
        accessorKey: 'category',
        sortUndefined: 'last',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.description'),
        accessorKey: 'description',
        sortingFn: 'text',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.quantity'),
        accessorKey: 'quantity',
        sortUndefined: 'last',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.unit'),
        accessorKey: 'unit',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.costPerUnit'),
        accessorKey: 'minorPrice',
        sortUndefined: 'last',
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.label'),
        accessorKey: 'labelId',
        sortingFn: 'text',
        sortUndefined: 'last',
      },
      {
        id: 'expand',
        header: '',
        accessorKey: '',
        enableSorting: false,
      },
    ],
    [headerCheckedIconGetter, headerTypographyGetter, selectedItemIds],
  );

  const onRowClick = (item: Item, hasWarning?: boolean) => {
    if (!item.id) {
      return;
    }
    if (selectedItemIds.has(item.id.value)) {
      selectedItemIds.delete(item.id.value);
    } else {
      if (hasWarning) {
        return;
      }
      selectedItemIds.add(item.id.value);
    }
    setSelectedItemIds(new Set(selectedItemIds));
    if (!showSelectedOnly) {
      setSelectedItemIdsForFilter(new Set(selectedItemIds));
    }
  };

  const confirmSubmit = (items: Item[]) => {
    onClose(items);
    closeModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE);
  };

  const onSubmit = async () => {
    const selectedItems = items.filter((item) => selectedItemIds.has(item.id.value));
    const duplicateItems = findGroupsOfItemsWithSameErpId(selectedItems);
    if (duplicateItems.length > 0) {
      setDuplicateErpWarning({
        duplicateItems,
        allSelectedItems: selectedItems,
      });
    } else {
      confirmSubmit(selectedItems);
    }
  };

  const rowElement = ({ item }: { item: Item }) => {
    const isSelected = selectedItemIds.has(item.id.value);
    const isDisabled = disabledItemIds.has(item.id.value);
    return (
      <ErpItemCard
        isDisabled={isDisabled}
        columnWidths={COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD}
        item={item}
        isSelected={isSelected}
        onRowClick={onRowClick}
      />
    );
  };
  return (
    <Modal
      isModalOpen
      handleClose={() => closeModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE)}
      showCloseButton
      height="90vh"
      width="90vw"
      heading={data.title ?? formatMessage({ id: 'common.label.addItems' })}
      data-testid="item-catalogue-modal-container"
    >
      {duplicateErpWarning && (
        <DuplicateErpGroupsModal
          isModalOpen
          handleClose={() => setDuplicateErpWarning(undefined)}
          duplicateErpWarning={duplicateErpWarning}
          confirm={(items: Item[]) => {
            confirmSubmit(items);
          }}
        />
      )}
      <Box height="100%" width="100%" sx={{ position: 'relative', marginTop: '16px' }}>
        <Stack gap="16px">
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="headline3">
              <FormattedMessage id="billOfMaterials.itemCatalogue.subtitle" />
            </Typography>
            <Button onClick={onSubmit} color="warning" style={{ width: '200px', height: '44px' }}>
              <FormattedMessage id="billOfMaterials.itemCatalogue.select" />
            </Button>
          </Stack>
          <Stack gap="8px" style={{ backgroundColor: beige[100], padding: '16px', borderRadius: '8px' }}>
            <Stack
              sx={{ '> .MuiFormControl-root': { flex: '0 0 200px' } }}
              direction="row"
              gap="24px"
              justifyContent="flex-start"
            >
              <Select
                sx={{
                  '.MuiSelect-select': { padding: '8px' },
                }}
                multiple
                onChange={(event) => {
                  setSelectedCategories(
                    new Set(Array.isArray(event.target.value) ? event.target.value : event.target.value.split(',')),
                  );
                }}
                renderValue={() => (
                  <Typography noWrap whiteSpace="nowrap">
                    {Array.from(selectedCategories).join(', ')}
                  </Typography>
                )}
                value={Array.from(selectedCategories)}
                label={formatMessage({ id: 'billOfMaterials.itemCatalogue.filter.category' })}
                name="Category"
                error={false}
                endAdornment={
                  selectedCategories.size > 0 && (
                    <InputAdornment sx={{ marginRight: '10px' }} position="end">
                      <IconButton
                        onClick={() => {
                          setSelectedCategories(new Set());
                        }}
                      >
                        <CloseIcon height={14} width={14} color={grey[900]} />
                      </IconButton>
                    </InputAdornment>
                  )
                }
              >
                {categories.map((category) => (
                  <MenuItem key={category.value} value={category.value}>
                    <Stack direction="row">
                      <MemoizedCheckbox
                        labelSx={{
                          '> .MuiBox-root': {
                            width: '20px',
                            height: '20px',
                            marginRight: '4px',
                          },
                        }}
                        label={category.label}
                        checked={!!category.value && selectedCategories.has(category.value)}
                        onChange={() => {}}
                      />
                    </Stack>
                  </MenuItem>
                ))}
              </Select>
              <Select
                sx={{
                  '.MuiSelect-select': { padding: '8px' },
                }}
                multiple
                onChange={(event) => {
                  setSelectedLabelIds(
                    new Set(Array.isArray(event.target.value) ? event.target.value : event.target.value.split(',')),
                  );
                }}
                renderValue={() => (
                  <Typography noWrap whiteSpace="nowrap">
                    {Array.from(selectedLabelNames).join(', ')}
                  </Typography>
                )}
                value={Array.from(selectedLabelIds)}
                label={formatMessage({ id: 'billOfMaterials.labels.selectLabel' })}
                name="Label"
                error={false}
                endAdornment={
                  selectedLabelIds.size > 0 && (
                    <InputAdornment sx={{ marginRight: '10px' }} position="end">
                      <IconButton
                        onClick={() => {
                          setSelectedLabelIds(new Set());
                        }}
                      >
                        <CloseIcon height={14} width={14} color={grey[900]} />
                      </IconButton>
                    </InputAdornment>
                  )
                }
              >
                {labels.map(
                  (label) =>
                    label.id?.value && (
                      <MenuItem key={label.id.value} value={label.id.value}>
                        <Stack direction="row">
                          <MemoizedCheckbox
                            labelSx={{
                              '> .MuiBox-root': {
                                width: '20px',
                                height: '20px',
                                marginRight: '4px',
                              },
                            }}
                            label={label.label}
                            checked={selectedLabelIds.has(label.id.value)}
                            onChange={() => {}}
                          />
                        </Stack>
                      </MenuItem>
                    ),
                )}
              </Select>
              <TextField
                sx={{
                  input: { padding: '8px' },
                  width: '300px',
                  flexDirection: 'row-reverse',
                }}
                label={formatMessage({ id: 'billOfMaterials.itemCatalogue.filter.search' })}
                onChange={(event) => setQuery(event.target.value)}
                name="Search"
                placeholder="Search..."
                icon={<MagnifyingGlassOutlinedIcon />}
              />
            </Stack>
            <TanstackTable
              styles={{
                columnWidths: COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD,
                bodyColumnWidths: '1fr',
                tableContainer: () => ({
                  padding: '0 8px',
                }),
                tableBodyRow: () => ({
                  paddingBottom: '8px',
                }),
                tableHeadCell: () => ({
                  border: 'none',
                }),
              }}
              columns={columns}
              customRowRenderer={(item) => rowElement({ item })}
              data={filteredItems}
              virtualizationOptions={{
                measureElement:
                  typeof window !== 'undefined' && navigator.userAgent.indexOf('Firefox') === -1
                    ? (element) => element?.getBoundingClientRect().height
                    : undefined,
                estimateSize: () => 53,
              }}
            />
          </Stack>
        </Stack>
      </Box>
    </Modal>
  );
}

export default memo(ItemCatalogueModal);
