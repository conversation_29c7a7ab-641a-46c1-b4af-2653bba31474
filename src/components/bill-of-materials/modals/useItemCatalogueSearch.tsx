import { useEffect, useMemo, useRef, useState } from 'react';
import { debounce } from 'lodash';
import { Item } from '../types';
import Fuse, { IFuseOptions } from 'fuse.js';

const QUERY_FIELDS = ['category', 'supplier', 'supplierId', 'description', 'label.label'];
const ID_FIELDS = ['erpId'];
const fuseOptions: IFuseOptions<Item> = {
  includeScore: true,
  keys: [...QUERY_FIELDS, ...ID_FIELDS],
  ignoreDiacritics: true,
  shouldSort: true,
  threshold: 0.3, // Adjust this number to make the fuzziness more or less sensitive
  useExtendedSearch: true,
};

export function useItemCatalogueSearch(items: Item[]) {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  // Order of search results matters so we need to retain as an array
  const [queriedItemIds, setQueriedItemIds] = useState<string[] | undefined | null>(undefined);
  const searchIndex = useRef(new Fuse(items, fuseOptions));
  const debouncedSetQuery = useMemo(() => debounce(setDebouncedQuery, 300), [setDebouncedQuery]);

  useEffect(() => {
    debouncedSetQuery(query);
  }, [debouncedSetQuery, query]);
  useEffect(() => {
    if (!debouncedQuery?.trim()) {
      setQueriedItemIds(undefined);
    } else {
      // We either want to hit a fuzzy-search on any of the query fields, OR search ID_FIELDS
      // by them *containing* the query (the single quote ' symbol is used for this)
      const searchQuery = {
        $or: [
          ...QUERY_FIELDS.map((field) => {
            return {
              [field]: debouncedQuery,
            };
          }),
          ...ID_FIELDS.map((field) => {
            return {
              [field]: `'${debouncedQuery}`,
            };
          }),
        ],
      };
      const searchResults = searchIndex.current.search(searchQuery);
      if (!searchResults.length) {
        setQueriedItemIds(null);
      } else {
        setQueriedItemIds(searchResults.map((result) => result.item.id?.value));
      }
    }
  }, [debouncedQuery]);

  return {
    setQuery,
    queriedItemIds,
    query,
  };
}
