import Typography from '@mui/material/Typography';
import { Stack } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { ErpGroupedItems } from '../utils';
import { Item } from '../types';
import { useCallback, useMemo, useState } from 'react';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import { ColumnDef } from '@tanstack/table-core';
import { ErpItemCard } from '../components/common/ErpItemCard';
import { beige, surface } from '@ui/theme/colors';
import { COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD } from './ItemCatalogueModal';

export interface DuplicateErpWarning {
  duplicateItems: ErpGroupedItems[];
  allSelectedItems: Item[];
}

const filterSelectedItems = (allSelectedItems: Item[], deselectedItemIds: Set<string>) => {
  return allSelectedItems.filter((item) => !deselectedItemIds.has(item.id.value));
};

function DuplicateErpGroupsModal({
  isModalOpen,
  handleClose,
  confirm,
  duplicateErpWarning: { duplicateItems, allSelectedItems },
}: {
  isModalOpen: boolean;
  handleClose: () => void;
  confirm: (selectItems: Item[]) => void;
  duplicateErpWarning: DuplicateErpWarning;
}) {
  const [deselectedItemIds, setDeselectedItemIds] = useState<Set<string>>(new Set());

  const headerTypographyGetter = useCallback(
    (text: MessageDescriptor['id']) => (
      <Typography fontWeight="500" variant="body2">
        <FormattedMessage id={text} />
      </Typography>
    ),
    [],
  );

  const columns = useMemo<ColumnDef<ErpGroupedItems>[]>(
    () => [
      {
        header: () => null,
        enableSorting: false,
        accessorKey: 'checkPlaceholder',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.erpId'),
        enableSorting: false,
        accessorKey: 'erpId',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.category'),
        enableSorting: false,
        accessorKey: 'category',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.description'),
        enableSorting: false,
        accessorKey: 'description',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.quantity'),
        enableSorting: false,
        accessorKey: 'quantity',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.unit'),
        enableSorting: false,
        accessorKey: 'unit',
      },
      {
        header: headerTypographyGetter.bind(null, 'billOfMaterials.itemCatalogue.table.costPerUnit'),
        enableSorting: false,
        accessorKey: 'minorPrice',
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.label'),
        accessorKey: 'labelId',
        enableSorting: false,
      },
      {
        id: 'expand',
        header: '',
        accessorKey: '',
        enableSorting: false,
      },
    ],
    [headerTypographyGetter],
  );

  const onItemClick = (item: Item) => {
    if (!item.id) {
      return;
    }
    setDeselectedItemIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(item.id.value)) {
        newSet.delete(item.id.value);
      } else {
        newSet.add(item.id.value);
      }
      return newSet;
    });
  };

  const rowElement = ({ item }: { item: ErpGroupedItems }) => {
    return (
      <Stack
        gap={2}
        sx={{
          display: 'flex',
          alignItems: 'col',
          padding: '16px',
          borderRadius: '16px',
          backgroundColor: surface['100'],
        }}
      >
        {item.items.map((subItem) => {
          const isSelected = !deselectedItemIds.has(subItem.id.value);
          return (
            <ErpItemCard
              key={subItem.id.value}
              item={subItem}
              isSelected={isSelected}
              columnWidths={COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD}
              onRowClick={onItemClick}
              sx={{
                backgroundColor: 'white !important',
              }}
              isExpanded
            />
          );
        })}
      </Stack>
    );
  };

  return (
    <Modal
      data-testid="duplicate-erp-groups-modal"
      isModalOpen={isModalOpen}
      handleClose={handleClose}
      showCloseButton
      height="80vh"
      width="80vw"
      heading="Duplicate ERP IDs"
      sx={{ backgroundColor: beige[100] }}
    >
      <Stack>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="body1" sx={{ marginBottom: 2, marginTop: 2 }}>
            <FormattedMessage id="billOfMaterials.itemCatalogue.duplicates.description" />
          </Typography>
          <Button
            data-testid="duplicate-erp-groups-modal-confirm-button"
            onClick={() => confirm(filterSelectedItems(allSelectedItems, deselectedItemIds))}
            color="warning"
            style={{ width: '200px', height: '44px' }}
          >
            <FormattedMessage id="billOfMaterials.itemCatalogue.select" />
          </Button>
        </Stack>
        <TanstackTable
          styles={{
            columnWidths: COLUMN_WIDTHS_MATCHING_ERP_ITEM_CARD,
            bodyColumnWidths: '1fr',
            tableContainer: () => ({
              padding: '0 8px',
            }),
            tableBodyRow: () => ({
              paddingBottom: '32px',
            }),
            tableHeadRow: () => ({
              padding: '16px',
            }),
            tableHeadCell: () => ({
              border: 'none',
            }),
          }}
          columns={columns}
          customRowRenderer={(item) => rowElement({ item })}
          data={duplicateItems}
          virtualizationOptions={{
            measureElement:
              typeof window !== 'undefined' && navigator.userAgent.indexOf('Firefox') === -1
                ? (element) => element?.getBoundingClientRect().height
                : undefined,
            estimateSize: () => 53,
          }}
        />
      </Stack>
    </Modal>
  );
}

export default DuplicateErpGroupsModal;
