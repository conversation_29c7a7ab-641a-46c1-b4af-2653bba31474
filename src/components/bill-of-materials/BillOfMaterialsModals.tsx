import ItemCatalogueModal from './modals/ItemCatalogueModal';
import { useBillOfMaterialsStore } from './BillOfMaterialsStore';

export const BILL_OF_MATERIALS_MODALS = {
  ITEM_CATALOGUE: 'ITEM_CATALOGUE',
} as const;

export function BillOfMaterialsModals() {
  const { openModals } = useBillOfMaterialsStore();
  const itemCatalogueModal = openModals.find((modal) => modal.modal === BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE);
  return <>{!!itemCatalogueModal && <ItemCatalogueModal data={itemCatalogueModal.data} />}</>;
}
