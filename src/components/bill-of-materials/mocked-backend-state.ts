import { createTestErpItems } from './test-utils';
import { v4 as uuidv4 } from 'uuid';
import { isNotNullish } from 'utils/isNotNullish';
import { Item } from './types';

export class MockedBackendState {
  items: Item[];

  constructor() {
    this.items = createTestErpItems();
  }

  setLabelForItems({ input: { itemIds, labelId } }: { input: { itemIds: string[]; labelId?: string } }) {
    const itemIdsToSet = new Set(itemIds);

    for (const item of this.items) {
      if (itemIdsToSet.has(item.id.value)) {
        item.labelId = labelId ? { value: labelId } : undefined;
      }
    }
    return {};
  }

  getItems() {
    return {
      items: this.items,
    };
  }

  createItemEquivalenceGroup({ input }: { input: { itemIds: string[] } }) {
    const groupId = { value: uuidv4() };
    const { itemIds } = input;
    for (const item of this.items) {
      if (itemIds.includes(item.id.value)) {
        item.equivalenceGroupId = groupId;
      }
    }
    return {
      groupId,
    };
  }

  removeItemsFromEquivalenceGroup({ input }: { input: { itemIds: string[] } }) {
    const { itemIds } = input;
    for (const item of this.items) {
      if (itemIds.includes(item.id.value)) {
        item.equivalenceGroupId = undefined;
      }
    }
    return {
      items: itemIds.map((id) => this.items.find((item) => item.id.value === id)).filter(isNotNullish),
    };
  }

  addItemsToEquivalenceGroup({ input }: { input: { itemIds: string[]; groupId: string } }) {
    const { itemIds, groupId } = input;
    for (const item of this.items) {
      if (itemIds.includes(item.id.value)) {
        item.equivalenceGroupId = { value: groupId };
      }
    }
    return itemIds.map((id) => this.items.find((item) => item.id.value === id)).filter(isNotNullish);
  }
}
