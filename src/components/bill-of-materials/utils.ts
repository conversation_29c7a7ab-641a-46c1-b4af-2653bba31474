import {
  Bundle,
  BundleCollection,
  BundleItem,
  InstallationKitBundle as InstallationKitBundleDetails,
  VanStockBundle as VanStockBundleDetails,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import {
  INSTALLATION_KIT_IDENTIFIER,
  InstallationKitBundle,
  InstallationKitBundleCollection,
  Item,
  STATIC_IDENTIFIERS,
  StaticBundleCollection,
  TEMPLATE_IDENTIFIER,
  TemplateBundleCollection,
  ValidStockType,
  VAN_STOCK_IDENTIFIER,
  VanStockBundle,
  VanStockBundleCollection,
} from './types';
import { getCountryCodeFromCountry, getCountryFromLocale, marketConfiguration } from '../../utils/marketConfigurations';
import { MessageKey } from 'messageType';

type AddBundleItemRequest = {
  bundleId: string;
  itemId: string;
  defaultQuantity?: number;
  editable: boolean;
  instructions?: string;
};

export function bundleItemsToBundleItemRequest(
  bundleItems: BundleItem[],
  bundleId?: string | undefined,
): AddBundleItemRequest[] {
  return bundleItems
    .map((bundleItem) => {
      const usedBundleId = bundleId ?? bundleItem.bundleId?.value;
      const itemId = bundleItem.itemId?.value;
      if (!usedBundleId || !itemId) {
        return undefined;
      }
      const request: AddBundleItemRequest = {
        bundleId: usedBundleId,
        itemId,
        defaultQuantity: bundleItem.defaultQuantity,
        editable: bundleItem.editable,
        instructions: bundleItem.instructions,
      };
      return request;
    })
    .filter((item: AddBundleItemRequest | undefined): item is AddBundleItemRequest => item !== undefined);
}

export function compareTimestamps(a?: Date, b?: Date): number {
  // Handle undefined dates
  if (!a && !b) return 0;
  if (!a) return 1;
  if (!b) return -1;

  // Convert to Date objects and compare (newest first)
  return new Date(String(b)).getTime() - new Date(String(a)).getTime();
}

export function isStaticBundleCollection(collection: BundleCollection): collection is StaticBundleCollection {
  return !!collection.details?.details?.$case && STATIC_IDENTIFIERS.includes(collection.details.details.$case);
}

export function isTemplateBundleCollection(collection: BundleCollection): collection is TemplateBundleCollection {
  return collection.details?.details?.$case === TEMPLATE_IDENTIFIER;
}

export function isVanStockBundleCollection(collection: BundleCollection): collection is VanStockBundleCollection {
  return collection.details?.details?.$case === VAN_STOCK_IDENTIFIER;
}

export function isVanStockBundleForRegion(regionId: string): (bundle: Bundle) => boolean {
  return (bundle: Bundle) =>
    isVanStockBundle(bundle) && bundle.details.details.vanStock.regions.some((id) => id.value === regionId);
}

export function isVanStockBundle(bundle: Bundle): bundle is VanStockBundle {
  return bundle.details?.details?.$case === VAN_STOCK_IDENTIFIER;
}

export function getVanStockDetails(bundle: Bundle): VanStockBundleDetails | undefined {
  if (isVanStockBundle(bundle)) {
    return bundle.details.details.vanStock;
  }

  return undefined;
}

export function createEmptyVanStockDetails(): VanStockBundleDetails {
  return {
    regions: [],
    stockType: VanStockType.VAN_STOCK_TYPE_UNSPECIFIED,
  };
}

/**
 * Creates an empty installation kit details object with an empty itemIds array
 */
export function createEmptyInstallationKitDetails(): InstallationKitBundleDetails {
  return {
    itemIds: [],
  };
}

export function isInstallationKitBundleCollection(
  collection: BundleCollection,
): collection is InstallationKitBundleCollection {
  return collection.details?.details?.$case === INSTALLATION_KIT_IDENTIFIER;
}

export function isInstallationKitBundle(bundle: Bundle): bundle is InstallationKitBundle {
  return bundle.details?.details?.$case === INSTALLATION_KIT_IDENTIFIER;
}

/**
 * Ensures a bundle has the proper structure to be used as a VanStockBundle.
 * If the bundle doesn't have VanStock details, it adds them with empty values.
 */
export function ensureVanStockBundle(bundle: Bundle): VanStockBundle {
  if (isVanStockBundle(bundle)) {
    return bundle;
  }
  return {
    ...bundle,
    details: {
      details: {
        $case: VAN_STOCK_IDENTIFIER,
        vanStock: createEmptyVanStockDetails(),
      },
    },
  };
}

/**
 * Ensures a bundle has the proper structure to be used as an InstallationKitBundle.
 * If the bundle doesn't have InstallationKit details, it adds them with empty values.
 */
export function ensureInstallationKitBundle(bundle: Bundle): InstallationKitBundle {
  if (isInstallationKitBundle(bundle)) {
    return bundle;
  }
  return {
    ...bundle,
    details: {
      details: {
        $case: INSTALLATION_KIT_IDENTIFIER,
        installationKit: createEmptyInstallationKitDetails(),
      },
    },
  };
}

/**
 * Returns a list of valid VanStockType enum values, excluding unspecified and unrecognized types.
 *
 * @returns {ValidStockType[]} Array of valid VanStockType values.
 */
export function getValidVanStockTypes(): ValidStockType[] {
  return Object.values(VanStockType).filter((value) => {
    const isValidType = value !== VanStockType.VAN_STOCK_TYPE_UNSPECIFIED && value !== VanStockType.UNRECOGNIZED;
    return isValidType && typeof value === 'number';
  });
}

/**
 * Maps VanStockType enum values to human-readable labels
 */
export function getVanStockTypeLabel(type: ValidStockType): string {
  switch (type) {
    case VanStockType.VAN_STOCK_TYPE_PLUMBING:
      return 'Plumbing';
    case VanStockType.VAN_STOCK_TYPE_ELECTRICAL:
      return 'Electrical';

    default:
      return type satisfies never;
  }
}

export function getItemMinorPricePerUnit({
  minorPrice,
  quantity: itemQuantity,
}: {
  minorPrice?: number;
  quantity?: number;
}) {
  if (!minorPrice) return 0;
  const quantity = itemQuantity ?? 1;
  return minorPrice / quantity;
}

export function getFormattedItemPricePerUnit(
  item?: { currency?: string; minorPrice?: number; quantity?: number },
  nextLocale?: string,
) {
  const locale = nextLocale ?? 'en-GB';
  const currency = item?.currency ?? 'EUR';

  if (item?.minorPrice === undefined || item?.quantity === undefined) {
    return '-';
  }

  const price = getItemMinorPricePerUnit({ quantity: item.quantity, minorPrice: item.minorPrice }) / 100;
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(price);
}

export type ErpGroupedItems = {
  erpId: string;
  items: Item[];
};

function groupItemsByErpId(items: Item[]) {
  return Map.groupBy(items, (item) => item.erpId);
}

export function findGroupsOfItemsWithSameErpId(items: Item[]): ErpGroupedItems[] {
  return groupItemsByErpId(items)
    .entries()
    .filter(([, itemsArray]) => itemsArray.length > 1)
    .map(([erpId, itemsArray]) => ({
      erpId,
      items: itemsArray,
    }))
    .toArray();
}

export function getFormattedItemPrice(
  item?: { currency?: string; minorPrice?: number; quantity?: number },
  nextLocale?: string,
) {
  const locale = nextLocale ?? 'en-GB';
  const country = getCountryFromLocale(locale);
  const countryCode = getCountryCodeFromCountry(country);
  const currency = item?.currency ?? marketConfiguration[countryCode].currency ?? 'EUR';
  const minorPrice = item?.minorPrice ?? 0;
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(minorPrice / 100);
}

/**
 * Returns true if the given ERP item is only referenced by the provided bundle
 * and that bundle is an Installation Kit.
 */
export function isItemOnlyUsedInInstallationKit(item: Item, bundle: Bundle): boolean {
  if (!isInstallationKitBundle(bundle)) return false;

  const included = item.includedInBundles ?? [];
  const otherTemplateBundles = included
    .filter((b) => b.id !== bundle.id!.value)
    .filter((b) => b.typeIdentifier === TEMPLATE_IDENTIFIER);
  const includedInTemplateBundle = otherTemplateBundles.length > 0;

  return !includedInTemplateBundle;
}

export interface WarningMessageDescriptor {
  id: MessageKey;
  defaultMessage?: string;
  values?: Record<string, string | number | boolean>;
}

/**
 * Computes context-specific warnings for an ERP Item in the BOM UI.
 * Returns an array of i18n message descriptors; the caller is responsible for formatting.
 */
export function getErpItemWarningsForContext(item: Item, context: { bundle?: Bundle }): WarningMessageDescriptor[] {
  const warnings: WarningMessageDescriptor[] = [];

  if (context.bundle && isItemOnlyUsedInInstallationKit(item, context.bundle)) {
    warnings.push({
      id: 'billOfMaterials.installationKit.itemNotUsedElsewhere',
    });
  }

  return warnings;
}
