import { cleanup, screen, waitFor } from '@testing-library/react';
import { useSearchParams } from 'next/navigation';
import { afterEach, Mock, vi } from 'vitest';
import { billOfMaterialsTestSetup } from './test-utils';
import { createRouterMock, useRouter } from '@mocks/next/router';
import { BillOfMaterials } from './BillOfMaterials';
import { renderWithProviders } from '../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { heatDesignTheme } from '../heat-design/HeatDesignTheme';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';

vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(),
}));
useRouter.mockReturnValue({
  ...createRouterMock(),
  pathname: '/bill-of-materials',
  query: {
    country: 'DE',
  },
  push: vi.fn(),
});

(useSearchParams as Mock).mockImplementation(() => ({
  get: vi.fn((key) => {
    if (key === 'country') return 'DE';
    return null;
  }),
}));

async function billOfMaterialsRendered() {
  renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <AiraThemeProvider theme={heatDesignTheme}>
        <BillOfMaterials />
      </AiraThemeProvider>
    </IntlProvider>,
  );

  await waitFor(() => {
    screen.getAllByTestId(/bundle-collection-card/);
  });
}

billOfMaterialsTestSetup();

afterEach(() => {
  cleanup();
});

describe('BillOfMaterials page', () => {
  it('should render BillOfMaterials component', async () => {
    await billOfMaterialsRendered();
    expect(screen.getByTestId('bill-of-materials-page')).toBeInTheDocument();
  });

  it('should render both static and template bundle collection sections', async () => {
    await billOfMaterialsRendered();
    const staticBundleCollectionsContainer = screen.getByTestId('static-bundle-collections');
    const templateBundleCollectionsContainer = screen.getByTestId('template-bundle-collections');
    expect(staticBundleCollectionsContainer).toBeVisible();
    expect(templateBundleCollectionsContainer).toBeVisible();
  });

  it('should show add bundle collection button', async () => {
    await billOfMaterialsRendered();
    const addButton = screen.getByTestId('add-bundle-collection-button');
    expect(addButton).toBeVisible();
  });

  it('should render correct tab buttons', async () => {
    await billOfMaterialsRendered();
    expect(screen.getAllByText('billOfMaterials.bundleCollections')[0]).toBeVisible();
    expect(screen.getByText('billOfMaterials.itemRelations')).toBeVisible();
  });
});
