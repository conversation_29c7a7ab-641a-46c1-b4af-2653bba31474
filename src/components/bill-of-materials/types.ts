import {
  Bundle,
  BundleCollection,
  InstallationKitBundleCollection as InstallationKitBundleCollectionDetails,
  ItemLabel,
  TemplateBundleCollection as TemplateBundleCollectionDetails,
  VanStockBundle as VanStockBundleDetails,
  VanStockBundleCollection as VanStockBundleCollectionDetails,
  InstallationKitBundle as InstallationKitBundleDetails,
  VanStockType,
  DesignedBomBundle as ProtoDesignedBomBundle,
  DesignedBomBundleItem as ProtoDesignedBomBundleItem,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { ErpItem } from '../../server/api/routers/billOfMaterials';
import { UUID } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/contract/common/v1/uuid';

export const INSTALLATION_KIT_IDENTIFIER = 'installationKit';
export const VAN_STOCK_IDENTIFIER = 'vanStock';
export const TEMPLATE_IDENTIFIER = 'template';
export const STATIC_IDENTIFIERS = [VAN_STOCK_IDENTIFIER, INSTALLATION_KIT_IDENTIFIER];
export type Identifier = typeof INSTALLATION_KIT_IDENTIFIER | typeof VAN_STOCK_IDENTIFIER | typeof TEMPLATE_IDENTIFIER;

export type VanStockBundle = Omit<Bundle, 'details'> & {
  details: {
    details: {
      $case: typeof VAN_STOCK_IDENTIFIER;
      vanStock: VanStockBundleDetails;
    };
  };
};

export type VanStockBundleCollection = Omit<BundleCollection, 'details'> & {
  details: {
    details: {
      $case: typeof VAN_STOCK_IDENTIFIER;
      vanStock: VanStockBundleCollectionDetails;
    };
  };
};

export type InstallationKitBundle = Omit<Bundle, 'details'> & {
  details: {
    details: {
      $case: typeof INSTALLATION_KIT_IDENTIFIER;
      installationKit: InstallationKitBundleDetails;
    };
  };
};

export type InstallationKitBundleCollection = Omit<BundleCollection, 'details'> & {
  details: {
    details: {
      $case: typeof INSTALLATION_KIT_IDENTIFIER;
      installationKit: InstallationKitBundleCollectionDetails;
    };
  };
};

export type StaticBundleCollection = VanStockBundleCollection | InstallationKitBundleCollection;

export type TemplateBundleCollection = Omit<BundleCollection, 'details'> & {
  details: {
    details: {
      $case: typeof TEMPLATE_IDENTIFIER;
      template: TemplateBundleCollectionDetails;
    };
  };
};

export type BundleReference = {
  id: string;
  title: string;
  typeIdentifier?: Identifier;
};

export type Item = ErpItem & {
  includedInBundles: BundleReference[];
  label?: ItemLabel;
};

export type BaseTableItem = {
  itemId: string;
  quantity?: number;
};

export type ErpTableItem = BaseTableItem & {
  type: 'erp';
  instructions?: string;
};

export type CustomTableItem = BaseTableItem & {
  type: 'custom';
  name?: string;
  cost?: number;
};

export type TableItem = ErpTableItem | CustomTableItem;

export type MiscellaneousItem = TableItem & {
  createdAt: Date;
};

export const currencySymbols: Record<string, string> = {
  EUR: '€',
  USD: '$',
  GBP: '£',
};

export type VanStockRegion = {
  id: UUID;
  name: string;
};

export type ValidStockType = Exclude<VanStockType, VanStockType.UNRECOGNIZED | VanStockType.VAN_STOCK_TYPE_UNSPECIFIED>;

export type DesignedBomBundleItem = Omit<ProtoDesignedBomBundleItem, 'quantity'> & {
  quantity?: number;
};

export type DesignedBomBundle = Omit<ProtoDesignedBomBundle, 'items'> & {
  items: DesignedBomBundleItem[];
};

export type RadiatorItem = {
  itemId: string;
  name: string;
  erpItem?: Item;
  quantity: number;
};
