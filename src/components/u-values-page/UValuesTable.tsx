import { Table, TableBody, TableCell, TableHead, TableRow, Typography } from '@mui/material';
import { grey, red } from '@ui/theme/colors';
import { formatUValueValue } from 'components/heat-design/components/u-value-input/helpers';
import { UValue } from 'components/heat-design/models/UValue';
import UValueSourceInfo from './UValueSourceInfo';

type Props = {
  uValues: UValue[];
  showSources: boolean;
  showZones: boolean;
};

export default function UValuesTable({ uValues, showSources, showZones }: Props) {
  return (
    <Table sx={{ border: `1px solid ${grey[200]}`, background: grey[100], mb: 6 }}>
      <TableHead>
        <TableRow>
          <TableCell>{/* index */}</TableCell>
          <TableCell width="100%">Name</TableCell>
          <TableCell>Value</TableCell>
          <TableCell>From</TableCell>
          <TableCell>Until</TableCell>
          {showZones && <TableCell>Zone</TableCell>}
        </TableRow>
      </TableHead>
      <TableBody sx={{ background: 'white' }}>
        {uValues.map((uValue, index) => (
          <TableRow hover key={uValue.id} sx={{ backgroundColor: uValue.metadata.isDeprecated ? red[100] : 'inherit' }}>
            <TableCell sx={{ fontVariantNumeric: 'tabular-nums', color: grey[400] }}>{index + 1}</TableCell>
            <TableCell>
              <Typography>
                {uValue.name}
                {uValue.metadata.isDeprecated && (
                  <span
                    style={{
                      background: red[600],
                      color: 'white',
                      fontWeight: 500,
                      display: 'inline-block',
                      borderRadius: 20,
                      padding: '0 8px',
                      marginLeft: '2ch',
                      fontSize: 14,
                    }}
                  >
                    deprecated
                  </span>
                )}
              </Typography>
              {showSources && uValue.metadata.source !== undefined && (
                <UValueSourceInfo uValueSource={uValue.metadata.source} />
              )}
            </TableCell>
            <TableCell sx={{ fontVariantNumeric: 'tabular-nums', fontWeight: 500 }}>
              {formatUValueValue(uValue.value)}
            </TableCell>
            <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }}>
              {uValue.metadata.fromYearInclusive ?? '-'}
            </TableCell>
            <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }}>{uValue.metadata.toYearInclusive ?? '-'}</TableCell>
            {showZones && (
              <TableCell>{uValue.metadata.climateZone ?? <span style={{ color: grey[500] }}>Any</span>}</TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
