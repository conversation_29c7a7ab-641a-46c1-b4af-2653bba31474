import { Typography } from '@mui/material';
import { blue, grey } from '@ui/theme/colors';
import { U_VALUE_SOURCE_INFO, UValueSource } from 'components/heat-design/u-values/u-value-sources';

type Props = {
  uValueSource: UValueSource;
};

export default function UValueSourceInfo({ uValueSource }: Props) {
  const { link, name } = U_VALUE_SOURCE_INFO[uValueSource];

  return (
    <Typography color={grey[600]}>
      <span style={{ marginRight: '1ch' }}>{name}</span>
      {link && (
        <a href={link} target="_blank" rel="noreferrer" style={{ color: blue[500], textDecoration: 'underline' }}>
          View
        </a>
      )}
    </Typography>
  );
}
