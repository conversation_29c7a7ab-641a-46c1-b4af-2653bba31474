import { ReactElement, ReactNode, useMemo, useRef, useState } from 'react';
import { FormattedMessage, IntlShape, useIntl } from 'react-intl';
import { Stack, Typography } from '@mui/material';
import BedOutlinedIcon from '@ui/components/Icons/material/BedOutlined';
import FamilyRestroomIcon from '@ui/components/Icons/material/FamilyRestroom';
import BathtubOutlinedIcon from '@ui/components/Icons/material/BathtubOutlined';
import HourglassTopIcon from '@ui/components/Icons/material/HourglassTop';
import { beige, grey } from '@ui/theme/colors';
import { Heading } from '@ui/components/Heading/Heading';
import { WaterDropIcon } from '@ui/components/StandardIcons/WaterDropIcon';
import { ThermometerIcon } from '@ui/components/StandardIcons/ThermometerIcon';
import { HouseWave3Icon } from '@ui/components/StandardIcons/HouseWave/HouseWave3Icon';
import { Button } from '@ui/components/Button/Button';
import { DeviceFilledIcon } from '@ui/components/StandardIcons/DeviceFilledIcon';
import { DeviceOutlinedIcon } from '@ui/components/StandardIcons/DeviceOutlinedIcon';
import { ChartLineUpIcon } from '@ui/components/StandardIcons/ChartLineUpIcon';
import { Wave3HorizontalOutlinedIcon } from '@ui/components/StandardIcons/Wave3HorizontalOutlinedIcon';
import { ThermometerOutlinedIcon } from '@ui/components/StandardIcons/ThermometerOutlinedIcon';
import HeatPumpPackageInformation from '../quotation/sections/HeatPumpPackageInformation';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { Box } from '@mui/system';
import { HeatPumpIndoorUnit, HeatPumpOutdoorUnit } from 'types/types';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { useProgressStore } from 'components/quotation/stores/ProgressStore';
import { LockOpen as LockOpenIcon } from '@ui/components/Icons/material';
import { MessageKey } from 'messageType';
import toast from 'react-hot-toast';
import { api } from 'utils/api';
import { StyledFormattedMessage } from 'utils/localization';
import { useRouter } from 'next/router';
import { useGroundwork } from 'context/groundwork-context';
import { useInvalidateSolution } from 'components/quotation/hooks/useSaveSolution';
import { CountryCode } from 'utils/marketConfigurations';
import { InfoRow } from './report/shared/InfoRow';
import { ErrorModal } from './modals/ErrorModal';
import { getHotWaterUsagePerDay, getHouseHeatDesignResult } from './utils/calculations';
import RadiatorList from './modals/RadiatorList';
import { calculatePerformance, useHeatPumpTechnicalSpecifications } from './utils/heatPumpPerformanceCharacteristics';
import { RadiatorsGroupedBySize, RadiatorSize, useFlowReturnDeltaT, useFlowTemp } from './stores/HeatSourceStore';
import {
  useAdjustedOutdoorDesignTemperature,
  useConstructionYear,
  useHeatDesignHouseInputsStore,
  useNumOccupants,
} from './stores/HouseInputsStore';
import {
  countBathrooms,
  countBedrooms,
  getRadiatorSizeGroup,
  optionalToNoDecimalPlaces,
  optionalTwoDecimalPlaces,
  toTwoDecimalPlaces,
} from './utils/helpers';
import { useRooms } from './stores/RoomsStore';
import HeatDesignCard from './HeatDesignCard';
import { useFloorsStore } from './stores/FloorsStore';
import { useUValuesStore } from './stores/UValuesStore';
import { useClimateDataStore } from './stores/ClimateDataStore';
import InfoRowsSection, { FlexItem } from './product-selection/InfoRowsSection';
import { useDwellingHeatDesignResult } from './hooks/useDwellingHeatDesignResult';
import { useRadiatorsForCountry } from './hooks/useRadiators';

const GRID_GAPS = 4;
const ROW_SPACING = 2;

function UnitDetailSection({ sectionHeader, children }: { sectionHeader: ReactNode; children: ReactNode }) {
  return (
    <Stack gap={GRID_GAPS}>
      <Heading variant="headline3" level={2}>
        {sectionHeader}
      </Heading>
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: GRID_GAPS,
        }}
      >
        {children}
      </Box>
    </Stack>
  );
}

function DetailsCard({
  title,
  label,
  testId,
  icon,
}: {
  title: string;
  label: ReactNode;
  testId: string;
  icon: ReactNode;
}): ReactElement {
  return (
    <Stack
      gap={2}
      sx={{
        background: 'white',
        borderRadius: 2,
        p: 3,
      }}
    >
      <InfoRow
        icon={icon}
        labelFontVariant="number1"
        label={title}
        data-testid={`${testId}-heading`}
        sx={{ borderBottom: 'none' }}
      />
      <InfoRow labelFontVariant="body1" label={label} />
    </Stack>
  );
}

function getCylinderReheatTime(
  formatMessage: IntlShape['formatMessage'],
  indoorUnits: HeatPumpIndoorUnit[],
  outdoorUnits: HeatPumpOutdoorUnit[],
  waterReheatTimeMinutes?: number,
): ReactElement | undefined {
  const errorIntro = 'Unable to determine cylinder reheat time';

  if (indoorUnits === undefined || indoorUnits.length === 0) {
    return <p>{errorIntro}: No indoor unit selected</p>;
  }

  if (indoorUnits.length > 1) {
    return <p>{errorIntro}: Multiple indoor units selected</p>;
  }

  if (outdoorUnits === undefined || outdoorUnits.length === 0) {
    return <p>{errorIntro}: No outdoor unit selected</p>;
  }

  if (outdoorUnits.length > 1) {
    return <p>{errorIntro}: Multiple outdoor units selected</p>;
  }

  const indoorUnit = indoorUnits[0]!;
  const outdoorUnit = outdoorUnits[0]!;

  if (outdoorUnit?.effect === undefined || outdoorUnit.effect <= 0) {
    return (
      <p>
        {errorIntro}: Invalid outdoor unit power: {outdoorUnit.effect}
      </p>
    );
  }

  if (indoorUnit?.capacity?.value === undefined || indoorUnit.capacity.value < 0) {
    return (
      <p>
        {errorIntro}: Invalid indoor unit capacity: {indoorUnit?.capacity?.value}
      </p>
    );
  }

  return indoorUnit.capacity.value > 0 ? (
    <DetailsCard
      label={<StyledFormattedMessage id="heatDesign.waterReheatTime" />}
      title={`${optionalToNoDecimalPlaces(waterReheatTimeMinutes)} ${formatMessage({ id: 'common.minutes' })}`}
      testId="cylinder-reheat-time"
      icon={<HourglassTopIcon />}
    />
  ) : undefined;
}

export default function ProductSelection() {
  const [errorModalIsOpen, setErrorModalIsOpen] = useState(false);
  const { formatMessage } = useIntl();
  const loadingToast = useRef<string>();
  const { solution: solutionId } = useRouter().query;
  const invalidateSolution = useInvalidateSolution();
  const flowTemperature = useFlowTemp();
  const adjustedOutdoorDesignTemperature = useAdjustedOutdoorDesignTemperature();
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const outdoorUnits = selectedHeatPumpPackages.heatPumpOutdoorUnit ?? [];
  const singleSelectedOutdoorUnit = outdoorUnits.length === 1 ? outdoorUnits[0] : undefined;
  const indoorUnits = selectedHeatPumpPackages.heatPumpIndoorUnit ?? [];
  const technicalSpecificationId = singleSelectedOutdoorUnit?.technicalSpecificationId;
  const technicalSpecification = useHeatPumpTechnicalSpecifications(technicalSpecificationId);
  const compatibilityGroup = singleSelectedOutdoorUnit?.compatibilityGroup?.name;
  const { totalHeatLoss, waterReheatTimeMinutes } = useDwellingHeatDesignResult();
  const houseInputsStore = useHeatDesignHouseInputsStore();
  const climateDataStore = useClimateDataStore();

  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const rooms = useRooms();
  const numberOfBedrooms = countBedrooms(rooms);
  const numberOfBathrooms = countBathrooms(rooms);
  const numberOfResidents = useNumOccupants();
  const uValuesStore = useUValuesStore();
  const { floors } = useFloorsStore();
  const flowReturnDeltaT = useFlowReturnDeltaT();

  const { data: catalogueRadiators } = useRadiatorsForCountry(countryCode);

  const { newRadiators, numNewRadiators } = useMemo(() => {
    const radiatorErpIds: Map<string, string | undefined> = new Map();

    catalogueRadiators?.radiators.forEach((catalogueRadiator) => {
      if (catalogueRadiator.radiatorId) {
        radiatorErpIds.set(catalogueRadiator.radiatorId.value, catalogueRadiator.erpId);
      }
    });

    const grouping: RadiatorsGroupedBySize = {
      [RadiatorSize.STANDARD]: [],
      [RadiatorSize.LARGE]: [],
      [RadiatorSize.DESIGN]: [],
    };
    let count = 0;

    rooms.forEach((room) => {
      const newlyAddedEnabledRadiators = room.radiators.filter((r) => !r.isExisting && r.enabled);
      newlyAddedEnabledRadiators.forEach((radiator) => {
        // Bind ERP ID since it is not otherwise persisted
        const erpId = radiator.specificationReferenceId
          ? radiatorErpIds.get(radiator.specificationReferenceId)
          : undefined;

        grouping[getRadiatorSizeGroup(radiator)].push({ ...radiator, erpId });
        count += 1;
      });
    });
    return { newRadiators: grouping, numNewRadiators: count };
  }, [catalogueRadiators, rooms]);

  const minimumDhwTankSize = getHotWaterUsagePerDay({ numberOfResidents, numberOfBedrooms });

  const result =
    technicalSpecification !== undefined
      ? calculatePerformance(
          adjustedOutdoorDesignTemperature,
          flowTemperature,
          totalHeatLoss,
          technicalSpecification,
          compatibilityGroup,
        )
      : undefined;

  const bivalencePointMaxHeatOutput =
    result?.bivalencePoint !== undefined ? result.bivalencePoint.heatingOutput / 1000 : undefined;
  const maxPerformanceOdtHeatOutputWithoutElectricHeater =
    result?.maxHeatOutputWithoutElectricHeater !== undefined
      ? result.maxHeatOutputWithoutElectricHeater / 1000
      : undefined;
  const maxPerformanceOdtHeatOutputWithElectricHeater =
    result?.maxHeatOutputWithElectricHeater !== undefined ? result.maxHeatOutputWithElectricHeater / 1000 : undefined;

  const bivalencePointTotalHeatLoss =
    result?.bivalencePoint !== undefined
      ? getHouseHeatDesignResult({
          rooms,
          floors,
          projectUValues: uValuesStore.projectUValues,
          outdoorTemperature: result.bivalencePoint.temperature,
          constructionYear,
          countryCode,
          numberOfResidents: houseInputsStore.numberOfResidents,
          flowReturnDeltaT,
          flowTemperature,
          heatPumpPackages: selectedHeatPumpPackages,
          climateDataStore,
          ventilationDesign: houseInputsStore.ventilationDesign,
        }).totalHeatLoss / 1000
      : undefined;

  const { currentProgress } = useProgressStore();
  const solutionProgress = Object.values(currentProgress ?? {});
  const isProductLocked = solutionProgress.some((progress) => progress.progress === 'productsLockedAt');

  const notify = (message: MessageKey) => toast(formatMessage({ id: `${message}` }));

  const { mutate: updateStage, error: actionError } = api.AiraBackend.applyActionGrpc.useMutation({
    onSuccess: () => {
      invalidateSolution();
      toast.dismiss(loadingToast.current);
      notify('quotation.notify.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS' as MessageKey);
    },
    onError: () => {
      toast.dismiss(loadingToast.current);
      setErrorModalIsOpen(true);
    },
  });

  const unlockProducts = () => {
    loadingToast.current = toast.loading(
      formatMessage({ id: 'quotation.notify.loading.ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS' as MessageKey }),
    );
    updateStage({ solution: solutionId as string, action: 'ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS' });
  };
  const productSelectionUnitParameters = {
    indoorUnits: [
      {
        testId: 'product-selection-num-occupants',
        label: formatMessage({ id: 'heatDesign.productSelection.waterUsage.numOccupants' }),
        value: numberOfResidents,
        icon: <FamilyRestroomIcon />,
      },
      {
        testId: 'product-selection-num-bedrooms',
        label: formatMessage({ id: 'heatDesign.productSelection.waterUsage.numBedrooms' }),
        value: numberOfBedrooms,
        icon: <BedOutlinedIcon />,
      },
      {
        testId: 'product-selection-num-bathrooms',
        label: formatMessage({ id: 'heatDesign.productSelection.waterUsage.numBathrooms' }),
        value: numberOfBathrooms,
        icon: <BathtubOutlinedIcon />,
      },
      {
        testId: 'product-selection-domestic-hot-water-capacity',
        label: formatMessage({ id: 'heatDesign.productSelection.waterUsage.domesticHotWaterCapacity' }),
        value: minimumDhwTankSize,
        icon: <WaterDropIcon />,
        trailingContent: (
          <TooltipAira
            title={
              <StyledFormattedMessage id="heatDesign.productSelection.waterUsage.domesticHotWaterCapacity.tooltip" />
            }
          />
        ),
      },
    ],
    outdoorUnit: {
      houseHeating: [
        {
          testId: 'product-selection-outdoor-houseHeating-design-temp',
          label: formatMessage({ id: 'heatDesign.productSelection.heatDetails.outdoorDesignTemp' }),
          value: adjustedOutdoorDesignTemperature,
          icon: <ThermometerIcon />,
          trailingContent: (
            <TooltipAira
              title={<StyledFormattedMessage id="heatDesign.productSelection.heatDetails.outdoorDesignTemp.tooltip" />}
            />
          ),
        },
        {
          testId: 'product-selection-chosen-flow-temp',
          label: formatMessage({ id: 'heatDesign.productSelection.heatDetails.chosenFlowTemp' }),
          value: flowTemperature,
          icon: <Wave3HorizontalOutlinedIcon />,
        },
      ],
      performanceOdt: [
        {
          testId: 'product-selection-outdoor-unit-performance-odt',
          label: formatMessage({
            id: 'heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.temperature',
          }),
          value: adjustedOutdoorDesignTemperature,
          icon: <ThermometerOutlinedIcon />,
        },
        {
          testId: 'product-selection-outdoor-unit-performance-odt-heat-loss',
          label: formatMessage({ id: 'heatDesign.productSelection.heatDetails.calculatedTotalHeatLoss' }),
          value: toTwoDecimalPlaces(totalHeatLoss / 1000),
          icon: <HouseWave3Icon />,
        },
        {
          testId: 'product-selection-outdoor-unit-performance-odt-max-heat-output-without-electric',
          label: (
            <StyledFormattedMessage id="heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithoutElectric" />
          ),
          value: optionalTwoDecimalPlaces(maxPerformanceOdtHeatOutputWithoutElectricHeater),
          icon: <DeviceOutlinedIcon />,
          trailingContent: (
            <TooltipAira
              title={
                <StyledFormattedMessage id="heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.without.electric.tooltip" />
              }
            />
          ),
        },
        {
          testId: 'product-selection-outdoor-unit-performance-odt-max-heat-output-with-electric',
          label: (
            <StyledFormattedMessage id="heatDesign.productSelection.outdoorUnitDetails.maxHeatOutputWithElectric" />
          ),
          value: optionalTwoDecimalPlaces(maxPerformanceOdtHeatOutputWithElectricHeater),
          hide: !maxPerformanceOdtHeatOutputWithElectricHeater,
          icon: <DeviceFilledIcon />,
          trailingContent: (
            <TooltipAira
              title={
                <StyledFormattedMessage id="heatDesign.productSelection.heatDetails.outdoorUnit.performanceOdt.heatOutput.with.electric.tooltip" />
              }
            />
          ),
        },
      ],
      bivalencePoint: [
        {
          testId: 'product-selection-outdoor-unit-bivalence-point-temp',
          label: formatMessage({ id: 'heatDesign.productSelection.outdoorUnitDetails.bivalencePoint' }),
          value: optionalTwoDecimalPlaces(result?.bivalencePoint?.temperature),
          icon: <ThermometerOutlinedIcon />,
        },
        {
          testId: 'product-selection-outdoor-unit-bivalence-point-heat-loss',
          label: formatMessage({ id: 'heatDesign.productSelection.heatDetails.calculatedTotalHeatLoss' }),
          value: optionalTwoDecimalPlaces(bivalencePointTotalHeatLoss),
          icon: <HouseWave3Icon />,
        },
        {
          testId: 'product-selection-outdoor-unit-bivalence-point-max-heat-output',
          label: <StyledFormattedMessage id="heatDesign.productSelection.outdoorUnitDetails.maxHeatOutput" />,
          value: optionalTwoDecimalPlaces(bivalencePointMaxHeatOutput),
          icon: <DeviceOutlinedIcon />,
          trailingContent: (
            <TooltipAira
              title={
                <StyledFormattedMessage id="heatDesign.productSelection.heatDetails.outdoorUnit.bivalencePoint.heatOutput.without.electric.tooltip" />
              }
            />
          ),
        },
      ],
    },
  };
  return (
    <>
      <Stack direction="row" maxWidth={1400} width="100%" spacing={GRID_GAPS}>
        <HeatDesignCard
          sx={{
            minHeight: '70vh',
            flex: 1,
            position: 'relative',
            display: 'flex',
            inset: 0,
            alignItems: 'normal',
            width: '40%',
          }}
        >
          <Stack spacing={ROW_SPACING} width="100%">
            <Stack borderBottom={1} pb={2}>
              <Heading variant="headline3" level={2}>
                <FormattedMessage id="heatDesign.title.productSelection" />
              </Heading>
            </Stack>
            <Stack sx={{ width: '100%' }} spacing={ROW_SPACING} pt={1}>
              {isProductLocked && (
                <Stack
                  direction="row"
                  borderRadius={1}
                  bgcolor={grey[200]}
                  gap={1}
                  justifyContent="space-between"
                  alignItems="center"
                  padding={1}
                >
                  <FormattedMessage id="heatDesign.productSelection.heatPumpPackageSelection.productsLocked" />
                  <Button
                    startIcon={<LockOpenIcon />}
                    onClick={unlockProducts}
                    label={formatMessage({ id: 'common.unlock' })}
                  />
                </Stack>
              )}
              <HeatPumpPackageInformation disabled={isProductLocked} showPrices={false} />
            </Stack>
          </Stack>
        </HeatDesignCard>
        <Stack spacing={GRID_GAPS} sx={{ width: '60%' }}>
          <Stack spacing={GRID_GAPS} direction="row" alignItems="flex-start">
            <UnitDetailSection
              sectionHeader={formatMessage({ id: 'heatDesign.productSelection.outdoorUnitDetails.title' })}
            >
              {singleSelectedOutdoorUnit !== undefined ? (
                <Stack spacing={ROW_SPACING}>
                  {result === undefined ? (
                    <FormattedMessage
                      id="heatDesign.productSelection.noPerformanceData"
                      values={{ sku: singleSelectedOutdoorUnit.sku }}
                    />
                  ) : (
                    <Stack gap={GRID_GAPS} flexWrap="wrap" direction="row">
                      <InfoRowsSection
                        header={formatMessage({
                          id: 'heatDesign.productSelection.outdoorUnitDetails.houseHeatingSection.title',
                        })}
                        units={productSelectionUnitParameters.outdoorUnit.houseHeating}
                      />
                      <FlexItem>
                        <DetailsCard
                          title={`${formatMessage({ id: 'heatDesign.productSelection.outdoorUnitDetails.scop' })} ${optionalTwoDecimalPlaces(result.scop)}`}
                          label={
                            <StyledFormattedMessage id="heatDesign.productSelection.outdoorUnitDetails.scop.description" />
                          }
                          testId="scope"
                          icon={<ChartLineUpIcon />}
                        />
                      </FlexItem>
                      <InfoRowsSection
                        header={formatMessage({
                          id: 'heatDesign.productSelection.outdoorUnitDetails.performanceODTSection.title',
                        })}
                        units={productSelectionUnitParameters.outdoorUnit.performanceOdt}
                      />
                      <InfoRowsSection
                        header={formatMessage({
                          id: 'heatDesign.productSelection.outdoorUnitDetails.bivalenceSection.title',
                        })}
                        units={productSelectionUnitParameters.outdoorUnit.bivalencePoint}
                      />
                    </Stack>
                  )}
                </Stack>
              ) : (
                <div>{outdoorUnits.length > 1 ? 'Multiple units selected' : 'No outdoor unit selected'}</div>
              )}
            </UnitDetailSection>
          </Stack>
          <UnitDetailSection
            sectionHeader={formatMessage({ id: 'heatDesign.productSelection.indoorUnitDetails.title' })}
          >
            <InfoRowsSection
              header={formatMessage({
                id: 'heatDesign.productSelection.indoorUnitDetails.houseWaterSection.title',
              })}
              units={productSelectionUnitParameters.indoorUnits}
            />
            {countryCode === CountryCode.GB && (
              <FlexItem>
                {getCylinderReheatTime(formatMessage, indoorUnits, outdoorUnits, waterReheatTimeMinutes)}
              </FlexItem>
            )}
          </UnitDetailSection>
          <HeatDesignCard>
            <Stack direction="row" alignItems="center" mb={1}>
              <Heading variant="headline3" level={2} pr={2}>
                <FormattedMessage id="heatDesign.customerReport.newRadiators.title" />
              </Heading>
              <TooltipAira title={<FormattedMessage id="heatDesign.productSelection.radiators.tooltip" />} />
            </Stack>
            <Typography component="p" sx={{ mb: 4 }}>
              <StyledFormattedMessage id="heatDesign.customerReport.newRadiators.text" />
            </Typography>
            {numNewRadiators > 0 ? (
              <RadiatorList groupedRadiators={newRadiators} />
            ) : (
              <Box sx={{ background: beige[150], padding: '32px 16px', textAlign: 'center', borderRadius: 1 }}>
                <Typography>
                  {formatMessage({
                    id: 'heatDesign.productSelection.noNewRadiators',
                  })}
                </Typography>
              </Box>
            )}
          </HeatDesignCard>
        </Stack>
      </Stack>
      <ErrorModal
        isOpen={errorModalIsOpen}
        onClose={() => setErrorModalIsOpen(false)}
        heading={<FormattedMessage id="heatDesign.productSelection.unlockProducts.error.title" />}
      >
        <StyledFormattedMessage
          id="heatDesign.productSelection.unlockProducts.error.body"
          values={{
            error: actionError?.message,
          }}
        />
      </ErrorModal>
    </>
  );
}
