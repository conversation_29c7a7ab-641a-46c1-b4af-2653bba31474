import { HeatPumpOutdoorUnitTechnicalSpecification_ScopClimateZone as ScopClimateZone } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { TestTechSpecs } from 'tests/heat-loss/fixtures/techSpecData';
import { calculateBivalencePoint, calculateMaxHeatOutput, calculateScop } from './heatPumpPerformanceCharacteristics';

describe('heat pump max heat output calculations', () => {
  const technicalSpecification = TestTechSpecs.VAILLANT_3_5_KW;

  it('handles -5 odt, 35 flow', () => {
    const result = calculateMaxHeatOutput({ odt: -5, flowTemp: 35, technicalSpecification });
    expect(result).toBeCloseTo(4200);
  });

  it('handles -4 odt, 35 flow', () => {
    const result = calculateMaxHeatOutput({ odt: -4, flowTemp: 35, technicalSpecification });
    expect(result).toBeCloseTo(4400);
  });

  it('handles -5 odt, 40 flow', () => {
    const result = calculateMaxHeatOutput({ odt: -5, flowTemp: 40, technicalSpecification });
    expect(result).toBeCloseTo(4100);
  });

  it('handles temperatures higher than the curve points', () => {
    const result = calculateMaxHeatOutput({ odt: 3, flowTemp: 35, technicalSpecification });
    expect(result).toBeCloseTo(5000);
  });

  it('flattens out after 10 degrees', () => {
    const resultTen = calculateMaxHeatOutput({ odt: 10, flowTemp: 35, technicalSpecification });
    expect(resultTen).toBeCloseTo(5700);

    const resultTwelve = calculateMaxHeatOutput({ odt: 12, flowTemp: 35, technicalSpecification });
    expect(resultTwelve).toBeCloseTo(5700);
  });

  it('handles temperatures lower than the curve points', () => {
    const result = calculateMaxHeatOutput({ odt: -6, flowTemp: 35, technicalSpecification });
    expect(result).toBeCloseTo(4000);
  });

  it('flattens out below -25 degrees', () => {
    const resultMinusTwentyFive = calculateMaxHeatOutput({ odt: -25, flowTemp: 35, technicalSpecification });
    expect(resultMinusTwentyFive).toBeCloseTo(200);

    const resultMinusThirty = calculateMaxHeatOutput({ odt: -35, flowTemp: 35, technicalSpecification });
    expect(resultMinusThirty).toBeCloseTo(200);
  });

  it('does not give negative output', () => {
    // not sure if this would ever happen with real data, but it would be silly if it did, so constructing a situation
    // here where we'd get negative output if we didn't handle it
    const result = calculateMaxHeatOutput({
      odt: 0,
      flowTemp: 35,
      technicalSpecification: {
        performanceAtFlowTemperature: [
          {
            flowTemperature: 35,
            scopForClimateZone: [
              {
                climateZone: ScopClimateZone.SCOP_CLIMATE_ZONE_AVERAGE,
                scop: 4,
              },
            ],
            outputAtOutdoorTemperature: [
              { outdoorTemperatureCelcius: 1, outputWatt: 0 },
              { outdoorTemperatureCelcius: 2, outputWatt: 1 },
            ],
          },
          {
            flowTemperature: 45,
            scopForClimateZone: [
              {
                climateZone: ScopClimateZone.SCOP_CLIMATE_ZONE_AVERAGE,
                scop: 4,
              },
            ],
            outputAtOutdoorTemperature: [
              { outdoorTemperatureCelcius: 1, outputWatt: 0 },
              { outdoorTemperatureCelcius: 2, outputWatt: 1 },
            ],
          },
        ],
      },
    });
    expect(result).toBeCloseTo(0);
  });

  it('picks the closest flow rate for heat output', () => {
    const result = calculateMaxHeatOutput({ odt: -5, flowTemp: 42, technicalSpecification });
    expect(result).toBeCloseTo(4100);
  });
});

describe('heat pump scop calculations', () => {
  const technicalSpecification = TestTechSpecs.VAILLANT_3_5_KW;

  it('flattens scop before the first point', () => {
    const result = calculateScop({ flowTemp: 2, technicalSpecification });
    expect(result).toBeCloseTo(4.41);
  });

  it('handles lowest flow temp', () => {
    const scop = calculateScop({ flowTemp: 35, technicalSpecification });
    expect(scop).toBeCloseTo(4.41);
  });

  it('interpolates scop by flow rate', () => {
    const result = calculateScop({ flowTemp: 37, technicalSpecification });
    expect(result).toBeCloseTo(4.258);
  });

  it('handles highest flow temp', () => {
    const result = calculateScop({ flowTemp: 55, technicalSpecification });
    expect(result).toBeCloseTo(3.1);
  });

  it('interpolates scop by flow rate after the last point', () => {
    const result = calculateScop({ flowTemp: 57, technicalSpecification });
    expect(result).toBeCloseTo(2.992);
  });
});

describe('heat pump bivalence point calculations', () => {
  it('calculates the bivalence point correctly for vaillant 12 kW, flowTemp: 55, heat loss: 13 kW and odt: -11.7', () => {
    const technicalSpecification = TestTechSpecs.VAILLANT_12_KW;
    const result = calculateBivalencePoint({
      technicalSpecification,
      flowTemp: 55,
      heatLoss: 13000,
      odt: -11.7,
    });
    expect(result?.temperature).toBeCloseTo(-6.55);
    expect(result?.heatingOutput).toBeCloseTo(10490.79); // 10.49 KW
  });

  it('calculates the bivalence point correctly for vaillant 10 kW, flowTemp: 55, heat loss: 13 kW and odt: -11.7', () => {
    const technicalSpecification = TestTechSpecs.VAILLANT_10_KW;
    const result = calculateBivalencePoint({
      technicalSpecification,
      flowTemp: 55,
      heatLoss: 13000,
      odt: -11.7,
    });
    expect(result?.temperature).toBeCloseTo(-3.63);
    expect(result?.heatingOutput).toBeCloseTo(9073.05); // 9 KW
  });

  it('calculates the bivalence point correctly for vaillant 10 kW, flowTemp: 45, heat loss: 13 kW and odt: -11.7', () => {
    const technicalSpecification = TestTechSpecs.VAILLANT_10_KW;
    const result = calculateBivalencePoint({
      technicalSpecification,
      flowTemp: 45,
      heatLoss: 13000,
      odt: -11.7,
    });
    expect(result?.temperature).toBeCloseTo(-4.57);
    expect(result?.heatingOutput).toBeCloseTo(9528.84); // 9.53 KW
  });

  it('calculates the bivalence point correctly for vaillant 12 kW, flowTemp: 55, heat loss: 13 kW and odt: -14.6', () => {
    const technicalSpecification = TestTechSpecs.VAILLANT_12_KW;
    const result = calculateBivalencePoint({
      technicalSpecification,
      flowTemp: 55,
      heatLoss: 13000,
      odt: -14.6,
    });
    expect(result?.temperature).toBeCloseTo(-8.15);
    expect(result?.heatingOutput).toBeCloseTo(10169.15); // 10.17 KW
  });

  it('calculates the bivalence point correctly for aira 12 kW, flowTemp: 55, heat loss: 13 kW and odt: -14.6', () => {
    const technicalSpecification = TestTechSpecs.AIRA_12_KW;
    const result = calculateBivalencePoint({
      technicalSpecification,
      flowTemp: 55,
      heatLoss: 13000,
      odt: -14.6,
    });
    expect(result?.temperature).toBeCloseTo(-7.34);
    expect(result?.heatingOutput).toBeCloseTo(9813.2); // 9.81 KW
  });
});
