import { v4 as uuidv4 } from 'uuid';
import {
  AboveCeilingType,
  BelowFloorType as ProtoBelowFloorType,
  DataSourceReference,
  Door as ProtoDoor,
  Floor,
  HeatDesign,
  ImageMap as ProtoImageMap,
  Radiator as ProtoRadiator,
  RoofGlazing as ProtoRoofGlazing,
  Room as ProtoRoom,
  SystemDesign,
  UnderfloorHeating as ProtoUnderfloorHeating,
  UValue as ProtoUValue,
  UValueDefaultEntry,
  Wall as ProtoWall,
  WallType as ProtoWallType,
  Window as ProtoWindow,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { CountryCode } from 'utils/marketConfigurations';
import { HeatSourceStore, WaterTemps } from '../stores/HeatSourceStore';
import { UValuesStore } from '../stores/UValuesStore';
import { RoomsStore } from '../stores/RoomsStore';
import { FloorsStore } from '../stores/FloorsStore';
import {
  AdjacentKind,
  AutomaticUnderfloorHeatingOutput,
  BelowFloorType,
  CustomUnderfloorHeatingOutput,
  Door,
  FABRIC_TYPES,
  FabricTypes,
  FloorProps,
  ImageMap,
  OutputType,
  PROJECT_FABRIC_TYPES,
  ProjectFallbackUValues,
  RadiatorData,
  RadiatorMode,
  RoofGlazing,
  Room,
  SpaceAboveRoofOrCeiling,
  SystemType,
  UnderfloorHeatingOutput,
  UValues,
  Wall,
  Window,
} from '../stores/types';
import { calculateVolume } from './calculations';
import { getDesignRoomTemp } from './heatCalculations';
import { mapEmitterDetails } from './radiatorHelpers';
import {
  mapAboveCeilingTypeFromProtobuf,
  mapBelowFloorFromProtobuf,
  mapFabricTypeFromProtobuf,
  mapOpenFlueTypeFromProtobuf,
  mapRoomTypeFromProtobuf,
} from './protobufEnumMapping';
import { UValue } from '../models/UValue';
import { getUValuesForCountryCode, getWorstUValueForBuildingConstructionYear } from '../uValues';
import { UNDERFLOOR_HEATING_MEAN_DELTA_T } from '../constants';
import { ClimateDataStore } from '../stores/ClimateDataStore';
import { marketConfiguration } from './marketConfigurations';

export function mapImageMapFromProtobuf(protoImageMap: ProtoImageMap | undefined): ImageMap {
  return {
    coordinates: (protoImageMap?.points || []).flatMap((point) => [point.x, point.y]),
  };
}

function mapWallFromProtobuf(protoWall: ProtoWall, uValue?: UValue): Wall {
  let surfaceType: Wall['surfaceType'];
  switch (protoWall.type) {
    case ProtoWallType.WALL_TYPE_EXTERNAL:
      surfaceType = 'externalWalls';
      break;
    case ProtoWallType.WALL_TYPE_PARTY:
      surfaceType = 'partyWalls';
      break;
    case ProtoWallType.WALL_TYPE_INTERNAL:
      surfaceType = 'internalWalls';
      break;
    default:
      throw new Error(`Unrecognized wall type: ${protoWall.type}`);
  }

  if (surfaceType === 'internalWalls') {
    return {
      uid: protoWall.id?.value ?? 'unknown',
      length: protoWall.widthM,
      imageMap: mapImageMapFromProtobuf(protoWall.imageMap),
      surfaceType,
      uValue,
      adjoiningRoomUID: protoWall.adjoiningRoomId?.value,
    };
  }

  if (surfaceType === 'externalWalls') {
    return {
      uid: protoWall.id?.value ?? 'unknown',
      length: protoWall.widthM,
      imageMap: mapImageMapFromProtobuf(protoWall.imageMap),
      surfaceType,
      uValue,
      soilPercentage: protoWall.soilPercentage ?? null,
    };
  }

  return {
    uid: protoWall.id?.value ?? 'unknown',
    length: protoWall.widthM,
    imageMap: mapImageMapFromProtobuf(protoWall.imageMap),
    surfaceType,
    uValue,
  };
}

function mapDoorFromProtobuf(protoDoor: ProtoDoor, protoWall: ProtoWall, uValue?: UValue): Door {
  return {
    surfaceType: 'doors',
    uid: protoDoor.id!.value,
    width: protoDoor.widthM,
    height: protoDoor.heightM,
    wallUID: protoWall.id!.value,
    area: protoDoor.widthM * protoDoor.heightM,
    imageMap: mapImageMapFromProtobuf(protoDoor.imageMap),
    uValue,
  };
}

function mapWindowFromProtobuf(protoDoor: ProtoWindow, protoWall: ProtoWall, uValue?: UValue): Window {
  return {
    surfaceType: 'windows',
    uid: protoDoor.id!.value,
    width: protoDoor.widthM,
    height: protoDoor.heightM,
    wallUID: protoWall.id!.value,
    area: protoDoor.widthM * protoDoor.heightM,
    imageMap: mapImageMapFromProtobuf(protoDoor.imageMap),
    uValue,
  };
}

function mapRoofGlazingFromProtobuf(protoRoofGlazing: ProtoRoofGlazing, uValue?: UValue): RoofGlazing {
  return {
    surfaceType: 'roofGlazings',
    uid: protoRoofGlazing.id!.value,
    width: protoRoofGlazing.widthM,
    length: protoRoofGlazing.heightM,
    uValue,
  };
}

function mapRadiatorFromProtobuf(protoRadiator: ProtoRadiator, protoRoom: ProtoRoom): RadiatorData {
  if (protoRadiator.id == null) {
    throw new Error('Radiator ID is missing');
  }
  if (protoRoom.id == null) {
    throw new Error('Room ID is missing');
  }
  return {
    uid: protoRadiator.id.value,
    area: protoRadiator.widthM * protoRadiator.heightM,
    floorImageMap: mapImageMapFromProtobuf(protoRadiator.floorImageMap),
    roomImageMap: mapImageMapFromProtobuf(protoRadiator.imageMap),
    info: {},
    width: protoRadiator.widthM * 1000,
    height: protoRadiator.heightM * 1000,
    isExisting: !protoRadiator.toBeInstalled, // TODO [model sync] why not keep toBeInstalled?
    replacedBy: protoRadiator.replacedBy?.value,
    enabled: protoRadiator.enabled,
    typeOfHeatEmitter: RadiatorMode.Panel,
    radiatorDetails: mapEmitterDetails(protoRadiator.radiatorDetails),
    comment: protoRadiator.comment,
    roomId: protoRoom.id.value,
    specificationReferenceId: protoRadiator.specificationReferenceId?.value,
  };
}

function getSystemTypeFromProtobuf(underfloorHeating: ProtoUnderfloorHeating): SystemType | undefined {
  switch (underfloorHeating.emitterDetails?.details?.$case) {
    case 'waterRadiatorDetails':
      return SystemType.WATER;
    case 'electricRadiatorDetails':
      return SystemType.ELECTRIC;
    default:
      return undefined;
  }
}

function mapUnderfloorHeatingFromProtobuf(
  underfloorHeating: ProtoUnderfloorHeating | undefined,
): UnderfloorHeatingOutput | undefined {
  if (underfloorHeating == null) return undefined;

  const systemType = getSystemTypeFromProtobuf(underfloorHeating);

  if (underfloorHeating.matchRoomHeatLoss) {
    return {
      outputType: OutputType.AUTOMATIC,
      systemType,
    } satisfies AutomaticUnderfloorHeatingOutput;
  }

  const emitterDetails = underfloorHeating.emitterDetails
    ? mapEmitterDetails(underfloorHeating.emitterDetails)
    : {
        nominalOutput: {
          deltaT: UNDERFLOOR_HEATING_MEAN_DELTA_T,
          outputWatt: underfloorHeating.outputWatt,
        },
        outputWatt: underfloorHeating.outputWatt,
      };

  return {
    outputType: OutputType.CUSTOM,
    ...emitterDetails,
  } satisfies CustomUnderfloorHeatingOutput;
}

export function findUrlFor3DModel(references: DataSourceReference[]): string {
  return references.find((reference) => reference.type === 'magicplan.3dmodel.embed.url')?.reference ?? '';
}

export function findMagicplanAddress(references: DataSourceReference[]): string {
  return references.find((reference) => reference.type === 'magicplan.plan.address')?.reference ?? '';
}

function extractCustomUValues(heatDesign: HeatDesign): UValues {
  return heatDesign.customUValues.reduce((acc, entry) => {
    const fabricType = mapFabricTypeFromProtobuf(entry.fabricType);
    const { uValue } = entry;
    const uValueId = uValue?.id?.value;
    const uValueName = uValue?.name;
    const uValueSi = uValue?.uValueSi;

    if (uValueSi && fabricType && uValueId && uValueName) {
      const previousFabricType = acc[fabricType] ?? [];
      return {
        ...acc,
        [fabricType]: [...previousFabricType, new UValue(uValueName, uValueSi, uValueId, { isCustom: true })],
      };
    }
    return acc;
  }, {} as UValues);
}

function extractFallbackUValuesFromProtobuf<T>(defaultEntries: UValueDefaultEntry[], uValues: UValues): T {
  return defaultEntries.reduce((acc, entry) => {
    const fabricType = mapFabricTypeFromProtobuf(entry.fabricType);
    const { uValue } = entry;
    const uValueId = uValue?.id?.value;

    if (fabricType && uValueId) {
      // Find uValue in pre-defined or custom U-values
      const uValueLookup = uValues[fabricType].find((u) => u.id === uValueId);
      return {
        ...acc,
        [fabricType]: uValueLookup,
      };
    }
    return acc;
  }, {} as T);
}

/**
 * Adds the given uValue to the given uValues object based on the uValue's fabricType,
 * but only if the uValue does not already exist for that fabric type.
 */
function addUValue(uValues: UValues, fabricType: FabricTypes, uValue: UValue): UValues {
  if (
    uValues[fabricType] !== undefined &&
    uValues[fabricType].find((existingUValue) => existingUValue.id === uValue.id)
  ) {
    return uValues;
  }

  return {
    ...uValues,
    [fabricType]: [...(uValues[fabricType] ?? []), uValue],
  };
}

function maybeGetDeletedUValue({
  fabricType,
  protoUValue,
  customUValues,
  uValuesForCountry,
}: {
  fabricType?: FabricTypes;
  protoUValue?: ProtoUValue;
  customUValues: UValues;
  uValuesForCountry: UValues;
}): UValue | null {
  const uValueID = protoUValue?.id?.value;
  const uValueName = protoUValue?.name;
  const uValueValue = protoUValue?.uValueSi;

  if (
    fabricType !== undefined &&
    uValueID !== undefined &&
    uValueName !== undefined &&
    uValueValue !== undefined &&
    (customUValues[fabricType] ?? []).find((uv) => uv.id === uValueID) === undefined &&
    uValuesForCountry[fabricType].find((uv) => uv.id === uValueID) === undefined
  ) {
    return new UValue(uValueName, uValueValue, uValueID, { isDeprecated: true });
  }

  return null;
}

export function determineDeletedUValues(
  customUValues: UValues,
  uValuesForCountry: UValues,
  heatDesign: HeatDesign,
): UValues {
  const dwellingDefaultUValues = heatDesign.dwellingUValueDefaults?.defaultEntries ?? [];
  const floorDefaultUValues =
    heatDesign.dwelling?.floors.flatMap((floor) => floor.floorUValueDefaults?.defaultEntries ?? []) ?? [];

  return [...dwellingDefaultUValues, ...floorDefaultUValues].reduce((acc, entry) => {
    const fabricType = mapFabricTypeFromProtobuf(entry.fabricType);
    const { uValue: protoUValue } = entry;

    // If the project default uValue for a given fabric is not in the
    // country-specific or custom uValue list, then it has been deleted. Here,
    // we manually add it as a deprecated option.
    //
    // Going forward, we want to avoid deleting U-values because we lose date
    // information when doing. Instead of deleting them from the code, we will
    // mark them as deprecated.

    const maybeDeletedUValue = maybeGetDeletedUValue({
      fabricType,
      protoUValue,
      customUValues,
      uValuesForCountry,
    });

    if (maybeDeletedUValue && fabricType) {
      let workingAccumulator = acc;

      // There are certain fabric types that are at the project level and others that
      // are at the room level. We need to ensure that if, for example, a u-value is
      // deleted from the 'roof' fabric type (which is only at the project level), then
      // this u-value should also be marked as deleted from the 'roofsOrCeilings' fabric
      // type (which is only at the room level) and vice-versa...

      // First, we make sure the deleted ProjectFabrics update the RoomFabrics
      if (fabricType === 'foundation') {
        workingAccumulator = addUValue(workingAccumulator, 'floors', maybeDeletedUValue);
      } else if (fabricType === 'intermediateFloors') {
        workingAccumulator = addUValue(workingAccumulator, 'floors', maybeDeletedUValue);
        workingAccumulator = addUValue(workingAccumulator, 'roofsOrCeilings', maybeDeletedUValue);
      } else if (fabricType === 'roof') {
        workingAccumulator = addUValue(workingAccumulator, 'roofsOrCeilings', maybeDeletedUValue);

        // Then, we make sure the deleted RoomFabrics update the ProjectFabrics
      } else if (fabricType === 'floors') {
        workingAccumulator = addUValue(workingAccumulator, 'foundation', maybeDeletedUValue);
        workingAccumulator = addUValue(workingAccumulator, 'intermediateFloors', maybeDeletedUValue);
      } else if (fabricType === 'roofsOrCeilings') {
        workingAccumulator = addUValue(workingAccumulator, 'roof', maybeDeletedUValue);
      }

      return addUValue(workingAccumulator, fabricType, maybeDeletedUValue);
    }

    return acc;
  }, {} as UValues);
}

/**
 * We want a list of the U-values that are deprecated and present in the
 * project, so that we can show them in the dropdowns.
 *
 * How to do this:
 * 1. store all the deprecated U-values in a map
 * 2. create a new map to store the deprecated U-values that are used in the
 *    project
 * 3. go through every surface in the project and check whether a deprecated
 *    U-value is used.
 */
export function getDeprecatedUValuesUsedInSolution(uValuesForCountry: UValues, heatDesign: HeatDesign): UValues {
  // 1. store all the deprecated U-values in a map
  const deprecatedUValuesPerFabric: { [key in FabricTypes]: Map<string, UValue> } = {
    doors: new Map(),
    externalWalls: new Map(),
    floors: new Map(),
    foundation: new Map(),
    intermediateFloors: new Map(),
    internalWalls: new Map(),
    partyWalls: new Map(),
    roof: new Map(),
    roofGlazings: new Map(),
    roofsOrCeilings: new Map(),
    windows: new Map(),
  };

  FABRIC_TYPES.forEach((fabricType) => {
    uValuesForCountry[fabricType]
      .filter((uValue) => uValue.metadata.isDeprecated)
      .forEach((uValue) => {
        deprecatedUValuesPerFabric[fabricType].set(uValue.id, uValue);
      });
  });

  // 2. create a new map to store the deprecated U-values that are used in the
  //    project
  const deprecatedUValuesUsedInSolution: { [key in FabricTypes]: Map<string, UValue> } = {
    doors: new Map(),
    externalWalls: new Map(),
    floors: new Map(),
    foundation: new Map(),
    intermediateFloors: new Map(),
    internalWalls: new Map(),
    partyWalls: new Map(),
    roof: new Map(),
    roofGlazings: new Map(),
    roofsOrCeilings: new Map(),
    windows: new Map(),
  };

  // 3. go through every surface in the project and check whether a deprecated
  //    U-value is used.
  function maybeAddToUValues(protoUValue?: ProtoUValue, fabricType?: FabricTypes) {
    if (protoUValue?.id?.value && fabricType) {
      const uValueId = protoUValue.id.value;
      const matchingUValue = deprecatedUValuesPerFabric[fabricType].get(uValueId);
      if (matchingUValue) {
        deprecatedUValuesUsedInSolution[fabricType].set(uValueId, matchingUValue);
      }
    }
  }

  heatDesign.dwellingUValueDefaults?.defaultEntries?.forEach((entry) => {
    const fabricType = mapFabricTypeFromProtobuf(entry.fabricType);
    maybeAddToUValues(entry.uValue, fabricType);
  });

  heatDesign.dwelling?.floors.forEach((floor) => {
    floor.floorUValueDefaults?.defaultEntries.forEach((entry) => {
      const fabricType = mapFabricTypeFromProtobuf(entry.fabricType);
      maybeAddToUValues(entry.uValue, fabricType);
    });
  });

  heatDesign.dwelling?.floors.forEach((floor) => {
    floor.rooms.forEach((room) => {
      maybeAddToUValues(room.ceilingUValue, 'roofsOrCeilings');
      maybeAddToUValues(room.floorSurfaceUValue, 'floors');

      // Add all the walls
      room.externalWalls?.forEach((wall) => maybeAddToUValues(wall.uValue, 'externalWalls'));
      room.internalWalls?.forEach((wall) => maybeAddToUValues(wall.uValue, 'internalWalls'));
      room.partyWalls?.forEach((wall) => maybeAddToUValues(wall.uValue, 'partyWalls'));

      // Add all the windows
      [
        ...room.externalWalls.map((w) => w.windows),
        ...room.internalWalls.map((w) => w.windows),
        ...room.partyWalls.map((w) => w.windows),
      ]
        .flat()
        .forEach((window) => maybeAddToUValues(window.uValue, 'windows'));

      // Add all the doors
      [
        ...room.externalWalls.map((w) => w.doors),
        ...room.internalWalls.map((w) => w.doors),
        ...room.partyWalls.map((w) => w.doors),
      ]
        .flat()
        .forEach((door) => maybeAddToUValues(door.uValue, 'doors'));

      // Add all the glazings
      room.roofGlazings.forEach((glazing) => maybeAddToUValues(glazing.uValue, 'roofGlazings'));
    });
  });

  return {
    doors: [...deprecatedUValuesUsedInSolution.doors.values()],
    externalWalls: [...deprecatedUValuesUsedInSolution.externalWalls.values()],
    floors: [...deprecatedUValuesUsedInSolution.floors.values()],
    foundation: [...deprecatedUValuesUsedInSolution.foundation.values()],
    intermediateFloors: [...deprecatedUValuesUsedInSolution.intermediateFloors.values()],
    internalWalls: [...deprecatedUValuesUsedInSolution.internalWalls.values()],
    partyWalls: [...deprecatedUValuesUsedInSolution.partyWalls.values()],
    roof: [...deprecatedUValuesUsedInSolution.roof.values()],
    roofGlazings: [...deprecatedUValuesUsedInSolution.roofGlazings.values()],
    roofsOrCeilings: [...deprecatedUValuesUsedInSolution.roofsOrCeilings.values()],
    windows: [...deprecatedUValuesUsedInSolution.windows.values()],
  };
}

function getSoilPercentageDefaultValue(floor: Floor): number | null {
  if (typeof floor.soilPercentageDefault === 'number') {
    return floor.soilPercentageDefault;
  }

  // If a floor is below the ground, we want to force the user to enter a value,
  // so we return null to make the validations fail.
  if (floor.floorLevel < 0) {
    return null;
  }

  return 0;
}

function mapFloorFromProtobuf(protoFloor: Floor, floorUValues: UValues): FloorProps {
  const floorData: FloorProps = {
    floorName: protoFloor.name,
    floorUValues: extractFallbackUValuesFromProtobuf(
      protoFloor.floorUValueDefaults?.defaultEntries ?? [],
      floorUValues,
    ),
    uid: protoFloor.id!.value,
    floorNr: protoFloor.floorLevel,
    // belowFloor: 'solid', // TODO
    // aboveCeiling: 'heated', // TODO
    imageSvgData: protoFloor.svgImageData,
    simplifiedImageSvgData: protoFloor.simplifiedSvgImageData,
    soilPercentageDefault: getSoilPercentageDefaultValue(protoFloor),
  };

  return floorData;
}

const getBelowFloorType = ({
  belowFloor,
  floorLevel: floorNumber,
  floors,
}: {
  belowFloor: ProtoBelowFloorType;
  floorLevel: number;
  floors: Floor[];
}): BelowFloorType => {
  const belowFloorType = mapBelowFloorFromProtobuf(belowFloor);
  if (belowFloorType) return belowFloorType;

  const lowestFloorLevel = Math.min(...floors.map((f) => f.floorLevel));
  if (floorNumber === lowestFloorLevel) return AdjacentKind.SolidFloor;

  return AdjacentKind.Heated;
};

export const getAboveCeilingType = ({
  aboveCeiling,
  floorLevel,
  floors,
  tempOfSpaceAbove,
}: {
  aboveCeiling: AboveCeilingType;
  floorLevel: number;
  floors: Floor[];
  tempOfSpaceAbove: number;
}): SpaceAboveRoofOrCeiling => {
  const aboveCeilingType = mapAboveCeilingTypeFromProtobuf(aboveCeiling);
  if (aboveCeilingType) {
    if (aboveCeilingType === AdjacentKind.Heated) return { type: aboveCeilingType, tempOfSpaceAbove };

    return { type: aboveCeilingType };
  }

  const highestFloorNr = Math.max(...floors.map((f) => f.floorLevel));

  // Default to "outside" for the ceilings of top floors
  if (floorLevel === highestFloorNr) return { type: AdjacentKind.Outside };

  return { type: AdjacentKind.Heated, tempOfSpaceAbove };
};

const getWaterTemperatures = ({ systemDesign }: { systemDesign: SystemDesign }) =>
  ({
    // TODO [model sync] why not call these the same thing?
    flowTemp: systemDesign.flowTemperatureCelsius,
    flowReturnDeltaT: systemDesign.flowReturnDeltaT,
  }) satisfies WaterTemps;

export const getDefaultWaterTemperatures = ({ countryCode }: { countryCode: CountryCode }): WaterTemps =>
  marketConfiguration[countryCode].defaultWaterTemperatures;

// Find the worst temperature for each floor
function getLowestRoomTempOnFloor(
  floor: Floor,
  constructionYear: number,
  countryCode: CountryCode,
  climateDataStore: ClimateDataStore,
): number | undefined {
  let lowestTempForFloor: number | undefined;
  floor.rooms.forEach((room) => {
    const roomTemp = getDesignRoomTemp(
      {
        isHeated: room.heated,
        roomType: mapRoomTypeFromProtobuf(room.roomType),
        designRoomTempOverride: room.designRoomTemperatureCelsius,
      },
      constructionYear,
      countryCode,
      climateDataStore,
    );

    if (lowestTempForFloor === undefined || roomTemp < lowestTempForFloor) {
      lowestTempForFloor = roomTemp;
    }
  });

  return lowestTempForFloor;
}

export function getUValuesForSolution(heatDesign: HeatDesign, uValuesForCountry: UValues) {
  const customUValues = extractCustomUValues(heatDesign);

  // An array of U-values that were deleted from the code (before we decided to
  // stop deleting U-values and mark them as deprecated instead)
  const deletedUValues = determineDeletedUValues(customUValues, uValuesForCountry, heatDesign);

  // An array of U-values that are deprecated but still used somewhere in the
  // solution
  const deprecatedUValues = getDeprecatedUValuesUsedInSolution(uValuesForCountry, heatDesign);

  const mergedUValues = FABRIC_TYPES.reduce((acc, fabricType) => {
    const existingNonDeprecatedUValues = uValuesForCountry[fabricType].filter(
      (uValue) => !uValue.metadata.isDeprecated,
    );
    const projectCustomUValues = customUValues[fabricType] ?? [];
    const deletedUValuesForFabric = deletedUValues[fabricType] ?? [];
    const deprecatedUValuesToShow = deprecatedUValues[fabricType] ?? [];
    const merged = [
      ...existingNonDeprecatedUValues,
      ...projectCustomUValues,
      ...deletedUValuesForFabric,
      ...deprecatedUValuesToShow,
    ];
    return {
      ...acc,
      [fabricType]: merged,
    };
  }, {} as UValues);

  return mergedUValues;
}

export const loadProjectFromProtobuf = ({
  countryCode,
  heatDesign,
  setRooms,
  setFloors,
  setUValues,
  setProjectUValues,
  updateWaterTemps,
  climateDataStore,
}: {
  countryCode: CountryCode;
  heatDesign: HeatDesign;
  updateWaterTemps: HeatSourceStore['actions']['updateWaterTemps'];
  setProjectUValues: UValuesStore['actions']['setProjectUValues'];
  setUValues: UValuesStore['actions']['setUValues'];
  setRooms: RoomsStore['actions']['setRooms'];
  setFloors: FloorsStore['actions']['setFloors'];
  climateDataStore: ClimateDataStore;
}) => {
  const { dwelling } = heatDesign;
  if (dwelling === undefined) {
    throw new Error('No dwelling in project');
  }

  const uValuesForCountry = getUValuesForCountryCode(countryCode);
  const mergedUValues = getUValuesForSolution(heatDesign, uValuesForCountry);
  setUValues(mergedUValues);

  const uValueById = Object.values(mergedUValues).reduce(
    (acc, uValues) => {
      uValues.forEach((uValue) => {
        acc[uValue.id] = uValue;
      });
      return acc;
    },
    {} as { [key: string]: UValue },
  );

  const findUValue = (uValue: ProtoUValue | undefined) => (uValue?.id?.value ? uValueById[uValue.id.value] : undefined);

  const projectUValues: ProjectFallbackUValues = extractFallbackUValuesFromProtobuf(
    heatDesign.dwellingUValueDefaults?.defaultEntries ?? [],
    mergedUValues,
  );

  // In Germany, we assign default U-Values to the project based on the construction year.
  if (countryCode === CountryCode.DE && heatDesign.dwelling?.constructionYear !== undefined) {
    const { constructionYear } = heatDesign.dwelling;
    PROJECT_FABRIC_TYPES.forEach((fabricType) => {
      // If there's no user-defined U-Value, assign a default one
      if (projectUValues[fabricType] === undefined) {
        // Assign the worst uValue we can find for the building year. If we don't
        // have data for that building year, don't assign anything.
        const worstValue = getWorstUValueForBuildingConstructionYear(uValuesForCountry[fabricType], constructionYear);
        if (worstValue) {
          projectUValues[fabricType] = worstValue;
        }
      }
    });
  }

  setProjectUValues(projectUValues);

  const rooms = dwelling.floors.flatMap((floor) =>
    floor.rooms.map((protoRoom) => {
      const roomType = mapRoomTypeFromProtobuf(protoRoom.roomType);
      const flueType = mapOpenFlueTypeFromProtobuf(protoRoom.openFlueType);
      const totalVolume = calculateVolume(protoRoom.areaSqm, protoRoom.averageCeilingHeightM);

      if (protoRoom.id === undefined) {
        throw new Error('Room ID is undefined');
      }
      if (floor.id === undefined) {
        throw new Error('Floor level ID is undefined');
      }

      const allWalls = [...protoRoom.externalWalls, ...protoRoom.internalWalls, ...protoRoom.partyWalls];

      // If there's a floor above, look for the lowest temperature of any rooms on that floor
      const floorAbove = dwelling.floors.find((f) => f.floorLevel === floor.floorLevel + 1);
      let worstTempInFloorAbove = 18;
      if (floorAbove) {
        worstTempInFloorAbove =
          getLowestRoomTempOnFloor(floorAbove, dwelling.constructionYear, countryCode, climateDataStore) ?? 18;
      }

      const room: Room = {
        id: protoRoom.id.value,
        floorId: floor.id.value,
        name: protoRoom.name,
        roomType,
        level: floor.floorLevel,
        floor: floor.name,
        designRoomTempOverride: protoRoom.designRoomTemperatureCelsius,
        avgAirChangesPerHourOverride: protoRoom.averageAirChangePerHour,
        openFlue: flueType,
        totalVolume,
        isHeated: protoRoom.heated,
        totalArea: protoRoom.areaSqm,
        averageHeight: protoRoom.averageCeilingHeightM,
        imageSvgData: protoRoom.svgImageData,
        simplifiedImageSvgData: protoRoom.simplifiedSvgImageData,
        imageMap: mapImageMapFromProtobuf(protoRoom.imageMap),
        enableRadiatorDeltaTAdjustment: protoRoom.enableRadiatorDeltaTAdjustment,
        surfaces: {
          floors: [
            {
              uid: uuidv4(),
              area: protoRoom.areaSqm,
              belowFloor: getBelowFloorType({
                belowFloor: protoRoom.belowFloor,
                floorLevel: floor.floorLevel,
                floors: dwelling.floors,
              }),
              uValue: findUValue(protoRoom.floorSurfaceUValue),
              surfaceType: 'floors',
            },
          ],
          roofsOrCeilings: [
            {
              uid: uuidv4(),
              area: protoRoom.areaSqm,
              spaceAbove: getAboveCeilingType({
                aboveCeiling: protoRoom.aboveCeiling,
                floorLevel: floor.floorLevel,
                floors: dwelling.floors,
                tempOfSpaceAbove: protoRoom.aboveCeilingTemperatureCelsius ?? worstTempInFloorAbove,
              }),
              surfaceType: 'roofsOrCeilings',
              uValue: findUValue(protoRoom.ceilingUValue),
            },
          ],
          walls: allWalls.map((w) => mapWallFromProtobuf(w, findUValue(w.uValue))),
          doors: allWalls.flatMap((wall) =>
            wall.doors.map((door) => mapDoorFromProtobuf(door, wall, findUValue(door.uValue))),
          ),
          windows: allWalls.flatMap((wall) =>
            wall.windows.map((win) => mapWindowFromProtobuf(win, wall, findUValue(win.uValue))),
          ),
          roofGlazings: protoRoom.roofGlazings.map((rg) => mapRoofGlazingFromProtobuf(rg, findUValue(rg.uValue))),
        },
        radiators: protoRoom.radiators.map((r) => mapRadiatorFromProtobuf(r, protoRoom)),
        underfloorHeating: mapUnderfloorHeatingFromProtobuf(protoRoom.underfloorHeatingWatt),
      };
      return room;
    }),
  );

  const waterTemperatures =
    dwelling.systemDesign === undefined
      ? getDefaultWaterTemperatures({ countryCode })
      : getWaterTemperatures({ systemDesign: dwelling.systemDesign });

  setFloors(dwelling.floors.map((floor) => mapFloorFromProtobuf(floor, mergedUValues)));
  setRooms(rooms);

  if (waterTemperatures !== undefined) {
    updateWaterTemps(waterTemperatures);
  }
};
