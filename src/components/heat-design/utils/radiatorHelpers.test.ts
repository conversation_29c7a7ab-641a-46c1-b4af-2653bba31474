import { createExampleSvg, createSampleRoom, mockRadiatorData } from 'tests/utils/testUtils';
import { CountryCode } from 'utils/marketConfigurations';
import {
  calculateNetHeatOutputByFloor,
  calculateNetHeatOutputForRoom,
  calculateNewHeatOutput,
  calculateWaterDensity,
  getCorrectionFactor,
  getMaximumFlowTemperature,
  getOutputAtDeltaT,
  getRadiatorFlowRate,
  getUnderfloorHeatingFlowRate,
} from './radiatorHelpers';
import { RoomHeatDesignResult } from '../stores/OutputsStore';
import { CustomUnderfloorHeatingOutput, OutputType, RadiatorData, RadiatorMode, SystemType } from '../stores/types';
import { UNDERFLOOR_HEATING_MEAN_DELTA_T } from '../constants';
import { ClimateDataStore } from '../stores/ClimateDataStore';

const mockedClimateDataStore: ClimateDataStore = {
  baseOutdoorDesignTemperature: 10,
  degreeDays: 0,
  localAnnualAverageExternalAirTemperature: 10,
  actions: { setClimateData: () => undefined },
};

function customWaterBasedUnderfloorHeating(nominalOutput: number): CustomUnderfloorHeatingOutput {
  return {
    outputType: OutputType.CUSTOM,
    systemType: SystemType.WATER,
    nominalOutput: {
      outputWatt: nominalOutput,
      deltaT: UNDERFLOOR_HEATING_MEAN_DELTA_T,
    },
    deltaTAdjustmentCelsius: 0,
  };
}

describe('the mean water to air temperature (MW-AT) factor (f1) calculations', () => {
  // Reference: CIBSE's domestic heating design, section 5.5.4.1, first example
  it('should match the result in example 1', () => {
    const result = getCorrectionFactor({
      meanWaterTemp: 55,
      designRoomTemp: 21,
      // The above give a meanWaterToAirTemp MW-AT of 34
      manufacturersDeltaT: 50,
    });

    expect(result).toBeCloseTo(0.606);
  });

  // Reference: CIBSE's domestic heating design, section 5.5.4.1, second example
  it('should match the result in example 2', () => {
    const result = getCorrectionFactor({
      meanWaterTemp: 52,
      designRoomTemp: 21,
      // The above give a meanWaterToAirTemp MW-AT of 31
      manufacturersDeltaT: 60,
    });

    expect(result).toBeCloseTo(0.424);
  });

  // Reference: CIBSE's domestic heating design, section 5.5.4.1, table sample
  it('should match the result in table', () => {
    const result = getCorrectionFactor({
      meanWaterTemp: 49,
      designRoomTemp: 21,
      // The above give a meanWaterToAirTemp MW-AT of 28
      // Note when reading the table, the row is 10s and
      // the columns are 1s. So 28 = row 20, column 8.
      manufacturersDeltaT: 50,
    });

    expect(result).toBeCloseTo(0.471);
  });

  // If the mean water temp is lower than the design room temp, we never return
  // a negative value.
  it('should never return a negative value', () => {
    const result = getCorrectionFactor({
      meanWaterTemp: 20,
      designRoomTemp: 21,
      manufacturersDeltaT: 50,
    });

    expect(result).toBe(0);
  });
});

describe("the radiator's output at the room's deltaT", () => {
  // Reference: Asa's heat design file ASHP https://wiki.airahome.com/s/aira/p/heat-design-projects-236T7hZe39
  // Appendex O, New Radaiators. Living Room, radiator type = K2.
  [
    { meanWaterTemp: 35, expectedOutput: 404 },
    { meanWaterTemp: 42.5, expectedOutput: 705 },
    { meanWaterTemp: 50, expectedOutput: 1040 },
    { meanWaterTemp: 55, expectedOutput: 1279 },
  ].forEach((testCase) =>
    it(`of ${testCase.meanWaterTemp} °C should match the result in Heat Engineer`, () => {
      const result = getOutputAtDeltaT({
        meanWaterTemp: testCase.meanWaterTemp,
        designRoomTemp: 21,
        outputAtManufacturersDeltaT: 2112,
        manufacturersDeltaT: 50,
      });

      expect(result).toBeCloseTo(testCase.expectedOutput);
    }),
  );

  // Reference: https://trystanlea.org.uk/roombyroomheatloss2 Living Room
  // Manually input the values below and verify they are the same.
  it('should match the example by Trystan Lea', () => {
    const result = getOutputAtDeltaT({
      meanWaterTemp: 40,
      designRoomTemp: 21,
      outputAtManufacturersDeltaT: 2146,
      manufacturersDeltaT: 50,
    });

    expect(result).toBeCloseTo(610);
  });

  // Reference: MCS Heat Pump Calculator, "HP Rad sizing" sheet
  // https://mcscertified.com/standards-tools-library
  // Manually input the values below and verify they are the same
  it("should match the result by MCS's Heat Pump Calculator", () => {
    const result = getOutputAtDeltaT({
      meanWaterTemp: 46,
      designRoomTemp: 21,
      outputAtManufacturersDeltaT: 1392,
      manufacturersDeltaT: 50,
    });

    // The values above give a MW-AT (mean water air temperature) of 25.
    // Note: MW-AT is labelled as "Delta T", column E in the MCS calculator.
    //
    // The MCS Heat Pump Calculator is less accurate than our calculations,
    // as they use a lookup table instead of a formula.. So this test case
    // was crafted to exactly match the first MW-AT in their lookup table
    // range - the same result would be reported for an MW-AT ≥25 and < 29.

    expect(result).toBeCloseTo(565.15, 0);
  });
});

describe('calculateNewHeatOutput', () => {
  it('should calculate the total heat output of the radiators and underfloor heating in a room', () => {
    const radiators: RadiatorData[] = [
      mockRadiatorData({
        uid: '1',
        width: 100,
        height: 100,
        isExisting: true,
        enabled: true,
        typeOfHeatEmitter: RadiatorMode.Panel,
        radiatorDetails: {
          systemType: SystemType.WATER,
          deltaTAdjustmentCelsius: 0,
          nominalOutput: {
            outputWatt: 100,
            deltaT: 50,
          },
        },
      }),
      mockRadiatorData({
        uid: '2',
        width: 100,
        height: 100,
        isExisting: false,
        enabled: false,
        typeOfHeatEmitter: RadiatorMode.Panel,
        radiatorDetails: {
          systemType: SystemType.WATER,
          deltaTAdjustmentCelsius: 0,
          nominalOutput: {
            outputWatt: 200,
            deltaT: 50,
          },
        },
      }),
      mockRadiatorData({
        uid: '3',
        width: 100,
        height: 100,
        isExisting: false,
        enabled: true,
        typeOfHeatEmitter: RadiatorMode.Panel,
        radiatorDetails: {
          systemType: SystemType.ELECTRIC,
          outputWatt: 500,
        },
      }),
    ];

    const result = calculateNewHeatOutput({
      constructionYear: 1990,
      flowReturnDeltaT: 6,
      flowTemperature: 74,
      roomHeatLoss: 0,
      room: createSampleRoom({
        radiators,
        underfloorHeating: customWaterBasedUnderfloorHeating(300),
      }),
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
    });

    expect(result.radiators).toBe(100 + 500);
    expect(result.underfloorHeating).toBe(300);
  });
});

function createRoomHeatLoss(roomID: string, totalHeatLoss: number): RoomHeatDesignResult {
  const stubbedOutput = { heatLoss: 0, energyDemand: 0 };
  return {
    floorId: 'floor',
    roomId: roomID,
    roomName: 'Living Room',
    roomType: 'Living Room',
    isHeated: true,
    averageHeight: 2.4,
    surfaceTotals: {
      externalWalls: stubbedOutput,
      floors: stubbedOutput,
      roofGlazings: stubbedOutput,
      partyWalls: stubbedOutput,
      internalWalls: stubbedOutput,
      doors: stubbedOutput,
      windows: stubbedOutput,
      roofsOrCeilings: stubbedOutput,
    },
    surfaceDetails: {
      externalWalls: [],
      floors: [],
      roofGlazings: [],
      partyWalls: [],
      internalWalls: [],
      doors: [],
      windows: [],
      roofsOrCeilings: [],
    },
    totalFabric: stubbedOutput,
    ventilation: stubbedOutput,
    additional: {
      highCeiling: stubbedOutput,
    },
    totalFloorArea: 20.1,
    totalAdditional: stubbedOutput,
    totalRoom: {
      heatLoss: totalHeatLoss, // This is the only one we care about here
      energyDemand: 0,
    },
    wattsPerMeterSquared: 20.1,
    calculatedUnderfloorHeatingOutputWatt: 0,
    calculatedUnderfloorHeatingFlowRate: 0,
    totalOutputOfRadiatorsWatt: 0,
    totalOutputOfHeatEmittersWatt: 0,
    resolvedDesignRoomTemperatureCelsius: 21,
    radiators: [],
    averageAirChangePerHour: 0,
  };
}

describe('calculateNetHeatOutputForRoom', () => {
  it('should calculate the net heating output for a room', () => {
    const roomID = '123';
    const roomHeatDesignResult: RoomHeatDesignResult = createRoomHeatLoss(roomID, 500);

    // Where do we set the radiator heat output?
    const result = calculateNetHeatOutputForRoom({
      room: createSampleRoom({
        id: roomID,
        radiators: [
          mockRadiatorData({
            radiatorDetails: {
              systemType: SystemType.WATER,
              deltaTAdjustmentCelsius: 0,
              nominalOutput: {
                outputWatt: 100,
                deltaT: 50,
              },
            },
          }),
        ],
        underfloorHeating: customWaterBasedUnderfloorHeating(300),
      }),
      roomHeatDesignResults: [roomHeatDesignResult],
      constructionYear: 2000,
      flowReturnDeltaT: 6,
      flowTemperature: 74,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
    });

    expect(result).toBe(-100); // 100 radiator + 300 underfloor = 400 - 500 heat loss
  });

  it('should calculate the net heating output for a room that has no heating source', () => {
    const roomID = '456';
    const roomHeatDesignResult: RoomHeatDesignResult = createRoomHeatLoss(roomID, 400);

    const result = calculateNetHeatOutputForRoom({
      room: createSampleRoom({ id: roomID, radiators: [] }),
      roomHeatDesignResults: [roomHeatDesignResult],

      constructionYear: 2000,
      flowReturnDeltaT: 6,
      flowTemperature: 74,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
    });

    expect(result).toBe(-400);
  });
});

describe('calculateNetHeatOutputByFloor', () => {
  it('should calculate the net heating output for floors', () => {
    const result = calculateNetHeatOutputByFloor({
      floors: [
        {
          floorName: 'Ground Floor',
          uid: '56789',
          floorNr: 0,
          imageSvgData: createExampleSvg(),
          simplifiedImageSvgData: createExampleSvg(),
          soilPercentageDefault: null,
        },
        {
          floorName: 'First Floor',
          uid: '94345',
          floorNr: 1,
          imageSvgData: createExampleSvg(),
          simplifiedImageSvgData: createExampleSvg(),
          soilPercentageDefault: null,
        },
      ],
      roomsByFloor: {
        '56789': [
          createSampleRoom({
            id: '1',
            radiators: [
              mockRadiatorData({
                radiatorDetails: {
                  systemType: SystemType.WATER,
                  deltaTAdjustmentCelsius: 0,
                  nominalOutput: {
                    outputWatt: 300,
                    deltaT: 50,
                  },
                },
              }),
            ],
          }),
          createSampleRoom({
            id: '2',
            radiators: [
              mockRadiatorData({
                radiatorDetails: {
                  systemType: SystemType.WATER,
                  deltaTAdjustmentCelsius: 0,
                  nominalOutput: {
                    outputWatt: 200,
                    deltaT: 50,
                  },
                },
              }),
            ],
          }),
        ],
        '94345': [
          createSampleRoom({
            id: '3',
            radiators: [
              mockRadiatorData({
                radiatorDetails: {
                  systemType: SystemType.WATER,
                  deltaTAdjustmentCelsius: 0,
                  nominalOutput: {
                    outputWatt: 600,
                    deltaT: 50,
                  },
                },
              }),
            ],
          }),
        ],
      },
      roomHeatDesignResults: [createRoomHeatLoss('1', 300), createRoomHeatLoss('2', 400), createRoomHeatLoss('3', 500)],
      constructionYear: 2000,
      flowReturnDeltaT: 10,
      flowTemperature: 76,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
    });

    expect(result).toEqual({
      'Ground Floor': -200, // room 1: 300 - 300 = 0, room 2: 200 - 400 = -200
      'First Floor': 100, // room 3: 600 - 500 = 100
    });
  });
});

describe('hydraulic balancing', () => {
  it('should calculate water density correctly', () => {
    const flowTemp = 55;
    const flowReturnDeltaT = 10;
    const result = calculateWaterDensity(flowTemp);

    expect(result).toBeCloseTo(985.67);

    expect(
      getRadiatorFlowRate(
        mockRadiatorData({
          radiatorDetails: {
            systemType: SystemType.WATER,
            deltaTAdjustmentCelsius: -9.9, // This is set...
          },
        }),
        createSampleRoom({
          enableRadiatorDeltaTAdjustment: false, // ... but the room doesn't allow adjustments
        }),
        flowTemp,
        flowReturnDeltaT,
        675,
      ),
    ).toBeCloseTo(58.88);
  });

  it('should calculate the flow rate correctly with adjusted flow rates', () => {
    const flowTemp = 55;
    const flowReturnDeltaT = 10;

    expect(
      getRadiatorFlowRate(
        mockRadiatorData({
          radiatorDetails: {
            systemType: SystemType.WATER,
            deltaTAdjustmentCelsius: -9.9,
          },
        }),
        createSampleRoom({
          enableRadiatorDeltaTAdjustment: true,
        }),
        flowTemp,
        flowReturnDeltaT,
        675,
      ),
    ).toBeCloseTo(5888.03);

    expect(
      getRadiatorFlowRate(
        mockRadiatorData({
          radiatorDetails: {
            systemType: SystemType.WATER,
            deltaTAdjustmentCelsius: 26.9,
          },
        }),
        createSampleRoom({
          enableRadiatorDeltaTAdjustment: true,
        }),
        flowTemp,
        flowReturnDeltaT,
        413.31,
      ),
    ).toBeCloseTo(9.77);
  });

  it('should calculate flow rate for underfloor heating', () => {
    expect(getUnderfloorHeatingFlowRate(800)).toBeCloseTo(69.46);
  });

  it('should return the correct maximum flow temperature depending on the country', () => {
    expect(getMaximumFlowTemperature({ countryCode: CountryCode.GB })).toBe(50);
    expect(getMaximumFlowTemperature({ countryCode: CountryCode.DE })).toBe(55);
    expect(getMaximumFlowTemperature({ countryCode: CountryCode.IT })).toBe(60);
    expect(() => getMaximumFlowTemperature({ countryCode: 'AU' as CountryCode })).toThrow();
  });
});
