import { CountryCode } from 'utils/marketConfigurations';
import { RoomType } from '../stores/types';
import { buildPeriodRange } from './constructionPeriodRange';
import { ROOM_DESIGN_TEMPERATURES_BY_COUNTRY } from '../constants';
import { ClimateDataStore } from '../stores/ClimateDataStore';

export function getUnheatedRoomTemperature(climateDataStore: ClimateDataStore): number {
  return climateDataStore.localAnnualAverageExternalAirTemperature;
}

export const getDesignRoomTemp = (
  room: {
    isHeated: boolean;
    roomType: RoomType;
    designRoomTempOverride?: number;
  },
  constructionYear: number,
  countryCode: CountryCode,
  climateDataStore: ClimateDataStore,
): number => {
  // Not heated takes precedence over explicit setting
  if (!room.isHeated) {
    return getUnheatedRoomTemperature(climateDataStore);
  }

  if (room.designRoomTempOverride !== undefined) return room.designRoomTempOverride;

  if (countryCode === CountryCode.GB && buildPeriodRange(constructionYear) === '>2005') {
    if (room.roomType === 'Bathroom') return 22;
    return 21;
  }

  return ROOM_DESIGN_TEMPERATURES_BY_COUNTRY[room.roomType][countryCode];
};
