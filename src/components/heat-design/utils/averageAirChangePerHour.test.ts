import { createSampleRoom } from 'tests/utils/testUtils';
import { ACPHDefaultValue, ACPHStandardizedDefaultValue, OpenFlueType, RoomType } from '../stores/types';
import { ACPHStandardizedDefaults, getAverageAirChangesPerHour } from './averageAirChangePerHour';
import { BuildingConstructionPeriod, buildPeriodRange } from './constructionPeriodRange';

const makeTestDescription = (e: Record<string, string | number>) =>
  Object.keys(e).reduce((acc, curr) => `${acc} ${curr}: ${e[curr]}`, '');

describe('air change', () => {
  const examples: Array<{
    roomType: RoomType;
    openFlue: OpenFlueType;
    totalVolume: number;
    result: number;
    acphDefault: ACPHDefaultValue;
  }> = [
    {
      roomType: 'Living room',
      openFlue: 'Yes (without throat restrictor)',
      totalVolume: 70,
      result: 5.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.CIBSE_BEFORE_2000 },
    },
    {
      roomType: 'Bathroom',
      openFlue: 'none',
      totalVolume: 50,
      result: 1.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.CIBSE_BEFORE_2006 },
    },
    {
      roomType: 'Hall',
      openFlue: 'Yes (with throat restrictor)',
      totalVolume: 20,
      result: 3.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.CIBSE_AFTER_INCLUDING_2006 },
    },
    {
      roomType: 'Utility room',
      openFlue: 'Yes (without throat restrictor)',
      totalVolume: 50,
      result: 6,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.CIBSE_BEFORE_2006 },
    },
    {
      roomType: 'Living room',
      openFlue: 'none',
      totalVolume: 50,
      result: 1.0,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.DIN_12831_BEFORE_1977 },
    },
    {
      roomType: 'Living room',
      openFlue: 'none',
      totalVolume: 50,
      result: 0.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.DIN_12831_BEFORE_1995 },
    },
    {
      roomType: 'Living room',
      openFlue: 'none',
      totalVolume: 50,
      result: 0.25,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.DIN_12831_AFTER_INCLUDING_1995 },
    },
  ];

  examples.forEach(({ result, acphDefault, roomType, openFlue, totalVolume }) => {
    it(
      makeTestDescription({
        roomType,
        openFlue,
        totalVolume,
        standard: (acphDefault as ACPHStandardizedDefaultValue).standard,
      }),
      () => {
        expect(getAverageAirChangesPerHour(createSampleRoom({ roomType, openFlue, totalVolume }), acphDefault)).toBe(
          result,
        );
      },
    );
  });
});

describe('buildPeriodRange', () => {
  const exemples: { expected: number; received: BuildingConstructionPeriod }[] = [
    { expected: 1900, received: '<2000' },
    { expected: 2000, received: '2000-2005' },
    { expected: 2001, received: '2000-2005' },
    { expected: 2005, received: '2000-2005' },
    { expected: 2007, received: '>2005' },
  ];
  exemples.forEach((e) =>
    it(makeTestDescription(e), () => {
      expect(buildPeriodRange(e.expected)).toBe(e.received);
    }),
  );
});

describe('air change values for Italy', () => {
  const examples: Array<{
    roomType: RoomType;
    openFlue: OpenFlueType;
    totalVolume: number;
    result: number;
    acphDefault: ACPHDefaultValue;
  }> = [
    {
      roomType: 'Living room',
      openFlue: 'none',
      totalVolume: 50,
      result: 0.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.UNI_12831 },
    },
    {
      roomType: 'Kitchen',
      openFlue: 'none',
      totalVolume: 50,
      result: 1.5,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.UNI_12831 },
    },
    {
      roomType: 'Bathroom',
      openFlue: 'none',
      totalVolume: 50,
      result: 2.0,
      acphDefault: { type: 'standardized', standard: ACPHStandardizedDefaults.UNI_12831 },
    },
  ];

  examples.forEach(({ result, acphDefault, roomType, openFlue, totalVolume }) => {
    it(
      makeTestDescription({
        roomType,
        openFlue,
        totalVolume,
        standard: (acphDefault as ACPHStandardizedDefaultValue).standard,
      }),
      () => {
        expect(getAverageAirChangesPerHour(createSampleRoom({ roomType, openFlue, totalVolume }), acphDefault)).toBe(
          result,
        );
      },
    );
  });
});

describe('custom ACPH default values', () => {
  const examples: Array<{
    desc: string;
    roomOverrides: Partial<Parameters<typeof createSampleRoom>[0]>;
    acphDefault: ACPHDefaultValue;
    expected: number;
  }> = [
    {
      desc: 'returns custom value regardless of flue or volume (1.2)',
      roomOverrides: { roomType: 'Living room', openFlue: 'Yes (without throat restrictor)', totalVolume: 120 },
      acphDefault: { type: 'custom', value: 1.2 },
      expected: 1.2,
    },
    {
      desc: 'returns custom value for bathroom with flue (3.4)',
      roomOverrides: { roomType: 'Bathroom', openFlue: 'Yes (with throat restrictor)', totalVolume: 15 },
      acphDefault: { type: 'custom', value: 3.4 },
      expected: 3.4,
    },
    {
      desc: 'room override takes precedence over custom value',
      roomOverrides: {
        roomType: 'Kitchen',
        openFlue: 'none',
        totalVolume: 30,
        avgAirChangesPerHourOverride: 5.5,
      },
      acphDefault: { type: 'custom', value: 1.0 },
      expected: 5.5,
    },
  ];

  examples.forEach(({ desc, roomOverrides, acphDefault, expected }) => {
    it(desc, () => {
      const room = createSampleRoom(roomOverrides);
      expect(getAverageAirChangesPerHour(room, acphDefault)).toBe(expected);
    });
  });
});
