import { CountryCode } from 'utils/marketConfigurations';
import { ACPHDefaultValue, OpenFlueType, Room, RoomType } from '../stores/types';
import { BuildingConstructionPeriod, buildPeriodRange } from './constructionPeriodRange';
import { StandardizedACPHDefaults } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

/**
 * Get the air change factor for a room based on the room type and the year the building was built.
 *
 * Reference: 3.5.4.1 Air Changes per Hour
 */
type BuildPeriodValues = { [k in BuildingConstructionPeriod]: number };

export enum ACPHStandardizedDefaults {
  // UK - CIBSE
  CIBSE_BEFORE_2000 = '<2000 (CIBSE)',
  CIBSE_BEFORE_2006 = '2000-2005 (CIBSE)',
  CIBSE_AFTER_INCLUDING_2006 = '≥2005 (CIBSE)',
  // DE - DIN12831 B.3.4 "Air change rate"
  DIN_12831_BEFORE_1977 = '<1977 (DIN12831)',
  DIN_12831_BEFORE_1995 = '1977-1995 (DIN12831)',
  DIN_12831_AFTER_INCLUDING_1995 = '≥1995 (DIN12831)',
  // IT - UNI 12831
  UNI_12831 = 'UNI12831',

  // EU - EN12831
  EN_12831 = 'Min. (EN12831)',
}

const acphDefaultValues: BuildPeriodValues = {
  '<2000': 1.5,
  '2000-2005': 1,
  '>2005': 0.7,
};

export const ACPH_DEFAULTS_PER_MARKET: { [key in CountryCode]: ACPHStandardizedDefaults[] } = {
  [CountryCode.GB]: [
    ACPHStandardizedDefaults.CIBSE_BEFORE_2000,
    ACPHStandardizedDefaults.CIBSE_BEFORE_2006,
    ACPHStandardizedDefaults.CIBSE_AFTER_INCLUDING_2006,
  ],
  [CountryCode.DE]: [
    ACPHStandardizedDefaults.DIN_12831_BEFORE_1977,
    ACPHStandardizedDefaults.DIN_12831_BEFORE_1995,
    ACPHStandardizedDefaults.DIN_12831_AFTER_INCLUDING_1995,
  ],
  [CountryCode.IT]: [ACPHStandardizedDefaults.UNI_12831],
};

export const STANDARDIZED_ACPH_DEFAULTS_MAP: {
  [key in StandardizedACPHDefaults]: ACPHStandardizedDefaults | undefined;
} = {
  [StandardizedACPHDefaults.UNRECOGNIZED]: undefined,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_UNSPECIFIED]: undefined,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_CIBSE_BEFORE_2000]: ACPHStandardizedDefaults.CIBSE_BEFORE_2000,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_CIBSE_BEFORE_2006]: ACPHStandardizedDefaults.CIBSE_BEFORE_2006,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_CIBSE_AFTER_INCLUDING_2006]:
    ACPHStandardizedDefaults.CIBSE_AFTER_INCLUDING_2006,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_DIN_12831_BEFORE_1977]:
    ACPHStandardizedDefaults.DIN_12831_BEFORE_1977,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_DIN_12831_BEFORE_1995]:
    ACPHStandardizedDefaults.DIN_12831_BEFORE_1995,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_DIN_12831_AFTER_INCLUDING_1995]:
    ACPHStandardizedDefaults.DIN_12831_AFTER_INCLUDING_1995,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_EN12831]: ACPHStandardizedDefaults.EN_12831,
  [StandardizedACPHDefaults.STANDARDIZED_ACPH_DEFAULTS_UNI_12831]: ACPHStandardizedDefaults.UNI_12831,
};

// DHDG2021 Table 3.8: Recommended room design number of air changes per hour
const cibseAirChanges: { [key in RoomType]: BuildPeriodValues } = {
  Bathroom: { '<2000': 3.0, '2000-2005': 1.5, '>2005': 0.5 },
  Bedroom: { '<2000': 1.0, '2000-2005': 1.0, '>2005': 0.5 },
  'Bedroom, including en suite bathroom': { '<2000': 2.0, '2000-2005': 1.5, '>2005': 1.0 },
  'Bedroom/study': { '<2000': 1.5, '2000-2005': 1.5, '>2005': 0.5 },
  'Bedsitting room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Breakfast room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Cloakroom/WC': { '<2000': 2.0, '2000-2005': 1.5, '>2005': 1.5 },
  'Dining room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Dressing room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Family/breakfast room': { '<2000': 2.0, '2000-2005': 1.5, '>2005': 0.5 },
  'Games room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  Hall: { '<2000': 2.0, '2000-2005': 1.0, '>2005': 0.5 },
  'Internal room or corridor': { '<2000': 0.0, '2000-2005': 0.0, '>2005': 0.0 },
  Kitchen: { '<2000': 2.0, '2000-2005': 1.5, '>2005': 0.5 },
  Landing: { '<2000': 2.0, '2000-2005': 1.0, '>2005': 0.5 },
  'Living room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Lounge/sitting room': { '<2000': 1.5, '2000-2005': 1.0, '>2005': 0.5 },
  'Shower room': { '<2000': 3.0, '2000-2005': 1.5, '>2005': 0.5 },
  'Store room': { '<2000': 1.0, '2000-2005': 0.5, '>2005': 0.5 },
  Study: { '<2000': 1.5, '2000-2005': 1.5, '>2005': 0.5 },
  Toilet: { '<2000': 3.0, '2000-2005': 1.5, '>2005': 1.5 },
  'Utility room': { '<2000': 3.0, '2000-2005': 2.0, '>2005': 0.5 },
  Other: acphDefaultValues,
};

const airChangeFactors = (cibseRoom: RoomType) => cibseAirChanges[cibseRoom];

const getCibseAirChangeFactor = (roomType: RoomType, yearBuilt: number) => {
  const buildingConstructionPeriod = buildPeriodRange(yearBuilt);
  return airChangeFactors(roomType)[buildingConstructionPeriod] ?? 1; // Fallback value
};

function getDin12831AirChangeFactor(yearBuilt: number): number {
  if (yearBuilt < 1977) return 1.0;
  if (yearBuilt < 1995) return 0.5;
  return 0.25;
}

function getUni12831AirChangeValues(roomType: RoomType): number {
  // Living space (default) 0.5, Kitchen 1.5, Bathroom 2.0
  switch (roomType) {
    case 'Kitchen':
      return 1.5;
    case 'Bathroom':
      return 2.0;
    default:
      return 0.5;
  }
}

function getEn12831AirChangeValues(roomType: RoomType): number {
  // Internal rooms: 0, all other: 0.5
  if (roomType === 'Internal room or corridor') {
    return 0;
  }
  return 0.5;
}

export const ACPH_STANDARDIZED_DEFAULT_VALUES: Record<ACPHStandardizedDefaults, (roomType: RoomType) => number> = {
  [ACPHStandardizedDefaults.CIBSE_BEFORE_2000]: (roomType: RoomType) => getCibseAirChangeFactor(roomType, 1999),
  [ACPHStandardizedDefaults.CIBSE_BEFORE_2006]: (roomType: RoomType) => getCibseAirChangeFactor(roomType, 2005),
  [ACPHStandardizedDefaults.CIBSE_AFTER_INCLUDING_2006]: (roomType: RoomType) =>
    getCibseAirChangeFactor(roomType, 2006),
  [ACPHStandardizedDefaults.DIN_12831_BEFORE_1977]: (_) => getDin12831AirChangeFactor(1976),
  [ACPHStandardizedDefaults.DIN_12831_BEFORE_1995]: (_) => getDin12831AirChangeFactor(1994),
  [ACPHStandardizedDefaults.DIN_12831_AFTER_INCLUDING_1995]: (_: RoomType) => getDin12831AirChangeFactor(1995),
  [ACPHStandardizedDefaults.UNI_12831]: (roomType: RoomType) => getUni12831AirChangeValues(roomType),
  [ACPHStandardizedDefaults.EN_12831]: (roomType: RoomType) => getEn12831AirChangeValues(roomType),
};

export function getStandardizedDefaultsForCountry(
  countryCode: CountryCode,
  yearBuilt: number,
): ACPHStandardizedDefaults {
  switch (countryCode) {
    case CountryCode.DE: {
      if (yearBuilt < 1977) return ACPHStandardizedDefaults.DIN_12831_BEFORE_1977;
      if (yearBuilt < 1995) return ACPHStandardizedDefaults.DIN_12831_BEFORE_1995;
      return ACPHStandardizedDefaults.DIN_12831_AFTER_INCLUDING_1995;
    }
    case CountryCode.IT: {
      return ACPHStandardizedDefaults.UNI_12831;
    }
    case CountryCode.GB: {
      if (yearBuilt < 2000) return ACPHStandardizedDefaults.CIBSE_BEFORE_2000;
      if (yearBuilt < 2006) return ACPHStandardizedDefaults.CIBSE_BEFORE_2006;
      return ACPHStandardizedDefaults.CIBSE_AFTER_INCLUDING_2006;
    }
  }
}

const airChangePerHour = ({
  roomType,
  openFlue,
  totalVolume,
  acphDefaults,
}: {
  openFlue: OpenFlueType;
  roomType: RoomType;
  totalVolume: number;
  acphDefaults: ACPHStandardizedDefaults;
}) => {
  const airChangeFactor = ACPH_STANDARDIZED_DEFAULT_VALUES[acphDefaults](roomType);

  const airChangeOpenFlueFactor = {
    'Yes (with throat restrictor)': { '<40m3': 3, '>40m3': 2 },
    'Yes (without throat restrictor)': { '<40m3': 5, '>40m3': 4 },
    none: { '<40m3': 0, '>40m3': 0 },
  };
  const totalVolumeFactor = totalVolume < 40 ? '<40m3' : '>40m3';

  return airChangeFactor + airChangeOpenFlueFactor[openFlue][totalVolumeFactor];
};

export const getAverageAirChangesPerHour = (room: Room, acphDefault: ACPHDefaultValue) => {
  if (room.avgAirChangesPerHourOverride !== undefined) {
    return room.avgAirChangesPerHourOverride;
  }
  if (acphDefault?.type === 'custom') {
    return acphDefault.value;
  }
  return airChangePerHour({
    roomType: room.roomType,
    openFlue: room.openFlue,
    totalVolume: room.totalVolume,
    acphDefaults: acphDefault?.standard,
  });
};
