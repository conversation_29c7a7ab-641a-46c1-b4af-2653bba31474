import { UValue } from 'components/heat-design/models/UValue';
import {
  coordinateListToCoordinatePairs,
  lookupUValueForSurfaceType,
  getUrlQueryParameter,
  isWithinLimits,
  sortFloorsTopToBottom,
  groupRadiatorsBySize,
  toLocalisedDecimalPlaces,
} from './helpers';
import { RadiatorData, FloorProps } from '../stores/types';
import { RadiatorSize } from '../stores/HeatSourceStore';

function createExampleSvg(): string {
  return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"></svg>';
}

describe('groupRadiatorsBySize', () => {
  it('should work with an empty list', () => {
    expect(groupRadiatorsBySize([])).toEqual({
      [RadiatorSize.STANDARD]: [],
      [RadiatorSize.LARGE]: [],
      [RadiatorSize.DESIGN]: [],
    });
  });

  it('should group radiators by size', () => {
    // Using Pick<> because the other fields aren't involved in the calculation
    const radiators: Pick<RadiatorData, 'width' | 'height' | 'comment'>[] = [
      {
        comment: 'CB K2',
        height: 600,
        width: 900,
      },
      {
        comment: 'K2',
        height: 600,
        width: 700,
      },
      {
        comment: 'CB K2',
        height: 450,
        width: 1400,
      },
      {
        comment: 'Stelrad Vertical K2',
        height: 1800,
        width: 500,
      },
      {
        comment: 'Horizontal',
        height: 400,
        width: 1600,
      },
    ];
    const groupedRadiators = groupRadiatorsBySize(radiators as RadiatorData[]);

    expect(groupedRadiators[RadiatorSize.STANDARD]).toEqual([
      {
        comment: 'CB K2',
        height: 600,
        width: 900,
      },
      {
        comment: 'K2',
        height: 600,
        width: 700,
      },
      {
        comment: 'CB K2',
        height: 450,
        width: 1400,
      },
    ]);

    expect(groupedRadiators[RadiatorSize.LARGE]).toEqual([
      {
        comment: 'Stelrad Vertical K2',
        height: 1800,
        width: 500,
      },
      {
        comment: 'Horizontal',
        height: 400,
        width: 1600,
      },
    ]);

    expect(groupedRadiators[RadiatorSize.DESIGN]).toEqual([]);
  });
});

describe('isWithinLimits', () => {
  it('should be true if equal to the minimum', () => {
    expect(isWithinLimits(-20.4, -20.4, 3.14)).toBe(true);
  });
  it('should be true if equal to the maximum', () => {
    expect(isWithinLimits(3.14, -20.4, 3.14)).toBe(true);
  });
  it('should be true if between the minimum and maximum', () => {
    expect(isWithinLimits(0, -20.4, 3.14)).toBe(true);
  });

  it('should be true if between the minimum and infinity', () => {
    expect(isWithinLimits(0, -20.4, Infinity)).toBe(true);
  });
  it('should be true if between negative infinity and the maximum', () => {
    expect(isWithinLimits(0, -Infinity, 3.14)).toBe(true);
  });

  it('should be false if less than the minimum', () => {
    expect(isWithinLimits(-20.5, -20.4, 3.14)).toBe(false);
  });
  it('should be false if more than the maximum', () => {
    expect(isWithinLimits(3.15, -20.4, 3.14)).toBe(false);
  });

  it('should be false if null', () => {
    expect(isWithinLimits(undefined, -20.4, 3.14)).toBe(false);
  });
  it('should be false if NaN', () => {
    expect(isWithinLimits(NaN, -20.4, 3.14)).toBe(false);
  });
});

describe('coordinateListToCoordinatePairs', () => {
  it('should handle an empty list', () => {
    expect(coordinateListToCoordinatePairs([])).toStrictEqual([]);
  });
  it('should handle a list with odd number', () => {
    expect(() => coordinateListToCoordinatePairs([1, 2, 3])).toThrow(
      'Coordinate input should be of an equal length but was of length 3',
    );
  });
  it('should handle a single coordinate', () => {
    expect(coordinateListToCoordinatePairs([1, 2])).toStrictEqual([[1, 2]]);
  });
  it('should multiple coordinates', () => {
    expect(coordinateListToCoordinatePairs([1, 2, 3, 4, 5, 6, 7, 8])).toStrictEqual([
      [1, 2],
      [3, 4],
      [5, 6],
      [7, 8],
    ]);
  });
});

describe('getUrlQueryParameter', () => {
  window = Object.create(window);
  const defineHref = (href: string) => {
    Object.defineProperty(window, 'location', {
      value: {
        href,
        search: href.split('?')[1],
      },
      writable: true, // possibility to override
    });
  };
  defineHref(
    'http://localhost:3000/solution/03797ed5-6707-4323-a185-1ead1785837d/heat-design?demo-data=true&blabla=true&useDefaults&noBlabla=false',
  );

  it('should not find the query parameter', () => {
    // const url = window.location.href as unknown as string;
    expect(getUrlQueryParameter({ key: 'missing' })).toBeFalsy();
  });
  it('should be false when value is wrong', () => {
    expect(getUrlQueryParameter({ key: 'demo-data', value: 'false' })).toBeFalsy();
  });
  it('should be true when key pair is right', () => {
    expect(getUrlQueryParameter({ key: 'demo-data', value: 'true' })).toBeTruthy();
  });
  it('should find the key without pair', () => {
    expect(getUrlQueryParameter({ key: 'useDefaults' })).toBeTruthy();
  });
  it('should find the key without pair', () => {
    expect(getUrlQueryParameter({ key: 'blabla' })).toBeTruthy();
  });
  it('should be false is query params says false', () => {
    expect(getUrlQueryParameter({ key: 'noBlabla' })).toBeFalsy();
  });
});

describe('lookupUValueForSurfaceType', () => {
  const groundFloor: FloorProps = {
    floorName: 'Ground Floor',
    uid: 'asa_ground_floor',
    floorNr: 0,
    ceilingUValue: 0.25,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    soilPercentageDefault: null,
  };

  it('should return the floor value', () => {
    const expectedUValue = new UValue(
      'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
      0.45,
      'floor-id',
    );
    const firstFloor: FloorProps = {
      floorName: 'First Floor',
      floorUValues: {
        externalWalls: new UValue(
          'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
          0.45,
          'floor-id',
        ),
      },
      uid: 'asa_first_floor',
      floorNr: 1,
      ceilingUValue: 0.25,
      imageSvgData: createExampleSvg(),
      simplifiedImageSvgData: createExampleSvg(),
      soilPercentageDefault: null,
    };
    expect(lookupUValueForSurfaceType('externalWalls', {}, firstFloor, [groundFloor, firstFloor])).toStrictEqual({
      uValue: expectedUValue,
      source: 'floorLevel',
    });
  });

  it('should return the project value', () => {
    const expectedUValue = new UValue(
      'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
      0.45,
      'project-id',
    );
    const firstFloor: FloorProps = {
      floorName: 'First Floor',
      uid: 'asa_first_floor',
      floorNr: 1,
      ceilingUValue: 0.25,
      imageSvgData: createExampleSvg(),
      simplifiedImageSvgData: createExampleSvg(),
      soilPercentageDefault: null,
    };
    const project = {
      externalWalls: new UValue(
        'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
        0.45,
        'project-id',
      ),
    };
    expect(lookupUValueForSurfaceType('externalWalls', project, firstFloor, [groundFloor, firstFloor])).toStrictEqual({
      uValue: expectedUValue,
      source: 'dwelling',
    });
  });

  it('should return an undefined if no value found', () => {
    const firstFloor: FloorProps = {
      floorName: 'First Floor',
      uid: 'asa_first_floor',
      floorNr: 1,
      ceilingUValue: 0.25,
      imageSvgData: createExampleSvg(),
      simplifiedImageSvgData: createExampleSvg(),
      soilPercentageDefault: null,
    };

    expect(lookupUValueForSurfaceType('externalWalls', {}, firstFloor, [groundFloor, firstFloor])?.uValue).toBe(
      undefined,
    );
  });
});

describe('sortFloorsTopToBottom', () => {
  it('sorts floors from highest to lowest', () => {
    const floors: FloorProps[] = [
      {
        floorName: 'Ground floor',
        floorNr: 0,
        imageSvgData: '',
        simplifiedImageSvgData: '',
        uid: 'ground',
        soilPercentageDefault: null,
      },
      {
        floorName: 'First floor',
        floorNr: 1,
        imageSvgData: '',
        simplifiedImageSvgData: '',
        uid: 'floor-1',
        soilPercentageDefault: null,
      },
      {
        floorName: 'Third floor',
        floorNr: 3,
        imageSvgData: '',
        simplifiedImageSvgData: '',
        uid: 'floor-3',
        soilPercentageDefault: null,
      },
      {
        floorName: 'Second floor',
        floorNr: 2,
        imageSvgData: '',
        simplifiedImageSvgData: '',
        uid: 'floor-2',
        soilPercentageDefault: null,
      },
    ];

    expect(sortFloorsTopToBottom(floors).map((f) => f.floorNr)).toEqual([3, 2, 1, 0]);
    expect(sortFloorsTopToBottom(floors).map((f) => f.floorName)).toEqual([
      'Third floor',
      'Second floor',
      'First floor',
      'Ground floor',
    ]);
  });
});

describe('toLocalisedDecimalPlaces', () => {
  it('should default to 2 decimal places with en-GB locale', () => {
    expect(toLocalisedDecimalPlaces({ num: 1000.6666 })).toBe('1,000.67');
  });
  it('should allow limiting decimal places', () => {
    expect(toLocalisedDecimalPlaces({ num: 1000.6666, decimalPlaces: 3 })).toBe('1,000.667');
  });
  it('should pad missing decimal places', () => {
    expect(toLocalisedDecimalPlaces({ num: 1000, decimalPlaces: 2 })).toBe('1,000.00');
  });
  it('should support a different locale', () => {
    expect(toLocalisedDecimalPlaces({ num: 1000.6666, locale: 'de-DE' })).toBe('1.000,67');
  });
  it('should handle undefined numbers', () => {
    // @ts-expect-error: Type 'undefined' is not assignable to type 'number'
    expect(toLocalisedDecimalPlaces({ num: undefined })).toBe('X');
  });
  it('should handle infinite numbers', () => {
    expect(toLocalisedDecimalPlaces({ num: Infinity })).toBe('X');
  });
  it('should handle negative infinite numbers', () => {
    expect(toLocalisedDecimalPlaces({ num: -Infinity })).toBe('X');
  });
  it('should handle NaN', () => {
    expect(toLocalisedDecimalPlaces({ num: NaN })).toBe('X');
  });
});
