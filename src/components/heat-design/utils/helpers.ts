import { GetEnergySolutionResponse } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.solution.v1';
import { IntlShape } from 'react-intl';
import { ServerEnvironment } from 'server/server-environment';
import { UValue } from '../models/UValue';
import { RadiatorsGroupedBySize, RadiatorSize } from '../stores/HeatSourceStore';
import {
  AdjacentKind,
  Door,
  FloorProps,
  PROJECT_FABRIC_TYPES,
  ProjectFabricTypes,
  ProjectFallbackUValues,
  RadiatorData,
  Room,
  RoomFabricTypes,
  VentilationCalculationMethod,
  Window,
} from '../stores/types';
import { FabricHeatDesignResult } from '../stores/OutputsStore';
import { Locale } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { StandardHouseInputVentilationDesign } from '../stores/HouseInputsStore';
import {
  AirPermeabilityOption,
  DwellingExposureOption,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { ACPHStandardizedDefaults } from './averageAirChangePerHour';

export function toOneDecimalPlace(num: number): number {
  return Math.round(num * 100) / 100;
}

export function toOneDecimalPlaceText(num: number): string {
  return (Math.round(num * 100) / 100).toFixed(1);
}

export function toTwoDecimalPlaces(num: number): number {
  return Math.round(num * 100) / 100;
}

export function toLocalisedDecimalPlaces({
  num,
  decimalPlaces = 2,
  locale = 'en-GB',
  optional = false,
}: {
  num: number;
  decimalPlaces?: number;
  locale?: string;
  optional?: boolean;
}): string {
  if ((optional && num === undefined) || !Number.isFinite(num) || Number.isNaN(num)) {
    return 'X';
  }

  return num.toLocaleString(locale, { maximumFractionDigits: decimalPlaces, minimumFractionDigits: decimalPlaces });
}

export function optionalTwoDecimalPlaces(num?: number): string {
  if (num === undefined || num === Infinity) {
    return 'X';
  }
  return toTwoDecimalPlaces(num).toString();
}

export function optionalToOneDecimalPlace(num?: number): string {
  if (num === undefined || num === Infinity) {
    return 'X';
  }
  return toOneDecimalPlaceText(num);
}

export function optionalToNoDecimalPlaces(num?: number): string {
  if (num === undefined || num === Infinity) {
    return 'X';
  }
  return Math.round(num).toString();
}

export function increaseFactorToPercentage(factor: number): number {
  return factor * 100 - 100;
}

export function titleCase(str: string): string {
  return str
    .toLowerCase()
    .split(/\s+/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

export function doorOrWindowIsIncludedInHeatLossCalculation(attachedItem: Door | Window, room: Room): boolean {
  const wallType = room.surfaces.walls.find((wall) => wall.uid === attachedItem.wallUID)?.surfaceType;
  return wallType === 'externalWalls';
}

export function filterEnabledSurfacesInRoom(room: Room): Room {
  return {
    ...room,
    surfaces: {
      ...room.surfaces,
      doors: room.surfaces.doors.filter((door) => doorOrWindowIsIncludedInHeatLossCalculation(door, room)),
      windows: room.surfaces.windows.filter((window) => doorOrWindowIsIncludedInHeatLossCalculation(window, room)),
    },
  };
}

export function filterEnabledSurfacesInRooms(room: Room[]): Room[] {
  return room.map((r) => filterEnabledSurfacesInRoom(r));
}

/**
 * @param val The value to check
 * @param min The minimum valid value
 * @param max The maximum valid value
 * @returns true if the value is within the min and max values (inclusive), false otherwise
 */
export function isWithinLimits(val: number | undefined, min: number, max: number): boolean {
  return val != null && val >= min && val <= max;
}

/**
 * Takes a list of coordinates where every other entry is a x coordinate and returns a list
 * of coordinates where the x and y values are grouped together.
 *
 * @param coordinateList A list of coordinates e.g. [1,2,3,4,5,6]
 * @returns A list of coordinate pairs e.g. [[1,2],[3,4],[5,6]]
 */
export function coordinateListToCoordinatePairs(coordinateList: number[]): [number, number][] {
  if (coordinateList.length % 2 !== 0) {
    throw new Error(`Coordinate input should be of an equal length but was of length ${coordinateList.length}`);
  }

  const coordinatePairs: [number, number][] = [];
  for (let i = 0; i < coordinateList.length; i += 2) {
    const x = coordinateList[i];
    const y = coordinateList[i + 1];

    if (x !== undefined && y !== undefined) {
      coordinatePairs.push([x, y]);
    }
  }
  return coordinatePairs;
}

export const getUrlQueryParameter = ({ key, value }: { key: string; value?: string }) => {
  const searchParams = new URLSearchParams(window.location.search);
  const valueOpt = () => {
    if (!value && searchParams.get(key) === 'false') {
      return false;
    }
    if (value === undefined) return true;
    if (!searchParams.get(key)) {
      return true;
    }
    return searchParams.get(key) === value;
  };
  return searchParams.has(key) && valueOpt();
};

type RoomFabricTypesWithoutWalls = Exclude<RoomFabricTypes, 'externalWalls' | 'internalWalls' | 'partyWalls'>;

export const aRoomHasUValueForFabricType = (fabricType: RoomFabricTypes, allRooms: Room[]): boolean => {
  if (!allRooms || allRooms.length === 0) return false;
  if (['externalWalls', 'internalWalls', 'partyWalls'].includes(fabricType)) {
    return allRooms.some((room) => room.surfaces.walls.some((w) => w.surfaceType === fabricType));
  }

  return allRooms.some((room) => room.surfaces[fabricType as RoomFabricTypesWithoutWalls].length > 0);
};

export function countBedrooms(rooms: Room[]): number {
  return rooms.filter((room) => room.roomType === 'Bedroom').length;
}

export function countBathrooms(rooms: Room[]): number {
  return rooms.filter((room) => room.roomType === 'Bathroom').length;
}

export const projectToSurfaceMap: { [key in ProjectFabricTypes]: RoomFabricTypes } = {
  foundation: 'floors',
  intermediateFloors: 'floors',
  roof: 'roofsOrCeilings',
  externalWalls: 'externalWalls',
  internalWalls: 'internalWalls',
  partyWalls: 'partyWalls',
  doors: 'doors',
  windows: 'windows',
  roofGlazings: 'roofGlazings',
};

export const getProjectFabricTypeForSurface = (
  surfaceType: RoomFabricTypes,
  floor: FloorProps,
  floors: FloorProps[],
): ProjectFabricTypes => {
  if (surfaceType === 'floors') {
    const lowestFloorNr = Math.min(...floors.map((f) => f.floorNr));
    return floor.floorNr === lowestFloorNr ? 'foundation' : 'intermediateFloors';
  }
  if (surfaceType === 'roofsOrCeilings') {
    const highestFloorNr = Math.max(...floors.map((f) => f.floorNr));
    // If top floor, return roof
    if (floor.floorNr === highestFloorNr) {
      return 'roof';
    }
    // Otherwise, return intermediate floor
    return 'intermediateFloors';
  }

  // Find the surface type in the projectToSurfaceMap and return the key
  return Object.entries(projectToSurfaceMap).find(([, surface]) => surface === surfaceType)?.[0] as ProjectFabricTypes;
};

export enum UValueSource {
  floorLevel = 'floorLevel',
  dwelling = 'dwelling',
  surface = 'surface',
  floorLevelAbove = 'floorLevelAbove',
}

export type UValueWithSource = { uValue: UValue; source: UValueSource };

// Look for a uValue in the floor and then project. Pick the first one found.
export const lookupUValueForSurfaceType = (
  surfaceType: RoomFabricTypes,
  projectFallbackValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
): UValueWithSource | undefined => {
  if (surfaceType === 'roofsOrCeilings') {
    const floorAbove = floors.find((f) => f.floorNr === floor.floorNr + 1);
    const currentLevelRoofUValue = floorAbove?.floorUValues?.floors;
    if (currentLevelRoofUValue) {
      return { uValue: currentLevelRoofUValue, source: UValueSource.floorLevelAbove };
    }
  }

  const floorLevelUValue = floor?.floorUValues?.[surfaceType];
  if (floorLevelUValue) {
    return { uValue: floorLevelUValue, source: UValueSource.floorLevel };
  }
  const projectSurfaceType = floor ? getProjectFabricTypeForSurface(surfaceType, floor, floors) : undefined;
  const projectUValue = projectSurfaceType ? projectFallbackValues?.[projectSurfaceType] : undefined;

  if (projectUValue) {
    return { uValue: projectUValue, source: UValueSource.dwelling };
  }

  return undefined;
};

export const aRoomHasUValueForProjectFabricType = (
  projectFabricType: ProjectFabricTypes,
  allRooms: Room[],
  allFloors: FloorProps[],
): boolean => {
  if (!allRooms || allRooms.length === 0) return false;

  if (projectFabricType === 'intermediateFloors') {
    return allFloors.length > 1;
  }

  const fabricType: RoomFabricTypes = projectToSurfaceMap[projectFabricType];

  if (['externalWalls', 'internalWalls', 'partyWalls'].includes(fabricType)) {
    return allRooms.some((room) => room.surfaces.walls.some((w) => w.surfaceType === fabricType));
  }
  return allRooms.some((room) => room.surfaces[fabricType as keyof typeof room.surfaces].length > 0);
};

export const projectUValuesDefaultsAreValid = (
  projectUValues: ProjectFallbackUValues,
  rooms: Room[],
  floors: FloorProps[],
) =>
  PROJECT_FABRIC_TYPES.filter((fabricType) => aRoomHasUValueForProjectFabricType(fabricType, rooms, floors)).filter(
    (fabricType) => !projectUValues[fabricType],
  ).length === 0;

export function sortFloorsTopToBottom(floors: FloorProps[]): FloorProps[] {
  return [...floors].sort((floorA, floorB) => floorB.floorNr - floorA.floorNr);
}

/**
 * Get the size classification of a radiator (Standard, Large, or Design).
 *
 * We take into account the largest dimension of radiators when grouping them
 * together. Note that in the UK (and maybe other countries), they use "length"
 * and "width" interchangeably to mean the same thing. This is because "length"
 * makes sense when looking at a floorplan from above, while "width" makes sense
 * when looking at a radiator at eye-level.
 */
export function getRadiatorSizeGroup(radiator: RadiatorData): RadiatorSize {
  const largestDimension = Math.max(radiator.width, radiator.height);
  if (largestDimension < 1600) {
    return RadiatorSize.STANDARD;
  }
  return RadiatorSize.LARGE;
  // "Design" radiators cannot be classified automatically at this time
}

export function groupRadiatorsBySize(radiators: RadiatorData[]): RadiatorsGroupedBySize {
  const groups: RadiatorsGroupedBySize = {
    [RadiatorSize.STANDARD]: [],
    [RadiatorSize.LARGE]: [],
    [RadiatorSize.DESIGN]: [],
  };
  return radiators.reduce((group, radiator) => {
    group[getRadiatorSizeGroup(radiator)].push(radiator);
    return group;
  }, groups);
}

export function getCustomerNameAndAddress(energySolution?: GetEnergySolutionResponse): {
  name: string;
  address: string;
} {
  const name =
    `${energySolution?.solution?.presentation?.customer?.firstName ?? ''} ${energySolution?.solution?.presentation?.customer?.lastName ?? ''}`.trim();
  const address =
    energySolution?.solution?.presentation?.location?.$case === 'exactAddress'
      ? energySolution.solution.presentation.location.exactAddress.formattedAddress
      : energySolution?.solution?.presentation?.location?.partialAddress.postalCode;
  return {
    name,
    address: address ?? '',
  };
}

export function getAdjacentName(
  fabricOutput: FabricHeatDesignResult,
  formatMessage: IntlShape['formatMessage'],
): string {
  if (fabricOutput.adjacentKind === undefined) {
    return '';
  }
  if (fabricOutput.adjacentKind === AdjacentKind.Room) {
    return fabricOutput.adjacentName ?? '';
  }
  return formatMessage({ id: `heatDesign.adjacentKind.${fabricOutput.adjacentKind}` });
}

export function getAdjacentLabel(
  fabricOutput: FabricHeatDesignResult,
  formatMessage: IntlShape['formatMessage'],
): string {
  const chunks: string[] = [fabricOutput.adjacentTemperature?.toString() ?? '-'];

  const adjacentName = getAdjacentName(fabricOutput, formatMessage);

  if (adjacentName.trim().length > 0) chunks.push(`(${adjacentName})`);

  return chunks.join(' ');
}

export function generateQuoteSummaryURL({
  serverEnvironment,
  locale,
  solutionId,
}: {
  serverEnvironment: ServerEnvironment;
  locale: string;
  solutionId?: string;
}) {
  return `${serverEnvironment.quoteWebUrl}/${locale}/${solutionId}/summary?mode=ro`;
}

export function mapRouterLocaleToHeatDesignLocale({ locale }: { locale: string }): Locale {
  if (locale === 'it') return Locale.LOCALE_IT;
  if (locale === 'de') return Locale.LOCALE_DE;
  return Locale.LOCALE_EN_GB;
}

export function getDefaultStandardVentilationInputs(
  overrides: Partial<StandardHouseInputVentilationDesign> = {},
): StandardHouseInputVentilationDesign {
  return {
    calculationMethod: VentilationCalculationMethod.STANDARD,
    airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
    airPermeabilityOverride: undefined,
    dwellingExposure: DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
    acphDefault: {
      type: 'standardized',
      standard: ACPHStandardizedDefaults.EN_12831,
    },
    ...overrides,
  };
}
