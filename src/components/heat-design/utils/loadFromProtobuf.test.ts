import {
  FabricType,
  HeatDesign,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import {
  mockHeatDesignDwelling,
  mockHeatDesignFloor,
  mockHeatDesignRoom,
  mockHeatDesignWall,
} from 'tests/utils/testUtils';
import { CountryCode } from 'utils/marketConfigurations';
import { UValues } from '../stores/types';
import { determineDeletedUValues, getDefaultWaterTemperatures, getUValuesForSolution } from './loadFromProtobuf';
import { UValue } from '../models/UValue';
import { WaterTemps } from '../stores/HeatSourceStore';

// The test data here is limited to floors and roofs (and the various variants
// of these that we have), to reduce the amount of clutter in this file.

const floorUValues: UValue[] = [
  new UValue('floors u-value', 1.4, '4'),
  new UValue('floors alternative u-value', 1.5, '5'),
];
const roofUValues: UValue[] = [
  new UValue('roof u-value', 2.4, '14'),
  new UValue('roof alternative u-value', 2.5, '15'),
];
const ceilingUValues: UValue[] = [
  new UValue('roofsOrCeilings u-value', 2.8, '18'),
  new UValue('roofsOrCeilings alternative u-value', 2.9, '19'),
];
const externalWallUValues: UValue[] = [
  new UValue('externalWalls u-value', 3.3, '21'),
  new UValue('externalWalls alternative u-value', 3.4, '22'),
];
const internalWallUValues: UValue[] = [
  new UValue('internalWalls u-value', 4.7, '31'),
  new UValue('internalWalls alternative u-value', 4.8, '32'),
];

const countryUValues: UValues = {
  // project-level fabrics
  foundation: floorUValues,
  intermediateFloors: floorUValues,
  roof: roofUValues,

  // room-level fabrics
  floors: floorUValues,
  roofsOrCeilings: [...roofUValues, ...ceilingUValues],

  // shared
  externalWalls: externalWallUValues,
  internalWalls: internalWallUValues,
  doors: [],
  partyWalls: [],
  roofGlazings: [],
  windows: [],
};

test('Deleted projectFabric u-values from the dwelling-level defaults should be detected', () => {
  const deletedUValues = determineDeletedUValues({} as UValues, countryUValues, {
    dwellingUValueDefaults: {
      defaultEntries: [
        {
          // This u-value is not in the country u-value list i.e. it has been deleted
          fabricType: FabricType.FABRIC_TYPE_FOUNDATION,
          uValue: {
            id: { value: '56789' },
            name: 'Some deleted foundation u-value',
            uValueSi: 1.23,
          },
        },
        {
          fabricType: FabricType.FABRIC_TYPE_JOIST_MATERIAL, // Intermediate floor
          uValue: {
            id: { value: floorUValues[1]?.id },
            name: floorUValues[1]?.name,
            uValueSi: floorUValues[1]?.value,
          },
        },
        {
          fabricType: FabricType.FABRIC_TYPE_ROOF,
          uValue: {
            id: { value: roofUValues[0]?.id },
            name: roofUValues[0]?.name,
            uValueSi: roofUValues[0]?.value,
          },
        },
      ],
    },
  } as HeatDesign);

  expect(deletedUValues.foundation).not.toBeUndefined();
  expect(deletedUValues.foundation).toHaveLength(1);
  expect(deletedUValues.foundation[0]?.id).toBe('56789');
  expect(deletedUValues.foundation[0]?.name).toBe('Some deleted foundation u-value');
  expect(deletedUValues.foundation[0]?.value).toBe(1.23);
  expect(deletedUValues.foundation[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.intermediateFloors).toBeUndefined();
  expect(deletedUValues.roof).toBeUndefined();
});

test('Deleted roomFabric u-values from the floor-level defaults should be detected', () => {
  const deletedUValues = determineDeletedUValues({} as UValues, countryUValues, {
    dwelling: {
      floors: [
        {
          floorUValueDefaults: {
            defaultEntries: [
              // It is not possible to set the floors or roofsOrCeilings u-values for a floor, as this is
              // calculated based on the dwelling-level defaults, depending on the floor's position. So,
              // we test here by using the externalWalls and internalWalls u-values.
              {
                fabricType: FabricType.FABRIC_TYPE_EXTERNAL_WALL,
                uValue: {
                  id: { value: externalWallUValues[1]?.id },
                  name: externalWallUValues[1]?.name,
                  uValueSi: externalWallUValues[1]?.value,
                },
              },
              {
                fabricType: FabricType.FABRIC_TYPE_INTERNAL_WALL,
                uValue: {
                  id: { value: '11114' },
                  name: 'Some deleted internalWalls u-value',
                  uValueSi: 0.19,
                },
              },
            ],
          },
        },
        {
          floorUValueDefaults: {
            defaultEntries: [
              // And we also test with multiple floors, this floor has a different internalWalls default
              // u-value (compared to the other floor) that has also been deleted.
              {
                fabricType: FabricType.FABRIC_TYPE_INTERNAL_WALL,
                uValue: {
                  id: { value: '55500' },
                  name: 'Some other deleted internalWalls u-value',
                  uValueSi: 0.76,
                },
              },
            ],
          },
        },
      ],
    },
  } as HeatDesign);

  expect(deletedUValues.externalWalls).toBeUndefined();

  expect(deletedUValues.internalWalls).not.toBeUndefined();
  expect(deletedUValues.internalWalls).toHaveLength(2);
  expect(deletedUValues.internalWalls[0]?.id).toBe('11114');
  expect(deletedUValues.internalWalls[0]?.name).toBe('Some deleted internalWalls u-value');
  expect(deletedUValues.internalWalls[0]?.value).toBe(0.19);
  expect(deletedUValues.internalWalls[0]?.metadata.isDeprecated).toBe(true);
  expect(deletedUValues.internalWalls[1]?.id).toBe('55500');
  expect(deletedUValues.internalWalls[1]?.name).toBe('Some other deleted internalWalls u-value');
  expect(deletedUValues.internalWalls[1]?.value).toBe(0.76);
  expect(deletedUValues.internalWalls[1]?.metadata.isDeprecated).toBe(true);
});

test('Deleted projectFabric-only u-values should be also be marked as deleted in the corresponding roomFabrics', () => {
  const deletedUValues = determineDeletedUValues({} as UValues, countryUValues, {
    dwellingUValueDefaults: {
      defaultEntries: [
        // These 3 fabric types are only in the projectFabric types, let's delete them
        {
          fabricType: FabricType.FABRIC_TYPE_FOUNDATION,
          uValue: {
            id: { value: '56789' },
            name: 'Some deleted foundation u-value',
            uValueSi: 1.23,
          },
        },
        {
          fabricType: FabricType.FABRIC_TYPE_JOIST_MATERIAL, // Intermediate floor
          uValue: {
            id: { value: '88990' },
            name: 'Some deleted intermediateFloors u-value',
            uValueSi: 2.34,
          },
        },
        {
          fabricType: FabricType.FABRIC_TYPE_ROOF,
          uValue: {
            id: { value: '11964' },
            name: 'Some deleted roof u-value',
            uValueSi: 3.45,
          },
        },
        // And this is a shared fabric type between the projectFabrics and roomFabrics
        {
          fabricType: FabricType.FABRIC_TYPE_INTERNAL_WALL,
          uValue: {
            id: { value: internalWallUValues[0]?.id },
            name: internalWallUValues[0]?.name,
            uValueSi: internalWallUValues[0]?.value,
          },
        },
      ],
    },
  } as HeatDesign);

  // So, first we would expect the deleted u-values to appear in the projectFabrics that they
  // were "deleted" from i.e. foundation, intermediateFloors and roof:
  expect(deletedUValues.foundation).not.toBeUndefined();
  expect(deletedUValues.foundation).toHaveLength(1);
  expect(deletedUValues.foundation[0]?.id).toBe('56789');
  expect(deletedUValues.foundation[0]?.name).toBe('Some deleted foundation u-value');
  expect(deletedUValues.foundation[0]?.value).toBe(1.23);
  expect(deletedUValues.foundation[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.intermediateFloors).not.toBeUndefined();
  expect(deletedUValues.intermediateFloors).toHaveLength(1);
  expect(deletedUValues.intermediateFloors[0]?.id).toBe('88990');
  expect(deletedUValues.intermediateFloors[0]?.name).toBe('Some deleted intermediateFloors u-value');
  expect(deletedUValues.intermediateFloors[0]?.value).toBe(2.34);
  expect(deletedUValues.intermediateFloors[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.roof).not.toBeUndefined();
  expect(deletedUValues.roof).toHaveLength(1);
  expect(deletedUValues.roof[0]?.id).toBe('11964');
  expect(deletedUValues.roof[0]?.name).toBe('Some deleted roof u-value');
  expect(deletedUValues.roof[0]?.value).toBe(3.45);
  expect(deletedUValues.roof[0]?.metadata.isDeprecated).toBe(true);

  // But we would now also expect these u-values to have been deleted from the corresponding
  // roomFabrics i.e. floors and roofsOrCeilings:
  expect(deletedUValues.floors).not.toBeUndefined();
  expect(deletedUValues.floors).toHaveLength(2);
  expect(deletedUValues.floors[0]?.id).toBe('56789');
  expect(deletedUValues.floors[0]?.name).toBe('Some deleted foundation u-value');
  expect(deletedUValues.floors[0]?.value).toBe(1.23);
  expect(deletedUValues.floors[0]?.metadata.isDeprecated).toBe(true);
  expect(deletedUValues.floors[1]?.id).toBe('88990');
  expect(deletedUValues.floors[1]?.name).toBe('Some deleted intermediateFloors u-value');
  expect(deletedUValues.floors[1]?.value).toBe(2.34);
  expect(deletedUValues.floors[1]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.roofsOrCeilings).not.toBeUndefined();
  expect(deletedUValues.roofsOrCeilings).toHaveLength(2);
  expect(deletedUValues.roofsOrCeilings[0]?.id).toBe('88990');
  expect(deletedUValues.roofsOrCeilings[0]?.name).toBe('Some deleted intermediateFloors u-value');
  expect(deletedUValues.roofsOrCeilings[0]?.value).toBe(2.34);
  expect(deletedUValues.roofsOrCeilings[0]?.metadata.isDeprecated).toBe(true);
  expect(deletedUValues.roofsOrCeilings[1]?.id).toBe('11964');
  expect(deletedUValues.roofsOrCeilings[1]?.name).toBe('Some deleted roof u-value');
  expect(deletedUValues.roofsOrCeilings[1]?.value).toBe(3.45);
  expect(deletedUValues.roofsOrCeilings[1]?.metadata.isDeprecated).toBe(true);

  // All other fabric types should be unaffected i.e. show no deletions
  expect(deletedUValues.doors).toBeUndefined();
  expect(deletedUValues.externalWalls).toBeUndefined();
  expect(deletedUValues.internalWalls).toBeUndefined();
  expect(deletedUValues.partyWalls).toBeUndefined();
  expect(deletedUValues.roofGlazings).toBeUndefined();
  expect(deletedUValues.windows).toBeUndefined();
});

test('Deleted roomFabric-only u-values should be also be marked as deleted in the corresponding projectFabrics', () => {
  const deletedUValues = determineDeletedUValues({} as UValues, countryUValues, {
    dwelling: {
      floors: [
        {
          floorUValueDefaults: {
            defaultEntries: [
              // These 2 fabric types are only in the roomFabrics types, let's delete them
              {
                fabricType: FabricType.FABRIC_TYPE_FLOOR,
                uValue: {
                  id: { value: '76378' },
                  name: 'Some deleted floors u-value',
                  uValueSi: 1.23,
                },
              },
              {
                fabricType: FabricType.FABRIC_TYPE_ROOF_OR_CEILING,
                uValue: {
                  id: { value: '10228' },
                  name: 'Some deleted roofsOrCeilings u-value',
                  uValueSi: 2.34,
                },
              },
              // And this is a shared fabric type between the projectFabrics and roomFabrics
              {
                fabricType: FabricType.FABRIC_TYPE_INTERNAL_WALL,
                uValue: {
                  id: { value: internalWallUValues[0]?.id },
                  name: internalWallUValues[0]?.name,
                  uValueSi: internalWallUValues[0]?.value,
                },
              },
            ],
          },
        },
        {
          floorUValueDefaults: {
            defaultEntries: [
              // And we also test with multiple floors, this floor has a different roofsOrCeilings
              // default u-value (compared to the other floor) that has also been deleted.
              {
                fabricType: FabricType.FABRIC_TYPE_ROOF_OR_CEILING,
                uValue: {
                  id: { value: '63520' },
                  name: 'Some other deleted roofsOrCeilings u-value',
                  uValueSi: 4.56,
                },
              },
            ],
          },
        },
      ],
    },
  } as HeatDesign);

  // So, first we would expect the deleted u-values to appear in the roomFabrics that they
  // were "deleted" from i.e. foundation, intermediateFloors and roof:
  expect(deletedUValues.floors).not.toBeUndefined();
  expect(deletedUValues.floors).toHaveLength(1);
  expect(deletedUValues.floors[0]?.id).toBe('76378');
  expect(deletedUValues.floors[0]?.name).toBe('Some deleted floors u-value');
  expect(deletedUValues.floors[0]?.value).toBe(1.23);
  expect(deletedUValues.floors[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.roofsOrCeilings).not.toBeUndefined();
  expect(deletedUValues.roofsOrCeilings).toHaveLength(2);
  expect(deletedUValues.roofsOrCeilings[0]?.id).toBe('10228');
  expect(deletedUValues.roofsOrCeilings[0]?.name).toBe('Some deleted roofsOrCeilings u-value');
  expect(deletedUValues.roofsOrCeilings[0]?.value).toBe(2.34);
  expect(deletedUValues.roofsOrCeilings[0]?.metadata.isDeprecated).toBe(true);
  expect(deletedUValues.roofsOrCeilings[1]?.id).toBe('63520');
  expect(deletedUValues.roofsOrCeilings[1]?.name).toBe('Some other deleted roofsOrCeilings u-value');
  expect(deletedUValues.roofsOrCeilings[1]?.value).toBe(4.56);
  expect(deletedUValues.roofsOrCeilings[1]?.metadata.isDeprecated).toBe(true);

  // But we would now also expect these u-values to have been deleted from the corresponding
  // projectFabrics i.e. foundation, intermediateFloors, roof
  expect(deletedUValues.foundation).not.toBeUndefined();
  expect(deletedUValues.foundation).toHaveLength(1);
  expect(deletedUValues.foundation[0]?.id).toBe('76378');
  expect(deletedUValues.foundation[0]?.name).toBe('Some deleted floors u-value');
  expect(deletedUValues.foundation[0]?.value).toBe(1.23);
  expect(deletedUValues.foundation[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.intermediateFloors).not.toBeUndefined();
  expect(deletedUValues.intermediateFloors).toHaveLength(1);
  expect(deletedUValues.intermediateFloors[0]?.id).toBe('76378');
  expect(deletedUValues.intermediateFloors[0]?.name).toBe('Some deleted floors u-value');
  expect(deletedUValues.intermediateFloors[0]?.value).toBe(1.23);
  expect(deletedUValues.intermediateFloors[0]?.metadata.isDeprecated).toBe(true);

  expect(deletedUValues.roof).not.toBeUndefined();
  expect(deletedUValues.roof).toHaveLength(2);
  expect(deletedUValues.roof[0]?.id).toBe('10228');
  expect(deletedUValues.roof[0]?.name).toBe('Some deleted roofsOrCeilings u-value');
  expect(deletedUValues.roof[0]?.value).toBe(2.34);
  expect(deletedUValues.roof[0]?.metadata.isDeprecated).toBe(true);
  expect(deletedUValues.roof[1]?.id).toBe('63520');
  expect(deletedUValues.roof[1]?.name).toBe('Some other deleted roofsOrCeilings u-value');
  expect(deletedUValues.roof[1]?.value).toBe(4.56);
  expect(deletedUValues.roof[1]?.metadata.isDeprecated).toBe(true);

  // All other fabric types should be unaffected i.e. show no deletions
  expect(deletedUValues.doors).toBeUndefined();
  expect(deletedUValues.externalWalls).toBeUndefined();
  expect(deletedUValues.internalWalls).toBeUndefined();
  expect(deletedUValues.partyWalls).toBeUndefined();
  expect(deletedUValues.roofGlazings).toBeUndefined();
  expect(deletedUValues.windows).toBeUndefined();
});

test('Deprecated values that are not used in the project should be excluded', () => {
  const uValuesWithDeprecatedValues = { ...countryUValues };
  uValuesWithDeprecatedValues.doors.push(new UValue('Deprecated door', 5, 'deprecated-1', { isDeprecated: true }));
  uValuesWithDeprecatedValues.foundation.push(
    new UValue('Deprecated foundation', 5, 'deprecated-2', { isDeprecated: true }),
  );
  uValuesWithDeprecatedValues.externalWalls.push(
    new UValue('Deprecated external wall', 5, 'deprecated-3', { isDeprecated: true }),
  );

  const allUValues = getUValuesForSolution(
    {
      id: { value: '' },
      dataSourceReferences: [],
      dwellingUValueDefaults: { defaultEntries: [] },
      auxiliaryData: { installationCommentForCustomer: '' },
      customUValues: [],
      climate: {
        baseOutdoorDesignTemperatureCelsius: 15,
        heatingDegreeDays: 60,
        localAnnualAverageExternalAirTemperatureCelsius: 14,
        adjustedOutdoorDesignTemperatureCelsius: 14,
      },
      dwelling: {
        ...mockHeatDesignDwelling('dwelling-1'),
        floors: [
          {
            ...mockHeatDesignFloor('floor-1'),
          },
        ],
      },
    },
    countryUValues,
  );

  expect(allUValues.doors.filter((x) => x.metadata.isDeprecated).length).toEqual(0);
  expect(allUValues.foundation.filter((x) => x.metadata.isDeprecated).length).toEqual(0);
  expect(allUValues.externalWalls.filter((x) => x.metadata.isDeprecated).length).toEqual(0);
});

test('Deprecated values that are used in the project should be included', () => {
  const uValuesWithDeprecatedValues = { ...countryUValues };
  uValuesWithDeprecatedValues.doors.push(new UValue('Deprecated door', 5, 'deprecated-door', { isDeprecated: true }));
  uValuesWithDeprecatedValues.floors.push(
    new UValue('Deprecated floor', 5, 'deprecated-floor', { isDeprecated: true }),
  );
  uValuesWithDeprecatedValues.externalWalls.push(
    new UValue('Deprecated external wall', 5, 'deprecated-wall', { isDeprecated: true }),
  );
  uValuesWithDeprecatedValues.roof.push(new UValue('Deprecated roof', 5, 'deprecated-roof', { isDeprecated: true }));

  const allUValues = getUValuesForSolution(
    {
      id: { value: '' },
      dataSourceReferences: [],
      dwellingUValueDefaults: { defaultEntries: [] },
      auxiliaryData: { installationCommentForCustomer: '' },
      customUValues: [],
      climate: {
        baseOutdoorDesignTemperatureCelsius: 15,
        heatingDegreeDays: 60,
        localAnnualAverageExternalAirTemperatureCelsius: 14,
        adjustedOutdoorDesignTemperatureCelsius: 14,
      },
      dwelling: {
        ...mockHeatDesignDwelling('dwelling-1'),
        floors: [
          {
            ...mockHeatDesignFloor('floor-1'),
            rooms: [
              {
                ...mockHeatDesignRoom('room-1'),
                floorSurfaceUValue: {
                  id: { value: 'deprecated-floor' },
                  name: 'Deprecated floor',
                  uValueSi: 5,
                },
                externalWalls: [
                  {
                    ...mockHeatDesignWall('wall-1'),
                    uValue: {
                      id: { value: 'deprecated-wall' },
                      name: 'Deprecated external wall',
                      uValueSi: 5,
                    },
                    doors: [
                      {
                        dataSourceReferences: [],
                        heightM: 0,
                        id: { value: 'door-1' },
                        imageMap: { points: [] },
                        widthM: 0,
                        uValue: {
                          id: { value: 'deprecated-door' },
                          name: 'Deprecated door',
                          uValueSi: 5,
                        },
                      },
                    ],
                  },
                ],
              },
            ],
            floorUValueDefaults: {
              defaultEntries: [
                {
                  fabricType: FabricType.FABRIC_TYPE_ROOF,
                  uValue: {
                    id: { value: 'deprecated-roof' },
                    name: 'Deprecated roof',
                    uValueSi: 5.0,
                  },
                },
              ],
            },
          },
        ],
      },
    } as HeatDesign,
    countryUValues,
  );

  expect(allUValues.doors.filter((x) => x.metadata.isDeprecated).length).toEqual(1);
  expect(allUValues.foundation.filter((x) => x.metadata.isDeprecated).length).toEqual(0);
  expect(allUValues.floors.filter((x) => x.metadata.isDeprecated).length).toEqual(1);
  expect(allUValues.internalWalls.filter((x) => x.metadata.isDeprecated).length).toEqual(0);
  expect(allUValues.externalWalls.filter((x) => x.metadata.isDeprecated).length).toEqual(1);
  expect(allUValues.roof.filter((x) => x.metadata.isDeprecated).length).toEqual(1);
});

test('Custom U-values should be marked as such', () => {
  const allUValues = getUValuesForSolution(
    {
      id: { value: '' },
      dataSourceReferences: [],
      dwellingUValueDefaults: { defaultEntries: [] },
      auxiliaryData: { installationCommentForCustomer: '' },
      climate: {
        baseOutdoorDesignTemperatureCelsius: 15,
        heatingDegreeDays: 60,
        localAnnualAverageExternalAirTemperatureCelsius: 14,
        adjustedOutdoorDesignTemperatureCelsius: 14,
      },
      customUValues: [
        {
          fabricType: FabricType.FABRIC_TYPE_FLOOR,
          uValue: {
            id: { value: '1' },
            name: 'Custom value',
            uValueSi: 12,
          },
        },
      ],
      dwelling: {
        ...mockHeatDesignDwelling('dwelling-1'),
        floors: [mockHeatDesignFloor('floor-1')],
      },
    } as HeatDesign,
    countryUValues,
  );

  expect(allUValues.floors.filter((u) => u.metadata.isCustom).length).toEqual(1);
});

it.each([
  [CountryCode.GB, { flowTemp: 45, deltaT: 5 }],
  [CountryCode.DE, { flowTemp: 45, deltaT: 10 }],
  [CountryCode.IT, { flowTemp: 45, deltaT: 5 }],
])(
  'Default water temperatures for %p are correct',
  (countryCode: CountryCode, { flowTemp, deltaT }: { flowTemp: number; deltaT: number }) => {
    expect(getDefaultWaterTemperatures({ countryCode })).toStrictEqual({
      flowTemp,
      flowReturnDeltaT: deltaT,
    } satisfies WaterTemps);
  },
);
