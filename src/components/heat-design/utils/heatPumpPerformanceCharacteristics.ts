import { api } from 'utils/api';
import { UUID } from '@aira/grpc-api/build/ts_out/index.com.aira.contract.common.v1';
import {
  HeatPumpOutdoorUnitTechnicalSpecification,
  HeatPumpOutdoorUnitTechnicalSpecification_ScopClimateZone,
  HeatPumpOutdoorUnitTechnicalSpecification_ScopForZone,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { toTwoDecimalPlaces } from './helpers';

const LOW_TEMPERATURE_WHERE_CURVE_FLATTENS = -25;
const HIGH_TEMPERATURE_WHERE_CURVE_FLATTENS = 10;

export interface CalculatedPerformance {
  maxHeatOutputWithoutElectricHeater: number;
  maxHeatOutputWithElectricHeater?: number;
  scop: number;
  bivalencePoint?: Bivalence;
}

interface Point {
  x: number;
  y: number;
}

function refPoints(sortedPoints: Point[], x: number): [Point, Point] {
  if (sortedPoints.length < 2) {
    throw new Error(`Not enough points to interpolate: ${sortedPoints.length}`);
  }
  if (x < sortedPoints[0]!.x) {
    return [sortedPoints[0]!, sortedPoints[1]!];
  }
  for (let i = 1; i < sortedPoints.length; i += 1) {
    const point = sortedPoints[i]!;
    if (x < point.x) {
      return [sortedPoints[i - 1]!, point];
    }
  }
  return [sortedPoints[sortedPoints.length - 2]!, sortedPoints[sortedPoints.length - 1]!];
}

function interpolate(sortedPoints: Array<{ x: number; y: number }>, x: number): number {
  const [lowerPoint, upperPoint] = refPoints(sortedPoints, x);
  const gradient = (upperPoint.y - lowerPoint.y) / (upperPoint.x - lowerPoint.x);
  return lowerPoint.y + gradient * (x - lowerPoint.x);
}

function sortPoints(points: Point[]): Point[] {
  return points.sort((a, b) => a.x - b.x);
}

function clamp(value: number, min: number, max: number) {
  return Math.max(min, Math.min(max, value));
}

function scopForAverageClimateZone(scopForClimateZone: HeatPumpOutdoorUnitTechnicalSpecification_ScopForZone[]) {
  return scopForClimateZone.find(
    (scop) => scop.climateZone === HeatPumpOutdoorUnitTechnicalSpecification_ScopClimateZone.SCOP_CLIMATE_ZONE_AVERAGE,
  )?.scop;
}

/**
 * This calculates/interpolates the SCOP of a heat pump at a given flow temperature. The way we do this have been agreed on
 * with our heat pump experts, see https://wiki.airahome.com/s/aira/p/performance-data-in-product-selection-aF2VcKRNJU
 */
export function calculateScop({
  flowTemp,
  technicalSpecification,
}: {
  flowTemp: number;
  technicalSpecification: HeatPumpOutdoorUnitTechnicalSpecification;
}) {
  const sortedPoints = sortPoints(
    technicalSpecification.performanceAtFlowTemperature.flatMap(({ flowTemperature, scopForClimateZone }) => {
      const scopAtFlowTemperature = scopForAverageClimateZone(scopForClimateZone);
      if (scopAtFlowTemperature === undefined) {
        return [];
      }
      return [
        {
          x: flowTemperature,
          y: scopAtFlowTemperature,
        },
      ];
    }),
  );
  if (sortedPoints.length < 2) {
    throw new Error('Could not find SCOP data for this heat pump at this climate zone');
  }
  return clamp(interpolate(sortedPoints, flowTemp), 0, sortedPoints[0]!.y);
}

/**
 * This calculates the max heat output of a heat pump at a given flow temperature. The way we do this have been agreed on
 * with our heat pump experts, see https://wiki.airahome.com/s/aira/p/performance-data-in-product-selection-aF2VcKRNJU
 *
 * @param odt The outdoor temperature
 * @param flowTemp The flow temperature of the heat pump
 * @param performanceData The performance data of the heat pump
 * @returns The max heat output of the heat pump at the given outdoor temperature and flow temperature (Watts)
 */
export function calculateMaxHeatOutput({
  odt,
  flowTemp,
  technicalSpecification,
}: {
  odt: number;
  flowTemp: number;
  technicalSpecification: HeatPumpOutdoorUnitTechnicalSpecification;
}) {
  const closestFlowRate = [...technicalSpecification.performanceAtFlowTemperature].sort(
    (a, b) => Math.abs(a.flowTemperature - flowTemp) - Math.abs(b.flowTemperature - flowTemp),
  )[0];
  if (closestFlowRate === undefined) {
    throw new Error('We cannot find the performance data for this flow rate');
  }
  const points = closestFlowRate.outputAtOutdoorTemperature.map(({ outdoorTemperatureCelcius, outputWatt }) => ({
    x: outdoorTemperatureCelcius,
    y: outputWatt,
  }));
  const clampedOdt = clamp(odt, LOW_TEMPERATURE_WHERE_CURVE_FLATTENS, HIGH_TEMPERATURE_WHERE_CURVE_FLATTENS);
  return clamp(interpolate(sortPoints(points), clampedOdt), 0, Infinity);
}
interface Bivalence {
  temperature: number;
  heatingOutput: number;
}
/**
 * Calculate the bivalence point of a heat pump and heating output in [KW]. The bivalence point is the outdoor temperature where the heat pump
 * can no longer provide enough heat to cover the heat loss of the building. This is the point where the heat pump
 * needs to be supplemented with another heat source, e.g. an electric heater.
 *
 * @param odt The outdoor temperature
 * @param flowTemp The flow temperature of the heat pump
 * @param heatLoss The heat loss of the building (W)
 * @param performanceData The performance data of the heat pump
 * @returns The bivalence point data, temperature of the heat pump rounded to two decimal places (°C) and the heating output in [kw]
 */
export const calculateBivalencePoint = ({
  odt,
  flowTemp,
  heatLoss,
  technicalSpecification,
}: {
  odt: number;
  flowTemp: number;
  heatLoss: number;
  technicalSpecification: HeatPumpOutdoorUnitTechnicalSpecification;
}): Bivalence | undefined => {
  if (heatLoss <= 0) {
    return undefined;
  }
  /**
   * Use binary search to find the intersection between these two lines:
   * Heat Loss Line: [ODT, Heat loss] -> [15, 0] (Interpolated)
   * Heat Pump Max Heat Output (calculateMaxHeatOutput)
   */

  const heatLossLine = (x: number) =>
    interpolate(
      sortPoints([
        { x: odt, y: heatLoss },
        { x: 15, y: 0 },
      ]),
      x,
    );

  // Binary search, starting with x = 15 and 30 steps backwards, limited to 10 iterations
  let intersectionIsBetweenPoints = false;
  let x2 = 15;
  let x1 = x2 - 30;
  let i = 0;
  do {
    // Check that the answer is between the points
    const heatLossLineX2 = heatLossLine(x2);
    const heatLossLineX1 = heatLossLine(x1);
    const maxHeatOutputX2 = calculateMaxHeatOutput({ odt: x2, flowTemp, technicalSpecification });
    const maxHeatOutputX1 = calculateMaxHeatOutput({ odt: x1, flowTemp, technicalSpecification });
    intersectionIsBetweenPoints = heatLossLineX1 >= maxHeatOutputX1 && heatLossLineX2 <= maxHeatOutputX2;

    // If the intersection is not between the points, move the search window 30 steps backwards
    if (!intersectionIsBetweenPoints) {
      x2 -= 30;
      x1 = x2 - 30;
    }
    i += 1;
  } while (!intersectionIsBetweenPoints && i < 10);

  if (!intersectionIsBetweenPoints) {
    return undefined;
  }

  // Binary search to find the intersection, limited to 100 iterations
  let x = (x1 + x2) / 2;
  let maxHeatOutput = calculateMaxHeatOutput({ odt: x, flowTemp, technicalSpecification });
  let heatLossLineY = heatLossLine(x);
  i = 0;
  while (Math.abs(maxHeatOutput - heatLossLineY) > 0.1 && i < 100) {
    if (maxHeatOutput >= heatLossLineY) {
      x2 = x;
    } else {
      x1 = x;
    }
    x = (x1 + x2) / 2;
    maxHeatOutput = calculateMaxHeatOutput({ odt: x, flowTemp, technicalSpecification });
    heatLossLineY = heatLossLine(x);
    i += 1;
  }

  // x is the actual bivalence point ,Y is the Kw range
  const bivalencePoint = {
    temperature: toTwoDecimalPlaces(x),
    heatingOutput: toTwoDecimalPlaces(heatLossLineY),
  };

  return i >= 100 ? undefined : bivalencePoint;
};

function getElectricHeaterMaxOutput(compatibilityGroup?: string): number | undefined {
  if (compatibilityGroup && compatibilityGroup.toLowerCase().includes('aira')) {
    // Deduced using the Installation Manual_Aira Indoor Unit_HPI-AO-XXX-1.0_Rev1.5, Section 7.14 Immersion Heating variants
    return 6000;
  }
  return undefined;
}

export const calculatePerformance = (
  odt: number,
  flowTemp: number,
  heatLoss: number,
  technicalSpecification: HeatPumpOutdoorUnitTechnicalSpecification,
  compatibilityGroup: string | undefined,
): CalculatedPerformance => {
  const maxHeatOutputWithoutElectricHeater = calculateMaxHeatOutput({
    odt,
    flowTemp,
    technicalSpecification,
  });
  const electricHeaterMaxOutput = getElectricHeaterMaxOutput(compatibilityGroup);
  const scop = calculateScop({ flowTemp, technicalSpecification });
  const bivalencePoint = calculateBivalencePoint({
    odt,
    flowTemp,
    technicalSpecification,
    heatLoss,
  });

  return {
    maxHeatOutputWithoutElectricHeater,
    scop,
    bivalencePoint,
    ...(electricHeaterMaxOutput && {
      maxHeatOutputWithElectricHeater: maxHeatOutputWithoutElectricHeater + electricHeaterMaxOutput,
    }),
  };
};

export function useTechnicalSpecifications() {
  const technicalSpecifications = api.AiraBackend.getTechnicalSpecifications.useQuery(undefined, {
    refetchOnMount: false,
  });

  return technicalSpecifications.data?.productTechnicalSpecifications;
}

export function useHeatPumpTechnicalSpecifications(technicalSpecificationId?: UUID) {
  const technicalSpecifications = useTechnicalSpecifications();
  if (!technicalSpecificationId || technicalSpecifications === undefined) {
    return undefined;
  }

  const spec = technicalSpecifications.find((s) => s.id?.value === technicalSpecificationId.value);

  return spec?.details && spec.details.$case === 'heatPumpOutdoorUnitTechnicalSpecification'
    ? spec.details.heatPumpOutdoorUnitTechnicalSpecification
    : undefined;
}
