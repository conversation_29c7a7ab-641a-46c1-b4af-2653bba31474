import {
  RoomType as ProtoRoomType,
  OpenFlueType as ProtoOpenFlueType,
  BelowFloorType as ProtoBelowFloorType,
  AboveCeilingType as ProtoAboveCeilingType,
  FabricType,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { RoomType, OpenFlueType, FabricTypes, BelowFloorType, AdjacentKind, SpaceAboveType } from '../stores/types';

// Fabrics
const fabricMap: [FabricType, FabricTypes][] = [
  [FabricType.FABRIC_TYPE_FOUNDATION, 'foundation'],
  [FabricType.FABRIC_TYPE_JOIST_MATERIAL, 'intermediateFloors'],
  [FabricType.FABRIC_TYPE_ROOF, 'roof'],
  [FabricType.FABRIC_TYPE_FLOOR, 'floors'],
  [FabricType.FABRIC_TYPE_ROOF_OR_CEILING, 'roofsOrCeilings'],
  [FabricType.FABRIC_TYPE_EXTERNAL_WALL, 'externalWalls'],
  [FabricType.FABRIC_TYPE_INTERNAL_WALL, 'internalWalls'],
  [FabricType.FABRIC_TYPE_PARTY_WALL, 'partyWalls'],
  [FabricType.FABRIC_TYPE_DOOR, 'doors'],
  [FabricType.FABRIC_TYPE_WINDOW, 'windows'],
  [FabricType.FABRIC_TYPE_ROOF_GLAZING, 'roofGlazings'],
];

const protoToFabricMap = new Map(fabricMap);
const fabricToProtoMap = new Map(fabricMap.map(([proto, domain]) => [domain, proto]));

export function mapFabricTypeFromProtobuf(fabricType: FabricType): FabricTypes | undefined {
  return protoToFabricMap.get(fabricType);
}

export function mapFabricTypeToProtobuf(fabricType: FabricTypes): FabricType {
  return fabricToProtoMap.get(fabricType) ?? FabricType.FABRIC_TYPE_UNSPECIFIED;
}

// Rooms

const roomMap: [ProtoRoomType, RoomType][] = [
  [ProtoRoomType.ROOM_TYPE_BATHROOM, 'Bathroom'],
  [ProtoRoomType.ROOM_TYPE_BEDROOM_INCLUDING_EN_SUITE_BATHROOM, 'Bedroom, including en suite bathroom'],
  [ProtoRoomType.ROOM_TYPE_BEDROOM, 'Bedroom'],
  [ProtoRoomType.ROOM_TYPE_BEDROOM_STUDY, 'Bedroom/study'],
  [ProtoRoomType.ROOM_TYPE_KITCHEN, 'Kitchen'],
  [ProtoRoomType.ROOM_TYPE_TOILET, 'Toilet'],
  [ProtoRoomType.ROOM_TYPE_BEDSITTING_ROOM, 'Bedsitting room'],
  [ProtoRoomType.ROOM_TYPE_BREAKFAST_ROOM, 'Breakfast room'],
  [ProtoRoomType.ROOM_TYPE_CLOAKROOM_WC, 'Cloakroom/WC'],
  [ProtoRoomType.ROOM_TYPE_DINING_ROOM, 'Dining room'],
  [ProtoRoomType.ROOM_TYPE_DRESSING_ROOM, 'Dressing room'],
  [ProtoRoomType.ROOM_TYPE_FAMILY_BREAKFAST_ROOM, 'Family/breakfast room'],
  [ProtoRoomType.ROOM_TYPE_GAMES_ROOM, 'Games room'],
  [ProtoRoomType.ROOM_TYPE_HALL, 'Hall'],
  [ProtoRoomType.ROOM_TYPE_INTERNAL_ROOM_OR_CORRIDOR, 'Internal room or corridor'],
  [ProtoRoomType.ROOM_TYPE_LANDING, 'Landing'],
  [ProtoRoomType.ROOM_TYPE_LIVING_ROOM, 'Living room'],
  [ProtoRoomType.ROOM_TYPE_LOUNGE_SITTING_ROOM, 'Lounge/sitting room'],
  [ProtoRoomType.ROOM_TYPE_SHOWER_ROOM, 'Shower room'],
  [ProtoRoomType.ROOM_TYPE_STORE_ROOM, 'Store room'],
  [ProtoRoomType.ROOM_TYPE_STUDY, 'Study'],
  [ProtoRoomType.ROOM_TYPE_UTILITY_ROOM, 'Utility room'],
  [ProtoRoomType.ROOM_TYPE_OTHER, 'Other'],
  [ProtoRoomType.ROOM_TYPE_UNSPECIFIED, 'Other'],
  [ProtoRoomType.UNRECOGNIZED, 'Other'],
];

const protoToRoomMap = new Map(roomMap);
const roomToProtoMap = new Map(roomMap.map(([proto, domain]) => [domain, proto]));

export function mapRoomTypeFromProtobuf(roomType: ProtoRoomType): RoomType {
  return protoToRoomMap.get(roomType) ?? 'Other';
}

export function mapRoomTypeToProtobuf(roomType: RoomType): ProtoRoomType {
  return roomToProtoMap.get(roomType) ?? ProtoRoomType.ROOM_TYPE_UNSPECIFIED;
}

// Open flues

const openFlueMap: [ProtoOpenFlueType, OpenFlueType][] = [
  [ProtoOpenFlueType.OPEN_FLUE_TYPE_NONE, 'none'],
  [ProtoOpenFlueType.OPEN_FLUE_TYPE_UNSPECIFIED, 'none'],
  [ProtoOpenFlueType.OPEN_FLUE_TYPE_YES_WITHOUT_THROAT_RESTRICTOR, 'Yes (without throat restrictor)'],
  [ProtoOpenFlueType.OPEN_FLUE_TYPE_YES_WITH_THROAT_RESTRICTOR, 'Yes (with throat restrictor)'],
];

const protoToOpenFlueMap = new Map(openFlueMap);
const openFlueToProtoMap = new Map(openFlueMap.map(([proto, domain]) => [domain, proto]));

export function mapOpenFlueTypeFromProtobuf(openFlueType: ProtoOpenFlueType): OpenFlueType {
  return protoToOpenFlueMap.get(openFlueType) ?? 'none';
}

export function mapOpenFlueTypeToProtobuf(openFlueType: OpenFlueType): ProtoOpenFlueType {
  return openFlueToProtoMap.get(openFlueType) ?? ProtoOpenFlueType.OPEN_FLUE_TYPE_UNSPECIFIED;
}

// Below floor
const belowFloorMap: [ProtoBelowFloorType, BelowFloorType][] = [
  [ProtoBelowFloorType.BELOW_FLOOR_TYPE_HEATED_ROOM, AdjacentKind.Heated],
  [ProtoBelowFloorType.BELOW_FLOOR_TYPE_UNHEATED_ROOM, AdjacentKind.Unheated],
  [ProtoBelowFloorType.BELOW_FLOOR_TYPE_SOLID, AdjacentKind.SolidFloor],
  [ProtoBelowFloorType.BELOW_FLOOR_TYPE_SUSPENDED, AdjacentKind.SuspendedFloor],
];

const protoToBelowFloorMap = new Map(belowFloorMap);
const belowFloorToProtoMap = new Map(belowFloorMap.map(([proto, domain]) => [domain, proto]));

export function mapBelowFloorFromProtobuf(protoBelowFloor: ProtoBelowFloorType): BelowFloorType | undefined {
  if (protoBelowFloor === ProtoBelowFloorType.UNRECOGNIZED) {
    throw new Error(`Unrecognized below floor type: ${protoBelowFloor}`);
  }
  return protoToBelowFloorMap.get(protoBelowFloor);
}

export function mapBelowFloorToProtobuf(belowFloor?: BelowFloorType): ProtoBelowFloorType {
  if (!belowFloor) {
    return ProtoBelowFloorType.BELOW_FLOOR_TYPE_UNSPECIFIED;
  }
  return belowFloorToProtoMap.get(belowFloor) ?? ProtoBelowFloorType.UNRECOGNIZED;
}

// Above ceiling
const aboveCeilingMap: [ProtoAboveCeilingType, SpaceAboveType][] = [
  [ProtoAboveCeilingType.ABOVE_CEILING_TYPE_HEATED_ROOM, AdjacentKind.Heated],
  [ProtoAboveCeilingType.ABOVE_CEILING_TYPE_OUTSIDE, AdjacentKind.Outside],
  [ProtoAboveCeilingType.ABOVE_CEILING_TYPE_UNHEATED_ROOM, AdjacentKind.Unheated],
];

const protoToAboveCeilingMap = new Map(aboveCeilingMap);
const aboveCeilingToProtoMap = new Map(aboveCeilingMap.map(([proto, domain]) => [domain, proto]));

export function mapAboveCeilingTypeFromProtobuf(
  protoAboveCeilingType: ProtoAboveCeilingType,
): SpaceAboveType | undefined {
  if (protoAboveCeilingType === ProtoAboveCeilingType.UNRECOGNIZED) {
    throw new Error(`Unrecognized below floor type: ${protoAboveCeilingType}`);
  }
  return protoToAboveCeilingMap.get(protoAboveCeilingType);
}

export function mapAboveCeilingTypeToProtobuf(aboveCeilingType?: SpaceAboveType): ProtoAboveCeilingType {
  if (!aboveCeilingType) {
    return ProtoAboveCeilingType.ABOVE_CEILING_TYPE_UNSPECIFIED;
  }
  return aboveCeilingToProtoMap.get(aboveCeilingType) ?? ProtoAboveCeilingType.UNRECOGNIZED;
}
