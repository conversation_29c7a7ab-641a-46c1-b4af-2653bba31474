import { CountryCode } from 'utils/marketConfigurations';
import { getHotWaterEnergyDemand } from 'utils/calculations';
import type { HeatPumpPackages } from 'components/quotation/stores/HeatPumpPackageStore';
import { DetailedHeatPumpOutdoorUnit, HeatPumpIndoorUnit, HeatPumpOutdoorUnit } from 'types/types';
import { ProductTechnicalSpecification } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import {
  DAYS_PER_YEAR,
  THERMAL_BRIDGING_UVALUE_ADJUSTMENT,
  AIR_CHANGE_FACTOR,
  externalWallAreaAdjustmentFactor,
  COLD_WATER_TEMPERATURE,
  TEMPERATURE_ADJUSTMENT_EXPOSED_LOCATION,
  AIR_PROPERTY_FACTOR,
  VOLUME_RATIO_BETWEEN_ROOM_AND_ZONE,
  CUBIC_METRES_IN_LITRE,
  SAP_INFILTRATION_TO_AIR_PERMEABILITY_RATIO,
  SECONDS_IN_HOUR,
} from '../constants';
import { UValue } from '../models/UValue';
import { UndefinedUValue } from '../models/UndefinedUValue';
import {
  FabricHeatDesignResult,
  FloorHeatDesignResult,
  HeatDesignResult,
  HeatLossResult,
  OutdoorUnitIds,
  RoomHeatDesignResult,
  WallAttachedFabricHeatDesignResult,
} from '../stores/OutputsStore';
import {
  type Room,
  RoomFabric,
  Door,
  Window,
  Floor,
  RoofOrCeiling,
  FloorProps,
  SurfaceFallbackUValues,
  Wall,
  InternalWall,
  RoofGlazing,
  ProjectFallbackUValues,
  ExternalWall,
  AdjacentKind,
  RoomType,
  VENTILATION_EXPOSURE_VALUES,
  VentilationCalculationMethod,
} from '../stores/types';
import type { HouseInputVentilationDesign, StandardHouseInputVentilationDesign } from '../stores/HouseInputsStore';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { getAverageAirChangesPerHour } from './averageAirChangePerHour';
import { countBedrooms, doorOrWindowIsIncludedInHeatLossCalculation, lookupUValueForSurfaceType } from './helpers';
import {
  calculateNewHeatOutput,
  calculateRadiatorDetailsForRoom,
  getUnderfloorHeatingFlowRate,
} from './radiatorHelpers';
import { getDesignRoomTemp, getUnheatedRoomTemperature } from './heatCalculations';
import { calculatePerformance } from './heatPumpPerformanceCharacteristics';
import { ClimateDataStore } from '../stores/ClimateDataStore';

// References are described in docs/REFERENCES.md.

/**
 * Calculate roof glazing area
 */
export const roofGlazingArea = (glazing: RoofGlazing) => (glazing.length ?? 0) * (glazing.width ?? 0);

/**
 * Calculate roof area without glazings
 *
 * @param roofArea
 * @param glazings
 * @returns roof area without glazings
 */
export const areaAfterGlazing = (roofArea: RoofOrCeiling['area'], glazings: RoofGlazing[]) =>
  glazings.reduce((acc, curr) => acc - roofGlazingArea(curr), roofArea);

export const getWallSoilPercentage = (wall: ExternalWall, floor?: FloorProps): number => {
  return wall.soilPercentage ?? floor?.soilPercentageDefault ?? 0;
};

/**
 * We can work backwards from the simple method
 * to figure out the corresponding ACPH.
 *
 * The equation becomes:
 * N = Q_v / (V * f_air * ΔT *)
 * where
 * N = air changes per hour (ACH)
 * Q_v = room/dwelling ventilation heat loss (W)
 * V = room/dwelling volume (m³)
 * f_air = air property factor (Constant: 0.33)
 * f_room/zone = volume flow ratio between room and zone
 * ΔT = insideOutsideTempDiff (K or °C difference)
 *
 * @param ventilationHeatLoss the ventilation heat loss of the space
 */
export function getAirChangesPerHourFromSimpleVentilationHeatLoss({
  ventilationHeatLoss,
  volumeInCubicMeters,
  insideOutsideTempDiff,
}: {
  ventilationHeatLoss: number;
  volumeInCubicMeters: number;
  insideOutsideTempDiff: number;
}): number {
  return ventilationHeatLoss / (volumeInCubicMeters * AIR_CHANGE_FACTOR * insideOutsideTempDiff);
}

export const getSimpleRoomVentilationHeatLossPerDegree = ({
  roomVolumeInCubicMeters,
  avgAirChangesPerHour,
  airPropertyFactor,
  ratioBetweenRoomAndZone,
}: {
  roomVolumeInCubicMeters: number;
  avgAirChangesPerHour: number;
  airPropertyFactor: number;
  ratioBetweenRoomAndZone: number;
}): number => {
  return roomVolumeInCubicMeters * avgAirChangesPerHour * airPropertyFactor * ratioBetweenRoomAndZone;
};

export const getInfiltrationRoomVentilationHeatLossPerDegree = ({
  envelopeAirPermeabilityAt50Pa,
  roomExternalEnvelopeAreaInSquareMeters,
  dwellingExternalEnvelopeAreaInSquareMeters,
  volumeFlowFactor,
  airPropertyFactor,
}: {
  envelopeAirPermeabilityAt50Pa: number;
  roomExternalEnvelopeAreaInSquareMeters: number;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  volumeFlowFactor: number;
  airPropertyFactor: number;
}): number => {
  const zoneInfiltrationVolumeFlow =
    envelopeAirPermeabilityAt50Pa * dwellingExternalEnvelopeAreaInSquareMeters * volumeFlowFactor;
  const roomInfiltrationVolumeFlow =
    zoneInfiltrationVolumeFlow * (roomExternalEnvelopeAreaInSquareMeters / dwellingExternalEnvelopeAreaInSquareMeters);

  return roomInfiltrationVolumeFlow * airPropertyFactor;
};

/**
 * Calculate the general formula of a ventilation heat loss, which refers to the kind of heat
 * loss that is due to the exchange of air (such as actual ventilation, or someone opening a door).
 *
 * Reference: DHDG2021, section 3.4.1, Equation 1
 *
 * @param {number} param0.roomVolumeInCubicMeters - The volume of the room in cubic meters (V).
 * @param {number} param0.avgAirChangesPerHour - The average number of air changes per hou, also called ACH (N).
 * @param {number} param0.insideOutsideTempDiff - The difference in temperature, T{in} - T{out} where T{in} is the indoor temperature and T{out} is the outdoor temperature, both expressed in Celcius/Kelvin.
 * @returns {number} The ventilation heat loss Q{V} expressed in W.
 */
export const getSimpleRoomVentilationHeatLoss = ({
  roomVolumeInCubicMeters,
  avgAirChangesPerHour,
  insideOutsideTempDiff,
}: {
  roomVolumeInCubicMeters: number;
  avgAirChangesPerHour: number;
  insideOutsideTempDiff: number;
}): number =>
  getSimpleRoomVentilationHeatLossPerDegree({
    roomVolumeInCubicMeters,
    avgAirChangesPerHour,
    airPropertyFactor: AIR_CHANGE_FACTOR, // Keeping the old factor to not change existing results
    ratioBetweenRoomAndZone: 1, // We don't use the zone method here
  }) * insideOutsideTempDiff;

export const getStandardRoomVentilationHeatLossPerDegree = ({
  roomVolumeInCubicMeters,
  avgAirChangesPerHour,
  roomExternalEnvelopeAreaInSquareMeters,
  dwellingExternalEnvelopeAreaInSquareMeters,
  envelopeAirPermeabilityAt50Pa,
  volumeFlowFactor,
}: {
  roomVolumeInCubicMeters: number;
  avgAirChangesPerHour: number;
  roomExternalEnvelopeAreaInSquareMeters: number;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  envelopeAirPermeabilityAt50Pa: number;
  volumeFlowFactor: number;
}): number => {
  const infiltrationComponent = getInfiltrationRoomVentilationHeatLossPerDegree({
    envelopeAirPermeabilityAt50Pa,
    roomExternalEnvelopeAreaInSquareMeters,
    dwellingExternalEnvelopeAreaInSquareMeters,
    volumeFlowFactor,
    airPropertyFactor: AIR_PROPERTY_FACTOR,
  });

  const simpleComponent = getSimpleRoomVentilationHeatLossPerDegree({
    roomVolumeInCubicMeters,
    avgAirChangesPerHour,
    airPropertyFactor: AIR_PROPERTY_FACTOR,
    ratioBetweenRoomAndZone: VOLUME_RATIO_BETWEEN_ROOM_AND_ZONE,
  });

  return Math.max(infiltrationComponent, simpleComponent);
};

/**
 * Ventilation heat loss standard method (EN12831-1:2017, section 6.3.3.3.1, Equation 16).
 * We decided to use the zone algorithm for rooms.
 *
 * Note: This algorithm is a simplified version of the standard method.
 * For example, we do not calculate the external air volume flow through ATDs
 * and we assume many of the variables to be zero which removes some components.
 */
export const getStandardRoomVentilationHeatLoss = ({
  roomVolumeInCubicMeters,
  avgAirChangesPerHour,
  insideOutsideTempDiff,
  roomExternalEnvelopeAreaInSquareMeters,
  dwellingExternalEnvelopeAreaInSquareMeters,
  envelopeAirPermeabilityAt50Pa,
  volumeFlowFactor,
}: {
  roomVolumeInCubicMeters: number;
  avgAirChangesPerHour: number;
  insideOutsideTempDiff: number;
  roomExternalEnvelopeAreaInSquareMeters: number;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  envelopeAirPermeabilityAt50Pa: number;
  volumeFlowFactor: number;
}): number => {
  const ventilationHeatLossPerDegree = getStandardRoomVentilationHeatLossPerDegree({
    roomVolumeInCubicMeters,
    avgAirChangesPerHour,
    roomExternalEnvelopeAreaInSquareMeters,
    dwellingExternalEnvelopeAreaInSquareMeters,
    envelopeAirPermeabilityAt50Pa,
    volumeFlowFactor,
  });
  return ventilationHeatLossPerDegree * insideOutsideTempDiff;
};

export const getStandardRoomVentilationEnergyDemand = ({
  roomVolumeInCubicMeters,
  avgAirChangesPerHour,
  degreeDays,
  roomExternalEnvelopeAreaInSquareMeters,
  dwellingExternalEnvelopeAreaInSquareMeters,
  envelopeAirPermeabilityAt50Pa,
  volumeFlowFactor,
}: {
  roomVolumeInCubicMeters: number;
  avgAirChangesPerHour: number;
  degreeDays: number;
  roomExternalEnvelopeAreaInSquareMeters: number;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  envelopeAirPermeabilityAt50Pa: number;
  volumeFlowFactor: number;
}): number => {
  const ventilationHeatLossPerDegree = getStandardRoomVentilationHeatLossPerDegree({
    roomVolumeInCubicMeters,
    avgAirChangesPerHour,
    roomExternalEnvelopeAreaInSquareMeters,
    dwellingExternalEnvelopeAreaInSquareMeters,
    envelopeAirPermeabilityAt50Pa,
    volumeFlowFactor,
  });

  return ventilationHeatLossPerDegree * degreeDays * (24 / 1000);
};

/**
 * Calculate the building envelope air permeability at 50 Pa based on the selected standard.
 *
 * Implementation rules (initial version):
 *  - EN12831 option: fixed value 12 (EN12831-1:2017, section B.2.15, table B.9)
 *  - CIBSE option: derive from CIBSE Guide Table 1.7 whole-building ventilation rates (l/s) and
 *    adjust for higher occupancy, then convert to air permeability at 50 Pa.
 *      * Table (bedrooms -> l/s): 1:13, 2:17, 3:21, 4:25, 5:29.
 *      * The table does not specify values for >5 bedrooms. Therefore, we extrapolate +4 l/s per extra bedroom.
 *      * Default occupancy assumed by the table: 2 occupants in main bedroom + 1 in every other bedroom.
 *        If actual occupants exceed this, add 4 l/s per additional occupant.
 *      * The minimum ventilation rate is 0.3 l/s per m² of internal floor area.
 *      * Conversion formula (provided by MCS) in shortened form:
 *          AP50 = Q(l/s) * 72 / A_e
 *        where:
 *          - 72 is a compound factor = 20 * 3600 * 0.001
 *              * 20  -> relationship factor from air infiltration rate to air permeability at 50 Pa
 *              * 3600 -> seconds per hour, converting l/s to l/h
 *              * 0.001 -> cubic metres per litre, converting l/h to m³/h
 *          - Q(l/s) is the selected ventilation rate in litres per second
 *          - A_e is the dwelling external envelope area in m²
 *  - Pulse test option: use the user provided override value if present; if absent fallback to 0.
 *
 * NOTE: We intentionally keep this calculation independent of areas/volumes and rely on the
 * constant factor derived from the provided example until fuller standard formulas are required.
 */
export function getEnvelopeAirPermeabilityAt50Pa({
  ventilationDesign,
  numberOfBedrooms,
  numberOfResidents,
  dwellingExternalEnvelopeAreaInSquareMeters,
  dwellingInternalFloorAreaInSquareMeters,
}: {
  ventilationDesign: StandardHouseInputVentilationDesign;
  numberOfBedrooms: number;
  numberOfResidents: number;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  dwellingInternalFloorAreaInSquareMeters: number;
}): number {
  if (dwellingExternalEnvelopeAreaInSquareMeters === 0) {
    // Avoid division by zero
    return 0;
  }
  const option = ventilationDesign.airPermeability;
  switch (option) {
    case AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831:
      return 12; // Simplified constant
    case AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST:
      return ventilationDesign.airPermeabilityOverride ?? 0;
    case AirPermeabilityOption.AIR_PERMEABILITY_OPTION_CIBSE: {
      const BEDROOM_BASE_RATES: number[] = [13, 17, 21, 25, 29]; // l/s for 1..5 bedrooms
      const bedrooms = Math.max(1, Math.floor(numberOfBedrooms));
      const baseRateLps =
        bedrooms <= 5 ? BEDROOM_BASE_RATES[bedrooms - 1]! : BEDROOM_BASE_RATES[4]! + 4 * (bedrooms - 5);

      // Default occupancy per table: 2 in main bedroom + 1 in each additional bedroom
      const defaultOccupants = bedrooms + 1;
      const actualOccupants = numberOfResidents > 0 ? numberOfResidents : defaultOccupants;
      const extraOccupants = Math.max(0, actualOccupants - defaultOccupants);
      const occupantAdjustedRateLps = baseRateLps + 4 * extraOccupants; // uplift 4 l/s per extra occupant

      const minRateFromAreaLps = 0.3 * dwellingInternalFloorAreaInSquareMeters;
      const ventilationRate = Math.max(occupantAdjustedRateLps, minRateFromAreaLps);

      // General conversion from l/s ventilation rate to air permeability at 50 Pa
      return (
        ventilationRate *
        ((SAP_INFILTRATION_TO_AIR_PERMEABILITY_RATIO * SECONDS_IN_HOUR * CUBIC_METRES_IN_LITRE) /
          dwellingExternalEnvelopeAreaInSquareMeters)
      );
    }
    case AirPermeabilityOption.AIR_PERMEABILITY_OPTION_UNSPECIFIED:
    case AirPermeabilityOption.UNRECOGNIZED:
      throw new Error(`Unsupported air permeability option: ${option}`);
    default:
      return option satisfies never;
  }
}

/**
 * Calculate the general formula of fabric heat loss, which refers to the kind of heat transfered
 * through the building's fabric (such as walls, windows, etc.) for a given surface. We add 0.1 to
 * all U-values to account for thermal bridging.
 *
 * Reference: DHDG2021, section 3.4.2, Equation 2
 *
 * @param {number} param0.area - The area of the surface in square meters (A).
 * @param {number} param0.uValue - The U-value of the surface in W/m2 K
 * @param {number} param0.insideOutsideTempDiff - The difference in temperature, T{in} - T{out} where T{in} is the indoor temperature and T{out} is the outdoor temperature, both expressed in Celcius/Kelvin.
 * @returns {number} The fabric heat loss Q{F} expressed in W.
 */
export const getHeatLossForSurface = ({
  area,
  uValue,
  insideOutsideTempDiff,
}: {
  area: number;
  uValue: UValue;
  insideOutsideTempDiff: number;
}): number => area * (uValue.value + THERMAL_BRIDGING_UVALUE_ADJUSTMENT) * insideOutsideTempDiff;

/**
 * Calculate adjustment for design temperature based on altitude of dwelling.
 *
 * Reference: DHDG2021, section *******
 *
 * @param {number} param0.altitude - Altitude of dwelling in meters
 * @returns The adjustment that should be added to the design temperature
 */
export const getDegreeAdjustmentForAltitude = ({ altitude }: { altitude: number }) => Math.floor(altitude / 50) * -0.3;

/**
 * Calculate the yearly energy demand corresponding to ventilation heat loss.
 *
 * @param {number} param0.roomVolume - The volume in m^3
 * @param {number} param0.avgAirChangesPerHour - The average number of air changes per hour, also called ACH
 * @param {number} param0.degreeDays - The degree days in K*day
 * @returns kWh used in one year to replace the ventilation heat loss
 */
export const getSimpleRoomVentilationEnergyDemand = ({
  roomVolume,
  avgAirChangesPerHour,
  degreeDays,
}: {
  roomVolume: number;
  avgAirChangesPerHour: number;
  degreeDays: number;
}) =>
  getSimpleRoomVentilationHeatLossPerDegree({
    roomVolumeInCubicMeters: roomVolume,
    avgAirChangesPerHour,
    ratioBetweenRoomAndZone: 1,
    airPropertyFactor: AIR_CHANGE_FACTOR,
  }) *
  degreeDays *
  (24 / 1000);

/**
 * Calculate the yearly energy demand corresponding to fabric heat loss for a surface towards the outside.
 *
 * Reference: TM41-2006, section 1.2.1
 *
 * To calculate the heating energy demand we use the following equation from section 1.2.1 in TM41:
 * Heating energy demand (kW·h) = overall heat loss coefficient (kW·K–1) * degree-days (K·day) * 24 (h·day–1)
 *
 * The overall heat loss coefficient (kW·K–1) is defined as:
 * overall heat loss coefficient (kW·K–1) = Heat loss (kW) / temperature difference (K)
 *
 * To get the heat loss coefficient for a surface at our design temperature, we therefore take the heat loss of each
 * surface and divide it by the temperature difference between design room temp and ODT. Note that the
 * opposing temperature used to calculate the heat loss could have been lower than the ODT, but that follows our
 * assumption that some surfaces have a lower opposing temperature, even when the outside temperature is at
 * the ODT. This is analogous to how EN 12831 uses a "temperature correction factor" to be able to sum the heat loss
 * of multiple surfaces with different opposing temperatures.
 *
 * @param {number} param0.degreeDays - The degree days in K*day (see reference)
 * @param {number} param0.heatLoss - The heat loss of the surface in W.
 * @param {number} param0.designRoomTemp - The design room temperature in Celcius/Kelvin.
 * @param {number} param0.outdoorDesignTemp - The outdoor design temperature in Celcius/Kelvin.
 * @returns kWh used in one year
 */
export const getYearlySurfaceEnergyDemand = ({
  degreeDays,
  heatLoss,
  designRoomTemp,
  outdoorDesignTemp,
}: {
  heatLoss: number;
  designRoomTemp: number;
  outdoorDesignTemp: number;
  degreeDays: number;
}) => (heatLoss / (designRoomTemp - outdoorDesignTemp)) * degreeDays * (24 / 1000);

/**
 *
 * Reference: (for solid floor only) MIS3005D 5.5.1c says to use the local annual average external air
 *  temperature, but DHDG2021 ******* suggests otherwise. We aligned with MCS.
 *
 * @param outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
export function getAdjacentForFloor(
  floor: Floor,
  designRoomTemp: number,
  outdoorTemperature: number,
  roomName: Room['name'],
  climateDataStore: ClimateDataStore,
): { temperature?: number; temperatureDifference: number } {
  switch (floor.belowFloor) {
    case AdjacentKind.SolidFloor:
      return {
        temperature: climateDataStore.localAnnualAverageExternalAirTemperature,
        temperatureDifference: designRoomTemp - climateDataStore.localAnnualAverageExternalAirTemperature, // TODO: Should this be the ground temperature instead?
      };
    case AdjacentKind.Heated:
      return { temperatureDifference: 0 }; // according to MCS spreadsheet mid and upper floors don't loose heat to room below? TODO: compare with CIBSE
    case AdjacentKind.Unheated: {
      const unheatedRoomTemp = climateDataStore.localAnnualAverageExternalAirTemperature;
      return {
        temperature: unheatedRoomTemp,
        temperatureDifference: designRoomTemp - unheatedRoomTemp,
      };
    }
    case AdjacentKind.SuspendedFloor:
      return {
        temperature: outdoorTemperature,
        temperatureDifference: designRoomTemp - outdoorTemperature,
      }; // according to MCS spreadsheet suspended floors should be treated as external walls
    default:
      throw new Error(`Invalid belowFloor: ${floor.belowFloor} in ${roomName}`);
  }
}

type AdjoiningRoomHeatDesignResult = { roomTemp: number; tempDiff: number; name: string };

function getAdjoiningForInternalWall(
  wall: InternalWall,
  designRoomTemp: number,
  roomTemps: RoomTemp[],
): AdjoiningRoomHeatDesignResult | null {
  // No adjoining room found for this internal wall
  if (!wall.adjoiningRoomUID) return null;

  const adjoiningRoom = roomTemps.find((r) => r.roomUID === wall.adjoiningRoomUID);
  if (!adjoiningRoom) return null;

  return {
    roomTemp: adjoiningRoom.roomTemp,
    tempDiff: designRoomTemp - adjoiningRoom.roomTemp,
    name: adjoiningRoom.name,
  };
}

/**
 * Reference for temp difference calculations: DHDG2021, section *******
 *
 * @param outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */

function getTempAboveRoofOrCeiling(
  roofOrCeiling: RoofOrCeiling,
  designRoomTemp: number,
  outdoorTemperature: number,
  climateDataStore: ClimateDataStore,
): { temperature?: number; temperatureDifference: number } {
  switch (roofOrCeiling.spaceAbove.type) {
    case AdjacentKind.Outside:
      return {
        temperature: outdoorTemperature,
        temperatureDifference: designRoomTemp - outdoorTemperature,
      };
    case AdjacentKind.Heated:
      return {
        temperature: roofOrCeiling.spaceAbove.tempOfSpaceAbove,
        temperatureDifference: designRoomTemp - (roofOrCeiling.spaceAbove.tempOfSpaceAbove ?? 0),
      };
    case AdjacentKind.Unheated: {
      const unheatedRoomTemp = getUnheatedRoomTemperature(climateDataStore);
      return {
        temperature: unheatedRoomTemp,
        temperatureDifference: designRoomTemp - unheatedRoomTemp,
      };
    }
    // no default
  }
}

export type RoomTemp = {
  roomUID: string;
  name: string;
  roomTemp: number;
};

function extractUValue(
  surface: RoomFabric,
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
): UValue {
  return (
    surface.uValue ??
    lookupUValueForSurfaceType(surface.surfaceType, projectUValues, floor, floors)?.uValue ??
    new UndefinedUValue()
  );
}

/**
 *  Reference: (for against-soil only) MIS3005D 5.5.1c says to use the local annual average external air
 *   temperature, but DHDG2021 ******* suggests otherwise. We aligned with MCS.
 *
 * @param outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
function getExternalHeatDesignResult(
  wall: ExternalWall,
  averageRoomHeight: Room['averageHeight'],
  designRoomTemp: number,
  outdoorTemperature: number,
  doors: Door[],
  windows: Window[],
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
  countryCode: CountryCode,
  climateDataStore: ClimateDataStore,
): FabricHeatDesignResult[] {
  const tempDiffAgainstAir = designRoomTemp - outdoorTemperature;
  const tempDiffAgainstSoil = designRoomTemp - climateDataStore.localAnnualAverageExternalAirTemperature; // See reference comment
  const externalWallAreaAdjustment = externalWallAreaAdjustmentFactor(countryCode) ?? 1;
  const areaIncludingDoorsAndWindows = (wall.length ?? 0) * averageRoomHeight * externalWallAreaAdjustment;
  const uValue = extractUValue(wall, projectUValues, floor, floors);
  const attachedDoorArea = doors.filter((door) => door.wallUID === wall.uid).reduce((acc, curr) => acc + curr.area, 0);
  const attachedWindowArea = windows
    .filter((window) => window.wallUID === wall.uid)
    .reduce((acc, curr) => acc + curr.area, 0);

  // Note: if this external wall is partially exposed to soil, don't have enough
  // information to determine how much area to remove from the doors and
  // windows, so we use an average.
  const area = areaIncludingDoorsAndWindows - attachedDoorArea - attachedWindowArea;

  const wallSoilPercentage = getWallSoilPercentage(wall, floor);

  if (wallSoilPercentage === 100 || wallSoilPercentage === 0) {
    // This external wall is either fully in contact with the air or the soil
    const adjacentKind = wallSoilPercentage === 100 ? AdjacentKind.Soil : AdjacentKind.Outside;
    const insideOutsideTempDiff = wallSoilPercentage === 100 ? tempDiffAgainstSoil : tempDiffAgainstAir;
    const heatLoss = getHeatLossForSurface({ insideOutsideTempDiff, area, uValue });
    return [
      {
        area: areaIncludingDoorsAndWindows,
        uValue,
        id: wall.uid,
        adjacentTemperature: outdoorTemperature,
        adjacentKind,
        heatLoss,
        energyDemand: getYearlySurfaceEnergyDemand({
          degreeDays: climateDataStore.degreeDays,
          heatLoss,
          designRoomTemp,
          outdoorDesignTemp: outdoorTemperature,
        }),
      },
    ];
  }

  // The wall is partially exposed to soil and air
  const outputs: FabricHeatDesignResult[] = [];
  const airRatio = 1 - wallSoilPercentage / 100.0;
  const soilRatio = wallSoilPercentage / 100.0;

  const airHeatLoss = getHeatLossForSurface({ insideOutsideTempDiff: tempDiffAgainstAir, area, uValue }) * airRatio;
  outputs.push({
    area: areaIncludingDoorsAndWindows * airRatio,
    uValue,
    id: wall.uid,
    adjacentTemperature: outdoorTemperature,
    adjacentKind: AdjacentKind.Outside,
    heatLoss: airHeatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({
      degreeDays: climateDataStore.degreeDays,
      heatLoss: airHeatLoss,
      designRoomTemp,
      outdoorDesignTemp: outdoorTemperature,
    }),
  });

  const soilHeatLoss = getHeatLossForSurface({ insideOutsideTempDiff: tempDiffAgainstSoil, area, uValue }) * soilRatio;
  outputs.push({
    area: areaIncludingDoorsAndWindows * soilRatio,
    uValue,
    id: `${wall.uid}-soil`,
    adjacentTemperature: climateDataStore.localAnnualAverageExternalAirTemperature, // See reference comment
    adjacentKind: AdjacentKind.Soil,
    heatLoss: soilHeatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({
      degreeDays: climateDataStore.degreeDays,
      heatLoss: soilHeatLoss,
      designRoomTemp,
      outdoorDesignTemp: outdoorTemperature,
    }),
  });

  return outputs;
}

function getInternalWallHeatDesignResult(
  wall: InternalWall,
  averageRoomHeight: Room['averageHeight'],
  designRoomTemp: number,
  roomTemps: RoomTemp[],
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
): FabricHeatDesignResult {
  const area = averageRoomHeight * (wall.length ?? 0);
  const uValue = extractUValue(wall, projectUValues, floor, floors);
  const adjoining = getAdjoiningForInternalWall(wall, designRoomTemp, roomTemps);

  const result: FabricHeatDesignResult = {
    area,
    uValue,
    id: wall.uid,
    heatLoss: getHeatLossForSurface({ insideOutsideTempDiff: adjoining?.tempDiff ?? 0, area, uValue }),
    energyDemand: 0,
  };

  if (adjoining) {
    result.adjacentTemperature = adjoining?.roomTemp;
    result.adjacentKind = AdjacentKind.Room;
    result.adjacentName = adjoining?.name;
  }

  return result;
}

function getPartyWallHeatDesignResult(
  wall: Wall,
  averageRoomHeight: Room['averageHeight'],
  designRoomTemp: number,
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
  climateDataStore: ClimateDataStore,
): FabricHeatDesignResult {
  const area = averageRoomHeight * (wall.length ?? 0);
  const uValue = extractUValue(wall, projectUValues, floor, floors);
  const unheatedRoomTemperature = getUnheatedRoomTemperature(climateDataStore);
  const tempDiff = designRoomTemp - unheatedRoomTemperature;

  return {
    area,
    uValue,
    id: wall.uid,
    adjacentTemperature: unheatedRoomTemperature,
    adjacentKind: AdjacentKind.Unheated,
    heatLoss: getHeatLossForSurface({ insideOutsideTempDiff: tempDiff, area, uValue }),
    // TODO: If the neighbouring dwelling is unheated, there will be heat loss, and thus energy will be consumed to replace it.
    // However, we can't use the regular degree days for this; we'd have to have a custom degree days measure.
    // Heat Engineer is simply ignoring energy demand for these party walls, so we will just do the same for now.
    energyDemand: 0,
  };
}

/**
 * @param outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
function getFloorHeatDesignResult(
  floor: Floor,
  room: Room,
  designRoomTemp: number,
  outdoorTemperature: number,
  degreeDays: number,
  projectUValues: ProjectFallbackUValues,
  level: FloorProps,
  floors: FloorProps[],
  climateDataStore: ClimateDataStore,
): FabricHeatDesignResult {
  const { area } = floor;
  const uValue = extractUValue(floor, projectUValues, level, floors);
  const adjacent = getAdjacentForFloor(floor, designRoomTemp, outdoorTemperature, room.name, climateDataStore);

  // NOTE: DHDG2021 provides various methods to calculate the U-value of ground floors.
  // Tables 3.21 to 3.23 provides various methods for regularly shaped rooms, and there are also equations for irregular shapes
  // and handling of insulation. We implemented this partly like this:
  //
  // if (['solid', 'suspended'].includes(floor.belowFloor)) {
  //   uValue = uValueUninsulatedIrregularGroundFloor(room);
  //   if (floor.insulated) {
  //     uValue = 1 / ((1 / uValue) + (1 / originalUValue));
  //   }
  // }
  //
  // However, we then agreed with heat designers to follow a simpler approach, that also Heat Engineer does, which is to just
  // let them set specific U-values.

  const heatLoss = getHeatLossForSurface({ insideOutsideTempDiff: adjacent.temperatureDifference, area, uValue });

  return {
    area,
    uValue,
    id: floor.uid,
    adjacentTemperature: adjacent.temperature,
    adjacentKind: floor.belowFloor,
    heatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({
      degreeDays,
      heatLoss,
      designRoomTemp,
      outdoorDesignTemp: outdoorTemperature,
    }),
  };
}

/**
 * @param outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
function getRoofOrCeilingHeatDesignResult(
  roofOrCeiling: RoofOrCeiling,
  outdoorTemperature: number,
  designRoomTemp: number,
  degreeDays: number,
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
  roofGlazings: RoofGlazing[],
  climateDataStore: ClimateDataStore,
): FabricHeatDesignResult {
  const area = areaAfterGlazing(roofOrCeiling.area, roofGlazings);
  const uValue = extractUValue(roofOrCeiling, projectUValues, floor, floors);
  const adjacent = getTempAboveRoofOrCeiling(roofOrCeiling, designRoomTemp, outdoorTemperature, climateDataStore);

  const heatLoss = getHeatLossForSurface({ insideOutsideTempDiff: adjacent.temperatureDifference, area, uValue });

  return {
    area,
    uValue,
    id: roofOrCeiling.uid,
    adjacentTemperature: adjacent.temperature,
    adjacentKind: roofOrCeiling.spaceAbove.type,
    heatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({
      degreeDays,
      heatLoss,
      designRoomTemp,
      outdoorDesignTemp: outdoorTemperature,
    }),
  };
}

/**
 * 3.5.2.5 Sloped Roof Windows
  Unless specified, the U-values such as those for windows given in Table 3.20 (see section 3.8.2) are for situations
  when the glass is vertical. If the glass is to be installed in a roof, a correction needs to be made to compensate
  for the extra heat that will be conducted through it. Table 3.3 shows suggested corrections for double-glazed
  windows angled from horizontal to vertical; if available, the manufacturer’s data should be used in preference.

  Measuring the angle is not easily doable at the moment. We add a +0.5 correction from the window U-values

 * @param surface 
 * @returns roof glazing uValue correction
 */
export const roofGlazingAngleCorrection = (surface: RoomFabric) => {
  if (surface.surfaceType !== 'roofGlazings') return 0;
  return 0.5;
};

function otherSurfaceArea(surface: Door | Window | RoofGlazing) {
  if (surface.surfaceType === 'roofGlazings') {
    return roofGlazingArea(surface);
  }
  return surface.area;
}

export function getWallAttachedSurfaceHeatDesignResult(
  surface: Door | Window,
  wallOutputs: FabricHeatDesignResult[],
  insideOutsideTempDiff: number,
  degreeDays: number,
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
  designRoomTemp: number,
  outdoorDesignTemp: number,
): WallAttachedFabricHeatDesignResult {
  const area = otherSurfaceArea(surface);
  const uValue = extractUValue(surface, projectUValues, floor, floors);

  // Find the matching wall, if there's one
  const wallOutput = wallOutputs.find((o) => o.id === surface.wallUID);

  const heatLoss = getHeatLossForSurface({ insideOutsideTempDiff, area, uValue });

  return {
    area,
    uValue,
    id: surface.uid,
    heatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({ degreeDays, heatLoss, designRoomTemp, outdoorDesignTemp }),
    adjacentTemperature: wallOutput?.adjacentTemperature,
    adjacentKind: wallOutput?.adjacentKind,
    adjacentName: wallOutput?.adjacentName,
    wallId: surface.wallUID,
  };
}

/**
 * We assume the adjacent space is outside for roof glazings
 */
export function getRoofGlazingHeatDesignResult(
  surface: RoofGlazing,
  degreeDays: number,
  projectUValues: ProjectFallbackUValues,
  floor: FloorProps,
  floors: FloorProps[],
  designRoomTemp: number,
  outdoorDesignTemp: number,
): FabricHeatDesignResult {
  const area = otherSurfaceArea(surface);
  const uValue = extractUValue(surface, projectUValues, floor, floors);
  const roofGlazingCorrection = roofGlazingAngleCorrection(surface);

  const uValueAdjustedForRoofGlazing =
    uValue instanceof UndefinedUValue ? uValue : new UValue(uValue.name, uValue.value + roofGlazingCorrection);

  const heatLoss = getHeatLossForSurface({
    insideOutsideTempDiff: designRoomTemp - outdoorDesignTemp,
    area,
    uValue: uValueAdjustedForRoofGlazing,
  });

  return {
    area,
    uValue: uValueAdjustedForRoofGlazing,
    id: surface.uid,
    heatLoss,
    energyDemand: getYearlySurfaceEnergyDemand({ degreeDays, heatLoss, designRoomTemp, outdoorDesignTemp }),
    adjacentTemperature: outdoorDesignTemp,
    adjacentKind: AdjacentKind.Outside,
  };
}

function sumHeatLossResults(results: HeatLossResult[]): HeatLossResult {
  return results.reduce(
    (acc, curr) => ({
      heatLoss: acc.heatLoss + curr.heatLoss,
      energyDemand: acc.energyDemand + curr.energyDemand,
    }),
    { heatLoss: 0, energyDemand: 0 },
  );
}

export function calculateVolume(areaSqM: number, heightM: number): number {
  if (areaSqM < 0 || heightM < 0) {
    return 0;
  }
  return areaSqM * heightM;
}

/**
 * Calculate a value to be added to the factor to be multiplied to the heat loss of a room
 * to account for high ceilings.
 *
 * Equation extracted from DBSP_DHDG_2021_v2 Figure 3.5 using exponential regression
 * Remade to calculate addition and not total using the follow X and Y values:
 * X-Values: [2.4, 3.5, 4.1, 4.7, 5.1, 5.5, 5.9, 6.3, 6.8]
 * Y-Values: [0, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08]
 *
 * Reference: DHDG2021, section *******, Figure 3.5.
 *
 * @param {number} roomHeight - Room height in meters
 * @returns High ceiling factor - 1
 */
export const highCeilingAdditionFactor = (roomHeight: number) => {
  // Below 2.6m the addition is negative, so return 0
  if (roomHeight < 2.6) return 0;
  const a = 0.02935;
  const b = 0.2243;
  const c = -0.05235;
  return a * Math.exp(b * roomHeight) + c;
};

function getUnheatedRoomHeatDesignResult(params: {
  floorId: string;
  roomId: string;
  roomName: string;
  roomType: RoomType;
  averageHeight: number;
  totalFloorArea: number;
  countryCode: CountryCode;
  climateDataStore: ClimateDataStore;
  externalEnvelopeArea: number;
}): RoomHeatDesignResult {
  const zeroOutput: FabricHeatDesignResult = {
    id: 'UNDEFINED',
    heatLoss: 0,
    energyDemand: 0,
    area: 0,
    uValue: new UndefinedUValue(),
    adjacentTemperature: 0,
  };

  return {
    floorId: params.floorId,
    roomId: params.roomId,
    roomName: params.roomName,
    roomType: params.roomType,
    averageHeight: params.averageHeight,
    isHeated: false,
    totalFloorArea: params.totalFloorArea,
    surfaceTotals: {
      externalWalls: zeroOutput,
      internalWalls: zeroOutput,
      partyWalls: zeroOutput,
      doors: zeroOutput,
      windows: zeroOutput,
      floors: zeroOutput,
      roofGlazings: zeroOutput,
      roofsOrCeilings: zeroOutput,
    },
    surfaceDetails: {
      externalWalls: [],
      internalWalls: [],
      partyWalls: [],
      doors: [],
      windows: [],
      floors: [],
      roofGlazings: [],
      roofsOrCeilings: [],
    },
    totalFabric: zeroOutput,
    ventilation: zeroOutput,
    additional: { highCeiling: zeroOutput },
    totalAdditional: zeroOutput,
    totalRoom: zeroOutput,
    wattsPerMeterSquared: 0,
    calculatedUnderfloorHeatingOutputWatt: 0,
    calculatedUnderfloorHeatingFlowRate: 0,
    totalOutputOfRadiatorsWatt: 0,
    totalOutputOfHeatEmittersWatt: 0,
    resolvedDesignRoomTemperatureCelsius: getUnheatedRoomTemperature(params.climateDataStore),
    radiators: [],
    averageAirChangePerHour: 0,
    externalEnvelopeArea: params.externalEnvelopeArea,
  };
}

/**
 * Calculate the full heat loss for a room.
 * @param {Room} param0.room - The room to calculate the heat loss for
 * @param param0.outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 * @returns
 */
export const getRoomHeatDesignResult = ({
  room,
  floor,
  projectUValues,
  floors,
  outdoorTemperature,
  roomTemps,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
  ventilationDesign,
  dwellingExternalEnvelopeAreaInSquareMeters,
  dwellingNumberOfBedrooms,
  dwellingNumberOfResidents,
  dwellingInternalFloorAreaInSquareMeters,
}: {
  room: Room;
  floor: FloorProps;
  projectUValues: ProjectFallbackUValues;
  floors: FloorProps[];
  outdoorTemperature: number;
  roomTemps: RoomTemp[];
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
  ventilationDesign: HouseInputVentilationDesign;
  dwellingExternalEnvelopeAreaInSquareMeters: number;
  dwellingNumberOfBedrooms: number;
  dwellingNumberOfResidents: number;
  dwellingInternalFloorAreaInSquareMeters: number;
}): RoomHeatDesignResult => {
  const {
    surfaces,
    id: roomId,
    name: roomName,
    roomType,
    totalArea,
    totalVolume,
    isHeated,
    averageHeight,
    floorId,
  } = room;

  const roomTemp = getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore);

  const avgAirChangesPerHour = getAverageAirChangesPerHour(room, ventilationDesign.acphDefault);

  const roomExternalEnvelopeAreaInSquareMeters = computeRoomExternalEnvelopeArea({
    room,
    floor,
    countryCode,
  });

  if (!isHeated) {
    return getUnheatedRoomHeatDesignResult({
      floorId,
      roomId,
      roomName,
      roomType,
      averageHeight,
      totalFloorArea: totalArea,
      countryCode,
      climateDataStore,
      externalEnvelopeArea: roomExternalEnvelopeAreaInSquareMeters,
    });
  }

  const highCeiling = highCeilingAdditionFactor(averageHeight);
  const insideOutsideTempDiff = roomTemp - outdoorTemperature;

  const externalWalls = surfaces.walls.filter((wall) => wall.surfaceType === 'externalWalls');
  const partyWalls = surfaces.walls.filter((wall) => wall.surfaceType === 'partyWalls');
  const internalWalls = surfaces.walls.filter((wall) => wall.surfaceType === 'internalWalls');

  const externalWallHeatDesignResults = externalWalls.flatMap((wall) =>
    getExternalHeatDesignResult(
      wall,
      room.averageHeight,
      roomTemp,
      outdoorTemperature,
      surfaces.doors,
      surfaces.windows,
      projectUValues,
      floor,
      floors,
      countryCode,
      climateDataStore,
    ),
  );

  const internalWallsHeatDesignResults = internalWalls.map((wall) =>
    getInternalWallHeatDesignResult(wall, room.averageHeight, roomTemp, roomTemps, projectUValues, floor, floors),
  );

  const partyWallsHeatDesignResults = partyWalls.map((wall) =>
    getPartyWallHeatDesignResult(wall, room.averageHeight, roomTemp, projectUValues, floor, floors, climateDataStore),
  );

  // We don't include the party walls in the array below because there should
  // never be a door or window in a party wall
  const allWallHeatDesignResults = [...externalWallHeatDesignResults, ...internalWallsHeatDesignResults];

  const doorsHeatDesignResults = surfaces.doors
    .filter((door) => doorOrWindowIsIncludedInHeatLossCalculation(door, room))
    .map((door) =>
      getWallAttachedSurfaceHeatDesignResult(
        door,
        allWallHeatDesignResults,
        insideOutsideTempDiff,
        climateDataStore.degreeDays,
        projectUValues,
        floor,
        floors,
        roomTemp,
        outdoorTemperature,
      ),
    );

  const windowsHeatDesignResults = surfaces.windows
    .filter((window) => doorOrWindowIsIncludedInHeatLossCalculation(window, room))
    .map((window) =>
      getWallAttachedSurfaceHeatDesignResult(
        window,
        allWallHeatDesignResults,
        insideOutsideTempDiff,
        climateDataStore.degreeDays,
        projectUValues,
        floor,
        floors,
        roomTemp,
        outdoorTemperature,
      ),
    );

  const floorHeatDesignResults = surfaces.floors.map((f) =>
    getFloorHeatDesignResult(
      f,
      room,
      roomTemp,
      outdoorTemperature,
      climateDataStore.degreeDays,
      projectUValues,
      floor,
      floors,
      climateDataStore,
    ),
  );

  const roofOrCeilingHeatDesignResults = surfaces.roofsOrCeilings.map((roofOrCeiling) =>
    getRoofOrCeilingHeatDesignResult(
      roofOrCeiling,
      outdoorTemperature,
      roomTemp,
      climateDataStore.degreeDays,
      projectUValues,
      floor,
      floors,
      surfaces.roofGlazings,
      climateDataStore,
    ),
  );

  const roofGlazingHeatDesignResults = surfaces.roofGlazings.map((glazing: RoofGlazing) =>
    getRoofGlazingHeatDesignResult(
      glazing,
      climateDataStore.degreeDays,
      projectUValues,
      floor,
      floors,
      roomTemp,
      outdoorTemperature,
    ),
  );

  const surfaceDetails = {
    externalWalls: externalWallHeatDesignResults,
    internalWalls: internalWallsHeatDesignResults,
    partyWalls: partyWallsHeatDesignResults,
    doors: doorsHeatDesignResults,
    windows: windowsHeatDesignResults,
    floors: floorHeatDesignResults,
    roofGlazings: roofGlazingHeatDesignResults,
    roofsOrCeilings: roofOrCeilingHeatDesignResults,
  };

  const surfaceHeatDesignResults: RoomHeatDesignResult['surfaceTotals'] = {
    externalWalls: sumHeatLossResults(externalWallHeatDesignResults),
    internalWalls: sumHeatLossResults(internalWallsHeatDesignResults),
    partyWalls: sumHeatLossResults(partyWallsHeatDesignResults),
    doors: sumHeatLossResults(doorsHeatDesignResults),
    windows: sumHeatLossResults(windowsHeatDesignResults),
    floors: sumHeatLossResults(floorHeatDesignResults),
    roofGlazings: sumHeatLossResults(roofGlazingHeatDesignResults),
    roofsOrCeilings: sumHeatLossResults(roofOrCeilingHeatDesignResults),
  };

  const totalFabric = sumHeatLossResults(Object.values(surfaceHeatDesignResults));

  const additional = {
    highCeiling: {
      heatLoss: highCeiling * totalFabric.heatLoss,
      energyDemand: highCeiling * totalFabric.energyDemand,
    },
  };

  const totalAdditional = sumHeatLossResults(Object.values(additional));

  // Decide which ventilation method to use
  const useStandardMethod = ventilationDesign.calculationMethod === VentilationCalculationMethod.STANDARD;

  let ventilationHeatDesignResult: HeatLossResult;
  let calculatedAirChangePerHourFromVentilationHeatLoss: number = avgAirChangesPerHour; // If the standard method is not used, we just use the default or user-provided ACPH

  if (useStandardMethod) {
    const envelopeAirPermeabilityAt50Pa = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign,
      numberOfBedrooms: dwellingNumberOfBedrooms,
      numberOfResidents: dwellingNumberOfResidents,
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters,
    });
    const volumeFlowFactor = VENTILATION_EXPOSURE_VALUES[ventilationDesign.dwellingExposure] ?? 0;

    const ventilationHeatLoss = getStandardRoomVentilationHeatLoss({
      roomVolumeInCubicMeters: totalVolume,
      avgAirChangesPerHour,
      insideOutsideTempDiff,
      roomExternalEnvelopeAreaInSquareMeters,
      dwellingExternalEnvelopeAreaInSquareMeters,
      envelopeAirPermeabilityAt50Pa,
      volumeFlowFactor,
    });

    const ventilationEnergyDemand = getStandardRoomVentilationEnergyDemand({
      roomVolumeInCubicMeters: totalVolume,
      avgAirChangesPerHour,
      degreeDays: climateDataStore.degreeDays,
      roomExternalEnvelopeAreaInSquareMeters,
      dwellingExternalEnvelopeAreaInSquareMeters,
      envelopeAirPermeabilityAt50Pa,
      volumeFlowFactor,
    });

    ventilationHeatDesignResult = {
      heatLoss: ventilationHeatLoss,
      energyDemand: ventilationEnergyDemand,
    };
    calculatedAirChangePerHourFromVentilationHeatLoss = getAirChangesPerHourFromSimpleVentilationHeatLoss({
      ventilationHeatLoss,
      volumeInCubicMeters: totalVolume,
      insideOutsideTempDiff,
    });
  } else {
    ventilationHeatDesignResult = {
      heatLoss: getSimpleRoomVentilationHeatLoss({
        insideOutsideTempDiff,
        avgAirChangesPerHour,
        roomVolumeInCubicMeters: totalVolume,
      }),
      energyDemand: getSimpleRoomVentilationEnergyDemand({
        degreeDays: climateDataStore.degreeDays,
        avgAirChangesPerHour,
        roomVolume: totalVolume,
      }),
    };
  }

  const totalRoom: HeatLossResult = sumHeatLossResults([totalFabric, ventilationHeatDesignResult, totalAdditional]);

  const roomHeatOutput = calculateNewHeatOutput({
    room,
    constructionYear,
    countryCode,
    roomHeatLoss: totalRoom.heatLoss,
    flowReturnDeltaT,
    flowTemperature,
    climateDataStore,
  });

  const radiatorDetails = calculateRadiatorDetailsForRoom({
    room,
    constructionYear,
    countryCode,
    flowTemperature,
    flowReturnDeltaT,
    climateDataStore,
  });

  return {
    floorId,
    roomId,
    roomName,
    averageHeight,
    isHeated: true,
    roomType,
    surfaceTotals: surfaceHeatDesignResults,
    surfaceDetails,
    totalFabric,
    ventilation: ventilationHeatDesignResult,
    additional,
    totalAdditional,
    totalFloorArea: totalArea,
    totalRoom,
    wattsPerMeterSquared: totalArea !== 0 ? totalRoom.heatLoss / totalArea : 0,
    calculatedUnderfloorHeatingOutputWatt: roomHeatOutput.underfloorHeating,
    calculatedUnderfloorHeatingFlowRate: getUnderfloorHeatingFlowRate(roomHeatOutput.underfloorHeating),
    totalOutputOfRadiatorsWatt: roomHeatOutput.radiators,
    resolvedDesignRoomTemperatureCelsius: roomTemp,
    radiators: radiatorDetails,
    totalOutputOfHeatEmittersWatt: roomHeatOutput.radiators + roomHeatOutput.underfloorHeating,
    averageAirChangePerHour: calculatedAirChangePerHourFromVentilationHeatLoss,
    externalEnvelopeArea: roomExternalEnvelopeAreaInSquareMeters,
  };
};

/**
 * @param obj.outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
export const getFloorsHeatDesignResults = ({
  rooms,
  floors,
  projectUValues,
  outdoorTemperature,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
  ventilationDesign,
  dwellingNumberOfResidents,
  dwellingNumberOfBedrooms,
}: {
  rooms: Room[];
  floors: FloorProps[];
  projectUValues: SurfaceFallbackUValues;
  outdoorTemperature: number;
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
  ventilationDesign: HouseInputVentilationDesign;
  dwellingNumberOfResidents: number;
  dwellingNumberOfBedrooms: number;
}): FloorHeatDesignResult[] => {
  const roomTemps: RoomTemp[] = rooms.map((room) => ({
    roomUID: room.id,
    name: room.name,
    roomTemp: getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore),
  }));
  const dwellingExternalEnvelopeAreaInSquareMeters = computeDwellingExternalEnvelopeArea({
    rooms,
    floors,
    countryCode,
  });
  const dwellingInternalFloorAreaInSquareMeters = rooms.reduce((acc, r) => acc + r.totalArea, 0);

  return floors.map((floor) => {
    const floorRooms = rooms.filter((room) => room.floorId === floor.uid);
    const roomsHeatDesignResults = floorRooms.map((room) =>
      getRoomHeatDesignResult({
        room,
        floor,
        projectUValues,
        floors,
        outdoorTemperature,
        roomTemps,
        constructionYear,
        countryCode,
        flowReturnDeltaT,
        flowTemperature,
        climateDataStore,
        ventilationDesign,
        dwellingExternalEnvelopeAreaInSquareMeters,
        dwellingNumberOfBedrooms,
        dwellingNumberOfResidents,
        dwellingInternalFloorAreaInSquareMeters,
      }),
    );

    const totalAreaSqm = floorRooms.reduce((acc, curr) => acc + curr.totalArea, 0);
    const totalHeatLossWatt = roomsHeatDesignResults.reduce((acc, curr) => acc + curr.totalRoom.heatLoss, 0);
    const totalOutputOfHeatEmittersWatt = roomsHeatDesignResults.reduce(
      (acc, curr) => acc + curr.totalOutputOfHeatEmittersWatt,
      0,
    );
    const numberOfBedrooms = countBedrooms(floorRooms);

    return {
      floorId: floor.uid,
      totalHeatLossWatt,
      totalOutputOfHeatEmittersWatt,
      totalAreaSqm,
      numberOfBedrooms,
      roomsResults: roomsHeatDesignResults,
    };
  });
};

/**
 * Compute the dwelling external envelope area in square meters.
 * This is the sum of all external wall areas (length * average room height)
 * adjusted by the market-specific external wall area adjustment factor (if any).
 */
export function computeDwellingExternalEnvelopeArea({
  rooms,
  floors,
  countryCode,
}: {
  rooms: Room[];
  floors: FloorProps[];
  countryCode: CountryCode;
}): number {
  return rooms.reduce((acc, room) => {
    const floor = floors.find((f) => f.uid === room.floorId);
    return acc + computeRoomExternalEnvelopeArea({ room, floor, countryCode });
  }, 0);
}

/**
 * Compute the external envelope area for a single room.
 * External envelope comprises any surface directly separating the room from external air.
 * We include:
 * External walls (length * averageHeight) with market-specific adjustment factor.
 * Roofs/ceilings whose spaceAbove.type === Outside (use area directly).
 * Floors that have belowFloor of SuspendedFloor.
 */
export function computeRoomExternalEnvelopeArea({
  room,
  countryCode,
  floor,
}: {
  room: Room;
  floor?: FloorProps;
  countryCode: CountryCode;
}): number {
  const wallAdjustment = externalWallAreaAdjustmentFactor(countryCode) ?? 1;

  const externalWallArea = room.surfaces.walls
    .filter((w) => w.surfaceType === 'externalWalls')
    .reduce((acc, wall) => {
      const wallSoilPercentage = getWallSoilPercentage(wall, floor);
      const airRatio = 1 - wallSoilPercentage / 100.0;
      const wallLength = wall.length ?? 0;
      return acc + wallLength * room.averageHeight * airRatio * wallAdjustment;
    }, 0);

  const externalRoofArea = room.surfaces.roofsOrCeilings
    .filter((r) => r.spaceAbove.type === AdjacentKind.Outside)
    .reduce((acc, roof) => acc + roof.area, 0);

  const externalFloorArea = room.surfaces.floors
    .filter((f) => f.belowFloor === AdjacentKind.SuspendedFloor)
    .reduce((acc, floor) => acc + floor.area, 0);

  return externalWallArea + externalRoofArea + externalFloorArea;
}

/** Returns the value of the highest designRoomTemp in an array of Rooms */
export const getHighestDesignRoomTemp = (
  rooms: Room[],
  constructionYear: number,
  countryCode: CountryCode,
  climateDataStore: ClimateDataStore,
) => {
  let highest = -Infinity;
  rooms.forEach((room) => {
    const temp = getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore);
    if (temp > highest) {
      highest = temp;
    }
  });
  return highest;
};

/**
 * Reference: MCS-MGD-007 2.2 Mathematics behind these cylinder sizes
 *
 * @param obj.cylinderCapacity In Litres
 * @param obj.cylinderWaterTemperature In degrees Celsius
 * @param obj.heatPumpPower In Watts
 * @returns The time in minutes it takes to heat the entirety of a domestic
 * hot water cylinder from cold water to the given hot water temperature.
 */
export function calculateCylinderReheatTime({
  cylinderCapacity,
  cylinderWaterTemperature,
  heatPumpPower,
}: {
  cylinderCapacity: number;
  cylinderWaterTemperature: number;
  heatPumpPower: number;
}) {
  const temperatureDifference = cylinderWaterTemperature - COLD_WATER_TEMPERATURE;
  return (cylinderCapacity * temperatureDifference) / (14.3 * heatPumpPower);
}

const COLD_WATER_TEMP = 10;
const SHOWER_TEMP = 38;
const SHOWERHEAD_LITERS_PER_MIN = 8.7;
const SHOWER_TEMP_THRESHOLD = 30;

/**
 * Reference: https://github.com/airahome/smart-control/blob/main/labs/calculate_shower_minutes.py
 *
 * @param obj.tankSizeLiters
 * @param obj.cylinderWaterTemperature in degrees Celsius, This is the domestic hot water (dhw) temperature
 * @returns  Shower time in minutes
 */
export const getShowerMinutes = ({
  tankSizeLiters,
  cylinderWaterTemperature,
}: {
  tankSizeLiters: number;
  cylinderWaterTemperature: number;
}): number => {
  if (cylinderWaterTemperature < SHOWER_TEMP_THRESHOLD) {
    return 0;
  }

  if (cylinderWaterTemperature === COLD_WATER_TEMP) {
    return 0;
  }

  const fractionOfTank = (SHOWER_TEMP - COLD_WATER_TEMP) / (cylinderWaterTemperature - COLD_WATER_TEMP);
  const hotWaterPerMin = SHOWERHEAD_LITERS_PER_MIN * fractionOfTank;
  const showerTime = tankSizeLiters / hotWaterPerMin;

  return showerTime;
};

/**
 * Reference: https://github.com/airahome/engagement-app/blob/main/lib/src/features/heat_pump/dhw_control/application/dhw_temps_to_minutes.dart
 */
export const getShowerMinutesRange = ({
  cylinderWaterTemperature,
  tankSizeLiters,
}: {
  cylinderWaterTemperature: number;
  tankSizeLiters: number;
}): [number, number] => {
  const showerMinutes = getShowerMinutes({
    cylinderWaterTemperature,
    tankSizeLiters,
  });
  if (showerMinutes <= 5) {
    return [0, 10];
  }
  if (tankSizeLiters > 100) {
    // base 5
    return [Math.ceil((showerMinutes - 5) / 5) * 5, Math.ceil((showerMinutes + 5) / 5) * 5];
  }
  // base 2
  return [Math.round((showerMinutes - 2) / 2) * 2, Math.round((showerMinutes + 2) / 2) * 2];
};

export function calculateCylinderWaterTemperature({ cylinderCapacity }: { cylinderCapacity: number }) {
  // This is a simplification based on a discussion with the heat design engineers
  return cylinderCapacity <= 100 ? 60 : 55;
}

/**
 * Calculate the daily hot water demand for domestic buildings in liters
 *
 * Reference: MIS3005D, section 5.6.2
 */
export const getHotWaterUsagePerDay = ({
  numberOfResidents,
  numberOfBedrooms,
}: {
  numberOfResidents: number;
  numberOfBedrooms: number;
}) => 45 * Math.max(numberOfResidents, numberOfBedrooms + 1);

export function getShowerMinutesRangeAndWaterReheatTimeMinutes(
  indoorUnit?: HeatPumpIndoorUnit,
  outdoorUnit?: HeatPumpOutdoorUnit,
) {
  if (!indoorUnit?.capacity?.value || !outdoorUnit?.effect) {
    return { waterReheatTimeMinutes: 0, showerTimeMinimumMinutes: 0, showerTimeMaximumMinutes: 0 };
  }

  const indoorUnitCapacity = indoorUnit.capacity.value;
  const cylinderWaterTemperature = calculateCylinderWaterTemperature({ cylinderCapacity: indoorUnitCapacity });
  const waterReheatTimeMinutes = calculateCylinderReheatTime({
    cylinderCapacity: indoorUnitCapacity,
    cylinderWaterTemperature,
    heatPumpPower: outdoorUnit.effect,
  });
  const [showerTimeMinimumMinutes, showerTimeMaximumMinutes] = getShowerMinutesRange({
    cylinderWaterTemperature,
    tankSizeLiters: indoorUnitCapacity,
  });

  return { waterReheatTimeMinutes, showerTimeMinimumMinutes, showerTimeMaximumMinutes };
}

type GetHeatDesignResultParams = {
  rooms: Room[];
  floorsHeatDesignResults: FloorHeatDesignResult[];
  hotWaterEnergyDemand: number;
  indoorUnit?: HeatPumpIndoorUnit;
  outdoorUnits?: DetailedHeatPumpOutdoorUnit[];
  countryCode: CountryCode;
  odt: number;
  flowTemp: number;
};
export const getHeatDesignResult = ({
  rooms,
  floorsHeatDesignResults,
  hotWaterEnergyDemand,
  indoorUnit,
  outdoorUnits,
  countryCode,
  odt,
  flowTemp,
}: GetHeatDesignResultParams): HeatDesignResult => {
  const roomHeatDesignResults = floorsHeatDesignResults.flatMap((floor) => floor.roomsResults);
  const totalAreaSqm = roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalFloorArea, 0);
  const heatingEnergyDemand = roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalRoom.energyDemand, 0);
  const totalHeatLoss = roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalRoom.heatLoss, 0);

  const productsData: Partial<HeatDesignResult> = {};

  if (outdoorUnits?.length === 1) {
    const outdoorUnit = outdoorUnits[0]!;
    const { waterReheatTimeMinutes, showerTimeMinimumMinutes, showerTimeMaximumMinutes } =
      getShowerMinutesRangeAndWaterReheatTimeMinutes(indoorUnit, outdoorUnit);
    productsData.waterReheatTimeMinutes = waterReheatTimeMinutes;
    productsData.showerTimeMinimumMinutes = showerTimeMinimumMinutes;
    productsData.showerTimeMaximumMinutes = showerTimeMaximumMinutes;

    const {
      maxHeatOutputWithoutElectricHeater = undefined,
      bivalencePoint = undefined,
      scop = undefined,
    } = outdoorUnit.technicalSpecification
      ? calculatePerformance(
          odt,
          flowTemp,
          totalHeatLoss,
          outdoorUnit.technicalSpecification,
          outdoorUnit.compatibilityGroup?.name,
        )
      : {};

    productsData.maxHeatOutputWithoutElectricHeaterWatt = maxHeatOutputWithoutElectricHeater;
    productsData.bivalencePointCelsius = bivalencePoint?.temperature;
    productsData.scop = scop;
  }

  const numberOfFloors = floorsHeatDesignResults.length;
  const numberOfBedrooms = countBedrooms(rooms);

  const outdoorUnitsIds: OutdoorUnitIds[] = (outdoorUnits || [])?.map((unit) => ({
    productId: unit.productId?.value,
    technicalSpecificationId: unit.technicalSpecificationId?.value,
  }));

  const totalVentilationHeatLoss = roomHeatDesignResults.reduce((acc, curr) => acc + curr.ventilation.heatLoss, 0);
  const totalVolume = roomHeatDesignResults.reduce(
    (acc, curr) => acc + calculateVolume(curr.totalFloorArea, curr.averageHeight),
    0,
  );
  const averageInsideOutsideTempDiff =
    roomHeatDesignResults.reduce((acc, curr) => {
      const roomTemp = curr.resolvedDesignRoomTemperatureCelsius;
      return acc + (roomTemp - odt);
    }, 0) / roomHeatDesignResults.length;

  const calculatedAirChangePerHourFromVentilationHeatLoss = getAirChangesPerHourFromSimpleVentilationHeatLoss({
    ventilationHeatLoss: totalVentilationHeatLoss,
    volumeInCubicMeters: totalVolume,
    insideOutsideTempDiff: averageInsideOutsideTempDiff,
  });

  return {
    floorsResults: floorsHeatDesignResults,
    totalHeatLoss,
    totalFabricHeatLoss: roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalFabric.heatLoss, 0),
    totalFabricEnergyDemand: roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalFabric.energyDemand, 0),
    totalVentilationHeatLoss,
    totalVentilationEnergyDemand: roomHeatDesignResults.reduce((acc, curr) => acc + curr.ventilation.energyDemand, 0),
    totalAdditionalHeatLoss: roomHeatDesignResults.reduce((acc, curr) => acc + curr.totalAdditional.heatLoss, 0),
    totalAdditionalEnergyDemand: roomHeatDesignResults.reduce(
      (acc, curr) => acc + curr.totalAdditional.energyDemand,
      0,
    ),
    dailyHotWaterEnergyDemand: hotWaterEnergyDemand / DAYS_PER_YEAR,
    yearlyHotWaterEnergyDemand: hotWaterEnergyDemand,
    heatingEnergyDemand,
    totalEnergyDemand: heatingEnergyDemand + hotWaterEnergyDemand,
    avgWattsPerMeterSquared: totalAreaSqm !== 0 ? totalHeatLoss / totalAreaSqm : 0,
    totalAreaSqm,
    ...productsData,
    totalOutputOfHeatEmittersWatt: roomHeatDesignResults.reduce(
      (acc, curr) => acc + curr.totalOutputOfHeatEmittersWatt,
      0,
    ),
    thermalBridgingUvalueAdjustment: THERMAL_BRIDGING_UVALUE_ADJUSTMENT,
    externalWallAdjustmentFactor: externalWallAreaAdjustmentFactor(countryCode),
    indoorUnitProductId: indoorUnit?.productId?.value,
    outdoorUnitsIds,
    numberOfFloors,
    numberOfBedrooms,
    averageAirChangesPerHour: calculatedAirChangePerHourFromVentilationHeatLoss,
  };
};

type GetHouseTotalOutputsParams = {
  rooms: Room[];
  floors: FloorProps[];
  projectUValues: ProjectFallbackUValues;
  outdoorTemperature: number;
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  numberOfResidents: number;
  heatPumpPackages: HeatPumpPackages;
  technicalSpecifications?: ProductTechnicalSpecification[];
  climateDataStore: ClimateDataStore;
  ventilationDesign: HouseInputVentilationDesign;
};

/**
 * @param obj.outdoorTemperature In most cases, this would be the adjusted outdoor design temperature. But
 * in a few cases, we want to find the heat loss at a different temperature e.g. the bivalence point.
 */
export const getHouseHeatDesignResult = ({
  rooms,
  constructionYear,
  countryCode,
  floors,
  outdoorTemperature,
  projectUValues,
  flowTemperature,
  flowReturnDeltaT,
  numberOfResidents,
  heatPumpPackages,
  technicalSpecifications,
  climateDataStore,
  ventilationDesign,
}: GetHouseTotalOutputsParams): HeatDesignResult => {
  const numBedrooms = countBedrooms(rooms);
  const floorsHeatDesignResults = getFloorsHeatDesignResults({
    rooms,
    floors,
    projectUValues,
    outdoorTemperature,
    constructionYear,
    countryCode,
    flowTemperature,
    flowReturnDeltaT,
    climateDataStore,
    ventilationDesign,
    dwellingNumberOfResidents: numberOfResidents,
    dwellingNumberOfBedrooms: numBedrooms,
  });

  const hotWaterEnergyDemand = getHotWaterEnergyDemand({
    numberOfResidents,
    numBedrooms,
  });

  const indoorUnit = heatPumpPackages.heatPumpIndoorUnit?.[0];

  const outdoorUnits = heatPumpPackages.heatPumpOutdoorUnit?.map((unit) => {
    const technicalSpecification = technicalSpecifications?.find(
      (spec) => spec.id?.value === unit.technicalSpecificationId?.value,
    );
    const heatPumpTechnicalSpecification =
      technicalSpecification?.details &&
      technicalSpecification.details.$case === 'heatPumpOutdoorUnitTechnicalSpecification'
        ? technicalSpecification.details.heatPumpOutdoorUnitTechnicalSpecification
        : undefined;

    return {
      ...unit,
      technicalSpecification: heatPumpTechnicalSpecification,
    };
  });

  return getHeatDesignResult({
    rooms,
    floorsHeatDesignResults,
    hotWaterEnergyDemand,
    indoorUnit,
    outdoorUnits,
    countryCode,
    odt: outdoorTemperature,
    flowTemp: flowTemperature,
  });
};

export function getAdjustedOutdoorDesignTemperatureCelsius({
  baseOutdoorDesignTemperature,
  isDwellingInExposedLocation,
  temperatureCompensation,
}: {
  baseOutdoorDesignTemperature: number;
  temperatureCompensation: number;
  isDwellingInExposedLocation: boolean;
}): number {
  return (
    baseOutdoorDesignTemperature +
    temperatureCompensation +
    (isDwellingInExposedLocation ? TEMPERATURE_ADJUSTMENT_EXPOSED_LOCATION : 0)
  );
}
