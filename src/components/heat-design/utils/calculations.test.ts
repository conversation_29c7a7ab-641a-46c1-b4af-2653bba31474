import { createExampleSvg, createImageMap } from 'tests/utils/testUtils';
import { CountryCode } from 'utils/marketConfigurations';
import { HeatPumpIndoorUnit, HeatPumpOutdoorUnit } from 'types/types';
import { UValue } from '../models/UValue';
import {
  SurfaceFallbackUValues,
  Room,
  RoofGlazing,
  FloorProps,
  Floor,
  RoomType,
  AdjacentKind,
  ROOM_TYPES,
  OutputType,
  SystemType,
  ACPHDefaultValue,
  VentilationCalculationMethod,
} from '../stores/types';
import {
  getDegreeAdjustmentForAltitude,
  getHeatLossForSurface,
  getRoomHeatDesignResult,
  getSimpleRoomVentilationHeatLoss,
  highCeilingAdditionFactor,
  areaAfterGlazing,
  roofGlazingAngleCorrection,
  getAdjacentForFloor,
  calculateCylinderReheatTime,
  calculateCylinderWaterTemperature,
  getHotWaterUsagePerDay,
  getShowerMinutesRange,
  getRoofGlazingHeatDesignResult,
  getShowerMinutesRangeAndWaterReheatTimeMinutes,
  getStandardRoomVentilationHeatLoss,
  getEnvelopeAirPermeabilityAt50Pa,
} from './calculations';
import { getDesignRoomTemp } from './heatCalculations';
import { ClimateDataStore } from '../stores/ClimateDataStore';
import { getStandardizedDefaultsForCountry } from './averageAirChangePerHour';
import {
  AirPermeabilityOption,
  DwellingExposureOption,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

const mockedClimateDataStore: ClimateDataStore = {
  baseOutdoorDesignTemperature: 3,
  degreeDays: 2255,
  localAnnualAverageExternalAirTemperature: 10,
  actions: { setClimateData: () => undefined },
};

const getAcphDefaultForBuildYearAndCountry = (countryCode: CountryCode, buildYear: number): ACPHDefaultValue => {
  return {
    type: 'standardized',
    standard: getStandardizedDefaultsForCountry(countryCode, buildYear),
  };
};

function createAsaFirstFloor(): FloorProps {
  return {
    floorName: 'First Floor',
    uid: 'asa_first_floor',
    floorNr: 1,
    ceilingUValue: 0.25,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    soilPercentageDefault: null,
  };
}

function createAsaGroundFloor(): FloorProps {
  return {
    floorName: 'Ground Floor',
    uid: 'asa_ground_floor',
    floorNr: 0,
    ceilingUValue: 0.25,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    soilPercentageDefault: null,
  };
}

function createAsaBedroom3(): Room {
  // This matches "Bedroom 3" in the Asa McDonald reference project.
  // Please see https://wiki.airahome.com/s/aira/p/heat-design-projects-236T7hZe39

  return {
    id: 'asa_bedroom_3',
    name: 'Bedroom 3',
    roomType: 'Bedroom',
    isHeated: true,
    level: 1,
    floor: 'asa_first_floor',
    floorId: 'asa_first_floor',
    enableRadiatorDeltaTAdjustment: false,
    designRoomTempOverride: 18,
    openFlue: 'none',
    totalVolume: 5.87 * 2.32,
    totalArea: 5.87,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    imageMap: createImageMap(),
    averageHeight: 2.32,
    surfaces: {
      floors: [
        {
          uid: 'asa_bedroom_3_floor',
          area: 5.87,
          uValue: new UValue('Ground floor no insulation', 1.15),
          belowFloor: AdjacentKind.SolidFloor,
          surfaceType: 'floors',
        },
      ],
      walls: [
        {
          surfaceType: 'externalWalls',
          uid: 'asa_bedroom_3_wall_external',
          length: 2.67,
          uValue: new UValue(
            'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
            0.45,
          ),
          imageMap: createImageMap(),
          soilPercentage: 0,
        },
        {
          surfaceType: 'internalWalls',
          uid: 'asa_bedroom_3_wall_internal',
          length: 4.87,
          uValue: new UValue('Plaster, Brick 102.5mm, Plaster', 1.76),
          imageMap: createImageMap(),
          adjoiningRoomUID: 'other_room',
        },
        {
          surfaceType: 'partyWalls',
          uid: 'asa_bedroom_3_wall_party',
          length: 2.2,
          uValue: new UValue('Party wall 1940+', 0.5),
          imageMap: createImageMap(),
        },
      ],
      roofsOrCeilings: [
        {
          uid: 'asa_bedroom_3_roof',
          area: 5.87,
          uValue: new UValue('Pitched roof with tiles, 300mm Insulation', 0.12),
          spaceAbove: {
            type: AdjacentKind.Outside,
          },
          surfaceType: 'roofsOrCeilings',
        },
      ],
      doors: [],
      windows: [
        {
          uid: 'asa_bedroom_3_window',
          width: 1.179, // sqrt of the area
          height: 1.179, // sqrt of the area
          area: 1.39,
          uValue: new UValue('Standard Double Glazing Wood/PVC frame', 2.8),
          imageMap: createImageMap(),
          roomUID: 'asa_bedroom_3',
          wallUID: 'asa_bedroom_3_wall_external',
          surfaceType: 'windows',
        },
      ],
      roofGlazings: [],
    },
    radiators: [],
  };
}

function createAsaToilet(): Room {
  return {
    id: 'asa_toilet',
    name: 'Toilet',
    roomType: 'Toilet',
    isHeated: true,
    level: 0,
    floor: 'asa_ground_floor',
    floorId: 'asa_ground_floor',
    designRoomTempOverride: 18,
    enableRadiatorDeltaTAdjustment: false,
    avgAirChangesPerHourOverride: 2,
    openFlue: 'none',
    totalArea: 1.66,
    totalVolume: 1.66 * 2.34,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    imageMap: createImageMap(),
    averageHeight: 2.34,
    surfaces: {
      floors: [
        {
          uid: 'asa_toilet_floor',
          area: 1.66,
          uValue: new UValue('Ground floor no insulation', 1.15),
          belowFloor: AdjacentKind.SolidFloor,
          surfaceType: 'floors',
        },
      ],
      walls: [
        {
          surfaceType: 'externalWalls',
          uid: 'asa_toilet_wall_external',
          length: 6.1,
          uValue: new UValue(
            'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
            0.45,
          ),
          imageMap: createImageMap(),
          soilPercentage: 0,
        },
        {
          surfaceType: 'internalWalls',
          uid: 'asa_toilet_wall_internal',
          length: 2.34,
          uValue: new UValue('Plaster, Brick 102.5mm, Plaster', 1.76),
          imageMap: createImageMap(),
          adjoiningRoomUID: 'other_room',
        },
      ],
      roofsOrCeilings: [
        {
          uid: 'asa_toilet_roof',
          area: 1.66,
          uValue: new UValue('Pitched roof with tiles, 300mm Insulation', 0.12),
          spaceAbove: {
            type: AdjacentKind.Outside,
          },
          surfaceType: 'roofsOrCeilings',
        },
      ],
      doors: [],
      windows: [
        {
          uid: 'asa_toilet_window',
          width: 0.906, // sqrt of the area
          height: 0.906, // sqrt of the area
          area: 0.82,
          uValue: new UValue('Standard Double Glazing Wood/PVC frame', 2.8),
          imageMap: createImageMap(),
          roomUID: 'asa_toilet',
          wallUID: 'asa_toilet_wall_external',
          surfaceType: 'windows',
        },
      ],
      roofGlazings: [],
    },
    radiators: [],
  };
}

function createRoomWithAllWallTypesAndWindows(): Room {
  return {
    id: 'room',
    name: 'Living Room',
    roomType: 'Living room',
    isHeated: true,
    level: 1,
    floor: 'first_floor',
    floorId: 'first_floor',
    enableRadiatorDeltaTAdjustment: false,
    designRoomTempOverride: 21,
    openFlue: 'none',
    totalVolume: 5.87 * 2.32,
    totalArea: 5.87,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    imageMap: createImageMap(),
    averageHeight: 2,
    surfaces: {
      floors: [],
      walls: [
        {
          surfaceType: 'externalWalls',
          uid: 'wall_external',
          length: 3,
          uValue: new UValue(
            'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster',
            0.45,
          ),
          imageMap: createImageMap(),
          soilPercentage: 0,
        },
        {
          surfaceType: 'internalWalls',
          uid: 'wall_internal',
          length: 4,
          uValue: new UValue('Plaster, Brick 102.5mm, Plaster', 1.76),
          imageMap: createImageMap(),
          adjoiningRoomUID: 'other_room',
        },
        {
          surfaceType: 'partyWalls',
          uid: 'wall_party',
          length: 2,
          uValue: new UValue('Party wall 1940+', 0.5),
          imageMap: createImageMap(),
        },
      ],
      roofsOrCeilings: [],
      doors: [],
      windows: [
        {
          uid: 'window_on_external_wall',
          width: Math.sqrt(3),
          height: Math.sqrt(3),
          area: 3,
          uValue: new UValue('Simple U-value', 2),
          imageMap: createImageMap(),
          roomUID: 'room',
          wallUID: 'wall_external',
          surfaceType: 'windows',
        },
        {
          uid: 'window_on_internal_wall',
          width: Math.sqrt(3),
          height: Math.sqrt(3),
          area: 3,
          uValue: new UValue('Simple U-value', 2),
          imageMap: createImageMap(),
          roomUID: 'room',
          wallUID: 'wall_internal',
          surfaceType: 'windows',
        },
        {
          uid: 'window_on_party_wall',
          width: Math.sqrt(3),
          height: Math.sqrt(3),
          area: 3,
          uValue: new UValue('Simple U-value', 2),
          imageMap: createImageMap(),
          roomUID: 'room',
          wallUID: 'wall_party',
          surfaceType: 'windows',
        },
      ],
      roofGlazings: [],
    },
    radiators: [],
  };
}

function createRoomWithAllRadiators(): Room {
  // This is based on the attic/loft room of this project:
  // https://aerospace.systest.airahome.com/de/solution/d4fda8f5-218a-4711-af81-a27e48d3377e/heat-design

  return {
    id: 'room',
    name: 'Attic / Loft',
    roomType: 'Other',
    isHeated: true,
    level: 1,
    floor: 'first_floor',
    floorId: 'first_floor',
    enableRadiatorDeltaTAdjustment: false,
    designRoomTempOverride: 21,
    openFlue: 'none',
    totalVolume: 6.25 * 2.44,
    totalArea: 6.25,
    imageSvgData: createExampleSvg(),
    simplifiedImageSvgData: createExampleSvg(),
    imageMap: createImageMap(),
    averageHeight: 2.44,
    surfaces: {
      floors: [
        {
          uid: 'floor',
          area: 6.25,
          uValue: new UValue('Building regulations for new-buildsn', 0.6),
          belowFloor: AdjacentKind.Heated,
          surfaceType: 'floors',
        },
      ],
      walls: [
        {
          surfaceType: 'externalWalls',
          uid: 'wall_external',
          length: 2.5 * 4,
          uValue: new UValue('Building regulations for new-builds', 0.6),
          imageMap: createImageMap(),
          soilPercentage: 0,
        },
      ],
      roofsOrCeilings: [
        {
          uid: 'roof',
          area: 6.25,
          uValue: new UValue('Building regulations for new-builds', 0.35),
          spaceAbove: {
            type: AdjacentKind.Outside,
          },
          surfaceType: 'roofsOrCeilings',
        },
      ],
      doors: [],
      windows: [],
      roofGlazings: [],
    },
    radiators: [],
    underfloorHeating: {
      outputType: OutputType.AUTOMATIC,
      systemType: SystemType.WATER,
    },
  };
}

describe('calculations', () => {
  describe('getVentilationHeatLoss', () => {
    // Reference: DHDG2021 3.4.1 example
    it('should calculate DHDG example', () => {
      const result = getSimpleRoomVentilationHeatLoss({
        roomVolumeInCubicMeters: 60,
        avgAirChangesPerHour: 1.5,
        insideOutsideTempDiff: 21 - -3,
      });
      expect(result).toBeCloseTo(712.8);
    });
  });

  describe('getHeatLossForSurface', () => {
    // Reference: DHDG2021 3.4.2 example 2
    it('should calculate DHDG example', () => {
      const result = getHeatLossForSurface({
        area: 20,
        uValue: new UValue('Test', 0.38),
        insideOutsideTempDiff: 21 - -3,
      });
      expect(result).toBeCloseTo(230.4);
    });
  });

  describe('getDegreeAdjustmentForAltitude', () => {
    // Reference: DHDG2021 3.5.3.3 example (steps 2 and 3)
    it('should calculate DHDG example', () => {
      const result = getDegreeAdjustmentForAltitude({
        altitude: 125,
      });
      expect(result).toBeCloseTo(-0.6);
    });
  });

  describe('highCeilingAdditionFactor', () => {
    // Reference: DHDG2021, section 3.5.3.2, High ceiling example.
    it('should calculate DHGDG example', () => {
      expect(highCeilingAdditionFactor(5.5)).toBeCloseTo(0.05);
    });
    it('should not affect low ceilings', () => {
      expect(highCeilingAdditionFactor(2.4)).toBeCloseTo(0);
    });
  });

  describe('getRoomHeatLoss - Asa Bedroom 3', () => {
    const firstFloor = createAsaFirstFloor();
    const groundFloor = createAsaGroundFloor();
    const result = getRoomHeatDesignResult({
      room: createAsaBedroom3(),
      floor: createAsaFirstFloor(),
      floors: [firstFloor, groundFloor],
      projectUValues: {},
      flowReturnDeltaT: 10,
      flowTemperature: 45,
      outdoorTemperature: -3.31,
      roomTemps: [
        {
          name: 'other_room',
          roomUID: 'other_room',
          roomTemp: 18,
        },
      ],
      constructionYear: 1969,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      },
      dwellingNumberOfBedrooms: 3,
      dwellingNumberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });

    it('should calculate floor heat loss', () => {
      expect(result.surfaceTotals.floors.heatLoss).toBeCloseTo(58.7);
    });
    it('should calculate external wall heat loss', () => {
      expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(56.31);
    });
    it('should calculate internal wall heat loss', () => {
      expect(result.surfaceTotals.internalWalls.heatLoss).toBeCloseTo(0);
    });
    it('should calculate party wall heat loss', () => {
      expect(result.surfaceTotals.partyWalls.heatLoss).toBeCloseTo(24.5);
    });
    it('should calculate roof heat loss', () => {
      expect(result.surfaceTotals.roofsOrCeilings.heatLoss).toBeCloseTo(27.52);
    });
    it('should calculate window heat loss', () => {
      expect(result.surfaceTotals.windows.heatLoss).toBeCloseTo(85.9);
    });
    it('should calculate ventilation heat loss', () => {
      expect(result.ventilation.heatLoss).toBeCloseTo(95.78, 1);
    });

    // Totals for the room
    it('should calculate total room heat loss', () => {
      expect(result.totalRoom.heatLoss).toBeCloseTo(348.7, 1);
    });
    it('should calculate the W/m2', () => {
      expect(result.wattsPerMeterSquared).toBeCloseTo(59.4);
    });
    it('should calculate total room energy demand', () => {
      expect(result.totalRoom.energyDemand).toBeCloseTo(823.35, 1);
    });
  });

  describe('getRoomHeatLoss - Asa Toilet', () => {
    const firstFloor = createAsaFirstFloor();
    const groundFloor = createAsaGroundFloor();
    const result = getRoomHeatDesignResult({
      room: createAsaToilet(),
      projectUValues: {},
      floor: createAsaFirstFloor(),
      floors: [firstFloor, groundFloor],
      outdoorTemperature: -3.31,
      flowReturnDeltaT: 10,
      flowTemperature: 45,
      roomTemps: [
        {
          name: 'other_room',
          roomUID: 'other_room',
          roomTemp: 18,
        },
      ],
      constructionYear: 1969,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      },
      dwellingNumberOfBedrooms: 3,
      dwellingNumberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });

    it('should calculate floor heat loss', () => {
      expect(result.surfaceTotals.floors.heatLoss).toBeCloseTo(16.6);
    });
    it('should calculate external wall heat loss', () => {
      expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(157.69);
    });
    it('should calculate internal wall heat loss', () => {
      expect(result.surfaceTotals.internalWalls.heatLoss).toBeCloseTo(0);
    });
    it('should calculate party wall heat loss', () => {
      expect(result.surfaceTotals.partyWalls.heatLoss).toBeCloseTo(0);
    });
    it('should calculate roof heat loss', () => {
      expect(result.surfaceTotals.roofsOrCeilings.heatLoss).toBeCloseTo(7.78);
    });
    it('should calculate window heat loss', () => {
      expect(result.surfaceTotals.windows.heatLoss).toBeCloseTo(50.68);
    });
    it('should calculate ventilation heat loss', () => {
      expect(result.ventilation.heatLoss).toBeCloseTo(54.64, 1);
    });

    // Totals for the room
    it('should calculate total room heat loss', () => {
      expect(result.totalRoom.heatLoss).toBeCloseTo(287.4, 1);
    });
    it('should calculate the W/m2', () => {
      expect(result.wattsPerMeterSquared).toBeCloseTo(173.12, 1);
    });
    it('should calculate total room energy demand', () => {
      expect(result.totalRoom.energyDemand).toBeCloseTo(729.84, 1);
    });
  });
});

describe('window heat loss', () => {
  const firstFloor = createAsaFirstFloor();
  const groundFloor = createAsaGroundFloor();
  const room = createRoomWithAllWallTypesAndWindows();
  const result = getRoomHeatDesignResult({
    room,
    floor: createAsaFirstFloor(),
    floors: [firstFloor, groundFloor],
    projectUValues: {},
    outdoorTemperature: -3,
    flowReturnDeltaT: 10,
    flowTemperature: 45,
    roomTemps: [
      {
        name: 'other_room',
        roomUID: 'other_room',
        roomTemp: 18,
      },
    ],
    constructionYear: 1969,
    countryCode: CountryCode.GB,
    climateDataStore: mockedClimateDataStore,
    ventilationDesign: {
      calculationMethod: VentilationCalculationMethod.SIMPLE,
      acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
    },
    dwellingNumberOfBedrooms: 3,
    dwellingNumberOfResidents: 3,
    dwellingExternalEnvelopeAreaInSquareMeters: 0,
    dwellingInternalFloorAreaInSquareMeters: 0,
  });

  // This test case has an external wall with a window, an internal wall with a window, and a party wall with a window.
  // We should not count any heat loss from the window on the internal wall or the one party wall, but we should count it for
  // the one on the external wall:
  //
  //    A = 3 m^2, U-value = 2, Temp diff = 21 - (-3) = 24
  //    Heat loss: 3 * (2 + 0.1) * 24 = 151.2
  //
  // Regarding the walls themselves, we should substract the window area only for the external wall, not for the internal wall
  // or the party wall:
  //
  //    External wall:
  //        A = 6 m^2 - 3 m^2, U-value = 0.45, Temp diff = 21 - (-3) = 24
  //                  ^^^^^^^^ subtracting the window area
  //        Heat loss: (6 - 3) * (0.45 + 0.1) * 24 = 39.6
  //    Internal wall:
  //        A = 8 m^2, U-value = 1.76, Temp diff = 21 - 18 = 3
  //        Heat loss: 8 * (1.76 + 0.1) * 3 = 44.64
  //    Party wall:
  //        A = 4 m^2, U-value = 0.5, Temp diff = 21 - 10 = 11
  //        Heat loss: 4 * (0.5 + 0.1) * 11 = 26.4

  it('should calculate window heat loss for external wall only', () => {
    expect(result.surfaceTotals.windows.heatLoss).toBeCloseTo(151.2);
  });
  it('should calculate wall heat loss', () => {
    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(39.6);
    expect(result.surfaceTotals.internalWalls.heatLoss).toBeCloseTo(44.64);
    expect(result.surfaceTotals.partyWalls.heatLoss).toBeCloseTo(26.4);
  });
});

describe('radiator output', () => {
  const floor = createAsaFirstFloor();
  const room = createRoomWithAllRadiators();
  const result = getRoomHeatDesignResult({
    room,
    floor: floor,
    floors: [floor],
    projectUValues: {},
    outdoorTemperature: -2.3,
    flowReturnDeltaT: 10,
    flowTemperature: 45,
    roomTemps: [],
    constructionYear: 1999,
    countryCode: CountryCode.GB,
    climateDataStore: mockedClimateDataStore,
    ventilationDesign: {
      calculationMethod: VentilationCalculationMethod.SIMPLE,
      acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1999),
    },
    dwellingNumberOfBedrooms: 1,
    dwellingNumberOfResidents: 2,
    dwellingExternalEnvelopeAreaInSquareMeters: 0,
    dwellingInternalFloorAreaInSquareMeters: 0,
  });

  it('should calculate the underfloor heating outputs', () => {
    expect(result.calculatedUnderfloorHeatingOutputWatt).toBeCloseTo(639.38);
    expect(result.calculatedUnderfloorHeatingFlowRate).toBeCloseTo(55.5, 1);
  });
});

describe('floor heat loss', () => {
  const adjustedOutdoorDesignTemperature = -3;
  const designRoomTemp = 21;
  const localAnnualAverageExternalAirTemperature = 9;
  const floorSurface: Floor = {
    uid: 'floor',
    area: 10,
    uValue: new UValue('Ground floor no insulation', 1.15),
    surfaceType: 'floors',
  };
  it('should calculate temp diff for solid floor', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.SolidFloor,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        { ...mockedClimateDataStore, localAnnualAverageExternalAirTemperature },
      ),
    ).toEqual({
      temperature: localAnnualAverageExternalAirTemperature,
      temperatureDifference: 12,
    });
  });

  it('should calculate temp diff for suspended floor', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.SuspendedFloor,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        mockedClimateDataStore,
      ),
    ).toEqual({
      temperature: adjustedOutdoorDesignTemperature,
      temperatureDifference: 24,
    });
  });

  it('should calculate temp diff for heated room below floor', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.Heated,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        mockedClimateDataStore,
      ),
    ).toEqual({
      temperatureDifference: 0,
    });
  });

  it('should calculate temp diff for unheated room below floor (UK)', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.Unheated,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        { ...mockedClimateDataStore, localAnnualAverageExternalAirTemperature },
      ),
    ).toEqual({
      temperature: localAnnualAverageExternalAirTemperature,
      temperatureDifference: 12,
    });
  });

  it('should calculate temp diff for unheated room below floor (DE)', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.Unheated,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        { ...mockedClimateDataStore, localAnnualAverageExternalAirTemperature },
      ),
    ).toEqual({
      temperature: localAnnualAverageExternalAirTemperature,
      temperatureDifference: 12,
    });
  });
  it('should calculate temp diff for unheated room below floor (IT)', () => {
    expect(
      getAdjacentForFloor(
        {
          ...floorSurface,
          belowFloor: AdjacentKind.Unheated,
        },
        designRoomTemp,
        adjustedOutdoorDesignTemperature,
        'roomName',
        { ...mockedClimateDataStore, localAnnualAverageExternalAirTemperature },
      ),
    ).toEqual({
      temperature: localAnnualAverageExternalAirTemperature,
      temperatureDifference: 12,
    });
  });
});

describe('fallback u-values', () => {
  // Test that the calculations use the fallback U-values when the surface doesn't have its own U-value.
  const asaBedroom3 = createAsaBedroom3();
  const asaBedroom3WallExternalUValue = asaBedroom3.surfaces.walls.find(
    (w) => w.uid === 'asa_bedroom_3_wall_external',
  )!.uValue;
  delete asaBedroom3.surfaces.walls.find((w) => w.uid === 'asa_bedroom_3_wall_external')!.uValue;

  it('should fail without any fallbacks', () => {
    const firstFloor = createAsaFirstFloor();
    const groundFloor = createAsaGroundFloor();
    const result = getRoomHeatDesignResult({
      room: asaBedroom3,
      floor: createAsaFirstFloor(),
      floors: [firstFloor, groundFloor],
      projectUValues: {},
      outdoorTemperature: -3.31,
      flowReturnDeltaT: 10,
      flowTemperature: 45,
      roomTemps: [
        {
          name: 'other_room',
          roomUID: 'other_room',
          roomTemp: 18,
        },
      ],
      constructionYear: 1969,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      },
      dwellingNumberOfBedrooms: 3,
      dwellingNumberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(10.24);
  });

  it('should use the floor fallback U-value for external walls', () => {
    const floorUValues: SurfaceFallbackUValues = {
      externalWalls: asaBedroom3WallExternalUValue,
    };
    const firstFloor = createAsaFirstFloor();
    const groundFloor = createAsaGroundFloor();
    firstFloor.floorUValues = floorUValues;

    const result = getRoomHeatDesignResult({
      room: asaBedroom3,
      floor: firstFloor,
      projectUValues: {},
      floors: [firstFloor, groundFloor],
      outdoorTemperature: -3.31,
      flowTemperature: 45,
      flowReturnDeltaT: 10,
      roomTemps: [
        {
          name: 'other_room',
          roomUID: 'other_room',
          roomTemp: 18,
        },
      ],
      constructionYear: 1969,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      },
      dwellingNumberOfBedrooms: 3,
      dwellingNumberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(56.31);
  });

  it('should use the project fallback U-value for external walls', () => {
    const projectUValues: SurfaceFallbackUValues = {
      externalWalls: asaBedroom3WallExternalUValue,
    };
    const firstFloor = createAsaFirstFloor();
    const groundFloor = createAsaGroundFloor();

    const result = getRoomHeatDesignResult({
      room: asaBedroom3,
      projectUValues,
      floor: createAsaFirstFloor(),
      floors: [firstFloor, groundFloor],
      outdoorTemperature: -3.31,
      flowReturnDeltaT: 10,
      flowTemperature: 45,
      roomTemps: [
        {
          name: 'other_room',
          roomUID: 'other_room',
          roomTemp: 18,
        },
      ],
      constructionYear: 1969,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      },
      dwellingNumberOfBedrooms: 3,
      dwellingNumberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(56.31);
  });
});

describe('areaAfterGlazing', () => {
  test('should return the same area when there are no roof glazings', () => {
    expect(areaAfterGlazing(5, [])).toBe(5);
  });
  test('should return different area when there are roof glazings', () => {
    expect(areaAfterGlazing(5, [{ uid: 'wetrf34tw4fs', width: 1, length: 1, surfaceType: 'roofGlazings' }])).toBe(4);
  });
});

describe('roofGlazingAngleCorrection', () => {
  const surface = {
    uid: '6bb7de08-20cb-45b8-b553-db361fa0b6e8',
    length: 1,
    width: 1,
    surfaceType: 'roofGlazings',
    uValue: {
      name: 'Current Building Regulations Window Wood/PVC frame',
      value: 2,
      id: 'Current Building Regulations Window Wood/PVC frame2',
    },
  } as RoofGlazing;

  test(`should return 0.5`, () => {
    expect(roofGlazingAngleCorrection({ ...surface })).toBe(0.5);
  });
});

describe('getOtherSurfaceOutput for roofGlazings', () => {
  it('should calculate the heat loss and energy demand for a roof glazing', () => {
    const roofGlazing = {
      length: 1,
      width: 1,
      uid: 'sdrtfyg4567',
      area: 1,
      uValue: new UValue('Standard Double Glazing Wood/PVC frame', 2.8),
      surfaceType: 'roofGlazings' as const,
    };
    const inputs = {
      degreeDays: 2255,
      projectUValues: {},
      floor: createAsaFirstFloor(),
      floors: [createAsaFirstFloor(), createAsaGroundFloor()],
      designRoomTemp: 21,
      outdoorDesignTemp: 5,
    };

    const result = getRoofGlazingHeatDesignResult(
      roofGlazing,
      inputs.degreeDays,
      inputs.projectUValues,
      inputs.floor,
      inputs.floors,
      inputs.designRoomTemp,
      inputs.outdoorDesignTemp,
    );
    expect(result.heatLoss).toBeCloseTo(54.4);
    expect(result.energyDemand).toBe(184.008);
  });
});

describe('calculateCylinderReheatTime', () => {
  // Examples in MCS MGD 007 2.2
  it('should match example for 3 kW heat pump', () => {
    expect(
      calculateCylinderReheatTime({
        cylinderCapacity: 60,
        cylinderWaterTemperature: 60,
        heatPumpPower: 3,
      }),
    ).toBeCloseTo(70, 0);
  });

  it('should match example for 8 kW heat pump', () => {
    expect(
      calculateCylinderReheatTime({
        cylinderCapacity: 60,
        cylinderWaterTemperature: 60,
        heatPumpPower: 8,
      }),
    ).toBeCloseTo(26, 0);
  });
});

describe('calculateCylinderWaterTemperature', () => {
  it('should be 55C for cylinders more than 100L', () => {
    expect(calculateCylinderWaterTemperature({ cylinderCapacity: 101 })).toBe(55);
  });

  it('should be 60C for cylinders of 100L', () => {
    expect(calculateCylinderWaterTemperature({ cylinderCapacity: 100 })).toBe(60);
  });

  it('should be 60C for cylinders less than 100L', () => {
    expect(calculateCylinderWaterTemperature({ cylinderCapacity: 100 })).toBe(60);
  });
});

describe('getDesignRoomTemp', () => {
  it.each<[CountryCode, number, RoomType, number]>([
    [CountryCode.GB, 2005, 'Bathroom', 22],
    [CountryCode.GB, 2005, 'Bedroom', 18],
    [CountryCode.GB, 2005, 'Bedroom, including en suite bathroom', 21],
    [CountryCode.GB, 2005, 'Bedroom/study', 21],
    [CountryCode.GB, 2005, 'Bedsitting room', 21],
    [CountryCode.GB, 2005, 'Breakfast room', 21],
    [CountryCode.GB, 2005, 'Cloakroom/WC', 18],
    [CountryCode.GB, 2005, 'Dining room', 21],
    [CountryCode.GB, 2005, 'Dressing room', 18],
    [CountryCode.GB, 2005, 'Family/breakfast room', 21],
    [CountryCode.GB, 2005, 'Games room', 21],
    [CountryCode.GB, 2005, 'Hall', 18],
    [CountryCode.GB, 2005, 'Internal room or corridor', 18],
    [CountryCode.GB, 2005, 'Kitchen', 18],
    [CountryCode.GB, 2005, 'Landing', 18],
    [CountryCode.GB, 2005, 'Living room', 21],
    [CountryCode.GB, 2005, 'Lounge/sitting room', 21],
    [CountryCode.GB, 2005, 'Shower room', 22],
    [CountryCode.GB, 2005, 'Store room', 18],
    [CountryCode.GB, 2005, 'Study', 21],
    [CountryCode.GB, 2005, 'Toilet', 18],
    [CountryCode.GB, 2005, 'Utility room', 18],
    [CountryCode.GB, 2005, 'Other', 21],

    // When UK rooms are built after 2005 all have a design temperature of 21 except the bathroom, which has 22
    [CountryCode.GB, 2006, 'Bathroom', 22],
    [CountryCode.GB, 2006, 'Bedroom', 21],
    [CountryCode.GB, 2006, 'Bedroom, including en suite bathroom', 21],
    [CountryCode.GB, 2006, 'Bedroom/study', 21],
    [CountryCode.GB, 2006, 'Bedsitting room', 21],
    [CountryCode.GB, 2006, 'Breakfast room', 21],
    [CountryCode.GB, 2006, 'Cloakroom/WC', 21],
    [CountryCode.GB, 2006, 'Dining room', 21],
    [CountryCode.GB, 2006, 'Dressing room', 21],
    [CountryCode.GB, 2006, 'Family/breakfast room', 21],
    [CountryCode.GB, 2006, 'Games room', 21],
    [CountryCode.GB, 2006, 'Hall', 21],
    [CountryCode.GB, 2006, 'Internal room or corridor', 21],
    [CountryCode.GB, 2006, 'Kitchen', 21],
    [CountryCode.GB, 2006, 'Landing', 21],
    [CountryCode.GB, 2006, 'Living room', 21],
    [CountryCode.GB, 2006, 'Lounge/sitting room', 21],
    [CountryCode.GB, 2006, 'Shower room', 21],
    [CountryCode.GB, 2006, 'Store room', 21],
    [CountryCode.GB, 2006, 'Study', 21],
    [CountryCode.GB, 2006, 'Toilet', 21],
    [CountryCode.GB, 2006, 'Utility room', 21],
    [CountryCode.GB, 2006, 'Other', 21],

    [CountryCode.DE, 2010, 'Bathroom', 24],
    [CountryCode.DE, 2010, 'Bedroom', 20],
    [CountryCode.DE, 2010, 'Bedroom, including en suite bathroom', 20],
    [CountryCode.DE, 2010, 'Bedroom/study', 20],
    [CountryCode.DE, 2010, 'Bedsitting room', 20],
    [CountryCode.DE, 2010, 'Breakfast room', 20],
    [CountryCode.DE, 2010, 'Cloakroom/WC', 20],
    [CountryCode.DE, 2010, 'Dining room', 20],
    [CountryCode.DE, 2010, 'Dressing room', 20],
    [CountryCode.DE, 2010, 'Family/breakfast room', 20],
    [CountryCode.DE, 2010, 'Games room', 20],
    [CountryCode.DE, 2010, 'Hall', 18],
    [CountryCode.DE, 2010, 'Internal room or corridor', 18],
    [CountryCode.DE, 2010, 'Kitchen', 20],
    [CountryCode.DE, 2010, 'Landing', 18],
    [CountryCode.DE, 2010, 'Living room', 20],
    [CountryCode.DE, 2010, 'Lounge/sitting room', 20],
    [CountryCode.DE, 2010, 'Shower room', 24],
    [CountryCode.DE, 2010, 'Store room', 16],
    [CountryCode.DE, 2010, 'Study', 20],
    [CountryCode.DE, 2010, 'Toilet', 20],
    [CountryCode.DE, 2010, 'Utility room', 16],
    [CountryCode.DE, 2010, 'Other', 20],
  ])(
    'should return the standard design room temperature for country %p, construction year %p and room type %p',
    (countryCode, constructionYear, roomType, expectedTemperature) => {
      expect(
        getDesignRoomTemp(
          {
            isHeated: true,
            roomType,
          },
          constructionYear,
          countryCode,
          mockedClimateDataStore,
        ),
      ).toEqual(expectedTemperature);
    },
  );

  it.each<[CountryCode, number]>([
    [CountryCode.GB, 10],
    [CountryCode.DE, 10],
  ])(
    'should return the average country temperature when the room is unheated',
    async (countryCode, expectedTemperature) => {
      expect(
        getDesignRoomTemp({ isHeated: false, roomType: 'Living room' }, 1995, countryCode, mockedClimateDataStore),
      ).toBe(expectedTemperature);
    },
  );

  it('should return default design room temperature for Italy', () => {
    // The relevant ones for us are living spaces 20 and Bathrooms 24. Independent of build year.
    const RoomTypeValues = ROOM_TYPES.map((roomType) => roomType).filter((roomType) => roomType !== 'Bathroom');
    for (const roomType of RoomTypeValues) {
      expect(getDesignRoomTemp({ isHeated: true, roomType }, 1995, CountryCode.IT, mockedClimateDataStore)).toBe(20);
    }
    expect(
      getDesignRoomTemp({ isHeated: true, roomType: 'Bathroom' }, 1995, CountryCode.IT, mockedClimateDataStore),
    ).toBe(24);
  });

  it('should return the design room temperature override if it is present', () => {
    expect(
      getDesignRoomTemp(
        { isHeated: true, roomType: 'Living room', designRoomTempOverride: 25 },
        1995,
        CountryCode.GB,
        mockedClimateDataStore,
      ),
    ).toBe(25);
  });

  it('should return the average country temperature if the room is unheated and the design room temperature override is present', () => {
    expect(
      getDesignRoomTemp(
        { isHeated: false, roomType: 'Living room', designRoomTempOverride: 25 },
        1995,
        CountryCode.GB,
        mockedClimateDataStore,
      ),
    ).toBe(10);
  });
});

describe('hot water demand', () => {
  it('should use number of bedrooms + 1 when more than residents', () => {
    const result = getHotWaterUsagePerDay({ numberOfResidents: 3, numberOfBedrooms: 3 });
    expect(result).toBe(180);
  });
  it('should use number of residents when more than bedrooms + 1', () => {
    const result = getHotWaterUsagePerDay({ numberOfResidents: 5, numberOfBedrooms: 3 });
    expect(result).toBe(225);
  });
});

describe('external wall heat loss', () => {
  const firstFloor = createAsaFirstFloor();
  const groundFloor = createAsaGroundFloor();
  const room = createRoomWithAllWallTypesAndWindows();
  const partialInputs = {
    room,
    floor: createAsaFirstFloor(),
    floors: [firstFloor, groundFloor],
    projectUValues: {},
    flowTemperature: 45,
    flowReturnDeltaT: 10,
    localAnnualAverageExternalAirTemperature: 10,
    outdoorTemperature: -3,
    roomTemps: [
      {
        name: 'other_room',
        roomUID: 'other_room',
        roomTemp: 18,
      },
    ],
    constructionYear: 1969,
  };

  it('should increase area for DE', () => {
    const result = getRoomHeatDesignResult({
      ...partialInputs,
      countryCode: CountryCode.DE,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.DE, partialInputs.constructionYear),
      },
      dwellingNumberOfBedrooms: 1,
      dwellingNumberOfResidents: 2,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });
    const externalWall = result.surfaceDetails.externalWalls[0]!;

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(59.4);
    expect(externalWall.area).toBeCloseTo(7.5);
  });

  it('should not increase area for GB', () => {
    const result = getRoomHeatDesignResult({
      ...partialInputs,
      countryCode: CountryCode.GB,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, partialInputs.constructionYear),
      },
      dwellingNumberOfBedrooms: 1,
      dwellingNumberOfResidents: 2,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });
    const externalWall = result.surfaceDetails.externalWalls[0]!;

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(39.6);
    expect(externalWall.area).toBeCloseTo(6);
  });

  it('should not increase area for IT', () => {
    const result = getRoomHeatDesignResult({
      ...partialInputs,
      countryCode: CountryCode.IT,
      climateDataStore: mockedClimateDataStore,
      ventilationDesign: {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.IT, partialInputs.constructionYear),
      },
      dwellingNumberOfBedrooms: 1,
      dwellingNumberOfResidents: 2,
      dwellingExternalEnvelopeAreaInSquareMeters: 0,
      dwellingInternalFloorAreaInSquareMeters: 0,
    });
    const externalWall = result.surfaceDetails.externalWalls[0]!;

    expect(result.surfaceTotals.externalWalls.heatLoss).toBeCloseTo(39.6);
    expect(externalWall.area).toBeCloseTo(6);
  });
});

describe('calculate getShowerMinutesRange', () => {
  // NOTE: These are the values that are displayed in the App and should be the
  // same, to get this value we made the changes to the
  // SHOWERHEAD_LITERS_PER_MIN
  it('shower time range calculations for 100L tanks, we use the actual tankSize (85)', () => {
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 55, tankSizeLiters: 85 }).toString()).toBe('14,18');
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 60, tankSizeLiters: 85 }).toString()).toBe('16,20');
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 65, tankSizeLiters: 85 }).toString()).toBe('18,22');
  });

  it('shower time range calculations for 250L tanks, we use the actual tankSize (217)', () => {
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 50, tankSizeLiters: 217 }).toString()).toBe('35,45');
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 55, tankSizeLiters: 217 }).toString()).toBe('40,50');
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 65, tankSizeLiters: 217 }).toString()).toBe('45,55');

    // some old values
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 53, tankSizeLiters: 217 }).toString()).toBe('35,45');
    expect(getShowerMinutesRange({ cylinderWaterTemperature: 57, tankSizeLiters: 217 }).toString()).toBe('40,50');
  });
});

describe('getShowerMinutesRangeAndWaterReheatTimeMinutes', () => {
  it('should handle an indoor unit with no hot water tank', () => {
    const outdoorUnit: HeatPumpOutdoorUnit = {
      productId: {
        value: 'a5aa80c0-e819-419c-be09-bd277b1428b8',
      },
      technicalSpecificationId: {
        value: 'ce47465b-230d-48fb-94b7-a988530571c7',
      },
      sku: '100076',
      displayName: 'Aira 8kW',
      price: 679000,
      quantity: 1,
      pricingMode: 1,
      effect: 8,
    };
    const indoorUnit: HeatPumpIndoorUnit = {
      productId: {
        value: '4f52f4cd-7e27-4c62-8ae6-64d4faf42273',
      },
      sku: 'compact-no-dhw',
      displayName: 'Compact - No water tank',
      price: 180000,
      quantity: 1,
      pricingMode: 1,
      capacity: {
        value: 0, // 0 capacity = no hot water tank
        unit: 'L',
      },
    };

    expect(getShowerMinutesRangeAndWaterReheatTimeMinutes(indoorUnit, outdoorUnit)).toEqual({
      waterReheatTimeMinutes: 0,
      showerTimeMinimumMinutes: 0,
      showerTimeMaximumMinutes: 0,
    });
  });
});

describe('getStandardRoomVentilationHeatLoss', () => {
  /**
   * These tests are based on the provided spreadsheet in
   * https://docs.openenergymonitor.org/heatpumps/air_change_rate_calculations.html
   * - "Example spreadsheet calculation using the EN12831-1:2017 standard method"
   * May 2025 version
   *
   * We only validate rooms WITHOUT design ATD volume flows.
   * because our current simplified implementation does not explicitly model qv,ATD,design,i.
   * Kitchen & Bathroom in the sheet have ATD design flows (10 m3/hr)
   * leading to higher heat losses than our simplified model would produce, so we exclude them here.
   *
   * Notes: the expected watts are slightly altered from the sheet as the sheet uses 0,33
   * as the air property factor, while we use 0,34 as per EN12831-1.
   */
  const OUTDOOR_TEMP = -2;
  const ENVELOPE_AIR_PERMEABILITY_AT_50_PA = 15.2; // D88 in sheet
  const VOLUME_FLOW_FACTOR = 0.05; // D91 in sheet
  const dwellingExternalEnvelopeAreaInSquareMeters = 133.04;

  type Case = {
    name: string;
    roomVolume: number; // m3
    acph: number;
    roomArea: number; // external envelope area m2
    roomTemp: number; // °C
    expectedWatts: number; // From sheet Zone (16)
  };

  const cases: Case[] = [
    {
      name: 'Livingroom',
      roomVolume: 56.93,
      acph: 0.5,
      roomArea: 36.72,
      roomTemp: 21,
      expectedWatts: 218.2,
    },
    {
      name: 'Hall',
      roomVolume: 15.84,
      acph: 0.5,
      roomArea: 9.6,
      roomTemp: 18,
      expectedWatts: 49.6,
    },
    {
      name: 'Bed 1',
      roomVolume: 23.52,
      acph: 0.5,
      roomArea: 13.88,
      roomTemp: 18,
      expectedWatts: 71.7,
    },
    {
      name: 'Bed 2',
      roomVolume: 20.74,
      acph: 0.5,
      roomArea: 14.88,
      roomTemp: 18,
      expectedWatts: 76.9,
    },
    {
      name: 'Bed 3',
      roomVolume: 9.5,
      acph: 0.5,
      roomArea: 8.28,
      roomTemp: 18,
      expectedWatts: 42.8,
    },
    {
      name: 'Landing',
      roomVolume: 19.01,
      acph: 0,
      roomArea: 7.92,
      roomTemp: 18,
      expectedWatts: 40.9,
    },
  ];

  cases.forEach(({ name, roomVolume, acph, roomArea, roomTemp, expectedWatts }) => {
    it(`matches sheet value for ${name}`, () => {
      const insideOutsideTempDiff = roomTemp - OUTDOOR_TEMP;

      const result = getStandardRoomVentilationHeatLoss({
        roomVolumeInCubicMeters: roomVolume,
        avgAirChangesPerHour: acph,
        insideOutsideTempDiff,
        roomExternalEnvelopeAreaInSquareMeters: roomArea,
        dwellingExternalEnvelopeAreaInSquareMeters,
        envelopeAirPermeabilityAt50Pa: ENVELOPE_AIR_PERMEABILITY_AT_50_PA,
        volumeFlowFactor: VOLUME_FLOW_FACTOR,
      });

      expect(result).toBeCloseTo(expectedWatts, 0);
    });
  });
});

describe('getEnvelopeAirPermeabilityAt50Pa', () => {
  const dwellingExternalEnvelopeAreaInSquareMeters = 149.61;
  const dwellingInternalFloorAreaInSquareMeters = 35;

  function baseDesign() {
    return {
      calculationMethod: VentilationCalculationMethod.STANDARD,
      acphDefault: getAcphDefaultForBuildYearAndCountry(CountryCode.GB, 1969),
      airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_CIBSE,
      airPermeabilityOverride: undefined,
      dwellingExposure: DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
    } as const;
  }

  it('returns 12 for EN12831 option', () => {
    const design = { ...baseDesign(), airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831 };
    const result = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign: design,
      numberOfBedrooms: 3,
      numberOfResidents: 3,
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters,
    });
    expect(result).toBe(12);
  });

  it('returns override for Pulse test and 0 when undefined', () => {
    const overrideDesign = {
      ...baseDesign(),
      airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST,
      airPermeabilityOverride: 7.5,
    };
    expect(
      getEnvelopeAirPermeabilityAt50Pa({
        ventilationDesign: overrideDesign,
        numberOfBedrooms: 3,
        numberOfResidents: 3,
        dwellingExternalEnvelopeAreaInSquareMeters,
        dwellingInternalFloorAreaInSquareMeters,
      }),
    ).toBe(7.5);

    const noOverride = { ...overrideDesign, airPermeabilityOverride: undefined };
    expect(
      getEnvelopeAirPermeabilityAt50Pa({
        ventilationDesign: noOverride,
        numberOfBedrooms: 3,
        numberOfResidents: 3,
        dwellingExternalEnvelopeAreaInSquareMeters,
        dwellingInternalFloorAreaInSquareMeters,
      }),
    ).toBe(0);
  });

  describe('CIBSE option bedroom base rates (no extra occupants)', () => {
    const cases: Array<{ bedrooms: number; rate: number; expected: number }> = [
      { bedrooms: 1, rate: 13, expected: 6.25626629 },
      { bedrooms: 2, rate: 17, expected: 8.18127131 },
      { bedrooms: 3, rate: 21, expected: 10.10627632 },
      { bedrooms: 4, rate: 25, expected: 12.03128133 },
      { bedrooms: 5, rate: 29, expected: 13.95628634 },
    ];
    cases.forEach(({ bedrooms, rate, expected }) => {
      it(`${bedrooms} bedroom(s) -> rate ${rate} l/s => AP50 ${expected}`, () => {
        const design = baseDesign();
        const result = getEnvelopeAirPermeabilityAt50Pa({
          ventilationDesign: design,
          numberOfBedrooms: bedrooms,
          numberOfResidents: bedrooms + 1,
          dwellingExternalEnvelopeAreaInSquareMeters,
          dwellingInternalFloorAreaInSquareMeters,
        });
        expect(result).toBeCloseTo(expected, 6);
      });
    });
  });

  it('CIBSE extrapolates +4 l/s per bedroom after 5 bedrooms (6 bedrooms case)', () => {
    // For 6 bedrooms: base rate = 29 + 4 = 33 l/s
    const expected = 15.88129136;
    const design = baseDesign();
    const result = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign: design,
      numberOfBedrooms: 6,
      numberOfResidents: 7, // default occupancy (6+1)
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters,
    });
    expect(result).toBeCloseTo(expected, 6);
  });

  it('CIBSE adds 4 l/s per extra occupant beyond default', () => {
    // 3 bedrooms: baseRate=21 l/s, default occupants=4 (3+1)
    // Provide 6 residents -> extra occupants = 2 -> adjusted rate = 21 + 8 = 29 l/s
    const expected = 13.95628634;
    const design = baseDesign();
    const result = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign: design,
      numberOfBedrooms: 3,
      numberOfResidents: 6,
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters,
    });
    expect(result).toBeCloseTo(expected, 6);
  });

  it('throws for unspecified option', () => {
    const design = {
      ...baseDesign(),
      airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_UNSPECIFIED,
    } as any;
    expect(() =>
      getEnvelopeAirPermeabilityAt50Pa({
        ventilationDesign: design,
        numberOfBedrooms: 3,
        numberOfResidents: 3,
        dwellingExternalEnvelopeAreaInSquareMeters,
        dwellingInternalFloorAreaInSquareMeters,
      }),
    ).toThrow('Unsupported air permeability option');
  });

  it('enforces minimum 0.3 l/s per m² internal floor area (area-based minimum larger than base)', () => {
    const design = baseDesign();
    const internalArea = 100;
    const result = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign: design,
      numberOfBedrooms: 1,
      numberOfResidents: 2,
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters: internalArea,
    });
    expect(result).toBeCloseTo(14.43753759, 6);
  });
});
