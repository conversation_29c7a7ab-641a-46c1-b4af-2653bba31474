import { CountryCode } from 'utils/marketConfigurations';
import { WaterTemps } from '../stores/HeatSourceStore';
import { RadiatorMode, SystemType } from '../stores/types';
import { RADIATOR_DELTA_T_OPTIONS } from '../radiators/radiatorConstants';

export enum CustomerDesignReportCard {
  HEAT_LOSS,
  WATER_REHEAT_TIME,
  FLOW_TEMPERATURE,
  UNDERFLOOR_HEATING_FLOW_TEMPERATURE,
  OUTDOOR_UNIT,
  INDOOR_UNIT,
  BUFFER_TANK,
}

type MarketConfiguration = {
  maximumFlowTemperature: number;
  defaultWaterTemperatures: WaterTemps;
  defaultUnderfloorHeatingType?: SystemType;
  externalWallAreaAdjustmentFactor?: number;
  customerDesignReportCards?: CustomerDesignReportCard[];
  radiatorDeltaT: (typeof RADIATOR_DELTA_T_OPTIONS)[number]['value'];
  radiatorPanelType: RadiatorMode;
};

export const marketConfiguration: { [key in CountryCode]: MarketConfiguration } = {
  [CountryCode.GB]: {
    maximumFlowTemperature: 50,
    defaultWaterTemperatures: {
      flowTemp: 45,
      flowReturnDeltaT: 5,
    },
    defaultUnderfloorHeatingType: SystemType.WATER,
    externalWallAreaAdjustmentFactor: undefined,
    customerDesignReportCards: [
      CustomerDesignReportCard.OUTDOOR_UNIT,
      CustomerDesignReportCard.INDOOR_UNIT,
      CustomerDesignReportCard.BUFFER_TANK,
      CustomerDesignReportCard.HEAT_LOSS,
      CustomerDesignReportCard.FLOW_TEMPERATURE,
      CustomerDesignReportCard.WATER_REHEAT_TIME,
    ],
    radiatorDeltaT: 50,
    radiatorPanelType: RadiatorMode.Panel,
  },

  [CountryCode.IT]: {
    maximumFlowTemperature: 60,
    defaultWaterTemperatures: {
      flowTemp: 45,
      flowReturnDeltaT: 5,
    },
    defaultUnderfloorHeatingType: SystemType.WATER,
    externalWallAreaAdjustmentFactor: undefined,
    customerDesignReportCards: [
      CustomerDesignReportCard.OUTDOOR_UNIT,
      CustomerDesignReportCard.INDOOR_UNIT,
      CustomerDesignReportCard.BUFFER_TANK,
      CustomerDesignReportCard.FLOW_TEMPERATURE,
    ],
    radiatorDeltaT: 30,
    radiatorPanelType: RadiatorMode.Column,
  },

  [CountryCode.DE]: {
    maximumFlowTemperature: 55,
    defaultWaterTemperatures: {
      flowTemp: 45,
      flowReturnDeltaT: 10,
    },
    defaultUnderfloorHeatingType: undefined,
    externalWallAreaAdjustmentFactor: 1.25,
    customerDesignReportCards: [
      CustomerDesignReportCard.OUTDOOR_UNIT,
      CustomerDesignReportCard.INDOOR_UNIT,
      CustomerDesignReportCard.BUFFER_TANK,
      CustomerDesignReportCard.HEAT_LOSS,
      CustomerDesignReportCard.FLOW_TEMPERATURE,
      CustomerDesignReportCard.UNDERFLOOR_HEATING_FLOW_TEMPERATURE,
    ],
    radiatorDeltaT: 50,
    radiatorPanelType: RadiatorMode.Panel,
  },
};
