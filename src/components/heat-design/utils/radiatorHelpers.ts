import { RadiatorDetails as ProtoRadiatorDetails } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { EmitterType } from 'components/heat-pump-config/stores/types';
import { IntlShape } from 'react-intl';
import { CountryCode } from 'utils/marketConfigurations';
import { UNDERFLOOR_HEATING_DELTA_T, UNDERFLOOR_HEATING_FLOW_TEMP } from '../constants';
import {
  EmitterOverviewDetails,
  RadiatorEmitterOverviewDetails,
  UnderfloorHeatingEmitterOverviewDetails,
} from '../Emitters/types';
import { ClimateDataStore } from '../stores/ClimateDataStore';
import { RoomHeatDesignResult } from '../stores/OutputsStore';
import { RoomsByFloor } from '../stores/RoomsStore';
import {
  CustomUnderfloorHeatingOutput,
  EmitterDetails,
  FloorProps,
  OutputType,
  RadiatorData,
  Room,
  SystemType,
} from '../stores/types';
import { getDesignRoomTemp } from './heatCalculations';
import { toOneDecimalPlace } from './helpers';
import { RADIATOR_DELTA_T_OPTIONS } from '../radiators/radiatorConstants';
import { marketConfiguration } from './marketConfigurations';

// The heat required to raise the temperature of 1 kg of water by 1 K is 4184 joules, so the specific heat capacity of water is 4184 J/(kg*K).
const WATER_SPECIFIC_HEAT_CAPACITY = 4187;

/**
 * Calculates the f1 MW-AT differential factor, as defined in CIBSE's domestic heating design
 * guide, section 5.5.4.1, equation 10. This is a way to determine the manufacture-rated output
 * of the radiator at the current designed room and radiator temperatures.
 *
 * MW-AT: mean water to air temperature: the difference between the mean water temperature (MWT)
 * and the room air temperature.
 *
 * Note: Confusingly, both the difference between the flow and return temperatures, and MW-AT,
 * are referred to as "Delta T" in different places..
 *
 * @param meanWaterTemp the average of the flow and return temperatures (MWT)
 * @param designRoomTemp the design temperature of the room
 * @param manufacturersDeltaT the MW-AT of the radiator at a reference MWT and room temperature,
 * typically 50⁰ i.e. a flow temperature of 75⁰, a return temperature of 65⁰ and a room temperature
 * of 20⁰. This gives a mean water temperature of 70⁰ and a MW-AT of 70-20 = 50⁰.
 */
export const getCorrectionFactor = ({
  meanWaterTemp,
  designRoomTemp,
  manufacturersDeltaT,
}: {
  meanWaterTemp: number;
  designRoomTemp: number;
  manufacturersDeltaT: number;
}) => {
  const meanWaterToAirTemp = meanWaterTemp - designRoomTemp;

  // If the mean water temp is lower than the design room temp, it doesn't make
  // sense to return a negative number, so we return 0 for now.
  if (meanWaterToAirTemp < 0) return 0;

  // As specified in Design section (5.5.4.1 f1 – MW-AD Differential Factor) Equation 10
  // We use MeanWater-AirTemp differential of 50°C as required by BS EN 442 as default for what the watts is specified at
  // In the future we may need to, if the manufacturer specifies wattage at a different deltaT, use that instead
  if (manufacturersDeltaT === 0) {
    return 0;
  }
  return (meanWaterToAirTemp / manufacturersDeltaT) ** 1.3;
};

export const getOutputAtDeltaT = ({
  meanWaterTemp,
  designRoomTemp,
  outputAtManufacturersDeltaT,
  manufacturersDeltaT,
}: {
  meanWaterTemp: number;
  designRoomTemp: number;
  outputAtManufacturersDeltaT: number;
  manufacturersDeltaT: number;
}) => {
  const outputAtDeltaT =
    outputAtManufacturersDeltaT *
    getCorrectionFactor({
      meanWaterTemp,
      designRoomTemp,
      manufacturersDeltaT,
    });
  return Math.round(outputAtDeltaT);
};

export function getUnderfloorHeatingOutput(underfloorHeating: CustomUnderfloorHeatingOutput): number {
  let outputWatt;

  switch (underfloorHeating.systemType) {
    case SystemType.ELECTRIC:
      outputWatt = underfloorHeating.outputWatt;
      break;
    case SystemType.WATER:
      outputWatt = underfloorHeating.nominalOutput?.outputWatt;
      break;
    default:
      // Try to get the outputWatt from any of the two fields
      if ('outputWatt' in underfloorHeating) {
        outputWatt = underfloorHeating.outputWatt;
      } else if ('nominalOutput' in underfloorHeating) {
        outputWatt = underfloorHeating.nominalOutput?.outputWatt;
      }
  }

  return outputWatt ?? 0;
}

export function calculateRadiatorDeltaT(
  flowReturnDeltaT: number,
  radiatorDetails?: EmitterDetails,
  room?: Room,
): number {
  if (radiatorDetails?.systemType === SystemType.ELECTRIC) {
    return 0;
  }

  if (room?.enableRadiatorDeltaTAdjustment && radiatorDetails?.deltaTAdjustmentCelsius) {
    return flowReturnDeltaT + radiatorDetails.deltaTAdjustmentCelsius;
  }

  return flowReturnDeltaT;
}

export function calculateReturnAndMeanWaterTemperature({
  flowTemperature,
  flowReturnDeltaT,
  emitterDetails,
  room,
}: {
  flowTemperature: number;
  flowReturnDeltaT: number;
  emitterDetails?: EmitterDetails;
  room?: Room;
}) {
  if (emitterDetails?.systemType === SystemType.ELECTRIC) {
    return { returnTemperature: 0, meanWaterTemp: 0 };
  }
  const calculatedDeltaTValue = calculateRadiatorDeltaT(flowReturnDeltaT, emitterDetails, room);

  const returnTemperature = flowTemperature - calculatedDeltaTValue;
  const meanWaterTemp = (flowTemperature + returnTemperature) / 2;
  return { returnTemperature, meanWaterTemp };
}

export function getCalculatedRadiatorOutput({
  room,
  radiatorOutput,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  room: Room;
  radiatorOutput: RadiatorData;
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
}): number {
  if (radiatorOutput.radiatorDetails.systemType === SystemType.ELECTRIC) {
    return radiatorOutput.radiatorDetails.outputWatt ?? 0;
  }
  const { meanWaterTemp } = calculateReturnAndMeanWaterTemperature({
    emitterDetails: radiatorOutput.radiatorDetails,
    flowTemperature,
    flowReturnDeltaT,
    room,
  });
  if (room !== undefined && radiatorOutput.radiatorDetails.nominalOutput !== undefined) {
    return getOutputAtDeltaT({
      manufacturersDeltaT: radiatorOutput.radiatorDetails.nominalOutput.deltaT,
      outputAtManufacturersDeltaT: radiatorOutput.radiatorDetails.nominalOutput.outputWatt,
      meanWaterTemp,
      designRoomTemp: getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore),
    });
  }
  return 0;
}

export type RoomHeatOutput = {
  radiators: number;
  underfloorHeating: number;
};

/**
 * Calculates the total heat output of the radiators and underfloor heating in a room.
 */
export function calculateNewHeatOutput({
  room,
  constructionYear,
  countryCode,
  roomHeatLoss,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  room: Room;
  constructionYear: number;
  countryCode: CountryCode;
  roomHeatLoss: number;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
}): RoomHeatOutput {
  const totalRadiatorOutput = room.radiators.reduce((acc, radiatorOutput) => {
    if (radiatorOutput && radiatorOutput.enabled) {
      return (
        acc +
        getCalculatedRadiatorOutput({
          room,
          radiatorOutput,
          constructionYear,
          countryCode,
          flowTemperature,
          flowReturnDeltaT,
          climateDataStore,
        })
      );
    }
    return acc;
  }, 0);

  let underfloorHeatingOutput = 0;
  if (room.underfloorHeating) {
    if (room.underfloorHeating.outputType === OutputType.AUTOMATIC) {
      // The UFH makes up for the lack of heat output, if any
      underfloorHeatingOutput = Math.max(0, roomHeatLoss - totalRadiatorOutput);
    } else {
      underfloorHeatingOutput = getUnderfloorHeatingOutput(room.underfloorHeating);
    }
  }
  return {
    radiators: totalRadiatorOutput,
    underfloorHeating: underfloorHeatingOutput,
  };
}

/**
 * Returns the difference between the total heat output of a given room and the room's heat loss
 */
export function calculateNetHeatOutputForRoom({
  room,
  roomHeatDesignResults,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  room: Room;
  roomHeatDesignResults: RoomHeatDesignResult[];
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
}): number {
  const roomHeatDesignResult = roomHeatDesignResults.find((r) => r.roomId === room.id);

  const roomHeatLoss = roomHeatDesignResult?.totalRoom.heatLoss ?? 0;
  const newHeatOutput = calculateNewHeatOutput({
    room,
    constructionYear,
    countryCode,
    roomHeatLoss,
    flowTemperature,
    flowReturnDeltaT,
    climateDataStore,
  });

  return newHeatOutput.radiators + newHeatOutput.underfloorHeating - roomHeatLoss;
}

/**
 * Returns the difference between the total heat output for each floor and the
 * floor's heat loss.
 */
export function calculateNetHeatOutputByFloor({
  floors,
  roomsByFloor,
  roomHeatDesignResults,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  floors: FloorProps[];
  roomsByFloor: { [key: Room['floorId']]: Room[] };
  roomHeatDesignResults: RoomHeatDesignResult[];
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
}): { [key: string]: number } {
  const result: { [key: string]: number } = {};

  floors.forEach((floor) => {
    result[floor.floorName] =
      roomsByFloor[floor.uid]?.reduce(
        (acc, room) =>
          acc +
          calculateNetHeatOutputForRoom({
            constructionYear,
            room,
            roomHeatDesignResults,
            countryCode,
            flowTemperature,
            flowReturnDeltaT,
            climateDataStore,
          }),
        0,
      ) ?? 0;
  });

  return result;
}

export const hasRadiatorBeenRemovedFromErp = (radiator: RadiatorData) =>
  radiator.specificationReferenceId !== undefined && radiator.erpId === undefined;

export function mapEmitterDetails(emitterDetails?: ProtoRadiatorDetails): EmitterDetails {
  if (emitterDetails?.details?.$case === 'electricRadiatorDetails') {
    return {
      outputWatt: emitterDetails.details.electricRadiatorDetails.outputWatt ?? 0,
      systemType: SystemType.ELECTRIC,
    };
  }
  return {
    deltaTAdjustmentCelsius: emitterDetails?.details?.waterRadiatorDetails.deltaTAdjustmentCelsius ?? 0,
    nominalOutput: {
      outputWatt: emitterDetails?.details?.waterRadiatorDetails.nominalOutput?.outputWatt ?? 0,
      deltaT: emitterDetails?.details?.waterRadiatorDetails.nominalOutput?.deltaT ?? 0,
    },
    systemType: SystemType.WATER,
  };
}

export function getDeltaTDescription(radiator: RadiatorData): string | undefined {
  if (
    radiator.radiatorDetails.systemType !== SystemType.WATER ||
    radiator.radiatorDetails.nominalOutput === undefined
  ) {
    return undefined;
  }
  const { deltaT } = radiator.radiatorDetails.nominalOutput;
  return RADIATOR_DELTA_T_OPTIONS.find((option) => option.value === deltaT)?.label;
}

export function calculateWaterDensity(flowTemp: number): number {
  return 999.96 + 0.026 * flowTemp - 0.0062 * flowTemp ** 2 + 0.000018 * flowTemp ** 3 + 0.045;
}

export function getRadiatorFlowRate(
  radiator: RadiatorData,
  room: Room,
  flowTemp: number,
  flowReturnDeltaT: number,
  radiatorOutput: number,
): number {
  if (radiator.radiatorDetails.systemType === SystemType.ELECTRIC) {
    return 0;
  }
  const radiatorDeltaT = calculateRadiatorDeltaT(flowReturnDeltaT, radiator.radiatorDetails, room);
  const waterDensity = calculateWaterDensity(flowTemp);

  return (radiatorOutput / (waterDensity * radiatorDeltaT * WATER_SPECIFIC_HEAT_CAPACITY)) * 1000 * 3600;
}

export function getUnderfloorHeatingFlowRate(calculatedUnderfloorHeatingOutputWatt: number): number {
  const waterDensity = calculateWaterDensity(UNDERFLOOR_HEATING_FLOW_TEMP);
  return (
    (calculatedUnderfloorHeatingOutputWatt /
      (waterDensity * UNDERFLOOR_HEATING_DELTA_T * WATER_SPECIFIC_HEAT_CAPACITY)) *
    1000 *
    3600
  );
}

export function calculateEmitterDetailsSummary(
  radiators: EmitterOverviewDetails[],
  flowTemperature: number,
  flowReturnDeltaT: number,
): { actualCircuitReturnTemperature: number; totalFlowNeeded: number } {
  const totalFlow = radiators.reduce((acc, rad) => acc + rad.flowRate, 0);
  const actualDeltaT = radiators.length
    ? radiators.reduce((acc, rad) => acc + rad.deltaT * rad.flowRate, 0) / totalFlow
    : flowReturnDeltaT;
  return {
    actualCircuitReturnTemperature: flowTemperature - actualDeltaT,
    totalFlowNeeded: totalFlow,
  };
}

export function calculateRadiatorDetailsForRoom({
  room,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  room: Room;
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
}): RadiatorEmitterOverviewDetails[] {
  return room.radiators
    .filter((radiator) => radiator.enabled)
    .map((radiator) => {
      const output = getCalculatedRadiatorOutput({
        room,
        radiatorOutput: radiator,
        constructionYear,
        countryCode,
        flowTemperature,
        flowReturnDeltaT,
        climateDataStore,
      });
      const { meanWaterTemp } = calculateReturnAndMeanWaterTemperature({
        emitterDetails: radiator.radiatorDetails,
        flowTemperature,
        flowReturnDeltaT,
        room,
      });
      return {
        ...radiator,
        room,
        deltaT: calculateRadiatorDeltaT(flowReturnDeltaT, radiator.radiatorDetails, room),
        flowRate: getRadiatorFlowRate(radiator, room, flowTemperature, flowReturnDeltaT, output),
        output,
        meanWaterTemp,
        type: EmitterType.RADIATOR,
      };
    });
}

export function calculateRadiatorDetailsForFloor({
  floor,
  roomsByFloor,
  constructionYear,
  countryCode,
  flowTemperature,
  flowReturnDeltaT,
  climateDataStore,
}: {
  floor: FloorProps;
  constructionYear: number;
  countryCode: CountryCode;
  flowTemperature: number;
  flowReturnDeltaT: number;
  roomsByFloor: RoomsByFloor;
  climateDataStore: ClimateDataStore;
}): RadiatorEmitterOverviewDetails[] {
  if (!floor?.uid) {
    return [];
  }

  return (
    roomsByFloor[floor.uid]?.flatMap((room) =>
      calculateRadiatorDetailsForRoom({
        room,
        constructionYear,
        countryCode,
        flowTemperature,
        flowReturnDeltaT,
        climateDataStore,
      }),
    ) ?? []
  );
}

export function getUnderfloorHeatingDescription(intl: IntlShape) {
  return intl.formatMessage({ id: 'heatDesign.report.roomDetails.underfloorHeating.title' });
}

export function calculateUnderfloorHeatingDetailsForFloor({
  intl,
  floor,
  roomsByFloor,
  roomsHeatDesignResults,
  flowTemperature,
  flowReturnDeltaT,
}: {
  intl: IntlShape;
  floor: FloorProps;
  roomsByFloor: RoomsByFloor;
  roomsHeatDesignResults: RoomHeatDesignResult[];
  flowTemperature: number;
  flowReturnDeltaT: number;
}): UnderfloorHeatingEmitterOverviewDetails[] {
  if (!floor?.uid) {
    return [];
  }

  return (
    roomsByFloor[floor.uid]
      ?.filter((room) => room.underfloorHeating?.systemType === SystemType.WATER)
      .flatMap((room) => {
        const roomHeatDesignResult = roomsHeatDesignResults.filter((result) => room.id === result.roomId)[0];
        if (room.underfloorHeating && roomHeatDesignResult) {
          const { meanWaterTemp } = calculateReturnAndMeanWaterTemperature({
            flowTemperature,
            flowReturnDeltaT,
            room,
          });
          return {
            ...room.underfloorHeating,
            room,
            deltaT: UNDERFLOOR_HEATING_DELTA_T,
            output: toOneDecimalPlace(roomHeatDesignResult.calculatedUnderfloorHeatingOutputWatt),
            flowRate: getUnderfloorHeatingFlowRate(roomHeatDesignResult.calculatedUnderfloorHeatingOutputWatt),
            type: EmitterType.UNDERFLOOR,
            description: getUnderfloorHeatingDescription(intl),
            meanWaterTemp,
          } satisfies UnderfloorHeatingEmitterOverviewDetails;
        }
        return [];
      }) ?? []
  );
}

// Source: https://teams.microsoft.com/l/message/19:eQZjr5h8wdZ3uqwy5X3dqyPNrsr_D_THudhrd_MAJgQ1@thread.tacv2/1727417689918
export const getMaximumFlowTemperature = ({ countryCode }: { countryCode: CountryCode }) =>
  marketConfiguration[countryCode].maximumFlowTemperature;
