import {
  ACPHDefaults,
  AuxiliaryData,
  CustomUValue,
  Door as <PERSON>Door,
  <PERSON><PERSON><PERSON>ult as ProtoDoorHeatDesignResult,
  Floor as ProtoFloor,
  FloorResult as ProtoFloorHeatDesignResult,
  FloorUValueDefaults,
  HeatDesign,
  HeatDesignClimate,
  HeatDesignResult as ProtoHeatDesignResult,
  ImageMap as ProtoImageMap,
  Point2D,
  <PERSON><PERSON><PERSON> as ProtoRadiator,
  RadiatorDetails as ProtoRadiatorDetails,
  RoofGlazing as ProtoRoofGlazing,
  RoofGlazingResult as ProtoRoofGlazingHeatDesignResult,
  Room as ProtoRoom,
  RoomResult as ProtoRoomHeatDesignResult,
  StandardizedACPHDefaults,
  UnderfloorHeating,
  UnderfloorHeatingResult,
  UValue as ProtoUValue,
  VentilationDesign,
  Wall as ProtoWall,
  WallResult as ProtoWallHeatDesignResult,
  WallType as ProtoWallType,
  Window as ProtoWindow,
  WindowResult as ProtoWindowHeatDesignResult,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { IntlShape } from 'react-intl';
import { HeatDesignHouseInputsStore, HouseInputVentilationDesign } from '../stores/HouseInputsStore';
import {
  mapAboveCeilingTypeToProtobuf,
  mapBelowFloorToProtobuf,
  mapFabricTypeToProtobuf,
  mapOpenFlueTypeToProtobuf,
  mapRoomTypeToProtobuf,
} from './protobufEnumMapping';
import {
  ACPHDefaultValue,
  AdjacentKind,
  CustomUnderfloorHeatingOutput,
  Door,
  EmitterDetails,
  FABRIC_TYPES,
  FloorProps,
  ImageMap,
  OutputType,
  PROJECT_FABRIC_TYPES,
  ProjectFallbackUValues,
  RadiatorData,
  RoofGlazing,
  Room,
  ROOM_FABRIC_TYPES,
  SystemType,
  UnderfloorHeatingOutput,
  UValues,
  VENTILATION_EXPOSURE_VALUES,
  VentilationCalculationMethod,
  Wall,
  Window,
} from '../stores/types';
import { UValue } from '../models/UValue';
import {
  FabricHeatDesignResult,
  FloorHeatDesignResult,
  HeatDesignResult,
  RoomHeatDesignResult,
  WallAttachedFabricHeatDesignResult,
} from '../stores/OutputsStore';
import { ClimateDataStore } from '../stores/ClimateDataStore';
import {
  getAdjustedOutdoorDesignTemperatureCelsius,
  getEnvelopeAirPermeabilityAt50Pa,
  computeDwellingExternalEnvelopeArea,
} from './calculations';
import { countBedrooms, getAdjacentName } from './helpers';
import { getUnderfloorHeatingOutput } from './radiatorHelpers';
import { isHeatDesignValid } from '../Validator';
import { UNDERFLOOR_HEATING_DELTA_T, UNDERFLOOR_HEATING_FLOW_TEMP } from '../constants';
import { CountryCode } from 'utils/marketConfigurations';
import { STANDARDIZED_ACPH_DEFAULTS_MAP } from './averageAirChangePerHour';

function mapUValueToProtobuf(uValue?: UValue): ProtoUValue | undefined {
  if (!uValue) {
    return undefined;
  }
  return {
    id: {
      value: uValue.id,
    },
    name: uValue.name,
    uValueSi: uValue.value,
    displayName: uValue.displayName(),
  };
}

function mapWallSurfaceTypeToProtobuf(surfaceType: 'externalWalls' | 'internalWalls' | 'partyWalls'): ProtoWallType {
  switch (surfaceType) {
    case 'externalWalls':
      return ProtoWallType.WALL_TYPE_EXTERNAL;
    case 'internalWalls':
      return ProtoWallType.WALL_TYPE_INTERNAL;
    case 'partyWalls':
      return ProtoWallType.WALL_TYPE_PARTY;
    default:
      throw new Error(`Unknown wall surface type: ${surfaceType}`);
  }
}

function mapDoorsToProtobuf(currentDoors: Door[], previousDoors: ProtoDoor[]): ProtoDoor[] {
  return currentDoors.map((door) => {
    const previousDoor = previousDoors.find((d) => d.id?.value === door.uid);
    if (!previousDoor) {
      throw new Error(`Could not map door ${door.uid} to previous door`);
    }
    return {
      ...previousDoor,
      id: { value: door.uid },
      widthM: door.width,
      heightM: door.height,
      uValue: mapUValueToProtobuf(door.uValue),
    };
  });
}

function mapWindowsToProtobuf(currentWindows: Window[], previousWindows: ProtoWindow[]): ProtoWindow[] {
  return currentWindows.map((window) => {
    const previousWindow = previousWindows.find((w) => w.id?.value === window.uid);
    if (!previousWindow) {
      throw new Error(`Could not map window ${window.uid} to previous window`);
    }
    return {
      ...previousWindow,
      id: { value: window.uid },
      widthM: window.width,
      heightM: window.height,
      uValue: mapUValueToProtobuf(window.uValue),
    };
  });
}

function mapWallToProtobuf(wall: Wall, previousWalls: ProtoWall[], doors: Door[], windows: Window[]): ProtoWall {
  const previousWall = previousWalls.find((w) => w.id?.value === wall.uid);
  const currentDoors = doors.filter((door) => door.wallUID === wall.uid);
  const currentWindows = windows.filter((window) => window.wallUID === wall.uid);
  const previousDoors = previousWall?.doors || [];
  const previousWindows = previousWall?.windows || [];

  if (!previousWall) {
    throw new Error(`Could not map wall ${wall.uid} to previous wall`);
  }

  const protoWall: ProtoWall = {
    ...previousWall,
    id: { value: wall.uid },
    type: mapWallSurfaceTypeToProtobuf(wall.surfaceType),
    widthM: wall.length!,
    uValue: mapUValueToProtobuf(wall.uValue),
    doors: mapDoorsToProtobuf(currentDoors, previousDoors),
    windows: mapWindowsToProtobuf(currentWindows, previousWindows),
  };

  if (wall.surfaceType === 'internalWalls' && wall.adjoiningRoomUID !== undefined) {
    protoWall.adjoiningRoomId = { value: wall.adjoiningRoomUID };
  }

  if (wall.surfaceType === 'externalWalls') {
    protoWall.soilPercentage = wall.soilPercentage ?? undefined;
  }

  return protoWall;
}

function mapRoofGlazingToProtobuf(glazing: RoofGlazing): ProtoRoofGlazing {
  return {
    id: { value: glazing.uid },
    widthM: glazing.width!,
    heightM: glazing.length!,
    dataSourceReferences: [],
    imageMap: {
      points: [],
    },
    uValue: mapUValueToProtobuf(glazing.uValue),
  };
}

function mapFloorDefaultsToProtobuf(floor: FloorProps): FloorUValueDefaults {
  return {
    defaultEntries: ROOM_FABRIC_TYPES.flatMap((fabricType) => {
      const uValue = floor.floorUValues?.[fabricType];
      if (!uValue) {
        return [];
      }
      return [
        {
          fabricType: mapFabricTypeToProtobuf(fabricType),
          uValue: mapUValueToProtobuf(uValue),
        },
      ];
    }),
  };
}

function mapDwellingDefaultsToProtobuf(uValues: ProjectFallbackUValues): FloorUValueDefaults {
  return {
    defaultEntries: PROJECT_FABRIC_TYPES.flatMap((fabricType) => {
      const uValue = uValues[fabricType];
      if (!uValue) {
        return [];
      }
      return [
        {
          fabricType: mapFabricTypeToProtobuf(fabricType),
          uValue: mapUValueToProtobuf(uValue),
        },
      ];
    }),
  };
}

function mapCustomUValuesToProtobuf(customUValues: UValues): CustomUValue[] {
  return FABRIC_TYPES.flatMap((fabricType) => {
    const uValues = customUValues[fabricType];
    if (!uValues) {
      return [];
    }
    return uValues.map((uValue) => ({
      fabricType: mapFabricTypeToProtobuf(fabricType),
      uValue: mapUValueToProtobuf(uValue),
    }));
  });
}

function mapImageMapToProtobuf(imageMap?: ImageMap): ProtoImageMap {
  return {
    points: (imageMap?.coordinates || []).reduce((result: Point2D[], _current, i, array: number[]) => {
      if (i % 2 === 0) {
        result.push({
          x: Number(array[i]),
          y: Number(array[i + 1]),
        });
      }

      return result;
    }, []),
  };
}

function mapEmitterDetailsToProtobuf(emitterDetails: EmitterDetails): ProtoRadiatorDetails {
  if (emitterDetails.systemType === 'electric') {
    return {
      details: {
        $case: 'electricRadiatorDetails',
        electricRadiatorDetails: {
          outputWatt: emitterDetails.outputWatt,
        },
      },
    };
  }

  return {
    details: {
      $case: 'waterRadiatorDetails',
      waterRadiatorDetails: {
        deltaTAdjustmentCelsius: emitterDetails.deltaTAdjustmentCelsius,
        nominalOutput: {
          outputWatt: emitterDetails.nominalOutput?.outputWatt ?? 0,
          deltaT: emitterDetails.nominalOutput?.deltaT ?? 0,
        },
      },
    },
  };
}

function mapAcphDefaultsToProtobuf(acph: ACPHDefaultValue): ACPHDefaults {
  switch (acph.type) {
    case 'custom':
      return {
        defaults: {
          $case: 'defaultAverageAirChangesPerHour',
          defaultAverageAirChangesPerHour: acph.value,
        },
      };
    case 'standardized': {
      // Since object keys are always strings, we need to cast it to a number as that's the expected type.
      const entry = Object.entries(STANDARDIZED_ACPH_DEFAULTS_MAP).find(([_, value]) => value === acph.standard);
      if (!entry) throw new Error('Unknown standardized ACPH default');
      const protoAcph = Number(entry[0]) as unknown as StandardizedACPHDefaults;
      return {
        defaults: {
          $case: 'standardizedAcphDefaults',
          standardizedAcphDefaults: protoAcph,
        },
      };
    }
    default:
      return acph satisfies never;
  }
}

function mapVentilationDesignToProtobuf(
  ventilationDesign: HouseInputVentilationDesign,
  rooms: Room[],
  floors: FloorProps[],
  numberOfResidents: number,
  countryCode: CountryCode,
): VentilationDesign {
  let mappedDesign: VentilationDesign | undefined = undefined;
  switch (ventilationDesign.calculationMethod) {
    case VentilationCalculationMethod.STANDARD: {
      const numberOfBedrooms = countBedrooms(rooms);
      const dwellingExternalEnvelopeAreaInSquareMeters = computeDwellingExternalEnvelopeArea({
        rooms,
        floors,
        countryCode,
      });

      const dwellingInternalFloorAreaInSquareMeters = rooms.reduce((acc, r) => acc + r.totalArea, 0);

      const airPermeabilityValue = getEnvelopeAirPermeabilityAt50Pa({
        ventilationDesign,
        numberOfBedrooms,
        numberOfResidents,
        dwellingExternalEnvelopeAreaInSquareMeters,
        dwellingInternalFloorAreaInSquareMeters,
      });
      mappedDesign = {
        ventilationMethod: {
          method: {
            $case: 'standardVentilation',
            standardVentilation: {
              acphDefaults: mapAcphDefaultsToProtobuf(ventilationDesign.acphDefault),
              airPermeability: {
                airPermeabilityValue,
                airPermeabilityOption: ventilationDesign.airPermeability,
              },
              dwellingExposure: {
                exposureCoefficient: VENTILATION_EXPOSURE_VALUES[ventilationDesign.dwellingExposure],
                dwellingExposureOption: ventilationDesign.dwellingExposure,
              },
            },
          },
        },
      };
      break;
    }
    case VentilationCalculationMethod.SIMPLE:
      mappedDesign = {
        ventilationMethod: {
          method: {
            $case: 'simpleVentilation',
            simpleVentilation: {
              acphDefaults: mapAcphDefaultsToProtobuf(ventilationDesign.acphDefault),
            },
          },
        },
      };
      break;
    default:
      return ventilationDesign satisfies never;
  }
  if (!mappedDesign) {
    throw new Error('Ventilation design mapping failed');
  }
  return mappedDesign;
}

/**
 * Checks that obligatory information is present in the underfloor heating output
 * and maps it to the protobuf format
 */
function mapCustomUnderfloorHeatingDetailsToProtobuf(
  customUnderfloorHeatingOutput: CustomUnderfloorHeatingOutput,
): ProtoRadiatorDetails | undefined {
  switch (customUnderfloorHeatingOutput.systemType) {
    case SystemType.ELECTRIC:
      return customUnderfloorHeatingOutput.outputWatt
        ? mapEmitterDetailsToProtobuf({
            ...customUnderfloorHeatingOutput,
            systemType: SystemType.ELECTRIC,
            outputWatt: customUnderfloorHeatingOutput.outputWatt,
          })
        : undefined;
    case SystemType.WATER:
      return mapEmitterDetailsToProtobuf({
        ...customUnderfloorHeatingOutput,
        systemType: SystemType.WATER,
        deltaTAdjustmentCelsius: 0, // N/A for underfloor heating currently,
      });
    default:
      return undefined;
  }
}

function mapAutomaticUnderfloorHeatingDetailsToProtobuf(
  automaticUnderfloorHeatingOutput: UnderfloorHeatingOutput,
): ProtoRadiatorDetails | undefined {
  switch (automaticUnderfloorHeatingOutput.systemType) {
    case SystemType.ELECTRIC:
      return mapEmitterDetailsToProtobuf({
        systemType: SystemType.ELECTRIC,
        outputWatt: 0,
      });
    case SystemType.WATER:
      return mapEmitterDetailsToProtobuf({
        systemType: SystemType.WATER,
        deltaTAdjustmentCelsius: 0,
      });
    default:
      return undefined;
  }
}

function mapUnderfloorHeatingToProtobuf(
  underfloorHeatingOutput: UnderfloorHeatingOutput,
): UnderfloorHeating | undefined {
  switch (underfloorHeatingOutput.outputType) {
    case OutputType.AUTOMATIC:
      return {
        outputWatt: 0, // We don't care about this, it's legacy that we need to keep
        matchRoomHeatLoss: true,
        // Save emitter details so we can retrieve the system type when loading the project
        emitterDetails: mapAutomaticUnderfloorHeatingDetailsToProtobuf(underfloorHeatingOutput),
      };
    case OutputType.CUSTOM:
      return {
        outputWatt: getUnderfloorHeatingOutput(underfloorHeatingOutput),
        matchRoomHeatLoss: false,
        emitterDetails: mapCustomUnderfloorHeatingDetailsToProtobuf(underfloorHeatingOutput),
      };
    // no default
  }
}

function mapRadiatorsToProtobuf(previousRadiators: ProtoRadiator[], radiators: RadiatorData[]): ProtoRadiator[] {
  return radiators.map((radiator) => {
    // Radiators added in the heat design calculator won't have a matching entry in radiators
    const previousRadiator = previousRadiators.find((r) => r.id?.value === radiator.uid);

    return {
      id: { value: radiator.uid },
      dataSourceReferences: previousRadiator?.dataSourceReferences ?? [],
      widthM: radiator.width / 1000,
      heightM: radiator.height / 1000,
      imageMap: mapImageMapToProtobuf(radiator?.roomImageMap),
      floorImageMap: mapImageMapToProtobuf(radiator?.floorImageMap),
      toBeInstalled: !radiator.isExisting,
      enabled: radiator.enabled,
      radiatorDetails: mapEmitterDetailsToProtobuf(radiator.radiatorDetails),
      comment: radiator.comment ?? '',
      replacedBy: radiator.replacedBy ? { value: radiator.replacedBy } : undefined,
      specificationReferenceId: radiator.specificationReferenceId
        ? { value: radiator.specificationReferenceId }
        : undefined,
    } satisfies ProtoRadiator;
  });
}

function mapSoilPercentageDefaultToProtobuf(
  soilPercentageDefault: FloorProps['soilPercentageDefault'],
): ProtoFloor['soilPercentageDefault'] {
  return soilPercentageDefault ?? undefined;
}

function mapToHeatDesignClimate(
  climateDataStore: ClimateDataStore,
  heatDesignInputsStore: HeatDesignHouseInputsStore,
): HeatDesignClimate {
  return {
    baseOutdoorDesignTemperatureCelsius: climateDataStore.baseOutdoorDesignTemperature,
    heatingDegreeDays: climateDataStore.degreeDays,
    localAnnualAverageExternalAirTemperatureCelsius: climateDataStore.localAnnualAverageExternalAirTemperature,
    adjustedOutdoorDesignTemperatureCelsius: getAdjustedOutdoorDesignTemperatureCelsius({
      baseOutdoorDesignTemperature: climateDataStore.baseOutdoorDesignTemperature,
      temperatureCompensation: heatDesignInputsStore.temperatureCompensation,
      isDwellingInExposedLocation: heatDesignInputsStore.isDwellingInExposedLocation,
    }),
  };
}

export const serializeProjectToProtobuf = ({
  previousHeatDesign,
  heatDesignInputsStore,
  floors,
  rooms,
  flowTemperatureCelsius,
  flowReturnDeltaT,
  uValues,
  projectUValues,
  auxiliaryData,
  climateDataStore,
  countryCode,
}: {
  previousHeatDesign: HeatDesign;
  heatDesignInputsStore: HeatDesignHouseInputsStore;
  floors: FloorProps[];
  rooms: Room[];
  uValues: UValues;
  projectUValues: ProjectFallbackUValues;
  auxiliaryData: AuxiliaryData;
  flowTemperatureCelsius: number;
  flowReturnDeltaT: number;
  climateDataStore: ClimateDataStore;
  countryCode: CountryCode;
}): HeatDesign => {
  const { dwelling: previousDwelling } = previousHeatDesign;
  if (!previousDwelling) {
    throw new Error('No dwelling found');
  }

  const previousFloors = previousDwelling.floors;
  const protoFloors: ProtoFloor[] = floors.map((floor) => {
    const previousFloor = previousFloors.find((f) => f.id?.value === floor.uid);
    if (!previousFloor) {
      throw new Error(`Could not map floor ${floor.uid} to previous floor`);
    }
    const previousRooms = previousFloor.rooms;

    const protoRooms: ProtoRoom[] = rooms
      .filter((room) => room.floorId === floor.uid)
      .map((room) => {
        const previousRoom = previousRooms.find((r) => r.id?.value === room.id);
        if (!previousRoom) {
          throw new Error(`Could not map room ${room.id} to previous room`);
        }
        const previousWalls = [
          ...previousRoom.externalWalls,
          ...previousRoom.internalWalls,
          ...previousRoom.partyWalls,
        ];

        const { doors, windows } = room.surfaces;

        const externalWalls = room.surfaces.walls
          .filter((wall) => wall.surfaceType === 'externalWalls')
          .map((wall) => mapWallToProtobuf(wall, previousWalls, doors, windows));
        const internalWalls = room.surfaces.walls
          .filter((wall) => wall.surfaceType === 'internalWalls')
          .map((wall) => mapWallToProtobuf(wall, previousWalls, doors, windows));
        const partyWalls = room.surfaces.walls
          .filter((wall) => wall.surfaceType === 'partyWalls')
          .map((wall) => mapWallToProtobuf(wall, previousWalls, doors, windows));
        const roofGlazings = room.surfaces.roofGlazings.map((glazing) => mapRoofGlazingToProtobuf(glazing));

        const roofOrCeiling = room.surfaces.roofsOrCeilings[0];
        const roomFloor = room.surfaces.floors[0];

        return {
          ...previousRoom,
          name: room.name,
          enableRadiatorDeltaTAdjustment: room.enableRadiatorDeltaTAdjustment,
          roomType: mapRoomTypeToProtobuf(room.roomType),
          openFlueType: mapOpenFlueTypeToProtobuf(room.openFlue),
          designRoomTemperatureCelsius: room.designRoomTempOverride,
          averageAirChangePerHour: room.avgAirChangesPerHourOverride,
          heated: room.isHeated,
          averageCeilingHeightM: room.averageHeight,
          externalWalls,
          internalWalls,
          partyWalls,
          roofGlazings,
          ceilingUValue: mapUValueToProtobuf(roofOrCeiling?.uValue),
          floorSurfaceUValue: mapUValueToProtobuf(roomFloor?.uValue),
          aboveCeiling: mapAboveCeilingTypeToProtobuf(roofOrCeiling?.spaceAbove.type),
          ...(roofOrCeiling?.spaceAbove.type === AdjacentKind.Heated && {
            aboveCeilingTemperatureCelsius: roofOrCeiling?.spaceAbove.tempOfSpaceAbove,
          }),
          belowFloor: mapBelowFloorToProtobuf(roomFloor?.belowFloor),
          radiators: mapRadiatorsToProtobuf(previousRoom.radiators, room.radiators),
          underfloorHeatingWatt:
            room.underfloorHeating === undefined ? undefined : mapUnderfloorHeatingToProtobuf(room.underfloorHeating),
        };
      });

    return {
      ...previousFloor,
      rooms: protoRooms,
      floorUValueDefaults: mapFloorDefaultsToProtobuf(floor),
      floorLevel: floor.floorNr, // TODO: Make the name consistent with the protobuf
      soilPercentageDefault: mapSoilPercentageDefaultToProtobuf(floor.soilPercentageDefault),
    };
  });

  const customUValues = FABRIC_TYPES.reduce(
    (acc, fabricType) => ({
      ...acc,
      [fabricType]: uValues[fabricType].filter((uValue) => uValue.metadata.isCustom),
    }),
    {} as UValues,
  );

  const hasAnyUnderfloorHeating =
    rooms.filter((room) => room.underfloorHeating?.systemType === SystemType.WATER).length > 0;

  return {
    ...previousHeatDesign,
    dwellingUValueDefaults: mapDwellingDefaultsToProtobuf(projectUValues),
    customUValues: mapCustomUValuesToProtobuf(customUValues),
    dwelling: {
      ...previousDwelling,
      constructionYear: heatDesignInputsStore.constructionYear,
      numberOfResidents: heatDesignInputsStore.numberOfResidents,
      exposedLocation: heatDesignInputsStore.isDwellingInExposedLocation,
      temperatureAdjustmentCelsius: heatDesignInputsStore.temperatureCompensation,
      floors: protoFloors,
      systemDesign: {
        flowTemperatureCelsius,
        flowReturnDeltaT,
        underfloorHeatingFlowTemperatureCelsius: hasAnyUnderfloorHeating ? UNDERFLOOR_HEATING_FLOW_TEMP : undefined,
        underfloorHeatingFlowReturnDeltaT: hasAnyUnderfloorHeating ? UNDERFLOOR_HEATING_DELTA_T : undefined,
      },
      ventilationDesign: mapVentilationDesignToProtobuf(
        heatDesignInputsStore.ventilationDesign,
        rooms,
        floors,
        heatDesignInputsStore.numberOfResidents,
        countryCode,
      ),
    },
    auxiliaryData,
    climate: mapToHeatDesignClimate(climateDataStore, heatDesignInputsStore),
  } satisfies HeatDesign;
};

function mapWallToResultProtobuf(
  wall: FabricHeatDesignResult,
  doors: WallAttachedFabricHeatDesignResult[],
  windows: WallAttachedFabricHeatDesignResult[],
  formatMessage: IntlShape['formatMessage'],
): ProtoWallHeatDesignResult {
  const attachedDoors = doors.filter((door) => door.wallId === wall.id);
  const attachedWindows = windows.filter((window) => window.wallId === wall.id);
  return {
    wallId: { value: wall.id },
    opposingTemperatureCelsius: wall.adjacentTemperature!,
    opposingSpaceName: getAdjacentName(wall, formatMessage),
    resolvedUValue: mapUValueToProtobuf(wall.uValue),
    fabricHeatLossWatt: wall.heatLoss,
    areaSqm: wall.area,
    doors: attachedDoors.map(
      (door) =>
        ({
          doorId: { value: door.id },
          opposingTemperatureCelsius: door.adjacentTemperature!,
          opposingSpaceName: getAdjacentName(door, formatMessage),
          resolvedUValue: mapUValueToProtobuf(door.uValue),
          fabricHeatLossWatt: door.heatLoss,
          areaSqm: door.area,
        }) satisfies ProtoDoorHeatDesignResult,
    ),
    windows: attachedWindows.map(
      (window) =>
        ({
          windowId: { value: window.id },
          opposingTemperatureCelsius: window.adjacentTemperature!,
          opposingSpaceName: getAdjacentName(window, formatMessage),
          resolvedUValue: mapUValueToProtobuf(window.uValue),
          fabricHeatLossWatt: window.heatLoss,
          areaSqm: window.area,
        }) satisfies ProtoWindowHeatDesignResult,
    ),
  };
}

function mapRoofGlazingToResultProtobuf(
  glazing: FabricHeatDesignResult,
  formatMessage: IntlShape['formatMessage'],
): ProtoRoofGlazingHeatDesignResult {
  return {
    roofGlazingId: { value: glazing.id },
    opposingTemperatureCelsius: glazing.adjacentTemperature!,
    opposingSpaceName: getAdjacentName(glazing, formatMessage),
    resolvedUValue: mapUValueToProtobuf(glazing.uValue),
    fabricHeatLossWatt: glazing.heatLoss,
    areaSqm: glazing.area,
  };
}

function mapUnderfloorHeatingToResultProtobuf(room: RoomHeatDesignResult): UnderfloorHeatingResult | undefined {
  if (room.calculatedUnderfloorHeatingOutputWatt || room.calculatedUnderfloorHeatingFlowRate) {
    return {
      calculatedOutputWatt: room.calculatedUnderfloorHeatingOutputWatt,
      flowRateLitersPerHour: room.calculatedUnderfloorHeatingFlowRate,
    };
  }
}

function mapRoomToProtobuf(
  room: RoomHeatDesignResult,
  formatMessage: IntlShape['formatMessage'],
): ProtoRoomHeatDesignResult {
  const allWalls = [
    ...room.surfaceDetails.externalWalls,
    ...room.surfaceDetails.internalWalls,
    ...room.surfaceDetails.partyWalls,
  ];

  return {
    roomId: { value: room.roomId },
    fabricHeatLossWatt: room.totalFabric.heatLoss,
    ventilationHeatLossWatt: room.ventilation.heatLoss,
    totalHeatLossWatt: room.totalRoom.heatLoss,
    totalHeatLossWattPerMeterSquared: room.wattsPerMeterSquared,
    resolvedDesignRoomTemperatureCelsius: room.resolvedDesignRoomTemperatureCelsius,
    floorSurfaceOpposingTemperatureCelsius: room.surfaceDetails.floors[0]!.adjacentTemperature,
    floorSurfaceOpposingSpaceName: getAdjacentName(room.surfaceDetails.floors[0]!, formatMessage),
    floorSurfaceResolvedUValue: mapUValueToProtobuf(room.surfaceDetails.floors[0]!.uValue),
    floorSurfaceHeatLossWatt: room.surfaceDetails.floors[0]!.heatLoss,
    floorSurfaceAreaSqm: room.surfaceDetails.floors[0]!.area,
    ceilingOpposingTemperatureCelsius: room.surfaceDetails.roofsOrCeilings[0]!.adjacentTemperature!,
    ceilingOpposingSpaceName: getAdjacentName(room.surfaceDetails.roofsOrCeilings[0]!, formatMessage),
    ceilingResolvedUValue: mapUValueToProtobuf(room.surfaceDetails.roofsOrCeilings[0]!.uValue),
    ceilingHeatLossWatt: room.surfaceDetails.roofsOrCeilings[0]!.heatLoss,
    ceilingAreaSqm: room.surfaceDetails.roofsOrCeilings[0]!.area,
    totalOutputOfRadiatorsWatt: room.totalOutputOfRadiatorsWatt,
    totalOutputOfHeatEmittersWatt: room.totalOutputOfHeatEmittersWatt,
    walls: allWalls.map((wall) =>
      mapWallToResultProtobuf(wall, room.surfaceDetails.doors, room.surfaceDetails.windows, formatMessage),
    ),
    roofGlazings: room.surfaceDetails.roofGlazings.map((glazing) =>
      mapRoofGlazingToResultProtobuf(glazing, formatMessage),
    ),
    radiators: room.radiators.map((radiator) => ({
      radiatorId: { value: radiator.uid },
      calculatedOutputWatt: radiator.output,
      meanWaterToAirTemperatureCelsius: radiator.meanWaterTemp - room.resolvedDesignRoomTemperatureCelsius,
      deltaTCelsius: radiator.deltaT,
      flowRateLitersPerHour: radiator.flowRate,
      replacedBy: radiator.replacedBy,
    })),
    underfloorHeating: mapUnderfloorHeatingToResultProtobuf(room),
    averageAirChangePerHour: room.averageAirChangePerHour,
    externalEnvelopeAreaSqm: room.externalEnvelopeArea,
  };
}

function mapFloorToProtobuf(
  floor: FloorHeatDesignResult,
  formatMessage: IntlShape['formatMessage'],
): ProtoFloorHeatDesignResult {
  return {
    floorId: { value: floor.floorId },
    totalHeatLossWatt: floor.totalHeatLossWatt,
    totalOutputOfHeatEmittersWatt: floor.totalOutputOfHeatEmittersWatt,
    totalAreaSqm: floor.totalAreaSqm,
    numberOfBedrooms: floor.numberOfBedrooms,
    rooms: floor.roomsResults.filter((room) => room.isHeated).map((room) => mapRoomToProtobuf(room, formatMessage)), // Only include heated rooms as we don't validate unheated rooms
  };
}

/**
 * This method serializes the HeatDesignResult to a ProtoHeatDesignResult
 * if and only if the HeatDesign is valid. Otherwise, it returns undefined.
 */
export function serializeHeatDesignResult(
  result: HeatDesignResult,
  heatDesign: HeatDesign,
  floors: FloorProps[],
  rooms: Room[],
  projectUValues: ProjectFallbackUValues,
  formatMessage: IntlShape['formatMessage'],
): ProtoHeatDesignResult | undefined {
  if (!isHeatDesignValid(heatDesign, floors, rooms, projectUValues)) {
    return undefined;
  }
  // Given that the heat design is valid, we can
  // make the assumptions that necessary data is present.

  return {
    // TODO [model-sync] could we sync these variable names?
    fabricAnnualEnergyDemandKwh: result.totalFabricEnergyDemand,
    fabricHeatLossWatt: result.totalFabricHeatLoss,
    hotWaterAnnualEnergyDemandKwh: result.yearlyHotWaterEnergyDemand,
    totalEnergyDemandKwh: result.totalEnergyDemand,
    totalHeatLossWatt: result.totalHeatLoss,
    ventilationAnnualEnergyDemandKwh: result.totalVentilationEnergyDemand,
    ventilationHeatLossWatt: result.totalVentilationHeatLoss,
    showerTimeMinimumMinutes: result.showerTimeMinimumMinutes,
    showerTimeMaximumMinutes: result.showerTimeMaximumMinutes,
    waterReheatTimeMinutes: result.waterReheatTimeMinutes,
    hotWaterDailyEnergyDemandKwh: result.dailyHotWaterEnergyDemand,
    totalAreaSqm: result.totalAreaSqm,
    totalOutputOfHeatEmittersWatt: result.totalOutputOfHeatEmittersWatt,
    thermalBridgingUvalueAdjustment: result.thermalBridgingUvalueAdjustment,
    externalWallAdjustmentFactor: result.externalWallAdjustmentFactor,
    maxHeatOutputWithoutElectricHeaterWatt: result.maxHeatOutputWithoutElectricHeaterWatt,
    bivalencePointCelsius: result.bivalencePointCelsius,
    scop: result.scop,
    indoorUnitProductId: { value: result.indoorUnitProductId! },
    outdoorUnits: result.outdoorUnitsIds.map((outdoorUnit) => ({
      outdoorUnitProductId: { value: outdoorUnit.productId! },
      technicalSpecificationId: { value: outdoorUnit.technicalSpecificationId },
    })),
    numberOfFloors: result.numberOfFloors,
    numberOfBedrooms: result.numberOfBedrooms,
    floors: result.floorsResults.map((floor) => mapFloorToProtobuf(floor, formatMessage)),
  };
}
