import { enGB, de, it } from 'date-fns/locale';
import { Locale, format } from 'date-fns';

const localeMap: { [key: string]: Locale } = {
  'en-GB': enGB,
  'de-DE': de,
  'it-IT': it,
  de,
  it,
};

export const getLocaleFromBrowser = ({ locale }: { locale: string }): Locale => localeMap[locale] || enGB;

// eg. "11-06-2024, 12:34"
export const formatDateTimeToProject = (date: Date, locale: string): string =>
  format(date, 'dd-MM-yyyy, HH:mm', { locale: getLocaleFromBrowser({ locale }) });
