import { useCallback } from 'react';
import { useFloorsStore, useSelectFloorForRoom } from '../stores/FloorsStore';
import { useUValuesStore } from '../stores/UValuesStore';
import { Room, RoomFabric } from '../stores/types';
import { lookupUValueForSurfaceType } from './helpers';

export function useValidateUValuesForRoom(room: Room) {
  const dwellingDefaultUValues = useUValuesStore((s) => s.projectUValues);
  const allFloors = useFloorsStore((state) => state.floors);
  const floor = useSelectFloorForRoom(room);

  const validateSurfaceUValues = useCallback(
    (surfaces: RoomFabric[]) =>
      surfaces.every((surface) => {
        if (!floor) return false;
        return (
          surface.uValue !== undefined ||
          lookupUValueForSurfaceType(surface.surfaceType, dwellingDefaultUValues, floor, allFloors) !== undefined
        );
      }),
    [dwellingDefaultUValues, floor, allFloors],
  );

  return { validateSurfaceUValues };
}
