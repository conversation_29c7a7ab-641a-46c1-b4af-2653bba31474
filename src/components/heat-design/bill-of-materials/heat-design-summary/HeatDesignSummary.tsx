import { memo, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Stack, Typography } from '@mui/material';
import styled from '@emotion/styled';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { DeviceOutlinedIcon } from '@ui/components/StandardIcons/DeviceOutlinedIcon';
import { Wave3VerticalCircleOutlinedIcon } from '@ui/components/StandardIcons/Wave3VerticalCircleOutlinedIcon';
import { WaterdropOutlinedIcon } from '@ui/components/StandardIcons/WaterdropOutlinedIcon';
import { CalendarViewWeekIcon } from '@ui/components/StandardIcons/CalendarViewWeekIcon';
import { RadiatorList } from './RadiatorList';
import { useHeatPumpStore } from '../../../quotation/stores/HeatPumpPackageStore';
import { FormattedMessage } from 'react-intl';
import InfoBlock from '../../radiators/RadiatorRenderModalInfoBlock';
import { useProtobufHeatDesignStore } from 'components/heat-design/stores/ProtobufHeatDesignStore';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import { WarningBox } from 'components/heat-design/components/WarningBox';

const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  borderRadius: '16px !important',
  padding: '16px',
  '.MuiAccordionDetails-root': {
    padding: 0,
  },
  '.MuiAccordionSummary-root': {
    padding: 0,
  },
});

function HeatDesignSummaryComponent() {
  const [isExpanded, setIsExpanded] = useState(false);
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const isHeatDesignLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const heatDesignUpdatedAt = useProtobufHeatDesignStore((s) => s.updatedAt);
  const billOfMaterialsUpdatedAt = useBillOfMaterialsDesignStore((s) => s.updatedAt);
  const isHeatDesignNewer =
    heatDesignUpdatedAt &&
    billOfMaterialsUpdatedAt &&
    heatDesignUpdatedAt.getTime() > billOfMaterialsUpdatedAt.getTime();

  return (
    <Stack gap={3}>
      <StyledAccordion expanded={isExpanded}>
        <AccordionSummary onClick={() => setIsExpanded(!isExpanded)}>
          <Stack gap={2}>
            <Stack direction="row" alignItems="center" gap={2}>
              <Chevron
                style={{ flex: '0 0 auto', height: '100%', width: '20px' }}
                transitionDuration="0.2s"
                direction={isExpanded ? 'up' : 'down'}
                height={24}
                width={20}
              />
              <Typography variant="headline3">
                <FormattedMessage id="heatDesign.billOfMaterials.heatDesignSummary.title" />
              </Typography>
              {!isHeatDesignLocked && <WarningBox contentId="heatDesign.billOfMaterials.notLockedWarning" />}
              {isHeatDesignNewer && (
                <WarningBox contentId="heatDesign.billOfMaterials.heatDesignSummary.updatedWarning" />
              )}
            </Stack>
            <Typography variant="body2">
              <FormattedMessage id="heatDesign.billOfMaterials.heatDesignSummary.summary" />
            </Typography>
            <Stack direction="row" gap={2} alignItems="center" sx={{ height: '30px  ' }}>
              <DeviceOutlinedIcon />
              <Typography variant="headline4" fontWeight={500}>
                <FormattedMessage id="heatDesign.billOfMaterials.heatDesignSummary.appliancesTitle" />
              </Typography>
            </Stack>
            <Stack direction="row" gap={2} alignItems="center">
              {selectedHeatPumpPackages.heatPumpOutdoorUnit?.[0] && (
                <InfoBlock
                  sx={{ backgroundColor: '#22222608', borderRadius: '16px', padding: '16px', flex: 1 }}
                  verticalLayout
                  id="outdoor"
                  icon={<DeviceOutlinedIcon />}
                  value="Outdoor unit"
                  label={selectedHeatPumpPackages.heatPumpOutdoorUnit[0].displayName}
                />
              )}
              {selectedHeatPumpPackages.heatPumpIndoorUnit?.[0] && (
                <InfoBlock
                  sx={{ backgroundColor: '#22222608', borderRadius: '16px', padding: '16px', flex: 1 }}
                  verticalLayout
                  id="indoor"
                  icon={<WaterdropOutlinedIcon />}
                  value="Indoor unit"
                  label={selectedHeatPumpPackages.heatPumpIndoorUnit?.[0].displayName}
                />
              )}
              {selectedHeatPumpPackages.bufferTank?.[0] && (
                <InfoBlock
                  sx={{ backgroundColor: '#22222608', borderRadius: '16px', padding: '16px', flex: 1 }}
                  verticalLayout
                  id="buffer"
                  icon={<Wave3VerticalCircleOutlinedIcon />}
                  value="Buffer tank"
                  label={selectedHeatPumpPackages.bufferTank?.[0].displayName}
                />
              )}
            </Stack>
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <Stack gap={2} sx={{ marginTop: '10px' }}>
            <Stack direction="row" gap={2} alignItems="center" sx={{ height: '30px' }}>
              <CalendarViewWeekIcon />
              <Typography variant="headline4" fontWeight={500}>
                <FormattedMessage id="common.label.radiators" />
              </Typography>
            </Stack>
            <RadiatorList />
          </Stack>
        </AccordionDetails>
      </StyledAccordion>
    </Stack>
  );
}

export const HeatDesignSummary = memo(HeatDesignSummaryComponent);
