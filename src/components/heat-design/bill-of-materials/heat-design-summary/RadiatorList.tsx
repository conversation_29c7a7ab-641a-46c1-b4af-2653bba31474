import { useRooms } from '../../stores/RoomsStore';
import { RadiatorData, Room } from '../../stores/types';
import { getEnabledNewRadiators } from '../utils';
import { useCallback, useMemo, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography } from '@mui/material';
import styled from '@emotion/styled';
import { ColumnDef } from '@tanstack/react-table';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import { useRadiatorsForCountry } from '../../hooks/useRadiators';
import { useGroundwork } from '../../../../context/groundwork-context';
import { Radiator } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { MarkerOutlinedIcon } from '@ui/components/StandardIcons/MarkerOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { useErpItemsWithLabels } from 'hooks/useErpItemsWithLabels';
import { Item } from 'components/bill-of-materials/types';
import { getCatalogueRadiatorLabel, getRadiatorDisplayName } from 'components/heat-design/radiators/utils';

const COLUMN_WIDTHS = '50px 1fr 200px 200px 250px 50px';

const StyledAccordion = styled(Accordion)({
  backgroundColor: '#22222608',
  transition: 'background-color 0.2s, box-shadow 0.2s',
  padding: 0,
  borderRadius: '16px !important',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: 'outline 0.2s',
  },
  '.details-controls': {
    opacity: 0,
    transition: 'opacity 0.2s',
  },
  '&:hover': {
    backgroundColor: '#2222260F',

    '.check-wrapper': {
      outline: '1px solid #ccc',
    },
    '.details-controls': {
      opacity: 1,
    },
  },
  '&.selected': {
    backgroundColor: '#2222261F',

    '&:hover': {
      '.check-wrapper': {
        outline: '1px solid transparent',
      },
    },
  },
  '&.Mui-expanded': {
    backgroundColor: '#fff',
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

interface RadiatorItemRoom {
  room: Room;
  radiatorQuantity: number;
}

interface RadiatorItemRow {
  totalQuantity: number;
  catalogueRadiator?: Radiator;
  erpItem?: Item;
  radiator: RadiatorData & { model?: string };
  rooms: RadiatorItemRoom[];
}

function aggregateRadiators(
  rooms: Room[],
  catalogueRadiators?: Radiator[],
  erpItemsWithLabels?: Item[],
): RadiatorItemRow[] {
  const map = new Map<string, Omit<RadiatorItemRow, 'rooms'> & { rooms: Map<string, RadiatorItemRoom> }>();

  const filteredRooms: Room[] = rooms.map((room) => ({
    ...room,
    radiators: getEnabledNewRadiators([room]),
  }));

  for (const room of filteredRooms) {
    for (const radiatorData of room.radiators) {
      const key = radiatorData.specificationReferenceId ?? radiatorData.uid;
      let entry = map.get(key);
      if (!entry) {
        const catalogueRadiator = catalogueRadiators?.find(
          (r) => r.radiatorId?.value === radiatorData.specificationReferenceId,
        );
        const erpItem = catalogueRadiator?.erpId
          ? erpItemsWithLabels?.find((e) => e.erpId === catalogueRadiator?.erpId)
          : undefined;

        entry = {
          totalQuantity: 0,
          catalogueRadiator,
          radiator: radiatorData,
          erpItem,
          rooms: new Map<string, RadiatorItemRoom>(),
        };
        map.set(key, entry);
      }
      entry.totalQuantity++;
      const roomEntry = entry.rooms.get(room.id);
      if (roomEntry) {
        roomEntry.radiatorQuantity++;
      } else {
        entry.rooms.set(room.id, { room, radiatorQuantity: 1 });
      }
    }
  }

  // Convert Map to array and flatten rooms
  return Array.from(map.values()).map(({ rooms, ...rest }) => ({
    ...rest,
    rooms: Array.from(rooms.values()),
  }));
}

export function RadiatorList() {
  const rooms = useRooms();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set<string>());
  const { countryCode } = useGroundwork();
  const { formatMessage } = useIntl();
  const catalogueRadiators = useRadiatorsForCountry(countryCode);
  const { items: erpItemsWithLabels } = useErpItemsWithLabels();
  const items = useMemo(
    () => aggregateRadiators(rooms, catalogueRadiators?.data?.radiators, erpItemsWithLabels),
    [rooms, catalogueRadiators?.data?.radiators, erpItemsWithLabels],
  );

  const onRowClick = (item: RadiatorItemRow) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(item.radiator.uid)) {
      newExpandedRows.delete(item.radiator.uid);
    } else {
      newExpandedRows.add(item.radiator.uid);
    }
    setExpandedRows(newExpandedRows);
  };
  const rowElement = ({ item }: { item: RadiatorItemRow }) => {
    const isExpanded = expandedRows.has(item.radiator.uid);
    return (
      <StyledAccordion className={`${isExpanded ? 'expanded' : ''}`} expanded={isExpanded}>
        <AccordionSummary
          style={{
            padding: '0px',
            borderBottom: isExpanded ? `1px solid #2222261F` : 'none',
          }}
          onClick={() => onRowClick(item)}
        >
          <Box
            width="100%"
            sx={{
              display: 'grid',
              wordBreak: 'break-all',
              gridTemplateColumns: COLUMN_WIDTHS,
              '&:hover': { '.expand-chevron': { opacity: 1 } },
            }}
          >
            <Stack justifyContent="center" direction="row" gap={1} alignItems="center" padding="16px 8px">
              <Typography variant="body2" align="center">
                {item.totalQuantity}
              </Typography>
            </Stack>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography variant="body2">
                  {getRadiatorDisplayName(item.catalogueRadiator, item.erpItem, item.radiator)}
                </Typography>
                {!item.catalogueRadiator && (
                  <TooltipAira
                    title={formatMessage({
                      id: 'heatDesign.billOfMaterials.heatDesignSummary.customRadiatorTooltip',
                    })}
                  >
                    <MarkerOutlinedIcon style={{ color: grey[500] }} height={20} width={20} />
                  </TooltipAira>
                )}
              </Stack>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">{item.radiator.typeOfHeatEmitter}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">{item.radiator.height}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">{item.radiator.width}</Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                padding: '16px 8px',
                opacity: isExpanded ? '1' : '0',
                transition: 'opacity 0.2s',
              }}
              className="expand-chevron"
            >
              <Chevron transitionDuration="0.2s" direction={isExpanded ? 'up' : 'down'} height={20} width={20} />
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Stack direction="column" sx={{ padding: '16px' }} gap={2}>
            <Stack direction="row" gap={2}>
              {item.catalogueRadiator && (
                <LabelValueDisplay
                  sx={{ width: 'auto', gap: 3, '& p': { textWrap: 'nowrap' } }}
                  label={formatMessage({ id: 'common.label.catalogueRadiator' })}
                  value={getCatalogueRadiatorLabel(item.catalogueRadiator)}
                />
              )}
              {item.catalogueRadiator?.erpId && (
                <LabelValueDisplay label="Erp ID" value={item.catalogueRadiator.erpId} />
              )}
              {(item.catalogueRadiator?.vendorId || item.catalogueRadiator?.manufacturerId) && (
                <LabelValueDisplay
                  label="External ID"
                  value={item.catalogueRadiator?.vendorId ?? item.catalogueRadiator?.manufacturerId}
                />
              )}
            </Stack>
            <Stack direction="row" gap={2}>
              {item.rooms.map((room) => {
                return (
                  <LabelValueDisplay
                    key={room.room.id}
                    label={room.room.name}
                    value={`${room.radiatorQuantity} pc`}
                    mode="pill"
                  />
                );
              })}
            </Stack>
          </Stack>
        </AccordionDetails>
      </StyledAccordion>
    );
  };

  const headerTypographyGetter = useCallback(
    (text: MessageDescriptor['id']) => (
      <Typography fontWeight="500" variant="body2">
        <FormattedMessage id={text} />
      </Typography>
    ),
    [],
  );

  const columns = useMemo<ColumnDef<RadiatorItemRow>[]>(
    () => [
      {
        header: headerTypographyGetter.bind(null, 'heatDesign.billOfMaterials.heatDesignSummary.radiatorList.quantity'),
        accessorKey: 'qty',
        enableSorting: false,
      },
      {
        header: headerTypographyGetter.bind(null, 'heatDesign.billOfMaterials.heatDesignSummary.radiatorList.model'),
        accessorKey: 'model',
        enableSorting: false,
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.label'),
        accessorKey: 'label',
        enableSorting: false,
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.measurement.heightmm'),
        accessorKey: 'category',
        enableSorting: false,
      },
      {
        header: headerTypographyGetter.bind(null, 'common.label.measurement.lengthmm'),
        accessorKey: 'type',
        enableSorting: false,
      },
      {
        header: '',
        accessorKey: 'expand',
        enableSorting: false,
      },
    ],
    [headerTypographyGetter],
  );

  return (
    <TanstackTable
      styles={{
        columnWidths: COLUMN_WIDTHS,
        bodyColumnWidths: '1fr',
        tableContainer: () => ({
          padding: '0 8px',
          maxHeight: '500px',
        }),
        tableBodyRow: () => ({
          paddingBottom: '8px',
        }),
        tableHead: () => ({
          backgroundColor: '#fff',
        }),
        tableHeadRow: () => ({
          backgroundColor: '#fff',
        }),
        tableHeadCell: () => ({
          border: 'none',
        }),
      }}
      columns={columns}
      customRowRenderer={(item) => rowElement({ item })}
      data={items}
    />
  );
}
