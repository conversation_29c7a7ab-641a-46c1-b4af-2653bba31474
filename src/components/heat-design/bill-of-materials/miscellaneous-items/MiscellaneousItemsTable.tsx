import { memo } from 'react';
import { FormattedMessage } from 'react-intl';
import { ItemsTable } from 'components/bill-of-materials/components/common/ItemsTable';
import ItemDetailsPanel from 'components/bill-of-materials/components/common/ItemDetailsPanel';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import { MiscellaneousItem } from 'components/bill-of-materials/types';
import { useMiscellaneousItemErrors } from '../store/selectors/useMiscellaneousItemErrors';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

interface MiscellaneousItemsTableProps {
  items: MiscellaneousItem[];
}

function MiscellaneousItemsTableComponent({ items }: Readonly<MiscellaneousItemsTableProps>) {
  const setMiscellaneousItems = useBillOfMaterialsDesignStore((s) => s.setMiscellaneousItems);
  const { erpItems, isLoading } = useErpItems();
  const miscellaneousItemErrors = useMiscellaneousItemErrors();
  // Handle deleting an item
  const handleDeleteItem = (itemId: string) => {
    const updatedItems = items.filter((item) => item.itemId !== itemId);
    setMiscellaneousItems(updatedItems);
  };

  // Handle updating item quantity
  const handleQuantityChange = (itemId: string, newQuantity?: number) => {
    const updatedItems = items.map((item) => {
      if (item.itemId === itemId) {
        return {
          ...item,
          quantity: newQuantity,
        };
      }
      return item;
    });
    setMiscellaneousItems(updatedItems);
  };

  // Handle updating item name
  const handleNameChange = (itemId: string, newName: string) => {
    const updatedItems = items.map((item) => {
      if (item.itemId === itemId) {
        return {
          ...item,
          name: newName,
        };
      }
      return item;
    });
    setMiscellaneousItems(updatedItems);
  };

  // Handle updating custom item cost
  const handleCustomItemCostChange = (itemId: string, newCost?: number) => {
    const updatedItems = items.map((item) => {
      if (item.itemId === itemId && item.type === 'custom') {
        return {
          ...item,
          cost: newCost,
        } satisfies MiscellaneousItem;
      }
      return item;
    });
    setMiscellaneousItems(updatedItems);
  };

  if (items.length === 0) {
    return (
      <div style={{ padding: '16px 0', textAlign: 'center' }}>
        <FormattedMessage id="billOfMaterialsDesignPage.addMiscellaneousItems" />
      </div>
    );
  }

  return (
    <ItemsTable
      items={items}
      isLoading={isLoading}
      showCostPerUnit
      renderDetailsPanel={(item) => {
        const erpItem = erpItems[item.itemId];
        if (item.type === 'erp' && erpItem !== undefined) {
          return <ItemDetailsPanel item={erpItem} showCostPerUnit={true} />;
        }
        return null;
      }}
      onDeleteItem={handleDeleteItem}
      onQuantityChange={handleQuantityChange}
      onNameChange={handleNameChange}
      onCustomItemCostChange={handleCustomItemCostChange}
      errors={miscellaneousItemErrors}
      tableBackgroundColor="#fff"
    />
  );
}

export const MiscellaneousItemsTable = memo(MiscellaneousItemsTableComponent);
