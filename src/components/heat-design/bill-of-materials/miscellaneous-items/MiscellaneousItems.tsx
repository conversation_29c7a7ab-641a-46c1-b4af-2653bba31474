import { v4 as uuidv4 } from 'uuid';
import { memo, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Stack, Typography } from '@mui/material';
import styled from '@emotion/styled';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { FormattedMessage } from 'react-intl';
import { Button } from '@ui/components/Button/Button';
import { useBillOfMaterialsStore } from 'components/bill-of-materials/BillOfMaterialsStore';
import { ListIcon } from '@ui/components/StandardIcons/ListIcon';
import { BILL_OF_MATERIALS_MODALS } from 'components/bill-of-materials/BillOfMaterialsModals';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import type { ErpItem } from 'server/api/routers/billOfMaterials';
import { MiscellaneousItemsTable } from './MiscellaneousItemsTable';
import { DEFAULT_QUANTITY_FOR_NEW_ITEM } from 'components/bill-of-materials/constants/constants';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { MiscellaneousItem } from 'components/bill-of-materials/types';
import { compareTimestamps } from 'components/bill-of-materials/utils';
import { WarningIcon } from '../../components/WarningIcon';
import isEmpty from 'lodash/isEmpty';
import { useMiscellaneousItemErrors } from '../store/selectors/useMiscellaneousItemErrors';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  borderRadius: '16px !important',
  padding: '16px',
  '.MuiAccordionDetails-root': {
    padding: 0,
  },
  '.MuiAccordionSummary-root': {
    padding: 0,
  },
});

function MiscellaneousItemsComponent() {
  const [isExpanded, setIsExpanded] = useState(false);
  const { openModal } = useBillOfMaterialsStore();
  const { erpItems } = useErpItems();
  const miscellaneousItems = useBillOfMaterialsDesignStore((s) => s.miscellaneousItems);
  const setMiscellaneousItems = useBillOfMaterialsDesignStore((s) => s.setMiscellaneousItems);
  const miscellaneousItemErrors = useMiscellaneousItemErrors();
  const hasError = !isEmpty(miscellaneousItemErrors);
  const onItemCatalogueClose = (selectedItems: ErpItem[]) => {
    const currentCustomItems = miscellaneousItems.filter((item) => item.type === 'custom');
    const selectedErpItems = selectedItems.map((erpItem): MiscellaneousItem => {
      const existingItem = miscellaneousItems.find((item) => item.type === 'erp' && item.itemId === erpItem.id?.value);

      if (existingItem) {
        return existingItem;
      }

      return {
        type: 'erp',
        itemId: erpItem.id?.value || '',
        quantity: erpItem.quantity ?? DEFAULT_QUANTITY_FOR_NEW_ITEM,
        createdAt: new Date(),
      };
    });

    setMiscellaneousItems(
      [...selectedErpItems, ...currentCustomItems].toSorted((a, b) => {
        const timestampComparison = compareTimestamps(a.createdAt, b.createdAt);

        // If timestamps are equal, sort alphabetically by description
        if (timestampComparison === 0) {
          const descriptionA = erpItems[a.itemId]?.description ?? '';
          const descriptionB = erpItems[b.itemId]?.description ?? '';
          return descriptionA.localeCompare(descriptionB);
        }

        return timestampComparison;
      }),
    );
    setIsExpanded(true);
  };

  return (
    <Stack gap={3}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
        <Typography variant="headline3">
          <FormattedMessage id="billOfMaterialsDesignPage.addMiscellaneousItems" />
        </Typography>
        <Stack direction="row" gap={2} alignItems="center">
          <Button
            data-testid="browse-items-button"
            onClick={() =>
              openModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE, {
                preSelectedItemIds: new Set(
                  miscellaneousItems.filter((item) => item.type === 'erp').map((item) => item.itemId),
                ),
                onClose: onItemCatalogueClose,
              })
            }
            sx={{
              borderRadius: '16px',
              backgroundColor: '#22222608',
              height: '100%',
              '&:hover': { backgroundColor: '#22222616' },
            }}
          >
            <Stack justifyContent="space-between" direction="row" alignItems="center" gap={1}>
              <ListIcon color="#000" />
              <Typography variant="body1Emphasis">
                <FormattedMessage id="common.label.addItems" />
              </Typography>
            </Stack>
          </Button>
          <Button
            data-testid="add-custom-item-button"
            onClick={() =>
              setMiscellaneousItems([
                { type: 'custom', itemId: uuidv4(), quantity: DEFAULT_QUANTITY_FOR_NEW_ITEM, createdAt: new Date() },
                ...miscellaneousItems,
              ])
            }
            sx={{
              borderRadius: '16px',
              backgroundColor: '#22222608',
              height: '100%',
              '&:hover': { backgroundColor: '#22222616' },
            }}
          >
            <Stack justifyContent="space-between" direction="row" alignItems="center" gap={1}>
              <AddOutlinedIcon color="#000" />
              <Typography variant="body1Emphasis">
                <FormattedMessage id="billOfMaterials.miscellaneousItems.addCustomItem" />
              </Typography>
            </Stack>
          </Button>
        </Stack>
      </Stack>
      <Typography variant="body2">
        <FormattedMessage id="billOfMaterialsDesignPage.addMiscellaneousItemsDescription" />
      </Typography>
      <StyledAccordion expanded={isExpanded}>
        <AccordionSummary onClick={() => setIsExpanded(!isExpanded)} sx={{ marginBottom: 1 }}>
          <Stack direction="row" alignItems="center" gap={2}>
            <Chevron
              style={{ flex: '0 0 auto', height: '100%', width: '20px' }}
              transitionDuration="0.2s"
              direction={isExpanded ? 'up' : 'down'}
              height={24}
              width={20}
            />
            {hasError && (
              <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
            )}
            <Typography variant="body1Emphasis">
              <FormattedMessage id="billOfMaterialsDesignPage.Items" />
            </Typography>
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <MiscellaneousItemsTable items={miscellaneousItems} />
        </AccordionDetails>
      </StyledAccordion>
    </Stack>
  );
}

export const MiscellaneousItems = memo(MiscellaneousItemsComponent);
