import { createContext, useContext, useRef } from 'react';
import { BillOfMaterialsDesignStore, initializeBillOfMaterialsDesignStore } from './BillOfMaterialsDesignStore';
import { useStore } from 'zustand';

const BillOfMaterialsStoreContext = createContext<BillOfMaterialsDesignStore | null>(null);

export const BillOfMaterialsProvider = ({ children }: { children: React.ReactNode }) => {
  const storeRef = useRef<BillOfMaterialsDesignStore>();
  storeRef.current ??= initializeBillOfMaterialsDesignStore();
  return (
    <BillOfMaterialsStoreContext.Provider value={storeRef.current}>{children}</BillOfMaterialsStoreContext.Provider>
  );
};

// Custom hook to access the store
export const useBillOfMaterialsDesignStore = <T,>(
  selector: (state: ReturnType<ReturnType<typeof initializeBillOfMaterialsDesignStore>['getState']>) => T,
): T => {
  const store = useContext(BillOfMaterialsStoreContext);
  if (!store) throw new Error('useBillOfMaterialsDesignStore must be used within a BillOfMaterialsDesignProvider');
  return useStore(store, selector);
};
