import { MiscellaneousItem, DesignedBomBundle } from '../../../bill-of-materials/types';
import { createStore, StateCreator, StoreApi } from 'zustand';
import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';

export type BillOfMaterialsDesignState = {
  bundleCollections: BundleCollection[]; // Here for reasons of performance + simplicity
  bundlePerCollection: Record<string, DesignedBomBundle | undefined>;
  miscellaneousItems: MiscellaneousItem[];
  isReadyForProcurement: boolean;
  updatedAt?: Date;
  setIsReadyForProcurement: (isReady: boolean) => void;
  setUpdatedAt: (timestamp?: Date) => void;
  setBundleCollections: (bundleCollections: BundleCollection[]) => void;
  setBundleForCollection: (collectionId: string, bundle?: DesignedBomBundle) => void;
  setMiscellaneousItems: (items: MiscellaneousItem[]) => void;
  updateBundleItemQuantity: (collectionId: string, itemId: string, quantity?: number) => void;
};

const createBillOfMaterialsDesignStore: StateCreator<BillOfMaterialsDesignState> = (set) => ({
  bundlePerCollection: {},
  bundleCollections: [],
  miscellaneousItems: [],
  isReadyForProcurement: false,
  updatedAt: undefined,
  setUpdatedAt: (timestamp) => set({ updatedAt: timestamp }),
  setIsReadyForProcurement: (isReady) => set({ isReadyForProcurement: isReady }),
  setBundleCollections: (items) => set({ bundleCollections: items }),
  setBundleForCollection: (collectionId, bundle) => {
    set((state) => {
      return {
        bundlePerCollection: {
          ...state.bundlePerCollection,
          [collectionId]: bundle,
        },
      };
    });
  },
  setMiscellaneousItems: (items) => set({ miscellaneousItems: items }),
  updateBundleItemQuantity: (collectionId, itemId, quantity) =>
    set((state) => {
      const bundle = state.bundlePerCollection[collectionId];
      if (!bundle) return state;

      const updatedItems = bundle.items.map((item) => {
        if (item.itemId?.value === itemId) {
          return { ...item, quantity };
        }
        return item;
      });

      return {
        bundlePerCollection: {
          ...state.bundlePerCollection,
          [collectionId]: {
            ...bundle,
            items: updatedItems,
          },
        },
      };
    }),
});

export type BillOfMaterialsDesignStore = StoreApi<ReturnType<typeof createBillOfMaterialsDesignStore>>;
export const initializeBillOfMaterialsDesignStore = () => createStore(createBillOfMaterialsDesignStore);
