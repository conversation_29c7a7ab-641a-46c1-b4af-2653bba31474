import { useMemo } from 'react';
import isEmpty from 'lodash/isEmpty';
import { useBundleCollectionIdsWithErrors } from './useBundleCollectionIdsWithErrors';
import { useBundleItemErrors } from './useBundleItemErrors';
import { useMiscellaneousItemErrors } from './useMiscellaneousItemErrors';
import { useProtobufHeatDesignStore } from 'components/heat-design/stores/ProtobufHeatDesignStore';
import { useBundleIdsWithErrors } from './useBundleIdsWithErrors';

export function useGeneralError() {
  const bundleCollectionIdsWithErrors = useBundleCollectionIdsWithErrors();
  const bundleIdsWithErrors = useBundleIdsWithErrors();
  const bundleItemErrors = useBundleItemErrors();
  const miscellaneousItemErrors = useMiscellaneousItemErrors();
  const isHeatDesignLocked = useProtobufHeatDesignStore((s) => s.isLocked);

  return useMemo(() => {
    return (
      !!bundleCollectionIdsWithErrors.size ||
      !!bundleIdsWithErrors.size ||
      !isEmpty(bundleItemErrors) ||
      !isEmpty(miscellaneousItemErrors) ||
      !isHeatDesignLocked
    );
  }, [
    bundleCollectionIdsWithErrors,
    bundleIdsWithErrors,
    bundleItemErrors,
    miscellaneousItemErrors,
    isHeatDesignLocked,
  ]);
}
