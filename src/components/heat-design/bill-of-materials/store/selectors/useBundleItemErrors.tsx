import { useMemo } from 'react';
import { useBillOfMaterialsDesignStore } from '../BillOfMaterialsContext';
import { DesignedBomBundle } from '../../../../bill-of-materials/types';
import { BundleErrorMap, BundleItemError, bundleItemValidation } from '../../validation';

export function useBundleItemErrors() {
  const bundlePerCollection = useBillOfMaterialsDesignStore((s) => s.bundlePerCollection);
  const bundleItemErrors = useMemo(() => {
    const result: BundleErrorMap = {};

    Object.values(bundlePerCollection)
      .filter((b): b is DesignedBomBundle => !!b)
      .forEach((bundle) => {
        const bundleCollectionId = bundle.bundleCollectionId?.value;
        const bundleId = bundle.bundleId?.value;
        if (!bundleCollectionId || !bundleId) {
          return;
        }

        bundle.items.forEach((item) => {
          const itemId = item.itemId?.value;

          if (!itemId) {
            return;
          }

          const itemErrors = Object.entries(bundleItemValidation)
            .filter(([_, validate]) => !validate(item))
            .map(([errorType]) => errorType as BundleItemError);

          if (itemErrors.length === 0) {
            return;
          }

          result[bundleCollectionId] ??= {};
          result[bundleCollectionId][bundleId] ??= {};

          result[bundleCollectionId][bundleId][itemId] = itemErrors;
        });
      });
    return result;
  }, [bundlePerCollection]);
  return bundleItemErrors;
}
