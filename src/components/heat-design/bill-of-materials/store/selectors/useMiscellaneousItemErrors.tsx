import { useMemo } from 'react';
import { useBillOfMaterialsDesignStore } from '../BillOfMaterialsContext';
import { MiscellaneousItemError, MiscellaneousItemsErrorMap, miscellaneousItemValidation } from '../../validation';

export function useMiscellaneousItemErrors() {
  const miscellaneousItems = useBillOfMaterialsDesignStore((s) => s.miscellaneousItems);
  const miscellaneousItemErrors = useMemo(() => {
    const miscellaneousItemErrors = miscellaneousItems.reduce((acc, item) => {
      if (!item.itemId) {
        return acc;
      }

      const itemErrors = Object.entries(miscellaneousItemValidation)
        .filter(([_, validate]) => !validate(item))
        .map(([error]) => error as MiscellaneousItemError);

      if (itemErrors.length === 0) {
        return acc;
      }

      acc[item.itemId] = itemErrors;

      return acc;
    }, {} as MiscellaneousItemsErrorMap);
    return miscellaneousItemErrors;
  }, [miscellaneousItems]);
  return miscellaneousItemErrors;
}
