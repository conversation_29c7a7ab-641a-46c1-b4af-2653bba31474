import { useMemo } from 'react';
import { isTemplateBundleCollection } from '../../../../bill-of-materials/utils';
import { useBillOfMaterialsDesignStore } from '../BillOfMaterialsContext';

export function useBundleCollectionIdsWithErrors() {
  const bundleCollections = useBillOfMaterialsDesignStore((s) => s.bundleCollections);
  const bundlePerCollection = useBillOfMaterialsDesignStore((s) => s.bundlePerCollection);
  const mandatoryBundleCollections = useMemo(() => {
    return (
      bundleCollections.filter((collection) => isTemplateBundleCollection(collection) && collection.mandatory) ?? []
    );
  }, [bundleCollections]);

  const bundleCollectionIdsWithErrors = useMemo(() => {
    return new Set(
      mandatoryBundleCollections
        .filter((collection) => {
          return collection.id?.value ? !bundlePerCollection[collection.id.value] : false;
        })
        .map((collection) => collection.id?.value)
        .filter((id): id is string => !!id),
    );
  }, [mandatoryBundleCollections, bundlePerCollection]);
  return bundleCollectionIdsWithErrors;
}
