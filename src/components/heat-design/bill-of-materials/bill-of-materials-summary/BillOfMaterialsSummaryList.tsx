import { useMemo, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography } from '@mui/material';
import styled from '@emotion/styled';
import { ColumnDef } from '@tanstack/react-table';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import { getFormattedItemPrice, getFormattedItemPricePerUnit } from '../../../bill-of-materials/utils';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { SummaryItemData } from './SummaryItemData';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { MarkerOutlinedIcon } from '@ui/components/StandardIcons/MarkerOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { currencySymbols } from '../../../bill-of-materials/types';
import { useMarketStore } from '../../../../utils/stores/marketStore';
import { SummaryItem, SummaryItemSource } from '../types';
import ItemWarningToolTip from 'components/bill-of-materials/components/common/ItemWarningToolTip';
import { useGroundwork } from '../../../../context/groundwork-context';
import { getLocaleFromCountry } from '../../../../utils/marketConfigurations';
import { getCatalogueRadiatorLabel } from 'components/heat-design/radiators/utils';

const COLUMN_WIDTHS = '80px minmax(400px, 1fr) 200px 400px 450px 50px';

const StyledAccordion = styled(Accordion)({
  backgroundColor: '#22222608',
  transition: 'background-color 0.2s, box-shadow 0.2s',
  padding: 0,
  borderRadius: '16px !important',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: 'outline 0.2s',
  },
  '.details-controls': {
    opacity: 0,
    transition: 'opacity 0.2s',
  },
  '&:hover': {
    backgroundColor: '#2222260F',

    '.check-wrapper': {
      outline: '1px solid #ccc',
    },
    '.details-controls': {
      opacity: 1,
    },
  },
  '&.selected': {
    backgroundColor: '#2222261F',

    '&:hover': {
      '.check-wrapper': {
        outline: '1px solid transparent',
      },
    },
  },
  '&.Mui-expanded': {
    backgroundColor: '#fff',
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

/** Sum quantity across all sources */
const getTotalQuantity = (sources: SummaryItemSource[]) => sources.reduce((acc, s) => acc + s.quantity, 0);
type BillOfMaterialsSummaryListProps = {
  summaryItems: SummaryItem[];
};

export function BillOfMaterialsSummaryList(props: BillOfMaterialsSummaryListProps) {
  const { country } = useGroundwork();
  const locale = getLocaleFromCountry(country);
  const { currency } = useMarketStore();
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set<string>());
  const { formatMessage } = useIntl();
  const onRowClick = (item: SummaryItem) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(item.id)) {
      newExpandedRows.delete(item.id);
    } else {
      newExpandedRows.add(item.id);
    }
    setExpandedRows(newExpandedRows);
  };

  const rowElement = ({ item }: { item: SummaryItem }) => {
    const source = item.sources[0];
    const isCustomRadiator = source && item.sources.length === 1 && source.type === 'radiator' && !item.erpItem;
    const catalogueRadiator = source && source.type === 'radiator' ? source.catalogueRadiator : undefined;
    const hlcDisplayName = source && source.type === 'hlc' ? source.hlcItemDisplayName : undefined;
    const catalogueRadiatorLabel = getCatalogueRadiatorLabel(catalogueRadiator);

    return (
      <StyledAccordion
        className={`${expandedRows.has(item.id) ? 'expanded' : ''}`}
        expanded={expandedRows.has(item.id)}
      >
        <AccordionSummary
          style={{
            padding: '0px',
            borderBottom: expandedRows.has(item.id) ? `1px solid #2222261F` : 'none',
          }}
          onClick={() => onRowClick(item)}
        >
          <Box
            width="100%"
            style={{
              display: 'grid',
              wordBreak: 'break-all',
              gridTemplateColumns: COLUMN_WIDTHS,
            }}
          >
            <Stack
              justifyContent="center"
              direction="row"
              gap={1}
              alignItems="center"
              padding="16px 8px"
              sx={{ width: '100%' }}
            >
              <Typography variant="body2" align="center" sx={{ display: 'flex', justifyContent: 'center' }}>
                {getTotalQuantity(item.sources)}
              </Typography>
            </Stack>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Stack direction="row" alignItems="center" gap={2}>
                {!!item.erpItem?.archivedAt && <ItemWarningToolTip item={item.erpItem} />}
                <Typography variant="body2">{item.name || ' - '}</Typography>
                {isCustomRadiator && (
                  <TooltipAira
                    title={formatMessage({
                      id: 'heatDesign.billOfMaterials.heatDesignSummary.customRadiatorTooltip',
                    })}
                  >
                    <MarkerOutlinedIcon style={{ color: grey[500] }} height={20} width={20} />
                  </TooltipAira>
                )}
              </Stack>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">
                {item.totalCostMinorPrice !== undefined
                  ? getFormattedItemPrice({ minorPrice: item.totalCostMinorPrice, currency }, locale)
                  : '-'}
              </Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">{item.erpItem?.erpId ?? ' - '}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Typography variant="body2">{item.erpItem?.label?.label ?? ' - '}</Typography>
            </Box>

            <Box style={{ display: 'flex', alignItems: 'center', padding: '16px 8px' }}>
              <Chevron
                transitionDuration="0.2s"
                direction={expandedRows.has(item.id) ? 'up' : 'down'}
                height={20}
                width={20}
              />
            </Box>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Stack gap={2} sx={{ padding: '16px', borderRadius: '16px' }}>
            <Stack direction="row" alignItems="center" gap={3}>
              {catalogueRadiator && (
                <LabelValueDisplay
                  sx={{ width: 'auto', gap: 3, '& p': { textWrap: 'nowrap' } }}
                  label={formatMessage({ id: 'common.label.catalogueRadiator' })}
                  value={catalogueRadiatorLabel}
                />
              )}
              {hlcDisplayName && (
                <LabelValueDisplay
                  sx={{ width: 'auto', gap: 3, '& p': { textWrap: 'nowrap' } }}
                  label={formatMessage({ id: 'common.label.hlcDisplayName', defaultMessage: 'Display Name' })}
                  value={hlcDisplayName}
                />
              )}
              {item.erpItem && (
                <>
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.supplier' })}
                    value={item.erpItem.supplier || ' - '}
                  />
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.unit' })}
                    value={item.erpItem.unit || ' - '}
                  />
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'heatDesign.billOfMaterials.summary.itemsPerUnit' })}
                    value={item.erpItem.quantity || ' - '}
                  />
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={`${formatMessage({ id: 'billOfMaterials.itemCatalogue.table.costPerUnit' })} (${currencySymbols[currency]})`}
                    value={item.erpItem.minorPrice ? getFormattedItemPricePerUnit(item.erpItem, locale) : '-'}
                  />
                </>
              )}
            </Stack>
            <Stack gap={2} direction="row" alignItems="center">
              {item.sources.map((source, index) => {
                return <SummaryItemData source={source} key={index} />;
              })}
            </Stack>
          </Stack>
        </AccordionDetails>
      </StyledAccordion>
    );
  };

  const headerTypographyGetter = (options: {
    text: MessageDescriptor['id'];
    isCentered?: boolean;
    messageValues?: Record<string, string>;
  }) => (
    <Typography
      fontWeight="500"
      variant="body2"
      sx={{ width: '100%', display: 'flex', justifyContent: options.isCentered ? 'center' : 'flex-start' }}
    >
      <FormattedMessage id={options.text} values={options.messageValues ?? {}} />
    </Typography>
  );

  const columns = useMemo<ColumnDef<SummaryItem>[]>(
    () => [
      {
        header: headerTypographyGetter.bind(null, {
          text: 'heatDesign.billOfMaterials.heatDesignSummary.radiatorList.quantity',
          isCentered: true,
        }),
        accessorKey: 'qty',
        enableSorting: false,
      },
      {
        header: headerTypographyGetter.bind(null, {
          text: 'billOfMaterials.editBundle.itemsTable.nameColumn',
        }),
        accessorKey: 'name',
        sortingFn: 'text',
      },
      {
        header: headerTypographyGetter.bind(null, {
          text: 'heatDesign.billOfMaterials.summary.cost',
          messageValues: {
            symbol: currencySymbols[currency] ?? '',
          },
        }),
        accessorKey: 'cost',
        enableSorting: true,
        sortingFn: (rowA, rowB) => {
          const aValue = rowA.original.totalCostMinorPrice;
          const bValue = rowB.original.totalCostMinorPrice;

          if (aValue === undefined && bValue === undefined) {
            return 0; // Handle undefined values gracefully
          } else if (aValue === undefined) {
            return -1; // Treat undefined as less than defined
          } else if (bValue === undefined) {
            return 1; // Treat defined as greater than undefined
          }
          return aValue - bValue;
        },
      },
      {
        header: headerTypographyGetter.bind(null, {
          text: 'heatDesign.common.erpId',
        }),
        accessorKey: 'erpId',
        enableSorting: true,
        sortingFn: (rowA, rowB) => {
          const aValue = rowA.original.erpItem?.erpId;
          const bValue = rowB.original.erpItem?.erpId;
          if (aValue === undefined && bValue === undefined) {
            return 0; // Handle undefined values gracefully
          } else if (aValue === undefined) {
            return -1; // Treat undefined as less than defined
          } else if (bValue === undefined) {
            return 1; // Treat defined as greater than undefined
          }
          return aValue.localeCompare(bValue);
        },
      },
      {
        header: headerTypographyGetter.bind(null, {
          text: 'common.label.label',
        }),
        accessorKey: 'label',
        enableSorting: false,
      },
    ],
    [currency],
  );

  return (
    <Box sx={{ background: '#fff', padding: '16px', borderRadius: '16px' }}>
      <TanstackTable
        styles={{
          columnWidths: COLUMN_WIDTHS,
          bodyColumnWidths: '1fr',
          tableContainer: () => ({
            padding: '0 8px',
            maxHeight: '500px',
          }),
          tableBodyRow: () => ({
            paddingBottom: '8px',
          }),
          tableHead: () => ({
            backgroundColor: '#fff',
          }),
          tableHeadRow: () => ({
            backgroundColor: '#fff',
          }),
          tableHeadCell: () => ({
            border: 'none',
          }),
        }}
        columns={columns}
        customRowRenderer={(item) => rowElement({ item })}
        data={props.summaryItems}
        initialState={{
          sorting: [
            {
              id: 'name',
              desc: false,
            },
          ],
        }}
      />
    </Box>
  );
}
