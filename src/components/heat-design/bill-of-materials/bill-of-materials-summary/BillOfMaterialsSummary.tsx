import styled from '@emotion/styled';
import { Accordion, AccordionDetails, AccordionSummary, Stack, Typography } from '@mui/material';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { MoneyDropOutlinedIcon } from '@ui/components/StandardIcons/MoneyDropOutlinedIcon';
import { QuoteOutlinedIcon } from '@ui/components/StandardIcons/QuoteOutlinedIcon';
import { RulerOutlinedIcon } from '@ui/components/StandardIcons/RulerOutlinedIcon';
import { MonetaryHeaderItem } from 'components/bill-of-materials/components/common/MonetaryHeaderItem';
import { getItemMinorPricePerUnit } from 'components/bill-of-materials/utils';
import { getRadiatorDisplayName } from 'components/heat-design/radiators/utils';
import groupBy from 'lodash/groupBy';
import { memo, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useCountry } from '../../../../hooks/useCountry';
import { useEnergySolutionId } from '../../../../hooks/useEnergySolution';
import { useErpItemsWithLabels } from '../../../../hooks/useErpItemsWithLabels';
import { api } from '../../../../utils/api';
import { getCountryEnum, getLocaleFromCountry, marketConfiguration } from '../../../../utils/marketConfigurations';
import { useRadiatorsForCountry } from '../../hooks/useRadiators';
import { useRooms } from '../../stores/RoomsStore';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import { MiscellaneousSource, SummaryItem, SummaryItemSource } from '../types';
import { getEnabledNewRadiators } from '../utils';
import { BillOfMaterialsSummaryList } from './BillOfMaterialsSummaryList';

const StyledAccordion = styled(Accordion)({
  backgroundColor: '#22222608',
  borderRadius: '16px !important',
  padding: '0',
  '.MuiAccordionDetails-root': {
    padding: 0,
  },
  '.MuiAccordionSummary-root': {
    padding: 0,
  },
});

export function mergeSummaryItems(items: SummaryItem[]): SummaryItem[] {
  const map = new Map<string, SummaryItem>();

  for (const item of items) {
    const itemKey = getSummaryKey(item);
    const existingItem = map.get(itemKey);

    if (!existingItem) {
      map.set(itemKey, {
        ...item,
        sources: item.sources.map((s) => ({ ...s })),
      });
      continue;
    }

    for (const incoming of item.sources) {
      const srcKey = getSourceKey(incoming);
      const existingSource = existingItem.sources.find((s) => getSourceKey(s) === srcKey);

      if (existingSource) {
        // add quantities (treat undefined as 0)
        existingSource.quantity = (existingSource.quantity ?? 0) + (incoming.quantity >= 0 ? incoming.quantity : 0);
      } else {
        // brand-new source for this SummaryItem
        existingItem.sources.push({ ...incoming });
      }
    }
  }

  return [...map.values()];
}

/**
 * Generates a key to identify non-unique sources inside summary-items
 * This is useful if a specific item is added to the BOM from multiple sources
 * In such a case we need to merge the quantities.
 * @param source
 */
function getSourceKey(source: SummaryItemSource): string {
  const type = source.type;
  switch (type) {
    case 'bundle':
      return `${source.bundleCollection.id?.value}:${source.bundle.id?.value}`;
    case 'miscellaneous':
      return 'misc'; // Miscellaneous summary items are always unique, so don't need a specific source key
    case 'radiator':
      return `${source.catalogueRadiator?.radiatorId?.value ?? source.radiator.uid}`;
    case 'hlc':
      return `hlc`; // HLC summary items are always unique, so don't need a specific source key
    default:
      return type satisfies never;
  }
}

function getSummaryKey(item: SummaryItem): string {
  const erp = item.erpItem;
  if (erp) {
    return erp.erpId ?? '';
  }
  return item.id;
}

function BillOfMaterialsSummaryComponent() {
  const [isExpanded, setIsExpanded] = useState(true);
  const { countryCode } = useGroundwork();
  const rooms = useRooms();
  const { currency } = marketConfiguration[countryCode];
  const energySolutionId = useEnergySolutionId();
  const countryEnum = getCountryEnum(countryCode);
  // We still need country and locale for other parts of the component
  const country = useCountry();
  const locale = getLocaleFromCountry(country);

  const solutionData = api.AiraBackend.getEnergySolutionDiff.useQuery(
    { energySolutionId: energySolutionId! },
    {
      enabled: !!energySolutionId,
    },
  );
  const catalogueRadiators = useRadiatorsForCountry(countryCode);
  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({
    country: countryEnum,
  });
  const chosenBundlesPerCollection = useBillOfMaterialsDesignStore((s) => s.bundlePerCollection);
  const miscellaneousItems = useBillOfMaterialsDesignStore((s) => s.miscellaneousItems);

  // Use the shared hook to get ERP items with labels
  const { items: erpItemsWithLabels } = useErpItemsWithLabels();

  const summaryItemsFromBundles = useMemo<SummaryItem[]>(() => {
    const collections = bundleCollectionQuery.data?.bundleCollections ?? [];

    return Object.entries(chosenBundlesPerCollection).flatMap(([collectionId, designedBomBundle]) => {
      const designedBomBundleId = designedBomBundle?.bundleId?.value;
      if (!collectionId || !designedBomBundleId) return [];

      const collection = collections.find((c) => c.id?.value === collectionId);
      const bundle = collection?.bundles.find((b) => b.id?.value === designedBomBundleId);
      if (!collection || !bundle) return [];

      return designedBomBundle.items.map<SummaryItem>((bundleItem) => {
        const erpItem = erpItemsWithLabels.find((e) => e.id?.value === bundleItem.itemId?.value);
        const totalCostMinorPrice =
          erpItem?.minorPrice && bundleItem.quantity
            ? getItemMinorPricePerUnit(erpItem) * bundleItem.quantity
            : undefined;

        return {
          id: erpItem ? erpItem.id.value : uuidv4(),
          name: erpItem?.description ?? '',
          erpItem,
          totalCostMinorPrice,
          sources: [
            {
              type: 'bundle',
              quantity: bundleItem.quantity ?? 0,
              bundle,
              bundleCollection: collection,
            },
          ],
        } satisfies SummaryItem;
      });
    });
  }, [chosenBundlesPerCollection, bundleCollectionQuery.data?.bundleCollections, erpItemsWithLabels]);

  const summaryItemsFromRadiators = useMemo<SummaryItem[]>(() => {
    const allRadiators = getEnabledNewRadiators(rooms);
    const grouped = groupBy(allRadiators, (r) => r.specificationReferenceId ?? r.uid);

    return Object.values(grouped).map((radiators) => {
      const radiator = radiators?.[0];
      if (!radiator) {
        throw new Error('No radiator found');
      }
      const catalogueRadiator = radiator.specificationReferenceId
        ? catalogueRadiators?.data?.radiators?.find((r) => r.radiatorId?.value === radiator.specificationReferenceId)
        : undefined;
      const erpItem = catalogueRadiator?.erpId
        ? erpItemsWithLabels.find((e) => e.erpId === catalogueRadiator?.erpId)
        : undefined;
      const name = getRadiatorDisplayName(catalogueRadiator, erpItem, radiator);
      const totalCostMinorPrice = erpItem?.minorPrice
        ? getItemMinorPricePerUnit(erpItem) * radiators.length
        : undefined;
      return {
        id: uuidv4(),
        name: name,
        erpItem,
        totalCostMinorPrice,
        sources: [
          {
            type: 'radiator',
            quantity: radiators.length,
            radiator: radiator,
            catalogueRadiator,
          },
        ],
      } satisfies SummaryItem;
    });
  }, [rooms, catalogueRadiators?.data?.radiators, erpItemsWithLabels]);

  const summaryMiscellaneousItems = useMemo<SummaryItem[]>(
    () =>
      miscellaneousItems.map((item) => {
        const erpItem = item.type == 'erp' ? erpItemsWithLabels.find((e) => e.id.value === item.itemId) : undefined;
        const customMinorPrice = item.type == 'custom' && item.cost !== undefined ? item.cost * 100 : undefined;
        const quantity = item.quantity ?? 0;
        const costPerUnit = erpItem ? getItemMinorPricePerUnit(erpItem) : customMinorPrice;
        const totalCostMinorPrice = costPerUnit !== undefined ? costPerUnit * quantity : undefined;

        return {
          id: uuidv4(),
          name: item.type == 'erp' ? (erpItem?.description ?? '') : (item.name ?? ''),
          erpItem,
          totalCostMinorPrice,
          sources: [
            {
              type: 'miscellaneous',
              quantity,
              customMinorPricePerQuantity: customMinorPrice,
            } as MiscellaneousSource,
          ],
        } satisfies SummaryItem;
      }),
    [miscellaneousItems, erpItemsWithLabels],
  );

  const allSummaryItems = useMemo<SummaryItem[]>(() => {
    const combined = [...summaryItemsFromBundles, ...summaryItemsFromRadiators, ...summaryMiscellaneousItems];

    return mergeSummaryItems(combined);
  }, [summaryItemsFromBundles, summaryItemsFromRadiators, summaryMiscellaneousItems]);
  const costOfAllItems = useMemo(() => {
    return allSummaryItems.reduce((acc, item) => {
      acc += item.totalCostMinorPrice ?? 0;
      return acc;
    }, 0);
  }, [allSummaryItems]);

  const salesPrice = solutionData?.data?.lastQuotedSolution?.presentation?.salesPrice;
  const designedPrice = solutionData?.data?.currentSolution?.presentation?.salesPrice;

  return (
    <Stack gap={3}>
      <StyledAccordion expanded={isExpanded}>
        <AccordionSummary onClick={() => setIsExpanded(!isExpanded)}>
          <Stack direction="row" alignItems="center" gap={2} sx={{ padding: '16px' }}>
            <Chevron
              style={{ flex: '0 0 auto', height: '100%', width: '20px' }}
              transitionDuration="0.2s"
              direction={isExpanded ? 'up' : 'down'}
              height={24}
              width={20}
            />
            <Typography mr={1} variant="headline3">
              Final BoM
            </Typography>
            {salesPrice && (
              <MonetaryHeaderItem
                icon={<QuoteOutlinedIcon />}
                label="heatDesign.billOfMaterials.summary.salesPrice"
                monetaryAmount={salesPrice}
                locale={locale}
                testId="quoted-price"
                showDivider={false}
              />
            )}
            {designedPrice && (
              <MonetaryHeaderItem
                icon={<RulerOutlinedIcon />}
                label="heatDesign.billOfMaterials.summary.designedPrice"
                monetaryAmount={designedPrice}
                locale={locale}
              />
            )}
            <MonetaryHeaderItem
              icon={<MoneyDropOutlinedIcon />}
              label="billOfMaterials.costOfAllItems"
              monetaryAmount={{
                currencyCode: currency,
                minorAmount: costOfAllItems,
              }}
              locale={locale}
              testId="cost-of-all-items"
            />
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <BillOfMaterialsSummaryList summaryItems={allSummaryItems} />
        </AccordionDetails>
      </StyledAccordion>
    </Stack>
  );
}

export const BillOfMaterialsSummary = memo(BillOfMaterialsSummaryComponent);
