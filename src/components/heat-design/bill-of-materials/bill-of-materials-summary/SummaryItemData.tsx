import { memo } from 'react';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { Divider, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { SummaryItemSource } from '../types';

export function SummaryItemDataComponent({ source }: { source: SummaryItemSource }) {
  const { formatMessage } = useIntl();
  const getContent = () => {
    switch (source.type) {
      case 'bundle': {
        const bundleLabel = (
          <Stack direction="row" spacing={2}>
            <Typography color="#fff" sx={{ whiteSpace: 'nowrap' }} variant="body2">
              {source.bundleCollection.title}
            </Typography>
            <Divider sx={{ borderColor: '#fff' }} orientation="vertical" flexItem={true} />
            <Typography fontWeight={500} color="#fff" sx={{ whiteSpace: 'nowrap' }} variant="body2">
              {source.bundle.title}
            </Typography>
          </Stack>
        );
        return (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 2 }}
            mode="pill"
            label={bundleLabel}
            value={`${source.quantity} pc`}
          />
        );
      }
      case 'miscellaneous': {
        return (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 2 }}
            mode="pill"
            label={formatMessage({ id: 'heatDesign.billOfMaterial.summary.miscellaneousAddition' })}
            value={`${source.quantity} pc`}
          />
        );
      }
      case 'radiator': {
        return (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 2 }}
            mode="pill"
            label={
              <Typography color="#fff" variant="body2">
                <FormattedMessage id="heatDesign.billOfMaterials.summary.prefilledFromHeatDesign" />
              </Typography>
            }
            value={`${source.quantity} pc`}
          />
        );
      }
      case 'hlc': {
        return (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 2 }}
            mode="pill"
            label={
              <Typography color="#fff" variant="body2">
                <FormattedMessage id="heatDesign.billOfMaterials.summary.prefilledFromHeatDesign" />
              </Typography>
            }
            value={`${source.quantity} pc`}
          />
        );
      }
      default:
        return source satisfies never;
    }
  };
  return getContent();
}

export const SummaryItemData = memo(SummaryItemDataComponent);
