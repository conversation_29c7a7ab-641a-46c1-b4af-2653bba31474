import { Box, Stack } from '@mui/material';
import { useNextLocale } from 'components/bill-of-materials/hooks/useNextLocale';
import { ErpTableItem } from 'components/bill-of-materials/types';
import { getFormattedItemPricePerUnit } from 'components/bill-of-materials/utils';
import { useIntl } from 'react-intl';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { useErpItems } from 'hooks/useErpItemsWithLabels';

interface DesignedBomBundleItemDetailsPanelProps {
  item: ErpTableItem;
  showCostPerUnit?: boolean;
}

function DesignedBomBundleItemDetailsPanel({ item }: Readonly<DesignedBomBundleItemDetailsPanelProps>) {
  const { formatMessage } = useIntl();
  const nextLocale = useNextLocale();
  const { erpItems } = useErpItems();
  const erpItem = erpItems[item.itemId];

  return (
    <Box sx={{ padding: '8px 16px 16px 16px' }}>
      <Stack direction="row" gap="16px">
        {erpItem?.category && (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 3 }}
            label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.category' })}
            value={erpItem?.category}
          />
        )}
        {erpItem?.supplier && (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 3 }}
            label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.supplier' })}
            value={erpItem?.supplier}
          />
        )}
        {erpItem?.version && (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 3 }}
            label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.version' })}
            value={erpItem?.version}
          />
        )}
        {erpItem?.unit && (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 3 }}
            label={formatMessage({ id: 'billOfMaterials.unit' })}
            value={erpItem?.unit}
          />
        )}
        {erpItem?.quantity && (
          <LabelValueDisplay
            sx={{ width: 'auto', gap: 3 }}
            label={formatMessage({ id: 'heatDesign.billOfMaterials.summary.itemsPerUnit' })}
            value={erpItem?.quantity}
          />
        )}
        <LabelValueDisplay
          sx={{ width: 'auto', gap: 3 }}
          label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.costPerUnit' })}
          value={getFormattedItemPricePerUnit(erpItem, nextLocale)}
        />
      </Stack>
    </Box>
  );
}

export default DesignedBomBundleItemDetailsPanel;
