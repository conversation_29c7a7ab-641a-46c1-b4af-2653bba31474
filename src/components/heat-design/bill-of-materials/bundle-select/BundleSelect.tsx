import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  CircularProgress,
  Divider,
  Stack,
  Typography,
} from '@mui/material';
import MuiAutocomplete from '@mui/material/Autocomplete';
import { api } from 'utils/api';
import { getCountryEnum } from 'utils/marketConfigurations';
import { FormattedMessage } from 'react-intl';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import { IAutocompleteOption } from '@ui/components/shared/types';
import { AutocompleteInputComponent } from '@ui/components/shared/AutocompleteInputComponent';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { grey } from '@ui/theme/colors';
import BundleSelectItemsTable from './BundleSelectItemsTable';
import { styled } from '@mui/system';
import { WarningIcon } from '../../components/WarningIcon';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useBundleCollectionIdsWithErrors } from '../store/selectors/useBundleCollectionIdsWithErrors';
import { useBundleIdsWithErrors } from '../store/selectors/useBundleIdsWithErrors';
import { createDesignedBomBundle, getTemplateBundleCollections } from '../utils';
import { WarningTooltip } from 'components/bill-of-materials/components/common/WarningTooltip';

const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  borderRadius: '16px !important',
  padding: '16px',
  '.MuiAccordionDetails-root': {
    padding: 0,
  },
  '.MuiAccordionSummary-root': {
    padding: 0,
  },
});

function BundleSelectComponent() {
  const [expandedCollections, setExpandedCollections] = useState<Set<string>>(new Set<string>());
  const { countryCode } = useGroundwork();
  const country = getCountryEnum(countryCode);
  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const bundlePerCollection = useBillOfMaterialsDesignStore((s) => s.bundlePerCollection);
  const setBundleForCollection = useBillOfMaterialsDesignStore((s) => s.setBundleForCollection);
  const isReadyForProcurement = useBillOfMaterialsDesignStore((s) => s.isReadyForProcurement);
  const updatedAt = useBillOfMaterialsDesignStore((s) => s.updatedAt);
  const bundleCollectionIdsWithErrors = useBundleCollectionIdsWithErrors();
  const bundleIdsWithErrors = useBundleIdsWithErrors();

  const setIsExpanded = useCallback((bundleCollectionId: string, isExpanded: boolean) => {
    setExpandedCollections((currentlyExpandedCollections) => {
      const newExpandedCollections = new Set(currentlyExpandedCollections);
      if (isExpanded) {
        newExpandedCollections.add(bundleCollectionId);
      } else {
        newExpandedCollections.delete(bundleCollectionId);
      }
      return newExpandedCollections;
    });
  }, []);

  const templateBundleCollections = useMemo(() => {
    return getTemplateBundleCollections(bundleCollectionQuery.data);
  }, [bundleCollectionQuery.data]);

  useEffect(() => {
    if (bundleIdsWithErrors.size > 0) {
      bundleIdsWithErrors.forEach((bundleId) => {
        const collectionId = Object.entries(bundlePerCollection).find(
          ([_, bundle]) => bundle?.bundleId?.value === bundleId,
        )?.[0];
        if (collectionId) {
          setIsExpanded(collectionId, true);
        }
      });
    }
  }, [bundleIdsWithErrors, bundlePerCollection, setIsExpanded]);

  // hashmap of bundle collections to bundles
  const bundleOptionsPerCollection = useMemo(() => {
    return templateBundleCollections.reduce(
      (acc, collection) => {
        if (!collection.id?.value) return acc;
        const bundleInfo = collection.bundles
          .filter((bundle) => bundle.id?.value)
          .map((bundle) => {
            return {
              label: bundle.title,
              value: bundle.id!.value,
            };
          });
        if (!bundleInfo) return acc;
        acc[collection.id.value] = bundleInfo;
        return acc;
      },
      {} as Record<string, IAutocompleteOption<string>[]>,
    );
  }, [templateBundleCollections]);

  return (
    <Stack gap={3}>
      <Typography variant="headline3">
        <FormattedMessage id="billOfMaterials.bundleSelect.title" />
      </Typography>
      <Typography variant="body2">
        <FormattedMessage id="billOfMaterials.bundleSelect.description" />
      </Typography>
      {bundleCollectionQuery.isLoading && (
        <Typography variant="body2">
          <FormattedMessage id="billOfMaterials.bundleSelect.loading" />
        </Typography>
      )}
      {bundleCollectionQuery.isError && (
        <Typography variant="body2">
          <FormattedMessage id="billOfMaterials.bundleSelect.error" />
        </Typography>
      )}
      <Stack gap={2}>
        {bundleCollectionQuery.isPending ? (
          <Stack sx={{ height: '500px' }} alignItems="center" justifyContent="center">
            <CircularProgress size={40} />
          </Stack>
        ) : (
          templateBundleCollections.map((templateBundleCollection) => {
            const collectionId = templateBundleCollection.id!.value;
            const selectedBundle = bundlePerCollection[collectionId];
            const selectedBundleId = selectedBundle?.bundleId?.value;
            const bundleIdOptions = bundleOptionsPerCollection[collectionId] || [];
            const selectedBundleOption = bundleIdOptions.find((option) => option.value === selectedBundleId) || null;
            const isExpanded = expandedCollections.has(collectionId);
            const selectedTemplateBundle = templateBundleCollection.bundles.find(
              (bundle) => bundle.id?.value === selectedBundleId,
            );
            const selectedBundleIsOutdated =
              isReadyForProcurement &&
              selectedTemplateBundle?.updatedAt &&
              updatedAt &&
              selectedTemplateBundle?.updatedAt?.getTime() > updatedAt.getTime();

            return (
              <Stack key={collectionId} gap={1}>
                <MuiAutocomplete
                  id={`bundle-select-${collectionId}`}
                  options={bundleIdOptions}
                  value={selectedBundleOption}
                  disablePortal
                  onChange={(_, option) => {
                    setBundleForCollection(
                      collectionId,
                      createDesignedBomBundle(templateBundleCollections, collectionId, option?.value),
                    );
                  }}
                  renderInput={(params) => (
                    <AutocompleteInputComponent
                      params={params}
                      error={
                        !!templateBundleCollection.id?.value &&
                        bundleCollectionIdsWithErrors.has(templateBundleCollection.id.value)
                      }
                      label={
                        <Stack direction="row" alignItems="center" gap={1}>
                          {selectedBundleIsOutdated && (
                            <WarningTooltip
                              title={<FormattedMessage id="billOfMaterials.bundleSelect.outdatedBundle" />}
                            />
                          )}
                          {templateBundleCollection.title}
                        </Stack>
                      }
                      placeholder="Select a bundle"
                      selectedValue={selectedBundleOption}
                    />
                  )}
                  renderOption={(props, option) => {
                    const bundle = templateBundleCollection.bundles.find((b) => b.id?.value === option.value);
                    return (
                      <Box component="li" {...props} gap={1} key={option.value}>
                        <Typography variant="body1Emphasis">{option.label}</Typography>
                        <Divider orientation="vertical" variant="middle" flexItem sx={{ borderColor: grey[500] }} />
                        <Typography variant="body2" sx={{ color: 'grey.500' }}>
                          {bundle?.description}
                        </Typography>
                      </Box>
                    );
                  }}
                />
                <Typography variant="body2">{templateBundleCollection.description}</Typography>
                {selectedBundle && (
                  <StyledAccordion key={collectionId} expanded={isExpanded}>
                    <AccordionSummary onClick={() => setIsExpanded(collectionId, !isExpanded)} sx={{ marginBottom: 1 }}>
                      <Stack direction="row" alignItems="center" gap={2}>
                        <Chevron
                          style={{ flex: '0 0 auto', height: '100%', width: '20px' }}
                          transitionDuration="0.2s"
                          direction={isExpanded ? 'up' : 'down'}
                          height={24}
                          width={20}
                        />
                        {selectedBundleId && bundleIdsWithErrors.has(selectedBundleId) && (
                          <WarningIcon
                            x={2.5}
                            y={2.5}
                            iconWidth={25}
                            iconHeight={25}
                            canvasWidth={25}
                            canvasHeight={25}
                          />
                        )}
                        <Typography variant="body1Emphasis">
                          <FormattedMessage id="billOfMaterialsDesignPage.Items" />
                        </Typography>
                      </Stack>
                    </AccordionSummary>
                    <AccordionDetails>
                      <BundleSelectItemsTable bundle={selectedBundle} />
                    </AccordionDetails>
                  </StyledAccordion>
                )}
              </Stack>
            );
          })
        )}
      </Stack>
    </Stack>
  );
}

export const BundleSelect = memo(BundleSelectComponent);
