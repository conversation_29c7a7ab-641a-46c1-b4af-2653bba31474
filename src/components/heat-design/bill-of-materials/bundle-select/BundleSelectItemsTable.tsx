import { ItemsTable } from 'components/bill-of-materials/components/common/ItemsTable';
import { ErpTableItem, DesignedBomBundle } from 'components/bill-of-materials/types';
import { useMemo } from 'react';
import { useBillOfMaterialsDesignStore } from '../store/BillOfMaterialsContext';
import DesignedBomBundleItemDetailsPanel from './DesignedBomBundleItemDetailsPanel';
import { useBundleItemErrors } from '../store/selectors/useBundleItemErrors';
import { isNotNullish } from '../../../../utils/isNotNullish';
import { useErpItems } from 'hooks/useErpItemsWithLabels';
import { api } from 'utils/api';
import { useCountry } from 'hooks/useCountry';
import { getTemplateBundleCollections } from '../utils';

export type BundleSelectItemsTableProps = {
  bundle: DesignedBomBundle;
};

export default function BundleSelectItemsTable({ bundle }: Readonly<BundleSelectItemsTableProps>) {
  const { erpItems } = useErpItems();
  const updateBundleItemQuantity = useBillOfMaterialsDesignStore((s) => s.updateBundleItemQuantity);
  const collectionId = bundle.bundleCollectionId?.value;
  const bundleId = bundle.bundleId?.value;
  const bundleItemErrors = useBundleItemErrors();
  const itemErrorsForBundle = collectionId && bundleId ? bundleItemErrors[collectionId]?.[bundleId] : {};
  const country = useCountry();
  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const templateBundleCollections = useMemo(() => {
    return getTemplateBundleCollections(bundleCollectionQuery.data);
  }, [bundleCollectionQuery.data]);
  const items = useMemo<ErpTableItem[]>(() => {
    const templateBundle = templateBundleCollections
      .find((c) => c.id?.value === collectionId)
      ?.bundles.find((b) => b.id?.value === bundleId);
    return bundle.items
      .map((item) => {
        const itemId = item.itemId?.value;
        if (!itemId || !erpItems[itemId]) return null;
        return {
          type: 'erp',
          itemId: itemId,
          quantity: item.quantity,
          instructions: templateBundle?.items.find((i) => i.itemId?.value === itemId)?.instructions,
        } satisfies ErpTableItem;
      })
      .filter(isNotNullish);
  }, [bundle.items, erpItems, bundleId, collectionId, templateBundleCollections]);

  const handleQuantityChange = (itemId: string, newQuantity?: number) => {
    if (!collectionId) return;
    updateBundleItemQuantity(collectionId, itemId, newQuantity);
  };

  return (
    <ItemsTable
      items={items}
      onQuantityChange={handleQuantityChange}
      errors={itemErrorsForBundle}
      showCostPerUnit
      showInstructionsRow
      renderDetailsPanel={(item) => {
        if (item.type === 'erp') {
          return <DesignedBomBundleItemDetailsPanel item={item} />;
        }
        return null;
      }}
      tableBackgroundColor="#fff"
    />
  );
}
