import { MiscellaneousItem, DesignedBomBundleItem } from 'components/bill-of-materials/types';

// Helper types
type BundleCollectionId = string;
type BundleId = string;
type ItemId = string;

export enum BundleItemError {
  QUANTITY = 'quantity',
}

export type BundleItemValidation = Record<BundleItemError, (item: DesignedBomBundleItem) => boolean>;

export const bundleItemValidation: BundleItemValidation = {
  [BundleItemError.QUANTITY]: (item) => item.quantity !== undefined && item.quantity >= 0,
} as const;

export type BundleErrorMap = Record<BundleCollectionId, Record<BundleId, Record<ItemId, BundleItemError[]>>>;

export enum MiscellaneousItemError {
  QUANTITY = 'quantity',
  NAME = 'name',
}
export type MiscellaneousItemValidation = Record<MiscellaneousItemError, (item: MiscellaneousItem) => boolean>;
export const miscellaneousItemValidation: MiscellaneousItemValidation = {
  [MiscellaneousItemError.QUANTITY]: (item) => item.quantity !== undefined && item.quantity > 0,
  [MiscellaneousItemError.NAME]: (item) => item.type !== 'custom' || (!!item.name && item.name.trim().length > 0),
} as const;

export type MiscellaneousItemsErrorMap = Record<string, MiscellaneousItemError[]>;

export type ItemError = BundleItemError | MiscellaneousItemError;
