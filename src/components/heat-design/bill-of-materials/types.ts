import { Item } from '../../../components/bill-of-materials/types';
import {
  Bundle,
  BundleCollection,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { RadiatorData } from '../stores/types';
import { Radiator } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';

export type BundleSource = {
  type: 'bundle';
  quantity: number;
  bundle: Bundle;
  bundleCollection: BundleCollection;
};

export type MiscellaneousSource = {
  type: 'miscellaneous';
  quantity: number;
  customMinorPricePerQuantity?: number;
};

export type HlcSource = {
  type: 'hlc';
  quantity: number;
  hlcItemDisplayName: string;
};

export type RadiatorSource = {
  type: 'radiator';
  quantity: number;
  radiator: RadiatorData;
  catalogueRadiator?: Radiator;
};

export type SummaryItemSource = BundleSource | MiscellaneousSource | HlcSource | RadiatorSource;

export type SummaryItem = {
  sources: SummaryItemSource[];
  id: string;
  name: string;
  erpItem?: Item;
  totalCostMinorPrice?: number;
};
