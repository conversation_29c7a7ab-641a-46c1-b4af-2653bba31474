import { Box, Stack, Typography } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { BillOfMaterialsModals } from 'components/bill-of-materials/BillOfMaterialsModals';
import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useEffect, useMemo } from 'react';
import toast from 'react-hot-toast';
import { FormattedMessage, useIntl } from 'react-intl';
import { DesignedBomBundle } from 'server/api/routers/billOfMaterials';
import { api } from 'utils/api';
import { useBillOfMaterialsDesignStore } from './store/BillOfMaterialsContext';
import { BundleSelect } from './bundle-select/BundleSelect';
import { HeatDesignSummary } from './heat-design-summary/HeatDesignSummary';
import { MiscellaneousItems } from './miscellaneous-items/MiscellaneousItems';
import { BillOfMaterialsSummary } from './bill-of-materials-summary/BillOfMaterialsSummary';
import { useCountry } from '../../../hooks/useCountry';
import { Button } from '@ui/components/Button/Button';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { useGeneralError } from './store/selectors/useGeneralError';
import { useInvalidateBillOfMaterials } from './useBillOfMaterials';
import { getLocaleFromCountry } from 'utils/marketConfigurations';
import { useErpItemsWithLabels } from '../../../hooks/useErpItemsWithLabels';
import { DesignedBomStatus } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { createDesignedBomBundle, getTemplateBundleCollections } from './utils';

function ReadyForProcurementPill() {
  return (
    <Box
      sx={{
        color: 'black',
        padding: '5px',
        margin: '0 10px',
        px: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: grey[200],
        height: 'auto',
        gap: 1,
        borderRadius: 1,
      }}
    >
      <CheckIconThin />
      <Typography variant="body1Emphasis">
        <FormattedMessage id="billOfMaterialsDesignPage.status.readyForProcurement" />
      </Typography>
    </Box>
  );
}

export function BillOfMaterialsDesign() {
  const intl = useIntl();
  const { groundwork } = useGroundwork();
  const country = useCountry();
  const energySolutionId = useEnergySolutionId();
  const invalidateBillOfMaterials = useInvalidateBillOfMaterials();
  const locale = getLocaleFromCountry(country);

  const setBundleCollections = useBillOfMaterialsDesignStore((s) => s.setBundleCollections);
  const bundlesPerCollection = useBillOfMaterialsDesignStore((s) => s.bundlePerCollection);
  const miscellaneousItems = useBillOfMaterialsDesignStore((s) => s.miscellaneousItems);
  const setMiscellaneousItems = useBillOfMaterialsDesignStore((s) => s.setMiscellaneousItems);
  const setBundleForCollection = useBillOfMaterialsDesignStore((s) => s.setBundleForCollection);
  const hasError = useGeneralError();
  const isReadyForProcurement = useBillOfMaterialsDesignStore((s) => s.isReadyForProcurement);
  const setIsReadyForProcurement = useBillOfMaterialsDesignStore((s) => s.setIsReadyForProcurement);
  const billOfMaterialsUpdateTimestamp = useBillOfMaterialsDesignStore((s) => s.updatedAt);
  const setBillOfMaterialsUpdateTimestamp = useBillOfMaterialsDesignStore((s) => s.setUpdatedAt);
  const existingDesignedBomQuery = api.BillOfMaterials.loadDesignedBom.useQuery(
    {
      installationGroundworkId: groundwork.id!.value,
      energySolutionId: energySolutionId!,
    },
    {
      enabled: !!groundwork.id?.value && !!energySolutionId,
    },
  );

  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const saveDesignedBom = api.BillOfMaterials.saveDesignedBom.useMutation();
  const markAsReady = api.BillOfMaterials.markDesignedBomAsReady.useMutation();
  const unmarkAsReady = api.BillOfMaterials.unmarkDesignedBomAsReady.useMutation();
  const { isLoading: isErpItemsLoading } = useErpItemsWithLabels();

  const templateBundleCollections = useMemo(() => {
    return getTemplateBundleCollections(bundleCollectionQuery.data);
  }, [bundleCollectionQuery.data]);

  useEffect(() => {
    setBundleCollections(bundleCollectionQuery?.data?.bundleCollections ?? []);
  }, [bundleCollectionQuery?.data, setBundleCollections]);
  useEffect(() => {
    setIsReadyForProcurement(
      existingDesignedBomQuery.data?.status === DesignedBomStatus.DESIGNED_BOM_STATUS_READY_FOR_PROCUREMENT,
    );
    setBillOfMaterialsUpdateTimestamp(existingDesignedBomQuery.data?.updatedAt ?? undefined);
  }, [existingDesignedBomQuery.data, setIsReadyForProcurement, setBillOfMaterialsUpdateTimestamp]);
  useEffect(() => {
    // If ready for procurment, load existing bundles without modification.
    if (isReadyForProcurement) {
      existingDesignedBomQuery.data?.bundles.forEach((bundle) => {
        if (bundle.bundleCollectionId?.value) {
          setBundleForCollection(bundle.bundleCollectionId.value, bundle);
        }
      });
    } else {
      // Load template bundle and update with existing quantity overrides
      existingDesignedBomQuery.data?.bundles.forEach((bundle) => {
        if (bundle.bundleCollectionId?.value && bundle.bundleId?.value) {
          const templateBundle = createDesignedBomBundle(
            templateBundleCollections,
            bundle.bundleCollectionId?.value,
            bundle.bundleId.value,
          );
          const updatedBundleItems =
            templateBundle?.items.map((item) => {
              const existingItem = bundle.items.find((i) => i.itemId?.value === item.itemId?.value);
              if (existingItem) {
                return {
                  ...item,
                  quantity: existingItem.quantity,
                };
              }
              return item;
            }) ?? [];
          setBundleForCollection(bundle.bundleCollectionId.value, {
            ...bundle,
            items: updatedBundleItems,
          });
        }
      });
    }
  }, [existingDesignedBomQuery.data, setBundleForCollection, templateBundleCollections, isReadyForProcurement]);

  useEffect(() => {
    // Wait until the ERP items store has been populated, then set the miscellaneous items
    if (!isErpItemsLoading) {
      setMiscellaneousItems(existingDesignedBomQuery.data?.miscellaneousItems ?? []);
    }
  }, [existingDesignedBomQuery.data, isErpItemsLoading, setMiscellaneousItems]);

  const onSave = async () => {
    if (!energySolutionId || !groundwork?.id?.value || !bundlesPerCollection) {
      return;
    }
    await toast.promise(
      saveDesignedBom.mutateAsync({
        energySolutionId: energySolutionId,
        installationGroundworkId: groundwork.id.value,
        bundles: Object.entries(bundlesPerCollection)
          .flatMap(([collectionId, bundle]) => {
            if (!bundle || !bundle.bundleId?.value) {
              return [];
            }

            return [
              {
                bundleId: bundle.bundleId.value,
                bundleCollectionId: collectionId,
                items: bundle.items.map((item) => {
                  return {
                    itemId: item.itemId?.value ?? '',
                    quantity: item.quantity ?? 0, // FIXME: This will error out until we validate before saving
                  };
                }),
              },
            ];
          })
          .filter((bundle): bundle is DesignedBomBundle => !!bundle),
        miscellaneousItems: miscellaneousItems,
      }),
      {
        loading: intl.formatMessage({ id: 'billOfMaterialsDesignPage.notification.saving' }),
        success: intl.formatMessage({ id: 'billOfMaterialsDesignPage.notification.saving.success' }),
        error: intl.formatMessage({ id: 'billOfMaterialsDesignPage.notification.saving.error' }),
      },
    );
  };

  const onMarkAsReady = async () => {
    if (!groundwork.id?.value || !energySolutionId) {
      return;
    }

    await toast.promise(
      markAsReady.mutateAsync({
        energySolutionId: energySolutionId,
        installationGroundworkId: groundwork.id.value,
      }),
      {
        loading: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.markingAsReady',
        }),
        success: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.markingAsReady.success',
        }),
        error: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.markingAsReady.error',
        }),
      },
    );

    invalidateBillOfMaterials();
  };

  const onUnmarkAsReady = async () => {
    if (!groundwork.id?.value || !energySolutionId) {
      return;
    }

    await toast.promise(
      unmarkAsReady.mutateAsync({
        energySolutionId: energySolutionId,
        installationGroundworkId: groundwork.id.value,
      }),
      {
        loading: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.unmarkingAsReady',
        }),
        success: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.unmarkingAsReady.success',
        }),
        error: intl.formatMessage({
          id: 'billOfMaterialsDesignPage.notification.unmarkingAsReady.error',
        }),
      },
    );

    setIsReadyForProcurement(false);
  };

  return (
    <Stack gap={2}>
      <Stack direction="row" gap={1}>
        <Typography variant="headline2">
          <FormattedMessage id="billOfMaterialsDesignPage.title" />
        </Typography>
        {isReadyForProcurement && <ReadyForProcurementPill />}
      </Stack>
      <Stack sx={{ borderRadius: '24px', padding: '24px', backgroundColor: beige[100] }} gap={3}>
        <HeatDesignSummary />
        <BundleSelect />
        <MiscellaneousItems />
        <BillOfMaterialsSummary />
        {isReadyForProcurement ? (
          <Stack direction="column" gap={2} alignItems="center" justifyContent="center">
            {billOfMaterialsUpdateTimestamp && (
              <Typography variant="body1" color={grey[900]}>
                <FormattedMessage
                  id="billOfMaterialsDesignPage.readyForProcurement"
                  values={{
                    timestamp: billOfMaterialsUpdateTimestamp?.toLocaleString(locale, {
                      timeZoneName: 'short',
                    }),
                  }}
                />
              </Typography>
            )}

            <Button
              variant="outlined"
              fullWidth
              onClick={async () => {
                await onUnmarkAsReady();
              }}
              sx={{
                color: grey[900],
                gap: '8px',
                alignItems: 'center',
                alignSelf: 'center',
                width: '50%',
              }}
            >
              <FormattedMessage id="billOfMaterialsDesignPage.unmarkAsReady" />
            </Button>
          </Stack>
        ) : (
          <Stack direction="row" gap={2} alignItems="center" justifyContent="space-between">
            <Button fullWidth variant="outlined" onClick={() => onSave()}>
              <FormattedMessage id="common.label.save" />
            </Button>

            <Button
              variant="contained"
              color="yellow"
              startIcon={<CheckIconThin />}
              fullWidth
              onClick={async () => {
                await onSave();
                await onMarkAsReady();
              }}
              disabled={hasError}
            >
              <FormattedMessage id="billOfMaterialsDesignPage.saveAndMarkAsReady" />
            </Button>
          </Stack>
        )}
      </Stack>
      <BillOfMaterialsModals />
    </Stack>
  );
}
