import { BundleCollection } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { Room, RadiatorData } from '../stores/types';
import {
  DesignedBomBundle,
  DesignedBomBundleItem,
  TEMPLATE_IDENTIFIER,
  TemplateBundleCollection,
} from 'components/bill-of-materials/types';
/**
 * Returns sorted TemplateBundleCollections from bundleCollections data
 */
export function getTemplateBundleCollections(bundleCollectionsData?: {
  bundleCollections: BundleCollection[];
}): TemplateBundleCollection[] {
  if (!bundleCollectionsData) return [];

  const collections = bundleCollectionsData.bundleCollections.filter(
    (collection): collection is TemplateBundleCollection => collection.details?.details?.$case === TEMPLATE_IDENTIFIER,
  );

  return collections.toSorted((a, b) => {
    const aId = a.id?.value;
    const bId = b.id?.value;
    if (!aId || !bId) return 0;
    const aOrder = a.details.details.template.displayOrder;
    const bOrder = b.details.details.template.displayOrder;
    return aOrder - bOrder;
  });
}

/**
 * Checks if any bundle in the designed BOM is outdated compared to the corresponding template bundles.
 * A bundle is considered outdated if its corresponding template bundle has been updated after the BOM date.
 */
export function isAnyDesignedBomBundleOutdated(
  designedBomData?: { bundles: DesignedBomBundle[]; updatedAt?: Date },
  bundleCollectionData?: { bundleCollections: BundleCollection[] },
): boolean {
  if (!designedBomData?.updatedAt || !bundleCollectionData) return false;

  const templateBundleCollections = getTemplateBundleCollections(bundleCollectionData);
  const templateBundles = templateBundleCollections.flatMap((collection) => collection.bundles);
  const bomDate = designedBomData.updatedAt;

  return designedBomData.bundles.some((bundle) => {
    const correspondingTemplateBundle = templateBundles.find(
      (templateBundle) => templateBundle.id?.value === bundle.bundleId?.value,
    );
    return (
      correspondingTemplateBundle?.updatedAt && correspondingTemplateBundle.updatedAt.getTime() > bomDate.getTime()
    );
  });
}

/**
 * Create a DesignedBomBundle from collections and bundle ids
 */
export function createDesignedBomBundle(
  templateBundleCollections: TemplateBundleCollection[],
  collectionId: string,
  bundleId?: string,
): DesignedBomBundle | undefined {
  const collection = templateBundleCollections.find((c) => c.id?.value === collectionId);
  if (!collection) return undefined;

  const bundle = collection.bundles.find((b) => b.id?.value === bundleId);
  if (!bundle) return undefined;
  const items = bundle.items.map((item) => {
    return {
      itemId: item.itemId,
      quantity: item.defaultQuantity,
    } satisfies DesignedBomBundleItem;
  });

  return {
    bundleId: bundle.id,
    bundleCollectionId: { value: collectionId },
    items,
  } satisfies DesignedBomBundle;
}

/**
 * Returns all radiators that are enabled and not marked as existing, across all rooms.
 * @param rooms Array of Room objects
 */
export function getEnabledNewRadiators(rooms: Room[]): RadiatorData[] {
  return rooms.flatMap((room) => room.radiators.filter((radiator) => radiator.enabled && !radiator.isExisting));
}
