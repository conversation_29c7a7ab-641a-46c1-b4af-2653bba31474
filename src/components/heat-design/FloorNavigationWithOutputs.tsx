import { Divider, Stack, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { FloorProps } from './stores/types';
import FloorOverviewButton from './room-components/FloorOverviewButton';
import { formatSquareMeters, formatWatts } from '../../utils/helpers';
import { useFloors } from './stores/FloorsStore';
import { useDwellingHeatDesignResult } from './hooks/useDwellingHeatDesignResult';
import { RoomHeatDesignResult } from './stores/OutputsStore';
import { TextPill } from '@ui/components/TextPill/TextPill';
import { green, red } from '@ui/theme/colors';
import { useGetRoomsByFloor } from './stores/RoomsStore';
import { useConstructionYear } from './stores/HouseInputsStore';
import { useClimateDataStore } from './stores/ClimateDataStore';
import { useGroundwork } from '../../context/groundwork-context';
import { useFlowReturnDeltaT, useFlowTemp } from './stores/HeatSourceStore';
import { calculateNetHeatOutputByFloor } from './utils/radiatorHelpers';
import { useIntl } from 'react-intl';

const getFloorArea = (rooms: RoomHeatDesignResult[]) => rooms.reduce((acc, room) => room.totalFloorArea + acc, 0);

const getHeatLossWatts = (rooms: RoomHeatDesignResult[]) =>
  rooms.reduce((acc, room) => room.totalRoom.heatLoss + acc, 0);

export function FloorNavigationWithOutputs({
  selectedFloor,
  selectFloor,
  displayFloorArea,
  displayFloorWattage,
  validator,
}: {
  selectedFloor: FloorProps | undefined;
  selectFloor: (floor: FloorProps) => void;
  displayFloorArea?: boolean;
  displayFloorWattage?: boolean;
  validator?: (floor: FloorProps) => boolean;
}) {
  const { locale } = useRouter();
  const roomsByFloor = useGetRoomsByFloor();
  const constructionYear = useConstructionYear();
  const climateDataStore = useClimateDataStore();
  const { countryCode } = useGroundwork();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const flowTemperature = useFlowTemp();
  const floors = useFloors();
  const { floorsResults } = useDwellingHeatDesignResult();

  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);

  const heatOutputByFloor = calculateNetHeatOutputByFloor({
    floors,
    roomsByFloor,
    roomHeatDesignResults,
    constructionYear,
    countryCode,
    flowReturnDeltaT,
    flowTemperature,
    climateDataStore,
  });
  const dwellingNetOutput = Math.round(Object.values(heatOutputByFloor).reduce((acc, cur) => acc + cur, 0));
  const intl = useIntl();

  const getAreaForFloor = (floorId: string) =>
    getFloorArea(roomHeatDesignResults.filter((rhdr) => rhdr.floorId === floorId));

  const getHeatLossForFloorWatts = (floorId: string) =>
    getHeatLossWatts(roomHeatDesignResults.filter((rhdr) => rhdr.floorId === floorId));

  return (
    <Stack direction="row" sx={{ gap: 2, mb: 2 }}>
      {floors.map((floor) => {
        const isValid = validator ? validator(floor) : true;
        const floorNetOutOutput = Math.round(heatOutputByFloor[floor.floorName] ?? 0);

        return (
          <Stack direction="row" key={floor.uid} data-testid={`floor-navigation-wrapper-${floor.floorName}`}>
            <Stack gap={1} justifyContent="center" alignItems="center">
              <FloorOverviewButton
                isValid={isValid}
                floor={floor}
                isSelected={floor.uid === selectedFloor?.uid}
                selectFloor={() => selectFloor(floor)}
              >
                {(displayFloorArea || displayFloorWattage) && (
                  <>
                    {displayFloorArea && (
                      <Typography
                        data-testid={`${floor.floorName}-area-label`}
                        component="p"
                        variant="body1Emphasis"
                        sx={{ color: 'inherit' }}
                      >
                        {formatSquareMeters(getAreaForFloor(floor.uid), 2)}
                      </Typography>
                    )}
                    {displayFloorWattage && (
                      <Typography
                        data-testid={`${floor.floorName}-wattage-label`}
                        component="p"
                        variant="body1Emphasis"
                        sx={{ color: 'inherit' }}
                      >
                        {formatWatts(getHeatLossForFloorWatts(floor.uid), locale, 0, true)}
                      </Typography>
                    )}
                  </>
                )}
              </FloorOverviewButton>
              <Stack direction="row" justifyContent="space-between" my="5px">
                <TextPill
                  text={`${floorNetOutOutput > 0 ? '+' : ''}${floorNetOutOutput} W`}
                  backgroundColour={floorNetOutOutput >= 0 ? green[800] : red[800]}
                />
              </Stack>
            </Stack>
          </Stack>
        );
      })}
      <Stack alignItems="center" justifyContent="center" gap={1} data-testid="house-total-output-wrapper">
        <Typography variant="body1Emphasis">
          {intl.formatMessage({ id: 'heatDesign.radiator.heatOutput.totals.title' })}
        </Typography>
        <Divider flexItem={true} />
        <TextPill
          key="output-total-sum"
          text={`${dwellingNetOutput > 0 ? '+' : ''}${dwellingNetOutput} W`}
          backgroundColour={dwellingNetOutput >= 0 ? green[800] : red[800]}
        />
      </Stack>
    </Stack>
  );
}
