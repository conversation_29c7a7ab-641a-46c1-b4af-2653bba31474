import { Al<PERSON>, Box, CircularProgress, Divider, List, ListItem, TextField } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { api } from 'utils/api';
import { Stack } from '@mui/system';
import { ProjectAddress } from '@aira/magicplan-grpc-api/build/ts_out/index.com.aira.magicplan.contract.v1';
import { useState } from 'react';
import { grey } from '@ui/theme/colors';

interface Props {
  onSelectPlan: (planId: string, projectId: string, referenceName: string) => void;
}

function formattedAddress(address: ProjectAddress): string {
  return `${address.street}, ${address.streetNumber}, ${address.city}, ${address.postalCode}, ${address.country}`;
}

export default function ProjectSelector({ onSelectPlan }: Props) {
  const [email, setEmail] = useState<string | undefined>(undefined);
  const { isFetching, refetch, data, error } = api.HeatLossCalculator.listMagicplanProjects.useQuery({
    email,
    page: 1,
  });

  return (
    <>
      <strong>WARNING:</strong> This is an experimental developer feature to set the Magicplan project that a certain
      Heat Design is based on. You can filter the projects by e-mail address in the text field below. By pressing
      &quot;Select&quot; it will immediately attempt to rewire the project. Use with caution.
      {isFetching && <CircularProgress />}
      {error && (
        <>
          <Alert severity="error">{error.message}</Alert>
          <Button onClick={() => refetch()}>Retry</Button>
        </>
      )}
      <TextField label="E-mail" onBlur={(e) => setEmail(e.currentTarget.value || undefined)} />
      {data && (
        <List>
          {data.projects.map((plan) => (
            <ListItem
              key={plan.id}
              disablePadding
              sx={{
                '&:hover': { backgroundColor: grey[200] },
              }}
            >
              <Stack direction="row" justifyContent="space-between" width="100%" alignItems="center" pr={2}>
                <Stack direction="row" gap={1}>
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  {plan.thumbnailUrl && <img src={plan.thumbnailUrl} alt={plan.name} width={100} height={100} />}

                  <Stack direction="column">
                    <Box>
                      <a href={plan.projectUrl}>
                        <strong>{plan.name}</strong>
                      </a>
                    </Box>
                    <Box>{plan.address && formattedAddress(plan.address)}</Box>
                  </Stack>
                </Stack>
                <Button onClick={() => onSelectPlan(plan.id, plan.projectId, plan.name)}>Select</Button>
              </Stack>
              <Divider />
            </ListItem>
          ))}
        </List>
      )}
    </>
  );
}
