import { Groundwork } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/api/gateway/groundwork/v1/model';
import { CircularProgress, IconButton, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { ContentCopyOutlined } from '@ui/components/Icons/material';
import { Modal } from '@ui/components/Modal/Modal';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import ProjectSelector from './ProjectSelector';
import { SurveyDataDevelopment } from './SurveyDataDevelopment';

export default function HeatDesignDevelopmentPage({
  groundwork,
  isLocal,
}: {
  groundwork: Groundwork;
  isLocal: boolean;
}) {
  const [message, setMessage] = useState('');
  const [isPlanSelectorOpen, setIsPlanSelectorOpen] = useState(false);
  const { formatMessage } = useIntl();

  const energySolutionId = useEnergySolutionId();
  const installationGroundworkId = groundwork.id?.value;

  const generateSubsidyDocumentMutation = api.InstallationGroundwork.generateSubsidyDocument.useMutation();

  const { mutate: storeHeatDesignSnapshot, isPending: isStoringSnapshot } =
    api.HeatLossCalculator.storeHousedataSnapshot.useMutation({
      onMutate: () => {
        setMessage('Storing file in file system...');
      },
      onSuccess() {
        setMessage(`File stored in heat-design-${installationGroundworkId}.json`);
      },
      onError() {
        setMessage('Error storing file');
      },
    });
  const { mutate: resetToMagicplan, isPending: resettingProject } = api.HeatLossCalculator.resetHeatDesign.useMutation({
    onMutate() {
      setMessage('Resetting heat design project to Magicplan data...');
    },
    onSuccess() {
      setMessage('Project reset to Magicplan data');
    },
    onError() {
      setMessage('Error resetting project to Magicplan data');
    },
  });
  const { mutate: setPlanForSurvey, isPending: settingPlanForSurvey } =
    api.HeatLossCalculator.setMagicplanProjectForSurvey.useMutation({
      onSuccess() {
        setIsPlanSelectorOpen(false);
        toast.success('Plan set for survey');
      },
      onError() {
        toast.error('Error setting plan for survey');
      },
    });
  const { mutate: triggerCustomExport, isPending: triggeringCustomExport } =
    api.HeatLossCalculator.triggerCustomExport.useMutation({
      onSuccess() {
        toast.success('Custom export triggered');
      },
      onError() {
        toast.error('Error triggering custom export');
      },
    });
  if (!installationGroundworkId || !energySolutionId) {
    return null;
  }
  const storeSnapshot = () => {
    storeHeatDesignSnapshot({ energySolutionId, installationGroundworkId });
  };
  const resetProject = () => {
    resetToMagicplan({ energySolutionId, installationGroundworkId });
  };

  const generateSubsidyDocument = () => {
    if (!installationGroundworkId) {
      toast.error('Installation groundwork ID is missing');
      return;
    }
    generateSubsidyDocumentMutation
      .mutateAsync({
        id: installationGroundworkId,
      })
      .then(() => {
        toast.success('Subsidy document generated');
      })
      .catch(() => {
        toast.error('Error generating subsidy document');
      });
  };

  return (
    <Stack gap={1} mt={2} sx={{ p: 2 }}>
      <Heading variant="headline3" level={1}>
        Heat design development tools
      </Heading>
      <em>These tools should only be used by developers.</em>

      <Stack direction="row" gap={2} alignItems="center">
        <Typography>Installation groundwork ID: {installationGroundworkId}</Typography>
        <IconButton
          onClick={() => {
            navigator.clipboard.writeText(installationGroundworkId);
            toast.success(formatMessage({ id: 'common.notify.copySuccess' }));
          }}
        >
          <ContentCopyOutlined />
        </IconButton>
      </Stack>

      {isLocal && (
        <Button onClick={storeSnapshot} disabled={isStoringSnapshot} style={{ width: '25vw' }}>
          {isStoringSnapshot ? <CircularProgress /> : <span>Dump data to file</span>}
        </Button>
      )}
      <Button onClick={resetProject} disabled={resettingProject} color="error" style={{ width: '25vw' }}>
        {resettingProject ? <CircularProgress /> : <FormattedMessage id="heatDesign.development.resetProject" />}
      </Button>
      <Button onClick={() => setIsPlanSelectorOpen(true)} style={{ width: '25vw' }}>
        Select plan
      </Button>
      <Stack>
        <Button
          onClick={() => triggerCustomExport({ energySolutionId, installationGroundworkId })}
          disabled={triggeringCustomExport}
          style={{ width: '25vw' }}
        >
          {triggeringCustomExport ? <CircularProgress /> : <span>Trigger custom export</span>}
        </Button>
        <Typography variant="body3" style={{ width: '25vw' }}>
          Note: if you wish to then reset the project, you should wait a few minutes after clicking this trigger custom
          export button before doing that
        </Typography>
      </Stack>
      <Button
        onClick={generateSubsidyDocument}
        disabled={generateSubsidyDocumentMutation.isPending}
        color="primary"
        style={{ width: '25vw' }}
      >
        {generateSubsidyDocumentMutation.isPending ? <CircularProgress /> : <span>Generate subsidy document</span>}
      </Button>
      <Modal height="95%" width="95%" isModalOpen={isPlanSelectorOpen} handleClose={() => setIsPlanSelectorOpen(false)}>
        {settingPlanForSurvey ? (
          <CircularProgress />
        ) : (
          <ProjectSelector
            onSelectPlan={(magicplanPlanId, magicplanProjectId, referenceName) =>
              setPlanForSurvey({
                energySolutionId,
                installationGroundworkId,
                magicplanPlanId,
                magicplanProjectId,
                referenceName,
              })
            }
          />
        )}
      </Modal>

      <div>{message}</div>
      <hr style={{ width: '100%' }} />
      <SurveyDataDevelopment energySolutionId={energySolutionId} installationGroundworkId={installationGroundworkId} />
    </Stack>
  );
}
