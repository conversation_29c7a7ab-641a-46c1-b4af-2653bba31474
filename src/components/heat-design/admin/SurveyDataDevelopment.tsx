import { Paper } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { api } from 'utils/api';

export function SurveyDataDevelopment({
  installationGroundworkId,
  energySolutionId,
}: {
  installationGroundworkId: string;
  energySolutionId: string;
}) {
  const q = api.HeatLossCalculator.fetchSurveyData.useQuery({ installationGroundworkId, energySolutionId });
  return (
    <Paper sx={{ p: 2 }}>
      <Heading level={3} variant="headline3">
        Survey data
      </Heading>
      {q.isFetching && 'Fetching...'}
      {q.isError && `Error fetching survey data: ${q.error}`}
      {q.data && <pre style={{ whiteSpace: 'break-spaces' }}>{JSON.stringify(q.data, null, 2)}</pre>}
    </Paper>
  );
}
