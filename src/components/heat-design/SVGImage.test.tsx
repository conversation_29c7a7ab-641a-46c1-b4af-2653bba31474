import { render, screen } from '@testing-library/react';
import { SVGImage } from './SVGImage';

describe('SVGImage', () => {
  it('should render the image with correct attributes', () => {
    const svgData = `<svg style="fill: black;"><text x='10' y='20'>Hello</text></svg>`;
    render(<SVGImage svgData={svgData} alt="Test SVG" />);

    const imgElement = screen.getByRole('img');
    expect(imgElement).toBeInTheDocument();
    expect(imgElement).toHaveAttribute('alt', 'Test SVG');
  });

  it('should keep other styles intact while removing text-shadow', () => {
    const svgData = `<svg style="fill: red; text-shadow: 1px 1px 2px gray;"><text x='10' y='20' >Test</text></svg>`;
    const { container } = render(<SVGImage svgData={svgData} alt="Styled SVG" />);

    const processedSvgData = decodeURIComponent(container?.querySelector('img')?.src?.split(',')?.[1] ?? '');

    expect(processedSvgData).not.toContain('text-shadow');
    expect(processedSvgData).toContain('fill: red');
  });

  it('should not remove text-shadow if it appears outside the SVG style attribute', () => {
    const svgData = `
      <svg style="fill: red; text-shadow: 1px 1px 2px gray;">
        <text style="text-shadow: 2px 2px 5px blue;" x='10' y='20'>Test</text>
        <text>text-shadow should not be removed here</text>
      </svg>
    `;

    const { container } = render(<SVGImage svgData={svgData} alt="Styled SVG" />);

    const processedSvgData = decodeURIComponent(container?.querySelector('img')?.src?.split(',')?.[1] ?? '');

    expect(processedSvgData).not.toContain('text-shadow: 1px 1px 2px gray');
    expect(processedSvgData).toContain('fill: red');

    expect(processedSvgData).toContain('text-shadow should not be removed here');
    expect(processedSvgData).toContain('text-shadow: 2px 2px 5px blue');
  });
});
