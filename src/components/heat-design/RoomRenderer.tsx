import { useState } from 'react';
import { Grid, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { SVGImage } from 'components/heat-design/SVGImage';
import { HEAT_DESIGN_FLOOR_PLAN_DIMENSION } from '@ui/theme/constants';
import { Door, Room, Wall, Window } from './stores/types';
import { doorOrWindowIsIncludedInHeatLossCalculation } from './utils/helpers';

import RoomPropertiesRenderer from './room-components/RoomPropertiesRenderer';
import RoomRendererEditor from './RoomRendererEditor';
import { typeIsValid, wallIsValid } from './Validator';
import { invalidPlanSurface } from './HeatDesignTheme';

import RoomRendererHeader from './RoomRendererHeader';
import SelectSurface, { RoomRendererSelectionType } from './SelectSurface';
import { useValidateUValuesForRoom } from './utils/useValidateUValuesForRoom';

function Room2DPlanRenderer({
  currentRoom,
  selected,
  setSelected,
  setHoveredSurface,
}: {
  currentRoom: Room;
  selected?: RoomRendererSelectionType;
  setSelected: (a: RoomRendererSelectionType) => void;
  setHoveredSurface: (a?: string) => void;
}) {
  const { formatMessage } = useIntl();
  const isIncluded = (doorOrWindow: Door | Window) =>
    doorOrWindowIsIncludedInHeatLossCalculation(doorOrWindow, currentRoom);
  const { validateSurfaceUValues } = useValidateUValuesForRoom(currentRoom);

  const checkWallIsValid = (wall: Wall) => wallIsValid(wall) && validateSurfaceUValues([wall]);

  return (
    <Stack alignItems="center" position="relative">
      <SVGImage
        style={{ position: 'absolute', filter: currentRoom.isHeated ? undefined : 'contrast(0.5)' }}
        svgData={currentRoom.imageSvgData}
        alt={currentRoom.name}
        useMap={`#${currentRoom.name}`}
        height={500}
        width={500}
      />
      <Stack
        sx={{
          position: 'relative',
          top: 0,
          left: 0,
          height: '500px',
          width: '500px',
        }}
      >
        <svg width={500} height={500}>
          {currentRoom?.surfaces.doors.map((door) => {
            const coords = (door.imageMap.coordinates || []).map((coord) => {
              if (Number.isNaN(coord)) return coord;
              return coord * (500 / HEAT_DESIGN_FLOOR_PLAN_DIMENSION);
            });
            const isSelected = selected?.surfaceUid === door.uid;
            return (
              <polygon
                key={door.uid}
                className={isSelected ? 'door selected' : 'door'}
                points={coords.toString()}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelected({ surfaceUid: door.uid, type: 'doors', wallUID: door.wallUID });
                }}
                onMouseEnter={() => setHoveredSurface(formatMessage({ id: 'heatDesign.surfaces.doors' }))}
                onMouseLeave={() => setHoveredSurface(undefined)}
                style={!isIncluded(door) || typeIsValid('doors', door) ? undefined : invalidPlanSurface}
              />
            );
          })}
          {currentRoom?.surfaces.walls.map((wall: Wall) => {
            const coords = (wall.imageMap.coordinates || []).map((coord) => {
              if (Number.isNaN(coord)) return coord;
              return coord * (500 / HEAT_DESIGN_FLOOR_PLAN_DIMENSION);
            });
            const isSelected = selected?.surfaceUid === wall.uid;
            let classname = '';
            if (wall.surfaceType === 'internalWalls') {
              classname = 'internalWall';
            } else if (wall.surfaceType === 'externalWalls') {
              classname = 'externalWall';
            } else {
              classname = 'partyWall';
            }
            classname = isSelected ? `${classname} selected` : classname;

            return (
              <polygon
                key={wall.uid}
                className={classname}
                points={coords.toString()}
                onClick={() => setSelected({ type: wall.surfaceType, surfaceUid: wall.uid })}
                onMouseEnter={() => setHoveredSurface(formatMessage({ id: `heatDesign.surfaces.${wall.surfaceType}` }))}
                onMouseLeave={() => setHoveredSurface(undefined)}
                style={checkWallIsValid(wall) ? undefined : invalidPlanSurface}
                data-testid={`wall-${wall.uid}`}
              />
            );
          })}
          {currentRoom?.surfaces.windows.map((window) => {
            const coords = (window.imageMap.coordinates || []).map((coord) => {
              if (Number.isNaN(coord)) return coord;
              return coord * (500 / HEAT_DESIGN_FLOOR_PLAN_DIMENSION);
            });
            const isSelected = selected?.surfaceUid === window.uid;
            return (
              <polygon
                key={window.uid}
                className={isSelected ? 'window selected' : 'window'}
                points={coords.toString()}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelected({ type: 'windows', surfaceUid: window.uid, wallUID: window.wallUID });
                }}
                onMouseEnter={() => setHoveredSurface(formatMessage({ id: 'heatDesign.surfaces.windows' }))}
                onMouseLeave={() => setHoveredSurface(undefined)}
                style={!isIncluded(window) || typeIsValid('windows', window) ? undefined : invalidPlanSurface}
              />
            );
          })}
        </svg>
      </Stack>
    </Stack>
  );
}

export default function RoomRenderer({ room: currentRoom }: { room?: Room }) {
  const [selected, setSelected] = useState<RoomRendererSelectionType>();
  const [hoveredSurface, setHoveredSurface] = useState<string | undefined>(undefined);

  if (!currentRoom) return null;

  return (
    <Stack direction="column">
      <Grid
        direction="row"
        spacing={2}
        container
        sx={{
          height: '81vh',
        }}
      >
        <Grid size={{ mobile: 2 }}>
          <RoomPropertiesRenderer room={currentRoom} />
        </Grid>
        <Grid size={{ mobile: 6 }}>
          <Stack direction="column" alignItems="center">
            <RoomRendererHeader roomId={currentRoom.id} hoveredSurface={hoveredSurface} />
            <Room2DPlanRenderer
              currentRoom={currentRoom}
              selected={selected}
              setSelected={setSelected}
              setHoveredSurface={setHoveredSurface}
            />
            <SelectSurface
              currentRoom={currentRoom}
              selectedSurface={selected}
              setSelectedSurface={setSelected}
              sx={{ maxWidth: 400 }}
            />
          </Stack>
        </Grid>
        <Grid size={{ mobile: 4 }}>
          <RoomRendererEditor selected={selected} setSelected={setSelected} room={currentRoom}>
            {!selected && (
              <Stack mt={10} alignItems="center">
                <Typography variant="body1" component="div">
                  <FormattedMessage id="heatDesign.roomRenderer.selectASurface" />
                </Typography>
              </Stack>
            )}
          </RoomRendererEditor>
        </Grid>
      </Grid>
    </Stack>
  );
}
