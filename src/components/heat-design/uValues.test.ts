import { CountryCode } from 'utils/marketConfigurations';
import { UValue } from './models/UValue';
import { FABRIC_TYPES } from './stores/types';
import { getUValuesForCountryCode, getWorstUValueForBuildingConstructionYear } from './uValues';

describe('U-values', () => {
  it('should have an ID for every U-value', () => {
    Object.keys(CountryCode).forEach((key) => {
      const uValuesForCountry = getUValuesForCountryCode(key as CountryCode);
      FABRIC_TYPES.forEach((fabricType) => {
        uValuesForCountry[fabricType].forEach((uValue) => {
          expect(uValue.id).toBeDefined();
          expect(uValue.id).not.toEqual('');
        });
      });
    });
  });

  it('should not have conflicting IDs in a fabricType', () => {
    Object.keys(CountryCode).forEach((key) => {
      const uValuesForCountry = getUValuesForCountryCode(key as CountryCode);
      FABRIC_TYPES.forEach((fabricType) => {
        const ids = new Set<string>();
        uValuesForCountry[fabricType].forEach((uValue) => {
          if (ids.has(uValue.id)) {
            throw new Error(`Duplicate ID ${uValue.id} in ${fabricType}`);
          }
          ids.add(uValue.id);
        });
      });
    });
  });

  it('should have be same U-value for same ID', () => {
    Object.keys(CountryCode).forEach((key) => {
      const uValuesForCountry = getUValuesForCountryCode(key as CountryCode);
      const uValueIds = new Map<string, UValue>();

      FABRIC_TYPES.forEach((fabricType) => {
        uValuesForCountry[fabricType].forEach((uValue) => {
          if (uValueIds.has(uValue.id)) {
            const otherUValue = uValueIds.get(uValue.id)!;
            expect(otherUValue.name).toEqual(uValue.name);
            expect(otherUValue.value).toEqual(uValue.value);
          } else {
            uValueIds.set(uValue.id, uValue);
          }
        });
      });
    });
  });
});

describe('getWorstUValueForBuildingConstructionYear()', () => {
  it('returns the worst U-Value based on the construction year', () => {
    const uValues = [
      new UValue('<1970', 5, '0', { toYearInclusive: 1970 }),
      new UValue('1990-2010', 4, '1', { fromYearInclusive: 1990, toYearInclusive: 2010 }),
      new UValue('2000-2020', 3, '2', { fromYearInclusive: 2000, toYearInclusive: 2020 }),
      new UValue('>2010', 2, '3', { fromYearInclusive: 2010 }),
      new UValue('>2020', 1, '4', { fromYearInclusive: 2020 }),
    ];

    expect(getWorstUValueForBuildingConstructionYear(uValues, 1970)?.id).toEqual('0');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 1971)).toBeNull();
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2010)?.id).toEqual('1');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 1999)?.id).toEqual('1');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2011)?.id).toEqual('2');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2021)?.id).toEqual('3');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 1980)).toBeNull();
  });

  it('omits any deprecated U-values', () => {
    const uValues = [
      new UValue('<1970', 5, '0', { toYearInclusive: 1970, isDeprecated: true }),
      new UValue('1990-2010', 4, '1', { fromYearInclusive: 1990, toYearInclusive: 2010 }),
      new UValue('2000-2020', 3, '2', { fromYearInclusive: 2000, toYearInclusive: 2020 }),
      new UValue('>2010', 2, '3', { fromYearInclusive: 2010, isDeprecated: true }),
      new UValue('>2020', 1, '4', { fromYearInclusive: 2020 }),
    ];

    expect(getWorstUValueForBuildingConstructionYear(uValues, 1970)).toBeNull();
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2010)?.id).toEqual('1');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 1999)?.id).toEqual('1');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2011)?.id).toEqual('2');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 2021)?.id).toEqual('4');
    expect(getWorstUValueForBuildingConstructionYear(uValues, 1980)).toBeNull();
  });
});

// This is just a guard to ensure that changes to either u-value source should also be applied to the other
// when it comes to the estimated u-values defined in DIN/BS EN 12831. The UK uses a smaller subset.
describe('UK and Germany share DIN/BS EN 12831 estimate u-values', () => {
  FABRIC_TYPES.forEach((fabricType) => {
    it(`for fabric type: ${fabricType}`, () => {
      const germanUValues = getUValuesForCountryCode(CountryCode.DE)[fabricType];
      const ukUValues = getUValuesForCountryCode(CountryCode.GB)[fabricType];

      const estimatedUKUValues = ukUValues.filter((uValue) => uValue.display().includes('BS EN 12831 estimation'));

      const matchingGermanUValuesCount = estimatedUKUValues
        .flatMap((ukUValue) => germanUValues.map((germanUValue) => (germanUValue.id === ukUValue.id ? 1 : 0)))
        .reduce((prev, curr) => prev + curr, 0 as number);

      expect(estimatedUKUValues.length === matchingGermanUValuesCount);
    });
  });
});
