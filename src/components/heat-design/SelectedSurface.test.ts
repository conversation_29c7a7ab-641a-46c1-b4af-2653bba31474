import { attachedElements } from './SelectSurface';
import { Wall, Door, Window } from './stores/types';

describe('attachedElements', () => {
  const wall: Wall = {
    uid: 'uid',
    length: 2.3,
    imageMap: {
      coordinates: [348, 103, 348, 87, 656, 87, 656, 103],
    },
    surfaceType: 'externalWalls',
    soilPercentage: null,
  };
  const window: Window = {
    uid: 'uid2',
    width: 1.7,
    height: 1.21,
    area: 2.05,
    surfaceType: 'windows',
    imageMap: {
      coordinates: [387, 87, 387, 103, 615, 103, 615, 87],
    },
  };
  const door: Door = {
    uid: 'uid3',
    width: 0.99,
    height: 2.05,
    area: 2.04,
    surfaceType: 'doors',
    wallUID: '65685938.d397abff',
    imageMap: {
      coordinates: [340, 314, 473, 314, 473, 181, 340, 181],
    },
  };
  test('windows should find its wall', () => {
    expect(
      attachedElements(
        [wall, { ...window, wallUID: wall.uid }, { ...window, uid: 'anotherwindowId', wallUID: 'anotherwallUID' }],
        { type: 'windows', surfaceUid: window.uid, wallUID: wall.uid },
      ).length,
    ).toBe(2);
  });
  test('windows should find its wall and siblings', () => {
    expect(
      attachedElements([wall, { ...window, wallUID: wall.uid }, { ...window, uid: 'blabla', wallUID: wall.uid }], {
        type: 'windows',
        surfaceUid: window.uid,
        wallUID: wall.uid,
      }).length,
    ).toBe(3);
  });
  test('a wall should find its children only', () => {
    expect(
      attachedElements(
        [wall, { ...door, wallUID: wall.uid }, { ...door, uid: 'anotherId', wallUID: 'somethingElse' }],
        {
          type: 'internalWalls',
          surfaceUid: wall.uid,
        },
      ).length,
    ).toBe(2);
  });
});
