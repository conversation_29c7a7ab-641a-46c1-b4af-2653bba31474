import { FormattedMessage } from 'react-intl';
import { beige, grey } from '@ui/theme/colors';
import { Table, TableBody, TableCell, TableHead, TableRow, Typography } from '@mui/material';
import styled from '@emotion/styled';
import { printRadiatorDescription } from './helpers';
import { RadiatorData } from '../stores/types';
import { useRooms } from '../stores/RoomsStore';
import { hasRadiatorBeenRemovedFromErp } from '../utils/radiatorHelpers';
import { WarningIcon } from '../components/WarningIcon';

const PrintableTable = styled(Table)(() => ({
  '@media print': {
    '*': {
      backgroundColor: 'transparent !important',
    },
    'tr td': {
      borderTop: `1px solid ${grey[500]}`,
    },
  },
}));

const compareErpId = (erpId?: string, otherErpId?: string): number => {
  if (erpId === undefined) {
    return 1;
  }
  if (otherErpId === undefined) {
    return -1;
  }
  return erpId.localeCompare(otherErpId);
};

type Props = {
  radiators: RadiatorData[];
  tableCellColor?: string;
  showErpId?: boolean;
};

export default function NewRadiatorsList({ radiators, tableCellColor = beige[150], showErpId = false }: Props) {
  const rooms = useRooms();
  const rows = radiators
    .sort((a, b) => compareErpId(a.erpId, b.erpId))
    .map((radiator, index) => ({
      id: radiator.uid,
      erpId: hasRadiatorBeenRemovedFromErp(radiator) ? (
        <WarningIcon x={1.8} y={1.8} iconWidth={18} iconHeight={18} canvasWidth={18} canvasHeight={18} />
      ) : (
        <Typography>{radiator.erpId ?? '-'}</Typography>
      ),
      roomName: rooms.find((room) => room.id === radiator.roomId)?.name ?? '',
      description: printRadiatorDescription(radiator),
      isEven: index % 2 === 0,
      isLast: index === radiators.length - 1,
    }));

  if (rows.length === 0) {
    return null;
  }

  return (
    <PrintableTable>
      <TableHead>
        <TableRow>
          <TableCell sx={{ borderBottom: 0, background: beige[200], borderRadius: '16px 0 0 0' }}>
            <Typography variant="body1Emphasis">
              <FormattedMessage id="common.label.room" />
            </Typography>
          </TableCell>
          {showErpId && (
            <TableCell sx={{ borderBottom: 0, background: beige[200] }}>
              <Typography variant="body1Emphasis">
                <FormattedMessage id="heatDesign.common.erpId" />
              </Typography>
            </TableCell>
          )}
          <TableCell sx={{ borderBottom: 0, background: beige[200], borderRadius: '0 16px 0 0' }}>
            <Typography variant="body1Emphasis">
              <FormattedMessage id="heatDesign.report.roomDetails.emitterDetails.description" />
            </Typography>
          </TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        {rows.map((row) => (
          <TableRow
            key={row.id}
            sx={{
              '.MuiTableCell-body': { borderBottom: 0 },
            }}
          >
            <TableCell
              sx={{
                background: row.isEven ? 'white' : tableCellColor,
                borderRadius: row.isLast ? '0 0 0 16px' : 0,
              }}
            >
              <Typography>{row.roomName}</Typography>
            </TableCell>
            {showErpId && (
              <TableCell
                sx={{
                  background: row.isEven ? 'white' : tableCellColor,
                }}
              >
                {row.erpId}
              </TableCell>
            )}
            <TableCell
              sx={{
                background: row.isEven ? 'white' : tableCellColor,
                borderRadius: row.isLast ? '0 0 16px 0' : 0,
              }}
            >
              <Typography>{row.description}</Typography>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </PrintableTable>
  );
}
