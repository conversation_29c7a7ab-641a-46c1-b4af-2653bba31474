import { Box, MenuItem, SelectChangeEvent, Stack } from '@mui/material';
import { LockedTechnicalReport } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { Select } from '@ui/components/Select/Select';
import { grey } from '@ui/theme/colors';
import { useCallback, useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useGroundwork } from 'context/groundwork-context';
import { api } from 'utils/api';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { isEqual } from 'lodash';
import { useRouter } from 'next/router';
import { useSerializedHeatDesignAndResults } from '../hooks/useSerializedHeatDesignAndResults';
import { InlineQueryError } from '../components/errors/InlineQueryError';
import { mapRouterLocaleToHeatDesignLocale } from '../utils/helpers';
import { defaultConfigurations } from 'utils/marketConfigurations';

interface Props {
  includePreview: boolean;
  reports: LockedTechnicalReport[];
}

type Option = 'preview' | LockedTechnicalReport;

function keyForOption(option: Option): string {
  return option === 'preview' ? 'preview' : option.key;
}

function PDFFrame({ url }: { url: string }) {
  const { formatMessage } = useIntl();

  return (
    <iframe
      style={{
        width: '100%',
        height: '750px',
        borderWidth: '1px',
        borderRadius: '16px',
        borderColor: grey[600],
      }}
      title={formatMessage({ id: 'heatDesign.technicalReport.title' })}
      src={url}
    />
  );
}

function createPdfBlobUrl(bytes: Uint8Array) {
  const blob = new Blob([bytes], { type: 'application/pdf' });
  return URL.createObjectURL(blob);
}

function Preview({
  installationGroundworkId,
  energySolutionId,
  locale = defaultConfigurations.locale,
}: {
  installationGroundworkId: string;
  energySolutionId: string;
  locale?: string;
}) {
  const serializedData = useSerializedHeatDesignAndResults();

  const previewMutation = api.HeatLossCalculator.renderPreview.useMutation();
  const renderPreview = useCallback(() => {
    if (!serializedData) {
      return;
    }
    const { heatDesign, result } = serializedData;

    previewMutation.mutate({
      energySolutionId,
      installationGroundworkId,
      heatDesign,
      result,
      locale: mapRouterLocaleToHeatDesignLocale({ locale }),
    });
  }, [energySolutionId, installationGroundworkId, previewMutation, serializedData, locale]);

  // The tRPC call to render a preview is a mutation, not a query. Why? Because queries use query parameters
  // to send data, and that doesn't work for the large amount of data we need to send to render.
  useEffect(() => {
    if (previewMutation.isIdle) {
      renderPreview();
    }
  }, [renderPreview, previewMutation]);

  const previewData = previewMutation.data?.pdf;
  const blobUrl = previewData ? createPdfBlobUrl(previewData) : undefined;

  return (
    <Box sx={{ height: '750px', width: '100%' }}>
      {blobUrl ? (
        <PDFFrame url={blobUrl} />
      ) : (
        <>
          {previewMutation.isPending && <HeatPumpLoader variant="compact" />}
          {previewMutation.error && (
            <InlineQueryError
              error={previewMutation.error}
              handleRetry={renderPreview}
              heading="Error when rendering preview"
            />
          )}
        </>
      )}
    </Box>
  );
}

function LockedReport({
  installationGroundworkId,
  energySolutionId,
  reportKey,
}: {
  installationGroundworkId: string;
  energySolutionId: string;
  reportKey: string;
}) {
  const presignedUrl = api.HeatLossCalculator.presignTechnicalReport.useQuery({
    installationGroundworkId,
    energySolutionId,
    key: reportKey,
  });

  return (
    <Box sx={{ height: '750px', width: '100%' }}>
      {presignedUrl.data ? (
        <PDFFrame url={presignedUrl.data} />
      ) : (
        <>
          {presignedUrl.isLoading && <HeatPumpLoader variant="compact" />}
          {presignedUrl.error && (
            <InlineQueryError
              error={presignedUrl.error}
              handleRetry={() => presignedUrl.refetch()}
              heading="Error when fetching presigned URL for report"
            />
          )}
        </>
      )}
    </Box>
  );
}

export function TechnicalReports({ reports, includePreview }: Props) {
  const { formatMessage } = useIntl();
  const energySolutionId = useEnergySolutionId();
  const { locale } = useRouter();
  const {
    groundwork: { id: groundworkId },
  } = useGroundwork();

  const previewOptions = includePreview ? (['preview'] as const) : [];
  const options: Option[] = [...previewOptions, ...reports];
  const [selectedOption, setSelectedOption] = useState<Option | undefined>(options[0]);
  const [previousOptions, setPreviousOptions] = useState(options);

  // This is the recommended pattern to this kind of situation: https://react.dev/learn/you-might-not-need-an-effect#adjusting-some-state-when-a-prop-changes
  if (!isEqual(options, previousOptions)) {
    setPreviousOptions(options);
    setSelectedOption(options[0]);
  }

  if (!groundworkId || !energySolutionId) {
    return <HeatPumpLoader variant="compact" />;
  }

  if (selectedOption === undefined) {
    return <FormattedMessage id="heatDesign.report.pdf.noReports" />;
  }

  const handleReportChange = (e: SelectChangeEvent) => {
    const newOption = options.find((option) => keyForOption(option) === e.target.value);
    setSelectedOption(newOption);
  };
  return (
    <Stack direction="column" gap={2}>
      <Box sx={{ width: '30em' }}>
        <Select
          // Workaround for an issue where the select would toggle the scrollbar and thus cause a layout shift
          // https://github.com/mui/material-ui/issues/17353#issuecomment-1698096612
          MenuProps={{ disableScrollLock: true }}
          name="report"
          id="report"
          label={formatMessage({ id: 'heatDesign.report.pdf.select' })}
          value={keyForOption(selectedOption)}
          onChange={handleReportChange}
        >
          {options.map((option) => (
            <MenuItem key={keyForOption(option)} value={keyForOption(option)}>
              {option === 'preview' ? (
                <FormattedMessage id="heatDesign.report.pdf.preview" />
              ) : (
                <FormattedMessage
                  id="heatDesign.report.pdf.locked"
                  values={{ generatedAt: option.updatedAt?.toLocaleString(locale, { timeZoneName: 'short' }) }}
                />
              )}
            </MenuItem>
          ))}
        </Select>
      </Box>
      {selectedOption === 'preview' ? (
        <Preview installationGroundworkId={groundworkId.value} energySolutionId={energySolutionId} locale={locale} />
      ) : (
        <LockedReport
          installationGroundworkId={groundworkId.value}
          energySolutionId={energySolutionId}
          reportKey={selectedOption.key}
        />
      )}
    </Stack>
  );
}
