import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useEnergySolution } from 'hooks/useEnergySolution';

import { getCustomerNameAndAddress } from '../utils/helpers';

import ReportInstructions from './ReportInstructions';
import EmitterOverview from '../Emitters/EmitterOverview';

export default function EmitterListReport() {
  const energySolution = useEnergySolution().data;
  const { name: customerName, address: customerAddress } = getCustomerNameAndAddress(energySolution);

  return (
    <Stack>
      <ReportInstructions
        title={<FormattedMessage id="heatDesign.reportInstructions.emitterReport.title" />}
        description={<FormattedMessage id="heatDesign.reportInstructions.emitterReport.description" />}
        showDownload
      />
      <Stack style={{ maxWidth: 900 }} mb={2} gap={5}>
        <Typography variant="headline3">
          <FormattedMessage id="heatDesign.emitterReport.title" />
        </Typography>
        <Stack direction="column">
          <Typography data-testid="emitter-report-customer-name" variant="body1Emphasis">
            {customerName}
          </Typography>
          <Typography data-testid="emitter-report-customer-address" variant="body2">
            {customerAddress}
          </Typography>
        </Stack>
        <Typography>
          <FormattedMessage id="heatDesign.emitterReport.upcomingChanges.description" />
        </Typography>
      </Stack>

      <EmitterOverview />
    </Stack>
  );
}
