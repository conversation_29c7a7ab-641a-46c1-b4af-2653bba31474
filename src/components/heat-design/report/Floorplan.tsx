import { Box, Stack, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import {
  MAGICPLAN_SYMBOL_BUFFER_TANK,
  MAGICPLAN_SYMBOL_INDOOR_UNIT,
  MAGICPLAN_SYMBOL_OUTDOOR_UNIT,
  MAGICPLAN_SYMBOL_THERMOSTAT,
} from '../constants';
import { FloorProps } from '../stores/types';
import { SVGImage } from '../SVGImage';

function LabelDot({ color }: { color: string }) {
  return (
    <span
      style={{
        width: 16,
        height: 16,
        backgroundColor: color,
        borderRadius: '50%',
        printColorAdjust: 'exact',
        WebkitPrintColorAdjust: 'exact',
      }}
    />
  );
}

const SYMBOLS = [
  MAGICPLAN_SYMBOL_OUTDOOR_UNIT,
  MAGICPLAN_SYMBOL_INDOOR_UNIT,
  MAGICPLAN_SYMBOL_BUFFER_TANK,
  MAGICPLAN_SYMBOL_THERMOSTAT,
];

function FloorPlanLegend() {
  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      sx={{ border: `1px solid ${grey[500]}`, borderRadius: 3, width: '100%', maxWidth: '550px' }}
      p={2}
      mx="auto"
    >
      {SYMBOLS.map((symbol) => (
        <Stack direction="row" gap={1} alignItems="center" key={symbol.key}>
          <LabelDot color={symbol.color} />
          <Typography>
            <FormattedMessage id={symbol.key} />
          </Typography>
        </Stack>
      ))}
    </Stack>
  );
}

function FloorPlanDisclaimer() {
  return (
    <Stack alignItems="center" width="100%" maxWidth="550px" mx="auto" mt={2}>
      <Typography variant="body3" align="center">
        <FormattedMessage id="heatDesign.customerReport.floorPlans.disclaimer" />
      </Typography>
    </Stack>
  );
}

type Props = {
  selectedFloorId: string | null;
  floors: FloorProps[];
};

/**
 * Renders the selected floor plan as an SVG. When the page is being printed,
 * all the floor plans are rendered.
 */
export default function Floorplan({ selectedFloorId, floors }: Props) {
  return (
    <Stack gap={8} sx={{ background: '#fff', borderRadius: 2, padding: 2, '@media print': { padding: 0 } }}>
      {floors.map((floor, index) => (
        <Stack
          direction="column"
          key={floor.uid}
          display={floor.uid === selectedFloorId ? 'block' : 'none'}
          sx={{
            '@media print': {
              display: 'block',

              ...(index < floors.length - 1 && { pageBreakAfter: 'always' }),
            },
          }}
        >
          <Box
            mb={2}
            sx={{
              '@media print': {
                border: `1px solid ${grey[500]}`,
                borderRadius: 2,
              },
            }}
          >
            <Typography display="none" displayPrint="block" mt={2} ml={2}>
              {floor.floorName}
            </Typography>
            <Box
              sx={{
                margin: '0 auto',
                width: 465,
                height: 465,
                '@media print': {
                  width: 500,
                  height: 500,
                },
              }}
            >
              <SVGImage
                svgData={floor.simplifiedImageSvgData || floor.imageSvgData}
                alt={floor.floorName}
                width="100%"
                height="100%"
                style={{ display: 'block' }}
              />
            </Box>
          </Box>
          <FloorPlanLegend />
          <FloorPlanDisclaimer />
        </Stack>
      ))}
    </Stack>
  );
}
