import { Stack, Typography } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { ReactNode } from 'react';

type Props = {
  icon: ReactNode;
  title: ReactNode;
  subtitle: ReactNode;
};

function CustomerReportCallout({ icon, title, subtitle, ...otherProps }: Props) {
  return (
    <Stack
      sx={{
        background: beige[200],
        borderRadius: 1.5,
        '@media print': {
          background: 'white',
          border: `1px solid ${grey[500]}`,
          padding: 2,
          gap: 1,
        },
        height: '100%',
        width: '100%',
      }}
      p={3}
      gap={2}
      {...otherProps}
    >
      <Stack direction="row" gap={1} alignItems="center">
        {icon}
        <Typography
          component="span"
          variant="number1"
          mt={0.5}
          sx={{ '@media print': { fontSize: 14 } }}
          data-testid="customer-report-flow-temperature"
        >
          {title}
        </Typography>
      </Stack>
      <Typography variant="body2">{subtitle}</Typography>
    </Stack>
  );
}

export default CustomerReportCallout;
