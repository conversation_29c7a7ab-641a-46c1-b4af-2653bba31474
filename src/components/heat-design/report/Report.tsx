import { Stack } from '@mui/material';
import { Tab<PERSON>ane<PERSON>, TabPanelsWrapper } from '@ui/components/Tabs/Tabs';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { InlineQueryError } from '../components/errors/InlineQueryError';
import { useFloors } from '../stores/FloorsStore';
import CustomerReport from './CustomerReport';
import EmitterListReport from './EmitterListReport';
import ReportInstructions from './ReportInstructions';
import ReportTabs, { ReportTab } from './ReportTabs';
import { TechnicalReports } from './TechnicalReports';

// Custom styles for printing the page nicely
const reportPrintSx = {
  '@media print': {
    display: 'block',
    th: { fontSize: '12px', lineHeight: 1.2, padding: '4px' },
    td: { fontSize: '12px', lineHeight: 1.2, padding: '4px' },
    '.MuiPaper-root': {
      padding: '16px 0 0',
    },
    '.MuiCollapse-root': {
      height: 'auto !important',
      visibility: 'visible',
    },
    '.MuiTypography-h5': {
      fontSize: '16px',
    },
    '.MuiAccordionSummary-root': {
      display: 'block',
    },
    '.MuiAccordionSummary-content > *': {
      fontSize: '18px',
    },
    '.MuiAccordionDetails-root': {
      padding: 0,
    },
  },
};

interface Props {
  isLocked: boolean;
}

export default function Report({ isLocked }: Props) {
  // We do not show technical report previews for locked projects. However, sometimes this is
  // useful for a support issue (e.g. old projects don't have any saved reports), so we have
  // this query flag to override that behaviour.
  const { showReportPreview } = useRouter().query;
  const floors = useFloors();
  const [selectedTab, setSelectedTab] = useState<ReportTab>(ReportTab.CustomerReport);

  const {
    groundwork: { id: groundworkId },
    countryCode,
  } = useGroundwork();
  const energySolutionId = useEnergySolutionId();

  const reportsQuery = api.HeatLossCalculator.fetchLockedTechnicalReports.useQuery(
    {
      energySolutionId: energySolutionId!,
      installationGroundworkId: groundworkId!.value!.toString(),
    },
    {
      enabled: !!energySolutionId && !!groundworkId && !!groundworkId.value,
    },
  );

  return (
    <Stack direction="column" sx={reportPrintSx}>
      <ReportTabs selectedTab={selectedTab} onChangeTab={setSelectedTab} />
      <TabPanelsWrapper
        sx={{
          maxWidth: selectedTab === ReportTab.CustomerReport || selectedTab === ReportTab.EmitterList ? 1200 : 'unset',
        }}
      >
        <TabPanel isSelected={selectedTab === ReportTab.EmitterList} data-testid="emitter-list-report-tab">
          <EmitterListReport />
        </TabPanel>
        <TabPanel isSelected={selectedTab === ReportTab.CustomerReport} data-testid="customer-report-tab">
          <CustomerReport floors={floors} countryCode={countryCode} />
        </TabPanel>
        <TabPanel isSelected={selectedTab === ReportTab.TechnicalReport} data-testid="technical-report-tab">
          <ReportInstructions
            title={<FormattedMessage id="heatDesign.report.instructions.title" />}
            description={<FormattedMessage id="heatDesign.report.instructions.description" />}
            showDownload={false}
          />
          <>
            {reportsQuery.isLoading && <HeatPumpLoader variant="compact" />}
            {reportsQuery.error && (
              <InlineQueryError
                error={reportsQuery.error}
                handleRetry={() => reportsQuery.refetch()}
                heading="Error when fetching locked reports"
              />
            )}
            {reportsQuery.data && (
              <TechnicalReports
                reports={reportsQuery.data.reports}
                includePreview={!isLocked || showReportPreview === 'true'}
              />
            )}
          </>
        </TabPanel>
      </TabPanelsWrapper>
    </Stack>
  );
}
