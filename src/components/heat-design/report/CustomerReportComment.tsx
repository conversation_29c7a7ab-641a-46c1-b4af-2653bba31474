import { Stack, Typography } from '@mui/material';
import { TextField } from '@ui/components/TextField/TextField';
import { FormattedMessage, useIntl } from 'react-intl';
import { useAuxiliaryData, useUpdateAuxiliaryData } from '../stores/HeatDesignAuxiliaryDataStore';
import { customerReportIsValid } from '../Validator';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import { Button } from '@ui/components/Button/Button';

export default function CustomerReportComment() {
  const { formatMessage, locale } = useIntl();
  const isLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const auxiliaryData = useAuxiliaryData();
  const updateAuxiliaryData = useUpdateAuxiliaryData();
  const updateCustomerComment = (newValue: string) => {
    updateAuxiliaryData({
      installationCommentForCustomer: newValue,
    });
  };

  const isValid = customerReportIsValid(auxiliaryData);
  const showTemplateButton = locale === 'it';
  const templateText = formatMessage({
    id: 'heatDesign.customerReport.comment.template.text',
  });

  return (
    <Stack my={5}>
      <Stack gap={1}>
        <TextField
          disabled={isLocked}
          label={formatMessage({ id: 'heatDesign.customerReport.comment.label' })}
          fullWidth
          name="customer-report-comment"
          type="textarea"
          placeholder={formatMessage({ id: 'heatDesign.customerReport.comment.placeholder' })}
          value={auxiliaryData.installationCommentForCustomer}
          minRows={5}
          onChange={(e) => updateCustomerComment(e.target.value)}
          required
          error={!isLocked && !isValid}
          sx={{ displayPrint: 'none' }}
        />
        {showTemplateButton && (
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              disabled={isLocked}
              size="small"
              variant="outlined"
              onClick={() => updateCustomerComment(templateText)}
              sx={{ displayPrint: 'none' }}
            >
              <FormattedMessage id="heatDesign.customerReport.comment.template.buttonTitle" />
            </Button>
          </div>
        )}
      </Stack>
      <Typography component="p" sx={{ display: 'none', displayPrint: 'block' }} variant="body2" mt={1}>
        {auxiliaryData.installationCommentForCustomer || '-'}
      </Typography>
    </Stack>
  );
}
