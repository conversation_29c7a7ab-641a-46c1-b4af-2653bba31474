import { Box, Stack, Typography } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { useMemo, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { useEnergySolution, useEnergySolutionId } from 'hooks/useEnergySolution';
import { api } from 'utils/api';
import { CountryCode, getLocaleFromCountry } from 'utils/marketConfigurations';
import { useMarketStore } from 'utils/stores/marketStore';
import Grid from '@mui/material/Grid';
import { FloorProps } from '../stores/types';
import Floorplan from './Floorplan';
import NewRadiatorsList from './NewRadiatorsList';
import { useRooms } from '../stores/RoomsStore';
import { generateQuoteSummaryURL, getCustomerNameAndAddress } from '../utils/helpers';
import CustomerReportComment from './CustomerReportComment';
import HeatDesignCard from '../HeatDesignCard';
import ReportInstructions from './ReportInstructions';
import { useDefaultFloor } from '../stores/FloorsStore';
import { useRadiatorsForCountry } from '../hooks/useRadiators';
import { marketConfiguration } from '../utils/marketConfigurations';
import { getCustomerDesignReportCard } from './CustomerReportCards';
import { FloorNavigation } from '../FloorNavigation';

type Props = {
  countryCode: CountryCode;
  floors: FloorProps[];
};

export default function CustomerReport({ countryCode, floors }: Props) {
  const { data: serverEnvironment } = api.AiraBackend.getServerEnvironment.useQuery();
  const { country } = useMarketStore();
  const locale = getLocaleFromCountry(country);
  const solutionId = useEnergySolutionId();

  const { data: catalogueRadiators } = useRadiatorsForCountry(countryCode);
  const [selectedFloor, setSelectedFloor] = useState<FloorProps | undefined>(useDefaultFloor());

  const energySolution = useEnergySolution().data;
  const { name: customerName, address: customerAddress } = getCustomerNameAndAddress(energySolution);

  // Generate the list of new radiators
  const rooms = useRooms();
  const newRadiators = useMemo(() => {
    const radiatorErpIds: Map<string, string | undefined> = new Map();

    catalogueRadiators?.radiators.forEach((catalogueRadiator) => {
      if (catalogueRadiator.radiatorId) {
        radiatorErpIds.set(catalogueRadiator.radiatorId.value, catalogueRadiator.erpId);
      }
    });

    return rooms
      .flatMap((room) => room.radiators.filter((r) => !r.isExisting && r.enabled))
      .map((rad) => {
        const erpId = rad.specificationReferenceId ? radiatorErpIds.get(rad.specificationReferenceId) : undefined;
        return { ...rad, erpId };
      });
  }, [catalogueRadiators, rooms]);

  const quoteSummaryURL =
    serverEnvironment === undefined ? undefined : generateQuoteSummaryURL({ serverEnvironment, locale, solutionId });

  const marketCustomerCards = marketConfiguration[countryCode].customerDesignReportCards ?? [];

  return (
    <div>
      <ReportInstructions
        title={<FormattedMessage id="heatDesign.customerReport.instructions.title" />}
        description={<FormattedMessage id="heatDesign.customerReport.instructions.description" />}
        shareableURL={quoteSummaryURL}
        showDownload
      />
      <HeatDesignCard variant="light">
        <Heading level={2} variant="headline3" mb={2}>
          <FormattedMessage id="heatDesign.customerReport.subtitle" />
        </Heading>
        <Stack style={{ maxWidth: 900 }} mb={2} gap={5}>
          <Stack direction="column">
            <Typography data-testid="customer-report-customer-name" variant="body1Emphasis">
              {customerName}
            </Typography>
            <Typography data-testid="customer-report-customer-address" variant="body2">
              {customerAddress}
            </Typography>
          </Stack>
          <Typography>
            <FormattedMessage id="heatDesign.customerReport.upcomingChanges.description" />
          </Typography>
        </Stack>
        <Heading level={3} variant="headline4" mb={2}>
          <FormattedMessage id="heatDesign.customerReport.calculationResults.title" />
        </Heading>
        <Grid container spacing={4} columns={{ mobile: 6, tablet: 12 }}>
          {marketCustomerCards.map((card) => (
            <Grid key={card} size={{ mobile: 4 }}>
              {getCustomerDesignReportCard(card)}
            </Grid>
          ))}
        </Grid>
        <Box mt={8} sx={{ '@media print': { breakInside: 'avoid' } }}>
          <Heading level={2} variant="headline3">
            <FormattedMessage id="heatDesign.customerReport.upcomingChanges.title" />
          </Heading>
          {newRadiators.length > 0 && (
            <section data-testid="new-radiators-table">
              <Heading level={3} variant="headline4" mt={2} mb={2}>
                <FormattedMessage id="heatDesign.customerReport.newRadiators.title" />
              </Heading>
              <NewRadiatorsList radiators={newRadiators} />
            </section>
          )}
          <CustomerReportComment />
        </Box>
        <Heading level={3} variant="headline3" mt={8} mb={2} sx={{ '@media print': { pageBreakBefore: 'always' } }}>
          <FormattedMessage id="heatDesign.customerReport.floorPlans.title" />
        </Heading>
        <Stack direction="row" gap={2} mb={4} displayPrint="none">
          <FloorNavigation selectFloor={setSelectedFloor} selectedFloor={selectedFloor} />
        </Stack>
        <Floorplan floors={floors} selectedFloorId={selectedFloor?.uid ?? null} />
      </HeatDesignCard>
    </div>
  );
}
