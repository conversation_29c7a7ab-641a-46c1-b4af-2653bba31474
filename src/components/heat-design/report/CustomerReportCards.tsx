import { HouseWave3Icon } from '@ui/components/StandardIcons/HouseWave/HouseWave3Icon';
import { Wave3Icon } from '@ui/components/StandardIcons/Wave/Wave3Icon';
import { DeviceOutlinedIcon } from '@ui/components/StandardIcons/DeviceOutlinedIcon';
import { WaterDropIcon } from '@ui/components/StandardIcons/WaterDropIcon';
import { CircleWaveVertical3Icon } from '@ui/components/StandardIcons/CircleWaveVertical/CircleWaveVertical3Icon';
import { FormattedMessage } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import { formatDegreeCelsius, formatWatts } from 'utils/helpers';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useMarketStore } from 'utils/stores/marketStore';
import { getLocaleFromCountry } from 'utils/marketConfigurations';
import { HourglassTopOutlined } from '@ui/components/Icons/material';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { optionalToNoDecimalPlaces } from '../utils/helpers';
import { useAdjustedOutdoorDesignTemperature } from '../stores/HouseInputsStore';
import { useFlowTemp } from '../stores/HeatSourceStore';
import CustomerReportCallout from './CustomerReportCallout';
import { CustomerDesignReportCard } from '../utils/marketConfigurations';
import { UNDERFLOOR_HEATING_FLOW_TEMP } from '../constants';
import { Wave3HorizontalSquareOutlinedIcon } from '@ui/components/StandardIcons/Wave3HorizontalSquareOutlinedIcon';
import { useRooms } from '../stores/RoomsStore';
import { SystemType } from '../stores/types';

export function HeatLossCard() {
  const { country } = useMarketStore();
  const locale = getLocaleFromCountry(country);
  const { totalHeatLoss } = useDwellingHeatDesignResult();
  const formattedHeatLoss = formatWatts(totalHeatLoss, locale, 1);

  const adjustedOutdoorDesignTemperature = useAdjustedOutdoorDesignTemperature();

  return (
    <CustomerReportCallout
      icon={<HouseWave3Icon width={32} height={32} />}
      data-testid="customer-report-heat-loss"
      title={formattedHeatLoss}
      subtitle={
        <StyledFormattedMessage
          id="heatDesign.customerReport.calculationResults.heatLossColdestDay"
          values={{ temp: adjustedOutdoorDesignTemperature }}
        />
      }
    />
  );
}

export function WaterReheatTimeCard() {
  const { waterReheatTimeMinutes } = useDwellingHeatDesignResult();
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const selectedOutdoorUnits = selectedHeatPumpPackages.heatPumpOutdoorUnit;

  return (
    waterReheatTimeMinutes !== undefined &&
    waterReheatTimeMinutes > 0 &&
    selectedOutdoorUnits?.length === 1 && (
      <CustomerReportCallout
        icon={<HourglassTopOutlined sx={{ width: 32, height: 32 }} />}
        data-testid="customer-report-reheat-time"
        title={
          <>
            {optionalToNoDecimalPlaces(waterReheatTimeMinutes)} <FormattedMessage id="common.minutes" />
          </>
        }
        subtitle={
          <StyledFormattedMessage
            id="heatDesign.waterReheatTime"
            values={{ reheatTime: optionalToNoDecimalPlaces(waterReheatTimeMinutes) }}
          />
        }
      />
    )
  );
}

export function FlowTemperatureCard() {
  const flowTemp = useFlowTemp();

  return (
    <CustomerReportCallout
      icon={<Wave3Icon width={32} height={32} />}
      data-testid="customer-report-flow-temperature"
      title={formatDegreeCelsius(flowTemp, 0)}
      subtitle={<StyledFormattedMessage id="heatDesign.customerReport.calculationResults.flowTemperature" />}
    />
  );
}

export function UnderfloorHeatingFlowTemperatureCard() {
  const rooms = useRooms();
  const hasAnyUnderfloorHeating =
    rooms.filter((room) => room.underfloorHeating?.systemType === SystemType.WATER).length > 0;

  return (
    hasAnyUnderfloorHeating && (
      <CustomerReportCallout
        icon={<Wave3HorizontalSquareOutlinedIcon width={32} height={32} />}
        data-testid="customer-report-underfloor-heating-flow-temperature"
        title={formatDegreeCelsius(UNDERFLOOR_HEATING_FLOW_TEMP, 0)}
        subtitle={<StyledFormattedMessage id="heatDesign.customerReport.calculationResults.ufhFlowTemperature" />}
      />
    )
  );
}

export function SelectedOutdoorUnit() {
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const selectedOutdoorUnits = selectedHeatPumpPackages.heatPumpOutdoorUnit;

  return (
    selectedOutdoorUnits && (
      <CustomerReportCallout
        icon={<DeviceOutlinedIcon width={32} height={32} />}
        data-testid="customer-report-outdoor-unit"
        title={
          <FormattedMessage
            id="heatDesign.customerReport.outdoorUnits"
            values={{ count: selectedHeatPumpPackages.heatPumpOutdoorUnit?.length }}
          />
        }
        subtitle={selectedOutdoorUnits.map((outdoorUnit) => outdoorUnit.displayName).join('\n')}
      />
    )
  );
}

export function SelectedIndoorUnit() {
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const selectedIndoorUnit = selectedHeatPumpPackages.heatPumpIndoorUnit?.[0];

  return (
    selectedIndoorUnit && (
      <CustomerReportCallout
        icon={<WaterDropIcon width={32} height={32} />}
        data-testid="customer-report-indoor-unit"
        title={<FormattedMessage id="floorPlans.symbols.indoorUnit" />}
        subtitle={selectedIndoorUnit.displayName}
      />
    )
  );
}

export function SelectedBufferTank() {
  const selectedHeatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const selectedBufferTank = selectedHeatPumpPackages.bufferTank?.[0];

  return (
    selectedBufferTank && (
      <CustomerReportCallout
        icon={<CircleWaveVertical3Icon width={32} height={32} />}
        data-testid="customer-report-buffer-tank"
        title={<FormattedMessage id="floorPlans.symbols.bufferTank" />}
        subtitle={selectedBufferTank.displayName}
      />
    )
  );
}

export function getCustomerDesignReportCard(card: CustomerDesignReportCard) {
  switch (card) {
    case CustomerDesignReportCard.HEAT_LOSS:
      return <HeatLossCard />;
    case CustomerDesignReportCard.WATER_REHEAT_TIME:
      return <WaterReheatTimeCard />;
    case CustomerDesignReportCard.FLOW_TEMPERATURE:
      return <FlowTemperatureCard />;
    case CustomerDesignReportCard.UNDERFLOOR_HEATING_FLOW_TEMPERATURE:
      return <UnderfloorHeatingFlowTemperatureCard />;
    case CustomerDesignReportCard.OUTDOOR_UNIT:
      return <SelectedOutdoorUnit />;
    case CustomerDesignReportCard.INDOOR_UNIT:
      return <SelectedIndoorUnit />;
    case CustomerDesignReportCard.BUFFER_TANK:
      return <SelectedBufferTank />;
    default:
      return card satisfies never;
  }
}
