import { ContentCopyOutlined, SaveAltOutlined } from '@ui/components/Icons/material';
import { Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { ReactNode } from 'react';
import toast from 'react-hot-toast';
import { FormattedMessage, useIntl } from 'react-intl';
import HeatDesignCard from '../HeatDesignCard';

type ReportInstructionsProps = {
  title: ReactNode;
  description: ReactNode;
  showDownload: boolean;
  onDownload?: () => void;
  shareableURL?: string;
};

export default function ReportInstructions({
  title,
  description,
  showDownload,
  onDownload = () => window.print(),
  shareableURL = undefined,
}: ReportInstructionsProps) {
  const { formatMessage } = useIntl();
  return (
    <HeatDesignCard variant="light" sx={{ marginBottom: '2rem', displayPrint: 'none' }}>
      <Stack direction="row" spacing={2} alignItems="start" mb={2} justifyContent="space-between">
        <Typography variant="headline3">{title}</Typography>
        {showDownload && (
          <Button startIcon={<SaveAltOutlined />} size="small" sx={{ flexShrink: 0 }} onClick={onDownload}>
            <FormattedMessage id="common.label.download" />
          </Button>
        )}
        {shareableURL && (
          <Button
            startIcon={<ContentCopyOutlined />}
            size="small"
            sx={{ flexShrink: 0 }}
            onClick={() => {
              navigator.clipboard.writeText(shareableURL!);
              toast.success(formatMessage({ id: 'common.notify.copySuccess' }));
            }}
          >
            <FormattedMessage id="heatDesign.customerReport.button" />
          </Button>
        )}
      </Stack>
      <Typography>{description}</Typography>
    </HeatDesignCard>
  );
}
