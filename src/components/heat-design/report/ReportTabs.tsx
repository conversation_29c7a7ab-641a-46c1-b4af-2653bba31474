import { Stack } from '@mui/material';
import { BankCardIcon } from '@ui/components/StandardIcons/BankCardIcon';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { ListIcon } from '@ui/components/StandardIcons/ListIcon';
import { TabButton, TabButtonsWrapper } from '@ui/components/Tabs/Tabs';
import { FormattedMessage } from 'react-intl';
import { useAuxiliaryData } from '../stores/HeatDesignAuxiliaryDataStore';
import { customerReportIsValid } from '../Validator';
import { WarningIcon } from '../components/WarningIcon';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';

export enum ReportTab {
  CustomerReport,
  TechnicalReport,
  EmitterList,
}

type Props = {
  selectedTab: ReportTab;
  onChangeTab: (tab: ReportTab) => void;
};

export default function ReportTabs({ selectedTab, onChangeTab }: Props) {
  const isLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const auxiliaryData = useAuxiliaryData();
  const showCustomerReportWarning = !customerReportIsValid(auxiliaryData);

  return (
    <TabButtonsWrapper>
      <TabButton
        isSelected={selectedTab === ReportTab.CustomerReport}
        onClick={() => onChangeTab(ReportTab.CustomerReport)}
      >
        <Stack direction="row" gap={1} alignItems="center">
          {!isLocked && showCustomerReportWarning && (
            <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
          )}
          <HousePersonOutsideIcon width={20} height={20} />
          <span>
            <FormattedMessage id="heatDesign.customerReport.title" />
          </span>
        </Stack>
      </TabButton>
      <TabButton
        isSelected={selectedTab === ReportTab.TechnicalReport}
        onClick={() => onChangeTab(ReportTab.TechnicalReport)}
      >
        <Stack direction="row" gap={1} alignItems="center">
          <BankCardIcon />
          <FormattedMessage id="heatDesign.technicalReport.title" />
        </Stack>
      </TabButton>
      <TabButton isSelected={selectedTab === ReportTab.EmitterList} onClick={() => onChangeTab(ReportTab.EmitterList)}>
        <Stack direction="row" gap={1} alignItems="center">
          <ListIcon />
          <FormattedMessage id="heatDesign.emitterReport.tab.title" />
        </Stack>
      </TabButton>
    </TabButtonsWrapper>
  );
}
