import { ReactNode, useState } from 'react';
import { Stack, SxProps, Theme, Typography, TypographyProps } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { v4 as uuidv4 } from 'uuid';

type Props = {
  icon?: ReactNode;
  trailingContent?: ReactNode;
  label: ReactNode;
  value?: string | number;
  labelFontVariant?: TypographyProps['variant'];
  'data-testid'?: string;
  sx?: SxProps<Theme>;
};

export function InfoRow({
  icon,
  trailingContent,
  label,
  value,
  labelFontVariant = 'body2Emphasis',
  'data-testid': dataTestid,
  sx,
}: Props) {
  // Assign a unique id to the value element so that we can call getByLabel() in the tests.
  const [fieldId] = useState(uuidv4());

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      sx={{
        printColorAdjust: 'exact',
        WebkitPrintColorAdjust: 'exact',
        borderBottom: `1px solid ${grey[300]}`,
        '&:last-child': {
          borderBottom: 0,
        },
        ...sx,
      }}
      alignItems="center"
      data-testid={dataTestid}
    >
      <Stack direction="row" alignItems="center">
        {/* Negative top margin on the icon to better align with the text */}
        {icon && <div style={{ width: 24, height: 24, marginRight: 8, marginTop: -2 }}>{icon}</div>}
        <Typography
          component="label"
          variant={labelFontVariant}
          data-testid={`${dataTestid}-label`}
          id={fieldId}
          sx={{
            '@media print': {
              fontSize: '12px',
            },
          }}
        >
          {label}
        </Typography>
        {trailingContent && <div style={{ marginLeft: 8, marginRight: 8 }}>{trailingContent}</div>}
      </Stack>
      <Typography
        aria-labelledby={fieldId}
        variant="body1Emphasis"
        data-testid={`${dataTestid}-value`}
        sx={{
          '@media print': {
            fontSize: '12px',
          },
          pl: 1,
        }}
      >
        {value}
      </Typography>
    </Stack>
  );
}
