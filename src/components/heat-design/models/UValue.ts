import { v4 as uuidv4 } from 'uuid';
import { UValueSource } from '../u-values/u-value-sources';

type UValueMetadata = {
  fromYearInclusive?: number;
  toYearInclusive?: number;
  isDeprecated?: boolean;
  isCustom?: boolean;
  climateZone?: string;
  source?: UValueSource;
};

function getBrowserLanguage() {
  if (typeof navigator !== 'undefined' && typeof navigator.language === 'string') {
    return navigator.language;
  }
  return 'en-GB';
}

let uValueFormatter: Intl.NumberFormat | undefined = undefined;
function getUValueFormatter(): Intl.NumberFormat {
  if (uValueFormatter === undefined) {
    uValueFormatter = Intl.NumberFormat(getBrowserLanguage(), { minimumFractionDigits: 2 });
  }
  return uValueFormatter;
}

export function getApplicableYearsAsString(uValue: UValue): string {
  if (uValue.metadata.fromYearInclusive === undefined && uValue.metadata.toYearInclusive === undefined) {
    return '';
  }

  if (uValue.metadata.toYearInclusive === undefined) {
    return `(≥${uValue.metadata.fromYearInclusive}) `;
  }

  if (uValue.metadata.fromYearInclusive === undefined) {
    return `(≤${uValue.metadata.toYearInclusive}) `;
  }

  return `(${uValue.metadata.fromYearInclusive}-${uValue.metadata.toYearInclusive}) `;
}

export class UValue {
  constructor(
    readonly name: string,
    readonly value: number,
    readonly id: string = uuidv4(),
    readonly metadata: UValueMetadata = {},
  ) {}

  displayName(): string {
    return `${getApplicableYearsAsString(this)}${this.name}`;
  }

  display(): string {
    return `${UValue.formatValue(this.value)} | ${this.displayName()}`;
  }

  static formatValue(value: number): string {
    return getUValueFormatter().format(value);
  }
}
