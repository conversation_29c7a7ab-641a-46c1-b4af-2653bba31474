import { UValue } from '../models/UValue';
import { UValues } from '../stores/types';
import { GLOBAL_GLAZINGS, GLOBAL_INTERNAL_WALLS } from './global-u-values';
import { UValueSource } from './u-value-sources';

/**
 * Germany: Fenster, Fenstertüren (windows, doors with windows)
 */
const GERMAN_GLAZINGS: UValue[] = [
  ...GLOBAL_GLAZINGS,
  // Wooden frame, single glazing
  new UValue('Ho<PERSON><PERSON><PERSON>men, Einfachverglasung', 5.0, '686897b6-7e7c-4b8b-824f-40f7fb8a267c', {
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Wooden frame, double glazing
  new UValue('Holzrahmen, Doppelverglasung', 2.7, 'c416d4cb-bd8c-4dc4-bbad-21bb208a02e3', {
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Wooden frame, insulation glazing
  new UValue('Hol<PERSON>rahmen, Isolierverglasung', 1.8, 'bfab3ec9-4c2a-42cf-b3fb-8a9baa4197ca', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Plastic frame, insulating glazing
  new UValue('Kunststoffrahmen, Isoliervarglasung', 3.0, '98ecd0ae-8445-4db0-b253-5883d1486a30', {
    fromYearInclusive: 1958,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Plastic frame, insulating glazing
  new UValue('Kunststoffrahmen, Isoliervarglasung', 1.8, '11e45625-863a-4a0c-8fc4-9ec100a151a6', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Metal frame, insulating glazing
  new UValue('Metallrahmen, Isolierverglasung', 4.3, '337342f3-e4b0-4bb9-a321-a737f7b71002', {
    fromYearInclusive: 1958,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Metal frame, insulating glazing
  new UValue('Metallrahmen, Isolierverglasung', 1.8, 'b9d75387-e123-4f7b-8987-c27685eb880e', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Windows, doors with windows
  // Source: EnEV
  new UValue(
    'Fenster, Fenstertüren (Änderung der Energieeinsparverordnung)',
    1.3,
    '5bb0dbc8-c629-4f91-8bf5-76d4b553a648',
    {
      fromYearInclusive: 2009,
      toYearInclusive: 2015,
      source: UValueSource.EN_EV_2009,
    },
  ),
  // Source: EnEV
  new UValue(
    'Fenster, Fenstertüren (Änderung der Energieeinsparverordnung)',
    0.98,
    '6686bb05-e22f-4899-9dfe-e8da1fc91817',
    {
      fromYearInclusive: 2016,
      source: UValueSource.EN_EV_2009,
    },
  ),
  // Source: Bundesgesetzblatt Jahrgang 2001
  new UValue(
    'Fenster, Fenstertüren (Änderung der Energieeinsparverordnung)',
    1.7,
    '0f2abebe-b16e-467b-bbe0-e47237c5f41c',
    {
      fromYearInclusive: 2002,
      toYearInclusive: 2008,
      source: UValueSource.GERMAN_LAW_GAZETTE_2001,
    },
  ),
  // Source: Bundesgesetzblatt Jahrgang 2009 Teil I Nr. 23, 2013 Teil I Nr. 67, 2020, 2023, Teil I Nr. 280
  new UValue(
    'Fenster, Fenstertüren (Änderung der Energieeinsparverordnung)',
    1.7,
    '167a7a74-97f1-48fa-90a5-ef573ead8539',
    {
      fromYearInclusive: 2009,
      source: UValueSource.GERMAN_LAW_GAZETTE_2009,
    },
  ),
];

const GERMAN_ROOF_GLAZINGS: UValue[] = [...GERMAN_GLAZINGS];

const GERMAN_WINDOWS: UValue[] = [...GERMAN_GLAZINGS];

/**
 * Germany: Türen (doors)
 */
const GERMAN_DOORS: UValue[] = [
  // We include all the U-values for windows. Typically, in German single-family
  // houses, glass doors or bigger-sized windows are installed in living rooms,
  // so German heat engineers requested this.
  ...GERMAN_WINDOWS,
  // Doors
  new UValue('Türen', 3.5, 'a1c46b63-4e98-4193-ad41-fe4ef0e91722', {
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Exterior doors
  // Source: EnEV
  new UValue('Außentüren', 1.8, '1b2a5dfd-5a45-4c73-96cb-d7b4f776e280', {
    fromYearInclusive: 2009,
    toYearInclusive: 2015,
    source: UValueSource.EN_EV_2009,
  }),
  // Source: EnEV
  new UValue('Außentüren', 1.35, '8f90e445-6b15-4f09-8e6d-fd5eba15c87c', {
    fromYearInclusive: 2016,
    source: UValueSource.EN_EV_2009,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2001
  new UValue('Türen (Änderung der Energieeinsparverordnung)', 2.9, '6ab1f2d5-7ee6-4363-85ea-d90fd42d393d', {
    fromYearInclusive: 2002,
    toYearInclusive: 2005,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2001
  new UValue('Türen (Änderung der Energieeinsparverordnung)', 1.8, '884d3f86-4a0d-46cd-a591-eb7e97343e2f', {
    fromYearInclusive: 2009,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
];

/**
 * Germany: Außenwände, Wände gegen das Erdreich, Innenwände gegen unbeheizte
 * Kellergeschosse (walls)
 */
const GERMAN_EXTERNAL_WALLS: UValue[] = [
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.7, '7becb818-360c-4214-86d7-23485f0386ba', {
    toYearInclusive: 1948,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.4, '26ce26cb-9e4e-4094-93ba-938c49226137', {
    fromYearInclusive: 1949,
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.0, 'b6e5a35a-600c-458b-8fe7-398255365727', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 0.8, '988780c8-d200-4768-9f8b-2946ed03d077', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 0.6, '210a7e33-c1fa-429c-b9d7-ee0643d75c59', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 0.5, 'aea67196-3f47-46ba-832a-6f8e6daa6873', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),

  // Wooden construction (wooden frame construction, prefab house or similar)
  new UValue(
    'Holzkonstruktion (Holzrahmenkonstruktion, Fertighaus oder Ähnliches)',
    2.0,
    '8c55476f-57cc-4e6b-8253-55b7660362e0',
    {
      toYearInclusive: 1948,
      source: UValueSource.DIN12831_TABLE_B_15,
    },
  ),
  // Wooden construction (wooden frame construction, prefab house or similar)
  new UValue(
    'Holzkonstruktion (Holzrahmenkonstruktion, Fertighaus oder Ähnliches)',
    1.4,
    '76f0a15b-8629-47cc-901e-0c0d4a64ec91',
    {
      fromYearInclusive: 1949,
      toYearInclusive: 1968,
      source: UValueSource.DIN12831_TABLE_B_15,
    },
  ),
  // Wooden construction (wooden frame construction, prefab house or similar)
  new UValue(
    'Holzkonstruktion (Holzrahmenkonstruktion, Fertighaus oder Ähnliches)',
    0.6,
    '3f16d7ec-04f5-49a3-a680-b5ba24691943',
    {
      fromYearInclusive: 1969,
      toYearInclusive: 1978,
      source: UValueSource.DIN12831_TABLE_B_15,
    },
  ),
  // Wooden construction (wooden frame construction, prefab house or similar)
  new UValue(
    'Holzkonstruktion (Holzrahmenkonstruktion, Fertighaus oder Ähnliches)',
    0.5,
    '5268f804-1c81-4519-baf6-b59f264ed6a8',
    {
      fromYearInclusive: 1979,
      toYearInclusive: 1983,
      source: UValueSource.DIN12831_TABLE_B_15,
    },
  ),
  // Wooden construction (wooden frame construction, prefab house or similar)
  new UValue(
    'Holzkonstruktion (Holzrahmenkonstruktion, Fertighaus oder Ähnliches)',
    0.4,
    'f8dc5bf2-f32a-4cc6-91f3-f6f3d7686cf4',
    { fromYearInclusive: 1984, source: UValueSource.DIN12831_TABLE_B_15 },
  ),
  // Außenwand = exterior wall
  // Source: EnEV
  new UValue('Außenwand', 0.28, '9a00823e-381c-4ece-bb1a-e65dba1abc05', {
    fromYearInclusive: 2009,
    toYearInclusive: 2015,
    source: UValueSource.EN_EV_2009,
  }),
  // Source: EnEV
  new UValue('Außenwand', 0.21, 'b40a46f8-626d-40fd-9c89-cc2e0db767d8', {
    fromYearInclusive: 2016,
    source: UValueSource.EN_EV_2009,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2001
  new UValue('Außenwand (Änderung der Energieeinsparverordnung)', 0.35, '35dc3522-9259-4c70-925d-296d663b0799', {
    fromYearInclusive: 2002,
    toYearInclusive: 2008,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2009 teil I Nr. 23
  new UValue('Außenwand (Änderung der Energieeinsparverordnung)', 0.28, '07ad5467-05d2-40e9-8e8e-242e5d0a3a2a', {
    fromYearInclusive: 2009,
    toYearInclusive: 2019,
    source: UValueSource.GERMAN_LAW_GAZETTE_2009,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2020 Teil I, 1767-1768
  new UValue('Außenwand (Änderung der Energieeinsparverordnung)', 0.24, '4b655578-4fcb-421b-9dd7-acf412bbbee0', {
    fromYearInclusive: 2020,
    source: UValueSource.GERMAN_LAW_GAZETTE_2020,
  }),
];

/**
 * Germany: Decken gegen das Erdreich oder unbeheizte Kellergeschosse (floors
 * over ground or unheated cellars)
 */
const GERMAN_FLOORS: UValue[] = [
  // Solid construction (masonry, concrete, or similar)
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.2, '94beb08d-f98f-4553-bed2-b3ea567902da', {
    toYearInclusive: 1948,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.5, 'df769263-932f-4fdd-bea2-cad8957451ae', {
    fromYearInclusive: 1949,
    toYearInclusive: 1957,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 1.0, 'e56c61e4-ee0b-4b39-ac15-2e108ca88633', {
    fromYearInclusive: 1958,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 0.8, '9a4e9e26-18e0-42e6-99bf-dd82655edec0', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise (Mauerwerk, Beton oder Ähnliches)', 0.6, 'f85cbe05-0ce7-4f96-9fc5-8a5305cd8b63', {
    fromYearInclusive: 1984,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Wooden beam ceiling
  new UValue('Holzbalkendecke', 1.0, 'dd871760-3f9f-429c-9b97-d0c8e8ce4895', {
    toYearInclusive: 1918,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.8, '652534df-ccad-405e-8218-8d7c6e527e05', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.6, '31e76ce1-6db8-479d-ac82-13b45d094823', {
    fromYearInclusive: 1969,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.4, 'd31a9484-bcaf-4050-8317-f651270eb0ee', {
    fromYearInclusive: 1984,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Floor slab (walls and ceilings to unheated rooms)
  // Source: EnEV
  new UValue('Bodenplatte (Wände und Decken zu unbeheizten Räumen)', 0.35, '70722b10-0dc7-4288-b5a0-978a2a1e0cae', {
    fromYearInclusive: 2009,
    toYearInclusive: 2015,
    source: UValueSource.EN_EV_2009,
  }),
  // Source: EnEV
  new UValue('Bodenplatte (Wände und Decken zu unbeheizten Räumen)', 0.26, '23b06373-8e35-4bd2-9e66-9d0d80204636', {
    fromYearInclusive: 2016,
    source: UValueSource.EN_EV_2009,
  }),
  // Bundesgesetzblatt Jahrgang 2001
  new UValue('Bodenplatte (Änderung der Energieeinsparverordnung)', 0.5, '917d399d-fad6-4c93-8be7-3b60053f3f32', {
    fromYearInclusive: 2002,
    toYearInclusive: 2008,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
  // Bundesgesetzblatt Jahrgang 2009 teil I Nr. 23
  new UValue('Bodenplatte (Änderung der Energieeinsparverordnung)', 0.35, '8a141fd4-39e0-493b-88c8-e02589fafdcc', {
    fromYearInclusive: 2009,
    toYearInclusive: 2019,
    source: UValueSource.GERMAN_LAW_GAZETTE_2009,
  }),
  // Bundesgesetzblatt Jahrgang 2020 Teil I, 1767-1768
  new UValue('Bodenplatte (Änderung der Energieeinsparverordnung)', 0.3, '0830c158-86f5-465f-9a10-89efd8bc2878', {
    fromYearInclusive: 2020,
    source: UValueSource.GERMAN_LAW_GAZETTE_2020,
  }),
];

/**
 * Germany: Dächer und Wände zwischen beheizten und unbeheizten Dachgeschossen
 * (roofs and walls between heated and unheated attics)
 *
 * We consider these CEILING values (not roof)
 */
const GERMAN_CEILINGS: UValue[] = [
  // Solid construction
  new UValue('Massivbauweise', 2.1, '388c41a6-253e-4294-83c7-0273fbf0cb12', {
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise', 0.6, '9631d2aa-dc4c-41d4-aa31-ad21d34e167c', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise', 0.5, '8d52bf42-3c0d-42eb-87d1-e7f06932d005', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise', 0.4, '7e45591a-8fdf-44c1-a910-5d4159ba782e', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massivbauweise', 0.3, 'd9722dce-f80e-4a75-a3ad-1bbb6bf5f858', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Wooden construction
  new UValue('Holzkonstruktion', 2.6, 'ea720470-7a81-4e06-b438-ef69513125a3', {
    toYearInclusive: 1918,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzkonstruktion', 1.4, 'dfcae97b-13d8-4be8-8efa-48bc58137805', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzkonstruktion', 0.8, '497b669b-c6c0-435c-ab1c-2aad78454f05', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzkonstruktion', 0.5, 'd9b5bc83-1bc9-420b-9dfa-dac2c2f7c42f', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzkonstruktion', 0.4, 'a19701a1-c175-4be2-9566-463452368410', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzkonstruktion', 0.3, 'c42423a1-b094-4a77-9183-307d27385f4c', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
];

/**
 * Germany: Decken im obersten Geschoss und Decken über Außenbereich (Durchgänge
 * usw.) (top story ceilings and ceilings above outdoor areas (passageways,
 * etc.))
 *
 * We consider these ROOF values!
 *
 */
const GERMAN_ROOFS: UValue[] = [
  // Solid
  new UValue('Massiv', 2.1, 'e8007e5e-639b-47fa-b2f1-cf53be4d250a', {
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massiv', 0.6, 'ddab9c35-157e-453a-8284-adb3a3fa9009', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massiv', 0.5, 'd47dd3c0-29e9-4992-8a48-c9b50fbe0f32', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massiv', 0.4, '1f398dba-6a08-4af7-8f7f-71aa9fd0b6ac', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Massiv', 0.3, '8232bfc7-35a2-4cfa-82fb-58e20fee88aa', {
    fromYearInclusive: 1995,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Wooden beam ceiling
  new UValue('Holzbalkendecke', 1.0, 'eb2d03fd-c54c-4b19-9822-a9c8411f9a59', {
    toYearInclusive: 1918,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.8, 'a1d64a35-43d1-4038-b969-b293747d88ce', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.6, '6113427a-5340-4bde-8427-059b621077e8', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.4, 'c3b85550-279d-44f7-9662-093c3b72585b', {
    fromYearInclusive: 1973,
    toYearInclusive: 1983,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  new UValue('Holzbalkendecke', 0.3, 'bcb15e21-fe17-4ecd-bf12-e873fb72de29', {
    fromYearInclusive: 1984,
    source: UValueSource.DIN12831_TABLE_B_15,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2001
  new UValue('Dach (Änderung der Energieeinsparverordnung)', 0.25, 'f9e4329e-1114-4349-b5da-a79a1a2bccc5', {
    fromYearInclusive: 2002,
    toYearInclusive: 2008,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
  // Source: Bundesgesetzblatt Jahrgang 2009 teil I Nr. 23
  new UValue('Dach (Änderung der Energieeinsparverordnung)', 0.24, '600efaf7-b4e9-484d-9e31-2ef203c1e68d', {
    fromYearInclusive: 2009,
    toYearInclusive: 2013,
    source: UValueSource.GERMAN_LAW_GAZETTE_2009,
  }),
  // Sources: Bundesgesetzblatt Jahrgang 2013 Teil I Nr. 67; § 7 Absatz 1 Nummer
  // 2 in Verbindung mit NummerVII.1 und 2
  new UValue('Dach (Änderung der Energieeinsparverordnung)', 0.2, '12a91dc6-576c-4c64-af08-4f5222759559', {
    fromYearInclusive: 2014,
    toYearInclusive: 2015,
    source: UValueSource.GERMAN_LAW_GAZETTE_2013,
  }),
  // Source: EnEV
  new UValue('Dach (Änderung der Energieeinsparverordnung)', 0.15, 'e2138577-6e28-43d9-844d-c62c3497f33c', {
    fromYearInclusive: 2016,
    source: UValueSource.EN_EV_2009,
  }),
];

const GERMAN_INTERNAL_WALLS: UValue[] = [
  ...GLOBAL_INTERNAL_WALLS,
  // Bundesgesetzblatt Jahrgang 2001
  new UValue('Wände', 0.5, 'e2cd4c01-5e0b-4faf-a727-f212cd7922c4', {
    fromYearInclusive: 2002,
    source: UValueSource.GERMAN_LAW_GAZETTE_2001,
  }),
];

export const GERMANY_U_VALUES: UValues = {
  doors: GERMAN_DOORS,
  externalWalls: GERMAN_EXTERNAL_WALLS,
  internalWalls: GERMAN_INTERNAL_WALLS,
  partyWalls: GERMAN_EXTERNAL_WALLS,
  floors: GERMAN_FLOORS,
  foundation: GERMAN_FLOORS,
  intermediateFloors: GERMAN_FLOORS,
  roof: GERMAN_ROOFS,
  roofsOrCeilings: [...GERMAN_CEILINGS, ...GERMAN_FLOORS],
  roofGlazings: GERMAN_ROOF_GLAZINGS,
  windows: GERMAN_WINDOWS,
};
