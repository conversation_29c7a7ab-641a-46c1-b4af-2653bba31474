/**
 * U-Values for dwellings in England and Wales.
 *
 * Note: Scotland define their own u-values, but for now, we apply those for
 *  England and Wales also to Scotland due to there being little difference.
 *
 * These values are taken from the following documents:
 *  - If the Building Regulations for that particular year specify a value
 *     for the given surface, then we use that value. More details in:
 *     https://wiki.airahome.com/s/aira/p/heat-design-faq-O0kwJkgb1t
 *  - If the building regulations do not have a value for that year and
 *     surface, then we use the estimates given by BS 12831 B.4.3.1 table B.15.
 *     Note that these are the same as the German u-values.
 * */

import { UValue } from '../models/UValue';
import { UValues } from '../stores/types';
import { BASE_U_VALUES } from './global-u-values';
import { UValueSource } from './u-value-sources';

const UK_GLAZINGS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue('Wooden frame, single glazing - BS EN 12831 estimation', 5, '686897b6-7e7c-4b8b-824f-40f7fb8a267c', {
    toYearInclusive: 1976,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden frame, double glazing - BS EN 12831 estimation', 2.7, 'c416d4cb-bd8c-4dc4-bbad-21bb208a02e3', {
    toYearInclusive: 1976,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Plastic frame, insulating glazing - BS EN 12831 estimation', 3, '98ecd0ae-8445-4db0-b253-5883d1486a30', {
    toYearInclusive: 1976,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Metal frame, insulating glazing - BS EN 12831 estimation', 4.3, '337342f3-e4b0-4bb9-a321-a737f7b71002', {
    toYearInclusive: 1976,
    source: UValueSource.BS12831_TABLE_B_15,
  }),

  // Building regulation u-values ---------------------------------------------
  new UValue('Single-glazing - building regulations for new-builds', 5.7, 'e1a9b218-1601-4788-a0c7-c33e16cd175d', {
    fromYearInclusive: 1977,
    toYearInclusive: 1985,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Double-glazing - building regulations for new-builds', 2.8, '4481a6a7-a29d-4b5a-98e1-ba71edfb7a39', {
    fromYearInclusive: 1977,
    toYearInclusive: 1985,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Treble-glazing - building regulations for new-builds', 2.0, 'fbd7b2d2-04d2-49ad-b216-03099db65010', {
    fromYearInclusive: 1982,
    toYearInclusive: 1985,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Building regulations for new-builds', 5.7, 'efb77526-1110-4cd9-84fc-6c43831c7a81', {
    fromYearInclusive: 1985,
    toYearInclusive: 1990,
    source: UValueSource.UK_BUILDING_REGULATIONS_1985,
  }),
  new UValue('Building regulations for new-builds (with SAP ≤ 60)', 3, '1cd4d769-eaa7-4adf-b63e-00a0138c12d9', {
    fromYearInclusive: 1995,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Building regulations for new-builds (with SAP > 60)', 3.3, 'ebbfce51-454a-4d5d-984c-1adb9ddde94d', {
    fromYearInclusive: 1995,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Metal frame - building regulations for new-builds', 2.5, '2bd2bb74-972b-4b78-90a0-30578f896beb', {
    fromYearInclusive: 2002,
    toYearInclusive: 2006,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Non-metal frame - building regulations for new-builds', 2, '92894dd3-28c7-4f22-9020-a73ff90af8b3', {
    fromYearInclusive: 2002,
    toYearInclusive: 2006,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Building regulations for new-builds', 2.2, '9e19465f-9433-46d6-a2c0-4c5f42f8e1bc', {
    fromYearInclusive: 2006,
    toYearInclusive: 2010,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2006,
  }),
  new UValue('Building regulations for new-builds', 2, 'e6a8b610-e8cf-4628-ab57-1ffe903baf62', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2010,
  }),
  new UValue('Building regulations for new-builds', 1.4, '4e342522-070d-4832-90db-d0dcc3a9ff17', {
    fromYearInclusive: 2014,
    toYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
  new UValue('Building regulations for new-builds', 1.2, '5c63670e-dfb2-42ff-87a5-8c2118243a64', {
    fromYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2021,
  }),
];

const UK_DOORS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue('BS EN 12831 estimation', 3.5, 'a1c46b63-4e98-4193-ad41-fe4ef0e91722', {
    toYearInclusive: 1994,
    source: UValueSource.BS12831_TABLE_B_15,
  }),

  // Building regulation u-values ---------------------------------------------
  new UValue('Building regulations for new-builds (with SAP ≤ 60)', 3, 'ac765cdc-f44e-43f4-ac37-375759ee85ca', {
    fromYearInclusive: 1995,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Building regulations for new-builds (with SAP > 60)', 3.3, 'c9727364-f2b7-4f94-88a9-bc7c188cf6f8', {
    fromYearInclusive: 1995,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Metal frame - building regulations for new-builds', 2.5, '69114303-b051-4931-aa93-30a1d8e2ce7c', {
    fromYearInclusive: 2002,
    toYearInclusive: 2006,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Non-metal frame - building regulations for new-builds', 2, '6277dd1c-40f7-4616-8ed3-80bc6b69e5f4', {
    fromYearInclusive: 2002,
    toYearInclusive: 2006,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Building regulations for new-builds', 2.2, '3a7dd884-60e5-4144-9a87-9b4aef2bedc4', {
    fromYearInclusive: 2006,
    toYearInclusive: 2010,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2006,
  }),
  new UValue('Building regulations for new-builds', 2, 'ac157b91-dc17-432d-94e2-484860eca014', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2010,
  }),
  new UValue('Opaque - building regulations for new-builds', 1, 'cb74cdf2-62c4-460f-9a43-b158e7506ffe', {
    fromYearInclusive: 2014,
    toYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
  new UValue('Semi-glazed - building regulations for new-builds', 1.2, '56ae64d6-935f-43fa-ad53-52c66d075bf1', {
    fromYearInclusive: 2014,
    toYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
  new UValue('Building regulations for new-builds', 1, '35ec0267-ecdb-4eb5-b0f0-e206aae85e16', {
    fromYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2021,
  }),
];

const UK_EXTERNAL_WALLS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1.7,
    '7becb818-360c-4214-86d7-23485f0386ba',
    {
      toYearInclusive: 1948,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1.4,
    '26ce26cb-9e4e-4094-93ba-938c49226137',
    {
      fromYearInclusive: 1949,
      toYearInclusive: 1968,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1,
    'b6e5a35a-600c-458b-8fe7-398255365727',
    {
      fromYearInclusive: 1969,
      toYearInclusive: 1971,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Wooden construction (timber frame, prefabricated or similar) - BS EN 12831 estimation',
    2,
    '8c55476f-57cc-4e6b-8253-55b7660362e0',
    {
      toYearInclusive: 1948,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Wooden construction (timber frame, prefabricated or similar) - BS EN 12831 estimation',
    1.4,
    '76f0a15b-8629-47cc-901e-0c0d4a64ec91',
    {
      fromYearInclusive: 1949,
      toYearInclusive: 1968,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Wooden construction (timber frame, prefabricated or similar) - BS EN 12831 estimation',
    0.6,
    '3f16d7ec-04f5-49a3-a680-b5ba24691943',
    {
      fromYearInclusive: 1969,
      toYearInclusive: 1971,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),

  // Building regulation u-values ---------------------------------------------
  new UValue('Building regulations for new-builds', 1.7, '316cb3ce-c8d8-4275-9d8d-6f6aeb7006d2', {
    fromYearInclusive: 1972,
    toYearInclusive: 1977,
    source: UValueSource.UK_BUILDING_REGULATIONS_1972,
  }),
  new UValue('Building regulations for new-builds', 1, '4a0ff00a-e7ae-4ba9-a391-2fe8215a3c66', {
    fromYearInclusive: 1977,
    toYearInclusive: 1982,
    source: UValueSource.UK_BUILDING_REGULATIONS_1976,
  }),
  new UValue('Building regulations for new-builds', 0.6, 'd09146f5-199e-4e8d-904a-d650f9e10984', {
    fromYearInclusive: 1982,
    toYearInclusive: 1990,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Building regulations for new-builds', 0.45, '15d3221f-3616-4744-8c82-ff5090e823c0', {
    fromYearInclusive: 1990,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Building regulations for new-builds', 0.35, 'ee0a7c62-a270-4a28-b97a-985a223a4286', {
    fromYearInclusive: 2002,
    toYearInclusive: 2010,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Building regulations for new-builds', 0.3, 'cc7f8fa5-8b0f-4887-8754-ffe1a401b6b5', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2010,
  }),
  new UValue('Building regulations for new-builds', 0.18, '2e6ca07f-0b92-4e85-9194-b5ccce601a71', {
    fromYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
];

/**
 * The estimated values from BS 12831 are labelled as "Ceilings against ground or unheated cellars",
 * like has been done in the germany-u-values.ts, we consider these as floor values.
 */
const UK_FLOORS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1.2,
    '94beb08d-f98f-4553-bed2-b3ea567902da',
    {
      toYearInclusive: 1948,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1.5,
    'df769263-932f-4fdd-bea2-cad8957451ae',
    {
      fromYearInclusive: 1949,
      toYearInclusive: 1957,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    1.0,
    'e56c61e4-ee0b-4b39-ac15-2e108ca88633',
    {
      fromYearInclusive: 1958,
      toYearInclusive: 1971,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue('Wooden beam - BS EN 12831 estimation', 1.0, 'dd871760-3f9f-429c-9b97-d0c8e8ce4895', {
    toYearInclusive: 1918,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden beam - BS EN 12831 estimation', 0.8, '652534df-ccad-405e-8218-8d7c6e527e05', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden beam - BS EN 12831 estimation', 0.6, '31e76ce1-6db8-479d-ac82-13b45d094823', {
    fromYearInclusive: 1969,
    toYearInclusive: 1971,
    source: UValueSource.BS12831_TABLE_B_15,
  }),

  // Building regulation u-values ---------------------------------------------
  new UValue('Building regulations for new-builds', 1.42, '18d596b7-7e79-44dc-a224-cc7205b339b6', {
    fromYearInclusive: 1972,
    toYearInclusive: 1977,
    source: UValueSource.UK_BUILDING_REGULATIONS_1972,
  }),
  new UValue('Building regulations for new-builds', 1, '1f92678f-60e8-47e7-b99f-b905b9630bad', {
    fromYearInclusive: 1977,
    toYearInclusive: 1982,
    source: UValueSource.UK_BUILDING_REGULATIONS_1976,
  }),
  new UValue('Building regulations for new-builds', 0.6, '638b8068-dff9-4cd1-af56-4478db32148e', {
    fromYearInclusive: 1982,
    toYearInclusive: 1990,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Building regulations for new-builds', 0.45, 'f9e093af-e65d-4dea-ad93-b84c8f6e4ebb', {
    fromYearInclusive: 1990,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Building regulations for new-builds', 0.25, 'c99a1fd8-6351-4d7e-afe1-b3a29d70764f', {
    fromYearInclusive: 2002,
    toYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue('Building regulations for new-builds', 0.13, '03e3078f-17f4-44f0-a995-67ffdcc880b3', {
    fromYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
];

/**
 * The estimated values from BS 12831 are labelled as "Roofs and walls between heated and unheated attics",
 * like has been done in the germany-u-values.ts, we consider these as roof values.
 */
const UK_CEILINGS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue('Solid construction - BS EN 12831 estimation', 2.1, '388c41a6-253e-4294-83c7-0273fbf0cb12', {
    toYearInclusive: 1968,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Solid construction - BS EN 12831 estimation', 0.6, '9631d2aa-dc4c-41d4-aa31-ad21d34e167c', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Solid construction - BS EN 12831 estimation', 0.5, '8d52bf42-3c0d-42eb-87d1-e7f06932d005', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Solid construction - BS EN 12831 estimation', 0.4, '7e45591a-8fdf-44c1-a910-5d4159ba782e', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Solid construction - BS EN 12831 estimation', 0.3, 'd9722dce-f80e-4a75-a3ad-1bbb6bf5f858', {
    fromYearInclusive: 1995,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 2.6, 'ea720470-7a81-4e06-b438-ef69513125a3', {
    toYearInclusive: 1918,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 1.4, 'dfcae97b-13d8-4be8-8efa-48bc58137805', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 0.8, '497b669b-c6c0-435c-ab1c-2aad78454f05', {
    fromYearInclusive: 1969,
    toYearInclusive: 1978,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 0.5, 'd9b5bc83-1bc9-420b-9dfa-dac2c2f7c42f', {
    fromYearInclusive: 1979,
    toYearInclusive: 1983,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 0.4, 'a19701a1-c175-4be2-9566-463452368410', {
    fromYearInclusive: 1984,
    toYearInclusive: 1994,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden construction - BS EN 12831 estimation', 0.3, 'c42423a1-b094-4a77-9183-307d27385f4c', {
    fromYearInclusive: 1995,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
];

/**
 * The estimated values from BS 12831 are labelled as "Top story ceilings and ceilings above ambient
 * (passageways, etc.)", like has been done in the germany-u-values.ts, we consider these as roof values.
 */
const UK_ROOFS: UValue[] = [
  // Estimates of u-values ----------------------------------------------------
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    2.1,
    'e8007e5e-639b-47fa-b2f1-cf53be4d250a',
    {
      toYearInclusive: 1968,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue(
    'Solid construction (masonry, concrete or similar) - BS EN 12831 estimation',
    0.6,
    'ddab9c35-157e-453a-8284-adb3a3fa9009',
    {
      fromYearInclusive: 1969,
      toYearInclusive: 1971,
      source: UValueSource.BS12831_TABLE_B_15,
    },
  ),
  new UValue('Wooden beam - BS EN 12831 estimation', 1.0, 'eb2d03fd-c54c-4b19-9822-a9c8411f9a59', {
    toYearInclusive: 1918,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden beam - BS EN 12831 estimation', 0.8, 'a1d64a35-43d1-4038-b969-b293747d88ce', {
    fromYearInclusive: 1919,
    toYearInclusive: 1968,
    source: UValueSource.BS12831_TABLE_B_15,
  }),
  new UValue('Wooden beam - BS EN 12831 estimation', 0.6, '6113427a-5340-4bde-8427-059b621077e8', {
    fromYearInclusive: 1969,
    toYearInclusive: 1971,
    source: UValueSource.BS12831_TABLE_B_15,
  }),

  // Building regulation u-values ---------------------------------------------
  new UValue('Building regulations for new-builds', 1.42, '07168aac-6af0-493e-ae6c-1158110bf9a1', {
    fromYearInclusive: 1972,
    toYearInclusive: 1977,
    source: UValueSource.UK_BUILDING_REGULATIONS_1972,
  }),
  new UValue('Building regulations for new-builds', 0.6, '5a1c3344-ceed-4cb2-864a-c359c99b62c9', {
    fromYearInclusive: 1977,
    toYearInclusive: 1982,
    source: UValueSource.UK_BUILDING_REGULATIONS_1976,
  }),
  new UValue('Building regulations for new-builds', 0.35, '85dfe097-4127-4989-bde5-c3878088d083', {
    fromYearInclusive: 1982,
    toYearInclusive: 1990,
    source: UValueSource.UK_BUILDING_REGULATIONS_1981,
  }),
  new UValue('Building regulations for new-builds (with SAP ≤ 60)', 0.2, '2aaf75fc-b6b2-49a2-8232-75c9385458d7', {
    fromYearInclusive: 1990,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1995,
  }),
  new UValue('Building regulations for new-builds (with SAP > 60)', 0.25, '53a290c8-91e7-4af4-b62a-99057dca7813', {
    fromYearInclusive: 1990,
    toYearInclusive: 2002,
    source: UValueSource.UK_BUILDING_REGULATIONS_1991_EDITION_1992,
  }),
  new UValue('Flat - building regulations for new-builds', 0.25, '5a086fa1-56e3-457b-919c-1e881173a885', {
    fromYearInclusive: 2002,
    toYearInclusive: 2006,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
  }),
  new UValue(
    'Pitched with insulation between rafters - building regulations for new-builds',
    0.2,
    '087e7589-4db3-49ff-bea2-e3b7fb23b565',
    {
      fromYearInclusive: 2002,
      toYearInclusive: 2006,
      source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
    },
  ),
  new UValue(
    'Pitched with integral insulation - building regulations for new-builds',
    0.2,
    '5cb4ae29-3d2f-47ab-89db-7729659fda54',
    {
      fromYearInclusive: 2002,
      toYearInclusive: 2006,
      source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
    },
  ),
  new UValue(
    'Pitched with insulation between joists - building regulations for new-builds',
    0.16,
    '72787107-a1d7-4f95-9758-43853204ddc8',
    {
      fromYearInclusive: 2002,
      toYearInclusive: 2006,
      source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2002,
    },
  ),
  new UValue('Building regulations for new-builds', 0.25, '2c4e3388-815a-4fcd-8035-d47685af2990', {
    fromYearInclusive: 2006,
    toYearInclusive: 2010,
    source: UValueSource.UK_BUILDING_REGULATIONS_2000_EDITION_2006,
  }),
  new UValue('Building regulations for new-builds', 0.2, '881ff812-4c62-4496-807d-f717d6efcd08', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2010,
  }),
  new UValue('Building regulations for new-builds', 0.13, '329b6b1c-5d0d-4e34-98f5-dfc56ff08498', {
    fromYearInclusive: 2014,
    toYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2013,
  }),
  new UValue('Building regulations for new-builds', 0.11, '1aff3fd8-6974-45c9-9c3d-3a3b032fadd9', {
    fromYearInclusive: 2022,
    source: UValueSource.UK_BUILDING_REGULATIONS_2010_EDITION_2021,
  }),
];

export const UK_U_VALUES: UValues = {
  doors: [...BASE_U_VALUES.doors, ...UK_DOORS],
  externalWalls: [...BASE_U_VALUES.externalWalls, ...UK_EXTERNAL_WALLS],
  internalWalls: BASE_U_VALUES.internalWalls,
  partyWalls: [...BASE_U_VALUES.partyWalls, ...UK_EXTERNAL_WALLS],
  floors: [...BASE_U_VALUES.floors, ...UK_FLOORS],
  foundation: [...BASE_U_VALUES.foundation, ...UK_FLOORS],
  intermediateFloors: [...BASE_U_VALUES.intermediateFloors, ...UK_FLOORS],
  roof: [...BASE_U_VALUES.roof, ...UK_ROOFS],
  roofsOrCeilings: [...BASE_U_VALUES.roofsOrCeilings, ...UK_CEILINGS, ...UK_ROOFS],
  roofGlazings: [...BASE_U_VALUES.roofGlazings, ...UK_GLAZINGS],
  windows: [...BASE_U_VALUES.windows, ...UK_GLAZINGS],
};
