import { UValue } from '../models/UValue';
import { UValues } from '../stores/types';
import { BASE_U_VALUES, ItalyClimateZone } from './global-u-values';
import { UValueSource } from './u-value-sources';

const ITALY_WINDOWS: UValue[] = [
  // Generic windows for all climate zones
  new UValue('Chiusure trasparenti, vetro singolo', 5.1, '5c47df99-1abb-4496-953e-cc8797bf78e5', {
    toYearInclusive: 1990,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, vetro singolo', 4.8, '6b36d246-de4f-4df7-8009-7b7210f137fe', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, vetro singolo', 5.7, 'b8f5a28e-50cd-46e8-80ca-11faa615b3f1', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, doppio vetro', 2.8, 'e28e072b-cf03-451f-8713-8ea98f05214b', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, doppio vetro', 3.4, 'b9eb03f8-6f9d-4ebc-93c4-8f486c25bdb5', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, doppio vetro, basso emissivo', 2.3, '8cd83af1-4e84-4a3c-8504-9513d017cb22', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, doppio vetro, basso emissivo Argon', 2.1, 'a2dc0b90-b83e-4285-89a3-924d6e0cf4af', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, doppio vetro, basso emissivo', 2.8, '522ea651-509f-4229-a283-932c92cab111', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, doppio vetro, basso emissivo Argon', 2.6, '554f127a-2908-4cc3-a130-11749a560df2', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, triplo vetro', 2.1, 'f3ee0e25-fd4d-47b3-9172-747042c9513a', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, triplo vetro', 2.6, '6a1584d5-16d7-44b5-97af-92feb59163f1', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, triplo vetro, basso emissivo', 1.7, '07ed0d83-8240-4415-aafd-250d00c231a1', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in legno/PVC, triplo vetro, basso emissivo Argon', 1.6, '013c70bd-4143-40f2-a293-1ab48a5e6e6a', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, triplo vetro, basso emissivo', 2.1, '7f8c3308-4fea-4cbc-be19-07737b626dd9', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Telaio in alluminio, triplo vetro, basso emissivo Argon', 2.0, 'daca8b57-03f4-46a6-a501-b523862099a6', {
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Double-pane windows, one for each climate zone
  new UValue('Chiusure trasparenti, vetro doppio', 5.0, '7a87bc2f-ea9f-4b23-86c9-65768f8d3c38', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure trasparenti, vetro doppio', 4.0, '3f0df0e2-f906-4bf6-8fed-e7dca143c018', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure trasparenti, vetro doppio', 3.6, '342ab72f-b1f5-48fd-bf86-ae9882a8166e', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure trasparenti, vetro doppio', 3.4, '28ba2148-8388-468f-8807-8346ec32a45f', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure trasparenti, vetro doppio', 3.2, '68b4b953-f20c-40b3-9683-ac1568b17629', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure trasparenti, vetro doppio', 3.1, 'a3556507-4857-476f-ad6c-c289f8cfbe32', {
    toYearInclusive: 1990,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Generic windows based on build year for climate zone A
  new UValue('Nuove costruzioni/ristrutturazioni', 5.0, 'b38e5ec0-993c-4d83-a55c-a723e2ab30ff', {
    fromYearInclusive: 2000,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 4.6, '6b467ce6-9fbb-4a7a-843a-0d8579fe8645', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.2, '9c1ab6c2-4470-47d1-9917-fd79f9426fe3', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.0, 'f0a462d8-df12-4253-a1cb-495f2bae2699', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  // Generic windows based on build year for climate zone B
  new UValue('Nuove costruzioni/ristrutturazioni', 4.0, '45a9a840-3875-4c73-9b1f-7f9d458499b2', {
    fromYearInclusive: 2000,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.6, '04677f6a-d873-413d-b759-f06f42b2eba9', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.0, 'a91cee5a-b17a-45be-86b6-dabdd153983f', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.2, '60a392c1-85cd-4022-9295-d2561801ad64', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.0, '7d49d4f1-e400-4826-a2f3-fbd6f179288d', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  // Generic windows based on build year for climate zone C
  new UValue('Nuove costruzioni/ristrutturazioni', 3.4, '76e9e6e6-0064-4071-9737-5a64908e7a14', {
    fromYearInclusive: 2000,
    toYearInclusive: 2005,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.3, 'c1bf449f-dac3-4d5c-b23f-6b1da15b2261', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.0, '4bd8d165-a03f-4e1e-bd60-6789ca8d8a93', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.6, 'fbf27c82-b123-4d2a-9a02-5261c31ff670', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.4, '0ac27c84-b931-4f69-8426-08edb3003a02', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.0, '655e5cfd-65f5-4a79-b126-16eb8ce0bd0f', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  // Generic windows based on build year for climate zone D
  new UValue('Nuove costruzioni/ristrutturazioni', 3.2, '56439797-d871-4d2f-a22e-6847bc7fd5ce', {
    fromYearInclusive: 2000,
    toYearInclusive: 2005,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 3.1, '7236748b-8c9e-47ff-8ef4-927262834bf5', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.8, 'f4aeb66c-8ce7-4404-8cae-0da65dc7eaa9', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.4, '1423c8eb-d937-4c31-96f1-9fc071c692cb', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.1, '0aad89ec-8314-4cb5-99b2-d809e7250b93', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 1.8, '2ca87999-642a-4ee4-8a2c-b23dc5fd4593', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  // Generic windows based on build year for climate zone E
  new UValue('Nuove costruzioni/ristrutturazioni', 3.0, 'e8d83714-920c-4b38-9e87-4296869c9d75', {
    fromYearInclusive: 2000,
    toYearInclusive: 2005,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.8, 'dd0bfb13-6695-473a-80a3-6ada716cecc2', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.4, '5d3ff7b3-f90c-4ad6-a12f-430df0648edb', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.2, '88867a6a-63c4-48e3-86d9-ecfeffbd5e14', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 1.9, '82cf7377-76fa-4608-8832-dc2095284e41', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 1.4, '827353fc-b566-484e-99e9-44c4209d4066', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  // Generic windows based on build year for climate zone F
  new UValue('Nuove costruzioni/ristrutturazioni', 2.6, '4120b9fa-fdb4-45f0-84fc-74508289ce1b', {
    fromYearInclusive: 2000,
    toYearInclusive: 2005,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.4, '7307a292-859b-45b7-a027-2aac577e3e63', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.2, '7f744dce-5323-4175-9181-681f55f7de16', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 2.0, '108b841d-b90c-4f16-8039-29321b5bc29c', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 1.7, 'e37fe6dc-e324-4654-8476-53108f6c5652', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
  new UValue('Nuove costruzioni/ristrutturazioni', 1.0, '4b5bd263-890f-4eb7-819c-4a41cfef317d', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_INTERMINISTERIAL_DECREE_2016,
  }),
];

const ITALY_DOORS: UValue[] = [
  // Wooden sash only (thickness approx. 35 mm)
  new UValue('Solo anta di legno (spessore circa 35 mm)', 3.5, 'c645df28-19c2-47d4-9827-703ea4732ca6', {
    source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE,
  }),
  // 'Wooden frame and sash (thickness approx. 35 mm)
  new UValue('Telaio e anta di legno (spessore circa 35 mm)', 3.5, '0e3080df-7808-4ccd-9750-33639c1fca57', {
    source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE,
  }),
  // Metal frame (with rebate), wooden sash (thickness approx. 35 mm)
  new UValue(
    'Telaio metallico (con battuta), anta di legno (spessore circa 35 mm)',
    4.2,
    'ad312c1b-2352-4734-89dc-6e42afa667e7',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Metal frame (inserted directly into wall), wooden sash (thickness approx. 35 mm)
  new UValue(
    'Telaio metallico (inserito direttamente nel muro), anta di legno (spessore circa 35 mm)',
    4.0,
    '6e982cb2-e0e6-4ddd-8052-3a63896513ef',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Wooden frame, wooden sash (approx. 35 mm thick) with glazed insert  (single-glazing)
  new UValue(
    'Telaio di legno, anta di legno (spessore circa 35 mm) con inserto vetrato (vetro singolo)',
    5.0,
    '24897968-07ac-471b-accc-674571af6305',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Metal frame (with rebate), wooden sash (approx. 35 mm thick) with glazed insert (single-glazing)
  new UValue(
    'Telaio di metallo (con battuta), anta di legno (spessore circa 35 mm) con inserto vetrato (vetro singolo)',
    6.4,
    '2069fab8-a1a5-4559-bc4a-69f04b201671',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Metal frame (inserted directly into wall), wooden sash (thickness approx. 35 mm) with glazed insert (single glazing)
  new UValue(
    'Telaio di metallo (inserito direttamente nel muro), anta di legno (spessore circa 35 mm) con inserto vetrato (vetro singolo)',
    5.7,
    '3ae47578-9fb9-4fd5-825e-fa104f8da67f',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Doors consisting of metal and metal panels with insulating core
  new UValue(
    'Porte costituite da metallo e pannelli di metallo con anima isolante',
    4.4,
    '5ede749f-7ab3-4808-86cc-aeb3d2adabce',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
  // Metal doors (frame without thermal break/sash made of a single metal panel)
  new UValue(
    'Porte di metallo (telaio senza taglio termico/anta costituita da un pannello singolo di metallo)',
    6.5,
    '8556e10a-e223-4442-a713-6e729e40add6',
    { source: UValueSource.ITALIAN_ESTIMATED_THERMAL_TRANSMITTANCE },
  ),
];

// "Chiusure opache orizzontali o inclinate di copertura" aka "Horizontal or
// inclined opaque covering closures" aka roofs
const ITALY_ROOFS: UValue[] = [
  // Values for all the climate zones
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 1.5, 'f821be9b-11d2-4a48-b007-1d8013673391', {
    toYearInclusive: 1994,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 1.0, '436872e7-c903-419b-9f32-e594555779e5', {
    fromYearInclusive: 1995,
    toYearInclusive: 2006,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone A
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.8, '0124f950-8c3d-4fc5-ac9b-4536197dad0c', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.42, '3ae96539-f6be-4e88-8ef4-b79938cf2ac3', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.38, 'ab448999-7324-48b2-bcdb-6ac6e78d9710', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.34, 'bda5a139-8128-44c8-94e4-e73d855edbda', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.32, 'c2083232-bd8c-470b-b4dc-1d712541dedd', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone B
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.6, '8d6cebbf-50d0-480d-868d-b52d2f51b1bc', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.42, '7cbe0ffd-d864-45a7-8662-c246a222c433', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.38, 'ed9c2fca-6905-4a00-ac87-15b3a3a1f976', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.34, 'df26e1b8-af51-4093-af4d-9f5f984c7276', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.32, 'afc77f21-547b-449d-bd71-4e394764c508', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone C
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.55, 'eaac908d-c867-4904-a25f-0bec139535ea', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.42, '1ddf8cf9-09ee-46d5-b943-45445d9234fd', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.38, '0da5dac6-5d71-4fec-b78a-f4bdf2aaad67', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.34, '711ba591-a551-4464-90df-6a07e7c96706', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.32, 'f79fe54e-b004-47cc-8082-11f7e75a4c29', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone D
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.46, 'fcd97a64-2dab-47a1-be9a-f279744460cf', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.35, '088b8329-49eb-4873-8b25-3840c3f970bb', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.32, 'eb628de1-8f0d-4721-9742-e0191d038d31', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.28, '8cbc0c2d-6025-41ff-bc58-1fffa9bb37ac', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.26, '4509b28c-6abf-4d83-b0cb-fd2c202d6698', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone E
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.43, '2cc164f0-0521-4d62-8afb-05d61645f76f', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.32, 'ce86bf29-959d-4a04-a2de-51fba9d5d944', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.3, '34c5f850-aab9-4b91-9999-d14f37a65a84', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.26, '9d76e5a4-12a9-4088-9b17-e10930880879', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.24, '4d929910-cd88-4657-84a2-01014f58068f', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone F
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.41, 'baa6aa6f-bab8-4695-9c7b-52ccc67e6c4a', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.31, '90b0a90a-d2bf-4021-92d1-126ffb5d0b0e', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.29, 'dacb5400-36b9-439e-af8b-dbde36ef6cc4', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.24, '446ca7c5-bea1-4601-bf6c-c8ff4a832e79', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache orizzontali o inclinate di copertura', 0.22, 'ffd1ef54-0034-41cf-a715-0327a4808cf0', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
];

// "Chiusure opache", aka "opaque closures" according to Google Translate. These are external walls.
const ITALY_EXTERNAL_WALLS: UValue[] = [
  // For all climate zones
  new UValue('Chiusure opache', 1.5, '773ca66a-8983-4288-9ab6-738325510a98', {
    toYearInclusive: 1994,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 1.0, '7a6700c2-80cf-4666-8a0f-3be883e9cdeb', {
    fromYearInclusive: 1995,
    toYearInclusive: 2005,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone A
  new UValue('Chiusure opache', 0.85, 'ebbe5b1c-d436-4171-b014-fa397527cafd', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.72, 'acba6286-fdbd-48d0-b0a3-0c36138f4f32', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.62, '327227d0-b711-43d3-ae32-7b5c38bf347c', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.45, '62e2ae81-12bd-46fb-9afa-2c5b16a2cee5', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.4, 'bfa97f0a-43f6-4052-abfc-d0e9d31f8085', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone B
  new UValue('Chiusure opache', 0.64, 'b77b3e54-5240-4c6d-bd32-294ca18ee035', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.54, '6e62af8b-1427-4203-b75f-4cfcb7fad343', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.48, '917e5bb8-dc11-4fc2-9b95-9d9fb2be062a', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.45, 'a4b8880f-eb99-4e6d-9f08-7587efda59c6', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.4, 'bffd3e18-da42-4f1c-9abf-b6f429abc0b1', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone C
  new UValue('Chiusure opache', 0.57, '55181a1d-8b9f-4478-b30f-e19a70945bd7', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.46, '252348b2-f1cb-4950-b273-09553461173e', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.4, '835cd5af-912e-4469-96a8-855049bac1a4', {
    fromYearInclusive: 2010,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.36, '5d356a91-b141-4364-b930-9e50dbe0e9f6', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone D
  new UValue('Chiusure opache', 0.5, 'df565c77-12ab-4909-a658-4947173d963a', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.4, '6927e85f-2459-481e-b689-8c6b3c41b4a3', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.36, 'a582cdb5-4253-4162-99f4-e86a0b67f17f', {
    fromYearInclusive: 2010,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.32, 'a3e5fbdd-866b-45c9-bd1f-888e701500f0', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone E
  new UValue('Chiusure opache', 0.46, '9758b4a4-8dba-4116-9e0a-ca49fa72892b', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.37, '80ba261b-e59b-4a11-8e62-072688f43312', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.34, '3326c5a5-8d57-453c-bd8a-3e89136f8472', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.3, 'c3ca6cfa-5ea1-45bc-aaa3-7acd305f391c', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.28, '021f42fe-8b43-49f9-87f2-4b370d3ed0f4', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone F
  new UValue('Chiusure opache', 0.44, '14854710-9993-47a8-a411-168e74115af1', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.35, 'dc0af5ff-c1fc-412e-a67b-4371e5493db5', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.33, 'eb3c857a-239e-48d6-9762-70c040a7c384', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.28, '5b4d46c7-e4dd-4487-8648-b7c4147f0ad7', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache', 0.26, 'cf9bedda-cf8f-4104-b13f-64956b611ac7', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
];

// "Chiusure opache pavimento" aka "opaque floor closures" aka floors. These
// values are specifically for floors against soil.
const ITALY_FLOORS_AGAINST_SOIL: UValue[] = [
  // All climate zones
  new UValue('Chiusure opache pavimento', 1.5, '30f68078-14fb-4d9f-8588-843f6814c95b', {
    toYearInclusive: 1994,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 1, '7457ab4c-385a-41f6-a759-57808bf62123', {
    fromYearInclusive: 1995,
    toYearInclusive: 2005,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone A
  new UValue('Chiusure opache pavimento', 0.8, 'e8d70420-3b8e-4c18-96d1-3c5a02ab48e5', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.74, '98586a83-d693-42a6-b7dd-359a3b2dac4e', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.65, '0278f27c-daf3-40c4-a6b5-d35d1e104ec5', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.48, 'e4e697df-0eaa-46d2-81cd-3794c7ce8e57', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.42, '93a4f8e9-cdab-4c4b-adb1-9fa902233ae5', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.A,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone B
  new UValue('Chiusure opache pavimento', 0.6, 'd72d1821-769d-43ab-8d8d-c2b6b44fae6e', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.55, '4d464b7b-05b5-4d05-9b58-59de73813fd2', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.49, 'aa9da71e-86fb-4528-b246-f826e03deee4', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.48, 'fba0f99a-ef24-4068-8d6d-bbc56fe7e7f8', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.42, '8ea3f265-3f2f-46ed-8c13-de76bd491967', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.B,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone C
  new UValue('Chiusure opache pavimento', 0.55, 'fef7b903-057d-45e2-aeb5-c63091b01702', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.49, 'b1521f11-f5fb-4831-aa79-00e951146d1c', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.42, 'f7d38664-0a58-4998-9d0a-6d0b63ead7fc', {
    fromYearInclusive: 2010,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.38, 'a7dfd9e5-d373-48e9-ad02-822746ecb45f', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.C,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone D
  new UValue('Chiusure opache pavimento', 0.46, 'acac2117-d4d8-40e3-a437-18ea3d59a108', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.41, '1f7aceb1-036e-4cbc-a9e1-8f88001847b3', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.36, 'a1de65e8-e8f0-4c4e-bbbf-d99dd467aaeb', {
    fromYearInclusive: 2010,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.32, '459e161d-da88-40e8-bbc6-3e8861aae0e9', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.D,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone E
  new UValue('Chiusure opache pavimento', 0.46, 'f460c2dc-3cfc-41a6-9f22-26d947f08e09', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.41, '70914baa-cfed-47ef-b10e-5945e16867b0', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.36, '5fe7d2fa-6699-4bf5-a8e7-9b940a326d87', {
    fromYearInclusive: 2010,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.32, '076b1dc3-b2ab-41df-a68c-fd97ac0aa163', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone E
  new UValue('Chiusure opache pavimento', 0.43, 'ba2b5342-5d38-4eb1-8171-9a0527a48957', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.38, '57e18fa2-4e25-4449-b054-36951acd8c9d', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.33, '4d12c54f-4baa-4deb-b551-474658bb4194', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.31, '31c6aed0-a488-43d6-9296-f4726d70f4d4', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.29, 'da568582-d31c-4def-bfdc-dcbd19575761', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.E,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  // Climate zone F
  new UValue('Chiusure opache pavimento', 0.41, '22d28027-10f8-4ebd-9038-afb6e3eb77a9', {
    fromYearInclusive: 2006,
    toYearInclusive: 2007,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.36, '15b0fb8c-2903-4aec-89c4-2540bd89dbfa', {
    fromYearInclusive: 2008,
    toYearInclusive: 2009,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.32, '1fe3daf1-131c-4264-9671-77a7d7fad1ad', {
    fromYearInclusive: 2010,
    toYearInclusive: 2014,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.3, '13762c91-c97b-4360-932d-9bf33d93528d', {
    fromYearInclusive: 2015,
    toYearInclusive: 2020,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
  new UValue('Chiusure opache pavimento', 0.28, '9de66120-7604-4451-aba2-b498c2409ebd', {
    fromYearInclusive: 2021,
    climateZone: ItalyClimateZone.F,
    source: UValueSource.ITALIAN_LEGISLATIVE_DECREE_192_311,
  }),
];

export const ITALY_U_VALUES: UValues = {
  doors: ITALY_DOORS,
  externalWalls: ITALY_EXTERNAL_WALLS,
  internalWalls: BASE_U_VALUES.internalWalls,
  partyWalls: ITALY_EXTERNAL_WALLS,
  floors: [...BASE_U_VALUES.intermediateFloors, ...ITALY_FLOORS_AGAINST_SOIL],
  foundation: ITALY_FLOORS_AGAINST_SOIL,
  intermediateFloors: BASE_U_VALUES.intermediateFloors,
  roof: ITALY_ROOFS,
  roofsOrCeilings: BASE_U_VALUES.roofsOrCeilings,
  roofGlazings: BASE_U_VALUES.roofGlazings,
  windows: ITALY_WINDOWS,
};
