/**
 *                                 ██
 *                               ██░░██
 *                             ██░░░░░░██
 *                           ██░░░░░░░░░░██
 *                           ██░░░░░░░░░░██
 *                         ██░░░░░░░░░░░░░░██
 *                       ██░░░░░░██████░░░░░░██
 *                       ██░░░░░░██████░░░░░░██
 *                     ██░░░░░░░░██████░░░░░░░░██
 *                     ██░░░░░░░░██████░░░░░░░░██
 *                   ██░░░░░░░░░░██████░░░░░░░░░░██
 *                 ██░░░░░░░░░░░░██████░░░░░░░░░░░░██
 *                 ██░░░░░░░░░░░░██████░░░░░░░░░░░░██
 *               ██░░░░░░░░░░░░░░██████░░░░░░░░░░░░░░██
 *               ██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
 *             ██░░░░░░░░░░░░░░░░██████░░░░░░░░░░░░░░░░██
 *             ██░░░░░░░░░░░░░░░░██████░░░░░░░░░░░░░░░░██
 *           ██░░░░░░░░░░░░░░░░░░██████░░░░░░░░░░░░░░░░░░██
 *           ██░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░██
 *             ██████████████████████████████████████████
 *
 *
 * These U-Values are shared across different countries. Be careful when making
 * changes!
 *
 * DO NOT DELETE U-VALUES. Mark them as deprecated instead.
 *
 */
import { UValue } from '../models/UValue';
import { UValues } from '../stores/types';
import { UValueSource } from './u-value-sources';

/** Italy has 6 climate zones that can have different temperatures, U-values, etc. */
export enum ItalyClimateZone {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
  E = 'E',
  F = 'F',
}

export const GLOBAL_GLAZINGS: UValue[] = [
  new UValue('Single Glazing Wood/PVC frame', 4.8, '6e0ce469-1ebb-4dcf-bdd5-c100e9ccf064', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Single Glazing Metal frame', 5.7, '96688a2c-cda4-4405-ac9c-68e6a1d98214', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Standard Double Glazing Wood/PVC frame', 2.8, 'd7548da6-337c-49cc-9b05-743dde5fc7ba', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Standard Double Glazing Metal frame', 3.4, 'dc4a03aa-a66a-4cab-bd0f-df3ad5059287', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Double Glazing Low-E Glass, Wood/PVC frame', 2.3, 'd3a203ef-1138-4ec8-9bbc-41dca1d45c1a', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Double Glazing Low-E, Argon Filled Wood/PVC frame', 2.1, 'd2d098ad-653a-4654-9e43-e9e90b47a5c5', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Double Glazing Low-E Glass, Metal frame', 2.8, 'dd1c98fc-4784-4fe4-aa35-38aacb0177f1', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Double Glazing Low-E, Argon Filled Metal frame', 2.6, '47e91ea8-4575-44c4-8cdf-9dad9ce32483', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Wood/PVC frame', 2.1, 'cfc5165f-2ca1-46d9-8f03-54e8915e5650', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Metal frame', 2.6, 'f62d51f6-cfcb-4d30-af9c-8a8e00c5257d', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Low-E Glass, Wood/PVC frame', 1.7, 'ca918166-d17e-4407-8506-6fbd3caf0174', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Low-E, Argon Filled Wood/PVC frame', 1.6, 'a2d2f6d6-14e2-43c2-8327-fbf3184eabd1', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Low-E Glass, Metal frame', 2.1, 'e2bdddad-d4fc-4415-a8e3-ec1f327caebf', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Triple Glazing Low-E, Argon Filled Metal frame', 2, '0d5e4d70-a5b7-41b4-aade-ee9b82e62875', {
    source: UValueSource.UNKNOWN,
  }),
];

export const GLOBAL_INTERNAL_WALLS: UValue[] = [
  new UValue(
    '12.5 mm plasterboard, 75 mm studding, 12.5 mm plasterboard',
    1.92,
    '60f1fe40-0ad2-4084-9c61-27e5e45b935c',
    { source: UValueSource.DHDG2021_TABLE_18 },
  ),
  new UValue(
    '13 mm plaster, 100 mm block, cavity, 100 mm block, 13 mm plaster',
    1.02,
    '609f3d9b-6a0a-48df-b30d-3639dbe11b2c',
    { source: UValueSource.DHDG2021_TABLE_18 },
  ),
  new UValue('13 mm plaster, 102.5 mm brick, 13 mm plaster', 1.82, 'a9989fef-9dcc-404a-bd6b-34fa1c6dc7a1', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('3 mm plaster, 215 mm brick, 13 mm plaster', 1.37, '5e131ff1-a0fc-4f95-85a4-9aee5d387f05', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('13 mm plaster, 100 mm breeze block, 13 mm plaster', 1.43, '84effc0f-c2c6-4e4e-a258-34bc577c4875', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue(
    '13 mm plaster, 100 mm standard aerated block, 13 mm plaster',
    1.06,
    'b74b70bf-8454-404a-bf0b-06f583bc4208',
    { source: UValueSource.DHDG2021_TABLE_18 },
  ),
  new UValue(
    '13 mm plaster, 125 mm standard aerated block, 13 mm plaster',
    0.93,
    '67e29d1f-54a2-4f41-a22c-edb3de3314f7',
    { source: UValueSource.DHDG2021_TABLE_18 },
  ),
];

// DHDG2021
// Table 3.19 Roofs

const GLOBAL_ROOFS: UValue[] = [
  new UValue(
    'Boarding 19mm, airspace between joists, 100mm insulation, 6mm sheeting - heat flow downward exposed to outside air or unheated space',
    0.33,
    '4b664d5a-9b98-4685-b371-82e2598cb404',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Boarding 19mm, airspace between joists, 150mm insulation, 6mm sheeting - heat flow downward exposed to outside air or unheated space',
    0.23,
    '27feb705-f6b6-4715-97f2-4db239d96541',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Boarding 19mm, airspace between joists, no insulation, 6mm sheeting - heat flow downward exposed to outside air or unheated space',
    1.75,
    '3b8f6750-e201-4b32-a211-41ea9a7029ec',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Screed 50mm, concrete slab 150mm, 100mm insulation between battens, 6mm sheeting, heat flow downward - exposed to outside air or unheated space',
    0.57,
    '694c43be-d8dd-4f86-ba93-b19cd80adb2c',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Screed 50mm, concrete slab 150mm, no insulation between battens, 6mm sheeting, heat flow downward - exposed to outside air or unheated space',
    1.82,
    '086593fa-446c-4c15-b533-f37485dae6b0',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, ventilated air space, 100mm insulation between joists, 9.5 mm plasterboard',
    0.35,
    '8f698ea8-a75d-439b-b606-242c3b1b7530',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, ventilated air space, 200mm insulation between joists, 9.5 mm plasterboard',
    0.18,
    '209fb0f3-82ba-4656-b04d-9a31d224e061',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, ventilated air space, 300mm insulation between joists, 9.5 mm plasterboard',
    0.12,
    '498184ce-28c9-4d89-a744-043f2dda42ea',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, ventilated air space, 50mm insulation between joists, 9.5 mm plasterboard',
    0.62,
    '31080760-66ce-4a7c-ba85-d4af15aceb28',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, ventilated air space, no insulation, 9.5 mm plasterboard',
    3.13,
    '285b33ad-c4d0-4237-aaae-dcf81ea12106',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 100mm insulation between joists, 9.5 mm plasterboard',
    0.34,
    '6aaf9265-6c95-4bea-9829-254ecdcfe5c1',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 100mm insulation between rafters, 9.5 mm plasterboard',
    0.34,
    'ffa262b7-1f23-4fe3-9081-cf17524564ad',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 200mm insulation between joists, 9.5 mm plasterboard',
    0.18,
    '07f74159-0b5b-4fff-9089-8056931c9ddd',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 200mm insulation between rafters, 9.5 mm plasterboard',
    0.18,
    '65199b34-95d9-4ad6-8a02-dcdc86c72e55',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 300mm insulation between joists, 9.5 mm plasterboard',
    0.12,
    '14f8fcde-5d73-4f2c-8154-ef5e7cfb0c7c',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 300mm insulation between rafters, 9.5 mm plasterboard',
    0.12,
    'ba92419c-2989-4754-8792-85b57a5b096b',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 50mm insulation between joists, 9.5 mm plasterboard',
    0.6,
    'd383c22d-4b30-40cf-9fd5-5c6e6de683a9',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, 50mm insulation between rafters, 9.5 mm plasterboard',
    0.6,
    '10518285-ec9e-476a-bb29-f471315dfcdf',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Pitched roof - Slates or tiles, sarking felt, ventilated air space, no insulation, 9.5 mm plasterboard',
    2.51,
    'ed068901-6508-44ad-bff1-1eb110a0f3b8',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue('50mm Insulation', 0.6, '8c15c460-7ae6-4113-a5a3-b01f456bde6c', {
    source: UValueSource.DHDG2021_TABLE_19,
  }),
  new UValue('100mm Insulation', 0.34, 'af8e7126-78ad-4003-834b-259523efe9fb', {
    source: UValueSource.DHDG2021_TABLE_19,
  }),
  new UValue('U-Value', 0.1, '2f615323-79e7-48ee-aaf9-d9461ebea008', { source: UValueSource.DHDG2021_TABLE_19 }),
  new UValue('U-Value', 0.075, '80ac9bf6-9069-47c9-95c7-913971dbb338', { source: UValueSource.DHDG2021_TABLE_19 }),
  new UValue(
    'Chippings, 3 layers of felt, boarding, air space, 200mm insulation, 9.5 mm plasterboard',
    0.17,
    '82f7981c-5f7f-4994-85b3-0bfddff634e8',
    { source: UValueSource.DHDG2021_TABLE_19 },
  ),
  new UValue(
    'Chippings, 3 layers of felt, boarding, air space, 300mm insulation, 9.5 mm plasterboard',
    0.12,
    'e0adacc7-5c2b-495f-8585-96fa789d9dfd',
    {
      source: UValueSource.DHDG2021_TABLE_19,
    },
  ),
  new UValue(
    'Chippings, 3 layers of felt, boarding, air space, 100mm insulation, 9.5 mm plasterboard',
    0.32,
    '92044b9b-3608-4392-8cee-dd7fb86c85cb',
    {
      source: UValueSource.DHDG2021_TABLE_19,
    },
  ),
  new UValue(
    'Chippings, 3 layers of felt, boarding, air space, 50mm insulation, 9.5 mm plasterboard',
    0.53,
    'a20f404d-98ee-4758-b46e-f86f0a056445',
    {
      source: UValueSource.DHDG2021_TABLE_19,
    },
  ),
  new UValue(
    'Chippings, 3 layers of felt, boarding, air space, no insulation, 9.5 mm plasterboard',
    1.69,
    '47b900a6-a269-4595-9ab7-e8352001dc54',
    {
      source: UValueSource.DHDG2021_TABLE_19,
    },
  ),
];

const GLOBAL_FOUNDATIONS: UValue[] = [
  new UValue('Ground Floor No Insulation', 1.15, 'aa5b272b-2323-419b-8006-0511d555ffcf', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor 25mm Insulation', 0.62, 'b455e443-1fb3-45ce-b060-cab50a43b12f', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor 50mm Insulation', 0.43, '139b371a-f1f8-411a-8aa9-096f73d1b979', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor 75mm Insulation', 0.32, 'fcc6bfb3-52c7-4f46-91e2-a6075f482513', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor 100mm Insulation', 0.26, '6b35a88b-d81c-4815-9fe7-04e61ce3ec7f', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor U-Value', 0.2, '05451c46-1638-4b2c-908b-0df7f61b313c', {
    isDeprecated: true,
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor U-Value', 0.15, '305365e9-7391-496e-ae9b-da4517296344', {
    isDeprecated: true,
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Ground Floor U-Value', 0.1, 'f80636d0-c855-4911-b2c7-bfe78512f83c', {
    isDeprecated: true,
    source: UValueSource.UNKNOWN,
  }),
];

const GLOBAL_PARTY_WALLS: UValue[] = [
  new UValue('Plasterboard with Studding', 1.72, 'a1429ccc-56a5-4481-9357-4634a3c0baa4', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('Brick 102.5mm', 1.76, '4ee2d486-84f9-4777-8508-aecbeefa7108', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('Brick 215mm', 1.33, '1c7bf8b8-c333-4a52-9768-c52bc89f9a57', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('Standard Aerated Block 100mm', 1.66, 'f3129169-81ee-4314-b741-e46846922211', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('Standard Aerated Block 125mm', 1.53, 'e8e93e61-224d-4a68-9270-9a30a2be6244', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
  new UValue('100mm Block with Cavity', 1.02, '8e2016da-14de-4796-b615-2a3307d94576', {
    source: UValueSource.DHDG2021_TABLE_18,
  }),
];

const GLOBAL_FLOORS: UValue[] = [
  ...GLOBAL_FOUNDATIONS,
  new UValue('Party Floor Concrete with insulation', 0.57, '638fec08-3f20-49ff-96bf-ba706f814803', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Party Floor Concrete without insulation', 1.82, '86c38083-ae6b-40dc-afa8-f6853726d81a', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Intermediate Floor Timber with insulation', 0.32, '504029aa-e02f-401e-a9f3-83c299d9c537', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue('Intermediate Floor Timber without insulation', 1.73, '7ebb29ab-fe42-4a98-bd77-d7892c73f044', {
    source: UValueSource.UNKNOWN,
  }),
  new UValue(
    'Intermediate floors, boarding 19mm, airspace 100mm insulation between joists, 9.5mm plasterboard heat flow downward',
    0.31,
    'a04252ce-7318-430a-bfbc-4c995666238c',
    { source: UValueSource.UNKNOWN },
  ),
  new UValue(
    'Intermediate floors, boarding 19mm, airspace 100mm insulation between joists, 9.5mm plasterboard heat flow upward',
    0.32,
    '796ec371-b3a5-429e-b4c8-5395d410923f',
    { source: UValueSource.UNKNOWN },
  ),
  new UValue(
    'Intermediate floors, boarding 19mm, airspace between joists, 9.5mm plasterboard heat flow downward',
    1.41,
    '79f72019-51d0-48f8-b6f5-9dddecb600b6',
    { source: UValueSource.UNKNOWN },
  ),
  new UValue(
    'Intermediate floors, boarding 19mm, airspace between joists, 9.5mm plasterboard heat flow upward',
    1.73,
    '8ee7fbd2-254b-45a4-ad22-5bffada4a687',
    { source: UValueSource.UNKNOWN },
  ),
];

const GLOBAL_EXTERNAL_WALLS: UValue[] = [
  new UValue('Solid Brick 102mm, plaster', 3.1, '18a94c03-fe26-4aaf-be37-fae296941222', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Brick 228mm, plaster', 2.11, '1a28a565-3990-4448-8bde-39a7e48d4bf2', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Brick 343mm, plaster', 1.64, 'a0e3853b-16e9-4a85-9839-d9ae9231db6e', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Stone 305mm', 2.41, '140eecda-76dc-4eff-993d-f0f74add2a90', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Stone 457mm', 1.89, 'e21be8ea-8951-468c-887a-ac01c0085e75', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Stone 610mm', 1.54, '41934338-1509-4795-89d8-391726cfff4e', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Concrete 102mm, Plaster', 3.51, '38138de2-1f7d-4256-89f8-97553bc61a38', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Concrete 152mm, Plaster', 3.12, '95afa03d-c93a-4f4c-b41e-6cd587b75418', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Concrete 204mm, Plaster', 2.8, 'f4e074a2-7188-4795-95d2-73d3be60a41e', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Solid Concrete 254mm, Plaster', 2.54, '5afface8-fd61-406a-ab49-70348fdfc869', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Open Cavity, Brick 102mm, plaster 13mm and skim', 1.37, '96ef49ea-5d12-44bd-b359-c52ff5935e97', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue('Open Cavity, Brick 102mm, plaster 12mm on dabs', 1.21, '43d3e300-8c23-41b4-ba84-6161d5009bea', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
  new UValue(
    'Open Cavity, Brick 102mm, Standard Aerated Block, Plaster 13mm',
    0.87,
    '78f501b7-b981-4c60-ad14-e45f2aeecdc5',
    { source: UValueSource.DHDG2021_TABLE_13 },
  ),
  new UValue(
    'Cavity Wall Filled, 19mm Render, Standard Aerated Block, Plaster 13mm',
    0.61,
    'daac7cfb-1dfe-4d7e-8f40-673ad97f4c2b',
    { source: UValueSource.DHDG2021_TABLE_13 },
  ),
  new UValue(
    'Cavity Wall Filled, 50mm Mineral Wool, Brick 102mm, Plaster 13mm and skim',
    0.56,
    '955c260d-8b17-4014-8eda-b4dea3293a85',
    { source: UValueSource.DHDG2021_TABLE_13 },
  ),
  new UValue(
    'Cavity Wall Filled, Mineral Wool 50mm, Brick 102mm, Standard Aerated Block, Plaster 13mm',
    0.45,
    '34ad15d3-828b-44c3-9e00-5ede22396a3f',
    { source: UValueSource.DHDG2021_TABLE_13 },
  ),
  new UValue(
    'Cavity Wall Filled, Mineral Wool 50mm, Render 19mm, Standard Aerated Block, Plaster 13mm',
    0.37,
    '7068edc5-094a-4bc0-b4de-58e8a9f2b70b',
    { source: UValueSource.DHDG2021_TABLE_13 },
  ),
  new UValue('Timber Frame with Cladding, 100mm Insulation', 0.32, '31106999-6900-4287-ba78-6a41fb590504', {
    source: UValueSource.DHDG2021_TABLE_13,
  }),
];

const GLOBAL_DOORS: UValue[] = [
  new UValue('Solid Wood Door', 3, '0fa3c5ad-0c53-4497-9b4a-b45fc04f30f3', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
  new UValue('High Quality Door 50% glazing', 2.2, '68942e5e-a658-4f02-a8e5-681878905739', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
  new UValue('Wooden door with 25% single glazing', 3.7, '42b0f1f0-705b-4272-b8f5-b4a97e281424', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
  new UValue('Wooden door with 50% single glazing', 4.4, 'a88849b5-7d00-42a9-959a-f49dc0a78bbd', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
  new UValue('Wooden door with 25% double glazing', 2.9, '24b3830b-19d5-4112-8491-2fbbf1977b8a', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
  new UValue('Wooden door with 50% double glazing', 2.8, 'c1f0d53f-1073-463c-958b-6513b75d8d04', {
    source: UValueSource.DHDG2021_TABLE_20,
  }),
];

export const BASE_U_VALUES: UValues = {
  foundation: GLOBAL_FOUNDATIONS,
  floors: GLOBAL_FLOORS,
  intermediateFloors: GLOBAL_FLOORS,
  roof: GLOBAL_ROOFS,
  roofsOrCeilings: [...GLOBAL_ROOFS, ...GLOBAL_FLOORS],
  externalWalls: GLOBAL_EXTERNAL_WALLS,
  internalWalls: [...GLOBAL_INTERNAL_WALLS, ...GLOBAL_PARTY_WALLS],
  partyWalls: GLOBAL_PARTY_WALLS,
  windows: GLOBAL_GLAZINGS,
  doors: GLOBAL_DOORS,
  roofGlazings: GLOBAL_GLAZINGS,
};
