import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { ProcurementWarning } from './ProcurementWarning';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { useProcurementStore } from './store/ProcurementContext';
import { renderWithProviders } from 'tests/utils/testUtils';
import { AIK_BUNDLE_ID, AIK_ERP_ID, DUPLICATE_ERP_ID, REMOVED_ERP_ITEM_ID } from '../../bill-of-materials/test-utils';
import { screen, waitFor } from '@testing-library/react';
import { ProcurementDataManagerInitialState } from './ProcurementDataManager';
import { PROCUREMENT_WARNING } from './types';

setupProcurementTests();

function ProcurementWarningWrapper({ id }: { id: string }) {
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  let equivalenceGroup = equivalenceGroups[id];
  if (!equivalenceGroup) {
    return <h1>No equivalence group found</h1>;
  }
  // Overriding procured quantity to always be there so the warnings are shown based on other conditions
  // this can be probably removed later on when we have implemented proper handling for when errors should be hidden
  // and we can write actual tests for that behavior
  equivalenceGroup = {
    ...equivalenceGroup,
    procuredQuantity: 1,
  };
  return <ProcurementWarning equivalenceGroup={equivalenceGroup} />;
}

describe('ProcurementWarning', async () => {
  const renderProcurementWarning = async (id: string, initialState?: ProcurementDataManagerInitialState) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper initialState={initialState}>
          <ProcurementWarningWrapper id={id} />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId(/procurement-warning/)).toBeInTheDocument();
    });
  };

  it('Should show warning for removed ERP items', async () => {
    await renderProcurementWarning(REMOVED_ERP_ITEM_ID);
    const warning = screen.getByTestId(`procurement-warning-${PROCUREMENT_WARNING.REMOVED_FROM_ERP}`);
    expect(warning).toBeInTheDocument();
  });

  it('Should show warning for duplicate ERP items in different groups', async () => {
    await renderProcurementWarning(DUPLICATE_ERP_ID);
    const warning = screen.getByTestId(`procurement-warning-${PROCUREMENT_WARNING.DUPLICATE}`);
    expect(warning).toBeInTheDocument();
  });

  it('Should show warning for custom ERP items', async () => {
    await renderProcurementWarning('custom-item');
    const warning = screen.getByTestId(`procurement-warning-${PROCUREMENT_WARNING.CUSTOM}`);
    expect(warning).toBeInTheDocument();
  });

  it('Should show warning if equivalency group has AIK quantity but no BoM quantity', async () => {
    await renderProcurementWarning(AIK_ERP_ID, { installationKit: AIK_BUNDLE_ID });
    const warning = screen.getByTestId(`procurement-warning-${PROCUREMENT_WARNING.AIK}`);
    expect(warning).toBeInTheDocument();
  });
});
