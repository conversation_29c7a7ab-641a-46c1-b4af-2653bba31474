import { screen, waitFor, within } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { Procurement } from './Procurement';
import { extraItems, ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import {
  expandEquivalenceGroupCard,
  getEquivalenceGroupCards,
  getProcuredQuantity,
  getProcurementItemCards,
  getSummedAikQuantity,
  getSummedBomQuantity,
  getSummedVanStockQuantity,
  selectInstallationKit,
} from './test-utils/test-utils';
import { PROCUREMENT_ITEM_TYPES } from './types';
import { AIK_BUNDLE_ID } from '../../bill-of-materials/test-utils';
import { ProcurementDataManagerInitialState } from './ProcurementDataManager';
import userEvent from '@testing-library/user-event';

setupProcurementTests();

describe('Procurement Integration Tests', () => {
  const renderProcurement = async (initialState?: ProcurementDataManagerInitialState) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper initialState={initialState}>
          <Procurement />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );

    await waitFor(() => {
      const cards = getEquivalenceGroupCards();
      expect(cards.length).toBeGreaterThan(0);
    });
  };

  it('should render the procurement component', async () => {
    await renderProcurement();
    expect(screen.getByTestId('procurement-page')).toBeInTheDocument();
  });

  it('should render the procurement header', async () => {
    await renderProcurement();
    expect(screen.getByTestId('procurement-header')).toBeInTheDocument();
  });

  it('should render the procurement table', async () => {
    await renderProcurement();
    expect(screen.getByTestId('procurement-table')).toBeInTheDocument();
  });

  it('should render the procurement comment', async () => {
    await renderProcurement();
    expect(screen.getByTestId('procurement-comment')).toBeInTheDocument();
  });

  it('should render the procurement actions', async () => {
    await renderProcurement();
    expect(screen.getByTestId('procurement-actions')).toBeInTheDocument();
  });

  it('should display equivalence group cards correctly', async () => {
    await renderProcurement();
    const cards = getEquivalenceGroupCards();
    expect(cards.length).toBeGreaterThanOrEqual(3);
    const card1 = cards[0];
    const procurementItemCards1 = await getProcurementItemCards(card1!);
    expect(procurementItemCards1.length).toBe(2);

    const card2 = cards[1];
    const procurementItemCards2 = await getProcurementItemCards(card2!);
    expect(procurementItemCards2.length).toBe(2);

    const card3 = cards[2];
    const procurementItemCards3 = await getProcurementItemCards(card3!);
    expect(procurementItemCards3.length).toBe(1);
  });

  it('should correctly change equivalence group card information when selecting a procurement item', async () => {
    await renderProcurement();

    await waitFor(() => {
      const cards = getEquivalenceGroupCards();
      expect(cards.length).toBeGreaterThan(0);
    });
    let equivalenceGroupCards = getEquivalenceGroupCards();
    let firstCard = equivalenceGroupCards[0];
    const descriptionBefore = within(firstCard!).getByTestId('selected-item-description').textContent;
    expect(descriptionBefore).toEqual('Test Erp Item 1');
    await expandEquivalenceGroupCard(firstCard);

    // Get procurement item cards
    const procurementItemCards = await getProcurementItemCards(firstCard!);
    expect(procurementItemCards.length).toBe(2);
    const unselectedItemCard = procurementItemCards[1];
    const checkWrapper = within(unselectedItemCard!).getByTestId('select-item-container');
    await userEvent.click(checkWrapper);
    await waitFor(() => {
      equivalenceGroupCards = getEquivalenceGroupCards();
      firstCard = equivalenceGroupCards[0];
      const descriptionAfter = within(firstCard!).getByTestId('selected-item-description').textContent;
      expect(descriptionAfter).toEqual('Test Erp Item 2');
    });
  });

  it('should correctly add AIK items to the table when an installation kit is selected', async () => {
    await renderProcurement();
    let cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(11);
    await selectInstallationKit(AIK_BUNDLE_ID);
    cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(13);
  });

  it('should display different types of equivalence groups correctly', async () => {
    await renderProcurement({
      extraItems: extraItems,
    });
    // Check for ERP type cards
    const erpCards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.ERP);
    const customCards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.CUSTOM);
    const extraCards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.EXTRA);

    const erpCard = erpCards[0];
    const customCard = customCards[0];
    const extraCard = extraCards[0];

    expect(erpCard).toBeInTheDocument();
    expect(customCard).toBeInTheDocument();
    expect(extraCard).toBeInTheDocument();

    const bomQuantityErp = await getSummedBomQuantity(erpCard!);
    const vanStockQuantityErp = await getSummedVanStockQuantity(erpCard!);
    const aikQuantityErp = await getSummedAikQuantity(erpCard!);
    const procuredQuantityErp = await getProcuredQuantity(erpCard!);

    expect(bomQuantityErp).toEqual('11');
    expect(vanStockQuantityErp).toEqual('0');
    expect(aikQuantityErp).toEqual('0');
    expect(procuredQuantityErp).toEqual('11');

    const bomQuantityCustom = await getSummedBomQuantity(customCard!);
    const vanStockQuantityCustom = await getSummedVanStockQuantity(customCard!);
    const aikQuantityCustom = await getSummedAikQuantity(customCard!);
    const procuredQuantityCustom = await getProcuredQuantity(customCard!);
    expect(bomQuantityCustom).toEqual('3');
    expect(vanStockQuantityCustom).toEqual('0');
    expect(aikQuantityCustom).toEqual('0');
    expect(procuredQuantityCustom).toEqual('3');

    const bomQuantityExtra = await getSummedBomQuantity(extraCard!);
    const vanStockQuantityExtra = await getSummedVanStockQuantity(extraCard!);
    const aikQuantityExtra = await getSummedAikQuantity(extraCard!);
    const procuredQuantityExtra = await getProcuredQuantity(extraCard!);

    // Extra items should have no quantities at all

    expect(bomQuantityExtra).toEqual('');
    expect(vanStockQuantityExtra).toEqual('');
    expect(aikQuantityExtra).toEqual('');
    expect(procuredQuantityExtra).toEqual('0');
  });

  it('Handle expanding equivalence group cards correctly', async () => {
    await renderProcurement();

    await waitFor(() => {
      const cards = getEquivalenceGroupCards();
      expect(cards.length).toBeGreaterThan(0);
    });

    const cards = getEquivalenceGroupCards();
    const firstCard = cards[0];

    await expandEquivalenceGroupCard(firstCard);

    const cardDetails = within(firstCard!).queryByTestId('equivalence-group-card-details');
    expect(cardDetails).toBeInTheDocument();

    const procurementItemCards = await getProcurementItemCards(firstCard!);
    expect(procurementItemCards.length).toEqual(2);

    procurementItemCards.forEach((itemCard) => {
      expect(itemCard).toBeVisible();
    });
  });

  it('should only show items to order when that toggle is enabled', async () => {
    await renderProcurement();

    let cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(11);

    const toggle = within(screen.getByTestId('procurement-show-only-ordered-switch')).getByRole('checkbox');

    await userEvent.click(toggle);
    expect(toggle).toBeChecked();

    cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(9);
  });

  it('should only show items with the selected label', async () => {
    await renderProcurement();

    const label = 'Label 1';
    let cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(11);

    const select = screen.getByTestId('item-relations-label-filter').querySelector('.MuiSelect-select');
    await userEvent.click(select!);
    const option = await screen.findByTestId(`label-select-option-${label}`);
    userEvent.click(option!);

    await waitFor(() => {
      cards = getEquivalenceGroupCards();
      expect(cards.length).toBe(3);
    });
  });

  it('should only show matching items when searching with ERP ID', async () => {
    await renderProcurement();

    let cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(11);

    const search = within(screen.getByTestId('procurement-search-input-input')).getByRole('textbox');
    await userEvent.type(search, 'ERP404');

    await waitFor(() => {
      cards = getEquivalenceGroupCards();
      expect(cards.length).toBe(2);
    });
  });

  it('should only show matching items when searching by search term', async () => {
    await renderProcurement();

    let cards = getEquivalenceGroupCards();
    expect(cards.length).toBe(11);

    const search = within(screen.getByTestId('procurement-search-input-input')).getByRole('textbox');
    await userEvent.type(search, 'no quantity');

    await waitFor(() => {
      cards = getEquivalenceGroupCards();
      expect(cards.length).toBe(2);
    });
  });
});
