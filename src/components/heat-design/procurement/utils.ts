import { InstallationKitBundle, Item } from 'components/bill-of-materials/types';
import {
  EquivalenceGroup,
  EquivalenceGroups,
  PROCUREMENT_ITEM_TYPES,
  PROCUREMENT_WARNING,
  ProcurementItem,
  ProcurementWarning,
} from './types';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { keyIsNotNullish } from '../../../utils/isNotNullish';
import {
  Bundle,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';

export function isExtraEquivalenceGroup(item: EquivalenceGroup) {
  const items = Object.values(item.items);
  return items.length === 1 && items[0]?.type === PROCUREMENT_ITEM_TYPES.EXTRA;
}

export function getProcurementItemDescription(item: ProcurementItem) {
  if (isProcurementItemFromErp(item)) {
    return item.erpItem?.description ?? '';
  } else {
    return item.name ?? '';
  }
}

export function getProcurementItemMinorPrice(item: ProcurementItem) {
  if (isProcurementItemFromErp(item)) {
    return item.erpItem?.minorPrice ?? 0;
  } else {
    return item.cost ?? 0;
  }
}

export function getProcurementItemErpId(item: ProcurementItem) {
  if (isProcurementItemFromErp(item)) {
    return item.erpItem?.erpId ?? '';
  } else {
    return undefined;
  }
}

export function getProcurementItemLabel(item: ProcurementItem) {
  if (isProcurementItemFromErp(item)) {
    return item.erpItem?.label?.label ?? '';
  } else {
    return undefined;
  }
}

export function hasRemovedFromErpError(group: EquivalenceGroup) {
  const selectedItem = group.items[group.selectedItemId];
  if (group.procuredQuantity === 0) {
    return false;
  } else if (selectedItem && isProcurementItemFromErp(selectedItem) && keyIsNotNullish('erpItem', selectedItem)) {
    return selectedItem.erpItem.archivedAt !== undefined;
  }
  return false;
}

export function getProcurementWarning(
  group: EquivalenceGroup,
  groups: EquivalenceGroups,
): ProcurementWarning | undefined {
  const duplicateItemError = hasDuplicateErpItemError(group, groups);
  const aikItemError = hasAikItemError(group);
  const customItemError = hasCustomItemError(group);
  const removedFromErpError = hasRemovedFromErpError(group);
  if (duplicateItemError) {
    return PROCUREMENT_WARNING.DUPLICATE;
  } else if (aikItemError) {
    return PROCUREMENT_WARNING.AIK;
  } else if (customItemError) {
    return PROCUREMENT_WARNING.CUSTOM;
  } else if (removedFromErpError) {
    return PROCUREMENT_WARNING.REMOVED_FROM_ERP;
  } else {
    return undefined;
  }
}

export function getDuplicateErpItemsInDifferentGroups(groups: EquivalenceGroups) {
  const duplicateErpItems = new Set<string>();
  const erpItemsInGroups = new Set<string>();
  Object.values(groups).forEach((group) => {
    Object.values(group.items).forEach((item) => {
      if (isProcurementItemFromErp(item) && keyIsNotNullish('erpItem', item)) {
        if (erpItemsInGroups.has(item.erpItem.erpId)) {
          duplicateErpItems.add(item.erpItem.erpId);
        } else {
          erpItemsInGroups.add(item.erpItem.erpId);
        }
      }
    });
  });
  return duplicateErpItems;
}

export function hasDuplicateErpItemError(group: EquivalenceGroup, groups: EquivalenceGroups) {
  const duplicateItems = getDuplicateErpItemsInDifferentGroups(groups);
  return Object.values(group.items).some((item) => {
    if (isProcurementItemFromErp(item) && keyIsNotNullish('erpItem', item)) {
      return duplicateItems.has(item.erpItem.erpId);
    }
    return false;
  });
}

const getVanStockType = (bundle: Bundle) => {
  if (bundle.details?.details?.$case === 'vanStock') {
    return bundle.details.details.vanStock.stockType;
  }
  return VanStockType.VAN_STOCK_TYPE_UNSPECIFIED;
};

export function defaultSelectedVanStockBundles(allBundles: Bundle[]): string[] {
  const bundlesByType = Map.groupBy(allBundles, getVanStockType);
  return bundlesByType
    .entries()
    .flatMap(([_type, bundles]) => {
      if (bundles.length === 1) {
        return [bundles[0]!.id!.value];
      } else {
        return [];
      }
    })
    .toArray();
}

export function hasAikItemError(group: EquivalenceGroup) {
  const bomQuantity = Object.values(group.items).reduce((acc, item) => acc + item.bomQuantity, 0);
  const aikQuantity = Object.values(group.items).reduce((acc, item) => acc + item.aikQuantity, 0);
  return aikQuantity > 0 && bomQuantity === 0;
}

export function hasCustomItemError(group: EquivalenceGroup) {
  return (
    Object.values(group.items).some((item) => item.type === PROCUREMENT_ITEM_TYPES.CUSTOM) && group.procuredQuantity > 0
  );
}

export function calculateMinorCostOfOrderedItems(equivalenceGroups: EquivalenceGroups) {
  return Object.values(equivalenceGroups)
    .filter((group) => group.procuredQuantity > 0)
    .reduce((acc, group) => {
      const count = group.procuredQuantity;
      const selectedItem = group.items[group.selectedItemId]!;
      const cost: number = selectedItem ? getProcurementItemMinorPrice(selectedItem) : 0;
      acc += cost * count;
      return acc;
    }, 0);
}

export function calculateMinorCostOfAIKs(
  installationKitBundle: InstallationKitBundle | undefined,
  erpItems: Record<string, Item>,
) {
  if (installationKitBundle?.details?.details?.$case === 'installationKit') {
    return installationKitBundle?.details.details.installationKit.itemIds
      .map((itemId) => erpItems[itemId]?.minorPrice ?? 0)
      .reduce((a, b) => a + b, 0);
  }
  return 0;
}

/**
 * Based on the assumption that the installers first pick from the ordered products and the AIK before the van stock.
 * Not an exact measure as we take the price from the selected item in the equivalence group.
 */
export function calculateMinorCostOfEstimatedConsumedVanStock(
  groups: EquivalenceGroups,
  erpItems: Record<string, Item>,
) {
  return Object.entries(groups).reduce((acc, [_, equivalenceGroup]) => {
    const selectedItem = equivalenceGroup.items[equivalenceGroup.selectedItemId];
    if (selectedItem === undefined || selectedItem.itemId === undefined) {
      return 0;
    }

    // An equivalence group can have many items, so we sum the BoM and AIK
    // quantities for each item to get totals for a given equivalence group.
    const {
      bomQuantity: equivalenceGroupBomQuantity,
      vanStockQuantity: equivalenceGroupVanStockQuantity,
      aikQuantity: equivalenceGroupAikQuantity,
    } = Object.values(equivalenceGroup.items).reduce(
      (acc, item) => {
        return {
          bomQuantity: acc.bomQuantity + item.bomQuantity,
          vanStockQuantity: acc.vanStockQuantity + item.vanStockQuantity,
          aikQuantity: acc.aikQuantity + item.aikQuantity,
        };
      },
      {
        bomQuantity: 0,
        vanStockQuantity: 0,
        aikQuantity: 0,
      },
    );

    const selectedItemPrice =
      selectedItem.type === 'custom' ? (selectedItem.cost ?? 0) : (erpItems[selectedItem.itemId]?.minorPrice ?? 0);

    // Estimate how many units are taken from van stock for this equivalence group:
    //  1) Start from the total BoM demand for the group.
    //  2) Subtract what is already covered by AIK and by items we procured (ordered).
    //  3) Do not allow negative usage (floor at 0).
    //  4) Cap the estimate by the recorded van stock quantity for the group (can't consume more than available).
    const estimatedVanStockUsage = Math.min(
      equivalenceGroupVanStockQuantity,
      Math.max(0, equivalenceGroupBomQuantity - equivalenceGroupAikQuantity - equivalenceGroup.procuredQuantity),
    );

    return acc + estimatedVanStockUsage * selectedItemPrice;
  }, 0);
}

export function calculateHardwareMinorCost(
  equivalenceGroups: EquivalenceGroups,
  installationKitBundles: InstallationKitBundle,
  erpItems: Record<string, Item>,
) {
  return (
    calculateMinorCostOfOrderedItems(equivalenceGroups) +
    calculateMinorCostOfAIKs(installationKitBundles, erpItems) +
    calculateMinorCostOfEstimatedConsumedVanStock(equivalenceGroups, erpItems)
  );
}

// Helper to compute expectedUsageQuantity (EUQ) for an equivalence group
// EUQ = min((total_vs_quantity + total_aik_quantity), total_bom_quantity) + order_quantity
export function computeExpectedUsageQuantityForGroup(group: EquivalenceGroup): number {
  const totals = Object.values(group.items).reduce(
    (acc, item) => {
      acc.totalBom += item.bomQuantity ?? 0;
      acc.totalVs += item.vanStockQuantity ?? 0;
      acc.totalAik += item.aikQuantity ?? 0;
      return acc;
    },
    { totalBom: 0, totalVs: 0, totalAik: 0 },
  );

  const suppliedFromStock = totals.totalVs + totals.totalAik;
  const designBom = totals.totalBom;
  const orderQuantity = group.procuredQuantity ?? 0;
  return Math.min(suppliedFromStock, designBom) + orderQuantity;
}
