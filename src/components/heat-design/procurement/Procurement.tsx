import { Box, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Heading } from '@ui/components/Heading/Heading';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { MergeIcon } from '@ui/components/StandardIcons/MergeIcon';
import { beige, grey } from '@ui/theme/colors';
import { useCountry } from 'hooks/useCountry';
import { FormattedMessage } from 'react-intl';
import { getLocaleFromCountry } from 'utils/marketConfigurations';
import { ProcurementActions } from './ProcurementActions';
import { ProcurementComment } from './ProcurementComment';
import ProcurementHeader from './ProcurementHeader';
import ProcurementTable from './ProcurementTable';
import { useProcurementStore } from './store/ProcurementContext';

function OrderedPill({ date, locale }: { date: Date; locale: string }) {
  return (
    <Box
      sx={{
        color: 'black',
        padding: '5px',
        margin: '0 10px',
        px: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: grey[200],
        height: 'auto',
        gap: 1,
        borderRadius: 1,
      }}
    >
      <CheckIconThin />
      <Typography variant="body1Emphasis">
        <FormattedMessage
          id="procurement.status.ordered"
          values={{
            date: date.toLocaleString(locale, {
              timeZoneName: 'short',
            }),
          }}
        />
      </Typography>
    </Box>
  );
}

export function Procurement() {
  const country = useCountry();
  const locale = getLocaleFromCountry(country);
  const orderedAt = useProcurementStore((s) => s.orderedAt);

  return (
    <Stack direction="column" gap={2} data-testid="procurement-page">
      <Stack direction="column" gap={1}>
        <Stack direction="row" alignItems="center" gap={1}>
          <MergeIcon />
          <Heading level={1}>
            <FormattedMessage id="procurement.title" />
          </Heading>
          {orderedAt && <OrderedPill date={orderedAt} locale={locale} />}
        </Stack>
        <Typography variant="body1">
          <FormattedMessage id="procurement.description" />
        </Typography>
      </Stack>
      <Stack sx={{ borderRadius: '24px', padding: '24px', backgroundColor: beige[100] }} gap={3}>
        <div data-testid="procurement-header">
          <ProcurementHeader />
        </div>
        <div data-testid="procurement-table">
          <ProcurementTable />
        </div>
        <div data-testid="procurement-comment">
          <ProcurementComment />
        </div>
        <div data-testid="procurement-actions">
          <ProcurementActions />
        </div>
      </Stack>
    </Stack>
  );
}
