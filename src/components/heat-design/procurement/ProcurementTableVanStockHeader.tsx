import { Checkbox, Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import React, { memo } from 'react';
import { useProcurementStore } from './store/ProcurementContext';
import { TRANSPOSE_WIDTH, VAN_STOCK_HEADER_MAX_WIDTH, VAN_STOCK_HEADER_MIN_WIDTH } from './ProcurementTable';
import { blue } from '@ui/theme/colors';
import { PlumbingIcon } from '@ui/components/StandardIcons/PlumbingIcon';
import { VAN_STOCK_TYPES } from './store/ProcurementStore';
import { BoltOutlinedIcon } from '@ui/components/StandardIcons/BoltOutlinedIcon';

function ProcurementTableVanStockHeaderComponent() {
  const isVanStockExpanded = useProcurementStore((s) => s.isVanStockExpanded);
  // const setIsVanStockExpanded = useProcurementStore((s) => s.setIsVanStockExpanded);
  const selectedVanStockTypes = useProcurementStore((s) => s.selectedVanStockTypes);
  const setSelectedVanStockTypes = useProcurementStore((s) => s.setSelectedVanStockTypes);
  return (
    <Stack
      direction="row"
      alignItems="center"
      sx={{
        width: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH - 16}px` : `${VAN_STOCK_HEADER_MIN_WIDTH}px`,
        backgroundColor: isVanStockExpanded ? blue[200] : 'transparent',
        borderRadius: '8px',
        padding: '4px !important',
        marginLeft: `${TRANSPOSE_WIDTH}px`,
      }}
      data-testid="van-stock-header"
    >
      <Typography
        fontWeight={500}
        variant="body2"
        sx={{ width: isVanStockExpanded ? '0px' : 'auto', overflow: 'hidden' }}
        data-testid="van-stock-label"
      >
        <FormattedMessage id="heatDesign.procurement.vanStock" />
      </Typography>
      <Stack
        sx={{
          transition: 'max-width 0.2s',
          overflow: 'hidden',
          height: '21px',
          maxWidth: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH - 16}px` : '0px',
        }}
        direction="row"
        alignItems="center"
      >
        <Stack direction="row" alignItems="center" sx={{ width: '65px' }}>
          <PlumbingIcon />
          <Checkbox
            checked={selectedVanStockTypes.PLUMBING}
            onChange={(e) =>
              setSelectedVanStockTypes({
                ...selectedVanStockTypes,
                [VAN_STOCK_TYPES.PLUMBING]: e.target.checked,
              })
            }
          />
        </Stack>
        <Stack direction="row" alignItems="center" sx={{ width: '65px' }}>
          <BoltOutlinedIcon />
          <Checkbox
            checked={selectedVanStockTypes.ELECTRICAL}
            onChange={(e) =>
              setSelectedVanStockTypes({
                ...selectedVanStockTypes,
                [VAN_STOCK_TYPES.ELECTRICAL]: e.target.checked,
              })
            }
          />
        </Stack>
      </Stack>
      {/*<IconButton*/}
      {/*  sx={{ height: '21px', width: '21px', padding: '2px' }}*/}
      {/*  onClick={() => setIsVanStockExpanded(!isVanStockExpanded)}*/}
      {/*  data-testid="van-stock-expand-button"*/}
      {/*>*/}
      {/*  <Chevron*/}
      {/*    style={{ flex: '0 0 auto', height: '100%', width: '20px' }}*/}
      {/*    transitionDuration="0.2s"*/}
      {/*    direction={isVanStockExpanded ? 'left' : 'right'}*/}
      {/*    height={24}*/}
      {/*    width={20}*/}
      {/*  />*/}
      {/*</IconButton>*/}
    </Stack>
  );
}

export const ProcurementTableVanStockHeader = memo(ProcurementTableVanStockHeaderComponent);
