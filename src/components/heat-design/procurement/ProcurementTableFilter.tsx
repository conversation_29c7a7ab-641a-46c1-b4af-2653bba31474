import { Icon<PERSON>utton, InputAdornment, MenuItem, Stack, Typography } from '@mui/material';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { grey } from '@ui/theme/colors';
import { Select } from '@ui/components/Select/Select';
import { useProcurementStore } from './store/ProcurementContext';
import useItemLabels from '../../bill-of-materials/hooks/useItemLabels';
import { useCountry } from '../../../hooks/useCountry';
import { FormattedMessage, useIntl } from 'react-intl';
import React, { memo, useMemo } from 'react';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { TextField } from '@ui/components/TextField/TextField';
import { MagnifyingGlassOutlinedIcon } from '@ui/components/StandardIcons/MagnifyingGlassOutlinedIcon';
import { Switch } from '@ui/components/Switch/Switch';
import { But<PERSON> } from '@ui/components/Button/Button';
import { BILL_OF_MATERIALS_MODALS, BillOfMaterialsModals } from '../../bill-of-materials/BillOfMaterialsModals';
import { ListIcon } from '@ui/components/StandardIcons/ListIcon';
import { useBillOfMaterialsStore } from '../../bill-of-materials/BillOfMaterialsStore';
import { Item } from '../../bill-of-materials/types';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { useProcurementItemSearch } from './useProcurementItemSearch';

const MemoizedCheckbox = memo(Checkbox, (prevProps, nextProps) => prevProps.checked === nextProps.checked);

function ProcurementTableFilterComponent() {
  const selectedLabelIds = useProcurementStore((s) => s.selectedLabelIds);
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  const extraItems = useProcurementStore((s) => s.extraItems);
  const setExtraItems = useProcurementStore((s) => s.setExtraItems);
  const areOnlyOrderedItemsShown = useProcurementStore((s) => s.areOnlyOrderedItemsShown);
  const setAreOnlyOrderedItemsShown = useProcurementStore((s) => s.setAreOnlyOrderedItemsShown);
  const setSelectedLabelIds = useProcurementStore((s) => s.setSelectedLabelIds);
  const { setQuery, query } = useProcurementItemSearch(equivalenceGroups);
  const { formatMessage } = useIntl();
  const country = useCountry();
  const { labels } = useItemLabels({ countries: [country] });
  const { openModal } = useBillOfMaterialsStore();

  const existingErpItemIds = useMemo(() => {
    return Object.values(equivalenceGroups).reduce((acc, group) => {
      Object.values(group.items).forEach((item) => {
        if (isProcurementItemFromErp(item) && !!item.erpItem) {
          acc.add(item.erpItem.id.value);
        }
      });
      return acc;
    }, new Set<string>());
  }, [equivalenceGroups]);
  const selectedLabelNames = useMemo(() => {
    const allLabels: string[] = [];
    labels.forEach((label) => {
      if (label.id?.value && selectedLabelIds.has(label.id.value)) {
        allLabels.push(label.label);
      }
    });
    return allLabels;
  }, [selectedLabelIds, labels]);
  const onItemCatalogueClose = (selectedItems: Item[]) => {
    setExtraItems(selectedItems);
  };

  return (
    <Stack
      direction="row"
      alignItems="flex-end"
      justifyContent="space-between"
      data-testid="procurement-table-filter-container"
    >
      <Stack direction="row" alignItems="center" gap={2} flex={1}>
        <Select
          sx={{
            '.MuiSelect-select': { padding: '8px' },
            width: '200px',
          }}
          data-testid="item-relations-label-filter"
          multiple
          onChange={(event) => {
            setSelectedLabelIds(
              new Set(Array.isArray(event.target.value) ? event.target.value : event.target.value.split(',')),
            );
          }}
          renderValue={() => (
            <Typography noWrap whiteSpace="nowrap">
              {selectedLabelNames.join(', ')}
            </Typography>
          )}
          value={Array.from(selectedLabelIds)}
          label={formatMessage({ id: 'billOfMaterials.labels.selectLabel' })}
          name="Label"
          error={false}
          endAdornment={
            selectedLabelIds.size > 0 && (
              <InputAdornment sx={{ marginRight: '10px' }} position="end">
                <IconButton
                  onClick={() => {
                    setSelectedLabelIds(new Set());
                  }}
                >
                  <CloseIcon height={14} width={14} color={grey[900]} />
                </IconButton>
              </InputAdornment>
            )
          }
        >
          {labels.map(
            (label) =>
              label.id?.value && (
                <MenuItem
                  key={label.id.value}
                  value={label.id.value}
                  data-testid={`label-select-option-${label.label}`}
                >
                  <Stack direction="row">
                    <MemoizedCheckbox
                      labelSx={{
                        '> .MuiBox-root': {
                          width: '20px',
                          height: '20px',
                          marginRight: '4px',
                        },
                      }}
                      label={label.label}
                      checked={selectedLabelIds.has(label.id.value)}
                      onChange={() => {}}
                    />
                  </Stack>
                </MenuItem>
              ),
          )}
        </Select>
        <TextField
          sx={{
            input: { padding: '8px' },
            width: '300px',
            flexDirection: 'row-reverse',
          }}
          value={query}
          label={formatMessage({ id: 'billOfMaterials.itemCatalogue.filter.search' })}
          onChange={(event) => setQuery(event.target.value)}
          name="Search"
          placeholder="Search..."
          icon={<MagnifyingGlassOutlinedIcon />}
          data-testid="procurement-search-input"
        />
      </Stack>
      <Stack direction="row" alignItems="center" gap={2} sx={{ height: '40px' }} alignSelf="flex-end">
        <Switch
          checked={areOnlyOrderedItemsShown}
          onChange={() => setAreOnlyOrderedItemsShown(!areOnlyOrderedItemsShown)}
          label={
            <Typography fontWeight={500} variant="body2">
              Only show items to order
            </Typography>
          }
          data-testid="procurement-show-only-ordered-switch"
        />
        <Button
          data-testid="browse-items-button"
          onClick={() =>
            openModal(BILL_OF_MATERIALS_MODALS.ITEM_CATALOGUE, {
              disabledItemIds: existingErpItemIds,
              preSelectedItemIds: new Set(extraItems.map((item) => item.id.value)),
              onClose: onItemCatalogueClose,
            })
          }
          sx={{
            borderRadius: '16px',
            backgroundColor: '#22222608',
            height: '100%',
            '&:hover': { backgroundColor: '#22222616' },
          }}
        >
          <Stack justifyContent="space-between" direction="row" alignItems="center" gap={1}>
            <ListIcon color="#000" />
            <Typography variant="body1Emphasis">
              <FormattedMessage id="common.label.addItems" />
            </Typography>
          </Stack>
        </Button>
      </Stack>
      <BillOfMaterialsModals />
    </Stack>
  );
}

export const ProcurementTableFilter = memo(ProcurementTableFilterComponent);
