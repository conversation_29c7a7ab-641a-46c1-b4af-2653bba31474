import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { ProcurementItemCard } from './ProcurementItemCard';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { useProcurementStore } from './store/ProcurementContext';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { EquivalenceGroup, ProcurementItem } from './types';
import { AIK_BUNDLE_ID, AIK_ERP_ID, VAN_STOCK_1_BUNDLE_ID, VAN_STOCK_ERP_ID } from '../../bill-of-materials/test-utils';
import { ProcurementDataManagerInitialState } from './ProcurementDataManager';
import { getProcurementItemQuantity } from './test-utils/test-utils';

setupProcurementTests();

// Test wrapper component to access procurement store and render item cards
function ProcurementItemCardWrapper(options?: {
  initialState?: ProcurementDataManagerInitialState;
  selectedItem?: {
    equivalenceGroupId: string;
    itemId: string;
  };
}) {
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  const updateEquivalenceGroup = useProcurementStore((s) => s.updateEquivalenceGroup);
  let item: ProcurementItem | undefined;

  let equivalenceGroup: EquivalenceGroup | undefined = undefined;
  if (options?.selectedItem) {
    equivalenceGroup = equivalenceGroups[options.selectedItem.equivalenceGroupId];
    item = equivalenceGroup?.items[options.selectedItem.itemId];
  } else {
    equivalenceGroup = Object.values(equivalenceGroups).find((group) => {
      return Object.values(group.items).length > 1;
    });
    item = equivalenceGroup?.items[equivalenceGroup?.selectedItemId];
  }

  if (!equivalenceGroup) {
    return <div>No equivalence group found</div>;
  }
  if (!item) {
    return <div>No procurement item found</div>;
  }

  const handleSelect = (item: ProcurementItem) => {
    updateEquivalenceGroup(equivalenceGroup.id, (group) => ({
      ...group,
      selectedItemId: item.itemId!,
    }));
  };

  return <ProcurementItemCard item={item} isSelected={true} onSelect={handleSelect} />;
}

describe('ProcurementItemCard', () => {
  const renderProcurementItemCard = async (
    initialState?: ProcurementDataManagerInitialState,
    selectedItem?: {
      equivalenceGroupId: string;
      itemId: string;
    },
  ) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper initialState={initialState}>
          <ProcurementItemCardWrapper selectedItem={selectedItem} />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );

    await waitFor(() => {
      expect(screen.getByTestId('procurement-item-container')).toBeInTheDocument();
    });
  };

  it('should render the procurement item card component', async () => {
    await renderProcurementItemCard();

    const itemCards = screen.getAllByTestId('procurement-item-container');
    expect(itemCards.length).toBeGreaterThan(0);
    expect(itemCards[0]).toBeInTheDocument();
  });

  it('should show quantities for an item', async () => {
    await renderProcurementItemCard();

    const itemContainer = screen.getByTestId('procurement-item-container');
    const bomQuantity = await getProcurementItemQuantity(itemContainer, 'bom');
    const vanStockQuantity = await getProcurementItemQuantity(itemContainer, 'van-stock');
    const aikQuantity = await getProcurementItemQuantity(itemContainer, 'aik');

    expect(bomQuantity).toBe('8');
    expect(vanStockQuantity).toBe('0');
    expect(aikQuantity).toBe('0');
  });

  it('should show AIK quantities when installation kit is selected', async () => {
    // Test with AIK installation kit selected
    await renderProcurementItemCard(
      { installationKit: AIK_BUNDLE_ID },
      { itemId: AIK_ERP_ID, equivalenceGroupId: AIK_ERP_ID },
    );

    const itemContainer = screen.getByTestId('procurement-item-container');
    const aikQuantity = await getProcurementItemQuantity(itemContainer, 'aik');
    expect(aikQuantity).toBe('1');
  });

  it('should show van stock quantity when van stock is selected', async () => {
    // Test with one van stock selected
    await renderProcurementItemCard(
      { vanStockBundleIds: [VAN_STOCK_1_BUNDLE_ID] },
      { itemId: VAN_STOCK_ERP_ID, equivalenceGroupId: VAN_STOCK_ERP_ID },
    );

    const itemContainer = screen.getByTestId('procurement-item-container');
    const vanStockQuantity = await getProcurementItemQuantity(itemContainer, 'van-stock');
    expect(vanStockQuantity).toBe('1');
  });
});
