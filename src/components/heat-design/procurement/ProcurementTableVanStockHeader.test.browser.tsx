import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { ProcurementTableVanStockHeader } from './ProcurementTableVanStockHeader';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { renderWithProviders } from '../../../tests/utils/testUtils';

setupProcurementTests();

describe('ProcurementTableVanStockHeader', () => {
  const renderProcurementTableVanStockHeader = async () => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper>
          <ProcurementTableVanStockHeader />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('van-stock-header')).toBeInTheDocument();
    });
  };

  it('should render the procurement table van stock header component', async () => {
    await renderProcurementTableVanStockHeader();
    expect(screen.getByTestId('van-stock-header')).toBeInTheDocument();
  });
});
