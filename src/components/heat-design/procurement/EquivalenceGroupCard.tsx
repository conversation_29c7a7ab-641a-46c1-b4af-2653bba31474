import { memo, useCallback, useState } from 'react';
import { Box, Divider, IconButton, Stack, Typography } from '@mui/material';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import styled from '@emotion/styled';
import { useRouter } from 'next/router';
import { NumericField } from '../../bill-of-materials/components/common/NumericField';
import { blue, green, grey, surface } from '@ui/theme/colors';
import { useElementBbox } from '../../../hooks/useElementBbox';
import {
  QUANTITY_COLUMNS_WIDTHS,
  TRANSPOSE_WIDTH,
  VAN_STOCK_HEADER_MAX_WIDTH,
  VAN_STOCK_HEADER_MIN_WIDTH,
} from './ProcurementTable';
import { ProcurementItemCard } from './ProcurementItemCard';
import { ANIMATION_DURATION } from '../../bill-of-materials/constants/constants';
import { isCustomProcurementItem } from './type-guards/isCustomProcurementItem';
import { MarkerOutlinedIcon } from '@ui/components/StandardIcons/MarkerOutlinedIcon';
import { isEqual } from 'lodash';
import { useProcurementStore } from './store/ProcurementContext';
import {
  getProcurementItemDescription,
  getProcurementItemErpId,
  getProcurementItemLabel,
  getProcurementItemMinorPrice,
  isExtraEquivalenceGroup,
} from './utils';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { ConfirmationPopover } from '../../bill-of-materials/components/common/ConfirmationPopover';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { useIntl } from 'react-intl';
import { EquivalenceGroup, ProcurementItem } from './types';
import { ProcurementWarning } from './ProcurementWarning';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { theme } from '@ui/theme/theme';
import { useGroundwork } from 'context/groundwork-context';
import { marketConfiguration } from 'utils/marketConfigurations';
import { getFormattedItemPrice } from 'components/bill-of-materials/utils';

export const MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT = '41px';

const StyledCard = styled(Box)({
  filter: 'none',
  transition: `filter ${ANIMATION_DURATION}ms, box-shadow ${ANIMATION_DURATION}ms, height ${ANIMATION_DURATION}ms ease-in-out`,
  padding: 0,
  borderRadius: '16px',
  overflow: 'hidden',
  position: 'relative',

  '&.custom': {
    '&:hover': {
      cursor: 'default',
      filter: 'none',
      '.details-controls': {
        opacity: 0,
      },
    },
  },

  '.details-controls': {
    opacity: 0,
    transition: `opacity ${ANIMATION_DURATION}ms`,
  },
  '&:hover': {
    cursor: 'pointer',
    filter: 'brightness(0.97)',
    '.details-controls': {
      opacity: 1,
    },
  },
  '&.expanded': {
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

// Can't use surface directly due to the alpha channel. With a non-white background, the alpha channel causes issues.
const SURFACE_RGB = `rgb(248, 248, 248)`;

export type ProcurementItemCardProps = {
  equivalenceGroup: EquivalenceGroup;
  disableQuantityField?: boolean;
};

function EquivalenceGroupCardComponent({ equivalenceGroup, disableQuantityField }: Readonly<ProcurementItemCardProps>) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { formatMessage } = useIntl();
  const { countryCode } = useGroundwork();
  const { currency } = marketConfiguration[countryCode];
  const [isTransitionEnabled, setIsTransitionEnabled] = useState(true);
  const isVanStockExpanded = useProcurementStore((s) => s.isVanStockExpanded);
  const updateEquivalenceGroup = useProcurementStore((s) => s.updateEquivalenceGroup);
  const { locale } = useRouter();
  // Use useElementBbox hook for dynamic height calculation
  const [element, setElement] = useState<HTMLDivElement | undefined>(undefined);
  const detailsBbox = useElementBbox({ element });
  const [deleteAnchorEl, setDeleteAnchorEl] = useState<HTMLElement | null>(null);
  const deletePopoverOpen = Boolean(deleteAnchorEl);
  const detailsHeight = isExpanded ? detailsBbox.height : 0;
  const equivalenceGroupItems = Object.values(equivalenceGroup.items);
  const setExtraItems = useProcurementStore((s) => s.setExtraItems);
  const extraItems = useProcurementStore((s) => s.extraItems);
  const { summedBomQuantity, summedVanStockQuantity, summedAikQuantity } = equivalenceGroupItems.reduce(
    (acc, eq) => {
      acc['summedBomQuantity'] += eq.bomQuantity ?? 0;
      acc['summedVanStockQuantity'] += eq.vanStockQuantity ?? 0;
      acc['summedAikQuantity'] += eq.aikQuantity ?? 0;
      return acc;
    },
    {
      summedAikQuantity: 0,
      summedBomQuantity: 0,
      summedVanStockQuantity: 0,
    },
  );

  const handleRefChange = useCallback(
    (node: HTMLDivElement | undefined) => {
      setElement(node);
    },
    [setElement],
  );

  const onEquivalenceGroupChange = useCallback(
    (group: EquivalenceGroup) => {
      updateEquivalenceGroup(group.id, () => group);
    },
    [updateEquivalenceGroup],
  );

  const onEquivalentItemSelect = (item: ProcurementItem) => {
    if (item.itemId) {
      onEquivalenceGroupChange({
        ...equivalenceGroup,
        selectedItemId: item.itemId,
      });
    }
  };
  const selectedItem = equivalenceGroupItems.find((item) => item.itemId === equivalenceGroup.selectedItemId);
  const handleDeleteClick = (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    event.stopPropagation();
    setDeleteAnchorEl(event.currentTarget);
  };

  const handleDeleteCancel = () => {
    setDeleteAnchorEl(null);
  };

  const handleDeleteConfirm = () => {
    setDeleteAnchorEl(null);
    onDelete();
  };

  const onDelete = () => {
    if (isExtraEquivalenceGroup(equivalenceGroup)) {
      setExtraItems(extraItems.filter((item) => item.id.value !== equivalenceGroup.selectedItemId));
    }
  };

  if (!selectedItem) {
    return;
  }
  const description = getProcurementItemDescription(selectedItem);
  const minorPrice = getProcurementItemMinorPrice(selectedItem);
  const totalCostMinorPrice = minorPrice !== undefined ? minorPrice * equivalenceGroup.procuredQuantity : 0;
  const formattedTotalCost = getFormattedItemPrice(
    {
      currency: currency,
      minorPrice: totalCostMinorPrice,
      quantity: equivalenceGroup.procuredQuantity,
    },
    locale,
  );
  const erpId = getProcurementItemErpId(selectedItem);
  const label = getProcurementItemLabel(selectedItem);
  const styledCardClass = `${isExpanded ? 'expanded' : ''} ${isCustomProcurementItem(selectedItem) ? 'custom' : ''}`;
  const isExtraGroup = isExtraEquivalenceGroup(equivalenceGroup);
  return (
    <StyledCard
      className={styledCardClass}
      data-testid={`equivalence-group-card-${selectedItem.type}-${selectedItem.itemId}`}
    >
      <Box sx={{ display: 'grid', gridTemplateColumns: 'auto 1fr', minHeight: '45px', position: 'relative' }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            transition: isTransitionEnabled ? `height ${ANIMATION_DURATION}ms ease-in-out` : 'none',
            height: isExpanded ? `${45 + detailsHeight}px` : '45px',
          }}
        >
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: QUANTITY_COLUMNS_WIDTHS,
              alignItems: 'stretch',
              height: '100%',
            }}
          >
            <Stack
              style={{
                backgroundColor: green[200],
                borderRadius: '16px 0 0 16px',
                height: '100%',
              }}
            />

            {/* VS Column */}
            <Stack
              sx={{
                backgroundColor: blue[200],
                borderRadius: '16px 0 0 16px',
                marginLeft: `${TRANSPOSE_WIDTH}px`,
                height: '100%',
                transition: 'width 0.2s',
                width: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH}px` : `${VAN_STOCK_HEADER_MIN_WIDTH}px`,
              }}
            />

            {/* AIK Column */}
            <Stack
              sx={{
                backgroundColor: blue[300],
                borderRadius: '16px 0 0 16px',
                marginLeft: `${TRANSPOSE_WIDTH}px`,
                height: '100%',
                justifyContent: isExpanded ? 'flex-start' : 'center',
              }}
            />
          </Box>
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: SURFACE_RGB,
            borderRadius: '16px 0 0 16px',
            marginLeft: '-24px',
            overflow: 'hidden',
          }}
        />
        <Stack sx={{ position: 'absolute', top: 0, left: 0, height: '100%', width: '100%' }}>
          <Box
            data-testid="expand-toggle"
            onClick={(e) => {
              e.stopPropagation();
              if (isCustomProcurementItem(selectedItem)) {
                return;
              }
              // Hack to disable transition when container is expanded
              // done because the container has expanding/collapsing containers inside of it
              // if the height has a transition at that point, the expansion of the parent container
              // lags behind the child containers expanding
              if (isExpanded) {
                setIsTransitionEnabled(true);
              } else {
                setTimeout(() => {
                  setIsTransitionEnabled(false);
                }, ANIMATION_DURATION);
              }
              setIsExpanded((prev) => !prev);
            }}
            sx={{
              width: '100%',
              display: 'grid',
              gridTemplateColumns: `${QUANTITY_COLUMNS_WIDTHS} 75px 1fr 100px 100px 100px 100px`,
              alignItems: 'center',
              height: '45px',
              borderRadius: '0 16px 16px 0',
              backgroundColor: 'transparent',
            }}
          >
            <Box sx={{ height: '45px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Typography
                data-testid="summed-bom-quantity"
                variant="body2"
                align="center"
                sx={{ height: '45px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                {!isExtraGroup && summedBomQuantity}
              </Typography>
            </Box>

            <Stack
              direction="row"
              alignItems="center"
              justifyContent={isVanStockExpanded ? 'flex-start' : 'center'}
              sx={{
                height: '45px',
                marginLeft: `${TRANSPOSE_WIDTH}px`,
                width: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH}px` : `${VAN_STOCK_HEADER_MIN_WIDTH}px`,
                transition: 'width 0.2s',
              }}
            >
              {/*TODO: We need a different way of calculating van-stock quantities - right now we can't handle separating by bundle*/}
              {isVanStockExpanded ? (
                <Stack
                  direction="row"
                  alignItems="center"
                  justifyContent="space-around"
                  sx={{ width: `${VAN_STOCK_HEADER_MAX_WIDTH - 30}px` }}
                >
                  <Typography
                    variant="body2"
                    align="center"
                    sx={{
                      height: '45px',
                      transition: 'width 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    {!isExtraGroup && summedVanStockQuantity}
                  </Typography>
                  <Typography
                    variant="body2"
                    align="center"
                    sx={{
                      height: '45px',
                      transition: 'width 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    {!isExtraGroup && summedVanStockQuantity}
                  </Typography>
                </Stack>
              ) : (
                <Typography
                  variant="body2"
                  align="center"
                  data-testid="summed-van-stock-quantity"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {!isExtraGroup && summedVanStockQuantity}
                </Typography>
              )}
            </Stack>
            <Box
              sx={{
                marginLeft: `${TRANSPOSE_WIDTH}px`,
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Typography
                variant="body2"
                align="center"
                data-testid="summed-aik-quantity"
                sx={{
                  marginLeft: `${TRANSPOSE_WIDTH * 2}px`,
                  height: '45px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {!isExtraGroup && summedAikQuantity}
              </Typography>
            </Box>

            <Box
              style={{
                display: 'flex',
                alignItems: 'center',
                zIndex: 1,
                height: '100%',
              }}
            >
              <Box onClick={(e) => e.stopPropagation()} sx={{ padding: '8px 8px' }}>
                <NumericField
                  value={equivalenceGroup.procuredQuantity}
                  sx={{ input: { padding: '4px 0', textAlign: 'center' }, padding: 0 }}
                  onChange={(value) => {
                    onEquivalenceGroupChange({
                      ...equivalenceGroup,
                      procuredQuantity: value ?? 0,
                    });
                  }}
                  disabled={disableQuantityField}
                  data-testid="procured-quantity"
                  error={isExtraGroup && !equivalenceGroup.procuredQuantity}
                  name="quantity"
                />
              </Box>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center' }}>
              <ProcurementWarning equivalenceGroup={equivalenceGroup} />
              <Typography variant="body2" data-testid="selected-item-description">
                {description}
              </Typography>
              {isCustomProcurementItem(selectedItem) && (
                <TooltipAira
                  title={formatMessage({
                    id: 'heatDesign.billOfMaterials.miscellaneousItemsTable.customItemTooltip',
                  })}
                >
                  <MarkerOutlinedIcon
                    style={{ marginLeft: theme.spacing(1), color: grey[500] }}
                    height={20}
                    width={20}
                  />
                </TooltipAira>
              )}
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', marginLeft: `${TRANSPOSE_WIDTH}px` }}>
              <Typography variant="body2">{formattedTotalCost}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', marginLeft: `${TRANSPOSE_WIDTH}px` }}>
              <Typography variant="body2">{erpId}</Typography>
            </Box>
            <Box
              style={{
                display: 'flex',
                alignItems: 'center',
                height: '21px',
              }}
            >
              <Typography
                sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', display: 'block' }}
                variant="body2"
              >
                {label}
              </Typography>
            </Box>
            <Stack direction="row" alignItems="center" className="details-controls" justifyContent="flex-end">
              {isExtraGroup && (
                <IconButton
                  sx={{ height: '24px', width: '24px', padding: '2px' }}
                  onClick={(e) => handleDeleteClick(e)}
                >
                  <BinOutlinedIcon style={{ color: grey[900] }} />
                </IconButton>
              )}
              <Stack
                gap="8px"
                sx={{
                  cursor: 'pointer',
                  padding: '0 8px',
                  display: isProcurementItemFromErp(selectedItem) ? 'flex' : 'none',
                }}
                alignItems="center"
                direction="row"
                data-testid="expand-details-button"
                justifyContent="flex-end"
              >
                <Chevron
                  transitionDuration={(ANIMATION_DURATION / 100).toString()}
                  direction={isExpanded ? 'up' : 'down'}
                  height={20}
                  width={20}
                />
              </Stack>
            </Stack>
          </Box>
          {isProcurementItemFromErp(selectedItem) && (
            <Stack
              sx={{
                width: '100%',
              }}
            >
              <Box
                data-testid="equivalence-group-card-details"
                sx={{
                  overflow: 'hidden',
                  transition: isExpanded ? 'none' : `height ${ANIMATION_DURATION}ms ease-in-out`,
                  backgroundColor: 'transparent',
                  height: `${detailsHeight}px`,
                }}
              >
                <Box
                  ref={handleRefChange}
                  sx={{ display: 'flex', flexDirection: 'column', gap: '8px', padding: '8px 0' }}
                >
                  {equivalenceGroupItems.map((equivalentItem, index) => (
                    <ProcurementItemCard
                      onSelect={onEquivalentItemSelect}
                      isSelected={selectedItem.itemId === equivalentItem.itemId}
                      item={equivalentItem}
                      key={`equivalent-item-${index}`}
                    />
                  ))}
                </Box>
              </Box>
            </Stack>
          )}
        </Stack>
      </Box>

      <Divider
        sx={{
          position: 'absolute',
          top: '45px',
          borderColor: surface[200],
          width: '100%',
          transition: `opacity ${ANIMATION_DURATION}ms`,
          opacity: isExpanded ? 1 : 0,
        }}
      />

      <ConfirmationPopover
        anchorEl={deleteAnchorEl}
        open={deletePopoverOpen}
        onClose={handleDeleteCancel}
        title={formatMessage({
          id: 'heatDesign.procurement.deleteEquivalenceGroup.title',
        })}
        description={formatMessage({
          id: 'heatDesign.procurement.deleteEquivalenceGroup.description',
        })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={handleDeleteConfirm}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        testId="delete-equivalence-group-confirmation-popover"
      />
    </StyledCard>
  );
}

export const EquivalenceGroupCard = memo(EquivalenceGroupCardComponent, (prevProps, nextProps) => {
  return isEqual(prevProps, nextProps);
});
