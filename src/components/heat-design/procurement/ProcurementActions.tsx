import { Stack } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { LockOutlinedIcon } from '@ui/components/StandardIcons/LockOutlinedIcon';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { useProcurementStore } from './store/ProcurementContext';
import { useCallback, useMemo, useState } from 'react';
import { ConfirmationPopover } from 'components/bill-of-materials/components/common/ConfirmationPopover';
import { toast } from 'react-hot-toast';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useGroundwork } from 'context/groundwork-context';
import { useCountry } from 'hooks/useCountry';
import { ProcurementItemSource } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { EquivalenceGroups } from './types';
import { isErpProcurementItem } from './type-guards/isErpProcurementItem';
import { InstallationProjectTimeline_TimelineStatus } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { computeExpectedUsageQuantityForGroup, isExtraEquivalenceGroup } from './utils';
import { useProcurementValidation } from './hooks/useProcurementValidation';
import { isInstallationKitBundle } from 'components/bill-of-materials/utils';
import { InstallationKitBundle } from 'components/bill-of-materials/types';
import { getLocaleFromCountry } from 'utils/marketConfigurations';

// Helper to map equivalenceGroups to procurement items
function mapEquivalenceGroupsToProcurementItems(groups: EquivalenceGroups) {
  return Object.values(groups).map((group) => ({
    originalItemId: group.id,
    selectedItemId: group.selectedItemId,
    orderQuantity: group.procuredQuantity,
    expectedUsageQuantity: computeExpectedUsageQuantityForGroup(group),
    source: isExtraEquivalenceGroup(group)
      ? ProcurementItemSource.PROCUREMENT_ITEM_SOURCE_PROCUREMENT
      : ProcurementItemSource.PROCUREMENT_ITEM_SOURCE_BOM,
  }));
}

// Helper to map equivalenceGroups to ERP order items
function mapEquivalenceGroupsToOrderItems(groups: EquivalenceGroups, installationDate: Date) {
  return Object.values(groups)
    .filter((group) => Object.values(group?.items).every((item) => isErpProcurementItem(item)))
    .filter((group) => group.procuredQuantity > 0)
    .map((group) => {
      const itemId = group.selectedItemId;
      return {
        itemId: itemId,
        orderQuantity: group.procuredQuantity,
        deliveryDate: installationDate,
      };
    });
}

function installationKitToOrderItems(installationKit: InstallationKitBundle, installationDate: Date) {
  return installationKit.details.details.installationKit.itemIds.map((itemId) => ({
    itemId,
    orderQuantity: 1,
    deliveryDate: installationDate,
  }));
}

export function ProcurementActions() {
  const { formatMessage } = useIntl();
  const apiUtils = api.useUtils();
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  const energySolutionId = useEnergySolutionId();
  const installationGroundworkId = useGroundwork().groundwork.id?.value;
  const country = useCountry();
  const locale = getLocaleFromCountry(country);
  const installationKitId = useProcurementStore((state) => state.installationKit);
  const selectedVanStockBundleIds = useProcurementStore((s) => s.selectedVanStockBundleIds);
  const comment = useProcurementStore((s) => s.comment);
  const orderedAt = useProcurementStore((s) => s.orderedAt);

  const { validationErrors: procurementValidationErrors, isLoading: isValidationLoading } = useProcurementValidation(
    installationGroundworkId,
    energySolutionId,
    country,
  );

  const save = api.BillOfMaterials.saveProcuredBom.useMutation();
  const sendItemsToErp = api.BillOfMaterials.sendItemsToErp.useMutation();
  const applyAction = api.AiraBackend.applyActionGrpc.useMutation();

  const [isOperating, setIsOperating] = useState(false);
  const [hasError, setHasError] = useState<string | null>(null);
  const [confirmPreliminaryPopoverAnchor, setConfirmPreliminaryPopoverAnchor] = useState<HTMLElement | null>(null);
  const [reorderConfirmationPopoverAnchor, setReorderConfirmationPopoverAnchor] = useState<HTMLElement | null>(null);

  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });

  const { data: installationProject } = api.InstallationProject.getInstallationProject.useQuery(
    energySolutionId ? { solutionId: energySolutionId } : ({} as any),
    { enabled: !!energySolutionId },
  );
  // Fetch solution to determine current stage for conditional setOrdered action
  const { data: solutionData } = api.AiraBackend.getGrpcEnergySolution.useQuery(
    { solution: energySolutionId! },
    { enabled: !!energySolutionId },
  );
  const installationDate = installationProject?.timeline?.start;
  const isInstallationDatePreliminary =
    installationProject?.timeline?.status === InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_PRELIMINARY;

  const installationKit = useMemo(() => {
    if (bundleCollectionQuery.data) {
      return bundleCollectionQuery.data.bundleCollections
        .flatMap((collection) => collection.bundles)
        .filter(isInstallationKitBundle)
        .find((bundle) => bundle.id?.value === installationKitId);
    }
  }, [bundleCollectionQuery.data, installationKitId]);

  const doSave = useCallback(async () => {
    if (!energySolutionId || !installationGroundworkId) {
      throw new Error('Missing required IDs');
    }

    // Save both installationKit and van stock bundles
    const staticBundleDependencies = [...selectedVanStockBundleIds, ...(installationKitId ? [installationKitId] : [])];
    return toast.promise(
      save.mutateAsync({
        energySolutionId,
        installationGroundworkId,
        staticBundleDependencies,
        items: mapEquivalenceGroupsToProcurementItems(equivalenceGroups),
        comment: comment,
      }),
      {
        loading: formatMessage({ id: 'procurement.actions.saving' }),
        success: formatMessage({ id: 'procurement.actions.saved' }),
        error: (err) => {
          setHasError(err?.message ?? formatMessage({ id: 'procurement.actions.saveFailed' }));
          return formatMessage({
            id: 'procurement.actions.saveFailedToast',
          });
        },
      },
    );
  }, [
    energySolutionId,
    installationGroundworkId,
    equivalenceGroups,
    save,
    formatMessage,
    installationKitId,
    comment,
    selectedVanStockBundleIds,
  ]);

  const doSendToErp = useCallback(async () => {
    if (!energySolutionId || !installationGroundworkId || !country || !installationDate) {
      throw new Error('Missing required IDs');
    }

    const equivalenceGroupItems = mapEquivalenceGroupsToOrderItems(equivalenceGroups, installationDate);
    const installationKitItems = installationKit ? installationKitToOrderItems(installationKit, installationDate) : [];
    await toast.promise(
      sendItemsToErp.mutateAsync({
        energySolutionId,
        installationGroundworkId,
        country,
        items: [...equivalenceGroupItems, ...installationKitItems],
      }),
      {
        loading: formatMessage({ id: 'procurement.actions.ordering' }),
        success: formatMessage({ id: 'procurement.actions.orderedSuccess' }),
        error: (err) => {
          const displayMessage = formatMessage({ id: 'procurement.actions.orderFailed' });
          setHasError(err?.message ?? displayMessage);
          return displayMessage;
        },
      },
    );
  }, [
    energySolutionId,
    installationKit,
    installationGroundworkId,
    country,
    installationDate,
    equivalenceGroups,
    sendItemsToErp,
    formatMessage,
  ]);

  const markSolutionAsOrdered = useCallback(async () => {
    if (!energySolutionId) {
      throw new Error('Missing required IDs');
    }

    await toast.promise(
      applyAction.mutateAsync({
        solution: energySolutionId,
        action: 'ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED',
      }),
      {
        loading: formatMessage({ id: 'procurement.actions.markingAsOrdered' }),
        success: formatMessage({ id: 'procurement.actions.markedAsOrdered' }),
        error: (err) => {
          const displayMessage = formatMessage({ id: 'procurement.actions.markOrderFailed' });
          setHasError(err?.message ?? displayMessage);
          return displayMessage;
        },
      },
    );
  }, [energySolutionId, applyAction, formatMessage]);

  // Save procurement only
  const handleSave = useCallback(async () => {
    setIsOperating(true);
    setHasError(null);
    try {
      await doSave().then(async () => apiUtils.BillOfMaterials.loadProcuredBom.invalidate());
    } catch (err) {
      setHasError((err as Error).message);
      console.error('Save procurement error', err);
    } finally {
      setIsOperating(false);
    }
  }, [doSave, apiUtils.BillOfMaterials.loadProcuredBom]);

  // Save procurement and send to ERP
  const doSaveAndSendToErp = useCallback(async () => {
    setIsOperating(true);
    setHasError(null);
    try {
      await doSave()
        .then(doSendToErp)
        .then(async () => {
          if (solutionData?.solution?.presentation?.currentStage?.details?.$case === 'readyForOrder') {
            await markSolutionAsOrdered();
          }
        })
        .then(async () => {
          apiUtils.AiraBackend.getGrpcEnergySolution.invalidate();
          apiUtils.BillOfMaterials.loadProcuredBom.invalidate();
        });
    } catch (err) {
      setHasError((err as Error).message);
      console.error('Send to ERP error', err);
    } finally {
      setIsOperating(false);
    }
  }, [
    doSave,
    doSendToErp,
    markSolutionAsOrdered,
    solutionData?.solution?.presentation?.currentStage?.details?.$case,
    apiUtils.BillOfMaterials.loadProcuredBom,
    apiUtils.AiraBackend.getGrpcEnergySolution,
  ]);

  // Handler for the button click
  function handleSaveAndSendToErpClick(event: React.MouseEvent<HTMLButtonElement>) {
    const isOrdered = orderedAt !== undefined;
    if (isOrdered) {
      setReorderConfirmationPopoverAnchor(event.currentTarget);
      return;
    }
    if (isInstallationDatePreliminary) {
      setConfirmPreliminaryPopoverAnchor(event.currentTarget);
      return;
    }
    doSaveAndSendToErp();
  }

  function handleConfirmPreliminaryProceed() {
    setConfirmPreliminaryPopoverAnchor(null);
    doSaveAndSendToErp();
  }

  function handleConfirmPreliminaryCancel() {
    setConfirmPreliminaryPopoverAnchor(null);
  }

  function handleReorderConfirmProceed() {
    setReorderConfirmationPopoverAnchor(null);
    doSaveAndSendToErp();
  }

  function handleReorderConfirmCancel() {
    setReorderConfirmationPopoverAnchor(null);
  }

  const isActionsDisabled = isOperating || isValidationLoading;
  const isOrderingDisabled = isActionsDisabled || Object.values(procurementValidationErrors).length > 0;

  return (
    <Stack direction="column" gap={2} sx={{ marginTop: 2 }} data-testid="procurement-actions-container">
      <Stack direction="row" gap={2}>
        <Button
          variant="outlined"
          onClick={handleSave}
          disabled={isActionsDisabled}
          fullWidth
          data-testid="procurement-save-button"
        >
          <FormattedMessage id="common.label.save" />
        </Button>
        <Stack direction="column" gap={1} width="100%">
          <Button
            variant="contained"
            color="yellow"
            fullWidth
            sx={{ gap: 1 }}
            onClick={handleSaveAndSendToErpClick}
            disabled={isOrderingDisabled}
            data-testid="procurement-save-and-order-button"
          >
            <LockOutlinedIcon />
            <FormattedMessage id="procurement.actions.saveAndOrder" />
          </Button>
          <ConfirmationPopover
            anchorEl={confirmPreliminaryPopoverAnchor}
            open={!!confirmPreliminaryPopoverAnchor}
            onClose={handleConfirmPreliminaryCancel}
            title={formatMessage({
              id: 'procurement.actions.preliminaryDateConfirmTitle',
            })}
            description={formatMessage({
              id: 'procurement.actions.preliminaryDateConfirmDescription',
            })}
            confirmText={formatMessage({ id: 'common.label.order' })}
            onConfirm={handleConfirmPreliminaryProceed}
            testId="preliminary-date-confirmation-popover"
          />
          <ConfirmationPopover
            anchorEl={reorderConfirmationPopoverAnchor}
            open={!!reorderConfirmationPopoverAnchor}
            onClose={handleReorderConfirmCancel}
            title={formatMessage(
              {
                id: 'procurement.actions.reorderConfirmTitle',
              },
              {
                datetime: orderedAt?.toLocaleString(locale, {
                  timeZoneName: 'short',
                }),
              },
            )}
            description={formatMessage({
              id: 'procurement.actions.reorderConfirmDescription',
            })}
            confirmText={formatMessage({ id: 'common.label.order' })}
            onConfirm={handleReorderConfirmProceed}
            testId="reorder-confirmation-popover"
          />
          {Object.values(procurementValidationErrors)}
        </Stack>
      </Stack>
      {hasError ? (
        <span style={{ color: 'red' }} data-testid="procurement-error-message">
          {formatMessage({ id: 'procurement.actions.errorPrefix' })}
          {hasError}
        </span>
      ) : null}
    </Stack>
  );
}
