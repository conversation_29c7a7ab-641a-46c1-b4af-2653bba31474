import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import ProcurementHeader from './ProcurementHeader';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { createRouterMock, useRouter } from '@mocks/next/router';

setupProcurementTests();

describe('ProcurementHeader', () => {
  const renderProcurementHeader = async () => {
    useRouter.mockImplementation(() => ({
      ...createRouterMock(),
      pathname: '/procurement',
      query: {
        country: 'DE',
        solution: 'c974d3f9-4ce8-495d-b8d2-d4521d78ba1a',
      },
      push: vi.fn(),
    }));

    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper>
          <ProcurementHeader />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('procurement-header-content')).toBeInTheDocument();
    });
  };

  it('should render information header section', async () => {
    await renderProcurementHeader();

    expect(screen.getByTestId('procurement-information-header')).toBeInTheDocument();
    expect(screen.getByTestId('cost-of-all-items')).toBeInTheDocument();

    await waitFor(() => expect(screen.getByTestId('quoted-price')).toBeInTheDocument());
    // Hidden until the gross margin 2 field is fixed by https://aira.atlassian.net/browse/SAD-1972
    // expect(screen.getByTestId('gross-margin-two')).toBeInTheDocument();
  });

  it('should render selection header section', async () => {
    await renderProcurementHeader();

    expect(screen.getByTestId('procurement-selection-header')).toBeInTheDocument();
    expect(screen.getByTestId('installation-date-section')).toBeInTheDocument();
    expect(screen.getByTestId('installation-kit-section')).toBeInTheDocument();
  });

  it('should display installation kit selection', async () => {
    await renderProcurementHeader();

    expect(screen.getByTestId('installation-kit-select')).toBeInTheDocument();
  });
});
