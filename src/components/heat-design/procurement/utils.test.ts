import {
  Bundle,
  VanStockType,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { defaultSelectedVanStockBundles } from './utils';
import {
  convertToEquivalenceGroupsRecord,
  convertToItemsRecord,
  convertToProcurementItemsRecord,
  createTestErpItems,
} from 'components/bill-of-materials/test-utils';
import { InstallationKitBundle, Item } from 'components/bill-of-materials/types';
import {
  calculateMinorCostOfAIKs,
  calculateMinorCostOfEstimatedConsumedVanStock,
  computeExpectedUsageQuantityForGroup,
} from './utils';
import { EquivalenceGroup, PROCUREMENT_ITEM_TYPES, ProcurementItem } from './types';

describe('selecting default van stock bundles', () => {
  it('should select a van stock bundle when it is the only one available', () => {
    const vanStockBundleCollections = [buildVanStockBundle('bundle-1', VanStockType.VAN_STOCK_TYPE_ELECTRICAL)];

    const defaultIds = defaultSelectedVanStockBundles(vanStockBundleCollections);

    expect(defaultIds).toEqual(['bundle-1']);
  });

  it('should not select van stock bundles when multiple bundles of same type are available', () => {
    const vanStockBundles = [
      buildVanStockBundle('bundle-1', VanStockType.VAN_STOCK_TYPE_ELECTRICAL),
      buildVanStockBundle('bundle-2', VanStockType.VAN_STOCK_TYPE_ELECTRICAL),
    ];

    const defaultIds = defaultSelectedVanStockBundles(vanStockBundles);

    expect(defaultIds).toEqual([]);
  });

  it('should handle van stock bundles of different types', () => {
    const vanStockBundles = [
      buildVanStockBundle('bundle-1', VanStockType.VAN_STOCK_TYPE_ELECTRICAL),
      buildVanStockBundle('bundle-2', VanStockType.VAN_STOCK_TYPE_ELECTRICAL),
      buildVanStockBundle('bundle-3', VanStockType.VAN_STOCK_TYPE_PLUMBING),
    ];

    const defaultIds = defaultSelectedVanStockBundles(vanStockBundles);

    expect(defaultIds).toEqual(['bundle-3']);
  });
});

describe('calculateMinorCostOfAIKs', () => {
  it('should sum the cost of an AIK bundle containing multiple AIK items', () => {
    const erpItems = createTestErpItems();
    const aikBundle = {
      id: { value: '17914a73-8fc9-4de3-8e3a-dc90fbbe5f69' },
      bundleCollectionId: { value: '19b1296f-d002-4311-a870-d54c5ecf4e0e' },
      title: '',
      description: '',
      items: [
        {
          bundleId: { value: '97c77934-e346-4699-b8fe-45986291e382' },
          itemId: { value: erpItems[0]!.id.value },
          defaultQuantity: 1,
          instructions: '',
          editable: true,
          createdAt: new Date(),
        },
        {
          bundleId: { value: '97c77934-e346-4699-b8fe-45986291e382' },
          itemId: { value: erpItems[1]!.id.value },
          defaultQuantity: 2,
          instructions: '',
          editable: true,
          createdAt: new Date(),
        },
      ],
      details: {
        details: {
          $case: 'installationKit',
          installationKit: {
            itemIds: [erpItems[2]!.id.value, erpItems[3]!.id.value],
          },
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    } satisfies InstallationKitBundle;

    // If the code summed the item costs, it would be 100 + 200 = 300
    const expected = 300 + 450;
    const result = calculateMinorCostOfAIKs(aikBundle, convertToItemsRecord(erpItems));

    expect(result).toBe(expected);
  });
});

describe('calculateMinorCostOfConsumedVanStock', () => {
  const erpItems = createTestErpItems();
  const sampleErpItem = erpItems[0]!;
  const sampleCost = sampleErpItem.minorPrice!;

  it.each([
    { bomQuantity: 0, vanStockQuantity: 2, aikQuantity: 0, procuredQuantity: 0, expected: 0 },
    { bomQuantity: 5, vanStockQuantity: 5, aikQuantity: 0, procuredQuantity: 0, expected: 5 * sampleCost },
    { bomQuantity: 5, vanStockQuantity: 8, aikQuantity: 0, procuredQuantity: 0, expected: 5 * sampleCost },
    { bomQuantity: 5, vanStockQuantity: 2, aikQuantity: 3, procuredQuantity: 0, expected: 2 * sampleCost },
    { bomQuantity: 5, vanStockQuantity: 5, aikQuantity: 3, procuredQuantity: 0, expected: 2 * sampleCost },
    { bomQuantity: 5, vanStockQuantity: 1, aikQuantity: 2, procuredQuantity: 2, expected: 1 * sampleCost },
    { bomQuantity: 5, vanStockQuantity: 1, aikQuantity: 2, procuredQuantity: 3, expected: 0 },
    { bomQuantity: 5, vanStockQuantity: 5, aikQuantity: 0, procuredQuantity: 5, expected: 0 },
  ])(
    'an item with %i BoM, %i van stock, %i AIK and %i procured quantity should give a cost of the estimated consumed van stock as %i',
    ({ bomQuantity, vanStockQuantity, aikQuantity, procuredQuantity, expected }) => {
      const equivalenceGroups = setupCostOfConsumedVanStockTestData({
        item: sampleErpItem,
        bomQuantity,
        vanStockQuantity,
        aikQuantity,
        procuredQuantity,
      });
      const result = calculateMinorCostOfEstimatedConsumedVanStock(equivalenceGroups, convertToItemsRecord(erpItems));
      expect(result).toBe(expected);
    },
  );

  it('should consider all item types: ERP, extra and custom', () => {
    const equivalenceGroups = convertToEquivalenceGroupsRecord([
      {
        id: 'e9ab7a3c-9eee-4540-8f7d-7ee8d8d0f17b',
        items: convertToProcurementItemsRecord([
          {
            bomQuantity: 4,
            vanStockQuantity: 3,
            aikQuantity: 1,
            erpItem: erpItems[0],
            itemId: erpItems[0]!.id.value,
            type: 'erp',
          },
          {
            bomQuantity: 5,
            vanStockQuantity: 4,
            aikQuantity: 2,
            erpItem: erpItems[1]!,
            itemId: erpItems[1]!.id.value,
            type: 'extra',
          },
          {
            bomQuantity: 6,
            vanStockQuantity: 5,
            aikQuantity: 3,
            itemId: '08529380-f74c-44ff-a9f1-d306105b2914',
            cost: 300,
            name: 'Custom item',
            type: 'custom',
          },
        ]),
        selectedItemId: '08529380-f74c-44ff-a9f1-d306105b2914',
        procuredQuantity: 0,
      },
    ]);

    // 15 required by the BoM, total van stock = 12, total AIK = 6, leaving 0 to be procured
    // and meaning 9 consumed van stock items - priced at that of the selected item
    const expected = 9 * 300;
    const result = calculateMinorCostOfEstimatedConsumedVanStock(equivalenceGroups, convertToItemsRecord(erpItems));

    expect(result).toBe(expected);
  });

  it('should sum across equivalence groups', () => {
    const equivalenceGroups = convertToEquivalenceGroupsRecord([
      {
        id: '6a5dc3d9-9370-4074-8794-ea69b2c68976',
        items: convertToProcurementItemsRecord([
          {
            bomQuantity: 5,
            vanStockQuantity: 3,
            aikQuantity: 1,
            erpItem: erpItems[0],
            itemId: erpItems[0]!.id.value,
            type: 'erp',
          },
        ]),
        selectedItemId: erpItems[0]!.id.value,
        procuredQuantity: 1,
      },
      {
        id: '4db646fb-747d-468f-9178-d232080d5082',
        items: convertToProcurementItemsRecord([
          {
            bomQuantity: 5,
            vanStockQuantity: 5,
            aikQuantity: 2,
            erpItem: erpItems[1],
            itemId: erpItems[1]!.id.value,
            type: 'erp',
          },
        ]),
        selectedItemId: erpItems[1]!.id.value,
        procuredQuantity: 0,
      },
    ]);

    const expected = 3 * 100 + 3 * 200;
    const result = calculateMinorCostOfEstimatedConsumedVanStock(equivalenceGroups, convertToItemsRecord(erpItems));

    expect(result).toBe(expected);
  });
});

function setupCostOfConsumedVanStockTestData({
  item,
  bomQuantity,
  vanStockQuantity,
  aikQuantity,
  procuredQuantity,
}: {
  item: Item;
  bomQuantity: number;
  vanStockQuantity: number;
  aikQuantity: number;
  procuredQuantity: number;
}) {
  return convertToEquivalenceGroupsRecord([
    {
      id: '2138316e-6b82-474f-88de-48c3884614a3',
      items: convertToProcurementItemsRecord([
        {
          bomQuantity: bomQuantity,
          vanStockQuantity: vanStockQuantity,
          aikQuantity: aikQuantity,
          erpItem: item,
          itemId: item!.id.value,
          type: 'erp',
        },
      ]),
      selectedItemId: item!.id.value,
      procuredQuantity: procuredQuantity,
    },
  ]);
}

function makeGroup(parts: Array<{ bom: number; vs: number; aik: number }>, orderQuantity: number): EquivalenceGroup {
  const items: Record<string, ProcurementItem> = {};
  parts.forEach((p, idx) => {
    items[String(idx)] = {
      // Use CUSTOM items to avoid depending on ERP types
      type: PROCUREMENT_ITEM_TYPES.CUSTOM,
      itemId: String(idx),
      bomQuantity: p.bom,
      vanStockQuantity: p.vs,
      aikQuantity: p.aik,
      // optional fields for custom items
      name: `custom-${idx}`,
      cost: 0,
    };
  });
  return {
    id: 'grp-1',
    items,
    selectedItemId: '0',
    procuredQuantity: orderQuantity,
  };
}

describe('computeExpectedUsageQuantityForGroup', () => {
  // EUQ = min((total_vs + total_aik), total_bom) + order_quantity

  it('adds stock usage when below design BOM', () => {
    const group = makeGroup(
      [
        { bom: 5, vs: 2, aik: 1 }, // totals: bom=5, stock=3
      ],
      4, // order
    );
    // min(3, 5) + 4 = 7
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(7);
  });

  it('caps stock usage at design BOM when stock exceeds BOM', () => {
    const group = makeGroup(
      [
        { bom: 6, vs: 4, aik: 5 }, // stock=9, bom=6
      ],
      2,
    );
    // min(9, 6) + 2 = 8
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(8);
  });

  it('handles multiple items by summing quantities across items', () => {
    const group = makeGroup(
      [
        { bom: 3, vs: 1, aik: 0 },
        { bom: 4, vs: 0, aik: 2 },
      ],
      1,
    );
    // totals: bom=7, stock=1+2=3 => min(3,7)+1 = 4
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(4);
  });

  it('returns only ordered quantity when BOM is zero', () => {
    const group = makeGroup([{ bom: 0, vs: 5, aik: 5 }], 3);
    // min(10, 0) + 3 = 3
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(3);
  });

  it('returns zero when everything is zero', () => {
    const group = makeGroup([{ bom: 0, vs: 0, aik: 0 }], 0);
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(0);
  });

  it('returns zero when bom and order is zero', () => {
    const group = makeGroup([{ bom: 0, vs: 1, aik: 2 }], 0);
    // min(3, 0) + 0 = 0
    expect(computeExpectedUsageQuantityForGroup(group)).toBe(0);
  });
});

// Test helpers

function buildVanStockBundle(id: string, stockType: VanStockType): Bundle {
  return {
    id: { value: id },
    title: `Bundle ${id}`,
    description: `Bundle ${id} Description`,
    bundleCollectionId: { value: 'collection-1' },
    createdAt: new Date(),
    updatedAt: new Date(),
    details: {
      details: {
        $case: 'vanStock',
        vanStock: {
          regions: [{ value: 'region-1' }],
          stockType,
        },
      },
    },
    items: [],
  };
}
