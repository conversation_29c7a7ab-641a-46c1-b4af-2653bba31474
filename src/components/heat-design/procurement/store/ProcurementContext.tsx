import { createContext, useContext, useRef } from 'react';
import { initializeProcurementStore, ProcurementStore } from './ProcurementStore';
import { useStore } from 'zustand';

const ProcurementStoreContext = createContext<ProcurementStore | null>(null);

export const ProcurementProvider = ({ children }: { children: React.ReactNode }) => {
  const storeRef = useRef<ProcurementStore>();
  storeRef.current ??= initializeProcurementStore();

  return <ProcurementStoreContext.Provider value={storeRef.current}>{children}</ProcurementStoreContext.Provider>;
};

// Custom hook to access the store
export const useProcurementStore = <T,>(
  selector: (state: ReturnType<ReturnType<typeof initializeProcurementStore>['getState']>) => T,
): T => {
  const store = useContext(ProcurementStoreContext);
  if (!store) throw new Error('useProcurementStore must be used within a ProcurementProvider');
  return useStore(store, selector);
};
