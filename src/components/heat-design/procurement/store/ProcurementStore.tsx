import { createStore, StateCreator, StoreApi } from 'zustand';
import { Item } from '../../../bill-of-materials/types';
import { EquivalenceGroup, EquivalenceGroups, ProcurementWarning } from '../types';
import { getProcurementWarning } from '../utils';

// TODO: There aren't actually these kinds of van stock types. Instead we want to separate by bundle.
export const VAN_STOCK_TYPES = {
  PLUMBING: 'PLUMBING',
  ELECTRICAL: 'ELECTRICAL',
} as const;

export type VanStockType = (typeof VAN_STOCK_TYPES)[keyof typeof VAN_STOCK_TYPES];

export type EquivalenceGroupWarnings = Record<string, ProcurementWarning | undefined>;

export type ProcurementState = {
  areOnlyOrderedItemsShown: boolean;
  comment: string;
  equivalenceGroups: EquivalenceGroups;
  equivalenceGroupWarnings: EquivalenceGroupWarnings;
  extraItems: Item[];
  installationDate?: Date;
  installationKit: string; // UUID of the installation kit
  isVanStockExpanded: boolean;
  orderedAt?: Date;
  query: string;
  // Order of search results matters so we need to retain as an array
  equivalenceGroupQueryIds: string[] | undefined | null;
  selectedLabelIds: Set<string>;
  selectedVanStockTypes: Record<VanStockType, boolean>;
  // Van stock bundle selection (multi)
  selectedVanStockBundleIds: string[];
  setSelectedVanStockBundleIds: (ids: string[]) => void;
  setAreOnlyOrderedItemsShown: (value: boolean) => void;
  setComment: (comment: string) => void;
  setEquivalenceGroups: (groups: EquivalenceGroups) => void;
  setExtraItems: (items: Item[]) => void;
  setInstallationKit: (kit: string) => void;
  setIsVanStockExpanded: (isExpanded: boolean) => void;
  setSelectedLabelIds: (labelIds: Set<string>) => void;
  setEquivalenceGroupQueryIds: (queryIds: string[] | undefined | null) => void;
  setSelectedVanStockTypes: (vanStockTypes: Record<VanStockType, boolean>) => void;
  setOrderedAt: (date: Date) => void;
  setQuery: (query: string) => void;

  updateEquivalenceGroup: (groupId: string, updater: (group: EquivalenceGroup) => EquivalenceGroup) => void;
  updateWarnings: () => void; // Recalculate warnings for all groups
};

const createProcurementStore: StateCreator<ProcurementState> = (set, get) => ({
  areOnlyOrderedItemsShown: false,
  comment: '',
  equivalenceGroups: {},
  equivalenceGroupWarnings: {},
  extraItems: [],
  installationDate: undefined,
  installationKit: '',
  isVanStockExpanded: false,
  equivalenceGroupQueryIds: [],
  selectedLabelIds: new Set(),
  selectedVanStockTypes: {
    [VAN_STOCK_TYPES.PLUMBING]: true,
    [VAN_STOCK_TYPES.ELECTRICAL]: true,
  },
  orderedDate: undefined,
  query: '',
  selectedVanStockBundleIds: [],
  setSelectedVanStockBundleIds: (ids) => set({ selectedVanStockBundleIds: ids }),
  setAreOnlyOrderedItemsShown: (value) => set({ areOnlyOrderedItemsShown: value }),
  setComment: (value) => set({ comment: value }),
  setEquivalenceGroups: (groups) => {
    set({ equivalenceGroups: groups });
    // Automatically update warnings when groups change
    get().updateWarnings();
  },
  setExtraItems: (items) => set({ extraItems: items }),
  setInstallationKit: (kit) => set({ installationKit: kit }),
  setIsVanStockExpanded: (isExpanded) => set({ isVanStockExpanded: isExpanded }),
  setSelectedLabelIds: (labelIds) => set({ selectedLabelIds: labelIds }),
  setSelectedVanStockTypes: (vanStockTypes) => set({ selectedVanStockTypes: vanStockTypes }),
  setOrderedAt: (orderedAt) => set({ orderedAt }),
  setQuery: (query) => set({ query }),

  updateEquivalenceGroup: (groupId, updater) => {
    set((state) => {
      const equivalenceGroup = state.equivalenceGroups[groupId];
      if (!equivalenceGroup) {
        throw new Error(`Equivalence group ${groupId} not found`);
      }
      return {
        equivalenceGroups: {
          ...state.equivalenceGroups,
          [groupId]: updater(equivalenceGroup),
        },
      };
    });
    // Automatically update warnings when a group changes
    get().updateWarnings();
  },
  setEquivalenceGroupQueryIds: (queryIds) => set({ equivalenceGroupQueryIds: queryIds }),
  // Warning calculation method
  updateWarnings: () => {
    const state = get();
    const newWarnings: EquivalenceGroupWarnings = {};

    Object.entries(state.equivalenceGroups).forEach(([groupId, group]) => {
      const warning = getProcurementWarning(group, state.equivalenceGroups);
      newWarnings[groupId] = warning;
    });

    set({ equivalenceGroupWarnings: newWarnings });
  },
});

export type ProcurementStore = StoreApi<ReturnType<typeof createProcurementStore>>;
export const initializeProcurementStore = () => createStore(createProcurementStore);
