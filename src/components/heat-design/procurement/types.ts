import { Item } from 'components/bill-of-materials/types';

type BaseProcurementItem = {
  bomQuantity: number;
  aikQuantity: number;
  vanStockQuantity: number;
  itemId: string | undefined;
};
export type ExtraProcurementItem = BaseProcurementItem & {
  erpItem: Item;
  type: typeof PROCUREMENT_ITEM_TYPES.EXTRA;
};

export type ErpProcurementItem = BaseProcurementItem & {
  erpItem: Item | undefined;
  type: typeof PROCUREMENT_ITEM_TYPES.ERP;
};

export type CustomProcurementItem = BaseProcurementItem & {
  name?: string;
  cost?: number;
  type: typeof PROCUREMENT_ITEM_TYPES.CUSTOM;
};

export type ProcurementItem = ErpProcurementItem | CustomProcurementItem | ExtraProcurementItem;

export type EquivalenceGroup = {
  id: string;
  items: Record<string, ProcurementItem>;
  selectedItemId: string;
  procuredQuantity: number;
};

export type EquivalenceGroups = Record<string, EquivalenceGroup>;

// Procurement warning types
export const PROCUREMENT_WARNING = {
  AIK: 'aik',
  DUPLICATE: 'duplicate',
  CUSTOM: 'custom',
  REMOVED_FROM_ERP: 'removedFromErp',
} as const;

export type ProcurementWarning = (typeof PROCUREMENT_WARNING)[keyof typeof PROCUREMENT_WARNING];

export const DATA_SOURCES = {
  BOM: 'BOM',
  VANSTOCK: 'VS',
  AIK: 'AIK',
  EXTRA: 'EXTRA',
  RADIATORS: 'RADIATORS',
} as const;

export const PROCUREMENT_ITEM_TYPES = {
  ERP: 'erp',
  CUSTOM: 'custom',
  EXTRA: 'extra',
} as const;

export type ProcurementItemType = (typeof PROCUREMENT_ITEM_TYPES)[keyof typeof PROCUREMENT_ITEM_TYPES];
