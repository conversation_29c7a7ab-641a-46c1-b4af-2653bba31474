import { Stack } from '@mui/material';
import { TextField } from '@ui/components/TextField/TextField';
import { useIntl } from 'react-intl';
import { useProcurementStore } from './store/ProcurementContext';

export function ProcurementComment() {
  const { formatMessage } = useIntl();
  const comment = useProcurementStore((s) => s.comment);
  const setComment = useProcurementStore((s) => s.setComment);

  // This should be synced with the validation done in useProcurementValidation in a later ticket
  const isValid = comment !== undefined && comment.length > 0;

  return (
    <Stack data-testid="procurement-comment-container">
      <TextField
        label={formatMessage({ id: 'procurement.comment.label' })}
        fullWidth
        name="procurement-comment"
        type="textarea"
        placeholder={formatMessage({ id: 'procurement.comment.placeholder' })}
        value={comment}
        minRows={3}
        onChange={(e) => setComment(e.target.value)}
        required
        error={!isValid}
      />
    </Stack>
  );
}
