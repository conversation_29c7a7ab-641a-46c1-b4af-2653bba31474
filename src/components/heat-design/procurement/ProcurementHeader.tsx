import {
  InstallationProjectTimeline,
  InstallationProjectTimeline_TimelineStatus,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Checkbox, InputLabel, MenuItem, Stack, Typography } from '@mui/material';
import MuiAutocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { Select } from '@ui/components/Select/Select';
import { MoneyDropOutlinedIcon } from '@ui/components/StandardIcons/MoneyDropOutlinedIcon';
import { QuoteOutlinedIcon } from '@ui/components/StandardIcons/QuoteOutlinedIcon';
import { RulerOutlinedIcon } from '@ui/components/StandardIcons/RulerOutlinedIcon';
import { LabelTooltip, TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { GrossMarginTwoHeaderItem } from 'components/bill-of-materials/components/common/GrossMarginTwoHeaderItem';
import { MonetaryHeaderItem } from 'components/bill-of-materials/components/common/MonetaryHeaderItem';
import { useGroundwork } from 'context/groundwork-context';
import { useCountry } from 'hooks/useCountry';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useErpItems } from 'hooks/useErpItemsWithLabels';
import { MessageKey } from 'messageType';
import React, { useMemo } from 'react';
import { FormattedMessage, IntlShape, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { formatNumberCurrency } from 'utils/helpers';
import { isNotNullish } from 'utils/isNotNullish';
import { getLocaleFromCountry, marketConfiguration } from 'utils/marketConfigurations';
import {
  isInstallationKitBundleCollection,
  isVanStockBundleCollection,
  isVanStockBundleForRegion,
} from '../../bill-of-materials/utils';
import { ProcurementValidationWarningKeys, useProcurementValidation } from './hooks/useProcurementValidation';
import { useProcurementStore } from './store/ProcurementContext';
import {
  calculateMinorCostOfAIKs,
  calculateMinorCostOfEstimatedConsumedVanStock,
  calculateMinorCostOfOrderedItems,
} from './utils';
import { InstallationKitBundle } from 'components/bill-of-materials/types';

function InfoRow({ label, value }: Readonly<{ label: React.ReactNode; value: string | number }>) {
  return (
    <Stack
      direction="row"
      gap={2}
      alignItems="center"
      borderRadius={1}
      padding={1}
      sx={{
        backgroundColor: 'rgba(34, 34, 38, 0.03)',
      }}
    >
      <Typography fontWeight={500} variant="body2">
        {label}
      </Typography>
      <Typography variant="body1">{value}</Typography>
    </Stack>
  );
}

function InformationHeader() {
  const intl = useIntl();
  const {
    countryCode,
    groundwork: { location },
  } = useGroundwork();
  const { currency } = marketConfiguration[countryCode];
  const country = useCountry();
  const locale = getLocaleFromCountry(country);
  const energySolutionId = useEnergySolutionId();
  const equivalenceGroups = useProcurementStore((state) => state.equivalenceGroups);

  const erpItems = useErpItems();
  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const selectedInstallationKitId = useProcurementStore((state) => state.installationKit);
  const selectedInstallationKit = bundleCollectionQuery.data?.bundleCollections
    .filter(isInstallationKitBundleCollection)
    .flatMap((bundleCollection) =>
      bundleCollection.bundles.find((bundle) => bundle.id?.value === selectedInstallationKitId),
    )
    .at(0) as InstallationKitBundle | undefined;

  const solutionData = api.AiraBackend.getEnergySolutionDiff.useQuery(
    { energySolutionId: energySolutionId! },
    {
      enabled: !!energySolutionId,
    },
  );

  const region = solutionData.data?.currentSolution?.region;
  const regionValue = [
    region?.name,
    intl.formatMessage({
      id:
        region?.iso3166?.$case === 'countrySubdivision'
          ? (`common.country.${countryCode}.${region.iso3166.countrySubdivision.subdivision}` as MessageKey)
          : (`common.country.${countryCode}` as MessageKey),
    }),
  ].reduce((acc, curr) => {
    if (!curr) return acc;
    return acc ? `${acc}, ${curr}` : curr;
  }, '');

  const formattedAddress = location?.$case === 'exactAddress' ? location.exactAddress.formattedAddress : undefined;

  const costOfOrderedItems = calculateMinorCostOfOrderedItems(equivalenceGroups);
  const costOfAIKs = calculateMinorCostOfAIKs(selectedInstallationKit, erpItems.erpItems);
  const costOfEstimatedConsumedVanStock = calculateMinorCostOfEstimatedConsumedVanStock(
    equivalenceGroups,
    erpItems.erpItems,
  );
  const costOfAllItems = costOfOrderedItems + costOfAIKs + costOfEstimatedConsumedVanStock;

  const salesPrice = solutionData?.data?.lastQuotedSolution?.presentation?.salesPrice;
  const designedPrice = solutionData?.data?.currentSolution?.presentation?.salesPrice;
  const designedPriceExcludingTax = designedPrice?.taxDetails?.minorAmountExcludingTax;
  const subsidy = solutionData.data?.currentSolution?.presentation?.subsidy?.resolvedAmount?.minorAmount;

  return (
    <Stack direction="column" gap={2} mb={2}>
      <Stack direction="row" gap={2}>
        {regionValue && <InfoRow label={<FormattedMessage id="quotation.title.region" />} value={regionValue} />}
        {formattedAddress && (
          <InfoRow label={<FormattedMessage id="common.label.address" />} value={formattedAddress} />
        )}
      </Stack>
      <Stack direction="row" gap={2} alignItems="center">
        {salesPrice && (
          <MonetaryHeaderItem
            icon={<QuoteOutlinedIcon />}
            label="heatDesign.billOfMaterials.summary.salesPrice"
            monetaryAmount={salesPrice}
            locale={locale}
            testId="quoted-price"
            showDivider={false}
          />
        )}
        {designedPrice && (
          <MonetaryHeaderItem
            icon={<RulerOutlinedIcon />}
            label="heatDesign.billOfMaterials.summary.designedPrice"
            monetaryAmount={designedPrice}
            locale={locale}
          />
        )}
        <MonetaryHeaderItem
          icon={<MoneyDropOutlinedIcon />}
          label="billOfMaterials.costOfAllItems"
          monetaryAmount={{
            currencyCode: currency,
            minorAmount: costOfAllItems,
          }}
          locale={locale}
          testId="cost-of-all-items"
          tooltip={
            <TooltipAira
              title={
                <FormattedMessage
                  id="billOfMaterials.costOfAllItems.info"
                  values={{
                    costOfOrderedItems: formatNumberCurrency(costOfOrderedItems, countryCode, currency),
                    costOfAIKs: formatNumberCurrency(costOfAIKs, countryCode, currency),
                    costOfEstimatedConsumedVanStock: formatNumberCurrency(
                      costOfEstimatedConsumedVanStock,
                      countryCode,
                      currency,
                    ),
                  }}
                />
              }
            />
          }
        />
        {designedPriceExcludingTax && (
          <GrossMarginTwoHeaderItem
            hardwareCost={costOfAllItems}
            designedPrice={designedPriceExcludingTax}
            subsidy={subsidy ?? 0}
            countryCode={countryCode}
          />
        )}
      </Stack>
    </Stack>
  );
}

function formatInstallationDate(intl: IntlShape, timeline?: InstallationProjectTimeline): React.ReactNode {
  const NO_DATE = '-';
  if (!timeline) return NO_DATE;
  const { start, status } = timeline;

  if (!start) return NO_DATE;

  const formattedStart = new Date(start).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  switch (status) {
    case InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_PRELIMINARY:
      return `${formattedStart} (${intl.formatMessage({ id: 'common.label.preliminary' })})`;
    case InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_CONFIRMED:
      return `${formattedStart} (${intl.formatMessage({ id: 'common.label.confirmed' })})`;
    case InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_UNSPECIFIED:
      return NO_DATE;
    case InstallationProjectTimeline_TimelineStatus.UNRECOGNIZED:
      return NO_DATE;
    default:
      return status satisfies never;
  }
}

function SelectionHeader() {
  const intl = useIntl();
  const installationKit = useProcurementStore((state) => state.installationKit);
  const setInstallationKit = useProcurementStore((state) => state.setInstallationKit);
  const selectedVanStockBundleIds = useProcurementStore((s) => s.selectedVanStockBundleIds);
  const setSelectedVanStockBundleIds = useProcurementStore((s) => s.setSelectedVanStockBundleIds);
  const country = useCountry();
  const installationGroundworkId = useGroundwork().groundwork.id?.value;
  const bundleCollectionQuery = api.BillOfMaterials.getBundleCollections.useQuery({ country });
  const solutionId = useEnergySolutionId();
  const { data: energySolution } = api.AiraBackend.getGrpcEnergySolution.useQuery(
    { solution: solutionId! },
    { enabled: !!solutionId },
  );
  const regionId = energySolution?.solution?.region?.id;
  const { data: installationProject, isLoading: isLoadingInstallationProject } =
    api.InstallationProject.getInstallationProject.useQuery({ solutionId: solutionId! }, { enabled: !!solutionId });
  const installationKitBundles = useMemo(() => {
    if (bundleCollectionQuery.data) {
      return bundleCollectionQuery.data.bundleCollections
        .filter(isInstallationKitBundleCollection)
        .map((collection) => collection.bundles)
        .flat()
        .toSorted((a, b) => {
          const aTitle = a.createdAt;
          const bTitle = b.createdAt;
          if (!aTitle || !bTitle) return 0;
          return bTitle?.valueOf() - aTitle?.valueOf();
        });
    }
    return [];
  }, [bundleCollectionQuery.data]);

  // Van stock bundles (multi)
  const vanStockBundleOptions = useMemo(() => {
    if (bundleCollectionQuery.data && regionId) {
      return bundleCollectionQuery.data.bundleCollections
        .filter(isVanStockBundleCollection)
        .flatMap((collection) => collection.bundles)
        .filter(isVanStockBundleForRegion(regionId.value))
        .toSorted((a, b) => {
          const aTitle = a.createdAt;
          const bTitle = b.createdAt;
          if (!aTitle || !bTitle) return 0;
          return bTitle?.valueOf() - aTitle?.valueOf();
        });
    }
    return [];
  }, [bundleCollectionQuery.data, regionId]);

  const selectedVanStockBundles = useMemo(
    () => vanStockBundleOptions.filter((b) => b.id && selectedVanStockBundleIds.includes(b.id.value)),
    [vanStockBundleOptions, selectedVanStockBundleIds],
  );

  const { validationWarnings } = useProcurementValidation(installationGroundworkId, solutionId, country);

  return (
    <Stack direction="row" alignItems="center" gap={1}>
      <Stack direction="column" gap={1} width="100%" data-testid="installation-date-section">
        <Typography variant="body2Emphasis">
          <FormattedMessage id="procurement.label.installationDate" />
        </Typography>
        <Stack direction="row" gap={1} alignItems="center">
          {validationWarnings?.[ProcurementValidationWarningKeys.INSTALLATION_DATE_PASSED]}
          {isLoadingInstallationProject ? (
            <Typography variant="body2" data-testid="installation-date-loading">
              {intl.formatMessage({ id: 'common.notify.loading' })}
            </Typography>
          ) : (
            <Typography variant="body2" data-testid="installation-date-value">
              {formatInstallationDate(intl, installationProject?.timeline)}
            </Typography>
          )}
        </Stack>
      </Stack>
      <Stack direction="column" width="100%" data-testid="van-stock-section">
        <InputLabel>
          <LabelTooltip
            label={
              <Typography variant="inputLabel">
                <FormattedMessage id="procurement.label.selectVanStockBundles" />
              </Typography>
            }
            tooltipLabel={
              <Typography color="white" variant="body2">
                <FormattedMessage id="procurement.label.selectVanStockBundles.tooltip" />
              </Typography>
            }
          />
        </InputLabel>
        <MuiAutocomplete
          size="small"
          multiple
          disablePortal
          options={vanStockBundleOptions}
          value={selectedVanStockBundles}
          getOptionLabel={(option) => option.title || option.id?.value || ''}
          isOptionEqualToValue={(option, value) => option.id?.value === value.id?.value}
          onChange={(_, newValue) => {
            setSelectedVanStockBundleIds(newValue.map((b) => b.id?.value).filter(isNotNullish));
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              variant="outlined"
              placeholder={
                selectedVanStockBundles.length === 0
                  ? intl.formatMessage({
                      id: 'procurement.label.selectVanStockBundlesPlaceholder',
                    })
                  : ''
              }
              error={selectedVanStockBundles.length === 0}
              fullWidth
            />
          )}
          renderOption={(props, option, { selected }) => (
            <li {...props}>
              <Checkbox checked={selected} />
              {option.title || option.id?.value}
            </li>
          )}
          slotProps={{
            listbox: {
              style: { maxHeight: 250, overflow: 'auto' },
            },
          }}
        />
      </Stack>
      <Stack direction="column" gap={1} width="100%" data-testid="installation-kit-section">
        <Select
          label={intl.formatMessage({
            id: 'procurement.label.selectInstallationKitVersion',
          })}
          toolTip="procurement.label.selectInstallationKitVersion.tooltip"
          value={installationKit}
          onChange={(e) => setInstallationKit(e.target.value)}
          name="installationKit"
          required
          fullWidth
          error={!installationKit}
          size="small"
          data-testid="installation-kit-select"
        >
          {installationKitBundles.map((bundle) => (
            <MenuItem
              key={bundle.id?.value}
              value={bundle.id?.value}
              data-testid={`installation-kit-option-${bundle.id?.value}`}
            >
              {bundle.title}
            </MenuItem>
          ))}
        </Select>
      </Stack>
    </Stack>
  );
}

export default function ProcurementHeader() {
  const installationGroundworkId = useGroundwork().groundwork.id?.value;
  const solutionId = useEnergySolutionId();
  const country = useCountry();
  const { validationWarnings } = useProcurementValidation(installationGroundworkId, solutionId, country);

  return (
    <Stack direction="column" gap={1} data-testid="procurement-header-content">
      {validationWarnings?.[ProcurementValidationWarningKeys.BOM_BUNDLE_OUTDATED]}
      <div data-testid="procurement-information-header">
        <InformationHeader />
      </div>
      <div data-testid="procurement-selection-header">
        <SelectionHeader />
      </div>
    </Stack>
  );
}
