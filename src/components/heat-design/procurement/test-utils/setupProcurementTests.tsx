import {
  DesignedBillOfMaterials,
  DesignedBomStatus,
  ProcuredBillOfMaterials,
  ProcurementItemSource,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { ReactNode } from 'react';
import { GroundworkContextProvider } from '../../../../context/groundwork-context';
import untypedAsaHouse from '../../../../tests/heat-loss/asa_house_response.json';
import {
  billOfMaterialsTestSetup,
  BUNDLE1_ID,
  BUNDLE2_ID,
  DUPLICATE_ERP_ID,
  ERP_ID1,
  ERP_ID2,
  ERP_ID3,
  ERP_ID4,
  EXTRA_ERP_ITEM,
  REMOVED_ERP_ITEM_ID,
  TEMPLATE1_ID,
  TEMPLATE2_ID,
  REGION_ID,
} from '../../../bill-of-materials/test-utils';
import { Item, MiscellaneousItem } from '../../../bill-of-materials/types';
import { ProcurementDataManagerInitialState } from '../ProcurementDataManager';
import { ProcurementProviders } from '../ProcurementProviders';
import { trpcMsw } from '../../../../tests/utils/testUtils';
import { GERMAN_HOUSE_GROUNDWORK } from 'tests/heat-loss/fixtures/germanHouseFixtures';
import { createRouterMock, useRouter } from '@mocks/next/router';
import { mocks } from '../../../../tests/utils/mockedTrpcCalls';
import { solution as asaSolution } from '../../../../tests/heat-loss/fixtures/asaTestData';
import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
export const BOM_ID = 'bom-id';
export const GROUNDWORK_ID = 'f3541c9d-4667-4e03-97ff-66ecf4f9b7c2';
export const INSTALLATION_BOM_PROJECT_ID = '79a6cd15-08cb-4b0f-9947-658966657244';

export const extraItems: Item[] = [{ ...EXTRA_ERP_ITEM, includedInBundles: [] }];

// Test data for procurement
export const testProcurementData: ProcuredBillOfMaterials = {
  id: {
    value: '458828e9-c47f-4b79-90b5-9bb7e62cd2f3',
  },
  installationBomProjectId: {
    value: INSTALLATION_BOM_PROJECT_ID,
  },
  staticBundleDependencies: [],
  items: [
    {
      originalItemId: { value: '44b08739-ecb0-42e3-bb57-0a9918615e8e' },
      selectedItemId: { value: '44b08739-ecb0-42e3-bb57-0a9918615e8e' },
      orderQuantity: 5,
      source: ProcurementItemSource.PROCUREMENT_ITEM_SOURCE_BOM,
    },
  ],
  comment: undefined,
  createdAt: new Date(),
  updatedAt: new Date(),
  orderedAt: undefined,
  orderedItems: [],
};

export const testSolutionData: Omit<DesignedBillOfMaterials, 'miscellaneousItems'> & {
  miscellaneousItems: MiscellaneousItem[];
} = {
  id: { value: BOM_ID },
  installationBomProjectId: { value: INSTALLATION_BOM_PROJECT_ID },
  bundles: [
    {
      bundleId: { value: BUNDLE1_ID },
      bundleCollectionId: { value: TEMPLATE1_ID },
      items: [
        {
          itemId: { value: ERP_ID1 },
          quantity: 5,
        },
        {
          itemId: { value: ERP_ID3 },
          quantity: 5,
        },
        {
          itemId: { value: ERP_ID4 },
          quantity: 0,
        },
      ],
    },
    {
      bundleId: { value: BUNDLE2_ID },
      bundleCollectionId: { value: TEMPLATE2_ID },
      items: [
        {
          itemId: { value: ERP_ID1 },
          quantity: 3,
        },
      ],
    },
  ],
  miscellaneousItems: [
    {
      type: 'erp',
      instructions: '',
      itemId: ERP_ID2,
      quantity: 3,
      createdAt: new Date(),
    },
    {
      type: 'erp',
      instructions: '',
      itemId: REMOVED_ERP_ITEM_ID,
      quantity: 3,
      createdAt: new Date(),
    },
    {
      type: 'erp',
      instructions: '',
      itemId: DUPLICATE_ERP_ID,
      quantity: 30,
      createdAt: new Date(),
    },
    {
      type: 'custom',
      name: '',
      cost: 100,
      itemId: 'custom-item',
      quantity: 3,
      createdAt: new Date(),
    },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
  status: DesignedBomStatus.DESIGNED_BOM_STATUS_ONGOING,
};

export function ProcurementTestWrapper({
  children,
  initialState,
}: {
  children: ReactNode;
  initialState?: ProcurementDataManagerInitialState;
}) {
  return (
    <GroundworkContextProvider solutionId={SOLUTION_ID}>
      <ProcurementProviders initialState={initialState}>{children}</ProcurementProviders>
    </GroundworkContextProvider>
  );
}

// Procurement-specific MSW handlers
export const procurementHandlers = [
  mocks.getGrpcEnergySolution.custom({
    ...asaSolution,
    region: {
      id: { value: REGION_ID },
      name: 'Region',
      timeZone: 'Timezone',
      iso3166: {
        $case: 'country',
        country: Country.COUNTRY_GB,
      },
    },
  }),
  trpcMsw.BillOfMaterials.loadProcuredBom.query(() => testProcurementData),
  trpcMsw.BillOfMaterials.loadDesignedBom.query(() => testSolutionData),
  trpcMsw.BillOfMaterials.saveProcuredBom.mutation(() => ({ value: 'new-procurement-id' })),
  trpcMsw.BillOfMaterials.sendItemsToErp.mutation(() => ({})),

  trpcMsw.AiraBackend.getGroundworkForSolution.query(() => Promise.resolve(GERMAN_HOUSE_GROUNDWORK)),
  mocks.energySolutionDiff,
  trpcMsw.InstallationProject.getInstallationProject.query(() => ({}) as InstallationProject),
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: untypedAsaHouse as unknown as ProtoHeatDesign,
    isLocked: false,
    result: undefined,
    events: [],
    updatedAt: new Date(),
  })),
];

export function setupProcurementTests() {
  billOfMaterialsTestSetup(procurementHandlers);

  beforeEach(() => {
    useRouter.mockImplementation(() => ({
      ...createRouterMock(),
      pathname: '/solution/[solution]/procurement',
      query: {
        solution: 'test-solution-id',
      },
    }));
  });
}
