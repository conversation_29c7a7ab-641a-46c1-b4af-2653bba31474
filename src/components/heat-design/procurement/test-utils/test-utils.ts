import { ProcurementItemType } from '../types';
import { fireEvent, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

export function getEquivalenceGroupCards(type?: ProcurementItemType) {
  const identifier = type ? `equivalence-group-card-${type}` : 'equivalence-group-card';
  const regex = new RegExp(identifier);
  return screen.getAllByTestId(regex);
}

export async function expandEquivalenceGroupCard(card: HTMLElement | undefined) {
  if (!card) {
    return;
  }
  const expandToggle = within(card).getByTestId('expand-toggle');

  fireEvent.click(expandToggle!);
  await sleep(300); // Wait for animation to finish
}

export async function selectInstallationKit(bundleId: string) {
  const select = screen.getByTestId('installation-kit-select').querySelector('.MuiSelect-select');
  await userEvent.click(select!);
  const option = await screen.findByTestId(`installation-kit-option-${bundleId}`);
  fireEvent.click(option!);
}

export async function getCardDetails(card: HTMLElement) {
  return within(card).queryByTestId('equivalence-group-card-details');
}

export async function getProcurementItemCards(card: HTMLElement) {
  return within(card).getAllByTestId('procurement-item-container');
}

export async function getSummedBomQuantity(card: HTMLElement) {
  return (await within(card).findByTestId('summed-bom-quantity'))?.textContent ?? '';
}

export async function getSummedVanStockQuantity(card: HTMLElement) {
  return (await within(card).findByTestId('summed-van-stock-quantity'))?.textContent ?? '';
}

export async function getSummedAikQuantity(card: HTMLElement) {
  return (await within(card).findByTestId('summed-aik-quantity'))?.textContent ?? '';
}

export async function getProcuredQuantity(card: HTMLElement) {
  return (await within(card).findByTestId('procured-quantity'))?.querySelector('input')?.value ?? '';
}

export async function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export async function getProcurementItemQuantity(card: HTMLElement, type: 'bom' | 'van-stock' | 'aik') {
  return (await within(card).findByTestId(`procurement-item-${type}-quantity`))?.textContent ?? '';
}
