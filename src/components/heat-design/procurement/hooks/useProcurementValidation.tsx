import { useMemo } from 'react';
import { api } from 'utils/api';
import { Box, Stack, Tooltip, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { useProcurementStore } from '../store/ProcurementContext';
import getDesignReviewState from 'components/quotation/helpers/getDesignReviewState';
import { DesignReviewStatus } from 'components/heat-design/stores/HeatDesignUIStore';
import { DesignedBomStatus } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';
import { isAnyDesignedBomBundleOutdated } from 'components/heat-design/bill-of-materials/utils';
import { FormatForTooltip } from '@ui/components/Tooltip/Tooltip';
import { WarningIcon } from 'components/heat-design/components/WarningIcon';
import { WarningBox } from 'components/heat-design/components/WarningBox';
import { EnergySolutionStage } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';

export enum ProcurementValidationErrorKeys {
  BOM_NOT_READY = 'procurement.actions.bomNotReadyForProcurement',
  INSTALLATION_DATE_REQUIRED = 'procurement.actions.installationDateRequired',
  BOM_DEPRECATED = 'procurement.actions.bomDeprecated',
  ALREADY_ORDERED = 'procurement.actions.ordered',
  INSTALLATION_DATE_PASSED = 'procurement.warning.installationDatePassed',
  INSTALLATION_KIT_REQUIRED = 'procurement.actions.installationKitRequired',
  VAN_STOCK_BUNDLE_REQUIRED = 'procurement.actions.vanStockBundleRequired',
  COMMENT_MISSING = 'procurement.actions.procurementCommentMissing',
  DESIGN_REVIEW_NOT_ACCEPTED = 'procurement.actions.designReviewNotAccepted',
  SOLUTION_NOT_READY_FOR_ORDER = 'procurement.actions.solutionNotReadyForOrder',
}

export enum ProcurementValidationWarningKeys {
  BOM_BUNDLE_OUTDATED = 'procurement.warning.bomBundleOutdated',
  INSTALLATION_DATE_PASSED = 'procurement.warning.installationDatePassed',
}

export type ProcurementValidationErrors = {
  [key in ProcurementValidationErrorKeys]?: React.ReactNode;
};

export type ProcurementValidationWarnings = {
  [key in ProcurementValidationWarningKeys]?: React.ReactNode;
};

export interface ProcurementValidationResult {
  validationErrors: ProcurementValidationErrors;
  validationWarnings?: ProcurementValidationWarnings;
  isLoading: boolean;
}

/**
 * Fetches and computes procurement validation errors and warnings.
 * Handles all required data fetching and validation logic.
 */
export function useProcurementValidation(
  installationGroundworkId?: string,
  energySolutionId?: string,
  country?: Country,
): ProcurementValidationResult {
  const { formatMessage } = useIntl();
  const installationKit = useProcurementStore((state) => state.installationKit);
  const selectedVanStockBundleIds = useProcurementStore((s) => s.selectedVanStockBundleIds);
  const comment = useProcurementStore((state) => state.comment);

  const { data: heatDesignData, isPending: isHeatDesignPending } = api.HeatLossCalculator.loadHeatDesign.useQuery(
    {
      installationGroundworkId: installationGroundworkId!,
      energySolutionId: energySolutionId!,
      demoData: false,
    },
    {
      enabled: !!installationGroundworkId && !!energySolutionId,
    },
  );
  const { data: designedBomData, isPending: isDesignedBomPending } = api.BillOfMaterials.loadDesignedBom.useQuery(
    { installationGroundworkId: installationGroundworkId!, energySolutionId: energySolutionId! },
    {
      enabled: !!installationGroundworkId && !!energySolutionId,
    },
  );
  const { data: installationProject, isPending: isInstallationProjectPending } =
    api.InstallationProject.getInstallationProject.useQuery(
      { solutionId: energySolutionId! },
      { enabled: !!energySolutionId },
    );

  const { data: energySolution, isPending: isSolutionPending } = api.AiraBackend.getGrpcEnergySolution.useQuery(
    { solution: energySolutionId! },
    { enabled: !!energySolutionId },
  );
  const { data: bundleCollections } = api.BillOfMaterials.getBundleCollections.useQuery(
    { country: country! },
    { enabled: !!country },
  );

  const isLoading = isHeatDesignPending || isDesignedBomPending || isInstallationProjectPending || isSolutionPending;

  const installationDate = installationProject?.timeline?.start;
  const isInstallationDatePassed = installationDate ? new Date() > installationDate : false;

  const validationErrors = useMemo(() => {
    if (isLoading) return {};
    const errors: ProcurementValidationErrors = {};

    // BOM deprecated
    if (designedBomData && heatDesignData) {
      const bomDate = designedBomData.updatedAt;
      const heatDesignDate = heatDesignData.updatedAt;
      if (bomDate && heatDesignDate && bomDate.getTime() < heatDesignDate.getTime()) {
        errors[ProcurementValidationErrorKeys.BOM_DEPRECATED] = (
          <Typography key="bomDeprecated">
            <FormattedMessage
              id={ProcurementValidationErrorKeys.BOM_DEPRECATED}
              values={{
                link: (chunks: React.ReactNode) => (
                  <a
                    href={`/solution/${energySolutionId}/bill-of-materials`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{ color: 'inherit', textDecoration: 'underline' }}
                  >
                    {chunks}
                  </a>
                ),
              }}
              description="Message shown when the BOM is older than the heat design. The link should lead to the BOM page for the current project."
            />
          </Typography>
        );
      }
    }
    // Installation date required
    if (!installationDate) {
      errors[ProcurementValidationErrorKeys.INSTALLATION_DATE_REQUIRED] = (
        <Typography key="installationDateRequired">
          <FormattedMessage id={ProcurementValidationErrorKeys.INSTALLATION_DATE_REQUIRED} />
        </Typography>
      );
    }
    // Installation date passed
    if (isInstallationDatePassed) {
      errors[ProcurementValidationErrorKeys.INSTALLATION_DATE_PASSED] = (
        <Typography key="installationDatePassed">
          <FormattedMessage id={ProcurementValidationErrorKeys.INSTALLATION_DATE_PASSED} />
        </Typography>
      );
    }
    // BOM not ready
    if (designedBomData?.status !== DesignedBomStatus.DESIGNED_BOM_STATUS_READY_FOR_PROCUREMENT) {
      errors[ProcurementValidationErrorKeys.BOM_NOT_READY] = (
        <Typography key="bomNotReady">
          <FormattedMessage id={ProcurementValidationErrorKeys.BOM_NOT_READY} />
        </Typography>
      );
    }
    // Installation kit required
    if (!installationKit) {
      errors[ProcurementValidationErrorKeys.INSTALLATION_KIT_REQUIRED] = (
        <Typography key="installationKitRequired">
          <FormattedMessage id={ProcurementValidationErrorKeys.INSTALLATION_KIT_REQUIRED} />
        </Typography>
      );
    }
    // Van stock bundle required
    if (!selectedVanStockBundleIds || selectedVanStockBundleIds.length === 0) {
      errors[ProcurementValidationErrorKeys.VAN_STOCK_BUNDLE_REQUIRED] = (
        <Typography key="vanStockBundleRequired">
          <FormattedMessage id={ProcurementValidationErrorKeys.VAN_STOCK_BUNDLE_REQUIRED} />
        </Typography>
      );
    }
    // Procurement comment missing
    if (comment === undefined || comment.length === 0) {
      errors[ProcurementValidationErrorKeys.COMMENT_MISSING] = (
        <Typography key="procurementCommentMissing">
          <FormattedMessage id={ProcurementValidationErrorKeys.COMMENT_MISSING} />
        </Typography>
      );
    }

    // If customer design review has not been accepted
    const designReviewState = getDesignReviewState(energySolution?.solution?.presentation);
    if (designReviewState?.status !== DesignReviewStatus.ACCEPTED) {
      errors[ProcurementValidationErrorKeys.DESIGN_REVIEW_NOT_ACCEPTED] = (
        <Typography key="designReviewNotAccepted">
          <FormattedMessage id={ProcurementValidationErrorKeys.DESIGN_REVIEW_NOT_ACCEPTED} />
        </Typography>
      );
    }

    // If solution stage is not "ready_for_order"
    const unsupportedStages = ['openForModification', 'priceLocked', 'productsLocked', 'cancelled'] as NonNullable<
      EnergySolutionStage['details']
    >['$case'][];
    const currentStage = energySolution?.solution?.presentation?.currentStage?.details?.$case;
    if (currentStage && unsupportedStages.includes(currentStage)) {
      errors[ProcurementValidationErrorKeys.SOLUTION_NOT_READY_FOR_ORDER] = (
        <Typography key="solutionNotReadyForOrder">
          <FormattedMessage
            id="procurement.actions.solutionNotReadyForOrder"
            values={{
              link: (chunks: React.ReactNode) => (
                <a
                  href={`/solution/${energySolutionId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ color: 'inherit', textDecoration: 'underline' }}
                >
                  {chunks}
                </a>
              ),
            }}
          />
        </Typography>
      );
    }

    return errors;
  }, [
    isLoading,
    designedBomData,
    heatDesignData,
    installationDate,
    isInstallationDatePassed,
    energySolutionId,
    comment,
    installationKit,
    selectedVanStockBundleIds,
    energySolution?.solution?.presentation,
  ]);

  const validationWarnings = useMemo(() => {
    const warnings: ProcurementValidationWarnings = {};

    if (designedBomData) {
      const isBundleOutdated = isAnyDesignedBomBundleOutdated(designedBomData, bundleCollections);

      if (isBundleOutdated) {
        warnings[ProcurementValidationWarningKeys.BOM_BUNDLE_OUTDATED] = (
          <Box width="fit-content">
            <WarningBox contentId="procurement.warning.bomBundleOutdated" />
          </Box>
        );
      }
    }

    if (isInstallationDatePassed) {
      warnings[ProcurementValidationWarningKeys.INSTALLATION_DATE_PASSED] = (
        <Tooltip
          title={FormatForTooltip(
            formatMessage({
              id: 'procurement.warning.installationDatePassed',
            }),
          )}
        >
          <Stack data-testid="installation-date-warning">
            <WarningIcon x={2.5} y={2.5} iconWidth={20} iconHeight={20} canvasWidth={20} canvasHeight={20} />
          </Stack>
        </Tooltip>
      );
    }

    return warnings;
  }, [bundleCollections, designedBomData, isInstallationDatePassed, formatMessage]);

  return { validationErrors, validationWarnings, isLoading };
}
