import {
  BundleItem,
  ProcurementItem as ProtoProcurementItem,
  ProcurementItemSource,
} from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { useGroundwork } from '../../../context/groundwork-context';
import { api } from '../../../utils/api';
import {
  isInstallationKitBundleCollection,
  isVanStockBundleCollection,
  isVanStockBundleForRegion,
} from '../../bill-of-materials/utils';
import { useErpItemsWithLabels } from '../../../hooks/useErpItemsWithLabels';
import React, { useEffect, useMemo, useState } from 'react';
import { useProcurementStore } from './store/ProcurementContext';
import {
  CustomProcurementItem,
  DATA_SOURCES,
  EquivalenceGroups,
  ErpProcurementItem,
  ExtraProcurementItem,
  PROCUREMENT_ITEM_TYPES,
} from './types';
import { v4 as uuidv4 } from 'uuid';
import { Item, MiscellaneousItem, DesignedBomBundleItem, RadiatorItem } from 'components/bill-of-materials/types';
import { useCountry } from '../../../hooks/useCountry';
import { CircularProgress, Stack } from '@mui/material';
import { isNotNullish, keyIsNotNullish } from 'utils/isNotNullish';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { getEnabledNewRadiators } from '../bill-of-materials/utils';
import { groupBy } from 'lodash';
import { useRooms } from '../stores/RoomsStore';
import { useRadiatorsForCountry } from '../hooks/useRadiators';
import { getRadiatorDisplayName } from '../radiators/utils';
import { defaultSelectedVanStockBundles } from './utils';

type AddItemsToEquivalenceGroupsBaseParams = {
  groups: EquivalenceGroups;
  erpItems: Record<string, Item>;
};

type AddBomItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: DesignedBomBundleItem[];
  source: typeof DATA_SOURCES.BOM;
};

type AddVanStockItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: BundleItem[];
  source: typeof DATA_SOURCES.VANSTOCK;
};

type AddAikItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: BundleItem[];
  source: typeof DATA_SOURCES.AIK;
};

type AddMiscellaneousItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: MiscellaneousItem[];
  source: typeof DATA_SOURCES.BOM;
};

type AddExtraItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: Item[];
  source: typeof DATA_SOURCES.EXTRA;
};

type AddRadiatorItemsToEquivalenceGroupsParams = AddItemsToEquivalenceGroupsBaseParams & {
  items: RadiatorItem[];
  source: typeof DATA_SOURCES.RADIATORS;
};

type AddItemsToEquivalenceGroupsParams =
  | AddBomItemsToEquivalenceGroupsParams
  | AddVanStockItemsToEquivalenceGroupsParams
  | AddAikItemsToEquivalenceGroupsParams
  | AddMiscellaneousItemsToEquivalenceGroupsParams
  | AddExtraItemsToEquivalenceGroupsParams
  | AddRadiatorItemsToEquivalenceGroupsParams;

type ErpSourceItem = DesignedBomBundleItem | BundleItem | MiscellaneousItem | RadiatorItem;

function addErpItemToEquivalenceGroups(
  newGroups: EquivalenceGroups,
  item: ErpSourceItem,
  erpItems: Record<string, Item>,
  source: typeof DATA_SOURCES.BOM | typeof DATA_SOURCES.VANSTOCK | typeof DATA_SOURCES.AIK,
): void {
  let itemId = item.itemId;
  if (!itemId) {
    return;
  }
  if (typeof itemId !== 'string') {
    itemId = itemId.value;
  }
  const erpItem = erpItems[itemId];
  if (!erpItem) {
    return;
  }
  const equivalenceGroupId = erpItem.equivalenceGroupId?.value ?? itemId;
  let quantity = erpItem.quantity ?? 0;
  if ('quantity' in item) {
    quantity = item.quantity ?? 0;
  } else if ('defaultQuantity' in item) {
    quantity = item.defaultQuantity ?? 0;
  }
  const equivalenceGroupItem: ErpProcurementItem = {
    itemId,
    aikQuantity: source === DATA_SOURCES.AIK ? quantity : 0,
    vanStockQuantity: source === DATA_SOURCES.VANSTOCK ? quantity : 0,
    bomQuantity: source === DATA_SOURCES.BOM ? quantity : 0,
    erpItem,
    type: PROCUREMENT_ITEM_TYPES.ERP,
  };
  if (!newGroups[equivalenceGroupId]) {
    newGroups[equivalenceGroupId] = {
      id: equivalenceGroupId,
      procuredQuantity: 0,
      items: {
        [itemId]: equivalenceGroupItem,
      },
      selectedItemId: itemId,
    };
  } else {
    const existingItemInGroup = newGroups[equivalenceGroupId].items[itemId];
    if (existingItemInGroup) {
      newGroups[equivalenceGroupId].items[itemId] = {
        ...existingItemInGroup,
        bomQuantity: (existingItemInGroup.bomQuantity += equivalenceGroupItem.bomQuantity),
        vanStockQuantity: (existingItemInGroup.vanStockQuantity += equivalenceGroupItem.vanStockQuantity),
        aikQuantity: (existingItemInGroup.aikQuantity += equivalenceGroupItem.aikQuantity),
      };
    } else {
      newGroups[equivalenceGroupId].items[itemId] = equivalenceGroupItem;
    }
  }
}

export function addItemsToEquivalenceGroups(options: AddItemsToEquivalenceGroupsParams) {
  const newGroups = {
    ...options.groups,
  };
  const source = options.source;
  switch (source) {
    case DATA_SOURCES.EXTRA: {
      options.items.forEach((extraItem) => {
        const id = extraItem.equivalenceGroupId?.value ?? extraItem.id.value;
        newGroups[id] = {
          id: id,
          procuredQuantity: 0,
          items: {
            [extraItem.id.value]: {
              itemId: extraItem.id.value,
              bomQuantity: 0,
              vanStockQuantity: 0,
              aikQuantity: 0,
              erpItem: extraItem,
              type: PROCUREMENT_ITEM_TYPES.EXTRA,
            } satisfies ExtraProcurementItem,
          },
          selectedItemId: extraItem.id.value,
        };
      });
      break;
    }
    case DATA_SOURCES.BOM: {
      options.items.forEach((item) => {
        if ('type' in item && item.type === 'custom') {
          const customItem = item;
          newGroups[customItem.itemId] = {
            id: customItem.itemId,
            procuredQuantity: 0,
            items: {
              [customItem.itemId]: {
                itemId: customItem.itemId,
                name: customItem.name,
                cost: customItem.cost,
                bomQuantity: customItem.quantity ?? 0,
                vanStockQuantity: 0,
                aikQuantity: 0,
                type: PROCUREMENT_ITEM_TYPES.CUSTOM,
              } satisfies CustomProcurementItem,
            },
            selectedItemId: customItem.itemId,
          };
        } else {
          addErpItemToEquivalenceGroups(newGroups, item, options.erpItems, DATA_SOURCES.BOM);
        }
      });
      break;
    }
    case DATA_SOURCES.VANSTOCK: {
      options.items.forEach((item) => {
        addErpItemToEquivalenceGroups(newGroups, item, options.erpItems, DATA_SOURCES.VANSTOCK);
      });
      break;
    }
    case DATA_SOURCES.AIK: {
      options.items.forEach((item) => {
        addErpItemToEquivalenceGroups(newGroups, item, options.erpItems, DATA_SOURCES.AIK);
      });
      break;
    }
    case DATA_SOURCES.RADIATORS: {
      options.items.forEach((item) => {
        const { itemId, name, erpItem, quantity } = item;
        if (!itemId) {
          return;
        }
        if (!erpItem) {
          newGroups[itemId] = {
            id: itemId,
            procuredQuantity: 0,
            items: {
              [itemId]: {
                itemId,
                name,
                bomQuantity: quantity,
                vanStockQuantity: 0,
                aikQuantity: 0,
                type: PROCUREMENT_ITEM_TYPES.CUSTOM,
              } satisfies CustomProcurementItem,
            },
            selectedItemId: itemId,
          };
        } else {
          addErpItemToEquivalenceGroups(newGroups, item, options.erpItems, DATA_SOURCES.BOM);
        }
      });
      break;
    }
    default:
      return source satisfies never;
  }
  return newGroups;
}

export function setDefaultProcuredQuantityForItems(groups: EquivalenceGroups, savedItems?: ProtoProcurementItem[]) {
  const newGroups = {
    ...groups,
  };
  // Hashmap of original item IDs to their procurement item
  const savedItemsMap: Record<string, ProtoProcurementItem> = {};
  savedItems?.forEach((item) => {
    if (item.originalItemId) {
      savedItemsMap[item.originalItemId.value] = item;
    }
  });

  Object.entries(newGroups).forEach(([key, value]) => {
    const procuredQuantity = Object.values(value.items).reduce((acc, item) => {
      acc += item.bomQuantity - item.vanStockQuantity - item.aikQuantity;
      return acc;
    }, 0);
    const savedItem = savedItemsMap[key];
    const savedQuantity = savedItem?.orderQuantity;
    if (newGroups[key]) {
      newGroups[key].procuredQuantity = savedQuantity ?? Math.max(0, procuredQuantity);
      if (savedItem?.selectedItemId) {
        newGroups[key].selectedItemId = savedItem.selectedItemId.value;
      }
    }
  });
  return newGroups;
}

export function addUnselectedErpItemsToEquivalenceGroups(groups: EquivalenceGroups, items: Item[]) {
  const newGroups = {
    ...groups,
  };
  // We need to get all ERP items that are *already* in existing groups so we don't add them twice
  const erpItemsInExistingGroups = Object.values(groups).reduce((acc, group) => {
    Object.values(group.items).forEach((item) => {
      if (isProcurementItemFromErp(item) && keyIsNotNullish('erpItem', item)) {
        acc.add(item.erpItem.id.value);
      }
    });
    return acc;
  }, new Set<string>());

  // Then for all remaining items that have an equivalency group ID but are not in any group yet, add them to
  // the correct groups
  items
    .filter(keyIsNotNullish('equivalenceGroupId'))
    .filter((item) => !erpItemsInExistingGroups.has(item.id.value))
    .forEach((item) => {
      const id = item.equivalenceGroupId.value;
      const existingGroup = newGroups[id];

      if (existingGroup) {
        const newItems = {
          ...existingGroup.items,
          [item.id.value]: {
            itemId: item.id.value,
            bomQuantity: 0,
            vanStockQuantity: 0,
            aikQuantity: 0,
            erpItem: item,
            type: PROCUREMENT_ITEM_TYPES.ERP,
          } satisfies ErpProcurementItem,
        };
        newGroups[id] = {
          ...existingGroup,
          items: newItems,
        };
      }
    });
  return newGroups;
}

export type ProcurementDataManagerInitialState = {
  installationKit?: string;
  extraItems?: Item[];
  comment?: string;
  vanStockBundleIds?: string[];
};
export type ProcurementDataManagerProps = {
  children: React.ReactNode;
  initialState?: ProcurementDataManagerInitialState;
};

export function ProcurementDataManager({ children, initialState }: Readonly<ProcurementDataManagerProps>) {
  const energySolutionId = useEnergySolutionId();
  const { groundwork, countryCode } = useGroundwork();
  const rooms = useRooms();
  const catalogueRadiators = useRadiatorsForCountry(countryCode);
  const setEquivalenceGroups = useProcurementStore((s) => s.setEquivalenceGroups);
  const setInstallationKit = useProcurementStore((s) => s.setInstallationKit);
  const selectedVanStockBundleIds = useProcurementStore((s) => s.selectedVanStockBundleIds);
  const setSelectedVanStockBundleIds = useProcurementStore((s) => s.setSelectedVanStockBundleIds);
  const setComment = useProcurementStore((s) => s.setComment);
  const setOrderedAt = useProcurementStore((s) => s.setOrderedAt);
  const extraItems = useProcurementStore((s) => s.extraItems);
  const [didSetDefaultData, setDidSetDefaultData] = useState(false);
  const { data: energySolution } = api.AiraBackend.getGrpcEnergySolution.useQuery(
    { solution: energySolutionId! },
    {
      enabled: !!energySolutionId,
    },
  );
  const currentRegionId = energySolution?.solution?.region?.id?.value;

  const country = useCountry();
  // Fetch procurement data
  const { data: procurementData } = api.BillOfMaterials.loadProcuredBom.useQuery(
    {
      installationGroundworkId: groundwork.id!.value,
      energySolutionId: energySolutionId!,
    },
    {
      enabled: !!groundwork.id?.value && !!energySolutionId,
    },
  );

  // Fetch BOM data
  const { data: bomData } = api.BillOfMaterials.loadDesignedBom.useQuery(
    {
      installationGroundworkId: groundwork.id!.value,
      energySolutionId: energySolutionId!,
    },
    {
      enabled: !!groundwork.id?.value && !!energySolutionId,
    },
  );
  const bundleCollections = api.BillOfMaterials.getBundleCollections.useQuery({
    country: country,
  });
  const installationKitUUID = useProcurementStore((s) => s.installationKit);
  const setExtraItems = useProcurementStore((s) => s.setExtraItems);

  const vanStockBundles = useMemo(() => {
    if (currentRegionId === undefined) {
      return undefined;
    }
    return bundleCollections.data?.bundleCollections
      ?.filter(isVanStockBundleCollection)
      .flatMap((collection) => collection.bundles)
      .filter(isVanStockBundleForRegion(currentRegionId));
  }, [bundleCollections.data, currentRegionId]);
  const installationKitBundles = useMemo(() => {
    if (bundleCollections.data) {
      return bundleCollections.data.bundleCollections
        .filter(isInstallationKitBundleCollection)
        .map((collection) => collection.bundles)
        .flat()
        .toSorted((a, b) => {
          const aTitle = a.createdAt;
          const bTitle = b.createdAt;
          if (!aTitle || !bTitle) return 0; // Handle cases where createdAt might be undefined
          return bTitle?.valueOf() - aTitle?.valueOf();
        });
    }
    return [];
  }, [bundleCollections.data]);
  // Get selected installation kit
  const selectedInstallationKit = useMemo(() => {
    const bundleCollection = bundleCollections.data?.bundleCollections?.find((c) => {
      return c.bundles.find((b) => b.id?.value === installationKitUUID);
    });
    if (!bundleCollection) return undefined;
    return bundleCollection.bundles.find((b) => b.id?.value === installationKitUUID);
  }, [bundleCollections.data, installationKitUUID]);

  // Use the shared hook to get ERP items with labels
  const { itemsMap: erpItems, isLoading, items: erpItemsWithLabels } = useErpItemsWithLabels();

  // Set initial installation kit and van stock bundles if provided
  useEffect(() => {
    if (initialState?.installationKit) {
      setInstallationKit(initialState.installationKit);
    }
    if (initialState?.extraItems?.length) {
      setExtraItems(initialState.extraItems);
    }
    if (initialState?.comment) {
      setComment(initialState.comment);
    }
    if (initialState?.vanStockBundleIds) {
      setSelectedVanStockBundleIds(initialState.vanStockBundleIds);
    }
  }, [initialState, setInstallationKit, setExtraItems, setComment, setSelectedVanStockBundleIds]);

  // Load extraItems from procurementData
  useEffect(() => {
    if (procurementData?.items && erpItems) {
      const extraItemIds = procurementData.items
        .filter(
          (item) =>
            item.source === ProcurementItemSource.PROCUREMENT_ITEM_SOURCE_PROCUREMENT && item.originalItemId?.value,
        )
        .map((item) => item.selectedItemId?.value ?? item.originalItemId?.value)
        .filter(isNotNullish);
      const loadedExtraItems = extraItemIds.map((id) => erpItems[id]).filter((item): item is Item => !!item);
      if (loadedExtraItems.length > 0) {
        setExtraItems(loadedExtraItems);
      }
    }
  }, [procurementData?.items, erpItems, setExtraItems]);

  // Load saved installation kit and van stock bundles from procurementData
  useEffect(() => {
    if (!didSetDefaultData && procurementData === null && vanStockBundles !== undefined) {
      // procurementData being null, rather than undefined, means that we have finished the loading but there was no stored procurement data.
      setSelectedVanStockBundleIds(defaultSelectedVanStockBundles(vanStockBundles));
      setDidSetDefaultData(true);
    } else if (procurementData?.staticBundleDependencies && procurementData.staticBundleDependencies.length > 0) {
      const staticIds = procurementData.staticBundleDependencies.map((id) => id.value);
      // Installation kit (single)
      const installationKitId = installationKitBundles.find(
        (installationKitBundle) => installationKitBundle.id && staticIds.includes(installationKitBundle.id.value),
      )?.id;
      if (installationKitId) {
        setInstallationKit(installationKitId.value);
      }

      // Van stock bundles (multi)
      const vanStockIds =
        vanStockBundles
          ?.filter((bundle) => bundle.id && staticIds.includes(bundle.id.value))
          .map((bundle) => bundle.id?.value)
          .filter(isNotNullish) ?? [];
      setSelectedVanStockBundleIds(vanStockIds);
    }
  }, [
    procurementData,
    setInstallationKit,
    installationKitBundles,
    vanStockBundles,
    setSelectedVanStockBundleIds,
    setDidSetDefaultData,
    didSetDefaultData,
  ]);

  // Main data population effect
  useEffect(() => {
    let groups: EquivalenceGroups = {};
    if (!bomData) {
      return;
    }

    const allRadiators = getEnabledNewRadiators(rooms);
    const grouped = groupBy(allRadiators, (r) => r.specificationReferenceId ?? r.uid);

    const radiatorItems = Object.values(grouped).map((radiators) => {
      const radiator = radiators?.[0];
      if (!radiator) {
        throw new Error('No radiator found');
      }
      const catalogueRadiator = radiator.specificationReferenceId
        ? catalogueRadiators?.data?.radiators?.find((r) => r.radiatorId?.value === radiator.specificationReferenceId)
        : undefined;
      const erpItem = catalogueRadiator?.erpId
        ? erpItemsWithLabels.find((e) => e.erpId === catalogueRadiator?.erpId)
        : undefined;
      const name = getRadiatorDisplayName(catalogueRadiator, erpItem, radiator);
      return {
        itemId: erpItem ? erpItem.id.value : uuidv4(),
        name: name,
        erpItem,
        quantity: radiators.length,
      };
    });
    const designedBomBundleItems = bomData.bundles.map((bundle) => bundle.items).flat();
    const vanStockItems =
      vanStockBundles
        ?.filter((bundle) => bundle.id?.value && selectedVanStockBundleIds.includes(bundle.id.value))
        .map((bundle) => bundle.items)
        .flat() ?? [];
    const aikItems = selectedInstallationKit?.items ?? [];
    const miscellaneousItems = bomData.miscellaneousItems;
    groups = addItemsToEquivalenceGroups({
      groups,
      items: designedBomBundleItems,
      erpItems,
      source: DATA_SOURCES.BOM,
    });
    groups = addItemsToEquivalenceGroups({
      groups,
      items: vanStockItems,
      erpItems,
      source: DATA_SOURCES.VANSTOCK,
    });
    groups = addItemsToEquivalenceGroups({
      groups,
      items: aikItems,
      erpItems,
      source: DATA_SOURCES.AIK,
    });
    groups = addItemsToEquivalenceGroups({
      groups,
      items: miscellaneousItems,
      erpItems,
      source: DATA_SOURCES.BOM,
    });
    groups = addItemsToEquivalenceGroups({
      groups,
      items: extraItems,
      erpItems,
      source: DATA_SOURCES.EXTRA,
    });
    groups = addItemsToEquivalenceGroups({
      groups,
      items: radiatorItems,
      erpItems,
      source: DATA_SOURCES.RADIATORS,
    });
    // In addition to all the items in BoM etc., we need to also add all remaining ERP items that haven't been
    // selected to the equivalency groups.
    groups = addUnselectedErpItemsToEquivalenceGroups(groups, Object.values(erpItems));

    // Calculate default procured quantity for all groups
    groups = setDefaultProcuredQuantityForItems(groups, procurementData?.items);
    setEquivalenceGroups(groups);

    if (procurementData?.comment !== undefined) {
      setComment(procurementData.comment);
    }

    if (procurementData?.orderedAt !== undefined) {
      setOrderedAt(procurementData.orderedAt);
    }
  }, [
    bomData,
    selectedInstallationKit,
    vanStockBundles,
    erpItems,
    erpItemsWithLabels,
    setEquivalenceGroups,
    setComment,
    setOrderedAt,
    procurementData?.orderedAt,
    procurementData?.items,
    procurementData?.comment,
    extraItems,
    selectedVanStockBundleIds,
    catalogueRadiators.data?.radiators,
    rooms,
  ]);

  return (
    <>
      {isLoading ? (
        <Stack
          alignItems="center"
          justifyContent="center"
          sx={{ width: '100%', height: '100%', position: 'absolute', top: 0, left: 0 }}
          data-testid="procurement-table-loading"
        >
          <CircularProgress />
        </Stack>
      ) : (
        children
      )}
    </>
  );
}
