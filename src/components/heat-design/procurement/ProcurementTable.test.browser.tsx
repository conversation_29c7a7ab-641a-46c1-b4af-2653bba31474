import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import ProcurementTable from './ProcurementTable';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';

setupProcurementTests();

describe('ProcurementTable', () => {
  const renderProcurementTable = async () => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper>
          <ProcurementTable />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('procurement-table-container')).toBeInTheDocument();
    });
  };

  it('should render the procurement table component', async () => {
    await renderProcurementTable();
  });
});
