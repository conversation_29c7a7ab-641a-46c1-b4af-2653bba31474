import { Stack, Typography } from '@mui/material';
import { ColumnDef } from '@tanstack/react-table';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import { useMemo } from 'react';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import { isNotNullish } from 'utils/isNotNullish';
import { useMarketStore } from '../../../utils/stores/marketStore';
import { currencySymbols } from '../../bill-of-materials/types';
import { EquivalenceGroupCard } from './EquivalenceGroupCard';
import { ProcurementTableFilter } from './ProcurementTableFilter';
import { ProcurementTableVanStockHeader } from './ProcurementTableVanStockHeader';
import { useProcurementStore } from './store/ProcurementContext';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { EquivalenceGroup } from './types';
import { LabelTooltip } from '@ui/components/Tooltip/Tooltip';

export const QUANTITY_COLUMNS_WIDTHS = '50px minmax(min-content, max-content) 50px';
const COLUMN_WIDTHS = `${QUANTITY_COLUMNS_WIDTHS} 75px 1fr 100px 100px 100px 100px`;

export const VAN_STOCK_HEADER_MIN_WIDTH = 50;
export const VAN_STOCK_HEADER_MAX_WIDTH = 166;
const NON_TRANSPOSED_HEADER_IDS = ['vanstock', 'bom', 'quantity', 'label'];
export const TRANSPOSE_WIDTH = -12;
export default function ProcurementTable() {
  const { currency } = useMarketStore();
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  const isVanStockExpanded = useProcurementStore((s) => s.isVanStockExpanded);
  const installationKit = useProcurementStore((s) => s.installationKit);

  const headerTypographyGetter = (options: {
    text: MessageDescriptor['id'];
    isCentered?: boolean;
    messageValues?: Record<string, string>;
  }) => (
    <Typography
      fontWeight="500"
      variant="body2"
      sx={{ width: '100%', display: 'flex', justifyContent: options.isCentered ? 'center' : 'flex-start' }}
    >
      <FormattedMessage id={options.text} values={options.messageValues ?? {}} />
    </Typography>
  );
  const columns: ColumnDef<EquivalenceGroup>[] = [
    {
      header: headerTypographyGetter.bind(null, { text: 'heatDesign.procurement.bom' }),
      accessorKey: '',
      enableSorting: false,
      id: 'bom',
    },
    {
      header: () => <ProcurementTableVanStockHeader />,
      accessorKey: '',
      enableSorting: false,
      id: 'vanstock',
    },
    {
      header: headerTypographyGetter.bind(null, { text: 'heatDesign.procurement.aik' }),
      accessorKey: '',
      enableSorting: false,
      id: 'aik',
    },
    {
      header: () => {
        return (
          <LabelTooltip
            gap={0.5}
            label={
              <Typography variant="inputLabel">
                <FormattedMessage id="heatDesign.procurement.quantity" />
              </Typography>
            }
            tooltipLabel={
              <Typography color="white" variant="body2">
                <FormattedMessage id="heatDesign.procurement.quantity.tooltip" />
              </Typography>
            }
          />
        );
      },
      accessorKey: '',
      enableSorting: false,
      id: 'quantity',
    },
    {
      header: headerTypographyGetter.bind(null, { text: 'heatDesign.procurement.itemName' }),
      accessorKey: '',
      enableSorting: false,
      id: 'name',
    },
    {
      header: headerTypographyGetter.bind(null, {
        text: 'heatDesign.billOfMaterials.summary.cost',
        messageValues: {
          symbol: currencySymbols[currency] ?? '',
        },
      }),
      accessorKey: '',
      enableSorting: false,
      id: 'cost',
    },
    {
      header: headerTypographyGetter.bind(null, { text: 'heatDesign.common.erpId' }),
      accessorKey: 'selectedItem.erpItem.erpId',
      enableSorting: true,
      sortingFn: (a, b) => {
        const itemA = a.original.items[a.original.selectedItemId];
        const itemB = b.original.items[b.original.selectedItemId];
        const erpIdA = itemA && isProcurementItemFromErp(itemA) ? (itemA?.erpItem?.erpId ?? '') : '';
        const erpIdB = itemB && isProcurementItemFromErp(itemB) ? (itemB?.erpItem?.erpId ?? '') : '';
        return erpIdA.localeCompare(erpIdB);
      },
      id: 'selectedItem.erpItem.erpId',
    },
    {
      header: headerTypographyGetter.bind(null, { text: 'common.label.label' }),
      accessorKey: 'selectedItem.erpItem.label',
      enableSorting: true,
      sortingFn: (a, b) => {
        const itemA = a.original.items[a.original.selectedItemId];
        const itemB = b.original.items[b.original.selectedItemId];
        const labelA = itemA && isProcurementItemFromErp(itemA) ? (itemA?.erpItem?.label?.label ?? '') : '';
        const labelB = itemB && isProcurementItemFromErp(itemB) ? (itemB?.erpItem?.label?.label ?? '') : '';
        if (!labelA) {
          return -1;
        }
        if (!labelB) {
          return 1;
        }
        return labelB.localeCompare(labelA);
      },
      id: 'label',
    },
    {
      id: 'expand',
      header: '',
      accessorKey: '',
      enableSorting: false,
    },
  ];

  const selectedLabelIds = useProcurementStore((s) => s.selectedLabelIds);
  const queriedEquivalenceGroupIds = useProcurementStore((s) => s.equivalenceGroupQueryIds);
  const areOnlyOrderedItemsShown = useProcurementStore((s) => s.areOnlyOrderedItemsShown);

  const filteredEquivalenceGroups = useMemo(() => {
    let equivalenceGroupsToFilter = Object.values(equivalenceGroups);

    if (queriedEquivalenceGroupIds !== undefined) {
      equivalenceGroupsToFilter =
        queriedEquivalenceGroupIds == null
          ? []
          : queriedEquivalenceGroupIds.map((id) => equivalenceGroups[id]).filter(isNotNullish);
    }

    if (areOnlyOrderedItemsShown) {
      equivalenceGroupsToFilter = equivalenceGroupsToFilter.filter(
        (equivalenceGroup) => equivalenceGroup.procuredQuantity > 0,
      );
    }

    return equivalenceGroupsToFilter.filter((equivalenceGroup) => {
      return (
        Object.values(equivalenceGroup.items).filter((item) => {
          const type = item.type;
          switch (type) {
            case 'erp':
            case 'extra':
              return selectedLabelIds.size
                ? !!item.erpItem?.label?.id?.value && selectedLabelIds.has(item.erpItem.label.id.value)
                : true;
            case 'custom':
              // Custom items don't (currently) support labels, so make sure they are shown regardless
              return true;
            default:
              return type satisfies never;
          }
        }).length > 0
      );
    });
  }, [equivalenceGroups, selectedLabelIds, queriedEquivalenceGroupIds, areOnlyOrderedItemsShown]);

  return (
    <Stack
      gap={2}
      sx={{ backgroundColor: '#fff', borderRadius: '16px', padding: '16px' }}
      data-testid="procurement-table-container"
    >
      <div data-testid="procurement-table-filter">
        <ProcurementTableFilter />
      </div>
      <Stack sx={{ minHeight: '500px', width: '100%', position: 'relative' }}>
        <TanstackTable
          data-testid="procurement-table"
          styles={{
            columnWidths: COLUMN_WIDTHS,
            bodyColumnWidths: '1fr',
            tableContainer: () => ({
              padding: '0 8px',
              backgroundColor: '#fff',
            }),
            tableBodyRow: () => ({
              paddingBottom: '8px',
            }),
            tableHead: () => ({
              backgroundColor: '#fff',
              zIndex: 10,
              height: '25px',
            }),
            tableHeadRow: () => ({
              backgroundColor: '#fff',
              height: '33px',
            }),
            tableHeadCell: (header) => ({
              padding: header.id === 'vanstock' ? '0 8px' : 0,
              height: '25px',
              transition: 'width 0.2s',
              marginLeft: NON_TRANSPOSED_HEADER_IDS.includes(header.id) ? '0' : `${TRANSPOSE_WIDTH}px`,
              width:
                header.id === 'vanstock'
                  ? isVanStockExpanded
                    ? `${VAN_STOCK_HEADER_MAX_WIDTH}px`
                    : `${VAN_STOCK_HEADER_MIN_WIDTH}px`
                  : 'auto',
              border: 'none',
              '.MuiStack-root': {
                padding: 0,
                height: '100%',
              },
            }),
          }}
          customRowRenderer={(item) => (
            <EquivalenceGroupCard key={item.id} equivalenceGroup={item} disableQuantityField={!installationKit} />
          )}
          data={filteredEquivalenceGroups}
          columns={columns}
        />
      </Stack>
    </Stack>
  );
}
