import Fuse, { IFuseOptions } from 'fuse.js';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useProcurementStore } from './store/ProcurementContext';
import { EquivalenceGroups, ProcurementItem } from './types';

const QUERY_FIELDS = [
  // These fields are for ERP items, align with those from useItemCatalogueSearch
  'erpItem.category',
  'erpItem.supplier',
  'erpItem.supplierId',
  'erpItem.description',
  'erpItem.label.label',
  // These fields are for custom items
  'name',
];
const ID_FIELDS = ['erpItem.erpId'];
const fuseOptions: IFuseOptions<SearchableEquivalenceGroupItems> = {
  includeScore: true,
  keys: [...QUERY_FIELDS, ...ID_FIELDS],
  ignoreDiacritics: true,
  shouldSort: true,
  threshold: 0.3, // Adjust this number to make the fuzziness more or less sensitive
  useExtendedSearch: true,
};

type SearchableEquivalenceGroupItems = ProcurementItem & {
  equivalenceGroupId: string;
};

function mapToSearchableData(equivalenceGroups: EquivalenceGroups): SearchableEquivalenceGroupItems[] {
  return Object.values(equivalenceGroups).flatMap((equivalenceGroup) => {
    return Object.values(equivalenceGroup.items).map((item) => {
      return {
        ...item,
        equivalenceGroupId: equivalenceGroup.id,
      };
    });
  });
}

export function useProcurementItemSearch(equivalenceGroups: EquivalenceGroups) {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [searchIndex, setSearchIndex] = useState(new Fuse(mapToSearchableData(equivalenceGroups), fuseOptions));
  const debouncedSetQuery = useMemo(() => debounce(setDebouncedQuery, 300), [setDebouncedQuery]);
  const setEquivalenceGroupQueryIds = useProcurementStore((s) => s.setEquivalenceGroupQueryIds);

  useEffect(() => {
    // Mainly needed because on initial load the equivalence groups are not ready, so we build the Fuse
    // search with an empty index. But we should also really rebuild the index anyway if the items change.
    setSearchIndex(new Fuse(mapToSearchableData(equivalenceGroups), fuseOptions));
  }, [equivalenceGroups]);

  useEffect(() => {
    debouncedSetQuery(query);
  }, [debouncedSetQuery, query]);
  useEffect(() => {
    if (!debouncedQuery?.trim()) {
      setEquivalenceGroupQueryIds(undefined);
    } else {
      // We either want to hit a fuzzy-search on any of the query fields, OR search ID_FIELDS
      // by them *containing* the query (the single quote ' symbol is used for this)
      const searchQuery = {
        $or: [
          ...QUERY_FIELDS.map((field) => {
            return {
              [field]: debouncedQuery,
            };
          }),
          ...ID_FIELDS.map((field) => {
            return {
              [field]: `'${debouncedQuery}`,
            };
          }),
        ],
      };
      const searchResults = searchIndex.search(searchQuery);

      if (!searchResults.length) {
        setEquivalenceGroupQueryIds(null);
      } else {
        // A side-effect of mapToSearchableData() is that we end up with duplicates by equivalenceGroupId,
        // which we fix here, using an approach that retains the ordering of the results.
        const seen = new Set();
        setEquivalenceGroupQueryIds(
          searchResults
            .filter((result) => {
              const equivalenceGroupId = result.item.equivalenceGroupId;
              if (seen.has(equivalenceGroupId)) {
                return false;
              } else {
                seen.add(equivalenceGroupId);
                return true;
              }
            })
            .map((result) => result.item.equivalenceGroupId),
        );
      }
    }
  }, [debouncedQuery, setEquivalenceGroupQueryIds, equivalenceGroups, searchIndex]);

  return {
    setQuery,
    query,
  };
}
