import { screen, waitFor, within } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { describe, expect, it } from 'vitest';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { ProcurementComment } from './ProcurementComment';
import { ProcurementDataManagerInitialState } from './ProcurementDataManager';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';

setupProcurementTests();

describe('ProcurementComment', () => {
  const renderProcurementComment = async (initialState?: ProcurementDataManagerInitialState) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper initialState={initialState}>
          <ProcurementComment />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('procurement-comment-container')).toBeInTheDocument();
    });
  };

  it('should show as invalid when there is no content', async () => {
    await renderProcurementComment({ comment: undefined });
    expect(within(screen.getByTestId('procurement-comment-container')).getByRole('textbox')).toHaveAttribute(
      'aria-invalid',
      'true',
    );
  });

  it('should show as valid when there is content', async () => {
    await renderProcurementComment({ comment: 'Some comment' });
    expect(within(screen.getByTestId('procurement-comment-container')).getByRole('textbox')).toHaveAttribute(
      'aria-invalid',
      'false',
    );
  });
});
