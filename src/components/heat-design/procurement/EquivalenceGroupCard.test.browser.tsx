import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { EquivalenceGroupCard } from './EquivalenceGroupCard';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';
import { useProcurementStore } from './store/ProcurementContext';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import {
  expandEquivalenceGroupCard,
  getCardDetails,
  getEquivalenceGroupCards,
  getProcuredQuantity,
  getProcurementItemCards,
  getSummedAikQuantity,
  getSummedBomQuantity,
  getSummedVanStockQuantity,
} from './test-utils/test-utils';
import { PROCUREMENT_ITEM_TYPES, ProcurementItemType } from './types';
import { ProcurementDataManagerInitialState } from './ProcurementDataManager';
import { AIK_BUNDLE_ID, VAN_STOCK_1_BUNDLE_ID } from '../../bill-of-materials/test-utils';

setupProcurementTests();

// Mock dependency to child item cards
vi.mock('./ProcurementItemCard', () => ({
  ProcurementItemCard: mockedProcurementItemCard,
}));

function mockedProcurementItemCard() {
  return <div data-testid="procurement-item-container" />;
}

function EquivalenceGroupCardWrapper({ selectedItemType }: { selectedItemType?: ProcurementItemType }) {
  const equivalenceGroups = useProcurementStore((s) => s.equivalenceGroups);
  const group = selectedItemType
    ? Object.values(equivalenceGroups).find((group) => {
        return Object.values(group.items).some((item) => item.type === selectedItemType);
      })
    : Object.values(equivalenceGroups)[0];
  if (!group) {
    return <h1>No equivalence groups found</h1>;
  }
  return <EquivalenceGroupCard equivalenceGroup={group} />;
}

describe('EquivalenceGroupCard', () => {
  const renderEquivalenceGroupCard = async (options?: {
    selectedItemType?: ProcurementItemType;
    initialState?: ProcurementDataManagerInitialState;
  }) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper initialState={options?.initialState}>
          <EquivalenceGroupCardWrapper {...(options ?? {})} />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(
      () => {
        const cards = screen.getAllByTestId(/equivalence-group-card/);
        expect(cards.length).toBeGreaterThan(0);
      },
      { timeout: 5000 },
    );
  };

  it('should render the equivalence group card component', async () => {
    await renderEquivalenceGroupCard();
    const cards = getEquivalenceGroupCards();
    expect(cards.length).toBeGreaterThan(0);
    expect(cards[0]).toBeInTheDocument();
  });

  it('should be possible to expand a group with ERP item', async () => {
    await renderEquivalenceGroupCard({
      selectedItemType: PROCUREMENT_ITEM_TYPES.ERP,
    });
    const cards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.ERP);
    const card = cards[0];
    expect(card).toBeInTheDocument();
    await expandEquivalenceGroupCard(card!);
  });

  it('should show procurement cards inside when expanded', async () => {
    await renderEquivalenceGroupCard({
      selectedItemType: PROCUREMENT_ITEM_TYPES.ERP,
    });
    const cards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.ERP);
    const card = cards[0];
    expect(card).toBeInTheDocument();
    await expandEquivalenceGroupCard(card!);
    await waitFor(async () => {
      const procurementItemCards = await getProcurementItemCards(card!);
      expect(procurementItemCards.length).toEqual(2);
    });
    const procurementItemCards = await getProcurementItemCards(card!);
    expect(procurementItemCards.length).toEqual(2);
  });

  it('should not be possible to expand a group with a custom item', async () => {
    await renderEquivalenceGroupCard({
      selectedItemType: PROCUREMENT_ITEM_TYPES.CUSTOM,
    });
    const cards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.CUSTOM);
    const card = cards[0];
    expect(card).toBeInTheDocument();
    await expandEquivalenceGroupCard(card!);
    const details = await getCardDetails(card!);
    expect(details).not.toBeInTheDocument();
  });

  it('should show correct quantities of items on initial load', async () => {
    await renderEquivalenceGroupCard({
      selectedItemType: PROCUREMENT_ITEM_TYPES.ERP,
    });
    const cards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.ERP);
    const card = cards[0];
    expect(card).toBeInTheDocument();
    const bomQuantity = await getSummedBomQuantity(card!);
    const vanStockQuantity = await getSummedVanStockQuantity(card!);
    const aikQuantity = await getSummedAikQuantity(card!);
    const procuredQuantity = await getProcuredQuantity(card!);

    expect(bomQuantity).toEqual('11');
    expect(vanStockQuantity).toEqual('0');
    expect(aikQuantity).toEqual('0');
    expect(procuredQuantity).toEqual('11');
  });

  it('should show correct quantities of items with AIK and Van Stock selected', async () => {
    await renderEquivalenceGroupCard({
      selectedItemType: PROCUREMENT_ITEM_TYPES.ERP,
      initialState: {
        installationKit: AIK_BUNDLE_ID,
        vanStockBundleIds: [VAN_STOCK_1_BUNDLE_ID],
      },
    });
    const cards = getEquivalenceGroupCards(PROCUREMENT_ITEM_TYPES.ERP);
    const card = cards[0];
    expect(card).toBeInTheDocument();
    const bomQuantity = await getSummedBomQuantity(card!);
    const vanStockQuantity = await getSummedVanStockQuantity(card!);
    const aikQuantity = await getSummedAikQuantity(card!);
    const procuredQuantity = await getProcuredQuantity(card!);
    expect(bomQuantity).toEqual('11');
    expect(vanStockQuantity).toEqual('1');
    expect(aikQuantity).toEqual('0');
    expect(procuredQuantity).toEqual('10');
  });
});
