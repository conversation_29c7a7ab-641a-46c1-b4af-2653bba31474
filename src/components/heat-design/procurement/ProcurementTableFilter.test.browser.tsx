import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { ProcurementTableFilter } from './ProcurementTableFilter';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';

setupProcurementTests();

describe('ProcurementTableFilter', () => {
  const renderProcurementTableFilter = async () => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper>
          <ProcurementTableFilter />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('procurement-table-filter-container')).toBeInTheDocument();
    });
  };

  it('should render the procurement table filter component', async () => {
    await renderProcurementTableFilter();
  });

  it('should render search input field', async () => {
    await renderProcurementTableFilter();
    expect(screen.getByTestId('procurement-search-input')).toBeInTheDocument();
  });

  it('should render label filter', async () => {
    await renderProcurementTableFilter();
    expect(screen.getByTestId('item-relations-label-filter')).toBeInTheDocument();
  });

  it('should render show only ordered items switch', async () => {
    await renderProcurementTableFilter();
    expect(screen.getByTestId('procurement-show-only-ordered-switch')).toBeInTheDocument();
  });

  it('should render browse items button', async () => {
    await renderProcurementTableFilter();
    expect(screen.getByTestId('browse-items-button')).toBeInTheDocument();
  });
});
