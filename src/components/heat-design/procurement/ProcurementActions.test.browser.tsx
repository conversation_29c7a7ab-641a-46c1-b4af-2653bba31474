import { screen, waitFor } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { IntlProvider } from 'react-intl';
import { ProcurementActions } from './ProcurementActions';
import { renderWithProviders } from '../../../tests/utils/testUtils';
import { ProcurementTestWrapper, setupProcurementTests } from './test-utils/setupProcurementTests';

setupProcurementTests();

describe('ProcurementActions', () => {
  const renderProcurementActions = async () => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <ProcurementTestWrapper>
          <ProcurementActions />
        </ProcurementTestWrapper>
      </IntlProvider>,
    );
    await waitFor(() => {
      expect(screen.getByTestId('procurement-actions-container')).toBeInTheDocument();
    });
  };

  it('should render save button', async () => {
    await renderProcurementActions();

    expect(screen.getByTestId('procurement-save-button')).toBeInTheDocument();
  });

  it('should render save and order button', async () => {
    await renderProcurementActions();
    expect(screen.getByTestId('procurement-save-and-order-button')).toBeInTheDocument();
  });
});
