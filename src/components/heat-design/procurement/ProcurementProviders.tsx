import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { heatDesignTheme } from '../HeatDesignTheme';
import { ProcurementProvider } from './store/ProcurementContext';
import { ProcurementDataManager, ProcurementDataManagerInitialState } from './ProcurementDataManager';
import { ReactNode } from 'react';

export function ProcurementProviders({
  initialState,
  children,
}: {
  initialState?: ProcurementDataManagerInitialState;
  children: ReactNode;
}) {
  return (
    <AiraThemeProvider theme={heatDesignTheme}>
      <ProcurementProvider>
        <ProcurementDataManager initialState={initialState}>{children}</ProcurementDataManager>
      </ProcurementProvider>
    </AiraThemeProvider>
  );
}
