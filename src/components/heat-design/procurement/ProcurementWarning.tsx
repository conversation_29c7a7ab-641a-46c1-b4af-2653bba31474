import { memo } from 'react';
import { WarningIcon } from '../components/WarningIcon';
import { useIntl } from 'react-intl';
import { Stack, Tooltip, Typography } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { EquivalenceGroup, PROCUREMENT_WARNING } from './types';
import { useProcurementStore } from './store/ProcurementContext';

export type ProcurementWarningProps = {
  equivalenceGroup: EquivalenceGroup;
};

const WARNING_TOOLTIPS = {
  [PROCUREMENT_WARNING.AIK]: 'heatDesign.procurement.aikItemError',
  [PROCUREMENT_WARNING.DUPLICATE]: 'heatDesign.procurement.duplicateItemError',
  [PROCUREMENT_WARNING.CUSTOM]: 'heatDesign.procurement.customItemError',
  [PROCUREMENT_WARNING.REMOVED_FROM_ERP]: 'heatDesign.procurement.removedFromErpError',
} as const;

function ProcurementWarningComponent({ equivalenceGroup }: ProcurementWarningProps) {
  const warning = useProcurementStore((s) => s.equivalenceGroupWarnings[equivalenceGroup.id]);
  const { formatMessage } = useIntl();
  if (!warning) {
    return null;
  }
  return (
    <Stack sx={{ mx: 2 }} direction="row" alignItems="center" data-testid={`procurement-warning-${warning}`}>
      <Tooltip
        componentsProps={{
          tooltip: {
            sx: {
              backgroundColor: beige[100],
              padding: '16px',
              borderRadius: '16px',
              boxShadow: '0 25px 36px 0 #00000040',
            },
          },
        }}
        title={
          <Stack gap={2}>
            <Typography variant="body1" fontSize="14px">
              {formatMessage({ id: WARNING_TOOLTIPS[warning] ?? '' })}
            </Typography>
          </Stack>
        }
        placement="top"
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
        </div>
      </Tooltip>
    </Stack>
  );
}

export const ProcurementWarning = memo(ProcurementWarningComponent);
