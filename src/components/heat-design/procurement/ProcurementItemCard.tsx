import { memo, useState } from 'react';
import {
  QUANTITY_COLUMNS_WIDTHS,
  TRANSPOSE_WIDTH,
  VAN_STOCK_HEADER_MAX_WIDTH,
  VAN_STOCK_HEADER_MIN_WIDTH,
} from './ProcurementTable';
import { Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography } from '@mui/material';
import { getFormattedItemPricePerUnit } from '../../bill-of-materials/utils';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { useIntl } from 'react-intl';
import { useRouter } from 'next/router';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import styled from '@emotion/styled';
import { surface } from '@ui/theme/colors';
import { MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT } from './EquivalenceGroupCard';
import { useProcurementStore } from './store/ProcurementContext';
import { isProcurementItemFromErp } from './type-guards/isProcurementItemFromErp';
import { isExtraProcurementItem } from './type-guards/isExtraProcurementItem';
import { ProcurementItem } from './types';
import {
  getProcurementItemDescription,
  getProcurementItemErpId,
  getProcurementItemLabel,
  getProcurementItemMinorPrice,
} from './utils';

const StyledAccordion = styled(Accordion)({
  transition: 'background-color 0.2s, box-shadow 0.2s',
  padding: 0,
  borderRadius: '8px !important',
  boxShadow: 'none',
  backgroundColor: surface[100],
  '.details-controls': {
    opacity: 0,
    transition: 'opacity 0.2s',
  },
  '&.Mui-expanded': {
    backgroundColor: surface[100],
    boxShadow: '0 2px 4px 0 #00000020',
    '.details-controls': {
      opacity: 1,
    },
  },
  '&:before': {
    display: 'none',
  },
});

export type EquivalentItemCardProps = {
  item: ProcurementItem;
  isSelected: boolean;
  onSelect: (item: ProcurementItem) => void;
};

function EquivalentItemCardComponent({ item, isSelected, onSelect }: EquivalentItemCardProps) {
  const { locale } = useRouter();
  const { formatMessage } = useIntl();
  const [isExpanded, setIsExpanded] = useState(false);
  const isVanStockExpanded = useProcurementStore((s) => s.isVanStockExpanded);
  const quantity = isProcurementItemFromErp(item) ? item.erpItem?.quantity : 1;
  const description = getProcurementItemDescription(item);
  const minorPrice = getProcurementItemMinorPrice(item);
  const itemCostPerUnit = minorPrice ? getFormattedItemPricePerUnit({ minorPrice, quantity: quantity }, locale) : '';
  const erpId = getProcurementItemErpId(item);
  const version = isProcurementItemFromErp(item) ? item.erpItem?.version : undefined;
  const label = getProcurementItemLabel(item);
  const isExtraItem = isExtraProcurementItem(item);

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: `${QUANTITY_COLUMNS_WIDTHS} 68px 1fr`,
        paddingRight: '16px',
      }}
      data-testid="procurement-item-container"
    >
      <Typography
        sx={{
          display: 'flex',
          height: MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT,
          alignItems: 'center',
          justifyContent: 'center',
        }}
        variant="body2"
        data-testid="procurement-item-bom-quantity"
      >
        {!isExtraItem && item.bomQuantity}
      </Typography>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent={isVanStockExpanded ? 'flex-start' : 'center'}
        sx={{
          marginLeft: `${TRANSPOSE_WIDTH}px`,
          height: MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT,
          transition: 'width 0.2s',
          width: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH}px` : `${VAN_STOCK_HEADER_MIN_WIDTH}px`,
        }}
      >
        {isVanStockExpanded ? (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-around"
            sx={{
              width: `${VAN_STOCK_HEADER_MAX_WIDTH - 30}px`,
            }}
          >
            <Typography
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              variant="body2"
            >
              {!isExtraItem && item.vanStockQuantity}
            </Typography>
            <Typography
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              variant="body2"
            >
              {!isExtraItem && item.vanStockQuantity}
            </Typography>
          </Stack>
        ) : (
          <Typography
            data-testid="procurement-item-van-stock-quantity"
            sx={{
              height: MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'width 0.2s',
              width: isVanStockExpanded ? `${VAN_STOCK_HEADER_MAX_WIDTH}px` : `${VAN_STOCK_HEADER_MIN_WIDTH}px`,
            }}
            variant="body2"
          >
            {!isExtraItem && item.vanStockQuantity}
          </Typography>
        )}
      </Stack>

      <Typography
        sx={{
          marginLeft: '-36px',
          height: MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        variant="body2"
        data-testid="procurement-item-aik-quantity"
      >
        {!isExtraItem && item.aikQuantity}
      </Typography>
      <Box
        sx={{
          display: 'flex',
          zIndex: 1,
          height: MINIMIZED_EQUIVALENCE_ITEM_ROW_HEIGHT,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          data-testid="select-item-container"
          onClick={() => onSelect(item)}
          className="check-wrapper"
          sx={{
            borderRadius: '50%',
            width: '20px',
            height: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            cursor: 'pointer',
            border: `1px solid ${surface[200]}`,
          }}
        >
          <Box
            sx={{
              width: '20px',
              height: '20px',
              display: 'flex',
              alignItems: 'center',
              opacity: isSelected ? 1 : 0,
              transition: 'opacity 0.2s',
              '&:hover': {
                opacity: isSelected ? 1 : 0.5,
              },
            }}
          >
            <CheckCircleFilledIcon
              style={{
                position: 'absolute',
                top: '-2px',
                left: '-2px',
                width: '24px',
                height: '24px',
              }}
            />
          </Box>
        </Box>
      </Box>
      <StyledAccordion
        className={`${isSelected ? 'selected' : ''}`}
        expanded={isExpanded}
        data-testid={'procurement-item-card-' + item.itemId}
      >
        <AccordionSummary
          style={{ padding: '8px', cursor: 'pointer', display: 'flex' }}
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded((prev) => !prev);
          }}
        >
          <Box
            width="100%"
            style={{
              alignItems: 'center',
              minHeight: '25px',
              display: 'grid',
              borderRadius: '8px',
              gridTemplateColumns: '1fr 100px 100px 100px 76px',
            }}
          >
            <Box style={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2">{description}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', marginLeft: `${TRANSPOSE_WIDTH}px` }}>
              <Typography variant="body2">{itemCostPerUnit}</Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', marginLeft: `${TRANSPOSE_WIDTH}px` }}>
              <Typography variant="body2">
                {erpId} {version}
              </Typography>
            </Box>
            <Box style={{ display: 'flex', alignItems: 'center', marginLeft: `${TRANSPOSE_WIDTH}px` }}>
              <Typography variant="body2">{label}</Typography>
            </Box>
            <Stack
              className="details-controls"
              gap="8px"
              sx={{ cursor: 'pointer', paddingRight: '8px' }}
              alignItems="center"
              direction="row"
              data-testid="expand-details-button"
              justifyContent="flex-end"
            >
              <Chevron transitionDuration="0.2s" direction={isExpanded ? 'up' : 'down'} height={20} width={20} />
            </Stack>
          </Box>
        </AccordionSummary>

        {isProcurementItemFromErp(item) && (
          <AccordionDetails sx={{ padding: '8px' }}>
            <Box>
              <Stack direction="row" gap="16px" flexWrap="wrap">
                {item.erpItem?.category && (
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.category' })}
                    value={item.erpItem.category}
                  />
                )}
                {item.erpItem?.supplier && (
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.supplier' })}
                    value={item.erpItem.supplier}
                  />
                )}
                {!!item.erpItem?.quantity && (
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'heatDesign.billOfMaterials.summary.itemsPerUnit' })}
                    value={item.erpItem.quantity}
                  />
                )}
                {item.erpItem?.unit && (
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.unit' })}
                    value={item.erpItem.unit}
                  />
                )}
                {!!item.erpItem?.minorPrice && (
                  <LabelValueDisplay
                    sx={{ width: 'auto', gap: 3 }}
                    label={formatMessage({ id: 'billOfMaterials.itemCatalogue.table.costPerUnit' })}
                    value={getFormattedItemPricePerUnit(item.erpItem, locale)}
                  />
                )}
              </Stack>
            </Box>
          </AccordionDetails>
        )}
      </StyledAccordion>
    </Box>
  );
}

export const ProcurementItemCard = memo(EquivalentItemCardComponent);
