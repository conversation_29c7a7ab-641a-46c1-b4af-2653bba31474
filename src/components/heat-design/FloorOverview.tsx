import { useEffect } from 'react';
import { Button, getButtonStartIcon } from '@ui/components/Button/Button';
import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { HEAT_DESIGN_FLOOR_PLAN_DIMENSION } from '@ui/theme/constants';
import { useGetRoomsByFloor } from './stores/RoomsStore';
import { FloorProps, Room, ROOM_FABRIC_TYPES } from './stores/types';
import { useDefaultFloor, useFloors } from './stores/FloorsStore';
import { arrayOfTypesIsValid, floorIsValid, typeIsValid } from './Validator';
import { generateWarningIconForRoom } from './components/WarningIcon';
import {
  HeatDesignModal,
  useHeatDesignModal,
  useHeatDesignUI,
  useHeatDesignUIActions,
} from './stores/HeatDesignUIStore';
import {
  aRoomHasUValueForFabricType,
  filterEnabledSurfacesInRoom,
  filterEnabledSurfacesInRooms,
  getProjectFabricTypeForSurface,
  lookupUValueForSurfaceType,
} from './utils/helpers';
import FloorDefaultValuesModal from './modals/FloorDefaultValuesModal';
import { useProjectUValues } from './stores/UValuesStore';
import HeatDesignCard from './HeatDesignCard';
import InteractableFloorPlan from './components/InteractableFloorPlan';
import { FloorNavigation } from './FloorNavigation';

export interface RoomsByFloor {
  [key: string]: Room[];
}

const floorUValuesAreValid = ({
  sFloor,
  roomsByFloor,
  projectUValues,
  floors,
}: {
  sFloor: FloorProps;
  roomsByFloor: { [key: Room['floorId']]: Room[] };
  projectUValues: any;
  floors: FloorProps[];
}) => {
  if (!floorIsValid(sFloor)) return false;

  return (
    ROOM_FABRIC_TYPES.filter((fabricType) => aRoomHasUValueForFabricType(fabricType, roomsByFloor[sFloor.uid]!))
      .filter((fabricType) => {
        const projectFabricType = getProjectFabricTypeForSurface(fabricType, sFloor, floors);
        return fabricType !== 'roofsOrCeilings' && !['foundation', 'roof'].includes(projectFabricType);
      })
      .filter((fabricType) => lookupUValueForSurfaceType(fabricType, projectUValues, sFloor, floors) === undefined)
      .length === 0
  );
};

export default function FloorOverview() {
  const floors = useFloors();
  const defaultFloor = useDefaultFloor();
  const projectUValues = useProjectUValues();
  const heatDesignUIActions = useHeatDesignUIActions();
  const { selectFloor } = heatDesignUIActions;
  const floorModalIsOpen = useHeatDesignModal() === HeatDesignModal.FloorDefaults;
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const selectedFloor = floors.find((floor) => floor.uid === selectedFloorId);

  useEffect(() => {
    if (defaultFloor) {
      selectFloor(defaultFloor);
    }
  }, [selectFloor, defaultFloor]);

  const roomsByFloor = useGetRoomsByFloor();

  if (!selectedFloor) return null;
  const floorUValueValid = (sFloor: FloorProps) =>
    floorUValuesAreValid({ sFloor, roomsByFloor, projectUValues, floors });

  const showFloorDefaultsModal = () => {
    sendGTMEvent({
      event: 'button_click',
      click_text: 'floor default values',
    });
    return heatDesignUIActions.showFloorDefaultsModal(selectedFloor.uid);
  };

  const floorValidator = (floor: FloorProps) => {
    const validFloor = arrayOfTypesIsValid('room', filterEnabledSurfacesInRooms(roomsByFloor[floor.uid] || []));
    return validFloor && floorUValueValid(floor);
  };

  return (
    <HeatDesignCard>
      <Stack direction="row" spacing={2} justifyContent="space-between" flexWrap="wrap">
        <FloorNavigation
          selectedFloor={selectedFloor}
          selectFloor={heatDesignUIActions.selectFloor}
          displayFloorArea
          validator={floorValidator}
        />
        <Typography>
          <FormattedMessage id="heatDesign.helper.floorPlan" />
        </Typography>
        <Button
          variant={floorUValueValid(selectedFloor) ? 'contained' : 'invalid'}
          startIcon={getButtonStartIcon(floorUValueValid(selectedFloor))}
          size="small"
          onClick={showFloorDefaultsModal}
        >
          <FormattedMessage id="heatDesign.title.floorLevelDefaultValues" />
        </Button>
      </Stack>
      {roomsByFloor[selectedFloor.uid] === undefined && (
        <Typography mt={3}>
          <FormattedMessage id="heatDesign.error.noRoomsOnFloor" />
        </Typography>
      )}
      {floors.map(
        (floor) =>
          floor.uid === selectedFloor.uid &&
          roomsByFloor[floor.uid] !== undefined && (
            <InteractableFloorPlan floor={floor} key={floor.uid}>
              <Stack
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                }}
              >
                <svg width={HEAT_DESIGN_FLOOR_PLAN_DIMENSION} height={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}>
                  {(roomsByFloor[floor.uid] || []).flatMap((room) => {
                    if (!typeIsValid('room', filterEnabledSurfacesInRoom(room))) {
                      return generateWarningIconForRoom(room);
                    }

                    return null;
                  })}
                  {(roomsByFloor[floor.uid] || []).map((room) => {
                    const coords = room?.imageMap?.coordinates.toString();
                    return (
                      <polygon
                        key={room.id + room.floor}
                        points={coords}
                        className={room.isHeated ? 'overview-room' : 'overview-room-unheated'}
                        onClick={(e) => {
                          if (!e.defaultPrevented) {
                            e.stopPropagation();
                            e.preventDefault();
                            heatDesignUIActions.showRoomModal(room);
                          }
                        }}
                        data-testid={`heat-design-room-${room.name.replace(' ', '_')}-area`}
                      />
                    );
                  })}
                </svg>
              </Stack>
            </InteractableFloorPlan>
          ),
      )}
      <FloorDefaultValuesModal
        isOpen={floorModalIsOpen}
        onClose={heatDesignUIActions.closeModal}
        floorUID={selectedFloor.uid}
      />
    </HeatDesignCard>
  );
}
