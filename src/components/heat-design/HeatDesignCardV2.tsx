import { Stack, SxProps, Theme } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { beige } from '@ui/theme/colors';
import { ReactNode } from 'react';

type Props = {
  children: ReactNode;
  title: string;
  titleComponent?: ReactNode;
  icon: ReactNode;
  sx?: SxProps<Theme>;
};

export default function HeatDesignCardV2({ children, title, titleComponent, icon, sx }: Props) {
  return (
    <Stack gap={1}>
      <Stack direction="row" gap={1} justifyContent="space-between" alignItems="center">
        <Stack direction="row" gap={1} alignItems="center">
          {icon}
          <Heading variant="headline4" level={4}>
            {title}
          </Heading>
        </Stack>
        {titleComponent}
      </Stack>
      <Stack
        gap={1}
        sx={{
          backgroundColor: beige[100],
          borderRadius: 2,
          padding: 2,
          overflowWrap: 'break-word',
          ...sx,
        }}
      >
        {children}
      </Stack>
    </Stack>
  );
}
