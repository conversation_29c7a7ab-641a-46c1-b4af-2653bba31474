import { useState } from 'react';
import { Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { EnvelopeOutlinedIcon } from '@ui/components/StandardIcons/EnvelopeOutlinedIcon';
import { FormattedMessage, useIntl } from 'react-intl';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useInvalidateSolution } from 'components/quotation/hooks/useSaveSolution';
import { api } from 'utils/api';
import { toast } from 'react-hot-toast';
import ConfirmationPopover from './ConfirmationPopover';

export default function SendEmailButton() {
  const intl = useIntl();
  const [anchorElement, setAnchorElement] = useState<HTMLButtonElement | null>(null);
  const { mutateAsync: sendDesignReviewEmailMutation } = api.HeatLossCalculator.sendDesignForReview.useMutation();
  const energySolutionId = useEnergySolutionId();
  const invalidateSolution = useInvalidateSolution();

  if (!energySolutionId) {
    return null;
  }

  const sendDesignReviewEmail = async () => {
    await toast.promise(sendDesignReviewEmailMutation({ energySolutionId }), {
      loading: intl.formatMessage({ id: 'heatDesign.notify.sendingDesignReviewEmail' }),
      success: intl.formatMessage({ id: 'heatDesign.notify.designReviewEmailSent' }),
      error: intl.formatMessage({ id: 'error.support.card.hlc.sendDesignReviewEmail.heading' }),
    });
    invalidateSolution();
  };

  const handleClose = () => {
    setAnchorElement(null);
  };

  const onClick = async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    setAnchorElement(event.currentTarget);
  };

  return (
    <>
      <ConfirmationPopover
        bodyMessageId="heatDesign.customerAcceptance.sendEmailConfirmation"
        onConfirm={sendDesignReviewEmail}
        anchorElement={anchorElement}
        onClose={handleClose}
      />
      <Button size="small" color="white" onClick={onClick}>
        <Stack direction="row" spacing={1} justifyContent="center" alignItems="center">
          <EnvelopeOutlinedIcon />
          <Typography>
            <FormattedMessage id="common.label.sendEmail" defaultMessage="Send email" />
          </Typography>
        </Stack>
      </Button>
    </>
  );
}
