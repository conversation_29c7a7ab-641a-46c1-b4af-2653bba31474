import { Button } from '@mui/material';
import { FormattedMessage } from 'react-intl';

export default function BackButton({
  backDisabled,
  onBackPressed,
}: {
  backDisabled: boolean;
  onBackPressed: () => void;
}) {
  return (
    <Button
      disabled={backDisabled}
      variant="contained"
      onClick={onBackPressed}
      size="small"
      sx={{
        minWidth: 100,
        width: '20vw',
      }}
    >
      <FormattedMessage id="common.link.back" />
    </Button>
  );
}
