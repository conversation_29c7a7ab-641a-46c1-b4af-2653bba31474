import { Box, Button, Stack } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useIsSolutionUpdated } from 'components/quotation/hooks/useIsSolutionUpdated';
import { getButtonStartIcon } from '@ui/components/Button/Button';
import SaveButton from './SaveButton';
import BackButton from './BackButton';
import NextButton from './NextButton';
import { DwellingValidationErrors } from '../modals/ValidationModal';
import { useFloorsStore } from '../stores/FloorsStore';
import { HlcPage, usePageNavigationStore } from '../stores/PageNavigationStore';
import { projectUValuesDefaultsAreValid } from '../utils/helpers';
import { floorHeatSourcesAreValid, getInvalidFloors, getInvalidRooms, propertyIsValid } from '../Validator';
import { useConstructionYear, useHeatDesignHouseInputsStore, useNumOccupants } from '../stores/HouseInputsStore';
import { useGetRoomsByFloor, useRooms } from '../stores/RoomsStore';
import { useProjectUValues } from '../stores/UValuesStore';
import { useHeatDesignUI } from '../stores/HeatDesignUIStore';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import UnlockButton from './UnlockButton';
import ResultSnapshotBanner from './ResultSnapshotBanner';
import HelpButton from './HelpButton';

function scrollToTop() {
  if (typeof window !== 'undefined') {
    window.scrollTo({ top: 0 });
  }
}

export type PageNavigationProps = {
  includeReload: boolean;
  refetchHeatDesign?: () => Promise<any>;
};

export default function PageNavigation({ includeReload, refetchHeatDesign }: PageNavigationProps) {
  const isLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const { groundwork } = useGroundwork();
  const constructionYear = useConstructionYear();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const { id: groundworkId } = groundwork;
  const energySolutionId = useEnergySolutionId();
  const projectUValues = useProjectUValues();
  const numOccupants = useNumOccupants();
  const rooms = useRooms();
  const { checkIsSolutionUpdated } = useIsSolutionUpdated();
  const roomsByFloor = useGetRoomsByFloor();
  const floorsData = useFloorsStore((s) => s.floors);
  const radiatorsOverviewIsValid = floorsData.every((floor) => floorHeatSourcesAreValid(roomsByFloor[floor.uid] || []));
  const floorOverviewIsValid =
    getInvalidRooms(floorsData, rooms, projectUValues).length === 0 && getInvalidFloors(floorsData).length === 0;
  const dwellingValidationErrors: DwellingValidationErrors = {
    constructionYear: !propertyIsValid('dwelling', 'constructionYear', constructionYear),
    numberOfResidents: !propertyIsValid('dwelling', 'numberOfResidents', numOccupants),
    uValues: !projectUValuesDefaultsAreValid(projectUValues, rooms, floorsData),
    acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
    pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
  };
  const dwellingIsValid = Object.values(dwellingValidationErrors).every((isError) => !isError);
  const page = usePageNavigationStore((s) => s.page);
  const pages = usePageNavigationStore((s) => s.pages);
  const isLastPage = usePageNavigationStore((s) => s.actions.isLastPage);
  const isFirstPage = usePageNavigationStore((s) => s.actions.isFirstPage);
  const goToPreviousPage = usePageNavigationStore((s) => s.actions.goToPreviousPage);
  const goToNextPage = usePageNavigationStore((s) => s.actions.goToNextPage);
  const {
    showDwellingValidationModal,
    showRoomValidationModal,
    showRoomEmittersValidationModal,
    showUnsavedChangesModal,
    showHelpModal,
    selectRoom,
  } = useHeatDesignUI((s) => s.actions);

  const handleBack = () => {
    selectRoom(undefined);
    goToPreviousPage();
    scrollToTop();
  };

  const handleNext = () => {
    selectRoom(undefined);
    if (page === HlcPage.PROPERTY_DETAILS) {
      if (!dwellingIsValid) {
        showDwellingValidationModal();
        return; // Do not allow proceeding to rooms overview page unless dwelling defaults are valid
      }
    } else if (page === HlcPage.FLOOR_OVERVIEW) {
      if (!floorOverviewIsValid) {
        showRoomValidationModal();
        return; // do not allow proceeding to calculation page unless all rooms are valid
      }
    } else if (page === HlcPage.RADIATORS_OVERVIEW) {
      if (!radiatorsOverviewIsValid) {
        showRoomEmittersValidationModal();
        // Don't allow the user to proceed with invalid radiators, otherwise these
        // radiators would be shown on the product selection and report pages.
        return;
      }
    } else if (page === HlcPage.PRODUCT_SELECTION) {
      if (checkIsSolutionUpdated()) {
        showUnsavedChangesModal();
        return;
      }
    }
    goToNextPage();
    scrollToTop();
  };

  const valuesAreValidForPage = () => {
    switch (page) {
      case HlcPage.PROPERTY_DETAILS:
        return dwellingIsValid;
      case HlcPage.FLOOR_OVERVIEW:
        return floorOverviewIsValid;
      case HlcPage.RADIATORS_OVERVIEW:
        return radiatorsOverviewIsValid;
      default:
        return true;
    }
  };

  if (!energySolutionId || !groundworkId) {
    return null;
  }

  const getLockStateModifierButton = () => {
    if (isLocked) {
      return <UnlockButton energySolutionId={energySolutionId} installationGroundworkId={groundworkId.value} />;
    }
    return (
      isLastPage() && (
        <SaveButton
          installationGroundworkId={groundworkId.value}
          energySolutionId={energySolutionId}
          lockOnSave
          refetchHeatDesign={refetchHeatDesign}
        />
      )
    );
  };

  return (
    <Stack
      direction="column"
      position="fixed"
      displayPrint="none"
      alignItems="center"
      gap={2}
      sx={{ inset: `auto 0 ${isLocked ? 0 : '20px'}`, pointerEvents: 'none' }}
      // Render this component below the modals
      zIndex={(t) => t.zIndex.modal - 1}
    >
      <Stack
        sx={{ pointerEvents: 'none' }}
        component="nav"
        flexDirection="row"
        gap={3}
        width="100%"
        justifyContent="center"
      >
        <Stack direction="row" sx={{ flex: '0', pointerEvents: 'all' }} gap={3}>
          {pages.length > 1 && (
            <Box width="100%" textAlign="right" visibility={isFirstPage() ? 'hidden' : 'visible'}>
              <BackButton backDisabled={isFirstPage()} onBackPressed={handleBack} />
            </Box>
          )}
          <Stack flexDirection="row" width="100%" gap={2} justifyContent={pages.length > 1 ? 'left' : 'center'}>
            {!isLastPage() && (
              <NextButton
                nextIcon={getButtonStartIcon(valuesAreValidForPage())}
                disabled={isLastPage()}
                onNextPressed={handleNext}
              />
            )}
            {getLockStateModifierButton()}
            {!isLocked && (
              <SaveButton installationGroundworkId={groundworkId.value} energySolutionId={energySolutionId} />
            )}
            <HelpButton onClick={() => showHelpModal()} />
            {includeReload && (
              <Button variant="contained" onClick={refetchHeatDesign}>
                <FormattedMessage id="common.label.reload" />
              </Button>
            )}
          </Stack>
        </Stack>
      </Stack>
      {isLocked && (
        <ResultSnapshotBanner
          position="bottom"
          sx={{
            maxWidth: '90vw',
            pointerEvents: 'all',
          }}
        />
      )}
    </Stack>
  );
}
