import { Button } from '@mui/material';
import { ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';

export default function NextButton({
  nextIcon,
  disabled = false,
  onNextPressed,
}: {
  nextIcon?: ReactNode;
  disabled?: boolean;
  onNextPressed: () => void;
}) {
  return (
    <Button
      disabled={disabled}
      variant="contained"
      onClick={onNextPressed}
      size="small"
      sx={{
        minWidth: 100,
        width: '20vw',
      }}
      startIcon={nextIcon}
      data-testid="heat-design-next-button"
    >
      <FormattedMessage id="common.link.next" />
    </Button>
  );
}
