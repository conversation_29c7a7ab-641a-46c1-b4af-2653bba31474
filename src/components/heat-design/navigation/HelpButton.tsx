import { IconButton } from '@mui/material';
import { QuestionCircleOutlinedIcon } from '@ui/components/StandardIcons/QuestionCircleOutlinedIcon';

export default function HelpButton({ onClick }: { onClick: () => void }) {
  return (
    <IconButton
      onClick={onClick}
      aria-label="help"
      color="primary"
      sx={{
        backgroundColor: '#FFF',
        boxShadow: 2,
        '&:hover': {
          backgroundColor: '#EEE',
          boxShadow: 3,
        },
      }}
    >
      <QuestionCircleOutlinedIcon />
    </IconButton>
  );
}
