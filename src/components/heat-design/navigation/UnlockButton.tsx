import { Button } from '@ui/components/Button/Button';
import { LockOpenFilledIcon } from '@ui/components/StandardIcons/LockOpenFilledIcon';
import { grey } from '@ui/theme/colors';
import React, { useState } from 'react';
import toast from 'react-hot-toast';
import { useIntl } from 'react-intl';
import { api } from 'utils/api';
import { SupportModal } from '../modals/SupportModal';
import { useHeatDesignUI } from '../stores/HeatDesignUIStore';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import ConfirmationPopover from './ConfirmationPopover';
import { usePageNavigationStore } from '../stores/PageNavigationStore';

interface Props {
  energySolutionId: string;
  installationGroundworkId: string;
}

export default function UnlockButton({ energySolutionId, installationGroundworkId }: Props) {
  const { formatMessage } = useIntl();
  const [errorModalIsOpen, setErrorModalIsOpen] = useState(false);
  const setIsLocked = useProtobufHeatDesignStore((s) => s.actions.setIsLocked);
  const [anchorElement, setAnchorElement] = React.useState<HTMLButtonElement | null>(null);
  const isDesignOperationInProgress = useHeatDesignUI((s) => s.isDesignOperationInProgress);
  const setIsOperationInProgress = useHeatDesignUI((s) => s.actions.setIsOperationInProgress);
  const isLastPage = usePageNavigationStore((s) => s.actions.isLastPage);

  const { mutateAsync: unlockHeatDesign, error: unlockError } = api.HeatLossCalculator.unlockHeatDesign.useMutation({
    onSuccess: () => {
      setIsLocked(false);
    },
    onError: () => {
      setErrorModalIsOpen(true);
      setIsOperationInProgress(false);
    },
  });

  const unlockProducts = async () => {
    setIsOperationInProgress(true);
    await toast.promise(unlockHeatDesign({ installationGroundworkId, energySolutionId }), {
      loading: formatMessage({
        id: 'heatDesign.notify.unlockingProject',
      }),
      success: formatMessage({ id: 'heatDesign.notify.projectUnlocked' }),
      error: formatMessage({
        id: 'error.support.card.title.unlock',
      }),
    });
    setIsOperationInProgress(false);
  };

  const handleClose = () => {
    setAnchorElement(null);
  };

  const onClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (anchorElement !== null) {
      handleClose();
    } else {
      setAnchorElement(event.currentTarget);
    }
  };

  return (
    <>
      <ConfirmationPopover
        bodyMessageId="heatDesign.unlock.confirmation"
        onConfirm={unlockProducts}
        anchorElement={anchorElement}
        onClose={handleClose}
      />
      <Button
        variant="contained"
        color="white"
        disableElevation={false}
        disabled={isDesignOperationInProgress}
        onClick={onClick}
        size="small"
        startIcon={<LockOpenFilledIcon />}
        sx={{
          color: grey[900],
          gap: '8px',
          alignItems: 'center',
          minWidth: 'fit-content',
          width: isLastPage() ? '20vw' : '10vw',
          whiteSpace: 'nowrap',
          '&:hover': {
            backgroundColor: 'white !important', // Forces the button to not become transparent on hover
          },
        }}
      >
        {formatMessage({ id: 'hlc.label.unlock.design' })}
      </Button>
      <SupportModal
        onClose={() => setErrorModalIsOpen(false)}
        isOpen={errorModalIsOpen}
        heading={formatMessage({ id: 'error.support.card.title.unlock' })}
        description={formatMessage({ id: 'error.support.card.description.unlock' })}
        errorMessage={unlockError?.message}
      />
    </>
  );
}
