import { Button } from '@ui/components/Button/Button';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import { Stack, Typography } from '@mui/material';
import { StyledFormattedMessage } from 'utils/localization';
import { ContextPopover } from '../components/ContextPopover';

export type ConfirmationPopoverProps = {
  onConfirm: () => void;
  anchorElement: null | HTMLElement;
  onClose: () => void;
  bodyMessageId: MessageDescriptor['id'];
  interstitialComponent?: React.ReactNode;
};

export default function ConfirmationPopover({
  onConfirm,
  anchorElement,
  onClose,
  bodyMessageId,
  interstitialComponent,
}: ConfirmationPopoverProps) {
  const handleConfirm = () => {
    onClose();
    onConfirm();
  };

  return (
    <ContextPopover anchorElement={anchorElement} onClose={onClose} placement="top">
      <Stack spacing={2} padding={2}>
        <Typography variant="body1">
          <StyledFormattedMessage id={bodyMessageId} />
        </Typography>
        {interstitialComponent}
        <Stack direction="row" spacing={2} alignItems="center" justifyContent="center">
          <Button onClick={handleConfirm} size="small">
            <FormattedMessage id="common.label.confirm" />
          </Button>
          <Button variant="outlined" onClick={onClose} size="small">
            <FormattedMessage id="common.label.cancel" />
          </Button>
        </Stack>
      </Stack>
    </ContextPopover>
  );
}
