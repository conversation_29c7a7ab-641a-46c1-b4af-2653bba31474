import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TRPCError } from '@trpc/server';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { RequestHandler } from 'msw';
import { SetupServer, setupServer } from 'msw/node';
import { ClientError } from 'nice-grpc-common/lib/client/ClientError';
import { IntlProvider } from 'react-intl';
import { mockGetServerEnvironment, mockGetTechnicalSpecifications_empty } from 'tests/utils/mockedTrpcCalls';
import {
  filterDifficultRooms,
  goToNextHeatDesignStep,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  trpcMsw,
} from 'tests/utils/testUtils';
import { vi } from 'vitest';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { installationGroundwork, products, solution } from '../../../tests/heat-loss/fixtures/asaTestData';
import HeatDesign from '../HeatDesign';
import { v4 as uuidv4 } from 'uuid';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';

const SAVE_RESPONSE_HAPPY_PATH = trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(async () => ({}));

const SAVE_RESPONSE_INTERNAL_SERVER_ERROR = trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(() => {
  throw new TRPCError({
    message: 'Computer says "no"',
    code: 'INTERNAL_SERVER_ERROR',
    cause: new ClientError('HeatLossCalculator.saveHeatDesign', 13, 'Computer says "no"'),
  });
});

const SAVE_RESPONSE_BAD_REQUEST = trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(() => {
  throw new TRPCError({
    message: 'Whoops, something went wrong when updating the selected products',
    code: 'BAD_REQUEST',
    cause: new ClientError(
      'AiraBackend.patchEnergySolution',
      2,
      'Whoops, something went wrong when updating the selected products',
    ),
  });
});

const SAVE_RESPONSE_LOCKED_DESIGN = trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(() => {
  throw new TRPCError({
    message:
      '/com.aira.acquisition.contract.installation.groundwork.heatdesign.v2.HeatDesignService/SaveHeatDesign FAILED_PRECONDITION: Cannot save a locked heat design for installation groundwork ID XXX',
    code: 'BAD_REQUEST',
    cause: new ClientError(
      'HeatLossCalculator.saveHeatDesign',
      9,
      'Cannot save a locked heat design for installation groundwork ID XXX',
    ),
  });
});

const SEND_DESIGN_FOR_REVIEW_RESPONSE_WRONG_STATE = trpcMsw.HeatLossCalculator.sendDesignForReview.mutation(() => {
  throw new TRPCError({
    message: 'Action SEND_DESIGN_FOR_REVIEW is not available for solution EnergySolutionId[value=XXX]',
    code: 'BAD_REQUEST',
    cause: new ClientError(
      'HeatLossCalculator.sendDesignForReview',
      9,
      'Action SEND_DESIGN_FOR_REVIEW is not available for solution EnergySolutionId[value=XXX]',
    ),
  });
});

const BASE_SERVER = setupServer(
  mockGetServerEnvironment(),
  mockGetTechnicalSpecifications_empty(),
  trpcMsw.AiraBackend.getGrpcEnergySolution.query(() => ({ solution })),
  trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => ({ currentSolution: solution, lastQuotedSolution: solution })),
  trpcMsw.AiraBackend.getProducts.query(() => Promise.resolve(products)),
  trpcMsw.AiraBackend.getGroundworkForSolution.query(() => Promise.resolve(installationGroundwork)),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({ surveyForms: [] })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => ['admin']),
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: {
      ...filterDifficultRooms(untypedAsaHouse as unknown as ProtoHeatDesign),
      ...sampleDefaultDwellingUValueDefaults,
    },
    isLocked: false,
    result: undefined,
    updatedAt: new Date(),
    events: [],
  })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators: [] })),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => ({ reports: [] })),
  trpcMsw.HeatLossCalculator.renderPreview.mutation(async () => ({ pdf: new Uint8Array([1, 2, 3, 4]) })),
  ...sharedHeatDesignHandlers,
);

const SOLUTION_ID = uuidv4();
mockRouterForHeatDesign();
const server: SetupServer = BASE_SERVER;
const setupTest = async (...extraHandlers: Array<RequestHandler>) => {
  server.use(...extraHandlers);

  renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>,
  );

  await reload();
};

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

vi.setConfig({ testTimeout: 60_000 });

// TODO: Test the happy path, but that would require us figuring out how to test that a
// toast notification is shown in Jest, which has proven non-trivial

// TODO: adjust this test to the new 1 accept flow
test('Save failed due to selected products', async () => {
  await setupTest(SAVE_RESPONSE_BAD_REQUEST);

  await userEvent.click(screen.getByRole('button', { name: 'hlc.label.save *' }));
  await screen.findByRole('presentation', { name: 'error.support.card.title.save' });
  await screen.findByText('error.support.card.description.save');
});

test('Save failed due to locked heat design', async () => {
  await setupTest(SAVE_RESPONSE_LOCKED_DESIGN);
  await userEvent.click(screen.getByRole('button', { name: 'hlc.label.save *' }));
  await screen.findByRole('presentation', { name: 'error.support.card.hlc.locked.heading' });
  await screen.findByText('error.support.card.hlc.locked.description');
});

test('Save failed due to INTERNAL_SERVER_ERROR', async () => {
  await setupTest(SAVE_RESPONSE_INTERNAL_SERVER_ERROR);
  await userEvent.click(screen.getByRole('button', { name: 'hlc.label.save *' }));
  await screen.findByRole('presentation', { name: 'error.support.card.title.save' });
  await screen.findByText('error.support.card.description.save');
});

test('Lock failed due to wrong solution state', async () => {
  await setupTest(SAVE_RESPONSE_HAPPY_PATH, SEND_DESIGN_FOR_REVIEW_RESPONSE_WRONG_STATE);

  // Advance to the final page of the heat loss calculator
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();

  await userEvent.click(screen.getByRole('button', { name: 'hlc.label.save.and.lock *' }));

  // Make sure the send email checkbox is ticked
  await userEvent.click(screen.getByRole('checkbox', { name: 'heatDesign.lock.confirmation.sendDesignReviewEmail' }));
  expect(
    await screen.findByRole('checkbox', { name: 'heatDesign.lock.confirmation.sendDesignReviewEmail' }),
  ).toBeChecked();

  await userEvent.click(screen.getByRole('button', { name: 'common.label.confirm' }));
  await screen.findByRole('presentation', { name: 'error.support.card.hlc.sendDesignReviewEmail.heading' });
  await screen.findByText('error.support.card.hlc.sendDesignReviewEmail.wrongState.description');
});

test('Try closing the support modal', async () => {
  await setupTest(SAVE_RESPONSE_INTERNAL_SERVER_ERROR);
  await userEvent.click(screen.getByRole('button', { name: 'hlc.label.save *' }));
  await screen.findByRole('presentation', { name: 'error.support.card.title.save' });
  await screen.findByText('error.support.card.description.save');

  await userEvent.click(screen.getByRole('button', { name: 'Close this dialog' }));
  expect(screen.queryByRole('presentation', { name: 'error.support.card.title.save' })).toBeNull();
});
