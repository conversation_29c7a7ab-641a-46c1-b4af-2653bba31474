import { TRPCClientError } from '@trpc/client';
import { Button } from '@ui/components/Button/Button';
import { Card } from '@ui/components/Card/Card';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { EnvelopeOutlinedIcon } from '@ui/components/StandardIcons/EnvelopeOutlinedIcon';
import { SaveIcon } from '@ui/components/StandardIcons/SaveIcon';
import { useIsSolutionUpdated } from 'components/quotation/hooks/useIsSolutionUpdated';
import { useInvalidateSolution, useSaveSolution } from 'components/quotation/hooks/useSaveSolution';
import { MessageKey } from 'messageType';
import { Status } from 'nice-grpc-common';
import React, { ReactNode, useState } from 'react';
import toast from 'react-hot-toast';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { getGrpcErrorDetails } from 'utils/errors/grpcErrorDetails';
import { parseErrorMessage } from 'utils/helpers';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { isEqualTreatingUndefinedAsMissing } from 'utils/comparison';
import { Checkbox, FormControlLabel, FormGroup, Link, Stack, Typography } from '@mui/material';
import { StyledFormattedMessage } from 'utils/localization';
import { grey } from '@ui/theme/colors';
import { useMarketStore } from 'utils/stores/marketStore';
import { CountryCode, defaultConfigurations, getLocaleFromCountry } from 'utils/marketConfigurations';
import {
  HeatDesignEvent,
  HeatDesignEventType,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { SupportModal } from '../modals/SupportModal';
import { DesignReviewState, DesignReviewStatus, useHeatDesignUI } from '../stores/HeatDesignUIStore';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import ConfirmationPopover from './ConfirmationPopover';
import { useSerializedHeatDesignAndResults } from '../hooks/useSerializedHeatDesignAndResults';
import { generateQuoteSummaryURL, mapRouterLocaleToHeatDesignLocale } from '../utils/helpers';
import { useRouter } from 'next/router';
import { useProductSelectionDifference } from '../modals/product-selection-price-adjust-modal/useProductSelectionDifference';
import { ProductSelectionPriceAdjustModal } from '../modals/product-selection-price-adjust-modal/ProductSelectionPriceAdjustModal';

export function linkText(chunks: ReactNode[], href: string) {
  return (
    <Link href={href} sx={{ textDecoration: 'underline' }} rel="noopener noreferrer" target="_blank">
      {chunks}
    </Link>
  );
}

function SendEmailCheckbox({
  checked,
  onChange,
  designReviewState,
  energySolutionId,
  disabled = false,
}: {
  checked: boolean;
  onChange: (newValue: boolean) => void;
  designReviewState?: DesignReviewState;
  energySolutionId: string;
  disabled?: boolean;
}) {
  const { data: serverEnvironment } = api.AiraBackend.getServerEnvironment.useQuery();
  const { country } = useMarketStore();
  const locale = getLocaleFromCountry(country);
  const finalSpecUrl =
    serverEnvironment === undefined
      ? undefined
      : generateQuoteSummaryURL({ serverEnvironment, locale, solutionId: energySolutionId });

  return (
    <FormGroup>
      {designReviewState && (
        <Card sx={{ border: 1, borderColor: grey[200] }} size="xsmall">
          <Typography>
            <StyledFormattedMessage
              id={`heatDesign.lock.confirmation.sendDesignReviewEmail.status.${designReviewState.status}`}
              values={{
                timestamp: designReviewState.timestamp?.toLocaleString(locale, { timeZoneName: 'short' }),
                linkToFinalSpec: (chunks) => (finalSpecUrl ? linkText(chunks, finalSpecUrl) : chunks),
              }}
            />
          </Typography>
        </Card>
      )}
      <FormControlLabel
        control={<Checkbox disabled={disabled} checked={checked} onChange={(e) => onChange(e.target.checked)} />}
        label={
          <Stack direction="row" spacing={1} alignItems="center">
            <EnvelopeOutlinedIcon />
            <Typography>
              <FormattedMessage id="heatDesign.lock.confirmation.sendDesignReviewEmail" />
            </Typography>
          </Stack>
        }
      />
    </FormGroup>
  );
}

function forceDesignReviewEmail(
  countryCode?: CountryCode,
  designReviewState?: DesignReviewState,
  events?: HeatDesignEvent[],
): boolean {
  if (countryCode === CountryCode.IT) {
    return false;
  }
  const isFirstTimeLocking =
    events?.some((event) => event.type === HeatDesignEventType.HEAT_DESIGN_EVENT_TYPE_LOCKED) === false;
  return (isFirstTimeLocking && designReviewState?.status === DesignReviewStatus.NOT_READY) ?? false;
}

interface Props {
  installationGroundworkId: string;
  energySolutionId: string;
  lockOnSave?: boolean;
  refetchHeatDesign?: () => Promise<any>;
}

export default function SaveButton({
  installationGroundworkId,
  energySolutionId,
  refetchHeatDesign = async () => {},
  lockOnSave = false,
}: Props) {
  const designReviewState = useHeatDesignUI((s) => s.designReviewState);
  const { countryCode } = useMarketStore(); // Solution country
  const { locale } = useRouter(); // Aerospace language
  const events = useProtobufHeatDesignStore((s) => s.events);
  const forceDesignReview = forceDesignReviewEmail(countryCode, designReviewState, events);
  const intl = useIntl();
  const [anchorElement, setAnchorElement] = useState<HTMLButtonElement | null>(null);
  const [errorModalIsOpen, setErrorModalIsOpen] = useState(false);
  const [isOneAcceptModalOpen, setIsOneAcceptModalOpen] = useState(false);
  const [sendDesignReviewEmail, setSendDesignReviewEmail] = useState(forceDesignReview);
  const serializedData = useSerializedHeatDesignAndResults();
  const setProtobufHeatDesign = useProtobufHeatDesignStore((s) => s.actions.setHeatDesign);
  const { checkIsSolutionUpdated } = useIsSolutionUpdated();
  const setIsLocked = useProtobufHeatDesignStore((s) => s.actions.setIsLocked);
  const isDesignOperationInProgress = useHeatDesignUI((s) => s.isDesignOperationInProgress);
  const setIsOperationInProgress = useHeatDesignUI((s) => s.actions.setIsOperationInProgress);
  const queryClient = useQueryClient();

  const originalHeatDesign = useProtobufHeatDesignStore((s) => s.heatDesign);
  const dirty = serializedData && !isEqualTreatingUndefinedAsMissing(originalHeatDesign, serializedData.heatDesign);

  const [errorModalMessage, setErrorModalMessage] = useState<{
    heading: MessageKey;
    description: MessageKey;
    errorMessage?: string;
    selfSupport?: boolean;
  }>({
    heading: 'error.support.card.title.save',
    description: 'error.support.card.description.save',
    errorMessage: undefined,
    selfSupport: false,
  });

  const invalidateSolution = useInvalidateSolution();
  const invalidateLockedReports = () => {
    queryClient.invalidateQueries({ queryKey: getQueryKey(api.HeatLossCalculator.fetchLockedTechnicalReports) });
  };
  const { mutateAsync: saveHeatDesign } = api.HeatLossCalculator.saveHeatDesign.useMutation();
  const { mutateAsync: lockHeatDesign } = api.HeatLossCalculator.lockHeatDesign.useMutation();
  const { mutateAsync: sendDesignReviewEmailMutation } = api.HeatLossCalculator.sendDesignForReview.useMutation();
  const { handleSave: handleSaveEnergySolution } = useSaveSolution();
  const productSelectionDifference = useProductSelectionDifference();

  const hasUnhandledProductChanges = productSelectionDifference.productChanges.unhandledChanges;
  const isPriceSelectionModalShown = hasUnhandledProductChanges;
  const lockDesign = async () => {
    if (sendDesignReviewEmail) {
      await toast.promise(sendDesignReviewEmailMutation({ energySolutionId }), {
        loading: intl.formatMessage({ id: 'heatDesign.notify.sendingDesignReviewEmail' }),
        success: intl.formatMessage({ id: 'heatDesign.notify.designReviewEmailSent' }),
        error: intl.formatMessage({ id: 'error.support.card.hlc.sendDesignReviewEmail.heading' }),
      });
    }
    await toast.promise(
      lockHeatDesign({
        installationGroundworkId,
        energySolutionId,
        locale: mapRouterLocaleToHeatDesignLocale({ locale: locale ?? defaultConfigurations.locale }),
      }),
      {
        loading: intl.formatMessage({ id: 'heatDesign.notify.lockingProject' }),
        success: intl.formatMessage({ id: 'heatDesign.notify.projectLocked' }),
        error: intl.formatMessage({ id: 'error.support.card.title.lock' }),
      },
    );

    setIsLocked(true);
    invalidateSolution();
    invalidateLockedReports();
    await refetchHeatDesign();
  };

  const saveDesign = async () => {
    setIsOneAcceptModalOpen(false);
    if (!serializedData) {
      throw new Error('No serialized data found');
    }
    const { heatDesign, result } = serializedData;
    await toast.promise(
      (async () => {
        // We update the locked price override for all products. By default this sets the override to true for all products
        // with reduced price.
        if (checkIsSolutionUpdated()) {
          await handleSaveEnergySolution();
        }

        await saveHeatDesign({
          installationGroundworkId,
          energySolutionId,
          heatDesign,
          result,
        });
        setProtobufHeatDesign(heatDesign);
      })(),
      {
        loading: intl.formatMessage({ id: 'heatPumpConfig.saveButtonTitle.saving' }),
        success: intl.formatMessage({ id: 'common.notify.saveSuccess' }),
        error: intl.formatMessage({ id: 'error.support.card.title.save' }),
      },
    );
  };

  const handleSave = async () => {
    // Save and if lockOnSave is true, lock the heat design
    setIsOperationInProgress(true);
    try {
      await saveDesign();
      if (lockOnSave) {
        await lockDesign();
      }
    } catch (error) {
      const grpcDetails = getGrpcErrorDetails(error);
      if (
        grpcDetails &&
        grpcDetails.code === Status.FAILED_PRECONDITION &&
        error instanceof TRPCClientError &&
        error.data?.path === 'HeatLossCalculator.saveHeatDesign'
      ) {
        // This will show when trying to save a locked heat design
        // (for example if you locked it in a different tab)
        setErrorModalMessage({
          heading: 'error.support.card.hlc.locked.heading',
          description: 'error.support.card.hlc.locked.description',
          errorMessage: error.message,
        });
        setErrorModalIsOpen(true);
      } else if (
        grpcDetails &&
        error instanceof TRPCClientError &&
        error.data?.path === 'HeatLossCalculator.sendDesignForReview'
      ) {
        if (grpcDetails.code === Status.FAILED_PRECONDITION) {
          // This will show when trying to send a design for review
          // but the solution was in the wrong state e.g. quote not sent
          setErrorModalMessage({
            heading: 'error.support.card.hlc.sendDesignReviewEmail.heading',
            description: 'error.support.card.hlc.sendDesignReviewEmail.wrongState.description',
            selfSupport: true,
          });
        } else {
          // This will show when trying to send a design for review
          // but something unexpected happened
          setErrorModalMessage({
            heading: 'error.support.card.hlc.sendDesignReviewEmail.heading',
            description: 'error.support.card.hlc.sendDesignReviewEmail.description',
            errorMessage: error.message,
          });
        }
        setErrorModalIsOpen(true);
      } else {
        // This will show on other errors during saving or locking
        setErrorModalMessage({
          heading: 'error.support.card.title.save',
          description: 'error.support.card.description.save',
          errorMessage: parseErrorMessage(error),
        });
        setErrorModalIsOpen(true);
      }
    } finally {
      setIsOperationInProgress(false);
    }
  };

  const handleClose = () => {
    setAnchorElement(null);
  };

  const onClick = async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    // Ask user for confirmation if lockOnSave is enabled
    if (lockOnSave) {
      if (anchorElement !== null) {
        handleClose();
      } else {
        setAnchorElement(event.currentTarget);
      }
    } else {
      if (isPriceSelectionModalShown) {
        setIsOneAcceptModalOpen(true);
      } else {
        await handleSave();
      }
    }
  };

  return (
    <>
      <ConfirmationPopover
        bodyMessageId="heatDesign.lock.confirmation.designReview"
        onConfirm={handleSave}
        anchorElement={anchorElement}
        onClose={handleClose}
        interstitialComponent={
          <SendEmailCheckbox
            checked={sendDesignReviewEmail}
            onChange={(newValue) => setSendDesignReviewEmail(newValue)}
            designReviewState={designReviewState}
            energySolutionId={energySolutionId}
            disabled={forceDesignReview}
          />
        }
      />
      <Button
        variant="contained"
        color={lockOnSave ? 'yellow' : 'white'}
        disabled={isDesignOperationInProgress}
        disableElevation={false}
        disableRipple={false}
        onClick={onClick}
        startIcon={lockOnSave ? <CheckIconThin /> : <SaveIcon />}
        size="small"
        sx={{
          gap: '8px',
          alignItems: 'center',
          textWrap: 'nowrap',
          ...(lockOnSave && {
            minWidth: 100,
            width: '20vw',
          }),
          ...(!lockOnSave && {
            '&:hover': {
              backgroundColor: '#EEE',
            },
          }),
        }}
      >
        {lockOnSave
          ? intl.formatMessage({ id: 'hlc.label.save.and.lock' })
          : intl.formatMessage({ id: 'hlc.label.save' })}
        {dirty && ' *'}
      </Button>
      <SupportModal
        onClose={() => setErrorModalIsOpen(false)}
        isOpen={errorModalIsOpen}
        heading={intl.formatMessage({ id: errorModalMessage.heading })}
        description={intl.formatMessage({ id: errorModalMessage.description })}
        errorMessage={errorModalMessage.errorMessage}
        selfSupport={errorModalMessage.selfSupport}
      />
      <ProductSelectionPriceAdjustModal
        onSubmit={saveDesign}
        isOpen={isOneAcceptModalOpen}
        onClose={() => setIsOneAcceptModalOpen(false)}
      />
    </>
  );
}
