import { <PERSON>lapse, Divider, Icon<PERSON>utton, Stack, Typography } from '@mui/material';
import { Box, SxProps } from '@mui/system';
import { EmitterIcon } from '@ui/components/StandardIcons/EmitterIcon';
import { Card } from '@ui/components/Card/Card';
import { HouseWave3Icon } from '@ui/components/StandardIcons/HouseWave/HouseWave3Icon';
import { Wave3HorizontalOutlinedIcon } from '@ui/components/StandardIcons/Wave3HorizontalOutlinedIcon';
import { ClockDotsOutlinedIcon } from '@ui/components/StandardIcons/ClockDotsOutlinedIcon';
import { EmptyOutlinedIcon } from '@ui/components/StandardIcons/EmptyOutlinedIcon';
import { EnvelopeCheckOutlinedIcon } from '@ui/components/StandardIcons/EnvelopeCheckOutlinedIcon';
import { ChevronDown, ChevronUp } from '@ui/components/Icons/Chevron/Chevron';
import { FormattedMessage } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import { grey } from '@ui/theme/colors';
import { ElementType, useState } from 'react';
import InfoBlock from '../radiators/RadiatorRenderModalInfoBlock';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import { toOneDecimalPlaceText, toTwoDecimalPlaces } from '../utils/helpers';
import { DesignReviewState, DesignReviewStatus, useHeatDesignUI } from '../stores/HeatDesignUIStore';
import SendEmailButton from './SendEmailButton';

export type InfoBlockProps = {
  icon?: React.ReactNode;
  value?: string;
  label: React.ReactNode;
  id?: string;
  sx?: SxProps;
};

function DarkInfoBlock({ sx, icon, value, label, id }: InfoBlockProps) {
  return (
    <Box
      borderRadius={2}
      p={1}
      px={2}
      sx={{
        display: 'flex',
        background: `${grey[900]}10`,
        ...sx,
      }}
    >
      <InfoBlock id={id} icon={icon} value={value ?? ''} label={label} />
    </Box>
  );
}

const designReviewIcons = {
  ready: ClockDotsOutlinedIcon,
  'not-ready': EmptyOutlinedIcon,
  accepted: EnvelopeCheckOutlinedIcon,
};

function DesignReviewStateIcon({ state }: { state: DesignReviewState }) {
  const Icon: ElementType | null = designReviewIcons[state.status];
  return Icon ? <Icon /> : null;
}

export type ResultSnapshotBannerProps = {
  sx?: SxProps;
  position?: 'top' | 'bottom';
};

export default function ResultSnapshotBanner({ sx, position }: ResultSnapshotBannerProps) {
  const BORDER_RADIUS = 16;
  const [statsVisible, setStatsVisible] = useState(true);

  const result = useProtobufHeatDesignStore((s) => s.result);
  const heatDesign = useProtobufHeatDesignStore((s) => s.heatDesign);
  const updatedAt = useProtobufHeatDesignStore((s) => s.updatedAt);
  const isDesignOperationInProgress = useHeatDesignUI((s) => s.isDesignOperationInProgress);
  const designReviewState = useHeatDesignUI((s) => s.designReviewState);

  const houseHeatLoss = result?.totalHeatLossWatt;
  const totalEmitterOutput = result?.totalOutputOfHeatEmittersWatt;
  const flowTemperature = heatDesign?.dwelling?.systemDesign?.flowTemperatureCelsius;

  const toggleStats = () => {
    setStatsVisible((prev) => !prev);
  };

  const hasStats = houseHeatLoss !== undefined || totalEmitterOutput !== undefined || flowTemperature !== undefined;

  return (
    <Stack>
      <Card
        size="xsmall"
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'background.default',
          boxShadow: '0px 10px 15px 0px #00000040',
          ...(position === 'top' && { borderBottomLeftRadius: BORDER_RADIUS, borderBottomRightRadius: BORDER_RADIUS }),
          ...(position === 'bottom' && { borderTopLeftRadius: BORDER_RADIUS, borderTopRightRadius: BORDER_RADIUS }),
          ...sx,
        }}
      >
        <Stack alignItems="center" width="100%" sx={{ position: 'relative' }}>
          <Stack direction="row" gap={1} alignItems="center" width="100%">
            {hasStats && (
              <IconButton
                onClick={toggleStats}
                size="small"
                aria-label={statsVisible ? 'Hide stats' : 'Show stats'}
                data-testid="heatDesign-resultSnapshot-toggleStats"
                sx={{
                  position: 'absolute',
                  left: 0,
                }}
              >
                {statsVisible ? <ChevronDown width={25} height={25} /> : <ChevronUp width={25} height={25} />}
              </IconButton>
            )}
            <Stack direction="row" width="100%" gap={1} px="35px" alignItems="center" justifyContent="center">
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="center"
                borderRadius={2}
                p={1}
                px={2}
                sx={{ background: `white` }}
              >
                <Stack direction="row" alignItems="center" gap={2} justifyContent="center">
                  <Typography data-testid="heatDesign-resultSnapshot-lockedAt">
                    <FormattedMessage
                      id="heatDesign.resultSnapshotBanner.lockedAt"
                      defaultMessage="Locked at {lockedAtDate}"
                      values={{ lockedAtDate: updatedAt?.toLocaleString() }}
                    />
                  </Typography>
                  {designReviewState && (
                    <>
                      <Divider
                        orientation="vertical"
                        variant="middle"
                        flexItem
                        sx={{
                          alignItems: 'center',
                          justifyContent: 'center',
                          height: '25px',
                          background: 'black',
                          margin: '0',
                        }}
                      />
                      <Stack direction="row" gap={1} justifyContent="center" alignItems="center">
                        <DesignReviewStateIcon state={designReviewState} />
                        <StyledFormattedMessage
                          id={`heatDesign.resultSnapshotBanner.designReviewState.${designReviewState.status}`}
                          values={{
                            timestamp: designReviewState.timestamp?.toLocaleString(),
                          }}
                        />
                      </Stack>
                    </>
                  )}
                </Stack>
              </Stack>
              {designReviewState?.status === DesignReviewStatus.NOT_READY && <SendEmailButton />}
            </Stack>
          </Stack>
          <Collapse in={statsVisible} timeout={300}>
            <Stack direction="column" gap={2} alignItems="center" justifyContent="center" pt={2}>
              {isDesignOperationInProgress ? (
                <Typography variant="body2">Loading...</Typography>
              ) : (
                <Stack direction="row" gap={3} justifyContent="center">
                  {houseHeatLoss !== undefined && houseHeatLoss > 0 && (
                    <DarkInfoBlock
                      id="heatDesign-resultSnapshot-heatLoss"
                      icon={<HouseWave3Icon />}
                      value={`${toTwoDecimalPlaces(houseHeatLoss / 1000)} kW`}
                      label={<FormattedMessage id="heatDesign.resultSnapshotBanner.houseHeatLoss" />}
                    />
                  )}
                  {totalEmitterOutput !== undefined && totalEmitterOutput > 0 && (
                    <DarkInfoBlock
                      id="heatDesign-resultSnapshot-totalEmitterOutput"
                      icon={<EmitterIcon />}
                      value={`${toTwoDecimalPlaces(totalEmitterOutput / 1000)} kW`}
                      label={<FormattedMessage id="heatDesign.resultSnapshotBanner.totalEmitterOutput" />}
                    />
                  )}
                  {flowTemperature !== undefined && flowTemperature > 0 && (
                    <DarkInfoBlock
                      id="heatDesign-resultSnapshot-flowTemperature"
                      value={`${toOneDecimalPlaceText(flowTemperature)} °C`}
                      icon={<Wave3HorizontalOutlinedIcon />}
                      label={<FormattedMessage id="heatDesign.resultSnapshotBanner.flowTemperature" />}
                    />
                  )}
                </Stack>
              )}

              <Typography variant="body2" data-testid="heatDesign-resultSnapshot-description">
                <StyledFormattedMessage id="heatDesign.resultSnapshotBanner.description" />
              </Typography>
            </Stack>
          </Collapse>
        </Stack>
      </Card>
    </Stack>
  );
}
