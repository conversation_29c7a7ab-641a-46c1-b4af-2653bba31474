import { Button, getButtonStartIcon, getButtonVariant } from '@ui/components/Button/Button';
import { Stack } from '@mui/system';
import { Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import DoorsWindowsRenderer from './room-components/DoorsWindowsRenderer';
import HorizontalSurfaceRenderer from './room-components/HorizontalSurfaceRenderer';
import RoofGlazingsRenderer from './room-components/RoofGlazingsRenderer';
import { arrayOfTypesIsValid } from './Validator';
import type { Room } from './stores/types';
import type { RoomRendererSelectionType } from './SelectSurface';
import WallsRenderer from './room-components/WallsRenderer';
import { useValidateUValuesForRoom } from './utils/useValidateUValuesForRoom';

interface RoomRendererEditorProps {
  selected?: RoomRendererSelectionType;
  setSelected: (a: RoomRendererSelectionType) => void;
  room: Room;
  children: React.ReactNode;
}

export default function RoomRendererEditor({ selected, setSelected, room, children }: RoomRendererEditorProps) {
  const { validateSurfaceUValues } = useValidateUValuesForRoom(room);
  const roofsOrCeilingsAreValid =
    arrayOfTypesIsValid('roofsOrCeilings', room.surfaces.roofsOrCeilings) &&
    validateSurfaceUValues(room.surfaces.roofsOrCeilings);

  const floorsAreValid =
    arrayOfTypesIsValid('floors', room.surfaces.floors) && validateSurfaceUValues(room.surfaces.floors);

  const roofGlazingsAreValid =
    arrayOfTypesIsValid('roofGlazings', room.surfaces.roofGlazings) &&
    validateSurfaceUValues(room.surfaces.roofGlazings);

  const { formatMessage } = useIntl();

  return (
    <Stack spacing={2}>
      <Stack direction="row" spacing={2} pb={2}>
        <Button
          variant={getButtonVariant(selected?.type === 'roofsOrCeilings', roofsOrCeilingsAreValid)}
          startIcon={getButtonStartIcon(roofsOrCeilingsAreValid)}
          onClick={() => setSelected({ type: 'roofsOrCeilings', surfaceUid: undefined })}
          size="small"
          fullWidth
        >
          <FormattedMessage id="heatDesign.surfaces.roof" />
        </Button>
        <Button
          variant={getButtonVariant(selected?.type === 'floors', floorsAreValid)}
          startIcon={getButtonStartIcon(floorsAreValid)}
          onClick={() => setSelected({ type: 'floors', surfaceUid: undefined })}
          size="small"
          fullWidth
        >
          <FormattedMessage id="heatDesign.surfaces.floor" />
        </Button>
        <Button
          variant={getButtonVariant(selected?.type === 'roofGlazings', roofGlazingsAreValid)}
          startIcon={getButtonStartIcon(roofGlazingsAreValid)}
          onClick={() => setSelected({ type: 'roofGlazings', surfaceUid: undefined })}
          size="small"
          fullWidth
        >
          <FormattedMessage id="heatDesign.roomSurfaceTypes.roofGlazings" />
        </Button>
      </Stack>
      <Stack direction="row" gap={2} alignItems="baseline" sx={{ textTransform: 'capitalize' }}>
        {selected && (
          <Typography variant="headline2">
            <FormattedMessage
              id="heatDesign.title.surfaceEditor"
              values={{
                surfaceType: formatMessage({ id: `heatDesign.surfaces.${selected.type}` }),
              }}
            />
          </Typography>
        )}
      </Stack>
      {(selected?.type === 'externalWalls' || selected?.type === 'partyWalls' || selected?.type === 'internalWalls') &&
        selected.surfaceUid && <WallsRenderer room={room} wallUid={selected.surfaceUid} />}
      {selected?.type === 'doors' && selected.surfaceUid && (
        <DoorsWindowsRenderer room={room} fabricType="doors" fabricId={selected.surfaceUid} />
      )}
      {selected?.type === 'windows' && selected.surfaceUid && (
        <DoorsWindowsRenderer room={room} fabricType="windows" fabricId={selected.surfaceUid} />
      )}

      {selected?.type === 'roofsOrCeilings' && <HorizontalSurfaceRenderer room={room} surfaceType="roofsOrCeilings" />}
      {selected?.type === 'floors' && <HorizontalSurfaceRenderer room={room} surfaceType="floors" />}
      {selected?.type === 'roofGlazings' && <RoofGlazingsRenderer room={room} />}
      {children}
    </Stack>
  );
}
