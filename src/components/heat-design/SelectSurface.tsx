import { Stack, MenuItem, Typography, SxProps, Theme } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { FormattedMessage } from 'react-intl';
import { theme } from '@ui/theme/theme';
import { Door, Room, RoomFabricTypes, Wall, Window } from './stores/types';
import { typeIsValid, wallIsValid } from './Validator';

export type RoomRendererSelectionType = {
  type: RoomFabricTypes;
  surfaceUid?: string;
  wallUID?: string;
};

const isValidAttachedSurface = (surface: Wall | Door | Window) => {
  if (surface.surfaceType === 'windows' || surface.surfaceType === 'doors') {
    return typeIsValid(surface.surfaceType, surface);
  }
  return wallIsValid(surface);
};

export const attachedElements = (surfaces: (Window | Door | Wall)[], selectedSurface: RoomRendererSelectionType) =>
  surfaces.filter((elmt: Window | Door | Wall) => {
    if (elmt.surfaceType === 'windows' || elmt.surfaceType === 'doors') {
      return (
        selectedSurface.wallUID === elmt.wallUID || // siblings
        selectedSurface.surfaceUid === elmt.wallUID
      ); // its children
    }
    return (
      selectedSurface.surfaceUid === elmt.uid || // itself
      selectedSurface.wallUID === elmt.uid
    ); // its wall
  });
const getType = (id: string, doors: Door[], walls: Wall[]): { selectionType: RoomRendererSelectionType['type'] } => {
  if (doors.some((d) => d.uid === id)) return { selectionType: 'doors' };
  const selectedWall = walls.find((wall) => wall.uid === id)!;
  if (selectedWall) {
    return {
      selectionType: selectedWall.surfaceType,
    };
  }
  return {
    selectionType: 'windows',
  };
};
export default function SelectRoomElement({
  currentRoom,
  selectedSurface,
  setSelectedSurface,
  sx,
}: {
  currentRoom: Room;
  selectedSurface?: RoomRendererSelectionType;
  setSelectedSurface: (selectedSurface?: RoomRendererSelectionType) => void;
  sx?: SxProps<Theme>;
}) {
  if (!selectedSurface?.surfaceUid) return null;

  const { walls, windows, doors } = currentRoom.surfaces;
  const attachedToWall = attachedElements([...walls, ...windows, ...doors], selectedSurface!);

  if (attachedToWall.length < 2) return null;

  const validBySurfaceId = attachedToWall.reduce(
    (acc, surface) => {
      acc[surface.uid] = isValidAttachedSurface(surface);
      return acc;
    },
    {} as Record<string, boolean>,
  );

  const anySurfaceIsInvalid = Object.values(validBySurfaceId).some((isValid) => !isValid);

  return (
    <Stack direction="column" spacing={2} justifyContent="center" alignItems="center" mb={2} sx={sx}>
      <Typography variant="inputLabel" component="div">
        <FormattedMessage id="heatDesign.title.dropdownAlternativeSelect" />
      </Typography>
      <Select
        error={anySurfaceIsInvalid}
        label=""
        labelId="select-room-element"
        id="select-room-element"
        value={selectedSurface?.surfaceUid ?? ''}
        name="select room element"
        onChange={({ target: { value } }) =>
          setSelectedSurface({
            type: getType(value, doors, walls).selectionType,
            surfaceUid: value as string,
            wallUID: [...doors, ...windows].find((elmt) => elmt.uid === value)?.wallUID,
          })
        }
      >
        {attachedToWall.map((surface: Wall | Door | Window) => (
          <MenuItem
            key={surface.uid}
            value={surface.uid}
            sx={{ ...(!validBySurfaceId[surface.uid] && { color: theme.palette.warning.main }) }}
          >
            {surface.surfaceType}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  );
}
