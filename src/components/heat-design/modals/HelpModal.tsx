import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { Modal } from '@ui/components/Modal/Modal';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { FormattedMessage } from 'react-intl';
import { ErrorContent } from '../components/errors/ErrorContent';
import { HeatDesignErrorInfo } from '../components/errors/HeatDesignErrorInfo';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HelpModal({ isOpen, onClose }: HelpModalProps) {
  const energySolutionId = useEnergySolutionId();
  const {
    groundwork: { id: groundworkId },
  } = useGroundwork();

  const onClickFAQ = () => {
    // this we should probably set in the env file? or a config file?
    const faqUrl = 'https://wiki.airahome.com/s/aira/p/frequently-asked-questions-fa-qs-UP96zwnQfz';
    window.open(faqUrl, '_blank');
  };

  return (
    <Modal showCloseButton isModalOpen={isOpen} handleClose={onClose} height="auto" data-testid="help-modal">
      <Stack gap={2}>
        <Heading variant="headline3" level={3}>
          <FormattedMessage id="heatDesign.helpModal.faq.title" />
        </Heading>
        <Typography>
          <FormattedMessage id="heatDesign.helpModal.faq.body" />
        </Typography>
        <Button size="medium" startIcon={<ArrowRightTopSquareOutlinedIcon />} variant="outlined" onClick={onClickFAQ}>
          <FormattedMessage id="heatDesign.helpModal.faq.button" />
        </Button>
        <Heading variant="headline3" level={3}>
          <FormattedMessage id="heatDesign.helpModal.request.title" />
        </Heading>
        <Typography>
          <FormattedMessage id="heatDesign.helpModal.request.body" />
        </Typography>
        <ErrorContent
          details={
            <>
              <HeatDesignErrorInfo solutionId={energySolutionId} groundworkId={groundworkId?.value} />
            </>
          }
        />
      </Stack>
    </Modal>
  );
}
