import { ButtonOwnProps, CircularProgress, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { MessageKey } from 'messageType';
import { FormattedMessage } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';

export function MagicplanReimportModal({
  isOpen,
  onClose,
  action,
  isActionInProgress,
  actionColour,
  actionIcon,
  actionInProgressTitle,
  actionTitle,
  title,
  body,
}: {
  isOpen: boolean;
  onClose: () => void;
  action: () => void;
  isActionInProgress: boolean;
  actionColour?: ButtonOwnProps['color'];
  actionIcon: ButtonOwnProps['startIcon'];
  actionInProgressTitle: MessageKey;
  actionTitle: MessageKey;
  title: MessageKey;
  body: MessageKey;
}) {
  return (
    <Modal
      height="auto"
      isModalOpen={isOpen}
      heading={<FormattedMessage id={title} />}
      handleClose={() => {
        // Don't allow the modal to be closed whilst the re-import is happening
        if (isActionInProgress) return;
        onClose();
      }}
    >
      <Stack gap={2} mt={3}>
        <Typography variant="body1">
          <StyledFormattedMessage id={body} />
        </Typography>
        <Stack direction="row" gap={2}>
          <Button
            variant="contained"
            color={actionColour}
            style={{ width: '100%' }}
            disabled={isActionInProgress}
            onClick={action}
            startIcon={isActionInProgress ? <CircularProgress /> : actionIcon}
          >
            <FormattedMessage id={isActionInProgress ? actionInProgressTitle : actionTitle} />
          </Button>
          <Button variant="outlined" style={{ width: '100%' }} onClick={onClose} disabled={isActionInProgress}>
            <FormattedMessage id="common.label.cancel" />
          </Button>
        </Stack>
      </Stack>
    </Modal>
  );
}
