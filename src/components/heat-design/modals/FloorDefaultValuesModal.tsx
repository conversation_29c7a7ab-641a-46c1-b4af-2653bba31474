import { Box, Stack } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { Modal } from '@ui/components/Modal/Modal';
import { ButtonOwnProps, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import { beige } from '@ui/theme/colors';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { useGetRoomsByFloor } from '../stores/RoomsStore';
import { RoomFabricTypes, ROOM_FABRIC_TYPES } from '../stores/types';
import { useProjectUValues } from '../stores/UValuesStore';
import { UValue } from '../models/UValue';
import { aRoomHasUValueForFabricType, lookupUValueForSurfaceType } from '../utils/helpers';
import { useFloors, useFloorsActions, useFloorsStore } from '../stores/FloorsStore';
import { UValueInput } from '../components/u-value-input/UValueInput';
import { SOIL_PERCENTAGE_VALUES } from '../constants';

interface UValuesModalProps {
  isOpen: boolean;
  onClose: () => void;
  floorUID: string;
}

function getSoilButtonVariant(storedSoilValue: null | number, buttonValue: number): ButtonOwnProps['variant'] {
  if (storedSoilValue === null) return 'invalid';
  if (buttonValue === storedSoilValue) return 'contained';
  return 'outlined';
}

export default function FloorDefaultValuesModal({ isOpen, onClose, floorUID }: UValuesModalProps) {
  const { formatMessage } = useIntl();
  const floor = useFloorsStore((state) => state.floors.find((f) => f.uid === floorUID));
  const floors = useFloors();
  const roomsInFloor = useGetRoomsByFloor()[floor?.uid ?? ''];
  const projectUValues = useProjectUValues();
  const floorActions = useFloorsActions();

  const handleUValueSelection = (fabricType: RoomFabricTypes, uValue?: UValue) => {
    floorActions.setFloorUValue({ floorUID, fabricType, uValue });
  };

  if (!isOpen || !roomsInFloor || !floor) return null;

  const isLowestLevel = floor.floorNr === Math.min(...floors.map((f) => f.floorNr));

  const updateSoildPercentageDefault = (newValue: number) => {
    floorActions.updateFloor({ ...floor, soilPercentageDefault: newValue });
  };

  return (
    <Modal
      isModalOpen={isOpen}
      handleClose={onClose}
      height="770px"
      width="800px"
      showCloseButton
      heading={<FormattedMessage id="heatDesign.floorDefaultsModal.title" />}
      sx={{ padding: 2.5 }}
    >
      <Stack sx={{ height: '100%', overflow: 'auto' }}>
        <Box>
          <Typography mb={3}>
            <StyledFormattedMessage id="heatDesign.floorDefaultsModal.description" />
          </Typography>
        </Box>
        <Stack direction="column" sx={{ backgroundColor: beige[100], padding: 2.5, borderRadius: 2, mb: 4 }}>
          <Heading level={2} variant="headline3" mb={1}>
            <FormattedMessage id="heatDesign.floorDefaultsModal.generalSection.title" />
          </Heading>
          <Typography
            mb={2}
            component="label"
            id="surface-contact-with-soil-label"
            display="inline-flex"
            gap={1}
            variant="body1Emphasis"
          >
            <FormattedMessage id="heatDesign.floorDefaultsModal.soilInput.label" />
            <TooltipAira
              placement="top-start"
              title={formatMessage({
                id: 'heatDesign.floorDefaultsModal.soilInput.tooltip',
              })}
            />
          </Typography>
          <Stack gap={2} direction="row">
            {SOIL_PERCENTAGE_VALUES.map((value) => (
              <Button
                key={value}
                label={`${value}%`}
                onClick={() => updateSoildPercentageDefault(value)}
                size="medium"
                variant={getSoilButtonVariant(floor.soilPercentageDefault, value)}
                fullWidth
                aria-labelledby="surface-contact-with-soil-label"
              />
            ))}
          </Stack>
        </Stack>
        <Stack direction="column" gap={3} sx={{ backgroundColor: beige[100], padding: 2.5, borderRadius: 2 }}>
          <Heading level={2} variant="headline3">
            <FormattedMessage id="heatDesign.uValues.uValues" />
          </Heading>
          {ROOM_FABRIC_TYPES.map((fabricType) => {
            const aRoomHasUValueForFabric = aRoomHasUValueForFabricType(fabricType, roomsInFloor);
            const uValueLookup = lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors);

            let allowUserToChangeValue = true;
            if (fabricType === 'roofsOrCeilings') {
              allowUserToChangeValue = false;
            } else if (fabricType === 'floors' && isLowestLevel) {
              allowUserToChangeValue = false;
            }

            const disabled = !aRoomHasUValueForFabric || !allowUserToChangeValue;
            let placeholderMessage = formatMessage({ id: 'heatDesign.uValues.placeholder' });

            if (!allowUserToChangeValue) {
              placeholderMessage = formatMessage({ id: 'heatDesign.uValues.placeholder.setAtDwelling' });
            } else if (!aRoomHasUValueForFabric) {
              placeholderMessage = formatMessage(
                { id: 'heatDesign.uValues.placeholder.noFabricTypeInProject' },
                { fabricType: formatMessage({ id: `heatDesign.surfaces.${fabricType}` }) },
              );
            }

            return (
              <Stack direction="column" key={fabricType}>
                <UValueInput
                  surfaceType={fabricType}
                  placeholder={placeholderMessage}
                  value={uValueLookup?.uValue}
                  label={formatMessage({ id: `heatDesign.roomSurfaceTypes.${fabricType}` })}
                  disabled={disabled}
                  error={!uValueLookup?.uValue}
                  onChange={(newUValue) => handleUValueSelection(fabricType, newUValue)}
                />
                {(uValueLookup?.source === 'dwelling' || uValueLookup?.source === 'floorLevelAbove') && (
                  <Typography variant="body2" mt={0.5}>
                    <StyledFormattedMessage id={`heatDesign.usingDefaultUValue.${uValueLookup.source}`} />
                  </Typography>
                )}
              </Stack>
            );
          })}
        </Stack>
      </Stack>
    </Modal>
  );
}
