import { HeatDesignModal, useHeatDesignUI, useHeatDesignUIActions } from '../stores/HeatDesignUIStore';
import { HelpModal } from './HelpModal';
import RoomModal from './RoomModal';
import { UnsavedChangeNoticeModal } from './UnSavedChangeNoticeModal';
import { DwellingValidationModal, RoomEmittersValidationModal, RoomValidationModal } from './ValidationModal';

export default function Modals() {
  const modal = useHeatDesignUI((s) => s.modal);
  const { closeModal } = useHeatDesignUIActions();
  return (
    <>
      <RoomEmittersValidationModal isOpen={modal === HeatDesignModal.RoomEmittersValidation} onClose={closeModal} />
      <RoomValidationModal isOpen={modal === HeatDesignModal.RoomValidation} onClose={closeModal} />
      <DwellingValidationModal isOpen={modal === HeatDesignModal.DwellingValidation} onClose={closeModal} />
      <UnsavedChangeNoticeModal onClose={closeModal} isOpen={modal === HeatDesignModal.UnsavedChangeNotice} />
      <RoomModal isOpen={modal === HeatDesignModal.Room} onClose={closeModal} />
      <HelpModal isOpen={modal === HeatDesignModal.Help} onClose={closeModal} />
    </>
  );
}
