import { api } from '../../../../utils/api';
import { useEnergySolutionId } from '../../../../hooks/useEnergySolution';
import { useMarketStore } from '../../../../utils/stores/marketStore';
import { useCallback, useMemo } from 'react';
import { HeatPumpPackages, useHeatPumpStore } from '../../../quotation/stores/HeatPumpPackageStore';
import {
  InstallationAddonsPackages,
  useInstallationAddonsStore,
} from '../../../quotation/stores/InstallationAddonsStore';
import { useAddonsStore } from '../../../quotation/stores/AddonsStore';
import { PackageItem } from '../../../../types/types';
import {
  Discount,
  EnergySolutionProduct,
} from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';
import { cloneDeep } from 'lodash';
import { isNotNullish } from '../../../../utils/isNotNullish';
import { useEvChargerStore } from '../../../quotation/stores/EvChargerStore';
import { useMiscellaneousStore } from '../../../quotation/stores/MiscellaneousStore';
import { useSolarAndBatteryStore } from '../../../quotation/stores/SolarStore';

export type ProductSelectionDifferenceGroup = {
  lastQuoted: PackageItem | (EnergySolutionProduct & { overrideLockedPrice?: boolean }) | undefined;
  current: PackageItem | (EnergySolutionProduct & { overrideLockedPrice?: boolean }) | undefined;
  priceDifference: number;
  currencyCode: string;
};

export type ProductSelectionDifference = {
  productChanges: {
    priceIncreasedProducts: ProductSelectionDifferenceGroup[];
    priceReducedProducts: ProductSelectionDifferenceGroup[];
    addedProducts: ProductSelectionDifferenceGroup[];
    removedProducts: ProductSelectionDifferenceGroup[];
    revertedProducts: ProductSelectionDifferenceGroup[];
    handledProducts: ProductSelectionDifferenceGroup[];
    unhandledChanges: boolean;
  };
  updateLockedPriceOverride: (productIdsToUpdate?: string[]) => void;
  quoteMinorAmountExcludingTax: number;
  updatedMinorAmountExcludingTax: number;
  currencyCode: string;
  hasLockedPriceAdjustment: boolean;
};

export const getProductCategory = (product: EnergySolutionProduct | PackageItem) => {
  const details = 'productDetails' in product ? product.productDetails?.details : product.details?.details;

  if (!details) {
    return getProductId(product);
  }
  if (details.$case === 'addon') {
    return details.addon.category + (product.productId?.value ?? '');
  } else if (details.$case === 'installationOption') {
    return details.installationOption.category;
  } else if (details.$case === 'installationAddon') {
    return details.installationAddon.category;
  } else if (details.$case === 'dynamic') {
    return getProductId(product);
  }
  return details.$case;
};

export const findProductInCategory = (products: EnergySolutionProduct[] | PackageItem[], category: string) => {
  return products.find((product) => {
    const currentCategory = getProductCategory(product);
    if (!currentCategory) {
      return false;
    }
    return category === currentCategory;
  });
};

export const getProductId = (product: EnergySolutionProduct | PackageItem) => {
  return product.productId?.value ?? product.sku;
};

export const getProductPrice = (product: PackageItem | EnergySolutionProduct | undefined) => {
  if (!product) {
    return 0;
  } else {
    if (typeof product.price === 'number') {
      return product.price * product.quantity;
    } else {
      return (product.price?.taxDetails?.minorAmountExcludingTax ?? 0) * product.quantity;
    }
  }
};

const getTotalDiscountAmount = (discounts: Discount[] | undefined) => {
  return (
    discounts?.reduce((acc, discount) => {
      if (discount.details?.variant?.$case === 'fixedAmountDiscount') {
        return acc + (discount.details?.variant.fixedAmountDiscount.amount?.taxDetails?.minorAmountExcludingTax ?? 0);
      }
      return acc;
    }, 0) ?? 0
  );
};

export function useProductSelectionDifference() {
  const energySolutionId = useEnergySolutionId();
  const { currency } = useMarketStore();
  const { selectedHeatPumpPackages, setSelectedHeatPumpPackages } = useHeatPumpStore();
  const { selectedInstallationAddonsPackages, setSelectedInstallationAddonsPackages } = useInstallationAddonsStore();
  const { selectedAddons, setSelectedAddons, getSelectedAddonsWithListPrice } = useAddonsStore();
  const { selectedEvChargerPackage, setSelectedEvChargerPackage } = useEvChargerStore();
  const { selectedMiscellaneous, setSelectedMiscellaneous } = useMiscellaneousStore();
  const { selectedSolarPackage, setSelectedSolarPackage } = useSolarAndBatteryStore();

  const solutionData = api.AiraBackend.getEnergySolutionDiff.useQuery(
    { energySolutionId: energySolutionId! },
    {
      enabled: !!energySolutionId,
    },
  );

  const hasLockedPriceAdjustment = useMemo(() => {
    return solutionData.data?.currentSolution?.presentation?.discounts.some(
      (discount) => discount.details?.variant?.$case === 'lockedPriceAdjustment',
    );
  }, [solutionData.data?.currentSolution?.presentation?.discounts]);

  const updatedPriceExclVat = useMemo(() => {
    const quoteDiscount = getTotalDiscountAmount(solutionData.data?.lastQuotedSolution?.presentation?.discounts);
    const solutionDiscount = getTotalDiscountAmount(solutionData.data?.currentSolution?.presentation?.discounts);

    const discountDiff = quoteDiscount - solutionDiscount;

    const quotedMinorAmountExclTax =
      solutionData.data?.lastQuotedSolution?.presentation?.salesPrice?.taxDetails?.minorAmountExcludingTax ?? 0;

    return quotedMinorAmountExclTax + discountDiff;
  }, [
    solutionData.data?.lastQuotedSolution?.presentation,
    solutionData.data?.currentSolution?.presentation?.discounts,
  ]);

  const lastQuotedPriceExclVat = useMemo(() => {
    return solutionData.data?.lastQuotedSolution?.presentation?.salesPrice?.taxDetails?.minorAmountExcludingTax;
  }, [solutionData.data?.lastQuotedSolution?.presentation?.salesPrice?.taxDetails?.minorAmountExcludingTax]);

  const selectedAddonsWithListPrice = getSelectedAddonsWithListPrice();
  const productChanges = useMemo(() => {
    const quotedProducts = solutionData.data?.lastQuotedSolution?.presentation?.products ?? [];
    const currentSolutionProducts = solutionData.data?.currentSolution?.presentation?.products ?? [];
    const selectedProducts: PackageItem[] = [
      ...Object.values(selectedHeatPumpPackages).flat(),
      ...Object.values(selectedInstallationAddonsPackages).flat(),
      ...selectedAddonsWithListPrice,
      ...selectedMiscellaneous,
      selectedEvChargerPackage,
      selectedSolarPackage,
    ].filter(isNotNullish);
    const removedProducts: ProductSelectionDifferenceGroup[] = [];
    const priceIncreasedProducts: ProductSelectionDifferenceGroup[] = [];
    const priceReducedProducts: ProductSelectionDifferenceGroup[] = [];
    const addedProducts: ProductSelectionDifferenceGroup[] = [];
    const productsRevertedToQuote: ProductSelectionDifferenceGroup[] = []; //Changes that means going back to what originally was selected in the quote.
    const handledProducts: ProductSelectionDifferenceGroup[] = []; //Changes for displaying only. They are old and have previously been handled by the user.

    for (const selectedProduct of selectedProducts) {
      const category = getProductCategory(selectedProduct);
      const currentSolutionProduct = findProductInCategory(currentSolutionProducts, category);
      const quotedProduct = findProductInCategory(quotedProducts, category);

      if (currentSolutionProduct) {
        const changedFromCurrentSolution =
          getProductId(selectedProduct) !== getProductId(currentSolutionProduct) ||
          selectedProduct.quantity !== currentSolutionProduct.quantity;
        if (changedFromCurrentSolution) {
          const priceDifference = getProductPrice(selectedProduct) - getProductPrice(quotedProduct);
          const matchesProductInQuote =
            quotedProduct &&
            getProductId(selectedProduct) === getProductId(quotedProduct) &&
            selectedProduct.quantity === quotedProduct.quantity;
          if (matchesProductInQuote) {
            productsRevertedToQuote.push({
              lastQuoted: quotedProduct,
              current: selectedProduct,
              priceDifference,
              currencyCode: currency,
            });
          } else if (priceDifference > 0 || selectedProduct.quantity > currentSolutionProduct.quantity) {
            priceIncreasedProducts.push({
              lastQuoted: quotedProduct,
              current: selectedProduct,
              priceDifference,
              currencyCode: currency,
            });
          } else if (priceDifference < 0 || selectedProduct.quantity < currentSolutionProduct.quantity) {
            priceReducedProducts.push({
              lastQuoted: quotedProduct,
              current: selectedProduct,
              priceDifference,
              currencyCode: currency,
            });
          }
        } else {
          // The product is not changed from the current solution, but has changed since the quote. It is an old change, that has already been handled by the user.
          const previouslyHandledChange =
            !quotedProduct ||
            getProductId(currentSolutionProduct) !== getProductId(quotedProduct) ||
            currentSolutionProduct.quantity !== quotedProduct?.quantity;
          if (previouslyHandledChange) {
            const priceDifference = getProductPrice(currentSolutionProduct) - getProductPrice(quotedProduct);
            handledProducts.push({
              lastQuoted: quotedProduct,
              current: currentSolutionProduct,
              priceDifference,
              currencyCode: currency,
            });
          }
        }
      } else if (selectedProduct.quantity > 0) {
        // Everything in selectedProducts that does not exist in currentSolution is either newly added or reverted to what is in the quote.
        // If the quantity matches the quote, it is reverted
        if (selectedProduct.quantity === quotedProduct?.quantity) {
          productsRevertedToQuote.push({
            lastQuoted: quotedProduct,
            current: quotedProduct,
            priceDifference: 0,
            currencyCode: currency,
          });
        } else {
          addedProducts.push({
            current: selectedProduct,
            lastQuoted: undefined,
            priceDifference: getProductPrice(selectedProduct),
            currencyCode: currency,
          });
        }
      }
    }

    // To find removals, we need to compare from the quote forward. If it exists in the quote but not in the selectedProducts, then it has been removed at some point.
    // If it does exist in the current solution - it is a new removal that needs to be handled now. If it does not, it is an old change.
    // The price check is to exlude ghost items etc in the original quote.
    for (const quotedProduct of quotedProducts) {
      const lastQuotedCategory = getProductCategory(quotedProduct);
      const selectedProduct = findProductInCategory(selectedProducts, lastQuotedCategory);
      const currentSolutionProduct = findProductInCategory(currentSolutionProducts, lastQuotedCategory);

      const price = getProductPrice(quotedProduct);
      if (!selectedProduct && price > 0) {
        if (currentSolutionProduct) {
          removedProducts.push({
            lastQuoted: quotedProduct,
            current: undefined,
            priceDifference: -1 * getProductPrice(quotedProduct),
            currencyCode: currency,
          });
        } else {
          handledProducts.push({
            lastQuoted: quotedProduct,
            current: undefined,
            priceDifference: -1 * getProductPrice(quotedProduct),
            currencyCode: currency,
          });
        }
      }
    }

    return {
      removedProducts,
      priceIncreasedProducts,
      priceReducedProducts,
      addedProducts,
      revertedProducts: productsRevertedToQuote,
      handledProducts,
      unhandledChanges:
        removedProducts.length +
          priceIncreasedProducts.length +
          priceReducedProducts.length +
          addedProducts.length +
          productsRevertedToQuote.length >
        0,
    };
  }, [
    currency,
    selectedAddonsWithListPrice,
    selectedEvChargerPackage,
    selectedHeatPumpPackages,
    selectedInstallationAddonsPackages,
    selectedMiscellaneous,
    selectedSolarPackage,
    solutionData.data?.lastQuotedSolution?.presentation?.products,
    solutionData.data?.currentSolution?.presentation?.products,
  ]);

  /**
   * Updates the locked price override for products that have a reduced price.
   * Additionally lets the user pass in SKUs of products that should also have their locked price overridden.
   * This is useful for example in the product price selection (or 1-accept) modal where the user can manually
   * select products to override the locked price.
   * @param additionalProductSkusToUpdate - Array of product SKUs to update the locked price override for.
   * If not provided, only products with a reduced price will be updated.
   * @returns void
   */
  const updateLockedPriceOverride = useCallback(
    (additionalProductIdsToUpdate: string[] = []) => {
      const newSelectedAddons = cloneDeep(selectedAddons);
      const newSelectedHeatPumpPackages = cloneDeep(selectedHeatPumpPackages);
      const newInstallationAddonsPackages = cloneDeep(selectedInstallationAddonsPackages);
      const newSelectedEvChargerPackage = cloneDeep(selectedEvChargerPackage);
      const newSelectedMiscellaneous = cloneDeep(selectedMiscellaneous);
      const newSelectedSolarPackage = cloneDeep(selectedSolarPackage);
      let areProductsChanged = false;
      const idForProductsWithReducedPrice = [...productChanges.priceReducedProducts, ...productChanges.revertedProducts] //should reverted products be included here i think yes
        .map((product) => (product.current ? getProductId(product.current) : undefined))
        .filter(isNotNullish);
      // We go over all the products where the override flag was set to true (reduced price products + whatever is given to the function)
      // and find the matching items in our stores, and set the flag of those to true + update the stores
      for (const id of Array.from(new Set([...additionalProductIdsToUpdate, ...idForProductsWithReducedPrice]))) {
        if (
          newSelectedEvChargerPackage &&
          getProductId(newSelectedEvChargerPackage) === id &&
          !newSelectedEvChargerPackage.overrideLockedPrice
        ) {
          newSelectedEvChargerPackage.overrideLockedPrice = true;
          areProductsChanged = true;
        }
        if (
          newSelectedSolarPackage &&
          getProductId(newSelectedSolarPackage) === id &&
          !newSelectedSolarPackage.overrideLockedPrice
        ) {
          newSelectedSolarPackage.overrideLockedPrice = true;
          areProductsChanged = true;
        }
        newSelectedMiscellaneous.forEach((packageItem) => {
          if (getProductId(packageItem) === id && !packageItem.overrideLockedPrice) {
            packageItem.overrideLockedPrice = true;
            areProductsChanged = true;
          }
        });
        newSelectedAddons.forEach((packageItem) => {
          if (getProductId(packageItem) === id && !packageItem.overrideLockedPrice) {
            packageItem.overrideLockedPrice = true;
            areProductsChanged = true;
          }
        });
        for (const key in newSelectedHeatPumpPackages) {
          const packageItems = newSelectedHeatPumpPackages[key as keyof HeatPumpPackages];

          const index = packageItems?.findIndex((p) => {
            return getProductId(p) === id;
          });
          if (isNotNullish(index) && index > -1) {
            const packageItem = newSelectedHeatPumpPackages[key as keyof HeatPumpPackages]![index]!;
            if (!packageItem.overrideLockedPrice) {
              packageItem.overrideLockedPrice = true;
              areProductsChanged = true;
            }
          }
        }
        for (const key in newInstallationAddonsPackages) {
          const packageItems = newInstallationAddonsPackages[key as keyof InstallationAddonsPackages];
          const index = packageItems?.findIndex((p) => {
            return getProductId(p) === id;
          });
          if (isNotNullish(index) && index > -1) {
            const packageItem = newInstallationAddonsPackages[key as keyof InstallationAddonsPackages]![index]!;
            if (!packageItem.overrideLockedPrice) {
              packageItem.overrideLockedPrice = true;
              areProductsChanged = true;
            }
          }
        }
        if (areProductsChanged) {
          setSelectedInstallationAddonsPackages(newInstallationAddonsPackages);
          setSelectedHeatPumpPackages(newSelectedHeatPumpPackages);
          setSelectedAddons(newSelectedAddons);
          setSelectedEvChargerPackage(newSelectedEvChargerPackage);
          setSelectedMiscellaneous(newSelectedMiscellaneous);
          setSelectedSolarPackage(newSelectedSolarPackage);
        }
      }
    },
    [
      productChanges.priceReducedProducts,
      productChanges.revertedProducts,
      selectedAddons,
      selectedEvChargerPackage,
      selectedHeatPumpPackages,
      selectedInstallationAddonsPackages,
      selectedMiscellaneous,
      selectedSolarPackage,
      setSelectedAddons,
      setSelectedEvChargerPackage,
      setSelectedHeatPumpPackages,
      setSelectedInstallationAddonsPackages,
      setSelectedMiscellaneous,
      setSelectedSolarPackage,
    ],
  );
  return useMemo(() => {
    return {
      productChanges,
      updateLockedPriceOverride,
      quoteMinorAmountExcludingTax: lastQuotedPriceExclVat,
      updatedMinorAmountExcludingTax: updatedPriceExclVat,
      currencyCode: currency,
      hasLockedPriceAdjustment,
    };
  }, [
    lastQuotedPriceExclVat,
    productChanges,
    updateLockedPriceOverride,
    updatedPriceExclVat,
    currency,
    hasLockedPriceAdjustment,
  ]) as ProductSelectionDifference;
}
