import { afterEach, beforeAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders, setSwitchToValue, sleep, trpcMsw } from '../../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { GroundworkContextProvider } from '../../../../context/groundwork-context';
import { ProductSelectionPriceAdjustModal } from './ProductSelectionPriceAdjustModal';
import { cleanup, screen, waitFor } from '@testing-library/react';
import { setupWorker } from 'msw/browser';
import { mockGetServerEnvironment, mocks } from '../../../../tests/utils/mockedTrpcCalls';
import { GERMAN_HOUSE_GROUNDWORK } from '../../../../tests/heat-loss/fixtures/germanHouseFixtures';
import { HeatPumpPackages, useHeatPumpStore } from '../../../quotation/stores/HeatPumpPackageStore';
import { useInstallationAddonsStore } from '../../../quotation/stores/InstallationAddonsStore';
import { useAddonsStore } from '../../../quotation/stores/AddonsStore';
import { useEffect, useState } from 'react';
import { createRouterMock, resetRouterMock, useRouter } from '@mocks/next/router';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
const worker = setupWorker(
  mockGetServerEnvironment(),
  mocks.energySolutionDiff,
  trpcMsw.AiraBackend.getGroundworkForSolution.query(() => Promise.resolve(GERMAN_HOUSE_GROUNDWORK)),
  trpcMsw.AiraBackend.patchEnergySolution.mutation(vi.fn()),
);

const OUTDOOR_UNIT_6KW = {
  id: {
    value: '0e7fe7ff-ccaf-496c-960a-1c0072e78264',
  },
  sku: '100075',
  iso3166: {
    $case: 'country',
    country: 3,
  },
  price: 323400,
  displayName: 'Aira 6kW',
  details: {
    details: {
      $case: 'heatPumpOutdoorUnit',
      heatPumpOutdoorUnit: {
        effect: 6,
      },
    },
  },
  pricingMode: 1,
  productMode: 1,
  compatibilityGroup: {
    id: {
      value: '3ad04d34-ea88-4369-9039-d952bf4090d4',
    },
    name: 'Aira',
  },
  technicalSpecificationId: {
    value: '040d1bc7-781b-4dd0-b975-2189cc518b0d',
  },
  quantity: 1,
  productId: {
    value: '0e7fe7ff-ccaf-496c-960a-1c0072e78264',
  },
  effect: 6,
} as const;

const OUTDOOR_UNIT_12KW = {
  id: {
    value: '8f458955-16d5-4c93-bff1-d463b24f3af1',
  },
  sku: '100077',
  iso3166: {
    $case: 'country',
    country: 3,
  },
  price: 461500,
  displayName: 'Aira 12kW',
  details: {
    details: {
      $case: 'heatPumpOutdoorUnit',
      heatPumpOutdoorUnit: {
        effect: 12,
      },
    },
  },
  pricingMode: 1,
  productMode: 1,
  compatibilityGroup: {
    id: {
      value: '3ad04d34-ea88-4369-9039-d952bf4090d4',
    },
    name: 'Aira',
  },
  technicalSpecificationId: {
    value: '490bf80c-672d-49b2-b368-f3bd394254b6',
  },
  quantity: 1,
  productId: {
    value: '8f458955-16d5-4c93-bff1-d463b24f3af1',
  },
  effect: 12,
} as const;

type SelectedState = { selectedHeatPumpPackages: HeatPumpPackages };

const ProductSelectionPriceAdjustModalWrapper = ({ selectedState }: { selectedState?: SelectedState }) => {
  const { setSelectedHeatPumpPackages } = useHeatPumpStore();
  const { setSelectedInstallationAddonsPackages } = useInstallationAddonsStore();
  const { setSelectedAddons } = useAddonsStore();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (selectedState) {
      setSelectedHeatPumpPackages(selectedState.selectedHeatPumpPackages);
    }
    setIsLoaded(true);
  }, [
    setSelectedAddons,
    setSelectedHeatPumpPackages,
    setSelectedInstallationAddonsPackages,
    setIsLoaded,
    selectedState,
  ]);
  return isLoaded ? <ProductSelectionPriceAdjustModal onClose={() => {}} onSubmit={() => {}} isOpen={true} /> : <></>;
};
describe('ProductSelectionPriceAdjustModal', () => {
  const renderProductSelectionPriceAdjustModal = async (selectedState?: SelectedState) => {
    renderWithProviders(
      <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
        <GroundworkContextProvider solutionId={SOLUTION_ID}>
          <ProductSelectionPriceAdjustModalWrapper selectedState={selectedState} />
        </GroundworkContextProvider>
      </IntlProvider>,
    );
    await sleep(100); // Kind of clunky, but on the first render some data isn't loaded yet from MSW/TRPC, so we need to wait a bit
    await waitFor(
      () => {
        expect(screen.getByTestId('product-selection-price-adjust-modal')).toBeInTheDocument();
      },
      { timeout: 5000 },
    );
  };
  beforeEach(async () => {
    useRouter.mockImplementation(() => ({
      ...createRouterMock(),
      pathname: '/heat-design',
      query: {
        country: 'DE',
        solution: SOLUTION_ID, // Ensure solution is always present for tests
      },
      push: vi.fn(),
    }));
  });
  beforeAll(async () => {
    await worker.start({
      onUnhandledRequest: (req, print) => {
        if (req.url.includes('/fonts/')) {
          return;
        }
        print.warning();
      },
      quiet: true,
    });
  });

  afterAll(() => {
    worker.stop();
  });

  afterEach(() => {
    worker.resetHandlers();
    cleanup();
    vi.clearAllMocks();
    resetRouterMock();
  });

  it('changing to more expensive product renders it as a price increase', async () => {
    await renderProductSelectionPriceAdjustModal({
      selectedHeatPumpPackages: {
        heatPumpOutdoorUnit: [OUTDOOR_UNIT_12KW],
      },
    });

    const priceIncreaseSection = screen.getByTestId('price-increased-section');
    const rows = screen.getAllByTestId(/product-price-adjust-row/);

    expect(priceIncreaseSection).toBeInTheDocument();
    expect(rows.length).toBe(1);
  });

  it('should update total when toggling override switch', async () => {
    await renderProductSelectionPriceAdjustModal({
      selectedHeatPumpPackages: {
        heatPumpOutdoorUnit: [OUTDOOR_UNIT_12KW],
      },
    });

    const originalUpdatedTotal = await screen.findByTestId('updated-total');
    expect(originalUpdatedTotal).toHaveTextContent('3,613');
    const productRows = await screen.findAllByTestId(/product-price-adjust-row/);
    await setSwitchToValue('product-price-adjust-switch', true, productRows[1]);
    await waitFor(async () => {
      const newUpdatedTotal = await screen.findByTestId('updated-total');
      expect(newUpdatedTotal).toHaveTextContent('4,615');
    });
  });

  it('changing to cheaper product renders it as a price decrease', async () => {
    await renderProductSelectionPriceAdjustModal({
      selectedHeatPumpPackages: {
        heatPumpOutdoorUnit: [OUTDOOR_UNIT_6KW],
      },
    });

    const priceDecreaseSection = screen.getByTestId('price-decrease-section');
    expect(priceDecreaseSection).toBeInTheDocument();
    const rows = screen.getAllByTestId(/product-price-adjust-row/);
    expect(rows.length).toBe(1);

    // Price decrease should be automatically applied
    const newUpdatedTotal = await screen.findByTestId('updated-total');
    expect(newUpdatedTotal).toHaveTextContent('3,234');
  });

  it('should not render switch for reduced price products', async () => {
    await renderProductSelectionPriceAdjustModal({
      selectedHeatPumpPackages: {
        heatPumpOutdoorUnit: [OUTDOOR_UNIT_6KW],
      },
    });
    const rows = screen.getAllByTestId(/product-price-adjust-row/);
    const switches = screen.queryAllByTestId('product-price-adjust-switch');
    expect(rows.length).toBe(1);
    expect(switches.length).toBe(0);
  });
});
