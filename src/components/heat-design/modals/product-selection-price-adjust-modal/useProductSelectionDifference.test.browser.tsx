import React from 'react';
import { afterAll, afterEach, beforeAll, describe, expect, it, vi } from 'vitest';
import { cleanup, screen, waitFor } from '@testing-library/react';

import { useProductSelectionDifference } from './useProductSelectionDifference';
import { mocks, testEnergySolutionDiffData } from '../../../../tests/utils/mockedTrpcCalls';
import { setupWorker } from 'msw/browser';
import { resetRouterMock } from '@mocks/next/router';
import { renderWithProviders, trpcMsw } from '../../../../tests/utils/testUtils';
import { HeatPumpOutdoorUnit, PackageItem } from '../../../../types/types';
import { EnergySolutionProduct } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';
import { InstallationAddonsPackages } from '../../../quotation/stores/InstallationAddonsStore';
import { HeatPumpPackages } from '../../../quotation/stores/HeatPumpPackageStore';
import { isNotNullish } from '../../../../utils/isNotNullish';

vi.mock('../../../../hooks/useEnergySolution', () => ({
  useEnergySolutionId: () => 'energy-solution-id',
}));

vi.mock('../../../../utils/stores/marketStore', () => ({
  useMarketStore: () => ({ currency: 'GBP' }),
}));

vi.mock('../../../quotation/stores/HeatPumpPackageStore', () => ({
  useHeatPumpStore: () => ({
    selectedHeatPumpPackages: storeMocks.heatPumpPackages,
    setSelectedHeatPumpPackages: vi.fn((next) => {
      storeMocks.heatPumpPackages = next;
    }),
  }),
  HeatPumpPackages: {} as any, // keep TS happy where the type is imported
}));

vi.mock('../../../quotation/stores/InstallationAddonsStore', () => ({
  useInstallationAddonsStore: () => ({
    selectedInstallationAddonsPackages: storeMocks.installationAddonsPackages,
    setSelectedInstallationAddonsPackages: vi.fn((next) => {
      storeMocks.installationAddonsPackages = next;
    }),
  }),
}));

vi.mock('../../../quotation/stores/MiscellaneousStore', () => ({
  useMiscellaneousStore: () => ({
    selectedMiscellaneous: storeMocks.miscellaneousProducts ?? [],
    setSelectedMiscellaneous: vi.fn((next) => {
      storeMocks.miscellaneousProducts = next;
    }),
  }),
}));

vi.mock('../../../quotation/stores/EvChargerStore', () => ({
  useEvChargerStore: () => ({
    selectedEvChargerPackage: (storeMocks.evChargerProducts ?? [])[0],
    setSelectedEvChargerPackage: vi.fn((next) => {
      storeMocks.evChargerProducts = next ? [next] : [];
    }),
  }),
}));

vi.mock('../../../quotation/stores/SolarStore', () => ({
  useSolarAndBatteryStore: () => ({
    selectedSolarPackage: (storeMocks.solarProducts ?? [])[0],
    setSelectedSolarPackage: vi.fn((next) => {
      storeMocks.solarProducts = next ? [next] : [];
    }),
  }),
}));

vi.mock('../../../quotation/stores/AddonsStore', () => ({
  useAddonsStore: () => ({
    selectedAddons: storeMocks.addonProducts ?? [],
    setSelectedAddons: vi.fn((next) => {
      storeMocks.addonProducts = next;
    }),
    getSelectedAddonsWithListPrice: () => storeMocks.addonProducts ?? [],
  }),
}));

const storeMocks = {
  heatPumpPackages: {} as Partial<HeatPumpPackages>,

  installationAddonsPackages: {} as InstallationAddonsPackages,

  addonProducts: [] as PackageItem[],

  miscellaneousProducts: [] as PackageItem[],

  evChargerProducts: [] as PackageItem[],

  solarProducts: [] as PackageItem[],
};

const server = setupWorker(mocks.energySolutionDiff);

beforeAll(async () => {
  await server.start();
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});

afterAll(() => {
  server.stop();
});

function setupTestData(options: {
  addonProducts?: PackageItem[];
  heatPumpPackages?: Partial<HeatPumpPackages>;
  installationAddonsPackages?: InstallationAddonsPackages;
  miscellaneousProducts?: PackageItem[];
  evChargerProducts?: PackageItem[]; // or a single item depending on your store semantics
  solarProducts?: PackageItem[]; // or a single item depending on your store semantics
  lastQuoteProducts?: EnergySolutionProduct[] | null | undefined;
  currentSolutionProducts?: EnergySolutionProduct[] | null | undefined;
}) {
  // 1) Update store state (simple reference mutation, mocks read from this)
  if (options.heatPumpPackages !== undefined) {
    storeMocks.heatPumpPackages = options.heatPumpPackages;
  }
  if (options.installationAddonsPackages !== undefined) {
    storeMocks.installationAddonsPackages = options.installationAddonsPackages;
  }
  if (options.addonProducts !== undefined) {
    storeMocks.addonProducts = options.addonProducts;
  }
  if (options.miscellaneousProducts) {
    storeMocks.miscellaneousProducts = options.miscellaneousProducts;
  }
  if (options.evChargerProducts) {
    storeMocks.evChargerProducts = options.evChargerProducts;
  }
  if (options.solarProducts) {
    storeMocks.solarProducts = options.solarProducts;
  }

  // 2) Build TRPC response override
  const energySolutionDiffData = testEnergySolutionDiffData;
  if (isNotNullish(energySolutionDiffData.currentSolution?.presentation)) {
    energySolutionDiffData.currentSolution.presentation.products =
      options.currentSolutionProducts ?? testEnergySolutionDiffData.currentSolution?.presentation?.products ?? [];
  }
  if (isNotNullish(energySolutionDiffData.lastQuotedSolution?.presentation)) {
    energySolutionDiffData.lastQuotedSolution.presentation.products =
      options.lastQuoteProducts ?? testEnergySolutionDiffData.lastQuotedSolution?.presentation?.products ?? [];
  }

  // 3) Ensure the MSW handler returns our tailor-made payload
  server.use(trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => energySolutionDiffData));
}

function HookHarness() {
  const { productChanges } = useProductSelectionDifference();
  return (
    <>
      <div data-testid="increases">{productChanges.priceIncreasedProducts.length}</div>
      <div data-testid="reductions">{productChanges.priceReducedProducts.length}</div>
      <div data-testid="added">{productChanges.addedProducts.length}</div>
      <div data-testid="removed">{productChanges.removedProducts.length}</div>
      <div data-testid="reverted">{productChanges.revertedProducts.length}</div>
    </>
  );
}

const MOCK_RADIATOR: PackageItem = {
  productId: {
    value: 'c535afd9-c071-4225-a1b5-8c0c2f4a9d20',
  },
  sku: '102420',
  displayName: 'Radiator (Standard)',
  quantity: 3,
  price: 19500,
  productMode: 1,
  details: {
    details: {
      $case: 'addon',
      addon: {
        category: 'RADIATOR',
      },
    },
  },
  pricingMode: 0,
};

const MOCK_8KW_OUTDOOR_UNIT: HeatPumpOutdoorUnit = {
  productId: {
    value: 'b41085b1-bde7-4add-806d-f0742b5aa2e8',
  },
  sku: '100076',
  quantity: 1,
  price: 361300,
  displayName: 'Aira 8kW',
  details: {
    details: {
      $case: 'heatPumpOutdoorUnit',
      heatPumpOutdoorUnit: {
        effect: 8,
      },
    },
  },
  productMode: 1,
  compatibilityGroup: {
    id: {
      value: '3ad04d34-ea88-4369-9039-d952bf4090d4',
    },
    name: 'Aira',
  },
  technicalSpecificationId: {
    value: 'acce4ca7-d856-4028-a73e-b6a430c1e4a3',
  },
  pricingMode: 0,
};
const MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT: EnergySolutionProduct = {
  ...MOCK_8KW_OUTDOOR_UNIT,
  details: undefined,
  productDetails: {
    details: {
      $case: 'heatPumpOutdoorUnit',
      heatPumpOutdoorUnit: {
        effect: 8,
      },
    },
  },
  price: {
    currencyCode: 'GBP',
    minorAmount: 361300,
    taxDetails: {
      minorAmountExcludingTax: 361300,
      taxMinorAmount: 0,
      taxRate: 0,
    },
  },
  productMode: 1,
};
const MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR: EnergySolutionProduct = {
  ...MOCK_RADIATOR,
  details: undefined,
  productDetails: {
    details: {
      $case: 'addon',
      addon: {
        category: 'RADIATOR',
      },
    },
  },
  price: {
    currencyCode: 'GBP',
    minorAmount: 19500,
    taxDetails: {
      minorAmountExcludingTax: 19500,
      taxMinorAmount: 0,
      taxRate: 0,
    },
  },
  productMode: 1,
};
describe('useProductSelectionDifference', async () => {
  it('should detect unhandled changes if radiator quantities increase', async () => {
    setupTestData({
      addonProducts: [{ ...MOCK_RADIATOR, quantity: MOCK_RADIATOR.quantity + 1 }],
      heatPumpPackages: {
        heatPumpOutdoorUnit: [MOCK_8KW_OUTDOOR_UNIT],
      },
      lastQuoteProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR],
      currentSolutionProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, { ...MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR }],
    });
    renderWithProviders(<HookHarness />);

    await waitFor(
      async () => {
        expect(screen.queryByTestId('increases')?.textContent).toBe('1');
        expect(screen.queryByTestId('reductions')?.textContent).toBe('0');
        expect(screen.queryByTestId('added')?.textContent).toBe('0');
        expect(screen.queryByTestId('removed')?.textContent).toBe('0');
        expect(screen.queryByTestId('reverted')?.textContent).toBe('0');
      },
      {
        timeout: 5000,
      },
    );
  });
  it('should detect removed products', async () => {
    setupTestData({
      addonProducts: [{ ...MOCK_RADIATOR }],
      heatPumpPackages: {
        heatPumpOutdoorUnit: [],
      },
      lastQuoteProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR],
      currentSolutionProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, { ...MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR }],
    });
    renderWithProviders(<HookHarness />);

    await waitFor(async () => {
      expect(screen.queryByTestId('increases')?.textContent).toBe('0');
      expect(screen.queryByTestId('reductions')?.textContent).toBe('0');
      expect(screen.queryByTestId('added')?.textContent).toBe('0');
      expect(screen.queryByTestId('removed')?.textContent).toBe('1');
      expect(screen.queryByTestId('reverted')?.textContent).toBe('0');
    });
  });

  it('should detect added products', async () => {
    setupTestData({
      addonProducts: [{ ...MOCK_RADIATOR }],
      heatPumpPackages: {
        heatPumpOutdoorUnit: [MOCK_8KW_OUTDOOR_UNIT],
      },
      lastQuoteProducts: [MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR],
      currentSolutionProducts: [{ ...MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR }],
    });
    // Re-render the hook with new data
    renderWithProviders(<HookHarness />);

    await waitFor(async () => {
      expect(screen.queryByTestId('increases')?.textContent).toBe('0');
      expect(screen.queryByTestId('reductions')?.textContent).toBe('0');
      expect(screen.queryByTestId('added')?.textContent).toBe('1');
      expect(screen.queryByTestId('removed')?.textContent).toBe('0');
      expect(screen.queryByTestId('reverted')?.textContent).toBe('0');
    });
  });

  it('should detect unhandled changes if radiator quantities decrease', async () => {
    setupTestData({
      addonProducts: [{ ...MOCK_RADIATOR, quantity: MOCK_RADIATOR.quantity - 1 }],
      heatPumpPackages: {
        heatPumpOutdoorUnit: [MOCK_8KW_OUTDOOR_UNIT],
      },
      lastQuoteProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR],
      currentSolutionProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, { ...MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR }],
    });
    renderWithProviders(<HookHarness />);

    await waitFor(async () => {
      expect(screen.queryByTestId('increases')?.textContent).toBe('0');
      expect(screen.queryByTestId('reductions')?.textContent).toBe('1');
      expect(screen.queryByTestId('added')?.textContent).toBe('0');
      expect(screen.queryByTestId('removed')?.textContent).toBe('0');
      expect(screen.queryByTestId('reverted')?.textContent).toBe('0');
    });
  });

  it('should detect reverted changes', async () => {
    setupTestData({
      addonProducts: [{ ...MOCK_RADIATOR }],
      heatPumpPackages: {
        heatPumpOutdoorUnit: [MOCK_8KW_OUTDOOR_UNIT],
      },
      lastQuoteProducts: [MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT, MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR],
      currentSolutionProducts: [
        MOCK_ENERGY_SOLUTION_8KW_OUTDOOR_UNIT,
        { ...MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR, quantity: MOCK_ENERGY_SOLUTION_STANDARD_RADIATOR.quantity + 1 },
      ],
    });
    renderWithProviders(<HookHarness />);

    await waitFor(async () => {
      expect(screen.queryByTestId('increases')?.textContent).toBe('0');
      expect(screen.queryByTestId('reductions')?.textContent).toBe('0');
      expect(screen.queryByTestId('added')?.textContent).toBe('0');
      expect(screen.queryByTestId('removed')?.textContent).toBe('0');
      expect(screen.queryByTestId('reverted')?.textContent).toBe('1');
    });
  });
});
