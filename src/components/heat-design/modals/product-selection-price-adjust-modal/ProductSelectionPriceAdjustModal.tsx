import { Modal } from '@ui/components/Modal/Modal';
import { Divider, IconButton, Link, Stack, Typography } from '@mui/material';
import { brandYellow, grey, red, surface } from '@ui/theme/colors';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import { useEnergySolutionId } from '../../../../hooks/useEnergySolution';
import React, { useMemo, useState } from 'react';
import { CheckOutlinedIcon } from '@ui/components/StandardIcons/CheckOutlinedIcon';
import { getProductId, useProductSelectionDifference } from './useProductSelectionDifference';
import { useHeatPumpStore } from '../../../quotation/stores/HeatPumpPackageStore';
import { useAddonsStore } from '../../../quotation/stores/AddonsStore';
import { useInstallationAddonsStore } from '../../../quotation/stores/InstallationAddonsStore';
import { keyIsNotNullish } from '../../../../utils/isNotNullish';
import { PackageItem } from '../../../../types/types';
import { ProductSelectionPriceAdjustRow } from './ProductSelectionPriceAdjustRow';
import { useRouter } from 'next/router';
import { green } from '@mui/material/colors';
import { FormattedMessage, useIntl } from 'react-intl';
import { WarningIcon } from 'components/heat-design/components/WarningIcon';

export function ProductSelectionPriceAdjustModal({
  onClose,
  isOpen,
  onSubmit,
}: {
  onClose: () => void;
  isOpen: boolean;
  onSubmit: () => void;
}) {
  const { locale } = useRouter();
  const { formatMessage } = useIntl();
  const productSelectionDifference = useProductSelectionDifference();
  const energySolutionId = useEnergySolutionId();
  const { selectedHeatPumpPackages } = useHeatPumpStore();
  const { selectedInstallationAddonsPackages } = useInstallationAddonsStore();
  const { selectedAddons } = useAddonsStore();
  const hpProducts: PackageItem[] = Object.values(selectedHeatPumpPackages).flat();
  const installationAddonsProducts: PackageItem[] = Object.values(selectedInstallationAddonsPackages).flat();
  const allProducts = [...hpProducts, ...installationAddonsProducts, ...selectedAddons];
  const [productsWithOverriddenPrice, setProductsWithOverriddenPrice] = useState<Set<string>>(
    new Set(
      allProducts
        .filter((product) => {
          return product.overrideLockedPrice;
        })
        .map((product) => {
          return getProductId(product);
        }),
    ),
  );

  const totals = useMemo(() => {
    const currencyCode = productSelectionDifference.currencyCode;
    const initialSum = productSelectionDifference.quoteMinorAmountExcludingTax ?? 0;
    let totalProductDifference = 0;
    [
      ...productSelectionDifference.productChanges.priceIncreasedProducts,
      ...productSelectionDifference.productChanges.addedProducts,
    ].forEach((product) => {
      // Calculate updated sum based on switch state
      const currentProduct = product.current;

      const groupId = currentProduct ? getProductId(currentProduct) : undefined;
      if (groupId && productsWithOverriddenPrice.has(groupId)) {
        // Use current prices for this group
        totalProductDifference += product.priceDifference;
      }
    });

    [
      ...productSelectionDifference.productChanges.priceReducedProducts,
      ...productSelectionDifference.productChanges.removedProducts,
      ...productSelectionDifference.productChanges.handledProducts,
    ].forEach((product) => {
      // Calculate updated sum based on switch state
      totalProductDifference += product.priceDifference;
    });

    const updatedSolutionSum = productSelectionDifference.updatedMinorAmountExcludingTax + totalProductDifference;

    if (!currencyCode) {
      return undefined;
    }
    const totalDiscount = [
      ...productSelectionDifference.productChanges.priceIncreasedProducts,
      ...productSelectionDifference.productChanges.addedProducts,
    ].reduce((acc, product) => {
      const currentProduct = product.current;
      const groupId = currentProduct ? getProductId(currentProduct) : undefined;
      if (groupId && !productsWithOverriddenPrice.has(groupId)) {
        acc += product.priceDifference;
      }
      return acc;
    }, 0);
    return {
      initialTotal: new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        maximumFractionDigits: 0,
      }).format((initialSum ?? 0) / 100),
      updatedTotal: new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        maximumFractionDigits: 0,
      }).format((updatedSolutionSum ?? 0) / 100),
      totalDifference: totalProductDifference,
      formattedTotalDifference:
        (totalProductDifference >= 0 ? '+ ' : '- ') +
        new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: currencyCode,
          maximumFractionDigits: 0,
        }).format(Math.abs(totalProductDifference ?? 0) / 100),
      totalDiscount: new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        maximumFractionDigits: 0,
      }).format(Math.abs(totalDiscount ?? 0) / 100),
    };
  }, [
    locale,
    productSelectionDifference.currencyCode,
    productSelectionDifference.quoteMinorAmountExcludingTax,
    productSelectionDifference.updatedMinorAmountExcludingTax,
    productSelectionDifference.productChanges.addedProducts,
    productSelectionDifference.productChanges.priceIncreasedProducts,
    productSelectionDifference.productChanges.priceReducedProducts,
    productSelectionDifference.productChanges.removedProducts,
    productSelectionDifference.productChanges.handledProducts,
    productsWithOverriddenPrice,
  ]);

  const doSubmit = async () => {
    // Update override_locked_price flag for products with switches toggled ON
    const productsToUpdate = [
      ...productSelectionDifference.productChanges.priceIncreasedProducts,
      ...productSelectionDifference.productChanges.addedProducts,
    ]
      .filter(keyIsNotNullish('current'))
      .filter((product) => {
        const id = getProductId(product.current);
        return productsWithOverriddenPrice.has(id);
      })
      .map((product) => getProductId(product.current));
    productSelectionDifference.updateLockedPriceOverride(productsToUpdate);
    onSubmit();
  };

  const showPriceIncreasedSection =
    !!productSelectionDifference.productChanges.priceIncreasedProducts.length ||
    !!productSelectionDifference.productChanges.addedProducts.length;
  const showPriceReducedSection =
    !!productSelectionDifference.productChanges.priceReducedProducts.length ||
    !!productSelectionDifference.productChanges.removedProducts.length;
  const showRevertedProductsSection = !!productSelectionDifference.productChanges.revertedProducts.length;
  const showHandledProductsSection = !!productSelectionDifference.productChanges.handledProducts.length;

  return (
    <Modal
      submitOptions={{
        node: (
          <IconButton
            sx={{ backgroundColor: brandYellow[400], '&:hover': { backgroundColor: brandYellow[300] } }}
            onClick={() => doSubmit()}
            aria-label="Submit this dialog"
            data-testid="submit-modal"
          >
            <CheckOutlinedIcon color={grey[900]} />
          </IconButton>
        ),
      }}
      isModalOpen={isOpen}
      handleClose={onClose}
      height="90vh"
      width="1200px"
      showCloseButton
      heading={formatMessage({ id: 'product-price-adjustment.title' })}
      sx={{ p: 3, width: '1200px' }}
      data-testid="product-selection-price-adjust-modal"
    >
      <Stack gap={3} flex={1} sx={{ mt: 2, overflow: 'hidden', height: '100%', p: 1, overflowY: 'auto' }}>
        <Typography variant="body2">
          <FormattedMessage id="product-price-adjustment.subtitle" />
        </Typography>
        <Typography variant="body2">
          <b style={{ marginRight: '4px' }}>
            <FormattedMessage id="common.note.label" />:
          </b>
          <FormattedMessage id="product-price-adjustment.note" />
        </Typography>
        <Stack gap={3} justifyContent="space-between" flex={1} sx={{ minHeight: 0 }}>
          <Stack gap={1} sx={{ overflow: 'auto', flex: 1, minHeight: '200px' }}>
            <Stack direction="row" alignItems="center" sx={{ marginRight: '12px' }}>
              <Typography sx={{ flex: 1 }} variant="headline3" fontWeight={500}>
                <FormattedMessage id="product-price-adjustment.revised-products" />
              </Typography>
              {showPriceIncreasedSection && (
                <Typography sx={{ display: 'flex', justifyContent: 'flex-end' }} variant="body1Emphasis">
                  <FormattedMessage id="product-price-adjustment.adjust-price" />
                </Typography>
              )}
            </Stack>

            <Stack
              gap={2}
              sx={{
                overflow: 'auto',
                flex: 1,
                backgroundColor: surface[100],
                borderRadius: 2,
                padding: 2,
              }}
            >
              {showPriceIncreasedSection && (
                <>
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    data-testid="price-increased-section"
                  >
                    <Typography variant="headline4">
                      <FormattedMessage id="product-price-adjustment.products-with-price-increase" />
                    </Typography>

                    <Typography variant="body3">
                      <FormattedMessage id="product-price-adjustment.products-with-price-increase-note" />
                    </Typography>
                  </Stack>
                  {productSelectionDifference.productChanges.priceIncreasedProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                      />
                    );
                  })}
                  {productSelectionDifference.productChanges.addedProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                      />
                    );
                  })}
                  {(showPriceReducedSection || showRevertedProductsSection) && <Divider sx={{ my: 2 }} flexItem />}
                </>
              )}
              {showPriceReducedSection && (
                <>
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    data-testid="price-decrease-section"
                  >
                    <Typography variant="headline4">
                      <FormattedMessage id="product-price-adjustment.products-with-price-reduction" />
                    </Typography>

                    <Typography variant="body3">
                      <FormattedMessage id="product-price-adjustment.products-with-price-reduction-note" />
                    </Typography>
                  </Stack>
                  {productSelectionDifference.productChanges.priceReducedProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                        showSwitch={false}
                      />
                    );
                  })}
                  {productSelectionDifference.productChanges.removedProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                        showSwitch={false}
                      />
                    );
                  })}
                  {showRevertedProductsSection && <Divider sx={{ my: 2 }} flexItem />}
                </>
              )}
              {showRevertedProductsSection && (
                <>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Typography variant="headline4">
                      <FormattedMessage id="product-price-adjustment.reverted-products" />
                    </Typography>

                    <Typography variant="body3">
                      <FormattedMessage id="product-price-adjustment.reverted-products-note" />
                    </Typography>
                  </Stack>
                  {productSelectionDifference.productChanges.revertedProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                        showSwitch={false}
                      />
                    );
                  })}
                </>
              )}

              {showHandledProductsSection && <Divider sx={{ my: 2 }} flexItem />}
              {showHandledProductsSection && (
                <>
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Typography variant="headline4">
                      <FormattedMessage id="product-price-adjustment.handled-products" />
                    </Typography>

                    <Typography variant="body3">
                      <FormattedMessage id="product-price-adjustment.handled-products-note" />
                    </Typography>
                  </Stack>
                  {productSelectionDifference.productChanges.handledProducts.map((productGroup) => {
                    return (
                      <ProductSelectionPriceAdjustRow
                        key={productGroup.current?.sku ?? productGroup.lastQuoted?.sku}
                        productGroup={productGroup}
                        productsWithOverriddenPrice={productsWithOverriddenPrice}
                        setProductsWithOverriddenPrice={setProductsWithOverriddenPrice}
                        showSwitch={false}
                      />
                    );
                  })}
                </>
              )}
            </Stack>
          </Stack>
          {totals && (
            <Stack gap={3} sx={{ flexShrink: 0, p: 1 }}>
              <Stack direction="row" justifyContent="space-between" gap={5}>
                <LabelValueDisplay
                  sx={{ flex: '1 0 40%', borderRadius: 2, p: 2, height: '56px' }}
                  label={
                    <Stack direction="row" alignItems="center" gap={2}>
                      <Typography variant="body2Emphasis">
                        <FormattedMessage id="product-price-adjustment.initial-quote" />
                      </Typography>
                      <Link href={`/solution/${energySolutionId}/summary`} target="_blank">
                        <IconButton>
                          <ArrowRightTopSquareOutlinedIcon />
                        </IconButton>
                      </Link>
                    </Stack>
                  }
                  value={
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Typography variant="number1">{totals.initialTotal}</Typography>
                      <Typography variant="number3" fontSize="12px">
                        <FormattedMessage id="product-price-adjustment.excluding-vat" />
                      </Typography>
                    </Stack>
                  }
                />
                {productSelectionDifference.hasLockedPriceAdjustment ? (
                  <Stack
                    direction="row"
                    alignItems="center"
                    gap={1}
                    sx={{ flex: '1 1 60%', backgroundColor: '#fff', p: 2, borderRadius: 2, height: '56px' }}
                  >
                    <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />

                    <Typography variant="body2Emphasis">
                      Cannot show updated price due to previous locked price adjustment
                    </Typography>
                  </Stack>
                ) : (
                  <LabelValueDisplay
                    sx={{ flex: '1 1 60%', backgroundColor: '#fff', p: 2, borderRadius: 2, height: '56px' }}
                    label={
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography variant="body2Emphasis">
                          <FormattedMessage id="product-price-adjustment.updated-price" />
                        </Typography>
                        <Typography
                          variant="number3"
                          fontSize="12px"
                          color={totals.totalDifference < 0 ? green[700] : red[600]}
                        >
                          ({totals.formattedTotalDifference})
                        </Typography>
                      </Stack>
                    }
                    value={
                      <Stack direction="row" alignItems="center" gap={3}>
                        <Stack direction="row" alignItems="center" gap={1}>
                          <Typography data-testid="updated-total" variant="number1">
                            {totals.updatedTotal}
                          </Typography>
                          <Typography variant="number3" fontSize="12px">
                            <FormattedMessage id="product-price-adjustment.excluding-vat" />
                          </Typography>
                        </Stack>
                      </Stack>
                    }
                  />
                )}
              </Stack>
            </Stack>
          )}
        </Stack>
      </Stack>
    </Modal>
  );
}
