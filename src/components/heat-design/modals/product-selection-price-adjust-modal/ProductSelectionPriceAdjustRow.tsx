import React, { Dispatch, memo, SetStateAction } from 'react';
import { ArrowRightOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { Stack, Typography } from '@mui/material';
import { red, surface } from '@ui/theme/colors';
import { green } from '@mui/material/colors';
import { Switch } from '@ui/components/Switch/Switch';
import { useRouter } from 'next/router';
import { getProductId, getProductPrice, ProductSelectionDifferenceGroup } from './useProductSelectionDifference';
import { PackageItem } from '../../../../types/types';
import { EnergySolutionProduct } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';

export type ProductSelectionPriceAdjustRowProps = {
  productGroup: ProductSelectionDifferenceGroup;
  productsWithOverriddenPrice: Set<string>;
  setProductsWithOverriddenPrice: Dispatch<SetStateAction<Set<string>>>;
  showSwitch?: boolean;
};

function ProductSelectionPriceAdjustRowComponent({
  productGroup,
  productsWithOverriddenPrice,
  setProductsWithOverriddenPrice,
  showSwitch = true,
}: ProductSelectionPriceAdjustRowProps) {
  const { locale } = useRouter();

  const getChangeIcon = (
    lastProduct: PackageItem | EnergySolutionProduct | undefined,
    currentProduct: PackageItem | EnergySolutionProduct | undefined,
  ) => {
    if (lastProduct && currentProduct) {
      return <ArrowRightOutlinedIcon />;
    } else if (lastProduct && !currentProduct) {
      return <CrossOutlinedIcon />;
    } else {
      return <AddOutlinedIcon />;
    }
  };

  const currentProductId = productGroup.current ? getProductId(productGroup.current) : undefined;
  const lastQuotedProductId = productGroup.lastQuoted ? getProductId(productGroup.lastQuoted) : undefined;
  const isUsingCurrentPricing = currentProductId ? productsWithOverriddenPrice.has(currentProductId) : true;

  const getDisplayName = (product: PackageItem | EnergySolutionProduct) => {
    return product.displayName;
  };
  const toggleSwitch = (id: string) => {
    setProductsWithOverriddenPrice((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const lastProduct = productGroup.lastQuoted;
  const currentProduct = productGroup.current;
  const currencyCode = productGroup.currencyCode;
  const currentFormattedPrice = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    maximumFractionDigits: 0,
  }).format(getProductPrice(currentProduct) / 100);
  const lastFormattedPrice = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    maximumFractionDigits: 0,
  }).format(getProductPrice(lastProduct) / 100);
  const formattedPriceDifference = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
    signDisplay: 'always',
    maximumFractionDigits: 0,
  }).format(productGroup.priceDifference / 100);
  const key = currentProductId ? currentProductId : (lastQuotedProductId ?? '');

  const currentProductBadge = () => {
    if (currentProduct) {
      return (
        <Stack
          direction="row"
          alignItems="center"
          gap={2}
          sx={{
            padding: '4px 16px',
            borderRadius: '16px',
            backgroundColor: '#fff',
            height: '40px',
            flex: 1,
          }}
        >
          <Typography variant="body2">{currentProduct.quantity}</Typography>
          <Typography variant="body2" sx={{ flex: 1 }}>
            {getDisplayName(currentProduct)}
          </Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            {((currentProductId && productsWithOverriddenPrice.has(currentProductId)) ||
              (!showSwitch && productGroup.priceDifference !== 0)) && (
              <Typography variant="body3" color={productGroup.priceDifference < 0 ? green[700] : red[600]}>
                {formattedPriceDifference}
              </Typography>
            )}

            <Typography variant="body2Emphasis">{currentFormattedPrice}</Typography>
          </Stack>
        </Stack>
      );
    } else if (lastProduct) {
      return (
        <Stack
          direction="row"
          alignItems="center"
          gap={2}
          sx={{
            padding: '4px 16px',
            borderRadius: '16px',
            backgroundColor: '#fff',
            height: '40px',
            opacity: 0.5,
            flex: 1,
          }}
        >
          <Typography variant="body2">0</Typography>
          <Typography variant="body2" sx={{ flex: 1 }}>
            Removed product
          </Typography>
          <Stack direction="row" alignItems="center" gap={2}>
            {((lastQuotedProductId && productsWithOverriddenPrice.has(lastQuotedProductId)) ||
              productGroup.priceDifference < 0) && (
              <Typography variant="body3" color={productGroup.priceDifference < 0 ? green[700] : red[600]}>
                {formattedPriceDifference}
              </Typography>
            )}

            <Typography variant="body2Emphasis">{currentFormattedPrice}</Typography>
          </Stack>
        </Stack>
      );
    } else {
      return (
        <Stack
          direction="row"
          alignItems="center"
          gap={2}
          sx={{ padding: '4px 16px', borderRadius: '16px', flex: 1, width: '60px' }}
        />
      );
    }
  };
  return (
    <Stack
      data-testid={`product-price-adjust-row-${currentProductId ?? ''}-${isUsingCurrentPricing ? 'selected' : ''}`}
      gap={1}
      key={key}
      direction="row"
      width="100%"
      alignItems="center"
      sx={{ height: '40px' }}
    >
      <Stack sx={{ flex: 1 }} gap={2}>
        <Stack direction="row" justifyContent="space-between" gap={2} alignItems="center">
          {lastProduct ? (
            <Stack
              direction="row"
              alignItems="center"
              gap={2}
              sx={{
                padding: '4px 16px',
                borderRadius: '16px',
                backgroundColor: surface[100],
                flex: 1,
                height: '40px',
              }}
            >
              <Typography variant="body2">{lastProduct.quantity}</Typography>
              <Typography variant="body2" sx={{ flex: 1 }}>
                {getDisplayName(lastProduct)}
              </Typography>
              <Typography variant="body2">{lastFormattedPrice}</Typography>
            </Stack>
          ) : (
            <Stack
              direction="row"
              alignItems="center"
              gap={2}
              sx={{ padding: '4px 16px', borderRadius: '16px', flex: 1 }}
            />
          )}
          {getChangeIcon(lastProduct, currentProduct)}
          {currentProductBadge()}
        </Stack>
      </Stack>

      <Stack direction="row" justifyContent="flex-end" gap={2} alignItems="center" sx={{ width: '80px' }}>
        {showSwitch && !productGroup.current?.sku.startsWith('custom-') && currentProduct && (
          <Switch
            data-testid="product-price-adjust-switch"
            checked={isUsingCurrentPricing}
            onChange={() => toggleSwitch(getProductId(currentProduct))}
          />
        )}
      </Stack>
    </Stack>
  );
}

export const ProductSelectionPriceAdjustRow = memo(ProductSelectionPriceAdjustRowComponent);
