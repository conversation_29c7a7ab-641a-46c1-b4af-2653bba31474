import { Button, Stack, Typography } from '@mui/material';

import { getButtonStartIcon } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { Modal } from '@ui/components/Modal/Modal';
import { MessageKey } from 'messageType';
import { ReactNode } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { WarningIcon } from '../components/WarningIcon';
import { useFloors } from '../stores/FloorsStore';
import { useHeatDesignUIActions } from '../stores/HeatDesignUIStore';
import { useConstructionYear, useHeatDesignHouseInputsStore, useNumOccupants } from '../stores/HouseInputsStore';
import { RoomsByFloor, useRooms } from '../stores/RoomsStore';
import { FloorProps, Room } from '../stores/types';
import { useUValuesStore } from '../stores/UValuesStore';
import { projectUValuesDefaultsAreValid } from '../utils/helpers';
import { getInvalidFloors, getInvalidRooms, propertyIsValid, roomHeatSourcesAreValid } from '../Validator';

function ValidationModal({
  isOpen,
  onClose,
  heading,
  // For now, just allow the modal body to be customised for each caller. Ideally,
  // there should be some sort of standardisation between the validation modals.
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  heading: ReactNode;
  children: ReactNode;
}) {
  return (
    <Modal isModalOpen={isOpen} handleClose={onClose} height="auto" width="60vw" showCloseButton heading={heading}>
      {children}
    </Modal>
  );
}

function InvalidRoomsByFloorValidationModalBody({
  introTextKey,
  floors,
  invalidRoomsByFloor,
  handleClickRoom,
}: {
  introTextKey: MessageKey;
  floors: FloorProps[];
  invalidRoomsByFloor: RoomsByFloor;
  handleClickRoom: (room: Room) => void;
}) {
  const numberOfInvalidRooms = floors.reduce((acc, floor) => acc + (invalidRoomsByFloor[floor.uid]?.length ?? 0), 0);
  return (
    <>
      <Typography mb={2}>
        <FormattedMessage id={introTextKey} values={{ count: numberOfInvalidRooms }} />
      </Typography>
      <Stack gap={2}>
        {floors.map((floor) => {
          const floorRooms = invalidRoomsByFloor[floor.uid];
          if (!floorRooms) return null;
          return (
            <Stack key={floor.uid}>
              <Heading variant="headline3" level={2} mb={1}>
                {floor.floorName}
              </Heading>
              <Stack alignItems="start" gap={1} flexDirection="row" flexWrap="wrap">
                {floorRooms.map((room) => (
                  <Button
                    data-testid={`invalid-room-${room.name}`}
                    key={room.id}
                    variant="outlined"
                    size="small"
                    onClick={() => handleClickRoom(room)}
                    sx={{ minWidth: '20%' }}
                    startIcon={getButtonStartIcon(false)}
                  >
                    {room.name}
                  </Button>
                ))}
              </Stack>
            </Stack>
          );
        })}
      </Stack>
    </>
  );
}

export function RoomEmittersValidationModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const rooms = useRooms();
  const floors = useFloors();
  const heatDesignUIActions = useHeatDesignUIActions();

  const invalidRooms = rooms.filter((output) => !roomHeatSourcesAreValid(output));

  const invalidRoomsByFloor = invalidRooms.reduce((acc: RoomsByFloor, room) => {
    if (room !== undefined) {
      acc[room.floorId] = acc[room.floorId] ?? [];
      acc[room.floorId]?.push(room);
    }
    return acc;
  }, {});

  const handleClickRoom = (room: Room) => {
    heatDesignUIActions.selectRoom(room);
    heatDesignUIActions.closeModal();
  };

  return (
    <ValidationModal
      isOpen={isOpen}
      onClose={onClose}
      heading={<FormattedMessage id="heatDesign.emittersValidationModal.title" />}
      data-testid="validation-modal"
    >
      <InvalidRoomsByFloorValidationModalBody
        introTextKey="heatDesign.emittersValidationModal.body"
        floors={floors}
        invalidRoomsByFloor={invalidRoomsByFloor}
        handleClickRoom={handleClickRoom}
      />
    </ValidationModal>
  );
}

export function RoomValidationModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const rooms = useRooms();
  const floors = useFloors();
  const heatDesignUIActions = useHeatDesignUIActions();
  const uValuesStore = useUValuesStore();

  if (!isOpen) return null;

  const invalidFloors = getInvalidFloors(floors);
  const invalidRooms = getInvalidRooms(floors, rooms, uValuesStore.projectUValues);
  const invalidRoomsByFloor = invalidRooms.reduce((acc: RoomsByFloor, room) => {
    acc[room.floorId] = acc[room.floorId] ?? [];
    acc[room.floorId]?.push(room);
    return acc;
  }, {});

  const handleClickRoom = (room: Room) => {
    heatDesignUIActions.showRoomModal(room);
  };

  function handleClickFloor(floor: FloorProps) {
    heatDesignUIActions.showFloorDefaultsModal(floor.uid);
  }

  return (
    <ValidationModal
      isOpen={isOpen}
      onClose={onClose}
      heading={<FormattedMessage id="heatDesign.roomValidationModal.title" />}
    >
      <Stack>
        {invalidFloors.length > 0 && (
          <Stack mb={invalidFloors.length > 0 && invalidRooms.length > 0 ? 4 : 0}>
            <Typography mt={2}>
              <FormattedMessage
                id="heatDesign.floorOverviewValidationModal.invalidFloors"
                values={{ count: invalidFloors.length }}
              />
            </Typography>
            <Stack alignItems="start" gap={1} mt={1} flexDirection="row" flexWrap="wrap">
              {invalidFloors.map((floor) => (
                <Button
                  key={floor.uid}
                  variant="outlined"
                  startIcon={getButtonStartIcon(false)}
                  size="small"
                  sx={{ minWidth: '20%' }}
                  onClick={() => handleClickFloor(floor)}
                >
                  {floor.floorName}
                </Button>
              ))}
            </Stack>
          </Stack>
        )}
        {invalidRooms.length > 0 && (
          <InvalidRoomsByFloorValidationModalBody
            introTextKey="heatDesign.floorOverviewValidationModal.body"
            floors={floors}
            invalidRoomsByFloor={invalidRoomsByFloor}
            handleClickRoom={handleClickRoom}
          />
        )}
      </Stack>
    </ValidationModal>
  );
}

export type DwellingValidationErrors = {
  uValues: boolean;
  constructionYear: boolean;
  numberOfResidents: boolean;
  acphDefault: boolean;
  pulseTest: boolean;
};

const COPY_FOR_ERROR = {
  constructionYear: 'heatDesign.propertyDetails.YearBuilt',
  numberOfResidents: 'heatDesign.propertyDetails.NumberOfOccupants',
  acphDefault: 'heatDesign.propertyDetails.ACPHDefault',
  uValues: 'heatDesign.uValues.uValues',
  pulseTest: 'heatDesign.title.pulseTestAirPermeability',
} as const;

export function DwellingValidationModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const intl = useIntl();
  const constructionYear = useConstructionYear();
  const numOccupants = useNumOccupants();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const rooms = useRooms();
  const floorsData = useFloors();
  const { projectUValues } = useUValuesStore();

  if (!isOpen) return null;

  const errors: DwellingValidationErrors = {
    constructionYear: !propertyIsValid('dwelling', 'constructionYear', constructionYear),
    numberOfResidents: !propertyIsValid('dwelling', 'numberOfResidents', numOccupants),
    uValues: !projectUValuesDefaultsAreValid(projectUValues, rooms, floorsData),
    acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
    pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
  };

  return (
    <ValidationModal
      isOpen={isOpen}
      onClose={onClose}
      heading={<FormattedMessage id="heatDesign.dwellingValidationModal.title" />}
    >
      {Object.keys(errors)
        .filter((errorKey) => errors[errorKey as keyof DwellingValidationErrors])
        .map((errorKey) => (
          <Stack key={errorKey} direction="row" alignItems="center" mt={2} gap={1}>
            <span>&bull;</span>
            <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
            <Stack direction="row" alignItems="center" gap={4} sx={{ width: '100%' }}>
              <Typography variant="body1">
                <FormattedMessage
                  id="heatDesign.validationModal.missingOrInvalid"
                  values={{
                    property: intl.formatMessage({ id: COPY_FOR_ERROR[errorKey as keyof DwellingValidationErrors] }),
                  }}
                />
              </Typography>
            </Stack>
          </Stack>
        ))}
    </ValidationModal>
  );
}
