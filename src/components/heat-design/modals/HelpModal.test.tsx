import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { screen, within } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { setupServer } from 'msw/node';
import { IntlProvider } from 'react-intl';
import { mockGetServerEnvironment, mockGetTechnicalSpecifications_empty, mocks } from 'tests/utils/mockedTrpcCalls';
import { reload, renderWithProviders, trpcMsw } from 'tests/utils/testUtils';
import { vi } from 'vitest';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { createRouterMock, useRouter } from '@mocks/next/router';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';
import { solution as asaSolution } from '../../../tests/heat-loss/fixtures/asaTestData';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';

// Test setup and lifecycle

useRouter.mockReturnValue({
  ...createRouterMock(),
  route: '/heat-design',
  pathname: '/heat-design',
  query: { solution: SOLUTION_ID },
  asPath: `/heat-design?solution=${SOLUTION_ID}`,
  basePath: '',
  isLocaleDomain: false,
  isFallback: false,
  isReady: true,
  isPreview: false,
  push: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  prefetch: () => Promise.resolve(),
  beforePopState: vi.fn(),
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
});

const server = setupServer(
  mockGetServerEnvironment(),
  mockGetTechnicalSpecifications_empty(),
  mocks.getGrpcEnergySolution.asa,
  trpcMsw.AiraBackend.getEnergySolutionDiff.query(() => ({
    currentSolution: asaSolution,
    lastQuotedSolution: asaSolution,
  })),
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.getHeatPumpParameters.simple,
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: untypedAsaHouse as unknown as ProtoHeatDesign,
    isLocked: false,
    result: undefined,
    events: [],
    updatedAt: new Date(),
  })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => []),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [],
  })),
  trpcMsw.InstallationGroundwork.sendHeatPumpParameters.mutation(() => ({})),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators: [] })),
  ...sharedHeatDesignHandlers,
);

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() => server.listen());
beforeEach(async () => {
  renderWithProviders(heatDesignComponent());
  await reload();
});
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

test('Ensure the help modal behaves properly', async () => {
  await userEvent.click(screen.getByRole('button', { name: 'help' }));

  const helpModal = screen.getByTestId('help-modal');
  expect(helpModal).toBeVisible();

  expect(within(helpModal).getByText('heatDesign.helpModal.faq.title')).toBeVisible();
  expect(within(helpModal).getByText('heatDesign.helpModal.faq.body')).toBeVisible();
  expect(within(helpModal).getByRole('button', { name: 'heatDesign.helpModal.faq.button' })).toBeVisible();

  expect(within(helpModal).getByText('heatDesign.helpModal.request.title')).toBeVisible();
  expect(within(helpModal).getByText('heatDesign.helpModal.request.body')).toBeVisible();
  expect(within(helpModal).getByRole('button', { name: 'error.support.card.askHelpText' })).toBeVisible();

  expect(helpModal).toHaveTextContent(SOLUTION_ID);

  await userEvent.click(screen.getByTestId('close-modal'));
}, 5_000);
