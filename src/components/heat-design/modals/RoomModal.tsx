import { Modal } from '@ui/components/Modal/Modal';
import RoomRenderer from '../RoomRenderer';
import { useHeatDesignUI } from '../stores/HeatDesignUIStore';
import { useRooms } from '../stores/RoomsStore';

function RoomModal({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const selectedRoomId = useHeatDesignUI((s) => s.selectedRoomId);
  const rooms = useRooms();
  if (!isOpen || !selectedRoomId) return null;
  const room = rooms.find((r) => r.id === selectedRoomId);
  return (
    <Modal
      isModalOpen={isOpen}
      showCloseButton
      handleClose={onClose}
      height="90vh"
      width="90vw"
      heading={room?.name}
      sx={{
        '&.MuiBox-root': {
          paddingBottom: '0px',
        },
      }}
    >
      <RoomRenderer room={room} />
    </Modal>
  );
}

export default RoomModal;
