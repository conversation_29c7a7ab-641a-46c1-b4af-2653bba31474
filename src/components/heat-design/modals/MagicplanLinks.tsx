import { Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { OpenInNewOutlined } from '@ui/components/Icons/material';
import { FormattedMessage } from 'react-intl';

type SurveyLinks = {
  deeplinkToMobile?: string;
  webUrl?: string;
};

export function MagicplanLinks({ surveyLinks, showTitle = true }: { surveyLinks?: SurveyLinks; showTitle?: boolean }) {
  return (
    <Stack gap={1} width="100%">
      {showTitle && (
        <Typography variant="body1">
          <FormattedMessage id="heatDesign.magicplan.links.title" />
        </Typography>
      )}
      <Stack direction="row" justifyContent="space-between" gap={2}>
        <Stack width="100%">
          <Button
            variant="contained"
            startIcon={<OpenInNewOutlined />}
            href={surveyLinks?.deeplinkToMobile}
            disabled={surveyLinks?.deeplinkToMobile === undefined}
            target="_blank"
            size="small"
            fullWidth
          >
            <FormattedMessage id="heatDesign.magicplan.links.mobile" />
          </Button>
          {surveyLinks?.deeplinkToMobile === undefined && (
            <Typography variant="body3">Link not currently available</Typography>
          )}
        </Stack>
        <Stack width="100%">
          <Button
            variant="contained"
            startIcon={<OpenInNewOutlined />}
            href={surveyLinks?.webUrl}
            disabled={surveyLinks?.webUrl === undefined}
            target="_blank"
            size="small"
            fullWidth
          >
            <FormattedMessage id="heatDesign.magicplan.links.web" />
          </Button>
          {surveyLinks?.webUrl === undefined && <Typography variant="body3">Link not currently available</Typography>}
        </Stack>
      </Stack>
    </Stack>
  );
}
