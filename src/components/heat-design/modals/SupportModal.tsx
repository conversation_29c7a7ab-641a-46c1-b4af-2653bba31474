import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { Typography } from '@mui/material';
import { ErrorModal } from './ErrorModal';
import { ErrorContent } from '../components/errors/ErrorContent';
import { HeatDesignErrorInfo } from '../components/errors/HeatDesignErrorInfo';
import HeatDesignCard from '../HeatDesignCard';

interface SupportModalProps {
  isOpen: boolean;
  onClose: () => void;
  heading: string;
  description: string;
  errorMessage?: string;
  /**
   * True if the user is expected to solve this error themselves.
   * False if they require developer support to solve the error.
   */
  selfSupport?: boolean;
}

export function SupportModal({
  isOpen,
  onClose,
  heading,
  description,
  errorMessage,
  selfSupport = false,
}: SupportModalProps) {
  const energySolutionId = useEnergySolutionId();
  const {
    groundwork: { id: groundworkId },
  } = useGroundwork();

  return (
    <ErrorModal isOpen={isOpen} onClose={onClose} heading={heading}>
      {selfSupport && (
        <HeatDesignCard variant="light" sx={{ whiteSpace: 'pre' }}>
          <Typography>{description}</Typography>
        </HeatDesignCard>
      )}
      {!selfSupport && (
        <ErrorContent
          details={
            <>
              <HeatDesignErrorInfo
                description={heading}
                solutionId={energySolutionId}
                groundworkId={groundworkId?.value}
              />
              {errorMessage && (
                <Typography mt={2} sx={{ overflowWrap: 'break-word' }}>
                  Error Message: {errorMessage}
                </Typography>
              )}
            </>
          }
        >
          {description}
        </ErrorContent>
      )}
    </ErrorModal>
  );
}
