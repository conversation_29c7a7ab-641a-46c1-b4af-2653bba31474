import { Box, Typography } from '@mui/material';
import { Stack, styled } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { PersonOutlined } from '@ui/components/Icons/material';
import { ArrowSpinAnticlockwiseOutlinedIcon } from '@ui/components/StandardIcons/ArrowSpinAnticlockwiseOutlinedIcon';
import { ArrowSpinUpDownSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowSpinUpDownSquareOutlinedIcon';
import { BetaPill } from '@ui/components/TextPill/BetaPill';
import { theme } from '@ui/theme/theme';
import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { isEqualTreatingUndefinedAsMissing } from 'utils/comparison';
import { getAerospaceErrorDetails } from 'utils/errors/aerospaceError';
import { useHeatDesignSurveyForm } from '../hooks/useHeatDesignSurveyForm';
import { useSerializedHeatDesignAndResults } from '../hooks/useSerializedHeatDesignAndResults';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import { ErrorModal } from './ErrorModal';
import { MagicplanLinks } from './MagicplanLinks';
import { MagicplanReimportModal } from './MagicplanReimportModal';
import { SupportModal } from './SupportModal';
import { beige } from '@ui/theme/colors';

export type AdvancedMagicPlanErrorType = 'REASSIGN' | 'REIMPORT' | 'MERGE' | 'MERGE_DUPLICATES';
type AdvancedMagicPlanActionsError = {
  errorType: AdvancedMagicPlanErrorType | null;
  errorMessage: string;
};

interface Props {
  onClose?: () => void;
}

export function AdvancedMagicplanActions({ onClose }: Props) {
  const { formatMessage } = useIntl();
  const energySolutionId = useEnergySolutionId();
  const [showMergeModal, setShowMergeModal] = useState<boolean>(false);
  const [showReimportModal, setShowReimportModal] = useState<boolean>(false);
  const [advanceActionsError, setAdvanceActionsError] = useState<AdvancedMagicPlanActionsError>({
    errorType: null,
    errorMessage: '',
  });
  const {
    groundwork: { id: installationGroundworkId },
  } = useGroundwork();
  const surveyForm = useHeatDesignSurveyForm({ installationGroundworkId: installationGroundworkId?.value });

  const handleCloseErrorModal = () => {
    setAdvanceActionsError({
      errorType: null,
      errorMessage: '',
    });
    if (onClose) onClose();
  };

  const { mutate: reassignProject, isPending: isReassigning } =
    api.HeatLossCalculator.reassignMagicplanProject.useMutation({
      onSuccess: () => {
        toast.success(formatMessage({ id: 'heatDesign.magicplan.reassign.success' }));
        if (onClose) onClose();
      },
      onError: (error) => {
        setAdvanceActionsError({
          errorType: 'REASSIGN',
          errorMessage: error?.message,
        });
      },
    });
  const { mutateAsync: resetToMagicplan, isPending: isReimporting } =
    api.HeatLossCalculator.resetHeatDesign.useMutation({
      onSuccess() {
        if (onClose) onClose();
        // Ensure the toast duration is longer than the pause before reloading the page,
        // to ensure the user has enough time to read the success message.
        toast.success(formatMessage({ id: 'heatDesign.magicplan.reimport.success' }), { duration: 8_000 });
        setTimeout(() => {
          window.location.reload();
        }, 4_000);
      },
      onError(error) {
        setAdvanceActionsError({
          errorType: 'REIMPORT',
          errorMessage: error?.message,
        });
      },
    });
  const { mutate: mergeWithMagicplan, isPending: isMerging } =
    api.HeatLossCalculator.resetHeatDesignUsingMerge.useMutation({
      onSuccess() {
        if (onClose) onClose();
        // Ensure the toast duration is longer than the pause before reloading the page,
        // to ensure the user has enough time to read the success message.
        toast.success(formatMessage({ id: 'heatDesign.magicplan.merge.success' }), { duration: 8_000 });
        setTimeout(() => {
          window.location.reload();
        }, 4_000);
      },
      onError(error) {
        const aerospaceErrorDetails = getAerospaceErrorDetails(error);
        if (aerospaceErrorDetails && aerospaceErrorDetails.detailType === 'MAGICPLAN_MERGE_DUPLICATES_ERROR') {
          setShowMergeModal(false);
          setAdvanceActionsError({
            errorType: 'MERGE_DUPLICATES',
            errorMessage: error?.message,
          });
        } else {
          setAdvanceActionsError({
            errorType: 'MERGE',
            errorMessage: error?.message,
          });
        }
      },
    });

  const errorTitle = (() => {
    switch (advanceActionsError.errorType) {
      case 'REASSIGN':
        return formatMessage({ id: 'heatDesign.magicplan.reassign.error.card.title' });
      case 'REIMPORT':
        return formatMessage({ id: 'heatDesign.magicplan.reimport.error.card.title' });
      case 'MERGE':
        return formatMessage({ id: 'heatDesign.magicplan.merge.error.card.title' });
      case 'MERGE_DUPLICATES':
        return formatMessage({ id: 'heatDesign.magicplan.merge.error.card.title' });
      default:
        return '';
    }
  })();

  const errorDescription = (() => {
    switch (advanceActionsError.errorType) {
      case 'REASSIGN':
        return formatMessage({ id: 'heatDesign.magicplan.reassign.error.card.description' });
      case 'REIMPORT':
        return formatMessage({ id: 'heatDesign.magicplan.reimport.error.card.description' });
      case 'MERGE':
        return formatMessage({ id: 'heatDesign.magicplan.merge.error.card.description' });
      case 'MERGE_DUPLICATES':
        return formatMessage({ id: 'heatDesign.magicplan.merge.error.card.description.duplicates' });
      default:
        return '';
    }
  })();

  const AdvancedMagicplanSection = styled(Stack)({
    padding: theme.spacing(1.5),
    margin: theme.spacing(1.5),
    borderRadius: '12px',
    backgroundColor: 'white',
  });

  const isLocked = useProtobufHeatDesignStore((state) => state.isLocked);

  const serializedData = useSerializedHeatDesignAndResults();
  const originalHeatDesign = useProtobufHeatDesignStore((s) => s.heatDesign);
  const dirty = serializedData && !isEqualTreatingUndefinedAsMissing(originalHeatDesign, serializedData.heatDesign);

  return (
    <Box>
      {installationGroundworkId !== undefined && energySolutionId !== undefined && (
        <Stack>
          <AdvancedMagicplanSection sx={{ backgroundColor: beige['200'] }}>
            <MagicplanLinks surveyLinks={surveyForm} />
          </AdvancedMagicplanSection>

          <AdvancedMagicplanSection gap={1}>
            <Typography variant="body1">
              <FormattedMessage id="heatDesign.magicplan.reassign.description" />
            </Typography>
            <Button
              variant="contained"
              disabled={isReassigning}
              startIcon={<PersonOutlined />}
              size="small"
              onClick={() =>
                reassignProject({
                  energySolutionId,
                  installationGroundworkId: installationGroundworkId.value,
                })
              }
            >
              <FormattedMessage
                id={
                  isReassigning
                    ? 'heatDesign.magicplan.reassign.buttonLabel.loading'
                    : 'heatDesign.magicplan.reassign.buttonLabel'
                }
              />
            </Button>
          </AdvancedMagicplanSection>

          <AdvancedMagicplanSection gap={1}>
            <Typography variant="body1">
              <FormattedMessage id="heatDesign.magicplan.merge.description" />
            </Typography>
            <Button
              variant="contained"
              startIcon={<ArrowSpinUpDownSquareOutlinedIcon />}
              size="small"
              disabled={isLocked || dirty}
              onClick={() => setShowMergeModal(true)}
            >
              <Stack direction="row" gap={1} alignItems="center">
                <BetaPill />
                <FormattedMessage id="heatDesign.magicplan.merge.buttonLabel" />
              </Stack>
            </Button>
            {dirty && (
              <Typography variant="body2">
                <FormattedMessage id="heatDesign.magicplan.merge.unsaved" />
              </Typography>
            )}
            {isLocked && (
              <Typography variant="body2">
                <FormattedMessage id="heatDesign.magicplan.merge.locked" />
              </Typography>
            )}
          </AdvancedMagicplanSection>

          <AdvancedMagicplanSection gap={1}>
            <Typography variant="body1">
              <FormattedMessage id="heatDesign.magicplan.reimport.description" />
            </Typography>
            <Button
              variant="contained"
              startIcon={<ArrowSpinAnticlockwiseOutlinedIcon />}
              size="small"
              disabled={isLocked}
              onClick={() => setShowReimportModal(true)}
              color="error"
            >
              <FormattedMessage id="heatDesign.magicplan.reimport.buttonLabel" />
            </Button>
            {isLocked && (
              <Typography variant="body2">
                <FormattedMessage id="heatDesign.magicplan.reimport.locked" />
              </Typography>
            )}
          </AdvancedMagicplanSection>
        </Stack>
      )}
      {installationGroundworkId && energySolutionId && (
        <>
          <MagicplanReimportModal
            isOpen={showMergeModal}
            onClose={() => setShowMergeModal(false)}
            action={() => {
              mergeWithMagicplan({
                energySolutionId,
                installationGroundworkId: installationGroundworkId.value,
              });
            }}
            isActionInProgress={isMerging}
            actionIcon={<ArrowSpinUpDownSquareOutlinedIcon />}
            actionInProgressTitle="heatDesign.magicplan.merge.buttonLabel.loading"
            actionTitle="heatDesign.magicplan.merge.buttonLabel"
            title="heatDesign.magicplan.merge.modal.title"
            body="heatDesign.magicplan.merge.modal.body"
          />
          <MagicplanReimportModal
            isOpen={showReimportModal}
            onClose={() => setShowReimportModal(false)}
            action={() => {
              resetToMagicplan({
                energySolutionId,
                installationGroundworkId: installationGroundworkId.value,
              });
            }}
            isActionInProgress={isReimporting}
            actionColour="error"
            actionIcon={<ArrowSpinAnticlockwiseOutlinedIcon />}
            actionInProgressTitle="heatDesign.magicplan.reimport.buttonLabel.loading"
            actionTitle="heatDesign.magicplan.reimport.buttonLabel"
            title="heatDesign.magicplan.reimport.modal.title"
            body="heatDesign.magicplan.reimport.modal.body"
          />
        </>
      )}
      <SupportModal
        onClose={handleCloseErrorModal}
        isOpen={advanceActionsError.errorType !== null && advanceActionsError.errorType !== 'MERGE_DUPLICATES'}
        errorMessage={advanceActionsError.errorMessage}
        heading={errorTitle}
        description={errorDescription}
      />
      <ErrorModal
        isOpen={advanceActionsError.errorType === 'MERGE_DUPLICATES'}
        onClose={handleCloseErrorModal}
        heading={errorTitle}
      >
        {errorDescription}
      </ErrorModal>
    </Box>
  );
}
