import { Modal } from '@ui/components/Modal/Modal';
import { Stack } from '@mui/system';
import { ReactNode } from 'react';
import { ErrorHeading } from '../components/errors/ErrorHeading';

interface ErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  heading: ReactNode;
  children: React.ReactNode;
}

export function ErrorModal({ isOpen, onClose, heading, children }: ErrorModalProps) {
  return (
    <Modal
      showCloseButton
      isModalOpen={isOpen}
      handleClose={onClose}
      heading={<ErrorHeading>{heading}</ErrorHeading>}
      height="auto"
    >
      <Stack gap={2}>{children}</Stack>
    </Modal>
  );
}
