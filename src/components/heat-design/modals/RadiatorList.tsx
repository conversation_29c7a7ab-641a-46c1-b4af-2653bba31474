import React from 'react';
import { Accordion } from '@ui/components/Accordion/Accordion';
import { useIntl } from 'react-intl';
import { Stack } from '@mui/material';
import { beige } from '@ui/theme/colors';
import NewRadiatorsList from '../report/NewRadiatorsList';
import { RadiatorsGroupedBySize, RadiatorSize } from '../stores/HeatSourceStore';
import { RadiatorData } from '../stores/types';
import { hasRadiatorBeenRemovedFromErp } from '../utils/radiatorHelpers';
import { WarningBox } from '../components/WarningBox';

function RadiatorGroup({ size, radiators }: { size: RadiatorSize; radiators: RadiatorData[] }) {
  const { formatMessage } = useIntl();
  const radiatorSizeLabel = formatMessage({ id: `heatDesign.radiatorSizeCategory.${size}` });

  return (
    <Stack
      sx={{
        borderBottom: '1px solid #D8D8D8',
        marginBottom: 2,
      }}
    >
      <Accordion
        header={formatMessage(
          { id: 'heatDesign.productSelection.radiatorsList.groupHeader.v2' },
          { group: radiatorSizeLabel, numberOfRadiators: radiators.length },
        )}
        data-test="accordion"
        sx={{
          background: 'transparent !important',
          paddingBottom: 2,
          paddingTop: 0,
          '.MuiAccordionDetails-root': {
            paddingLeft: 0,
            paddingRight: 0,
          },
        }}
        headingLevel={1}
        headingVariant="body1Emphasis"
        arrowPosition="right"
      >
        <NewRadiatorsList radiators={radiators} tableCellColor={beige[150]} showErpId />
      </Accordion>
    </Stack>
  );
}

function hasAnyRadiatorBeenRemovedFromERP({ groupedRadiators }: { groupedRadiators: RadiatorsGroupedBySize }) {
  return (
    Object.values(groupedRadiators)
      .flatMap((r) => r)
      .find((radiator) => hasRadiatorBeenRemovedFromErp(radiator)) !== undefined
  );
}

function RadiatorList({ groupedRadiators }: { groupedRadiators: RadiatorsGroupedBySize }) {
  const smallRadiators = groupedRadiators[RadiatorSize.STANDARD];
  const largeRadiators = groupedRadiators[RadiatorSize.LARGE];
  const designRadiators = groupedRadiators[RadiatorSize.DESIGN];

  return (
    <Stack>
      {designRadiators.length > 0 && <RadiatorGroup size={RadiatorSize.DESIGN} radiators={designRadiators} />}
      {largeRadiators.length > 0 && <RadiatorGroup size={RadiatorSize.LARGE} radiators={largeRadiators} />}
      {smallRadiators.length > 0 && <RadiatorGroup size={RadiatorSize.STANDARD} radiators={smallRadiators} />}
      {hasAnyRadiatorBeenRemovedFromERP({ groupedRadiators }) && (
        <WarningBox contentId="heatDesign.radiatorModal.removedFromErp" />
      )}
    </Stack>
  );
}
export default RadiatorList;
