import { Modal } from '@ui/components/Modal/Modal';
import { Stack } from '@mui/system';
import { Typography } from '@mui/material';
import { useIntl } from 'react-intl';

interface SupportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UnsavedChangeNoticeModal({ isOpen, onClose }: SupportModalProps) {
  const { formatMessage } = useIntl();

  return (
    <Modal
      width="40vw"
      showCloseButton
      isModalOpen={isOpen}
      handleClose={onClose}
      heading={
        <Stack mb={2} justifyContent="center" alignContent="center" direction="row" gap={2}>
          <Typography variant="headline2">{formatMessage({ id: 'hlc.unsavedChanges.modal.tittle' })}</Typography>
        </Stack>
      }
      height="auto"
    >
      <Stack>
        <Typography variant="body1">{formatMessage({ id: 'hlc.unsavedChanges.modal.description' })}</Typography>
      </Stack>
    </Modal>
  );
}
