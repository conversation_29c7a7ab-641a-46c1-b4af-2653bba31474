import { EmitterType } from 'components/heat-pump-config/stores/types';
import { ReactNode } from 'react';
import { printRadiatorDescription } from '../report/helpers';
import { EmitterOverviewDetails } from './types';

export function printEmitterOverviewDescription(emitterOverviewDetails: EmitterOverviewDetails): ReactNode {
  switch (emitterOverviewDetails.type) {
    case EmitterType.RADIATOR:
      return printRadiatorDescription(emitterOverviewDetails);
    case EmitterType.UNDERFLOOR:
      return emitterOverviewDetails.description ?? '-';
    default:
      throw new Error(`Unknown emitter type on: ${emitterOverviewDetails}`);
  }
}
