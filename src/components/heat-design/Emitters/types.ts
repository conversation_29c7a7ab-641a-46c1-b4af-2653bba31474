import { EmitterType } from 'components/heat-pump-config/stores/types';
import { RadiatorData, Room } from '../stores/types';

type BaseEmitterOverviewDetails = {
  description?: string;
  room: Room;
  deltaT: number;
  flowRate: number;
  meanWaterTemp: number;
  output: number;
};

export type RadiatorEmitterOverviewDetails = BaseEmitterOverviewDetails &
  RadiatorData & {
    type: EmitterType.RADIATOR;
  };

export type UnderfloorHeatingEmitterOverviewDetails = BaseEmitterOverviewDetails & {
  type: EmitterType.UNDERFLOOR;
};

export type EmitterOverviewDetails = RadiatorEmitterOverviewDetails | UnderfloorHeatingEmitterOverviewDetails;
