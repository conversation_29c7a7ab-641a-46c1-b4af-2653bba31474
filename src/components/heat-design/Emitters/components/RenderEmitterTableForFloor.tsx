import React from 'react';

import { useIntl } from 'react-intl';
import {
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { FloorProps, SystemType } from 'components/heat-design/stores/types';
import { beige, grey } from '@ui/theme/colors';
import { useFlowReturnDeltaT } from 'components/heat-design/stores/HeatSourceStore';
import { toOneDecimalPlaceText } from 'components/heat-design/utils/helpers';
import { EmitterType } from 'components/heat-pump-config/stores/types';
import { printEmitterOverviewDescription } from '../helpers';
import { EmitterOverviewDetails } from '../types';

function printRadiatorDeltaT(radiator: EmitterOverviewDetails, flowReturnDeltaT: number): string {
  if (radiator.type === EmitterType.RADIATOR) {
    if (radiator.radiatorDetails.systemType === SystemType.ELECTRIC) {
      return `N/A`;
    }
    if (radiator.deltaT !== flowReturnDeltaT) {
      return `${radiator.deltaT}*`;
    }
  }

  return `${radiator.deltaT}`;
}

function printRadiatorFlowRate(radiator: EmitterOverviewDetails): string {
  if (radiator.type === EmitterType.RADIATOR) {
    if (radiator.radiatorDetails.systemType === SystemType.ELECTRIC) {
      return `N/A`;
    }
  }

  return toOneDecimalPlaceText(radiator.flowRate);
}

function getEmitterIdentifier(emitterDetail: EmitterOverviewDetails): React.Key {
  switch (emitterDetail.type) {
    case EmitterType.RADIATOR:
      return emitterDetail.uid;
    case EmitterType.UNDERFLOOR:
      return `${emitterDetail.room.name}-underfloor-heating`;
    default:
      throw new Error(`Unknown emitter type on: ${emitterDetail}`);
  }
}

export function RenderEmitterTableForFloor({
  floor,
  emitterDetails,
  selectedEmitterType,
}: {
  floor: FloorProps;
  emitterDetails: EmitterOverviewDetails[];
  selectedEmitterType: EmitterType;
}) {
  const { formatMessage } = useIntl();

  const flowReturnDeltaT = useFlowReturnDeltaT();

  const formatRoomNameForTestId = (name: string) => name.toLowerCase().replace(' ', '-');

  const showExistingNewColumn = selectedEmitterType === EmitterType.RADIATOR;

  const filteredEmitterDetails = emitterDetails.filter((emitterDetail) => emitterDetail.type === selectedEmitterType);

  if (filteredEmitterDetails.length === 0) {
    return null;
  }

  return (
    <>
      <Typography variant="headline4" gutterBottom sx={{ '@media print': { fontSize: 16, fontWeight: 'normal' } }}>
        {floor.floorName}
      </Typography>
      <TableContainer>
        <Table
          sx={{
            [`& .${tableCellClasses.root}`]: {
              borderBottom: 'none',
              '@media print': {
                borderBottom: `1px solid ${grey[500]}`,
              },
            },
            '@media print': {
              border: `1px solid ${grey[500]}`,
            },
          }}
        >
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  borderTopLeftRadius: 8,
                  backgroundColor: beige[200],
                  '@media print': { border: `1px solid ${grey[500]}` },
                }}
              >
                {formatMessage({ id: 'heatDesign.tableHeaders.roomName' })}
              </TableCell>
              {showExistingNewColumn && (
                <TableCell sx={{ backgroundColor: beige[200], '@media print': { border: `1px solid ${grey[500]}` } }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.existing-new' })}
                </TableCell>
              )}
              <TableCell sx={{ backgroundColor: beige[200], '@media print': { border: `1px solid ${grey[500]}` } }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.emitterDescription' })}
              </TableCell>
              <TableCell sx={{ backgroundColor: beige[200], '@media print': { border: `1px solid ${grey[500]}` } }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.emitterDeltaT' })}
              </TableCell>
              <TableCell sx={{ backgroundColor: beige[200], '@media print': { border: `1px solid ${grey[500]}` } }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.flowRate' })}
              </TableCell>
              <TableCell
                sx={{
                  borderTopRightRadius: 8,
                  backgroundColor: beige[200],
                  '@media print': { border: `1px solid ${grey[500]}` },
                }}
              >
                {formatMessage({ id: 'heatDesign.tableHeaders.OutputWatt' })}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredEmitterDetails.map((emitterDetail, index) => (
              <TableRow
                data-testid={`emitter-list-row-${index}`}
                key={getEmitterIdentifier(emitterDetail)}
                sx={{
                  background: index % 2 === 0 ? 'white' : beige[100],
                  '@media print': { border: `1px solid ${grey[500]}` },
                }}
              >
                <TableCell
                  sx={{
                    '@media print': { border: `1px solid ${grey[500]}` },
                    ...(index === emitterDetails.length - 1 && {
                      borderBottomLeftRadius: 8,
                      borderBottomRightRadius: 8,
                    }),
                  }}
                >
                  {emitterDetail.room.name}
                </TableCell>
                {emitterDetail.type === EmitterType.RADIATOR && (
                  <TableCell
                    sx={{
                      '@media print': { border: `1px solid ${grey[500]}` },
                    }}
                  >
                    {emitterDetail.isExisting ? 'Existing' : 'New'}
                  </TableCell>
                )}
                <TableCell
                  sx={{
                    '@media print': { border: `1px solid ${grey[500]}` },
                  }}
                >
                  {printEmitterOverviewDescription(emitterDetail)}
                </TableCell>
                <TableCell
                  data-testid={`heatDesign-emitterList-emitterDeltaT-${formatRoomNameForTestId(emitterDetail.room.name)}`}
                  sx={{
                    '@media print': { border: `1px solid ${grey[500]}` },
                  }}
                >
                  {printRadiatorDeltaT(emitterDetail, flowReturnDeltaT)}
                </TableCell>
                <TableCell
                  sx={{
                    '@media print': { border: `1px solid ${grey[500]}` },
                  }}
                  data-testid={`heatDesign-emitterList-flowRate-${formatRoomNameForTestId(emitterDetail.room.name)}`}
                >
                  {printRadiatorFlowRate(emitterDetail)}
                </TableCell>
                <TableCell
                  data-testid="emitter-list-output-cell"
                  sx={{
                    '@media print': { border: `1px solid ${grey[500]}` },
                    ...(index === emitterDetails.length - 1 && {
                      borderBottomLeftRadius: 8,
                      borderBottomRightRadius: 8,
                    }),
                  }}
                >
                  {emitterDetail.output}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}
