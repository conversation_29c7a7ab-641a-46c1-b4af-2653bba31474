import { RoomsByFloor, useGetRoomsByFloor } from 'components/heat-design/stores/RoomsStore';
import { Box, Stack, Typography } from '@mui/material';
import { FormattedMessage, IntlShape, MessageDescriptor, useIntl } from 'react-intl';
import { beige } from '@ui/theme/colors';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { Heading } from '@ui/components/Heading/Heading';
import { Wave1HorizontalCircleIcon } from '@ui/components/StandardIcons/Wave/Wave1HorizontalCircleIcon';
import { Wave2Icon } from '@ui/components/StandardIcons/Wave/Wave2Icon';
import { Wave3Icon } from '@ui/components/StandardIcons/Wave/Wave3Icon';
import { grey } from '@mui/material/colors';
import { formatDegreeCelsius } from 'utils/helpers';
import { toOneDecimalPlaceText } from 'components/heat-design/utils/helpers';
import { useFlowReturnDeltaT, useFlowTemp } from 'components/heat-design/stores/HeatSourceStore';
import {
  calculateEmitterDetailsSummary,
  calculateUnderfloorHeatingDetailsForFloor,
} from 'components/heat-design/utils/radiatorHelpers';

import { useState } from 'react';
import { StyledFormattedMessage } from 'utils/localization';
import { EmitterType } from 'components/heat-pump-config/stores/types';
import { useFloors } from '../stores/FloorsStore';
import { RenderEmitterTableForFloor } from './components/RenderEmitterTableForFloor';
import { WarningIcon } from '../components/WarningIcon';
import { FloorProps, SystemType } from '../stores/types';
import { UNDERFLOOR_HEATING_FLOW_TEMP } from '../constants';
import { RadiatorEmitterOverviewDetails, UnderfloorHeatingEmitterOverviewDetails } from './types';
import { RoomHeatDesignResult } from '../stores/OutputsStore';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import InfoBlock from '../radiators/RadiatorRenderModalInfoBlock';
import { useRadiatorSummary } from '../radiators/hooks/useRadiatorSummary';

type MessageId = MessageDescriptor['id'];

function getMessageIdForEmitterType(type: EmitterType): {
  flowTempId: MessageId;
  returnTempId: MessageId;
  flowNeededId: MessageId;
} {
  switch (type) {
    case EmitterType.RADIATOR:
      return {
        flowTempId: 'heatDesign.emitterList.summary.radiatorFlowTemperature',
        returnTempId: 'heatDesign.emitterList.summary.radiatorReturnTemperature',
        flowNeededId: 'heatDesign.emitterList.summary.flowNeededForRadiators',
      };
    case EmitterType.UNDERFLOOR:
      return {
        flowTempId: 'heatDesign.emitterList.summary.underfloorHeatingFlowTemperature',
        returnTempId: 'heatDesign.emitterList.summary.underfloorHeatingReturnTemperature',
        flowNeededId: 'heatDesign.emitterList.summary.flowNeededForUnderfloorHeating',
      };
    default:
      throw new Error(`Unknown emitter type: ${type}`);
  }
}

function calculateUnderfloorHeatingEmitterDetailsByFloor(
  intl: IntlShape,
  floors: FloorProps[],
  roomsByFloor: RoomsByFloor,
  roomsHeatDesignResults: RoomHeatDesignResult[],
  flowTemperature: number,
  flowReturnDeltaT: number,
): { [floorId: string]: UnderfloorHeatingEmitterOverviewDetails[] } {
  return floors.reduce(
    (acc, floor) => {
      const emitterDetails = calculateUnderfloorHeatingDetailsForFloor({
        intl,
        floor,
        roomsByFloor,
        roomsHeatDesignResults,
        flowTemperature,
        flowReturnDeltaT,
      });

      acc[floor.uid] = emitterDetails;
      return acc;
    },
    {} as { [floorId: string]: UnderfloorHeatingEmitterOverviewDetails[] },
  );
}

type EmitterOverviewSummaryAndDetailsProps = {
  emitterDetailsSummary: {
    totalFlowNeeded: number;
    actualCircuitReturnTemperature: number;
  };
  emitterDetailsByFloor: {
    [floorId: string]: RadiatorEmitterOverviewDetails[] | UnderfloorHeatingEmitterOverviewDetails[];
  };
  floors: FloorProps[];
  flowTemp: number;
  showUnderfloorHeatingDisclaimer: boolean;
  emitterType: EmitterType;
  translationIds: {
    flowTempId: MessageId;
    returnTempId: MessageId;
    flowNeededId: MessageId;
  };
  onlyOneEmitter: boolean;
};

function EmitterOverviewSummaryAndDetails({
  emitterDetailsSummary,
  emitterDetailsByFloor,
  floors,
  flowTemp,
  showUnderfloorHeatingDisclaimer,
  emitterType,
  translationIds,
  onlyOneEmitter,
}: EmitterOverviewSummaryAndDetailsProps) {
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const showNote =
    emitterType === EmitterType.RADIATOR &&
    floors.some((floor) =>
      emitterDetailsByFloor[floor.uid]
        ?.filter(
          (emitterDetail) =>
            emitterDetail.type === EmitterType.RADIATOR &&
            emitterDetail.radiatorDetails.systemType === SystemType.WATER,
        )
        .some((emitter) => emitter.deltaT !== flowReturnDeltaT),
    );

  return (
    <Stack direction="column" justifyContent="space-between" spacing={2}>
      <Heading
        variant="headline3"
        mb={2}
        level={3}
        sx={{
          display: onlyOneEmitter ? 'block' : 'none',
          '@media print': {
            display: 'block',
          },
        }}
      >
        <FormattedMessage id={`heatDesign.emitterList.emitterType.${emitterType}`} />
      </Heading>
      <Stack direction="row" spacing={2} width="100%">
        <Box
          width="100%"
          borderRadius={2}
          p={2}
          sx={{
            background: 'white',
            '@media print': {
              border: `1px solid ${grey[500]}`,
            },
          }}
        >
          <InfoBlock
            verticalLayout
            id="heatDesign-emitterList-summary-flowTemp"
            icon={<Wave3Icon />}
            value={formatDegreeCelsius(flowTemp)}
            label={<FormattedMessage id={translationIds.flowTempId} />}
          />
        </Box>
        <Box
          width="100%"
          borderRadius={2}
          p={2}
          sx={{
            background: 'white',
            '@media print': {
              border: `1px solid ${grey[500]}`,
            },
          }}
        >
          <InfoBlock
            verticalLayout
            id="heatDesign-emitterList-summary-actualCircuitReturnTemperature"
            icon={<Wave2Icon />}
            value={formatDegreeCelsius(emitterDetailsSummary.actualCircuitReturnTemperature)}
            label={<FormattedMessage id={translationIds.returnTempId} />}
          />
        </Box>
        <Box
          width="100%"
          borderRadius={2}
          p={2}
          sx={{
            background: onlyOneEmitter ? beige[200] : 'white',
            '@media print': {
              border: `1px solid ${grey[500]}`,
            },
          }}
        >
          <InfoBlock
            verticalLayout
            id="heatDesign-emitterList-summary-flowNeeded"
            icon={<Wave1HorizontalCircleIcon />}
            value={`${toOneDecimalPlaceText(emitterDetailsSummary.totalFlowNeeded)} l/h`}
            label={<FormattedMessage id={translationIds.flowNeededId} />}
          />
        </Box>
      </Stack>
      {showUnderfloorHeatingDisclaimer && emitterType === 'underfloor' && (
        <Stack direction="row" alignItems="center" borderRadius={2} p={2} spacing={2} sx={{ background: 'white' }}>
          <WarningIcon
            x={2.5}
            y={2.5}
            iconWidth={25}
            iconHeight={25}
            canvasWidth={25}
            canvasHeight={25}
            containerFill="black"
            exclamationFill="white"
          />
          <Typography variant="body1" justifyContent="center">
            <StyledFormattedMessage id="heatDesign.emitterList.underfloorHeatingDisclaimer" />
          </Typography>
        </Stack>
      )}
      <Stack gap={2} sx={{ '@media print': { padding: 0 } }}>
        {floors.map((floor) => {
          const emitterDetailsForFloor = emitterDetailsByFloor[floor.uid] || [];
          return (
            <Stack
              key={floor.uid}
              sx={{
                '@media print': {
                  display: 'block',
                },
              }}
            >
              <RenderEmitterTableForFloor
                key={floor.uid}
                floor={floor}
                emitterDetails={emitterDetailsForFloor}
                selectedEmitterType={emitterType}
              />
            </Stack>
          );
        })}
        {emitterType === EmitterType.RADIATOR && showNote && (
          <Typography variant="body2" mt={2}>
            * <FormattedMessage id="heatDesign.report.roomDetails.emitterDetails.note" />
          </Typography>
        )}
      </Stack>
    </Stack>
  );
}

export type EmitterOverviewProps = {
  showUnderfloorHeatingDisclaimer?: boolean;
};

export default function EmitterOverview({ showUnderfloorHeatingDisclaimer = false }: EmitterOverviewProps) {
  const intl = useIntl();
  const floors = useFloors();
  const roomsByFloor = useGetRoomsByFloor();
  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const flowTemperature = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const radiatorSummary = useRadiatorSummary();

  const [selectedEmitterType, setSelectedEmitterType] = useState<EmitterType>(EmitterType.RADIATOR);

  if (selectedEmitterType !== EmitterType.RADIATOR && selectedEmitterType !== EmitterType.UNDERFLOOR) {
    throw new Error(`Unknown/Unsupported emitter type: ${selectedEmitterType}`);
  }

  const underfloorHeatingDetailsByFloor = calculateUnderfloorHeatingEmitterDetailsByFloor(
    intl,
    floors,
    roomsByFloor,
    roomHeatDesignResults,
    flowTemperature,
    flowReturnDeltaT,
  );

  const underfloorHeatingEmitterDetails = floors
    .flatMap((floor) => underfloorHeatingDetailsByFloor[floor.uid])
    .filter(Boolean) as UnderfloorHeatingEmitterOverviewDetails[];

  const underfloorHeatingEmpty = underfloorHeatingEmitterDetails.length === 0;
  const areRadiatorsEmpty = Object.values(radiatorSummary.radiatorDetailsByFloor).flat().length === 0;
  if (underfloorHeatingEmpty && areRadiatorsEmpty) {
    return (
      <Heading level={4} variant="headline4">
        <FormattedMessage id="heatDesign.emitterList.noEmitters" />
      </Heading>
    );
  }

  // If one of them are empty set soleEmitterType to the other
  let soleEmitterType;

  if (!areRadiatorsEmpty && underfloorHeatingEmpty) {
    soleEmitterType = EmitterType.RADIATOR;
  } else if (!underfloorHeatingEmpty && areRadiatorsEmpty) {
    soleEmitterType = EmitterType.UNDERFLOOR;
  }
  if (soleEmitterType && selectedEmitterType !== soleEmitterType) {
    setSelectedEmitterType(soleEmitterType);
  }

  const radiatorTranslationIds = getMessageIdForEmitterType(EmitterType.RADIATOR);
  const underfloorTranslationIds = getMessageIdForEmitterType(EmitterType.UNDERFLOOR);

  const underfloorHeatingEmitterDetailsSummary = calculateEmitterDetailsSummary(
    underfloorHeatingEmitterDetails,
    UNDERFLOOR_HEATING_FLOW_TEMP,
    flowReturnDeltaT,
  );

  return (
    <Stack direction="column" justifyContent="space-between" spacing={2}>
      {!soleEmitterType && (
        <>
          <Box
            borderRadius={2}
            width="100%"
            p={2}
            sx={{
              backgroundColor: beige[200],
              '@media print': {
                background: 'white',
                border: `1px solid ${grey[500]}`,
              },
            }}
          >
            <InfoBlock
              id="heatDesign-emitterList-summary-totalFlowNeededForHouse"
              icon={<Wave1HorizontalCircleIcon />}
              value={`${toOneDecimalPlaceText(radiatorSummary.totalFlowNeeded + underfloorHeatingEmitterDetailsSummary.totalFlowNeeded)} l/h`}
              label={<FormattedMessage id="heatDesign.emitterList.summary.totalFlowNeededForHouse" />}
            />
          </Box>
          <Stack
            direction="row"
            spacing={2}
            width="100%"
            sx={{
              '@media print': {
                display: 'none',
              },
            }}
          >
            {Object.values(EmitterType).map((type) => {
              const isSelected = selectedEmitterType === type;
              return (
                <Stack
                  direction="row"
                  alignItems="center"
                  key={type}
                  borderRadius={4}
                  px={isSelected ? 1 : 2}
                  py={1}
                  sx={{
                    background: beige[200],
                    cursor: 'pointer',
                    '@media print': {
                      display: 'none',
                    },
                  }}
                  onClick={() => setSelectedEmitterType(type)}
                >
                  {isSelected && <CheckCircleFilledIcon color={beige[400]} />}
                  <Typography variant={isSelected ? 'body1Emphasis' : 'body1'}>
                    <FormattedMessage id={`heatDesign.emitterList.emitterType.${type}`} />
                  </Typography>
                </Stack>
              );
            })}
          </Stack>
        </>
      )}
      <Box
        sx={{
          display: selectedEmitterType === EmitterType.RADIATOR ? 'block' : 'none',
          '@media print': {
            display: areRadiatorsEmpty ? 'none' : 'block',
          },
        }}
      >
        <EmitterOverviewSummaryAndDetails
          emitterDetailsSummary={{
            totalFlowNeeded: radiatorSummary.totalFlowNeeded,
            actualCircuitReturnTemperature: radiatorSummary.actualCircuitReturnTemperature,
          }}
          emitterDetailsByFloor={radiatorSummary.radiatorDetailsByFloor}
          floors={floors}
          flowTemp={flowTemperature}
          showUnderfloorHeatingDisclaimer={showUnderfloorHeatingDisclaimer}
          emitterType={EmitterType.RADIATOR}
          translationIds={radiatorTranslationIds}
          onlyOneEmitter={soleEmitterType !== undefined}
        />
      </Box>
      <Box
        sx={{
          display: selectedEmitterType === EmitterType.UNDERFLOOR ? 'block' : 'none',
          '@media print': {
            pageBreakBefore: areRadiatorsEmpty ? 'auto' : 'always',
            display: underfloorHeatingEmpty ? 'none' : 'block',
          },
        }}
      >
        <EmitterOverviewSummaryAndDetails
          emitterDetailsSummary={underfloorHeatingEmitterDetailsSummary}
          emitterDetailsByFloor={underfloorHeatingDetailsByFloor}
          floors={floors}
          flowTemp={UNDERFLOOR_HEATING_FLOW_TEMP}
          showUnderfloorHeatingDisclaimer={showUnderfloorHeatingDisclaimer}
          emitterType={EmitterType.UNDERFLOOR}
          translationIds={underfloorTranslationIds}
          onlyOneEmitter={soleEmitterType !== undefined}
        />
      </Box>
    </Stack>
  );
}
