import { Box, Drawer, IconButton, Tooltip, useMediaQuery } from '@mui/material';
import { useState } from 'react';
import TechnicalSurvey from '../technical-survey/TechnicalSurvey';
import { ChevronLeft, ChevronRight } from '@ui/components/Icons/Chevron/Chevron';

export function TechnicalSurveySidebar() {
  const isMobile = useMediaQuery('(max-width: 700px)');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const drawerWidth = 400;

  return (
    <>
      {!isMobile && (
        <Tooltip title={sidebarOpen ? 'Hide technical survey' : 'Show technical survey'} placement="left">
          <IconButton
            onClick={toggleSidebar}
            sx={{
              position: 'fixed',
              right: sidebarOpen ? drawerWidth : 0,
              top: '50%',
              zIndex: 1200,
              backgroundColor: 'background.paper',
              border: '1px solid',
              borderColor: 'divider',
              boxShadow: 2,
              '&:hover': {
                backgroundColor: 'action.hover',
              },
              transition: 'right 225ms cubic-bezier(0, 0, 0.2, 1) 0ms',
            }}
            size="small"
          >
            {sidebarOpen ? <ChevronRight /> : <ChevronLeft />}
          </IconButton>
        </Tooltip>
      )}
      <Drawer
        variant="persistent"
        anchor="right"
        open={sidebarOpen}
        sx={{
          // width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            height: '100%',
            top: 0,
            zIndex: 1100,
          },
          displayPrint: 'none',
        }}
      >
        <Box sx={{ height: '100%', overflow: 'auto', p: 2 }}>
          <TechnicalSurvey />
        </Box>
      </Drawer>
    </>
  );
}
