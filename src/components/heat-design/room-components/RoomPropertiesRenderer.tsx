import { MenuItem, SelectChangeEvent, Stack } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { useIntl } from 'react-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { useRoomsActions } from '../stores/RoomsStore';
import AvgAirChangesPerHour from './AvgAirChangesPerHour';
import { OPEN_FLUE_TYPES, Room, ROOM_TYPES, RoomType } from '../stores/types';
import DesignRoomTemp from './DesignRoomTemp';
import { propertyIsValid } from '../Validator';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { toTwoDecimalPlaces } from '../utils/helpers';
import { calculateVolume } from '../utils/calculations';
import useSliderDebounce from '../hooks/useDebounce';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { FormHelperText } from '@ui/components/FormHelperText/FormHelperText';

type Props = {
  room: Room;
};

function RoomPropertiesRenderer({ room }: Props) {
  const intl = useIntl();
  const roomsActions = useRoomsActions();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);

  const isPulseTest =
    ventilationDesign.calculationMethod === 'standardVentilation' &&
    ventilationDesign.airPermeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST;

  const debouncedSliderInteractionEvent = useSliderDebounce(
    () =>
      sendGTMEvent({
        event: 'slider_interaction',
        slider_name: 'design room temperature',
        room: room.name,
      }),
    [room.name],
  );

  if (!room) return null;

  const { name, roomType, openFlue } = room;

  const handleOpenFlueTypeChange = (e: any) => {
    roomsActions.updateRoomById(room.id, { openFlue: e.target.value, avgAirChangesPerHourOverride: undefined });
  };

  const handleAverageHeightChange = (averageHeight: number) => {
    roomsActions.updateRoomById(room.id, {
      averageHeight,
      totalVolume: calculateVolume(room.totalArea, averageHeight),
      // If openFlue is not none, clear the ACPH override.
      // Changing the height changes the volume which affects the ACPH calculation when openFlue is not none.
      avgAirChangesPerHourOverride: room.openFlue !== 'none' ? undefined : room.avgAirChangesPerHourOverride,
    });
  };

  const handleDesignRoomTempChange = (newTemp: number) => {
    roomsActions.updateRoomById(room.id, { designRoomTempOverride: newTemp });
    debouncedSliderInteractionEvent();
  };

  const handleRoomTypeChange = (e: SelectChangeEvent) => {
    roomsActions.updateRoomById(room.id, {
      roomType: e.target.value as RoomType,
      // Clear the room temp override
      designRoomTempOverride: undefined,
      // Clear the ACPH override
      // Changing the room type may change the default ACPH value.
      avgAirChangesPerHourOverride: undefined,
    });
  };

  return (
    <Stack gap={3}>
      <Stack>
        <TextField
          label={intl.formatMessage({ id: 'heatDesign.room.roomName' })}
          name="name"
          value={name}
          size="small"
          onChange={(e: { target: { value: string } }) =>
            roomsActions.updateRoomById(room.id, { name: e.target.value as string })
          }
          error={!propertyIsValid('room', 'name', name)}
        />
      </Stack>
      <Stack>
        <Select
          name="roomType"
          id="roomType"
          label={intl.formatMessage({ id: 'heatDesign.room.roomType' })}
          value={roomType}
          size="small"
          error={!propertyIsValid('room', 'roomType', roomType)}
          onChange={handleRoomTypeChange}
        >
          {ROOM_TYPES.map((rt) => (
            <MenuItem key={rt} value={rt}>
              {rt}
            </MenuItem>
          ))}
        </Select>
      </Stack>
      <DesignRoomTemp room={room} updateDesignRoomTempOverride={handleDesignRoomTempChange} />
      <Stack>
        <Select
          name="openFlue"
          disabled={isPulseTest}
          label={intl.formatMessage({ id: 'heatDesign.room.openFlue' })}
          value={openFlue}
          size="small"
          error={!propertyIsValid('room', 'openFlue', openFlue)}
          onChange={handleOpenFlueTypeChange}
        >
          {OPEN_FLUE_TYPES.map((oft) => (
            <MenuItem key={oft} value={oft}>
              {oft}
            </MenuItem>
          ))}
        </Select>
        {isPulseTest && (
          <FormHelperText
            helperText={intl.formatMessage({
              id: 'heatDesign.room.openFlue.disabledByPulseTest',
            })}
          />
        )}
      </Stack>
      <AvgAirChangesPerHour room={room} />
      <NumericTextField
        name="highCeiling"
        label={intl.formatMessage({ id: 'heatDesign.room.averageCeilingHeight' })}
        size="small"
        suffix="m"
        value={toTwoDecimalPlaces(room.averageHeight)}
        error={!propertyIsValid('room', 'averageHeight', room?.averageHeight)}
        onChange={handleAverageHeightChange}
      />
    </Stack>
  );
}

export default RoomPropertiesRenderer;
