import { FormattedMessage, useIntl } from 'react-intl';
import { Stack } from '@mui/system';
import { Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { SOIL_PERCENTAGE_VALUES } from '../constants';
import { ExternalWall, Room } from '../stores/types';
import { useRoomsActions } from '../stores/RoomsStore';
import { useFloors } from '../stores/FloorsStore';

type Props = {
  room: Room;
  wallUid: string;
};

export default function ExternalWallSoilForm({ room, wallUid }: Props) {
  const { formatMessage } = useIntl();
  const allFloors = useFloors();
  const thisFloor = allFloors.find((f) => f.uid === room.floorId);

  const roomActions = useRoomsActions();
  const thisWall = room.surfaces.walls.find((w) => w.uid === wallUid);

  if (!thisFloor) return null;
  if (!thisWall) return null;
  if (thisWall.surfaceType !== 'externalWalls') return null;

  const updateSoilPercentage = (soilPercentage: ExternalWall['soilPercentage']) => () => {
    const updatedWalls = room.surfaces.walls.map((wall) => {
      if (thisWall.uid === wall.uid) {
        return {
          ...wall,
          soilPercentage,
        };
      }
      return wall;
    });

    roomActions.updateRoomById(room.id, {
      surfaces: {
        ...room.surfaces,
        walls: updatedWalls,
      },
    });
  };

  const soilValue = thisWall.soilPercentage ?? thisFloor.soilPercentageDefault;
  const soilValueOverridesFloorSoilValue = thisWall.soilPercentage != null;

  return (
    <Stack>
      <Typography variant="inputLabel" component="label" mb={1} display="flex" gap={1} alignItems="center">
        <FormattedMessage id="heatDesign.wallsRenderer.soilPercentage.label" />
        <TooltipAira
          placement="top-start"
          title={formatMessage({
            id: 'heatDesign.wallsRenderer.soilPercentage.tooltip',
          })}
        />
      </Typography>
      <Stack direction="row" gap={1}>
        {SOIL_PERCENTAGE_VALUES.map((value) => (
          <Button
            size="small"
            label={`${value}%`}
            key={value}
            variant={value === soilValue ? 'contained' : 'outlined'}
            onClick={updateSoilPercentage(value)}
          />
        ))}
      </Stack>
      <Stack mt={1} direction="row">
        <Button
          size="small"
          disabled={!soilValueOverridesFloorSoilValue}
          variant="outlined"
          label={formatMessage({ id: 'heatDesign.wallsRenderer.soilPercentage.resetToDefaults' })}
          onClick={updateSoilPercentage(null)}
        />
      </Stack>
    </Stack>
  );
}
