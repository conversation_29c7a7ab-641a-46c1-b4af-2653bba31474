import { ReactNode } from 'react';
import { Box, MenuItem, Stack } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { FormattedMessage, useIntl } from 'react-intl';
import { useRoomsActions } from '../stores/RoomsStore';
import {
  BelowFloorType,
  Floor,
  HorizontalSurfaceType,
  RoofOrCeiling,
  Room,
  SPACE_ABOVE_TYPES,
  AdjacentKind,
  BELOW_FLOOR_TYPES,
  SpaceAboveType,
} from '../stores/types';
import { propertyIsValid, tempOfSpaceAboveIsValid, typeOfSpaceAboveIsValid } from '../Validator';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { useSelectFloorForRoom } from '../stores/FloorsStore';
import SurfaceUValueSelect from './SurfaceUValueSelect';
import { ClimateDataStore, useClimateDataStore } from '../stores/ClimateDataStore';
import { useAdjustedOutdoorDesignTemperature } from '../stores/HouseInputsStore';
import { getUnheatedRoomTemperature } from '../utils/heatCalculations';

function belowFloorTypeToLabel(
  belowFloor: BelowFloorType,
  adjustedOutdoorDesignTemperature: number,
  unheatedRoomTemperature: number,
  climateDataStore: ClimateDataStore,
): ReactNode {
  switch (belowFloor) {
    case AdjacentKind.SolidFloor:
      return (
        <FormattedMessage
          id="heatDesign.room.belowFloorLabel.solidFloor"
          values={{ temp: climateDataStore.localAnnualAverageExternalAirTemperature }}
        />
      );
    case AdjacentKind.SuspendedFloor:
      return (
        <FormattedMessage
          id="heatDesign.room.belowFloorLabel.suspendedFloor"
          values={{ temp: adjustedOutdoorDesignTemperature }}
        />
      );
    case AdjacentKind.Heated:
      return <FormattedMessage id="heatDesign.room.belowFloorLabel.heatedRoom" />;
    case AdjacentKind.Unheated:
      return (
        <FormattedMessage
          id="heatDesign.room.belowFloorLabel.unheatedRoom"
          values={{ temp: unheatedRoomTemperature }}
        />
      );
    default:
      return belowFloor;
  }
}

type Props = {
  room: Room;
  surfaceType: HorizontalSurfaceType;
};

export default function HorizontalSurfaceRenderer({ room, surfaceType }: Props) {
  const intl = useIntl();
  const roomActions = useRoomsActions();
  const floor = useSelectFloorForRoom(room);
  const adjustedOutdoorDesignTemperature = useAdjustedOutdoorDesignTemperature();
  const climateDataStore = useClimateDataStore();
  const unheatedRoomTemperature = getUnheatedRoomTemperature(climateDataStore);
  if (!room || !floor) return null;
  const fabricArray = room.surfaces[surfaceType] as RoofOrCeiling[] | Floor[];

  return (
    <Stack>
      <Stack>
        {fabricArray?.map((surface) => (
          <Stack key={surface.uid}>
            <Box key={surface.uid}>
              <Stack
                direction="row"
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, 120px)',
                  gridGap: '0.5rem',
                  justifyContent: 'space-between',
                  alignItems: 'flex-end',
                }}
              >
                {surface.surfaceType === 'roofsOrCeilings' && (
                  <Select
                    name={`typeOfSpaceAbove + ${surface.uid}`}
                    key={`typeOfSpaceAbove + ${surface.uid}`}
                    label={intl.formatMessage({ id: 'heatDesign.room.typeOfSpaceAbove' })}
                    size="small"
                    value={surface.spaceAbove.type}
                    error={!typeOfSpaceAboveIsValid(surface.spaceAbove)}
                    inputProps={{ 'data-testid': 'heat-design-room-typeOfSpaceAbove' }}
                    onChange={(e) => {
                      const { value } = e.target as { value: SpaceAboveType };

                      const newDimensions = fabricArray
                        .filter((rd) => rd.surfaceType === 'roofsOrCeilings')
                        .map((rd) =>
                          rd.uid === surface.uid ? { ...rd, spaceAbove: { ...rd.spaceAbove, type: value } } : rd,
                        ) satisfies RoofOrCeiling[];
                      roomActions.updateRoom({
                        ...room,
                        surfaces: {
                          ...room.surfaces,
                          roofsOrCeilings: newDimensions,
                        },
                      });
                    }}
                  >
                    {SPACE_ABOVE_TYPES.map((type) => (
                      <MenuItem key={type} value={type}>
                        <FormattedMessage id={`heatDesign.adjacentKind.${type}`} />
                      </MenuItem>
                    ))}
                  </Select>
                )}

                {surface.surfaceType === 'roofsOrCeilings' && surface.spaceAbove.type === AdjacentKind.Heated && (
                  <NumericTextField
                    name={`tempOfSpaceAbove + ${surface.uid}`}
                    label={intl.formatMessage({ id: 'heatDesign.room.tempOfSpaceAbove' })}
                    size="small"
                    suffix="°C"
                    inputProps={{ 'data-testid': 'heat-design-room-tempOfSpaceAbove' }}
                    value={surface.spaceAbove.tempOfSpaceAbove ?? 0}
                    error={!tempOfSpaceAboveIsValid(surface.spaceAbove)}
                    onChange={(tempOfSpaceAbove: number) => {
                      const newDimensions = fabricArray
                        .filter((rd) => rd.surfaceType === 'roofsOrCeilings')
                        .map((rd) =>
                          rd.uid === surface.uid
                            ? { ...rd, spaceAbove: { type: AdjacentKind.Heated, tempOfSpaceAbove } }
                            : rd,
                        ) satisfies RoofOrCeiling[];
                      roomActions.updateRoom({
                        ...room,
                        surfaces: {
                          ...room.surfaces,
                          roofsOrCeilings: newDimensions,
                        },
                      });
                    }}
                  />
                )}
              </Stack>
              {surface.surfaceType === 'floors' && (
                <Stack>
                  <Select
                    name={`belowFloor + ${surface.uid}`}
                    key={`belowFloor + ${surface.uid}`}
                    label={intl.formatMessage({ id: 'heatDesign.room.whatIsBelowTheFloor' })}
                    toolTip="heatDesign.roomDetails.belowFloor.tooltip"
                    size="small"
                    inputProps={{ 'data-testid': 'heat-design-room-belowFloor' }}
                    value={surface.belowFloor}
                    error={!propertyIsValid('floors', 'belowFloor', surface.belowFloor)}
                    onChange={(e) => {
                      const { value } = e.target as { value: BelowFloorType };
                      const newDimensions = fabricArray.map((rd) =>
                        rd.uid === surface.uid ? { ...rd, belowFloor: value } : rd,
                      );
                      roomActions.updateRoom({
                        ...room,
                        surfaces: {
                          ...room.surfaces,
                          [surfaceType]: newDimensions,
                        },
                      });
                    }}
                  >
                    {BELOW_FLOOR_TYPES.map((type) => (
                      <MenuItem key={type} value={type}>
                        {belowFloorTypeToLabel(
                          type,
                          adjustedOutdoorDesignTemperature,
                          unheatedRoomTemperature,
                          climateDataStore,
                        )}
                      </MenuItem>
                    ))}
                  </Select>
                </Stack>
              )}
            </Box>
            <Stack direction="column" spacing={2} pt={2} pb={3}>
              <SurfaceUValueSelect
                surface={surface}
                room={room}
                onChange={(uValue) => {
                  const newDimensions = fabricArray.map((rd) => (rd.uid === surface.uid ? { ...rd, uValue } : rd));
                  roomActions.updateRoom({
                    ...room,
                    surfaces: {
                      ...room.surfaces,
                      [surfaceType]: newDimensions,
                    },
                  });
                }}
              />
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Stack>
  );
}
