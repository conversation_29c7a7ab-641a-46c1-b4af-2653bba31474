import { Checkbox, InputLabel, Slider, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { ClickableTooltipAira } from '@ui/components/Tooltip/Tooltip';
import { useGroundwork } from 'context/groundwork-context';
import { getDesignRoomTemp } from '../utils/heatCalculations';
import { Room, ROOM_TEMPERATURES } from '../stores/types';
import { useRoomsActions } from '../stores/RoomsStore';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useClimateDataStore } from '../stores/ClimateDataStore';

export default function DesignRoomTemp({
  room,
  updateDesignRoomTempOverride,
}: {
  room: Room;
  updateDesignRoomTempOverride: (designRoomTemp: number) => void;
}) {
  const { formatMessage } = useIntl();
  const roomsActions = useRoomsActions();
  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const { isHeated, id } = room;
  const climateDataStore = useClimateDataStore();

  const designRoomTemp = getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore);

  return (
    <Stack>
      <Typography variant="inputLabel">
        <FormattedMessage id="heatDesign.room.designRoomTemp" />
      </Typography>
      {isHeated ? (
        <Slider
          aria-label="Room temperature"
          disabled={!isHeated}
          value={designRoomTemp}
          valueLabelFormat={`${designRoomTemp}°C`}
          onChange={(_, val) => updateDesignRoomTempOverride(val as number)}
          min={Math.min(...ROOM_TEMPERATURES)}
          max={Math.max(...ROOM_TEMPERATURES)}
          step={1}
          valueLabelDisplay="auto"
          marks={ROOM_TEMPERATURES.filter((temp) => temp % 2 === 0).map((temp) => ({
            value: temp,
            label: temp,
          }))}
          data-testid="heat-loss-room-editor-design-temp-slider"
        />
      ) : (
        <Typography mt={1} variant="body2" data-testid="heat-loss-room-editor-design-temp-unheated-label">
          <FormattedMessage id="heatDesign.room.designRoomUnheated" values={{ temperature: designRoomTemp }} />
        </Typography>
      )}
      <Stack direction="row" alignItems="center" justifyContent="start" gap={1}>
        <InputLabel htmlFor="roomIsHeated" sx={{ mb: 0 }}>
          <Typography variant="inputLabel">
            <FormattedMessage id="heatDesign.room.heatedRoom" />
          </Typography>
        </InputLabel>
        <Checkbox
          name="roomIsHeated"
          checked={isHeated}
          onChange={(e: any) => {
            roomsActions.updateRoomById(id, { isHeated: e.target.checked });
          }}
          inputProps={
            {
              'data-testid': `heat-loss-room-editor-design-temp-is-heated-checkbox`,
            } as React.InputHTMLAttributes<HTMLInputElement>
          }
        />
        <ClickableTooltipAira
          placement="top"
          title={formatMessage({
            id: 'heatDesign.room.heatedRoom.tooltip',
          })}
        />
      </Stack>
    </Stack>
  );
}
