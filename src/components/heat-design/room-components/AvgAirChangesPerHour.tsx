import { useIntl } from 'react-intl';
import { Stack } from '@mui/material';
import CloseOutlinedIcon from '@ui/components/Icons/material/CloseOutlined';
import { Room, VentilationCalculationMethod } from '../stores/types';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { getAverageAirChangesPerHour } from '../utils/averageAirChangePerHour';
import { useRoomsActions } from '../stores/RoomsStore';
import { propertyIsValid } from '../Validator';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { StyledFormattedMessage } from 'utils/localization';

function AvgAirChangePerHour({ room: currentRoom }: { room: Room }) {
  const intl = useIntl();
  const roomsActions = useRoomsActions();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const currentValue = getAverageAirChangesPerHour(currentRoom, ventilationDesign.acphDefault);

  const initialOverride = currentRoom.avgAirChangesPerHourOverride !== undefined;

  const updateAirChangesPerHour = (newValue: number) => {
    roomsActions.updateRoomById(currentRoom.id, { avgAirChangesPerHourOverride: newValue });
  };
  const handleDeleteAirChangesPerHour = () => {
    roomsActions.updateRoomById(currentRoom.id, { avgAirChangesPerHourOverride: undefined });
  };

  const showHelperText = !initialOverride;
  const isStandardVentilationMethod = ventilationDesign.calculationMethod == VentilationCalculationMethod.STANDARD;
  const isPulseTest =
    isStandardVentilationMethod &&
    ventilationDesign.airPermeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST;

  const getHelperText = () => {
    if (isPulseTest) {
      return intl.formatMessage({ id: 'heatDesign.room.avgAirChangesPerHour.disabledByPulseTest' });
    }
    const acphDefault = ventilationDesign.acphDefault;
    switch (acphDefault.type) {
      case 'standardized':
        return intl.formatMessage(
          {
            id: 'heatDesign.room.avgAirChangesPerHour.helperText.standardized',
          },
          { standard: acphDefault.standard },
        );
      case 'custom':
        return intl.formatMessage({
          id: 'heatDesign.room.avgAirChangesPerHour.helperText.custom',
        });
      default:
        return acphDefault satisfies never;
    }
  };

  return (
    <Stack>
      <NumericTextField
        name="avgAirChangesPerHour"
        label={intl.formatMessage({
          id: isStandardVentilationMethod
            ? 'heatDesign.room.referenceAirChangesPerHour'
            : 'heatDesign.room.avgAirChangesPerHour',
        })}
        tooltipLabel={<StyledFormattedMessage id="heatDesign.room.referenceAirChangesPerHourTooltip" />}
        size="small"
        suffix={intl.formatMessage({ id: 'heatDesign.room.avgAirChangesPerHourSuffix' })}
        value={currentValue}
        disabled={isPulseTest}
        onChange={updateAirChangesPerHour}
        error={!propertyIsValid('room', 'avgAirChangesPerHourOverride', currentValue)}
        {...(showHelperText && {
          helperText: getHelperText(),
        })}
        icon={
          initialOverride ? (
            <CloseOutlinedIcon fontSize="small" sx={{ cursor: 'pointer' }} onClick={handleDeleteAirChangesPerHour} />
          ) : undefined
        }
      />
    </Stack>
  );
}

export default AvgAirChangePerHour;
