import { Box, Stack } from '@mui/material';
import { TextField } from '@ui/components/TextField/TextField';
import { FormattedMessage, useIntl } from 'react-intl';
import { useRoomsActions } from '../stores/RoomsStore';
import { Door, Room, Window } from '../stores/types';
import { doorOrWindowIsIncludedInHeatLossCalculation, toTwoDecimalPlaces } from '../utils/helpers';
import { useSelectFloorForRoom } from '../stores/FloorsStore';
import SurfaceUValueSelect from './SurfaceUValueSelect';

function DoorsWindowsRenderer({
  room,
  fabricType,
  fabricId,
}: {
  room: Room;
  fabricType: 'doors' | 'windows';
  fabricId: string;
}) {
  const intl = useIntl();
  const floor = useSelectFloorForRoom(room);

  const roomActions = useRoomsActions();
  if (!room || !floor) return null;
  const fabricArray = room.surfaces[fabricType] as Door[] | Window[];
  const fabric = fabricArray.find((f) => f.uid === fabricId);
  if (!fabric) return null;

  const includedInCalculations = doorOrWindowIsIncludedInHeatLossCalculation(fabric, room);

  if (!fabric) return null;

  return (
    <Stack key={fabric.uid}>
      {!includedInCalculations && (
        <Box sx={{ mt: 2 }}>
          <FormattedMessage id="heatDesign.roomEditor.internalDoorsAndWindowsNotIncluded" />
        </Box>
      )}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, 120px)',
          gridGap: '0.5rem',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
        }}
        key={fabric.uid}
      >
        <TextField
          name={`width + ${fabric.uid}`}
          key={`width + ${fabric.uid}`}
          disabled
          type="number"
          label={intl.formatMessage({ id: 'common.label.measurement.width' })}
          size="small"
          value={toTwoDecimalPlaces(fabric.width)}
          onChange={() => {}}
        />
        <TextField
          name={`height + ${fabric.uid}`}
          key={`height + ${fabric.uid}`}
          type="number"
          label={intl.formatMessage({ id: 'common.label.measurement.height' })}
          size="small"
          disabled
          value={toTwoDecimalPlaces(fabric.height)}
          onChange={() => {}}
        />
        <TextField
          name={`area + ${fabric.uid}`}
          key={`area + ${fabric.uid}`}
          type="number"
          label={intl.formatMessage({ id: 'common.label.measurement.area' })}
          size="small"
          disabled
          value={toTwoDecimalPlaces(fabric.area)}
          onChange={() => {}}
        />
      </Box>
      <Stack direction="column" pt={2} pb={3}>
        <SurfaceUValueSelect
          surface={fabric}
          room={room}
          onChange={(uValue) => {
            const updatedRoom = {
              ...room,
              surfaces: {
                ...room.surfaces,
                [fabricType]: fabricArray.map((f) => {
                  if (f.uid === fabric.uid) {
                    return {
                      ...f,
                      uValue,
                    };
                  }
                  return f;
                }),
              },
            };
            roomActions.updateRoom(updatedRoom);
          }}
        />
      </Stack>
    </Stack>
  );
}

export default DoorsWindowsRenderer;
