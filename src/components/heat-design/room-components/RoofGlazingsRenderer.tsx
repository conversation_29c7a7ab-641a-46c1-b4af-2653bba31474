import { Box, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { FormattedMessage, useIntl } from 'react-intl';
import { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useRoomsActions } from '../stores/RoomsStore';
import { RoofGlazing, Room } from '../stores/types';
import { propertyIsValid } from '../Validator';
import { useSelectFloorForRoom } from '../stores/FloorsStore';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import SurfaceUValueSelect from './SurfaceUValueSelect';
import { roofGlazingArea } from '../utils/calculations';
import { UValue } from '../models/UValue';

function RoofGlazingPropertiesEditor({
  glazing,
  room,
  handleUpdateLength,
  handleUpdateWidth,
  handleSaveRoofGlazing,
  handleDeleteRoofGlazing,
  handleSetUValue,
}: {
  glazing: RoofGlazing;
  room: Room;
  handleUpdateLength: (value: number) => void;
  handleUpdateWidth: (value: number) => void;
  handleSaveRoofGlazing?: () => void;
  handleDeleteRoofGlazing: () => void;
  handleSetUValue: (uValue?: UValue) => void;
}) {
  const intl = useIntl();

  return (
    <Stack spacing={2}>
      <SurfaceUValueSelect
        surface={glazing}
        room={room}
        onChange={(uValue) => {
          handleSetUValue(uValue);
        }}
      />
      <Stack>
        <Typography variant="body2">
          <FormattedMessage id="heatDesign.RoofGlazingsRenderer.UVAlueAngleCompensation" />
        </Typography>
      </Stack>
      <Stack
        key={glazing.uid}
        direction="column"
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, 120px)',
          gridGap: '0.5rem',
          justifyContent: 'space-between',
        }}
      >
        <NumericTextField
          label={intl.formatMessage({ id: 'common.label.measurement.length' })}
          name={`length-${glazing.uid}`}
          key={`length-${glazing.uid}`}
          type="number"
          size="small"
          suffix="m"
          value={glazing.length ?? 0}
          error={!propertyIsValid('roofGlazings', 'length', glazing?.length)}
          onChange={(value) => handleUpdateLength(value)}
        />
        <NumericTextField
          label={intl.formatMessage({ id: 'common.label.measurement.width' })}
          name={`width-${glazing.uid}`}
          key={`width-${glazing.uid}`}
          type="number"
          size="small"
          suffix="m"
          value={glazing.width ?? 0}
          error={!propertyIsValid('roofGlazings', 'width', glazing?.width)}
          onChange={(value) => handleUpdateWidth(value)}
        />
        <NumericTextField
          label={intl.formatMessage({ id: 'common.label.measurement.area' })}
          name={`area-${glazing.uid}`}
          key={`area-${glazing.uid}`}
          size="small"
          type="number"
          value={roofGlazingArea(glazing)}
          suffix="m²"
          disabled
          onChange={() => {}} // disabled
        />
      </Stack>
      <Stack direction="row" spacing={2}>
        {handleSaveRoofGlazing && (
          <Button
            variant="contained"
            size="small"
            onClick={handleSaveRoofGlazing}
            label={intl.formatMessage({ id: 'common.label.save' })}
            data-testid="save-roof-glazing"
          />
        )}
        <Button
          variant="outlined"
          size="small"
          label={intl.formatMessage({ id: 'common.label.delete' })}
          onClick={handleDeleteRoofGlazing}
        />
      </Stack>
    </Stack>
  );
}

export default function RoofGlazingsRenderer({ room }: { room: Room }) {
  const intl = useIntl();
  const [isAddingRoofGlazing, setIsAddingRoofGlazing] = useState(false);
  const [newRoofGlazing, setNewRoofGlazing] = useState<RoofGlazing>({
    uid: uuidv4(),
    length: 0,
    width: 0,
    surfaceType: 'roofGlazings',
  });
  const roomActions = useRoomsActions();
  const floor = useSelectFloorForRoom(room);
  if (!room || !floor) return null;
  const { roofGlazings } = room.surfaces;

  const handleAddRoofGlazing = (roofGlazing: RoofGlazing) => {
    const updatedRoom = {
      ...room,
      surfaces: {
        ...room.surfaces,
        roofGlazings: [...roofGlazings, roofGlazing],
      },
    };
    roomActions.updateRoom(updatedRoom);
  };

  const handleDeleteRoofGlazing = (id: string) => {
    const updatedRoom = {
      ...room,
      surfaces: {
        ...room.surfaces,
        roofGlazings: roofGlazings.filter((r) => r.uid !== id),
      },
    };
    roomActions.updateRoom(updatedRoom);
  };

  const updateRoofGlazings = (newGlazings: RoofGlazing[]) => {
    roomActions.updateRoom({
      ...room,
      surfaces: {
        ...room.surfaces,
        roofGlazings: newGlazings,
      },
    });
  };

  const handleUpdateLength = (id: string, value: number) => {
    updateRoofGlazings(
      roofGlazings.map((roofGlazing) => {
        if (roofGlazing.uid === id) {
          return {
            ...roofGlazing,
            length: value,
          };
        }
        return roofGlazing;
      }),
    );
  };

  const handleUpdateWidth = (id: string, value: number) => {
    updateRoofGlazings(
      roofGlazings.map((roofGlazing) => {
        if (roofGlazing.uid === id) {
          return {
            ...roofGlazing,
            width: value,
          };
        }
        return roofGlazing;
      }),
    );
  };

  const handleSetUValue = (id: string, uValue?: UValue) => {
    updateRoofGlazings(
      roofGlazings.map((roofGlazing) => {
        if (roofGlazing.uid === id) {
          return {
            ...roofGlazing,
            uValue,
          };
        }
        return roofGlazing;
      }),
    );
  };

  return (
    <Stack
      spacing={5}
      pb={4}
      sx={{
        overflowY: 'scroll',
        maxHeight: '60vh',
        backgroundColor: 'transparent',
      }}
    >
      {roofGlazings?.map((glazing, index) => (
        <Box key={glazing.uid} borderTop={index > 0 ? 2 : 0} pt={2}>
          <RoofGlazingPropertiesEditor
            glazing={glazing}
            room={room}
            handleUpdateLength={(value) => handleUpdateLength(glazing.uid, value)}
            handleUpdateWidth={(value) => handleUpdateWidth(glazing.uid, value)}
            handleDeleteRoofGlazing={() => handleDeleteRoofGlazing(glazing.uid)}
            handleSetUValue={(uValue) => handleSetUValue(glazing.uid, uValue)}
          />
        </Box>
      ))}
      {isAddingRoofGlazing ? (
        <Box
          pt={2}
          sx={{
            borderTop: roofGlazings.length > 0 ? 2 : 0,
          }}
        >
          <RoofGlazingPropertiesEditor
            glazing={newRoofGlazing}
            room={room}
            handleUpdateLength={(value) => setNewRoofGlazing({ ...newRoofGlazing, length: value })}
            handleUpdateWidth={(value) => setNewRoofGlazing({ ...newRoofGlazing, width: value })}
            handleSaveRoofGlazing={() => {
              const isValid =
                propertyIsValid('roofGlazings', 'length', newRoofGlazing.length) &&
                propertyIsValid('roofGlazings', 'width', newRoofGlazing.width);
              if (!isValid) return;
              handleAddRoofGlazing(newRoofGlazing);
              setNewRoofGlazing({ uid: uuidv4(), length: 0, width: 0, surfaceType: 'roofGlazings' });
              setIsAddingRoofGlazing(false);
            }}
            handleDeleteRoofGlazing={() => {
              setNewRoofGlazing({ ...newRoofGlazing, length: 0, width: 0, uValue: undefined });
              setIsAddingRoofGlazing(false);
            }}
            handleSetUValue={(uValue) => setNewRoofGlazing({ ...newRoofGlazing, uValue })}
          />
        </Box>
      ) : (
        <Box minHeight={40}>
          <Button
            size="small"
            onClick={() => setIsAddingRoofGlazing(true)}
            label={intl.formatMessage({ id: 'heatDesign.RoofGlazingsRenderer.addRoofGlazing' })}
          />
        </Box>
      )}
    </Stack>
  );
}
