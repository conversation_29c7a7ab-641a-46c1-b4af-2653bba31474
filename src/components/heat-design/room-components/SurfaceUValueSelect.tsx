import { Typography, Stack } from '@mui/material';
import { useIntl } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import { Room, RoomFabric } from '../stores/types';
import { UValueSource, UValueWithSource, lookupUValueForSurfaceType } from '../utils/helpers';
import { useFloors, useSelectFloorForRoom } from '../stores/FloorsStore';
import { useProjectUValues } from '../stores/UValuesStore';
import { UValueInput } from '../components/u-value-input/UValueInput';
import { UValue } from '../models/UValue';

export type SurfaceUValueSelectProps = {
  surface: RoomFabric;
  room: Room;
  onChange: (uValue?: UValue) => void;
};

export default function SurfaceUValueSelect({ surface, room, onChange }: SurfaceUValueSelectProps) {
  const intl = useIntl();
  const floors = useFloors();
  const projectUValues = useProjectUValues();
  const floor = useSelectFloorForRoom(room);

  if (!floor) return null;

  const uValueWithSource: UValueWithSource | undefined = surface.uValue
    ? { uValue: surface.uValue, source: UValueSource.surface }
    : lookupUValueForSurfaceType(surface.surfaceType, projectUValues, floor, floors);

  return (
    <Stack>
      <UValueInput
        surfaceType={surface.surfaceType}
        placeholder={intl.formatMessage({ id: 'heatDesign.uValues.placeholder' })}
        value={uValueWithSource?.uValue}
        label={intl.formatMessage({ id: 'heatDesign.uValues.uValue' })}
        error={!uValueWithSource}
        onChange={onChange}
        data-testid={`heat-design-uvalue-modal-select-${surface.surfaceType}`}
        inputProps={{ 'data-testid': `heat-design-uvalue-modal-input-${surface.surfaceType}` }}
      />
      {uValueWithSource && uValueWithSource.source !== 'surface' && (
        <Typography variant="body2" mt={0.5}>
          <StyledFormattedMessage id={`heatDesign.usingDefaultUValue.${uValueWithSource.source}`} />
        </Typography>
      )}
    </Stack>
  );
}
