import { Box, Stack, Table, TableBody, TableCell, TableFooter, TableHead, TableRow, Typography } from '@mui/material';
import { beige, grey, red } from '@ui/theme/colors';
import { theme } from '@ui/theme/theme';
import { useGroundwork } from 'context/groundwork-context';
import { MessageKey } from 'messageType';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { externalWallAreaAdjustmentFactor, THERMAL_BRIDGING_UVALUE_ADJUSTMENT } from '../constants';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { useDefaultFloor } from '../stores/FloorsStore';
import { increaseFactorToPercentage, toLocalisedDecimalPlaces } from '../utils/helpers';
import { CellsUnitsBold } from './TableCellBoldUnit';
import { FloorNavigation } from '../FloorNavigation';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { VentilationCalculationMethod } from '../stores/types';

interface ClauseDefinition {
  id: 'thermalBridging' | 'externalWallAdjustment' | 'effectiveAirChanges';
  messageId: MessageKey;
  values?: Record<string, string | number>;
  isEnabled: boolean;
}

function RoomOutputsRenderer() {
  const { locale } = useRouter();
  const { formatMessage } = useIntl();
  const { countryCode } = useGroundwork();
  const [selectedFloor, setSelectedFloor] = useState(useDefaultFloor());
  const { floorsResults } = useDwellingHeatDesignResult();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const isStandardVentilationMethod = ventilationDesign.calculationMethod === VentilationCalculationMethod.STANDARD;

  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);

  const sortedResults = roomHeatDesignResults
    .filter((r) => r.floorId === selectedFloor?.uid)
    .toSorted((a, b) => +b.isHeated - +a.isHeated || a.roomName.localeCompare(b.roomName));

  if (!roomHeatDesignResults.length) return null;
  const externalWallAreaAdjustment = externalWallAreaAdjustmentFactor(countryCode);
  const displayHelperText = roomHeatDesignResults.some((result) => result.averageHeight < 2);

  const clauseDefinitions: ClauseDefinition[] = [
    {
      id: 'thermalBridging',
      messageId: 'heatDesign.report.thermalBridgingIncludedInHeatLoss' satisfies MessageKey,
      values: { thermalBridgingAdjustment: THERMAL_BRIDGING_UVALUE_ADJUSTMENT },
      isEnabled: true,
    },
    {
      id: 'externalWallAdjustment',
      messageId: 'heatDesign.report.externalWallAdditionalSurfaceArea' satisfies MessageKey,
      values: externalWallAreaAdjustment
        ? { additionalAreaPercentage: increaseFactorToPercentage(externalWallAreaAdjustment) }
        : undefined,
      isEnabled: !!externalWallAreaAdjustment,
    },
    {
      id: 'effectiveAirChanges',
      messageId: 'heatDesign.report.clause.effectiveAirChangesPerHour' satisfies MessageKey,
      isEnabled: isStandardVentilationMethod,
    },
  ];

  const activeClauses = clauseDefinitions.filter((c) => c.isEnabled);

  const getClauseMarker = (id: ClauseDefinition['id']): string => {
    const index = activeClauses.findIndex((c) => c.id === id);
    if (index === -1) return '';
    return '*'.repeat(index + 1);
  };

  const thermalBridgingMarker = getClauseMarker('thermalBridging');
  const externalWallMarker = getClauseMarker('externalWallAdjustment');
  const effectiveAirChangesMarker = getClauseMarker('effectiveAirChanges');

  return (
    <Stack>
      <Stack direction="row" sx={{ gap: 2, mb: 2 }}>
        <FloorNavigation
          selectedFloor={selectedFloor}
          selectFloor={setSelectedFloor}
          displayFloorArea
          displayFloorWattage
        />
      </Stack>
      <Box style={{ overflowX: 'auto' }}>
        <Table stickyHeader sx={{ tableLayout: 'fixed', minWidth: '1024px' }}>
          <TableHead>
            <TableRow>
              <TableCell sx={{ borderTopLeftRadius: 16, backgroundColor: beige[200] }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.roomName' })}
              </TableCell>
              <TableCell align="right" sx={{ backgroundColor: beige[200] }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.floorArea' })}
              </TableCell>
              <TableCell align="right" sx={{ backgroundColor: beige[200] }}>
                {formatMessage({ id: 'heatDesign.tableHeaders.ceilingHeight' })}
              </TableCell>
              <CellsUnitsBold
                sx={{ backgroundColor: beige[200] }}
                title={formatMessage({ id: 'heatDesign.tableHeaders.wattPerSqM' })}
                unit="(W/m²)"
              />
              <CellsUnitsBold
                sx={{ backgroundColor: beige[200] }}
                title={formatMessage({ id: 'heatDesign.tableHeaders.roomHeatLoss' })}
                unit={`(W)${thermalBridgingMarker}`}
              />
              {Object.keys(sortedResults[0]?.surfaceTotals || []).map((surfaceType) => (
                <TableCell align="right" sx={{ backgroundColor: beige[200] }} key={surfaceType}>
                  {formatMessage({ id: `heatDesign.roomSurfaceTypes.${surfaceType}` as MessageKey })}
                  {surfaceType === 'externalWalls' && externalWallMarker}
                </TableCell>
              ))}
              <TableCell align="right" sx={{ backgroundColor: beige[200] }}>
                {formatMessage({
                  id: isStandardVentilationMethod
                    ? 'heatDesign.tableHeaders.effectiveAirChangesPerHour'
                    : 'heatDesign.room.avgAirChangesPerHourSuffix',
                })}
                {effectiveAirChangesMarker}
              </TableCell>
              <TableCell
                align="right"
                sx={{
                  borderTopRightRadius: 16,
                  backgroundColor: beige[200],
                }}
              >
                {formatMessage({ id: 'heatDesign.tableHeaders.ventilationHeatLoss' })}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody sx={{ background: beige[100] }}>
            {sortedResults.length === 0 && (
              <TableRow>
                <TableCell colSpan={6}>
                  <FormattedMessage id="heatDesign.error.noRoomsOnFloor" />
                </TableCell>
              </TableRow>
            )}
            {sortedResults.length !== 0 &&
              sortedResults.map((result) => (
                <TableRow
                  key={result.roomId}
                  sx={result.isHeated ? undefined : { backgroundColor: grey[200], td: { color: grey[700] } }}
                >
                  <TableCell>{result.roomName}</TableCell>
                  <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }} align="right">
                    {toLocalisedDecimalPlaces({ num: result.totalFloorArea, locale })}
                  </TableCell>
                  <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }} align="right">
                    {result.averageHeight < 2 ? (
                      <Typography sx={{ backgroundColor: red[200] }}>
                        {toLocalisedDecimalPlaces({ num: result.averageHeight, locale })}
                      </Typography>
                    ) : (
                      toLocalisedDecimalPlaces({ num: result.averageHeight, locale })
                    )}
                  </TableCell>
                  <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }} align="right">
                    {toLocalisedDecimalPlaces({ num: result.wattsPerMeterSquared, locale })}
                  </TableCell>
                  <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }} align="right">
                    {toLocalisedDecimalPlaces({
                      num: result.totalRoom.heatLoss,
                      decimalPlaces: 0,
                      locale,
                      optional: true,
                    })}
                  </TableCell>
                  {Object.keys(result.surfaceTotals).map((surfaceType) => (
                    <TableCell
                      sx={{ fontVariantNumeric: 'tabular-nums' }}
                      align="right"
                      key={surfaceType}
                      data-testid={`heat-design-${result.roomName}-output-${surfaceType}`}
                    >
                      {toLocalisedDecimalPlaces({
                        num: result.surfaceTotals[surfaceType as keyof typeof result.surfaceTotals].heatLoss,
                        decimalPlaces: 0,
                        locale,
                        optional: true,
                      })}
                    </TableCell>
                  ))}
                  <TableCell sx={{ fontVariantNumeric: 'tabular-nums' }} align="right">
                    {toLocalisedDecimalPlaces({
                      num: result.averageAirChangePerHour,
                      decimalPlaces: 2,
                      locale,
                    })}
                  </TableCell>
                  <TableCell
                    sx={{ fontVariantNumeric: 'tabular-nums' }}
                    align="right"
                    data-testid={`heat-design-${result.roomName}-output-ventilationHeatLoss`}
                  >
                    {toLocalisedDecimalPlaces({ num: result.ventilation.heatLoss, decimalPlaces: 0, locale })}
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                sx={{
                  backgroundColor: beige[200],
                  borderBottomLeftRadius: theme.spacing(2),
                  borderBottomRightRadius: theme.spacing(2),
                }}
                colSpan={15}
              >
                {displayHelperText && (
                  <Typography marginBottom={2} variant="body2">
                    <FormattedMessage id="heatDesign.roomOutput.helperText" />
                  </Typography>
                )}
                {activeClauses.map((clause) => (
                  <Typography variant="body2" key={clause.id}>
                    {getClauseMarker(clause.id)} <FormattedMessage id={clause.messageId} values={clause.values} />
                  </Typography>
                ))}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </Box>
    </Stack>
  );
}

export default RoomOutputsRenderer;
