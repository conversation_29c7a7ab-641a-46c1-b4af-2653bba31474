import { Box, MenuItem, Stack, Typography } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { grey } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { useGroundwork } from 'context/groundwork-context';
import { Room, Wall } from '../stores/types';
import { useRoomsStore, useRoomsActions } from '../stores/RoomsStore';
import { getDesignRoomTemp } from '../utils/heatCalculations';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { propertyIsValid } from '../Validator';
import { useClimateDataStore } from '../stores/ClimateDataStore';

export default function AdjacentRoom({ room, wallUid }: { room: Room; wallUid: string }) {
  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const climateDataStore = useClimateDataStore();
  const wallArray = room.surfaces.walls;
  const wall = room.surfaces.walls.find((w) => w.uid === wallUid);
  const roomActions = useRoomsActions();
  const { rooms } = useRoomsStore();
  const adjoiningRoom = useRoomsStore(({ rooms: r }) =>
    wall && 'adjoiningRoomUID' in wall ? r.find((rm) => rm.id === wall.adjoiningRoomUID) : undefined,
  );
  if (!wall || wall.surfaceType !== 'internalWalls') return null;

  const handleChange = (elmt: Partial<Wall>) => {
    const newDimensions = wallArray.map((surface) =>
      surface.uid === wall.uid ? { ...surface, ...elmt } : surface,
    ) as Wall[];
    roomActions.updateRoom({
      ...room,
      surfaces: {
        ...room.surfaces,
        walls: newDimensions,
      },
    });
  };

  return (
    <Stack>
      <Typography variant="headline3" pb={1}>
        <FormattedMessage id="heatDesign.wallsRenderer.adjoiningRoom.title" />
      </Typography>
      <Box
        p={2}
        sx={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gridGap: '0.5rem',
          justifyContent: 'space-between',
          borderRadius: '8px',
          border: `1px solid ${grey[400]}`,
        }}
      >
        <Stack>
          <Typography variant="inputLabel" pb={1}>
            <FormattedMessage id="heatDesign.wallsRenderer.adjoiningRoom.roomNameLabel" />
          </Typography>
          <Select
            name="adjoiningRoom"
            label=""
            size="small"
            error={!propertyIsValid('internalWalls', 'adjoiningRoomUID', adjoiningRoom?.id)}
            value={adjoiningRoom?.id ?? ''}
            onChange={({ target: { value } }) => handleChange({ adjoiningRoomUID: value })}
          >
            {rooms
              .filter((r) => r.floor === room.floor)
              .map(({ name, id }) => (
                <MenuItem key={id} value={id}>
                  {name}
                </MenuItem>
              ))}
          </Select>
        </Stack>
        <Stack>
          <Typography variant="inputLabel" pb={1}>
            <FormattedMessage id="heatDesign.wallsRenderer.adjoiningRoom.tempLabel" />
          </Typography>
          <Typography variant="body2">
            {adjoiningRoom ? getDesignRoomTemp(adjoiningRoom, constructionYear, countryCode, climateDataStore) : '–'}
            &deg;C (
            <FormattedMessage
              id={`heatDesign.wallsRenderer.adjoiningRoom.${adjoiningRoom?.isHeated ? 'heated' : 'unheated'}`}
            />
            )
          </Typography>
        </Stack>
      </Box>
    </Stack>
  );
}
