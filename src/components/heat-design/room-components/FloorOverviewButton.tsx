import { ReactNode } from 'react';
import { BUTTON_PILL_VARIANTS, ButtonPill } from '@ui/components/ButtonPill/ButtonPill';
import { beige, grey } from '@ui/theme/colors';
import { Box } from '@mui/material';
import { FloorProps } from '../stores/types';

type Props = {
  floor: FloorProps;
  isSelected: boolean;
  children: ReactNode;
  selectFloor: () => void;
  isValid: boolean;
};

export default function FloorOverviewButton({ floor, isSelected, selectFloor, children, isValid }: Props) {
  const variant = isValid ? BUTTON_PILL_VARIANTS.DEFAULT : BUTTON_PILL_VARIANTS.WARNING;

  return (
    <Box
      data-valid={isValid}
      data-testid={`floor-overview-button-${floor.floorName}`}
      style={{ background: 'white', borderRadius: 24, minWidth: 150 }}
      onClick={selectFloor}
    >
      <ButtonPill variant={variant} isSelected={isSelected} style={{ minWidth: '100%' }}>
        {floor.floorName}
      </ButtonPill>
      {children ? (
        <Box
          onClick={selectFloor}
          style={{ padding: 16, color: isSelected ? grey[900] : beige[500], textAlign: 'center', cursor: 'pointer' }}
        >
          {children}
        </Box>
      ) : null}
    </Box>
  );
}
