import { SelectChangeEvent, Stack } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { Box } from '@mui/system';
import { useIntl } from 'react-intl';
import { useRoomsActions } from '../stores/RoomsStore';
import { Room, Wall, WallType, WALL_TYPES } from '../stores/types';
import WallDimensionForm from './WallDimensionForm';
import AdjacentRoom from './AdjacentRoomWall';
import SurfaceUValueSelect from './SurfaceUValueSelect';
import { UValue } from '../models/UValue';
import ExternalWallSoilForm from './ExternalWallSoilForm';

function WallsRenderer({ room, wallUid }: { room: Room; wallUid: string }) {
  const intl = useIntl();
  const roomActions = useRoomsActions();
  const wall = room.surfaces.walls.find((w) => w.uid === wallUid);

  if (!wall) return null;

  const wallArray = room.surfaces.walls;

  const handleChangeWallType = (e: SelectChangeEvent) => {
    const { value: newWallType } = e.target;
    roomActions.updateRoom({
      ...room,
      surfaces: {
        ...room.surfaces,
        walls: wallArray.map((surface): Wall => {
          if (surface.uid === wall.uid) {
            return {
              ...surface,
              uValue: undefined,
              surfaceType: newWallType as WallType,
            } as Wall;
          }
          return surface;
        }),
      },
    });
  };

  const handleChangeUValue = (uValue?: UValue) => {
    const walls = room.surfaces.walls as Wall[];
    const updatedWalls = walls.map((w) => {
      if (w.uid === wall.uid) {
        return {
          ...w,
          uValue,
        };
      }
      return w;
    });
    const updatedRoom = {
      ...room,
      surfaces: {
        ...room.surfaces,
        walls: updatedWalls,
      },
    };
    roomActions.updateRoom(updatedRoom);
  };

  return (
    <Stack direction="column" gap={2} key={wall.uid}>
      <Box maxWidth={180}>
        <Select
          name={`type + ${wall.uid}`}
          key={`type + ${wall.uid}`}
          label={intl.formatMessage({ id: 'common.label.type' })}
          size="small"
          error={!WALL_TYPES.includes(wall.surfaceType)}
          onChange={handleChangeWallType}
          value={wall.surfaceType}
          native
          inputProps={{ id: `type + ${wall.uid}` }}
        >
          <option value="externalWalls">External Wall</option>
          <option value="internalWalls">Internal Wall</option>
          <option value="partyWalls">Party Wall</option>
        </Select>
      </Box>
      <SurfaceUValueSelect surface={wall} room={room} onChange={handleChangeUValue} />
      <ExternalWallSoilForm room={room} wallUid={wallUid} />
      <WallDimensionForm room={room} wallUid={wallUid} />
      <AdjacentRoom room={room} wallUid={wallUid} />
    </Stack>
  );
}

export default WallsRenderer;
