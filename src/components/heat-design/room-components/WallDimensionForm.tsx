import { FormattedMessage, useIntl } from 'react-intl';

import { Stack } from '@mui/system';
import { LabelTooltip } from '@ui/components/Tooltip/Tooltip';
import { StyledFormattedMessage } from 'utils/localization';
import { Room } from '../stores/types';
import { useRoomsActions } from '../stores/RoomsStore';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { toTwoDecimalPlaces } from '../utils/helpers';

export default function DimensionsWallForm({ room, wallUid }: { room: Room; wallUid: string }) {
  const intl = useIntl();
  const roomActions = useRoomsActions();
  const wall = room.surfaces.walls.find((w) => w.uid === wallUid);

  if (!wall) return null;
  const wallArray = room.surfaces.walls;

  const handleSet = (length: number) => {
    const newDimensions = wallArray.map((surface) => {
      if (surface.uid === wall.uid) {
        return {
          ...surface,
          length,
        };
      }
      return surface;
    });
    roomActions.updateRoomById(room.id, {
      surfaces: {
        ...room.surfaces,
        walls: newDimensions,
      },
    });
  };
  return (
    <Stack
      sx={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, 120px)',
        gridGap: '0.5rem',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
      }}
    >
      <NumericTextField
        label={<FormattedMessage id="heatDesign.wallsRenderer.length" />}
        name={intl.formatMessage({ id: 'heatDesign.wallsRenderer.length' })}
        size="small"
        suffix="m"
        value={wall.length ?? 0}
        onChange={(value) => handleSet(value)}
        // We disallow users to change length of walls to prevent the counter-intuitive behavior
        // that floor area does not change when the length of a wall is changed. Code to handle
        // wall length in the state etc. is still in place as we may want to re-enable this feature
        // given user feedback.
        disabled
      />
      <NumericTextField
        label={
          <LabelTooltip
            label={<FormattedMessage id="heatDesign.wallsRenderer.area" />}
            tooltipLabel={<StyledFormattedMessage id="heatDesign.wallsRenderer.area.tooltip" />}
          />
        }
        name={intl.formatMessage({ id: 'heatDesign.wallsRenderer.area' })}
        size="small"
        suffix="m²"
        value={toTwoDecimalPlaces(room.averageHeight * (wall.length ?? 0))}
        disabled
        onChange={() => {}}
        // Smaller space between the label and input due to the tooltip icon
        containerProps={{ spacing: '3px' }}
      />
    </Stack>
  );
}
