import { create } from 'zustand';
import { getDefaultFloor, useFloorsStore } from './FloorsStore';
import { Floor, FloorProps, Room as RoomType } from './types';

export enum HeatDesignModal {
  Room,
  RoomEmitters,
  FloorDefaults,
  RoomEmittersValidation,
  RoomValidation,
  DwellingValidation,
  DwellingDefaults,
  UnsavedChangeNotice,
  Catalogue,
  Help,
  ProductSelectionPriceAdjust,
}

export enum DesignReviewStatus {
  READY = 'ready',
  ACCEPTED = 'accepted',
  NOT_READY = 'not-ready',
}

export type DesignReviewState = {
  status: DesignReviewStatus;
  timestamp?: Date;
};

type FloorId = Floor['uid'];
type RoomId = RoomType['id'];

/**
 * This store contains information necessary to render some modals and keep
 * track of selected items in the /heat-design page.
 */
type HeatDesignUIState = {
  modal: HeatDesignModal | null;
  selectedRoomId: RoomId | null;
  selectedFloorId: FloorId | null;
  isDesignOperationInProgress: boolean;
  designReviewState?: DesignReviewState;
  actions: {
    showRoomModal: (room: RoomType) => void;
    selectRoom: (room: RoomType | undefined) => void;
    showFloorDefaultsModal: (selectedFloorId: FloorId) => void;
    showRoomEmittersValidationModal: () => void;
    showRoomValidationModal: () => void;
    showDwellingValidationModal: () => void;
    showUnsavedChangesModal: () => void;
    showHelpModal: () => void;
    selectFloor: (floor: FloorProps) => void;
    setIsOperationInProgress: (isOperationInProgress: boolean) => void;
    setDesignReviewState: (des?: DesignReviewState) => void;
    closeModal: () => void;
  };
};

export const useHeatDesignUI = create<HeatDesignUIState>()((set) => ({
  modal: null,
  selectedFloorId: null,
  selectedRoomId: null,
  isDesignOperationInProgress: false,
  designReviewStatus: undefined,
  actions: {
    showRoomModal: (room) => {
      set({ selectedRoomId: room.id, modal: HeatDesignModal.Room });
    },
    selectRoom: (room) => {
      set({ selectedRoomId: room?.id });
    },
    showFloorDefaultsModal: (selectedFloorId) => {
      set({ selectedFloorId, modal: HeatDesignModal.FloorDefaults });
    },
    showRoomEmittersValidationModal: () => {
      set({ modal: HeatDesignModal.RoomEmittersValidation });
    },
    showRoomValidationModal: () => {
      set({ modal: HeatDesignModal.RoomValidation });
    },
    showDwellingValidationModal: () => {
      set({ modal: HeatDesignModal.DwellingValidation });
    },
    showUnsavedChangesModal: () => {
      set({ modal: HeatDesignModal.UnsavedChangeNotice });
    },
    showHelpModal: () => {
      set({ modal: HeatDesignModal.Help });
    },
    selectFloor: (floor) => {
      set({ selectedFloorId: floor.uid, selectedRoomId: null });
    },
    setIsOperationInProgress: (isOperationInProgress) => {
      set({ isDesignOperationInProgress: isOperationInProgress });
    },
    setDesignReviewState: (state) => {
      set({ designReviewState: state });
    },
    closeModal: () => {
      set({ modal: null });
    },
  },
}));

/// Initialise the selected floor to the default floor
const unsubscribe = useFloorsStore.subscribe((floorsStore) => {
  const defaultFloor = getDefaultFloor(floorsStore.floors);
  if (defaultFloor) {
    useHeatDesignUI.setState({
      selectedFloorId: defaultFloor.uid,
    });
    unsubscribe();
  }
});

export const useHeatDesignUIActions = () => useHeatDesignUI((state) => state.actions);
export const useHeatDesignModal = () => useHeatDesignUI((state) => state.modal);
