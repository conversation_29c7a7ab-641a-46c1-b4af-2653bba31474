import { create } from 'zustand';

export enum HlcPage {
  PROPERTY_DETAILS = 'heatDesign.title.propertyDetails',
  FLOOR_OVERVIEW = 'heatDesign.title.floorOverview',
  HEAT_LOSS_OVERVIEW = 'heatDesign.title.heatLossOverview',
  RADIATORS_OVERVIEW = 'heatDesign.title.radiatorsOverview',
  PRODUCT_SELECTION = 'heatDesign.title.productSelection',
  REPORT = 'heatDesign.title.resultsExport',
}
export type PageNavigationState = {
  page: HlcPage;
  previouslyVisitedPage?: HlcPage;
  pages: HlcPage[];
  localizedPages: string[];
  currentIndex: number;
};

export type PageNavigationStore = PageNavigationState & {
  actions: {
    goToPreviousPage: () => void;
    goToNextPage: () => void;
    isFirstPage: () => boolean;
    isLastPage: () => boolean;
    setInitialData: (data: PageNavigationState) => void;
  };
};

export const usePageNavigationStore = create<PageNavigationStore>((set, get) => ({
  page: HlcPage.PROPERTY_DETAILS,
  previouslyVisitedPage: undefined,
  pages: [],
  localizedPages: [],
  currentIndex: 0,
  actions: {
    getCurrentIndex: () => get().pages.indexOf(get().page),
    goToNextPage: () => {
      const { currentIndex, page: currentPage, pages } = get();
      if (currentIndex < get().pages.length - 1) {
        set({ previouslyVisitedPage: currentPage, page: pages[currentIndex + 1], currentIndex: currentIndex + 1 });
      }
    },
    goToPreviousPage: () => {
      const { currentIndex, page: currentPage, pages } = get();
      if (currentIndex > 0) {
        set({
          previouslyVisitedPage: currentPage,
          page: pages[currentIndex - 1],
          currentIndex: currentIndex - 1,
        });
      }
    },
    isFirstPage: () => get().currentIndex === 0,
    isLastPage: () => get().currentIndex === get().pages.length - 1,
    setInitialData: (data) => set(data),
  },
}));
