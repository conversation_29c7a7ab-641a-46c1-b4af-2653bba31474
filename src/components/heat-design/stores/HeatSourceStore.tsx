import { create } from 'zustand';
import { RadiatorData } from './types';

// The string values below map to a key in the translations
export enum RadiatorSize {
  STANDARD = 'standard',
  LARGE = 'large',
  DESIGN = 'design',
}

export type RadiatorsGroupedBySize = {
  [key in RadiatorSize]: RadiatorData[];
};

export type WaterTemps = {
  flowTemp: number;
  flowReturnDeltaT: number;
};

export type HeatSourceStore = {
  flowTemp: number;
  flowReturnDeltaT: number;
  actions: {
    updateWaterTemps: (waterTemps: WaterTemps) => void;
  };
};

export const useHeatSourceStore = create<HeatSourceStore>((set) => ({
  // Note: these default values are useless, but needed for Typescript.
  // The defaults are set on load in getDefaultWaterTemperatures().
  flowTemp: -1,
  flowReturnDeltaT: -1,

  actions: {
    updateWaterTemps: ({ flowTemp, flowReturnDeltaT }) =>
      set({
        flowTemp,
        flowReturnDeltaT,
      }),
  },
}));

export const useHeatSourceActions = () => useHeatSourceStore((state) => state.actions);
export const useFlowTemp = () => useHeatSourceStore((state) => state.flowTemp);
export const useFlowReturnDeltaT = () => useHeatSourceStore((state) => state.flowReturnDeltaT);
