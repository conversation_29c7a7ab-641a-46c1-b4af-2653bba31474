import { AuxiliaryData } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { create } from 'zustand';

export type AuxiliaryDataStore = {
  auxiliaryData: AuxiliaryData;
  actions: {
    setData: (auxiliaryData: AuxiliaryData) => void;
    updateAuxiliaryData: (partialData: Partial<AuxiliaryData>) => void;
  };
};

export const useHeatDesignAuxiliaryDataStore = create<AuxiliaryDataStore>((set, get) => ({
  auxiliaryData: {},
  actions: {
    setData: (auxiliaryData) => set({ auxiliaryData }),
    updateAuxiliaryData: (partialData) => {
      set({
        auxiliaryData: { ...get().auxiliaryData, ...partialData },
      });
    },
  },
}));

export const useAuxiliaryData = () => useHeatDesignAuxiliaryDataStore((s) => s.auxiliaryData);
export const useSetAuxiliaryData = () => useHeatDesignAuxiliaryDataStore((s) => s.actions.setData);
export const useUpdateAuxiliaryData = () => useHeatDesignAuxiliaryDataStore((s) => s.actions.updateAuxiliaryData);
export const useInstallationComment = () =>
  useHeatDesignAuxiliaryDataStore((s) => s.auxiliaryData.installationCommentForCustomer ?? '');
