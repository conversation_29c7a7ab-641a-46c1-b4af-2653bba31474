import { create } from 'zustand';
import { FloorProps, Room, RoomFabricTypes } from './types';
import { UValue } from '../models/UValue';

export type FloorsStore = {
  floors: FloorProps[];
  actions: {
    setFloors: (floors: FloorProps[]) => void;
    createFloor: (floor: FloorProps) => void;
    updateFloor: (floor: FloorProps) => void;
    deleteFloor: (floor: FloorProps) => void;
    setFloorUValue: ({
      floorUID,
      fabricType,
      uValue,
    }: {
      floorUID: string;
      fabricType: RoomFabricTypes;
      uValue?: UValue;
    }) => void;
  };
};

export const useFloorsStore = create<FloorsStore>((set) => ({
  floors: [],
  actions: {
    setFloors: (floors) => set({ floors }),
    createFloor: (floor) => set((state) => ({ floors: [...state.floors, floor] })),
    updateFloor: (floor) => set((state) => ({ floors: state.floors.map((f) => (f.uid === floor.uid ? floor : f)) })),
    deleteFloor: (floor) => set((state) => ({ floors: state.floors.filter((f) => f.uid !== floor.uid) })),
    setFloorUValue: ({
      floorUID,
      fabricType,
      uValue,
    }: {
      floorUID: string;
      fabricType: RoomFabricTypes;
      uValue?: UValue;
    }) =>
      set((state) => {
        const floor = state.floors.find((f) => f.uid === floorUID);
        if (!floor) return state;
        const updatedFloor = {
          ...floor,
          floorUValues: {
            ...floor.floorUValues,
            [fabricType]: uValue,
          },
        };
        return {
          floors: state.floors.map((f) => (f.uid === floorUID ? updatedFloor : f)),
        };
      }),
  },
}));

export const useFloorsActions = () => useFloorsStore((state) => state.actions);
export const useFloors = () => useFloorsStore((state) => state.floors);
export const getGroundFloorUid = (floors: FloorProps[]): string | undefined => floors.find((f) => f.floorNr === 0)?.uid;

export function getDefaultFloor(floors: FloorProps[]): FloorProps | undefined {
  const groundFloor = floors.find((floor) => floor.floorNr === 0);

  const firstFloor = floors[0];

  return groundFloor ?? firstFloor;
}

export const useDefaultFloor = (): FloorProps | undefined => useFloorsStore((state) => getDefaultFloor(state.floors));

export function useSelectFloorForRoom(room: Room): FloorProps | undefined {
  return useFloorsStore((state) => state.floors.find((f) => f.floorNr === room.level));
}
