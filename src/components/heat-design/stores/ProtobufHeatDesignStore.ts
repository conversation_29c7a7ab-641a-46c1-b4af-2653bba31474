import {
  HeatDesign,
  HeatDesignEvent,
  HeatDesignResult,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { create } from 'zustand';

// As we save data to the protobuf format, we need to retain a bunch of data
// that were originally stored in the protobuf format, but is not kept otherwise
// in the application state. This store keeps track of the original.

export type ProtobufHeatDesignStore = {
  heatDesign: HeatDesign | null;
  result?: HeatDesignResult;
  events?: HeatDesignEvent[];
  isLocked: boolean;
  updatedAt?: Date;
  actions: {
    setHeatDesign: (heatDesign: HeatDesign) => void;
    setResult: (result?: HeatDesignResult) => void;
    setEvents: (events: HeatDesignEvent[]) => void;
    setUpdatedAt: (updatedAt?: Date) => void;
    setIsLocked: (isLocked: boolean) => void;
  };
};

export const useProtobufHeatDesignStore = create<ProtobufHeatDesignStore>((set) => ({
  heatDesign: null,
  result: undefined,
  updatedAt: undefined,
  isLocked: false,
  actions: {
    setHeatDesign: (heatDesign) => set({ heatDesign }),
    setResult: (result) => set({ result }),
    setEvents: (events) => set({ events }),
    setUpdatedAt: (updatedAt) => set({ updatedAt }),
    setIsLocked: (isLocked) => set({ isLocked }),
  },
}));
