import { create } from 'zustand';

// Temperature definitions in the Aira Wiki: https://wiki.airahome.com/s/aira/p/team-terminology-Yh6v30u72J
type ClimateData = {
  baseOutdoorDesignTemperature: number;
  degreeDays: number;
  localAnnualAverageExternalAirTemperature: number;
  altitude?: number;
  zone?: string;
};

export type ClimateDataStore = ClimateData & {
  actions: {
    setClimateData: (climateData: ClimateData) => void;
  };
};

export const useClimateDataStore = create<ClimateDataStore>((set) => ({
  baseOutdoorDesignTemperature: 0,
  degreeDays: 0,
  localAnnualAverageExternalAirTemperature: 0,
  altitude: undefined,
  zone: undefined,
  actions: {
    setClimateData: (climateData) => set(climateData),
  },
}));

export const useBaseOutdoorDesignTemperature = () => useClimateDataStore((state) => state.baseOutdoorDesignTemperature);
export const useDegreeDays = () => useClimateDataStore((state) => state.degreeDays);
export const useClimateZone = () => useClimateDataStore((state) => state.zone);
export const useLocalAnnualAverageExternalAirTemperature = () =>
  useClimateDataStore((state) => state.localAnnualAverageExternalAirTemperature);
export const useSetClimateData = () => useClimateDataStore((state) => state.actions.setClimateData);
