import { create } from 'zustand';
import { Room, RoomFabricTypes } from './types';
import { UValue } from '../models/UValue';

export type RoomsStore = {
  rooms: Room[];
  actions: {
    createRoom: (room: Room) => void;
    updateRoom: (room: Room) => void;
    updateRoomById: (roomId: string, newRoomProps: Partial<Room>) => void;
    deleteRoom: (id: Room['id']) => void;
    setRooms: (rooms: Room[]) => void;
  };
};

export const useRoomsStore = create<RoomsStore>((set) => ({
  rooms: [],
  actions: {
    createRoom: (room: Room) =>
      set((state) => {
        // Overwrite the room if it already exists
        const newRooms = state.rooms.filter((r) => r.id !== room.id);
        newRooms.push(room);
        return { rooms: newRooms };
      }),
    updateRoom: (room: Room) => set((state) => ({ rooms: state.rooms.map((r) => (r.id === room.id ? room : r)) })),
    updateRoomById: (roomId: string, newRoomProps: Partial<Room>) =>
      set((state) => ({ rooms: state.rooms.map((r) => (r.id === roomId ? { ...r, ...newRoomProps } : r)) })),
    deleteRoom: (id: Room['id']) => set((state) => ({ rooms: state.rooms.filter((r) => r.id !== id) })),
    setRooms: (rooms: Room[]) => set(() => ({ rooms })),
  },
}));

export const useRooms = () => useRoomsStore((state) => state.rooms);
export const useRoomsActions = () => useRoomsStore((state) => state.actions);
export const useUpdateRoomById = () => useRoomsStore((state) => state.actions.updateRoomById);
export type RoomsByFloor = Record<Room['floorId'], Room[]>;
export const useGetRoomsByFloor: () => RoomsByFloor = () =>
  useRoomsStore((state) => state.rooms).reduce((acc: { [key: string]: Room[] }, room: Room) => {
    const { floorId } = room;
    acc[floorId] = acc[floorId] || [];
    acc[floorId]?.push(room);
    return acc;
  }, {});

export type UValueNameLocalizer = (roomName: string, type: RoomFabricTypes) => string;

export const getUValue = ({
  roomName,
  type,
  uValueSpecificToSurface,
  uValueNameLocalizer,
}: {
  roomName: string;
  type: RoomFabricTypes;
  uValueSpecificToSurface: number;
  uValueNameLocalizer: UValueNameLocalizer;
}) => new UValue(uValueNameLocalizer(roomName, type), uValueSpecificToSurface);
