import { create } from 'zustand';
import { useClimateDataStore } from './ClimateDataStore';
import { getAdjustedOutdoorDesignTemperatureCelsius } from '../utils/calculations';
import { ACPH_DEFAULT_CUSTOM_VALUE, TEMPERATURE_ADJUSTMENT_EXPOSED_LOCATION } from '../constants';
import { ACPHDefaultValue, VentilationCalculationMethod } from './types';
import {
  AirPermeabilityOption,
  DwellingExposureOption,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

export type SimpleHouseInputVentilationDesign = {
  calculationMethod: VentilationCalculationMethod.SIMPLE;
  acphDefault: ACPHDefaultValue;
};

export type StandardHouseInputVentilationDesign = {
  calculationMethod: VentilationCalculationMethod.STANDARD;
  airPermeability: AirPermeabilityOption;
  airPermeabilityOverride: number | undefined;
  dwellingExposure: DwellingExposureOption;
  acphDefault: ACPHDefaultValue;
};

export type HouseInputVentilationDesign = SimpleHouseInputVentilationDesign | StandardHouseInputVentilationDesign;

export const isStandardHouseInputVentilationDesign = (
  ventilationDesign: HouseInputVentilationDesign,
): ventilationDesign is StandardHouseInputVentilationDesign => {
  return ventilationDesign.calculationMethod === VentilationCalculationMethod.STANDARD;
};

export const isSimpleHouseInputVentilationDesign = (
  ventilationDesign: HouseInputVentilationDesign,
): ventilationDesign is SimpleHouseInputVentilationDesign => {
  return ventilationDesign.calculationMethod === VentilationCalculationMethod.SIMPLE;
};

type HeatDesignHouseInputs = {
  constructionYear: number;
  numberOfResidents: number;
  embed3dUrl: string;
  magicplanAddress: string;
  isDwellingInExposedLocation: boolean;
  temperatureCompensation: number;
  ventilationDesign: HouseInputVentilationDesign;
};

export type HeatDesignHouseInputsStore = HeatDesignHouseInputs & {
  actions: {
    setHouseInputs: (houseInputs: HeatDesignHouseInputs) => void;
    setConstructionYear: (constructionYear: number) => void;
    setNumberOfResidents: (numOccupants: number) => void;
    setTemperatureCompensation: (temperatureCompensation: number) => void;
    setIsDwellingInExposedLocation: (isDwellingInExposedLocation: boolean) => void;
    setVentilationDesign: (ventilationDesign: HeatDesignHouseInputs['ventilationDesign']) => void;
  };
};

export const useHeatDesignHouseInputsStore = create<HeatDesignHouseInputsStore>((set) => ({
  constructionYear: 0,
  numberOfResidents: 1,
  embed3dUrl: '',
  magicplanAddress: '',
  isDwellingInExposedLocation: false,
  temperatureCompensation: 0,
  ventilationDesign: {
    acphDefault: {
      type: 'custom',
      value: ACPH_DEFAULT_CUSTOM_VALUE,
    },
    calculationMethod: VentilationCalculationMethod.SIMPLE,
    airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
    airPermeabilityOverride: undefined,
    dwellingExposure: DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
  },
  actions: {
    setHouseInputs: (houseInputs) => set(houseInputs),
    setConstructionYear: (constructionYear: number) => set({ constructionYear }),
    setNumberOfResidents: (numOccupants: number) => set({ numberOfResidents: numOccupants }),
    setTemperatureCompensation: (temperatureCompensation: number) => set({ temperatureCompensation }),
    setIsDwellingInExposedLocation: (isDwellingInExposedLocation: boolean) => set({ isDwellingInExposedLocation }),
    setVentilationDesign: (ventilationDesign) => set({ ventilationDesign }),
  },
}));

export const useConstructionYear = () => useHeatDesignHouseInputsStore((state) => state.constructionYear);
export const useNumOccupants = () => useHeatDesignHouseInputsStore((state) => state.numberOfResidents);
export const useEmbed3dUrl = () => useHeatDesignHouseInputsStore((state) => state.embed3dUrl);
export const useHeatDesignHouseInputsActions = () => useHeatDesignHouseInputsStore((state) => state.actions);
export const useSetHouseInputs = () => useHeatDesignHouseInputsStore((state) => state.actions.setHouseInputs);

export const useTemperatureCompensated = () =>
  useHeatDesignHouseInputsStore(
    ({ temperatureCompensation, isDwellingInExposedLocation }) =>
      temperatureCompensation + (isDwellingInExposedLocation ? TEMPERATURE_ADJUSTMENT_EXPOSED_LOCATION : 0),
  );

export const useAdjustedOutdoorDesignTemperature = () => {
  const baseOutdoorDesignTemperature = useClimateDataStore((s) => s.baseOutdoorDesignTemperature);
  const temperatureCompensation = useHeatDesignHouseInputsStore((s) => s.temperatureCompensation);
  const isDwellingInExposedLocation = useHeatDesignHouseInputsStore((s) => s.isDwellingInExposedLocation);

  return getAdjustedOutdoorDesignTemperatureCelsius({
    baseOutdoorDesignTemperature,
    isDwellingInExposedLocation,
    temperatureCompensation,
  });
};
