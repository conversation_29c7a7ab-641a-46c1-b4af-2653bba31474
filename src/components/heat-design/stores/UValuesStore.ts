import { create } from 'zustand';
import { FabricTypes, ProjectFabricTypes, ProjectFallbackUValues, type UValues } from './types';
import { UValue } from '../models/UValue';
import { getUValuesForClimateZone } from '../uValues';

const EMPTY_U_VALUES: UValues = {
  doors: [],
  externalWalls: [],
  floors: [],
  foundation: [],
  intermediateFloors: [],
  internalWalls: [],
  partyWalls: [],
  roof: [],
  roofGlazings: [],
  roofsOrCeilings: [],
  windows: [],
};

export type UValuesStore = {
  uValues: UValues;
  projectUValues: ProjectFallbackUValues;
  actions: {
    addUValue: ({ fabricType, uValue }: { fabricType: FabricTypes; uValue: UValue }) => void;
    setProjectUValue: ({ fabricType, uValue }: { fabricType: ProjectFabricTypes; uValue: UValue }) => void;
    setUValues: (values: UValues) => void;
    setProjectUValues: (projectUValues: ProjectFallbackUValues) => void;
    filterUValuesByClimateZone: (climateZone: string) => void;
  };
};

export const useUValuesStore = create<UValuesStore>((set) => ({
  uValues: EMPTY_U_VALUES, // U-Values depend on the country, so we fill them in later
  projectUValues: {},
  actions: {
    setUValues: (values: UValues) => {
      set((state) => ({
        ...state,
        uValues: values,
      }));
    },
    setProjectUValues: (projectUValues: ProjectFallbackUValues) => {
      set((state) => ({
        ...state,
        projectUValues,
      }));
    },
    addUValue: ({ fabricType, uValue }: { fabricType: FabricTypes; uValue: UValue }) => {
      set((state) => {
        const updatedUValues = state.uValues[fabricType];
        if (updatedUValues.find((u) => u.id === uValue.id)) return state;
        updatedUValues.unshift(uValue);
        return {
          uValues: {
            ...state.uValues,
            [fabricType]: updatedUValues,
          },
        };
      });
    },
    setProjectUValue: ({ fabricType, uValue }: { fabricType: ProjectFabricTypes; uValue: UValue }) => {
      set((state) => ({
        ...state,
        projectUValues: {
          ...state.projectUValues,
          [fabricType]: uValue,
        },
      }));
    },
    filterUValuesByClimateZone: (climateZone: string) => {
      set((state) => ({
        ...state,
        uValues: getUValuesForClimateZone(state.uValues, climateZone),
      }));
    },
  },
}));

export const useUValues = () => useUValuesStore((state) => state.uValues);
export const useProjectUValues = () => useUValuesStore((state) => state.projectUValues);
export const useUValuesActions = () => useUValuesStore((state) => state.actions);
export const useFilterUValuesByClimateZone = () => useUValuesStore((state) => state.actions.filterUValuesByClimateZone);
