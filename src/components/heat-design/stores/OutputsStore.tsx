import { RadiatorEmitterOverviewDetails } from '../Emitters/types';
import { UValue } from '../models/UValue';
import { AdjacentKind } from './types';

export type HeatLossResult = {
  heatLoss: number;
  energyDemand: number;
};

export type FabricHeatDesignResult = HeatLossResult & {
  id: string;
  area: number;
  uValue: UValue;
  adjacentTemperature?: number;
  /** The kind/type of the adjacent thing, if any. */
  adjacentKind?: AdjacentKind;
  /** A name or label for the adjacent thing, usually a room name like "Bedroom
   * 1". We do not localize this value since it is user-generated. */
  adjacentName?: string;
};

export type WallAttachedFabricHeatDesignResult = FabricHeatDesignResult & {
  wallId?: string;
};

export type RoomHeatDesignResult = {
  floorId: string;
  roomId: string;
  roomName: string;
  roomType: string;
  averageHeight: number;
  isHeated: boolean;
  surfaceTotals: {
    externalWalls: HeatLossResult;
    floors: HeatLossResult;
    roofGlazings: HeatLossResult;
    partyWalls: HeatLossResult;
    internalWalls: HeatLossResult;
    doors: HeatLossResult;
    windows: HeatLossResult;
    roofsOrCeilings: HeatLossResult;
  };
  surfaceDetails: {
    externalWalls: FabricHeatDesignResult[];
    /**
     * We only expected one floor per room, but we keep it as an array for
     * type consistency with the other surfaces.
     */
    floors: FabricHeatDesignResult[];
    roofGlazings: FabricHeatDesignResult[];
    partyWalls: FabricHeatDesignResult[];
    internalWalls: FabricHeatDesignResult[];
    doors: WallAttachedFabricHeatDesignResult[];
    windows: WallAttachedFabricHeatDesignResult[];
    /**
     * We only expected one roof or ceiling per room, but we keep it as an array
     * for type consistency with the other surfaces.
     */
    roofsOrCeilings: FabricHeatDesignResult[];
  };
  totalFabric: HeatLossResult;
  ventilation: HeatLossResult;
  additional: {
    highCeiling: HeatLossResult;
  };
  totalFloorArea: number;
  totalAdditional: HeatLossResult;
  totalRoom: HeatLossResult;
  wattsPerMeterSquared: number;
  calculatedUnderfloorHeatingOutputWatt: number;
  calculatedUnderfloorHeatingFlowRate: number;
  totalOutputOfRadiatorsWatt: number;
  totalOutputOfHeatEmittersWatt: number;
  resolvedDesignRoomTemperatureCelsius: number;
  radiators: RadiatorEmitterOverviewDetails[];
  averageAirChangePerHour: number;
  externalEnvelopeArea: number;
};

export type FloorHeatDesignResult = {
  floorId: string;
  totalHeatLossWatt: number;
  totalOutputOfHeatEmittersWatt: number;
  totalAreaSqm: number;
  numberOfBedrooms: number;
  roomsResults: RoomHeatDesignResult[];
};

export type OutdoorUnitIds = {
  productId?: string;
  technicalSpecificationId?: string;
};

export type HeatDesignResult = {
  floorsResults: FloorHeatDesignResult[];
  totalFabricHeatLoss: number;
  totalFabricEnergyDemand: number;
  totalVentilationHeatLoss: number;
  totalVentilationEnergyDemand: number;
  totalAdditionalHeatLoss: number;
  totalAdditionalEnergyDemand: number;
  totalHeatLoss: number;
  dailyHotWaterEnergyDemand: number;
  yearlyHotWaterEnergyDemand: number;
  heatingEnergyDemand: number;
  totalEnergyDemand: number;
  avgWattsPerMeterSquared: number;
  showerTimeMinimumMinutes?: number;
  showerTimeMaximumMinutes?: number;
  waterReheatTimeMinutes?: number;
  totalAreaSqm: number;
  totalOutputOfHeatEmittersWatt: number;
  thermalBridgingUvalueAdjustment: number;
  externalWallAdjustmentFactor?: number;
  maxHeatOutputWithoutElectricHeaterWatt?: number;
  bivalencePointCelsius?: number;
  scop?: number;
  indoorUnitProductId?: string;
  outdoorUnitsIds: OutdoorUnitIds[];
  numberOfFloors: number;
  numberOfBedrooms: number;
  averageAirChangesPerHour: number;
};
