import {
  ElectricRadiatorDetails,
  WaterRadiatorDetails,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import {
  PanelRadiatorType,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { UValue } from '../models/UValue';
import { ACPHStandardizedDefaults } from '../utils/averageAirChangePerHour';
import {
  AirPermeabilityOption,
  DwellingExposureOption,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

export enum RadiatorMode {
  Panel = 'Panel',
  Column = 'Column',
}

export type UValues = {
  [key in FabricTypes]: UValue[];
};

export type SurfaceFallbackUValues = {
  [key in RoomFabricTypes]?: UValue;
};

export type ProjectFallbackUValues = {
  [key in ProjectFabricTypes]?: UValue;
};

export type RoomDimensions = {
  uid: string;
  length?: number;
  width?: number;
  area: number;
  height: number;
  volume: number;
};

export type HorizontalSurface = {
  uid: string;
  length?: number;
  width?: number;
  uValue?: UValue;
};

export enum AdjacentKind {
  Room = 'room',
  Outside = 'outside',
  Soil = 'soil',
  Unheated = 'unheated',
  Heated = 'heated',
  SolidFloor = 'solidFloor',
  SuspendedFloor = 'suspendedFloor',
}

export const BELOW_FLOOR_TYPES = [
  AdjacentKind.SolidFloor,
  AdjacentKind.SuspendedFloor,
  AdjacentKind.Heated,
  AdjacentKind.Unheated,
] as const;

export type BelowFloorType = (typeof BELOW_FLOOR_TYPES)[number];

export const SPACE_ABOVE_TYPES = [AdjacentKind.Outside, AdjacentKind.Unheated, AdjacentKind.Heated] as const;
export type SpaceAboveType = (typeof SPACE_ABOVE_TYPES)[number];

export type Floor = HorizontalSurface & { area: number; belowFloor?: BelowFloorType; surfaceType: 'floors' };

export type SpaceAboveRoofOrCeiling = {
  type: SpaceAboveType;
  tempOfSpaceAbove?: number;
};

export type RoofOrCeiling = HorizontalSurface & {
  area: number;
  spaceAbove: SpaceAboveRoofOrCeiling;
  surfaceType: 'roofsOrCeilings';
};

export type Door = {
  uid: string;
  width: number;
  height: number;
  area: number;
  roomUID?: string;
  wallUID?: string;
  uValue?: UValue;
  imageMap: ImageMap;
  surfaceType: 'doors';
};

export type Window = Omit<Door, 'surfaceType'> & {
  surfaceType: 'windows';
};

export const ROOM_TEMPERATURES = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24] as const;
export type TypeRoomTemp = (typeof ROOM_TEMPERATURES)[number];

// Ensure that the subtypes derive from TypeOfWall
export type SubtypeCheck<T> = T extends WallType ? true : false;

export type VerticalSurface = {
  uid: string;
  length?: number;
  uValue?: UValue;
  imageMap: ImageMap;
};

export type InternalWall = VerticalSurface & {
  adjoiningRoomUID?: string;
  surfaceType: 'internalWalls';
};

export type ExternalWall = VerticalSurface & {
  surfaceType: 'externalWalls';
  soilPercentage: number | null;
};

export type PartyWall = VerticalSurface & { surfaceType: 'partyWalls' };

export type Wall = ExternalWall | PartyWall | InternalWall;

export type RoofGlazing = HorizontalSurface & { surfaceType: 'roofGlazings' };

// These correspond to the CIBSE rooms
export const ROOM_TYPES = [
  'Bathroom',
  'Bedroom',
  'Bedroom, including en suite bathroom',
  'Bedroom/study',
  'Bedsitting room',
  'Breakfast room',
  'Cloakroom/WC',
  'Dining room',
  'Dressing room',
  'Family/breakfast room',
  'Games room',
  'Hall',
  'Internal room or corridor',
  'Kitchen',
  'Landing',
  'Living room',
  'Lounge/sitting room',
  'Shower room',
  'Store room',
  'Study',
  'Toilet',
  'Utility room',
  'Other',
] as const;

export type RoomType = (typeof ROOM_TYPES)[number];

export const OPEN_FLUE_TYPES = ['none', 'Yes (with throat restrictor)', 'Yes (without throat restrictor)'] as const;
export type OpenFlueType = (typeof OPEN_FLUE_TYPES)[number];

export const FLUE_TYPE_TO_AVG_AIR_CHANGES_PER_HOUR = {
  none: 1.5,
  'Yes (with throat restrictor)': 2,
  'Yes (without throat restrictor)': 4,
}; // Double check in MCS to see if there is anything else that affects this

export type RoomFabric = Floor | Wall | RoofOrCeiling | Door | Window | RoofGlazing;

export const WALL_TYPES: RoomFabricTypes[] = ['externalWalls', 'internalWalls', 'partyWalls'];
export type WallType = (typeof WALL_TYPES)[number];

export const HORIZONTAL_SURFACE_TYPES = ['floors', 'roofsOrCeilings'] as const;
export type HorizontalSurfaceType = (typeof HORIZONTAL_SURFACE_TYPES)[number];

export interface ImageMap {
  coordinates: number[];
}

export enum SystemType {
  ELECTRIC = 'electric',
  WATER = 'water',
}

export enum OutputType {
  CUSTOM = 'custom',
  AUTOMATIC = 'automatic',
}

export type EmitterDetails =
  | (ElectricRadiatorDetails & { systemType: SystemType.ELECTRIC })
  | (WaterRadiatorDetails & { systemType: SystemType.WATER });

/**
 * EmitterDetails and systemType are optional becasuse they are not present in newly created underfloor heating
 */
export type CustomUnderfloorHeatingOutput = { outputType: OutputType.CUSTOM } & Partial<EmitterDetails>;

export type AutomaticUnderfloorHeatingOutput = {
  outputType: OutputType.AUTOMATIC;
  systemType?: SystemType;
};

export type UnderfloorHeatingOutput = CustomUnderfloorHeatingOutput | AutomaticUnderfloorHeatingOutput;
export type RadiatorData = {
  uid: string;
  erpId?: string;
  specificationReferenceId?: string;
  area: number;
  floorImageMap: ImageMap;
  roomImageMap: ImageMap;
  info: RadiatorInfo;
  width: number;
  height: number;
  isExisting: boolean;
  enabled: boolean;
  typeOfHeatEmitter?: RadiatorMode;
  radiatorDetails: EmitterDetails;
  roomId: string;
  panelType?: PanelRadiatorType;
  style?: string;
  depth?: number;
  description?: string;
  comment?: string;
  replacedBy?: string;
};

export type RadiatorDataWithCatalogueProperties = RadiatorData & {
  material?: RadiatorMaterial;
  numberOfColumns?: number;
};

export type RadiatorInfo = {
  typeOfHeatEmitter?: string;
  style?: string;
  panelType?: string;
  depth?: number;
  description?: string;
  comment?: string;
};

export const ROOM_FABRIC_TYPES = [
  'floors',
  'roofsOrCeilings',
  'externalWalls',
  'internalWalls',
  'partyWalls',
  'doors',
  'windows',
  'roofGlazings',
] as const;

export const PROJECT_FABRIC_TYPES = [
  'foundation',
  'roof',
  'intermediateFloors',
  'externalWalls',
  'internalWalls',
  'partyWalls',
  'doors',
  'windows',
  'roofGlazings',
] as const;

// We're using a Set below to remove duplicates since the two arrays have
// overlapping values.
export const FABRIC_TYPES = [...new Set([...ROOM_FABRIC_TYPES, ...PROJECT_FABRIC_TYPES])] as const;

export type RoomFabricTypes = (typeof ROOM_FABRIC_TYPES)[number];
export type ProjectFabricTypes = (typeof PROJECT_FABRIC_TYPES)[number];
export type FabricTypes = ProjectFabricTypes | RoomFabricTypes;

export type Room = {
  id: string;
  name: string;
  roomType: RoomType;
  isHeated: boolean;
  level: number;
  floor: string;
  floorId: string;
  designRoomTempOverride?: number;
  avgAirChangesPerHourOverride?: number;
  openFlue: OpenFlueType;
  totalVolume: number;
  totalArea: number;
  averageHeight: number;
  imageSvgData: string;
  simplifiedImageSvgData: string;
  imageMap: ImageMap;
  surfaces: {
    floors: Floor[];
    walls: Wall[];
    roofsOrCeilings: RoofOrCeiling[];
    doors: Door[];
    windows: Window[];
    roofGlazings: RoofGlazing[];
  };
  radiators: RadiatorData[];
  enableRadiatorDeltaTAdjustment: boolean;
  underfloorHeating?: UnderfloorHeatingOutput;
};

export type FloorProps = {
  floorName: string;
  uid: string;
  floorNr: number;
  ceilingUValue?: number;
  imageSvgData: string;
  simplifiedImageSvgData: string;
  floorUValues?: SurfaceFallbackUValues;
  soilPercentageDefault: number | null;
};

export type ACPHStandardizedDefaultValue = {
  type: 'standardized';
  standard: ACPHStandardizedDefaults;
};

export type ACPHCustomValue = {
  type: 'custom';
  value: number;
};

export type ACPHDefaultValue = ACPHStandardizedDefaultValue | ACPHCustomValue;

export enum VentilationCalculationMethod {
  SIMPLE = 'simpleVentilation',
  STANDARD = 'standardVentilation',
}

export const VENTILATION_CALCULATION_METHOD_LABELS = {
  [VentilationCalculationMethod.SIMPLE]: 'Simple',
  [VentilationCalculationMethod.STANDARD]: 'Standard',
} as const;

export const AIR_PERMEABILITY_OPTIONS = [
  AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
  AirPermeabilityOption.AIR_PERMEABILITY_OPTION_CIBSE,
  AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST,
] as const;

export const AIR_PERMEABILITY_LABELS = {
  [AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831]: 'EN12831',
  [AirPermeabilityOption.AIR_PERMEABILITY_OPTION_CIBSE]: 'CIBSE',
  [AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST]: 'Pulse test',
} as const;

export const DWELLING_EXPOSURE_OPTIONS = [
  DwellingExposureOption.DWELLING_EXPOSURE_OPTION_SHIELDED,
  DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
  DwellingExposureOption.DWELLING_EXPOSURE_OPTION_EXPOSED,
] as const;

export const DWELLING_EXPOSURE_LABELS = {
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_SHIELDED]: 'Shielded',
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED]: 'Moderate',
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_EXPOSED]: 'Exposed',
};

// B.2.11 Coefficient for the volume flow ratio
export const VENTILATION_EXPOSURE_VALUES: Record<DwellingExposureOption, number> = {
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_UNSPECIFIED]: 0,
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_SHIELDED]: 0.03,
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED]: 0.05,
  [DwellingExposureOption.DWELLING_EXPOSURE_OPTION_EXPOSED]: 0.07,
  [DwellingExposureOption.UNRECOGNIZED]: -1,
} as const;
