import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet';
import { DivIcon, LeafletMouseEvent, Map as LeafletMap } from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-defaulticon-compatibility';
import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useElementBbox } from '../../../hooks/useElementBbox';
import { Box } from '@mui/material';

function makeOrientedDivIcon(heading: number) {
  // Normalize heading to [0,360)
  const h = ((heading % 360) + 360) % 360;

  const html = `
    <div style="
      width: 28px; height: 28px;
      z-index:2;
      display: grid; place-items: center;
      transform: rotate(${h}deg);
    ">
      <div style="
        position: absolute;
        width: 20px; height: 20px;
        border-radius: 50%;
        z-index: 0;
        background: #ffffff;
      "></div>
      <div style="
        position: absolute;
        width: 17px; height: 17px;
        border-radius: 50%;
        background: #1c1c1c;
        z-index: 1;
      "></div>
      <div style="
        position: absolute;
        width: 0; height: 0;
        top: -5px;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid #323232;
      "></div>
    </div>
  `;
  return new DivIcon({
    className: 'sv-orientation-icon',
    html,
    iconSize: [28, 28],
    iconAnchor: [14, 14],
  });
}

function PropertyDetailsMap({
  location,
  streetViewPosition,
  streetViewHeading,
  onStreetviewMove,
}: {
  location: {
    lat: number;
    lng: number;
  };
  streetViewPosition?: { lat: number; lng: number };
  streetViewHeading?: number;
  onStreetviewMove: (position: google.maps.LatLng | google.maps.LatLngLiteral | null) => void;
  isActive: boolean;
}) {
  const [leafletMap, setLeafletMap] = useState<LeafletMap | null>(null);
  const markerRef = useRef<any>(null);

  const mapRefCallback = useCallback((map: LeafletMap | null) => {
    setLeafletMap(map);
  }, []);
  const [element, setElement] = useState<HTMLDivElement | undefined>(undefined);

  const bbox = useElementBbox({ element });
  const orientedIcon = streetViewHeading ? makeOrientedDivIcon(streetViewHeading) : undefined;

  const eventHandlers = useMemo(() => {
    return {
      dragend: () => {
        const marker = markerRef.current;
        if (marker != null) {
          onStreetviewMove(marker.getLatLng());
        }
      },
    };
  }, [onStreetviewMove]);

  const handleContainerChange = useCallback(
    (node: HTMLDivElement) => {
      setElement(node);
    },
    [setElement],
  );

  useEffect(() => {
    if (leafletMap && bbox) {
      leafletMap.invalidateSize();
    }
  }, [bbox, leafletMap]);

  useEffect(() => {
    const onDoubleClick = (e: LeafletMouseEvent) => {
      onStreetviewMove(e.latlng);
    };
    if (leafletMap) {
      leafletMap.on('dblclick', onDoubleClick);
    }
    return () => {
      if (leafletMap) {
        leafletMap.off('dblclick', onDoubleClick);
      }
    };
  }, [leafletMap, onStreetviewMove]);

  return (
    <Box
      ref={handleContainerChange}
      sx={{
        height: '100%',
        width: '100%',
        borderRadius: 2,
        position: 'relative',
      }}
    >
      <MapContainer
        ref={mapRefCallback}
        doubleClickZoom={false}
        key={`${location.lat}-${location.lng}`}
        center={location}
        zoom={16}
        zoomControl={false}
        style={{ height: '100%', width: '100%' }}
        attributionControl={false}
      >
        <TileLayer attribution="" url="https://www.google.com/maps/vt?lyrs=m@189&gl=cn&x={x}&y={y}&z={z}" />
        {streetViewPosition && orientedIcon && (
          <Marker
            draggable={true}
            ref={markerRef}
            eventHandlers={eventHandlers}
            position={streetViewPosition}
            icon={orientedIcon}
            zIndexOffset={101}
          />
        )}
        <Marker position={location} />
      </MapContainer>
    </Box>
  );
}

export default PropertyDetailsMap;
