import { Box, InputLabel, Typography, useMediaQuery } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { CloudSmallSunWaterdropOutlinedIcon } from '@ui/components/StandardIcons/CloudSmallSunWaterdropOutlinedIcon';
import { grey } from '@ui/theme/colors';
import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import HeatDesignCardV2 from '../HeatDesignCardV2';
import { useClimateDataStore, useClimateZone } from '../stores/ClimateDataStore';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import TemperatureCompensation from './TemperatureCompensation';

export default function DerivedTemperatureValues() {
  const intl = useIntl();
  const { localAnnualAverageExternalAirTemperature, baseOutdoorDesignTemperature, degreeDays } = useClimateDataStore();
  const climateZone = useClimateZone();
  const altitude = useClimateDataStore((s) => s.altitude);
  const isMobile = useMediaQuery('(max-width: 1200px)');

  const actions = useHeatDesignHouseInputsStore((s) => s.actions);
  const isDwellingInExposedLocation = useHeatDesignHouseInputsStore((s) => s.isDwellingInExposedLocation);
  // Until all projects that used this flag have been finished, we need to keep track of this
  // so users can toggle the switch off for those projects if desired. Once no projects use this flag anymore,
  // both this check and the toggle can be removed.
  const [wasDwellingInitiallyInExposedLocation] = useState(isDwellingInExposedLocation);
  const handleChangeExposedLocation = (exposedLocation: boolean) => {
    actions.setIsDwellingInExposedLocation(exposedLocation);
  };

  const labelValueDisplayOrientation = isMobile ? 'vertical' : 'horizontal';
  return (
    <HeatDesignCardV2
      title={intl.formatMessage({ id: 'heatDesign.propertyDetails.AddressTemperatureData' })}
      icon={<CloudSmallSunWaterdropOutlinedIcon />}
      sx={{ width: '100%' }}
    >
      <LabelValueDisplay
        label={intl.formatMessage({ id: 'heatDesign.propertyDetails.DegreeDays' })}
        value={degreeDays}
        orientation={labelValueDisplayOrientation}
        sx={{ width: '100%', minWidth: '0' }}
      />
      {climateZone && (
        <LabelValueDisplay
          label={intl.formatMessage({ id: 'heatDesign.propertyDetails.climateZone' })}
          value={climateZone}
          orientation={labelValueDisplayOrientation}
          sx={{ width: '100%', minWidth: '0' }}
        />
      )}
      {altitude !== undefined && (
        <LabelValueDisplay
          data-testid="altitude-label"
          label={intl.formatMessage({ id: 'heatDesign.propertyDetails.altitude.label' })}
          value={altitude}
          orientation={labelValueDisplayOrientation}
          sx={{ width: '100%', minWidth: '0' }}
        />
      )}
      <LabelValueDisplay
        label={intl.formatMessage({ id: 'heatDesign.propertyDetails.MeanAirTemp' })}
        value={localAnnualAverageExternalAirTemperature + ' °C'}
        orientation={labelValueDisplayOrientation}
        sx={{ width: '100%', minWidth: '0' }}
      />
      <LabelValueDisplay
        data-testid="heatDesign.propertyDetails.ExternalTempForLocation"
        label={intl.formatMessage({ id: 'heatDesign.propertyDetails.ExternalTempForLocation' })}
        value={baseOutdoorDesignTemperature + ' °C'}
        orientation={labelValueDisplayOrientation}
        sx={{ width: '100%', minWidth: '0' }}
      />
      {wasDwellingInitiallyInExposedLocation && (
        <Box sx={{ marginTop: 1 }}>
          <InputLabel htmlFor="dwellingExposedLocationInput">
            <Typography variant="inputLabel">
              {intl.formatMessage({ id: 'heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation' })}
            </Typography>
          </InputLabel>
          <Box
            id="dwellingExposedLocationInput"
            sx={{
              display: 'flex',
              flexDirection: isMobile ? 'column' : 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
              gap: 1,
            }}
          >
            <Button
              color={isDwellingInExposedLocation === true ? 'primary' : 'primary'}
              variant={isDwellingInExposedLocation === true ? 'contained' : 'outlined'}
              onClick={() => handleChangeExposedLocation(true)}
              size="small"
              sx={{
                width: '100%',
                flex: 1,
                borderRadius: 2,
                minHeight: '40px',
              }}
              disabled={!isDwellingInExposedLocation}
            >
              {intl.formatMessage({ id: 'baselineCalc.boolean.true' })}
            </Button>
            <Button
              color={isDwellingInExposedLocation === false ? 'primary' : 'primary'}
              variant={isDwellingInExposedLocation === false ? 'contained' : 'outlined'}
              onClick={() => handleChangeExposedLocation(false)}
              size="small"
              sx={{
                width: '100%',
                flex: 1,
                borderRadius: 2,
                minHeight: '40px',
              }}
            >
              {intl.formatMessage({ id: 'baselineCalc.boolean.false' })}
            </Button>
          </Box>
          <Typography mt={1} color={grey[900]} variant="body2">
            {intl.formatMessage({
              id: !isDwellingInExposedLocation
                ? 'heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.disabled'
                : 'heatDesign.propertyDetails.IsTheDwellingInAnExposedLocation.description',
            })}
          </Typography>
        </Box>
      )}
      <TemperatureCompensation />
    </HeatDesignCardV2>
  );
}
