import { Box } from '@mui/material';
import { Stack } from '@mui/system';
import { WindOutlinedIcon } from '@ui/components/StandardIcons/WindOutlinedIcon';
import { useIntl } from 'react-intl';
import HeatDesignCardV2 from '../HeatDesignCardV2';
import DerivedTemperatureValues from './DerivedTemperatureValues';
import DwellingUValues from './DwellingUValues';
import MagicplanActionsCard from './MagicplanActionsCard';
import PropertyDetails from './PropertyDetails';
import { VentilationForm } from './VentilationForm';

export default function DwellingOverview({
  formattedAddress,
  embed3dUrl,
}: Readonly<{
  formattedAddress: string;
  embed3dUrl: string;
}>) {
  const intl = useIntl();

  return (
    <Stack gap={2}>
      <PropertyDetails embed3dUrl={embed3dUrl} address={formattedAddress} />

      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 2fr', columnGap: 2 }}>
        <DwellingUValues />
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', columnGap: 2 }}>
          <Stack gap={2}>
            <HeatDesignCardV2
              icon={<WindOutlinedIcon />}
              title={intl.formatMessage({ id: 'heatDesign.ventilation.ventilation' })}
              sx={{ width: '100%' }}
            >
              <VentilationForm />
            </HeatDesignCardV2>
          </Stack>
          <Stack gap={3}>
            <DerivedTemperatureValues />
            <MagicplanActionsCard />
          </Stack>
        </Box>
      </Box>
    </Stack>
  );
}
