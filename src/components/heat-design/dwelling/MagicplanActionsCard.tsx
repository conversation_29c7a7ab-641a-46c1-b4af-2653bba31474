import { IconButton, Stack, Typography } from '@mui/material';
import { grey } from '@mui/material/colors';
import { Box } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { MagicplanIcon } from '@ui/components/Icons/Magicplan/MagicplanIcon';
import { SettingsOutlined } from '@ui/components/Icons/material';
import FileDownloadOutlinedIcon from '@ui/components/Icons/material/FileDownloadOutlined';
import { theme } from '@ui/theme/theme';
import { useGroundwork } from 'context/groundwork-context';
import { useRouter } from 'next/router';
import React from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import HeatDesignCardV2 from '../HeatDesignCardV2';
import { ContextPopover } from '../components/ContextPopover';
import { useHeatDesignSurveyForm } from '../hooks/useHeatDesignSurveyForm';
import { AdvancedMagicplanActions } from '../modals/AdvancedMagicplanActions';
import { formatDateTimeToProject } from '../utils/timeUtils';

type TSubmissionReport = {
  submittedAt: string;
  url: string;
};

function HeadingRow() {
  const [anchorElement, setAnchorElement] = React.useState<HTMLButtonElement | null>(null);

  const handleClose = () => {
    setAnchorElement(null);
  };

  return (
    <Stack direction="row" justifyContent="space-between" alignItems="center">
      <ContextPopover anchorElement={anchorElement} onClose={handleClose}>
        <AdvancedMagicplanActions onClose={handleClose} />
      </ContextPopover>

      <Button
        variant="outlined"
        onClick={(event) => setAnchorElement(event.currentTarget)}
        startIcon={<SettingsOutlined />}
        size="small"
        sx={{
          height: '30px',
          borderRadius: 3,
        }}
      >
        <FormattedMessage id="heatDesign.magicplan.advanced" />
      </Button>
    </Stack>
  );
}

function DownloadablePdf({ submittedAt, url }: TSubmissionReport) {
  const { formatMessage } = useIntl();
  return (
    <Box
      my={1}
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
      }}
      data-testid={`downloadable-pdfLink-object-${url}`}
    >
      <Typography data-testid={`downloadable-pdfLink-date-${submittedAt}`} variant="body2Emphasis">
        {formatMessage({
          id: 'common.label.date',
          defaultMessage: 'Date',
        })}
        {': '}
        <span
          style={{
            fontWeight: 400,
          }}
        >
          {submittedAt}
        </span>
      </Typography>
      <IconButton
        onClick={() => window.open(url, '_blank')}
        aria-label={formatMessage({ id: 'common.label.download' })}
        size="small"
        sx={{ border: '1px solid' }}
      >
        <FileDownloadOutlinedIcon />
      </IconButton>
    </Box>
  );
}

export default function MagicplanActionsCard() {
  const { locale } = useRouter();
  const { formatMessage } = useIntl();
  const {
    groundwork: { id: groundworkId },
  } = useGroundwork();
  const surveyForm = useHeatDesignSurveyForm({ installationGroundworkId: groundworkId?.value?.toString() });

  if (!groundworkId?.value) {
    return null;
  }

  let pdfLinks: TSubmissionReport[] = [];
  pdfLinks =
    surveyForm && surveyForm.submissionReports
      ? surveyForm.submissionReports.map((report) => ({
          submittedAt:
            report.submittedAt === undefined ? '?' : formatDateTimeToProject(report.submittedAt, locale ?? 'en-GB'),
          url: report.url,
        }))
      : [];

  return (
    <HeatDesignCardV2
      icon={<MagicplanIcon color={grey[900]} />}
      title={formatMessage({ id: 'heatDesign.magicplan.heading' })}
      titleComponent={<HeadingRow />}
      sx={{ width: '100%' }}
    >
      <Stack spacing={2}>
        <Typography variant="body2">
          {formatMessage({
            id: 'heatDesign.surveyForms.download.section.description',
            defaultMessage: 'Download the survey PDF that was generated when doing the on-site scan of the project.',
          })}
        </Typography>
        <Stack
          direction="column"
          paddingX={2}
          borderRadius={1}
          sx={{
            maxHeight: '180px',
            overflow: 'auto',
            backgroundColor: theme.palette.background.paper,
          }}
        >
          {pdfLinks.length === 0 ? (
            <Typography variant="body2" sx={{ textAlign: 'center' }}>
              {formatMessage({ id: 'heatDesign.surveyForms.download.notSubmitted' })}
            </Typography>
          ) : (
            pdfLinks.map(({ submittedAt, url }) => <DownloadablePdf key={url} submittedAt={submittedAt} url={url} />)
          )}
        </Stack>
      </Stack>
    </HeatDesignCardV2>
  );
}
