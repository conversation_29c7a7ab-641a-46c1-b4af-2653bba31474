import { Box, IconButton, Stack } from '@mui/material';
import React, { useState } from 'react';
import { useIntl } from 'react-intl';
import { useConstructionYear, useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { propertyIsValid } from '../Validator';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { PROJECT_FABRIC_TYPES } from '../stores/types';
import { useProjectUValues, useUValues, useUValuesActions } from '../stores/UValuesStore';
import { getWorstUValueForBuildingConstructionYear, isYearWithinUValueRange, UValueDiff } from '../uValues';
import UpdateUValuesModal from '../components/UpdateUValuesModal';
import UValueDiffTable from '../components/UValueDiffTable';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { SubmitIconButton } from '../components/SubmitIconButton';
import { CancelIconButton } from '../components/CancelIconButton';

export default function ConstructionYearForm() {
  const constructionYear = useConstructionYear();

  const uValues = useUValues();
  const uValuesActions = useUValuesActions();
  const projectUValues = useProjectUValues();

  const actions = useHeatDesignHouseInputsStore((s) => s.actions);

  const intl = useIntl();

  // We keep the new construction year in a temporary state so that we don't
  // update the Zustand store on every keystroke and can revert to the original
  // value if needed.
  const [newConstructionYear, setNewConstructionYear] = useState(constructionYear);

  // Keep track of whether we're editing the construction year
  const [isEditingConstructionYear, setIsEditingConstructionYear] = useState(false);

  // Keep track of the confirmation modal that's displayed when we suggest
  // U-values updates based on the contruction year
  const [uValueModalState, setUValueModalState] = useState<{ isOpen: boolean; uValueDiffs: UValueDiff[] }>({
    isOpen: false,
    uValueDiffs: [],
  });

  const saveNewConstructionYear = () => {
    setIsEditingConstructionYear(false);

    if (newConstructionYear !== constructionYear) {
      actions.setConstructionYear(newConstructionYear);
    }

    // If the updated construction year is not valid, exit early
    if (newConstructionYear === 0 || !propertyIsValid('dwelling', 'constructionYear', newConstructionYear)) return;

    // Update the dwelling-level U-values if we can find U-values that better
    // match the building year.
    const uValueDiffs: UValueDiff[] = [];
    PROJECT_FABRIC_TYPES.forEach((fabricType) => {
      const currentUValue = projectUValues[fabricType];
      // If there's no U-value for this fabric type OR the current U-value
      // doesn't match the construction year...
      if (currentUValue === undefined || !isYearWithinUValueRange(newConstructionYear, currentUValue)) {
        // Find a better U-value!
        const suggestedUValue = getWorstUValueForBuildingConstructionYear(uValues[fabricType], newConstructionYear);
        if (suggestedUValue) {
          uValueDiffs.push({
            before: currentUValue ?? null,
            after: suggestedUValue,
            fabricType,
          });
        }
      }
    });

    if (uValueDiffs.length > 0) {
      setUValueModalState({
        isOpen: true,
        uValueDiffs,
      });
    }
  };

  const acceptNewUValues = () => {
    uValueModalState.uValueDiffs.forEach(({ after: uValue, fabricType }) => {
      if (uValue !== null) {
        uValuesActions.setProjectUValue({ fabricType, uValue });
      }
    });
    setUValueModalState({
      isOpen: false,
      uValueDiffs: [],
    });
  };

  const cancelEditingConstructionYear = () => {
    setIsEditingConstructionYear(false);
    setNewConstructionYear(constructionYear);
  };

  const displayedConstructionYear = isEditingConstructionYear ? newConstructionYear : constructionYear;

  return (
    <Box sx={{ flex: 8 }}>
      <UpdateUValuesModal
        isOpen={uValueModalState.isOpen}
        onRequestClose={() =>
          setUValueModalState({
            isOpen: false,
            uValueDiffs: [],
          })
        }
        onConfirmChanges={acceptNewUValues}
      >
        <UValueDiffTable uValueDiffs={uValueModalState.uValueDiffs} />
      </UpdateUValuesModal>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          saveNewConstructionYear();
        }}
      >
        <Stack flexDirection="row" gap={1} alignItems="flex-end">
          <NumericTextField
            containerProps={{ sx: { flex: '1 1 auto', minWidth: '100px' } }}
            name="yearBuilt"
            required
            label={intl.formatMessage({ id: 'heatDesign.propertyDetails.YearBuilt' })}
            value={displayedConstructionYear}
            onChange={setNewConstructionYear}
            size="small"
            fullWidth
            disabled={!isEditingConstructionYear}
            aria-readonly={!isEditingConstructionYear}
            error={!propertyIsValid('dwelling', 'constructionYear', displayedConstructionYear)}
            autoFocus={isEditingConstructionYear}
          />
          {isEditingConstructionYear ? (
            <Stack flexDirection="row" gap={2} alignItems="flex-end">
              <SubmitIconButton data-testid="save-construction-year" type="submit" />
              <CancelIconButton onClick={cancelEditingConstructionYear} />
            </Stack>
          ) : (
            <IconButton
              data-testid="edit-construction-year"
              sx={{ border: `1px solid ${grey[900]}` }}
              onClick={() => setIsEditingConstructionYear(true)}
            >
              <PenOutlinedIcon height="22px" width="22px" color={grey[900]} />
            </IconButton>
          )}
        </Stack>
      </form>
    </Box>
  );
}
