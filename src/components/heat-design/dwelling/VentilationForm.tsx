import { memo } from 'react';
import { Stack, Typography, useMediaQuery } from '@mui/material';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Button } from '@ui/components/Button/Button';
import { isSimpleHouseInputVentilationDesign, useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { VENTILATION_CALCULATION_METHOD_LABELS, VentilationCalculationMethod } from '../stores/types';
import capitalize from 'lodash/capitalize';
import { SimpleVentilationForm } from './SimpleVentilationForm';
import { StandardVentilationForm } from './StandardVentilationForm';
import { FormattedMessage, useIntl } from 'react-intl';
import { getDefaultStandardVentilationInputs } from '../utils/helpers';

function VentilationFormComponent() {
  const { formatMessage } = useIntl();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const setVentilationDesign = useHeatDesignHouseInputsStore((s) => s.actions.setVentilationDesign);
  const isMobile = useMediaQuery('(max-width: 1200px)');

  const onCalculationMethodChange = (method: VentilationCalculationMethod) => {
    if (isSimpleHouseInputVentilationDesign(ventilationDesign) && method === VentilationCalculationMethod.STANDARD) {
      setVentilationDesign({
        ...getDefaultStandardVentilationInputs({
          calculationMethod: method,
          acphDefault: ventilationDesign.acphDefault,
        }),
      });
    } else if (method === VentilationCalculationMethod.SIMPLE) {
      setVentilationDesign({
        calculationMethod: method,
        acphDefault: ventilationDesign.acphDefault,
      });
    }
  };

  return (
    <Stack gap={2} data-testid="ventilation-form-wrapper" sx={{ minWidth: '200px' }}>
      <Stack gap={1}>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography variant="body2Emphasis">
            <FormattedMessage id="heatDesign.ventilation.calculationMethod" />
          </Typography>
          <TooltipAira
            title={formatMessage({ id: 'heatDesign.propertyOverview.ventilation.calculationMethodTooltip' })}
          />
        </Stack>
        <Stack direction={isMobile ? 'column' : 'row'} alignItems="center" gap={1}>
          {Object.values(VentilationCalculationMethod).map((method) => (
            <Button
              data-testid="ventilation-design-calculation-method"
              key={method}
              sx={{ padding: '8px', height: '40px', minHeight: '40px', width: '100%', borderRadius: 2, flex: 1 }}
              variant={method === ventilationDesign.calculationMethod ? 'contained' : 'outlined'}
              onClick={() => onCalculationMethodChange(method)}
            >
              {capitalize(VENTILATION_CALCULATION_METHOD_LABELS[method])}
            </Button>
          ))}
        </Stack>
      </Stack>
      {isSimpleHouseInputVentilationDesign(ventilationDesign) ? <SimpleVentilationForm /> : <StandardVentilationForm />}
    </Stack>
  );
}

export const VentilationForm = memo(VentilationFormComponent);
