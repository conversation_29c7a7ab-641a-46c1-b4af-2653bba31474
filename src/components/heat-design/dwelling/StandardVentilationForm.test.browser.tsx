import { renderWithProviders } from '../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { GroundworkContextProvider } from '../../../context/groundwork-context';
import React from 'react';
import { beforeEach, expect, vi } from 'vitest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { heatDesignTestSetup } from '../../../tests/utils/heatDesignTestSetup';
import { StandardVentilationForm } from './StandardVentilationForm';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';

vi.mock('../stores/HouseInputsStore', async (importOriginal) => {
  const actual = await importOriginal<typeof import('../stores/HouseInputsStore')>();

  let mockState = {
    constructionYear: 0,
    numberOfResidents: 1,
    embed3dUrl: '',
    magicplanAddress: '',
    isDwellingInExposedLocation: false,
    temperatureCompensation: 0,
    ventilationDesign: {
      acphDefault: { type: 'standardized', value: 'EN12831' },
      calculationMethod: 'standardVentilation',
      airPermeability: 1,
      airPermeabilityOverride: 123,
      dwellingExposure: 2,
      exposedFacades: 2,
    },
    actions: {
      setVentilationDesign: vi.fn((v) => {
        mockState = { ...mockState, ventilationDesign: v };
      }),
      setHouseInputs: vi.fn((v) => {
        mockState = { ...mockState, ...v };
      }),
      setConstructionYear: vi.fn((n) => (mockState.constructionYear = n)),
      setNumberOfResidents: vi.fn((n) => (mockState.numberOfResidents = n)),
      setTemperatureCompensation: vi.fn((n) => (mockState.temperatureCompensation = n)),
      setIsDwellingInExposedLocation: vi.fn((b) => (mockState.isDwellingInExposedLocation = b)),
    },
  };

  const useHeatDesignHouseInputsStore = (selector = (s: any) => s) => selector(mockState);
  const useHeatDesignHouseInputsActions = () => mockState.actions;
  const __setHouseInputsMock = (partial: Partial<typeof mockState>) => {
    mockState = { ...mockState, ...partial };
  };

  return {
    ...actual, // keep type guards and other exports
    useHeatDesignHouseInputsStore,
    useHeatDesignHouseInputsActions,
    __setHouseInputsMock,
  };
});

heatDesignTestSetup();

function StandardVentilationFormComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <StandardVentilationForm />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

beforeEach(async () => {
  renderWithProviders(<StandardVentilationFormComponent />);
  await waitFor(() => {
    expect(screen.getByTestId('standard-ventilation-form-wrapper')).toBeInTheDocument();
  });
});

describe('StandardVentilationForm', () => {
  it('should render standard ventilation form', () => {
    expect(screen.getByTestId('standard-ventilation-form-wrapper')).toBeInTheDocument();
  });

  it('should render air permeability buttons', () => {
    const ventilationMethodButtons = screen.getAllByTestId(/air-permeability-button/);
    expect(ventilationMethodButtons.length).toBeGreaterThan(0);
  });

  it('should render exposure of house buttons', () => {
    const ventilationMethodButtons = screen.getAllByTestId('house-exposure-button');
    expect(ventilationMethodButtons.length).toBeGreaterThan(0);
  });

  it('should render ACPH defaults', () => {
    const acphDefaults = screen.getByTestId('acph-defaults-wrapper');
    expect(acphDefaults).toBeVisible();
  });

  it('should render air permeability override -field when selecting pulse test', () => {
    const pulseTestButton = screen.getByTestId(
      `air-permeability-button-${AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST}`,
    );
    fireEvent.click(pulseTestButton);
    const airPermeabilityOverride = screen.getByTestId('air-permeability-override');
    expect(airPermeabilityOverride).toBeVisible();
  });
});
