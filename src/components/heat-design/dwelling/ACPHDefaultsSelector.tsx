import { useGroundwork } from 'context/groundwork-context';
import {
  ACPH_DEFAULTS_PER_MARKET,
  ACPH_STANDARDIZED_DEFAULT_VALUES,
  ACPHStandardizedDefaults,
} from '../utils/averageAirChangePerHour';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Stack,
  SxProps,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { isStandardHouseInputVentilationDesign, useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { ACPHDefaultValue, Room, RoomType } from '../stores/types';
import { NumericField } from 'components/bill-of-materials/components/common/NumericField';
import { ACPH_DEFAULT_CUSTOM_VALUE } from '../constants';
import { propertyIsValid } from '../Validator';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { useMemo, useState } from 'react';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { useRooms } from '../stores/RoomsStore';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Button } from '@ui/components/Button/Button';
import { MessageKey } from '../../../messageType';

// Some of these room types do not have tooltips defined, however it seems that the ones with no tooltip defined
// are in fact mapped into one of the roomtypes with a tooltip in the backend.
const ROOM_TOOLTIPS: Record<RoomType, MessageKey | undefined> = {
  Bathroom: 'heatDesign.propertyOverview.ventilation.roomTooltip.bathroom',
  Bedroom: 'heatDesign.propertyOverview.ventilation.roomTooltip.bedroom',
  'Bedroom, including en suite bathroom': undefined,
  'Bedroom/study': undefined,
  'Bedsitting room': undefined,
  'Breakfast room': undefined,
  'Cloakroom/WC': undefined,
  'Dining room': 'heatDesign.propertyOverview.ventilation.roomTooltip.diningRoom',
  'Dressing room': 'heatDesign.propertyOverview.ventilation.roomTooltip.dressingRoom',
  'Family/breakfast room': undefined,
  'Games room': 'heatDesign.propertyOverview.ventilation.roomTooltip.gamesRoom',
  Hall: 'heatDesign.propertyOverview.ventilation.roomTooltip.hall',
  'Internal room or corridor': undefined,
  Kitchen: 'heatDesign.propertyOverview.ventilation.roomTooltip.kitchen',
  Landing: 'heatDesign.propertyOverview.ventilation.roomTooltip.landing',
  'Living room': 'heatDesign.propertyOverview.ventilation.roomTooltip.livingRoom',
  'Lounge/sitting room': undefined,
  'Shower room': undefined,
  'Store room': 'heatDesign.propertyOverview.ventilation.roomTooltip.storeRoom',
  Study: 'heatDesign.propertyOverview.ventilation.roomTooltip.study',
  Toilet: 'heatDesign.propertyOverview.ventilation.roomTooltip.toilet',
  'Utility room': 'heatDesign.propertyOverview.ventilation.roomTooltip.utilityRoom',
  Other: 'heatDesign.propertyOverview.ventilation.roomTooltip.other',
};

export function ACPHDefaultsSelector() {
  const { formatMessage } = useIntl();
  const { countryCode } = useGroundwork();
  const options = ACPH_DEFAULTS_PER_MARKET[countryCode];
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const setVentilationDesign = useHeatDesignHouseInputsStore((s) => s.actions.setVentilationDesign);
  const [isRoomTypeDropdownOpen, setIsRoomTypeDropdownOpen] = useState(false);
  const rooms = useRooms();
  const isMobile = useMediaQuery('(max-width: 1200px)');

  function getToggleValue(acphDefault: ACPHDefaultValue): string {
    switch (acphDefault.type) {
      case 'standardized':
        return acphDefault.standard;
      case 'custom':
        return 'custom';
    }
  }

  function handleACPHChange(value: string): void {
    setVentilationDesign({
      ...ventilationDesign,
      acphDefault:
        value === 'custom'
          ? { type: 'custom', value: ACPH_DEFAULT_CUSTOM_VALUE }
          : { type: 'standardized', standard: value as ACPHStandardizedDefaults },
    });
  }

  const uniqueSortedRooms = useMemo(() => {
    const roomMap = new Map<string, Room>();
    rooms.forEach((room) => {
      if (!roomMap.has(room.roomType)) {
        roomMap.set(room.roomType, room);
      }
    });
    return Array.from(roomMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [rooms]);

  const isPulseTest =
    isStandardHouseInputVentilationDesign(ventilationDesign) &&
    ventilationDesign.airPermeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST;
  const buttonStyle: SxProps = {
    width: '100%',
    p: '8px 16px',
    height: '40px',
    borderRadius: 2,
  };

  return (
    <Stack gap={1} data-testid="acph-defaults-wrapper">
      <Stack direction="row" alignItems="center" gap={1}>
        <Typography variant="body2Emphasis">
          <FormattedMessage id="heatDesign.ventilation.acphDefaults" />
        </Typography>
        <TooltipAira title={formatMessage({ id: 'heatDesign.propertyOverview.ventilation.acphDefaultsTooltip' })} />
      </Stack>
      <Box sx={{ display: 'grid', gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr', gap: 1 }}>
        {options.map((standardizedDefault) => {
          if (isPulseTest) {
            buttonStyle.border = 'none !important'; // Have to override the disabled button style with !important
          }
          return (
            <Button
              data-testid={`acph-default-button-${standardizedDefault}`}
              disabled={isPulseTest}
              key={standardizedDefault}
              onClick={() => handleACPHChange(standardizedDefault)}
              variant={getToggleValue(ventilationDesign.acphDefault) === standardizedDefault ? 'contained' : 'outlined'}
              sx={buttonStyle}
            >
              {standardizedDefault}
            </Button>
          );
        })}
        <Button
          disabled={isPulseTest}
          variant={getToggleValue(ventilationDesign.acphDefault) === 'custom' ? 'contained' : 'outlined'}
          onClick={() => handleACPHChange('custom')}
          sx={{ ...buttonStyle, border: isPulseTest ? 'none !important' : undefined }}
        >
          Custom
        </Button>
      </Box>
      {isPulseTest && (
        <Typography variant="body2">
          <FormattedMessage id="heatDesign.ventilation.acphDefaultsDisabledPulseTest" />
        </Typography>
      )}
      {ventilationDesign.acphDefault.type === 'custom' && (
        <>
          <Stack direction="row" alignItems="center" gap={1}>
            <Typography variant="body2Emphasis">
              <FormattedMessage id="heatDesign.ventilation.houseWideAcph" />
            </Typography>
            <TooltipAira title={formatMessage({ id: 'heatDesign.ventilation.houseWideAcphTooltip' })} />
          </Stack>

          <NumericField
            size="small"
            name="custom-acph"
            suffix="ACPH"
            value={ventilationDesign.acphDefault.value}
            onChange={(value) => {
              setVentilationDesign({
                ...ventilationDesign,
                acphDefault: { type: 'custom', value: value ?? ACPH_DEFAULT_CUSTOM_VALUE },
              });
            }}
            error={!propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault)}
          />
        </>
      )}

      <Accordion sx={{ backgroundColor: 'transparent' }}>
        <AccordionSummary sx={{ padding: 0 }} onClick={() => setIsRoomTypeDropdownOpen(!isRoomTypeDropdownOpen)}>
          <Stack
            sx={{ width: '100%', padding: 1, backgroundColor: 'transparent' }}
            direction="row"
            alignItems="center"
            justifyContent="space-between"
          >
            <Typography variant="body2Emphasis">
              <FormattedMessage id="heatDesign.ventilation.roomType" />
            </Typography>
            <Stack direction="row" alignItems="center" gap={1}>
              <Typography variant="body2Emphasis">ACPH</Typography>
              <Chevron
                height={24}
                width={24}
                direction={isRoomTypeDropdownOpen ? 'up' : 'down'}
                transitionDuration="0.2s"
              />
            </Stack>
          </Stack>
        </AccordionSummary>
        <AccordionDetails sx={{ padding: 0 }}>
          <Stack gap={1}>
            {uniqueSortedRooms.map((room) => {
              const acphValue =
                ventilationDesign.acphDefault.type === 'standardized'
                  ? ACPH_STANDARDIZED_DEFAULT_VALUES[ventilationDesign.acphDefault.standard](room.roomType)
                  : ventilationDesign.acphDefault.value;
              return (
                <LabelValueDisplay
                  key={room.id}
                  sx={{ width: '100%' }}
                  label={
                    <Stack gap={1} direction="row" alignItems="center">
                      <Typography>{room.roomType}</Typography>
                      {ROOM_TOOLTIPS[room.roomType] && (
                        <TooltipAira
                          title={formatMessage({
                            id: ROOM_TOOLTIPS[room.roomType],
                          })}
                        />
                      )}
                    </Stack>
                  }
                  value={acphValue}
                />
              );
            })}
          </Stack>
        </AccordionDetails>
      </Accordion>
    </Stack>
  );
}
