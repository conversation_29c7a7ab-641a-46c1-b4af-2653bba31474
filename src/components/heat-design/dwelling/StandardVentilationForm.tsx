import { memo, useCallback, useMemo } from 'react';
import { Stack, Typography, useMediaQuery } from '@mui/material';
import { ACPHDefaultsSelector } from './ACPHDefaultsSelector';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Button } from '@ui/components/Button/Button';
import { isStandardHouseInputVentilationDesign, useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import {
  AIR_PERMEABILITY_LABELS,
  AIR_PERMEABILITY_OPTIONS,
  DWELLING_EXPOSURE_LABELS,
  DWELLING_EXPOSURE_OPTIONS,
} from '../stores/types';
import { NumericField } from '../../bill-of-materials/components/common/NumericField';
import { ACPHStandardizedDefaults } from '../utils/averageAirChangePerHour';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';
import { FormattedMessage, useIntl } from 'react-intl';
import { propertyIsValid } from '../Validator';
import { useRooms, useRoomsActions } from '../stores/RoomsStore';
import debounce from 'lodash/debounce';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { useAirPermeabilityValue } from '../hooks/useAirPermabilityValue';

function StandardVentilationFormComponent() {
  const { formatMessage } = useIntl();
  const rooms = useRooms();
  const { setRooms } = useRoomsActions();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const setVentilationDesign = useHeatDesignHouseInputsStore((s) => s.actions.setVentilationDesign);
  const isMobile = useMediaQuery('(max-width: 1200px)');
  const airPermeabilityValue = useAirPermeabilityValue();

  // When using pulse test, we should clear any ACPH overrides in rooms, as they are not applicable.
  // We decided to go with this simple method instead of trying to ignore the room-level ACPH values in the calculations,
  // until such a time that it becomes actually relevant to implement a more complicated solution
  const resetAirChangeOverridesInAllRooms = useCallback(() => {
    setRooms(
      rooms.map((room) => {
        const newRoom = { ...room, openFlue: 'none' as const };
        delete newRoom.avgAirChangesPerHourOverride;
        return newRoom;
      }),
    );
  }, [rooms, setRooms]);
  const debouncedResetAirChangeOverrides = useMemo(
    () => debounce(resetAirChangeOverridesInAllRooms, 1000),
    [resetAirChangeOverridesInAllRooms],
  );
  if (!isStandardHouseInputVentilationDesign(ventilationDesign)) {
    return null;
  }
  const onAirPermeabilityChange = (permeability: AirPermeabilityOption) => {
    setVentilationDesign({
      ...ventilationDesign,
      airPermeability: permeability,
      acphDefault:
        permeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST
          ? {
              type: 'standardized',
              standard: ACPHStandardizedDefaults.EN_12831,
            }
          : ventilationDesign.acphDefault,
    });
    if (
      permeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST &&
      ventilationDesign.airPermeabilityOverride
    ) {
      resetAirChangeOverridesInAllRooms();
    }
  };

  const onAirPermeabilityOverrideChange = (value: number | undefined) => {
    setVentilationDesign({ ...ventilationDesign, airPermeabilityOverride: value });
    if (value) {
      debouncedResetAirChangeOverrides();
    }
  };

  return (
    <Stack gap={2} data-testid="standard-ventilation-form-wrapper">
      <Stack gap={2}>
        <Stack gap={1}>
          <Stack direction="row" alignItems="center" gap={1}>
            <Typography variant="body2Emphasis">
              <FormattedMessage id="heatDesign.ventilation.airPermeability" />
            </Typography>
            <TooltipAira
              title={formatMessage({ id: 'heatDesign.propertyOverview.ventilation.airPermeabilityTooltip' })}
            />
          </Stack>
          <Stack direction={isMobile ? 'column' : 'row'} alignItems="center" gap={1}>
            {AIR_PERMEABILITY_OPTIONS.map((permeability) => (
              <Button
                data-testid={`air-permeability-button-${permeability}`}
                key={permeability}
                sx={{ padding: '8px', minHeight: '40px', height: '40px', borderRadius: 2, flex: 1, width: '100%' }}
                variant={permeability === ventilationDesign.airPermeability ? 'contained' : 'outlined'}
                onClick={() => onAirPermeabilityChange(permeability)}
              >
                {AIR_PERMEABILITY_LABELS[permeability]}
              </Button>
            ))}
          </Stack>
        </Stack>
        {ventilationDesign.airPermeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST && (
          <NumericField
            data-testid="air-permeability-override"
            name="air-permeability-override"
            value={ventilationDesign.airPermeabilityOverride}
            onChange={onAirPermeabilityOverrideChange}
            error={!propertyIsValid('dwelling', 'pulseTest', ventilationDesign)}
            size="small"
            sx={{
              '.MuiInputAdornment-root > p': {
                whiteSpace: 'nowrap',
              },
              paddingTop: 0,
              paddingBottom: 0,
              minHeight: '40px',
            }}
            suffix="@50Pa m³/(m² ∙ h)"
          />
        )}
        {[
          AirPermeabilityOption.AIR_PERMEABILITY_OPTION_CIBSE,
          AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
        ].includes(ventilationDesign.airPermeability) && (
          <LabelValueDisplay
            label={<FormattedMessage id="heatDesign.ventilation.airPermeability" />}
            value={
              <Stack direction="row" alignItems="center" gap={1}>
                <Typography variant="body2Emphasis">{airPermeabilityValue}</Typography>
                <Typography variant="body2">@50Pa m³/(m² ∙ h)</Typography>
              </Stack>
            }
            sx={{
              width: '100%',
            }}
          />
        )}
      </Stack>

      <Stack gap={1}>
        <Stack direction="row" alignItems="center" gap={1}>
          <Typography variant="body2Emphasis">
            <FormattedMessage id="heatDesign.ventilation.exposureOfHouse" />
          </Typography>
          <TooltipAira title={formatMessage({ id: 'heatDesign.propertyOverview.ventilation.exposureTooltip' })} />
        </Stack>
        <Stack direction={isMobile ? 'column' : 'row'} alignItems="center" gap={1}>
          {DWELLING_EXPOSURE_OPTIONS.map((singleExposure) => (
            <Button
              data-testid="house-exposure-button"
              key={singleExposure}
              sx={{ padding: '8px', minHeight: '40px', height: '40px', borderRadius: 2, flex: 1, width: '100%' }}
              variant={singleExposure === ventilationDesign.dwellingExposure ? 'contained' : 'outlined'}
              onClick={() =>
                setVentilationDesign({
                  ...ventilationDesign,
                  dwellingExposure: singleExposure,
                })
              }
            >
              {DWELLING_EXPOSURE_LABELS[singleExposure]}
            </Button>
          ))}
        </Stack>
      </Stack>

      <ACPHDefaultsSelector />
    </Stack>
  );
}

export const StandardVentilationForm = memo(StandardVentilationFormComponent);
