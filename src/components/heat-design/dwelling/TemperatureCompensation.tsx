import { InputLabel, Stack, Typography } from '@mui/material';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { LabelTooltip } from '@ui/components/Tooltip/Tooltip';
import { useIntl } from 'react-intl';
import { useAdjustedOutdoorDesignTemperature, useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { toTwoDecimalPlaces } from '../utils/helpers';

export default function TemperatureCompensation() {
  const intl = useIntl();
  const { actions, temperatureCompensation } = useHeatDesignHouseInputsStore();
  const adjustedOutdoorDesignTemperature = useAdjustedOutdoorDesignTemperature();

  return (
    <Stack direction="column" gap={1} mt={1} data-testid="heat-design-first-page-outside-temp-component">
      <LabelTooltip
        label={
          <InputLabel
            sx={{
              // Necessary so that the input is horizontally-aligned with the
              // tooltip as flex rules don't seem to have an affect.
              // paddingTop: '0.5em',
              marginBottom: 0,
            }}
            htmlFor="setTemperatureCompensation"
          >
            <Typography variant="inputLabel">
              {intl.formatMessage({ id: 'heatDesign.propertyDetails.TemperatureCompensation' })}
            </Typography>
          </InputLabel>
        }
        tooltipLabel={intl.formatMessage({ id: 'heatDesign.propertyDetails.TemperatureCompensation.description' })}
      />

      <Stack direction="row" gap={2}>
        <NumericTextField
          value={toTwoDecimalPlaces(temperatureCompensation ?? 0)}
          onChange={(temp: number) => {
            actions.setTemperatureCompensation(temp);
          }}
          name="setTemperatureCompensation"
          size="small"
          suffix="°C"
        />
        <LabelValueDisplay
          data-testid="heat-design-first-page-outside-temp"
          label={intl.formatMessage({ id: 'heatDesign.propertyDetails.OutsideTemp' })}
          value={toTwoDecimalPlaces(adjustedOutdoorDesignTemperature) + ' °C'}
          sx={{ width: '100%' }}
        />
      </Stack>
    </Stack>
  );
}
