import { renderWithProviders } from '../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { GroundworkContextProvider } from '../../../context/groundwork-context';
import React from 'react';
import { beforeEach, expect } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { heatDesignTestSetup } from '../../../tests/utils/heatDesignTestSetup';
import { ACPHDefaultsSelector } from './ACPHDefaultsSelector';

vi.mock('../stores/HouseInputsStore', async (importOriginal) => {
  const actual = await importOriginal<typeof import('../stores/HouseInputsStore')>();

  let mockState = {
    constructionYear: 0,
    numberOfResidents: 1,
    embed3dUrl: '',
    magicplanAddress: '',
    isDwellingInExposedLocation: false,
    temperatureCompensation: 0,
    ventilationDesign: {
      acphDefault: { type: 'standardized', value: 'EN12831' },
      calculationMethod: 'standardVentilation',
      airPermeability: 1,
      airPermeabilityOverride: 123,
      dwellingExposure: 2,
      exposedFacades: 2,
    },
    actions: {
      setVentilationDesign: vi.fn((v) => {
        mockState = { ...mockState, ventilationDesign: v };
      }),
      setHouseInputs: vi.fn((v) => {
        mockState = { ...mockState, ...v };
      }),
      setConstructionYear: vi.fn((n) => (mockState.constructionYear = n)),
      setNumberOfResidents: vi.fn((n) => (mockState.numberOfResidents = n)),
      setTemperatureCompensation: vi.fn((n) => (mockState.temperatureCompensation = n)),
      setIsDwellingInExposedLocation: vi.fn((b) => (mockState.isDwellingInExposedLocation = b)),
    },
  };

  const useHeatDesignHouseInputsStore = (selector = (s: any) => s) => selector(mockState);
  const useHeatDesignHouseInputsActions = () => mockState.actions;
  const __setHouseInputsMock = (partial: Partial<typeof mockState>) => {
    mockState = { ...mockState, ...partial };
  };

  return {
    ...actual, // keep type guards and other exports
    useHeatDesignHouseInputsStore,
    useHeatDesignHouseInputsActions,
    __setHouseInputsMock,
  };
});
heatDesignTestSetup();

function SimpleVentilationFormComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <ACPHDefaultsSelector />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

beforeEach(async () => {
  renderWithProviders(<SimpleVentilationFormComponent />);
  await waitFor(() => {
    expect(screen.getByTestId('acph-defaults-wrapper')).toBeInTheDocument();
  });
});

describe('ACPHDefaultsSelector', () => {
  it('should render', () => {
    expect(screen.getByTestId('acph-defaults-wrapper')).toBeInTheDocument();
  });

  it('should render ACPH defaults options', () => {
    const acphDefaults = screen.getAllByTestId(/acph-default-button/);
    expect(acphDefaults.length).toBeGreaterThan(0);
  });

  it('should have options disabled if pulse test is selected', async () => {
    renderWithProviders(<SimpleVentilationFormComponent />);
    await waitFor(async () => {
      const disabledButtons = screen
        .getAllByTestId(/acph-default-button/)
        .filter((button) => button.classList.contains('Mui-disabled'));
      expect(disabledButtons.length).toBeGreaterThan(0);
    });
  });
});
