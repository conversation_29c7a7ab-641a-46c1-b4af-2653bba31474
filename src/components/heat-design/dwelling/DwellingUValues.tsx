import { HousePersonOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutlinedIcon';
import { useIntl } from 'react-intl';
import { UValueInput } from '../components/u-value-input/UValueInput';
import HeatDesignCardV2 from '../HeatDesignCardV2';
import { UValue } from '../models/UValue';
import { useFloors } from '../stores/FloorsStore';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useRooms } from '../stores/RoomsStore';
import { PROJECT_FABRIC_TYPES, ProjectFabricTypes } from '../stores/types';
import { useProjectUValues, useUValuesActions } from '../stores/UValuesStore';
import { aRoomHasUValueForProjectFabricType } from '../utils/helpers';

export default function DwellingUValues() {
  const { formatMessage } = useIntl();
  const rooms = useRooms();
  const floors = useFloors();
  const constructionYear = useConstructionYear();
  const projectUValues = useProjectUValues();
  const uValuesActions = useUValuesActions();

  const handleUValueSelection = (fabricType: ProjectFabricTypes, uValue?: UValue) => {
    if (!uValue) return;
    uValuesActions.setProjectUValue({ fabricType, uValue });
  };

  return (
    <HeatDesignCardV2
      sx={{ gap: 2, minWidth: '200px' }}
      icon={<HousePersonOutlinedIcon />}
      title={formatMessage({ id: 'heatDesign.uValues.uValues' })}
    >
      {PROJECT_FABRIC_TYPES.map((fabricType) => {
        const fabricTypeIsPresent = aRoomHasUValueForProjectFabricType(fabricType, rooms, floors);

        return (
          <UValueInput
            key={fabricType}
            surfaceType={fabricType}
            placeholder={formatMessage({ id: 'heatDesign.uValues.placeholder' })}
            value={projectUValues[fabricType]}
            label={formatMessage({ id: `heatDesign.roomSurfaceTypes.${fabricType}` })}
            disableClearable
            // Only show an error if the fabric type is present in the dwelling
            error={fabricTypeIsPresent && !projectUValues[fabricType]}
            onChange={(newUValue) => handleUValueSelection(fabricType, newUValue)}
            data-testid={`heat-design-uvalue-modal-select-${fabricType}`}
            inputProps={{ 'data-testid': `heat-design-uvalue-modal-input-${fabricType}` }}
            constructionYear={constructionYear}
          />
        );
      })}
    </HeatDesignCardV2>
  );
}
