import { renderWithProviders } from '../../../tests/utils/testUtils';
import { IntlProvider } from 'react-intl';
import { GroundworkContextProvider } from '../../../context/groundwork-context';
import React from 'react';
import { VentilationForm } from './VentilationForm';
import { beforeEach, expect } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { heatDesignTestSetup } from '../../../tests/utils/heatDesignTestSetup';

heatDesignTestSetup();

function VentilationFormComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <VentilationForm />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

beforeEach(async () => {
  renderWithProviders(<VentilationFormComponent />);
  await waitFor(() => {
    expect(screen.getByTestId('ventilation-form-wrapper')).toBeInTheDocument();
  });
});

describe('VentilationForm', () => {
  it('should render ventilation form', () => {
    expect(screen.getByTestId('ventilation-form-wrapper')).toBeInTheDocument();
  });

  it('should render calculation method buttons', () => {
    const ventilationMethodButtons = screen.getAllByTestId('ventilation-design-calculation-method');
    expect(ventilationMethodButtons.length).toBeGreaterThan(0);
  });
});
