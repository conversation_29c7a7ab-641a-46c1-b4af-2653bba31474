import React, { ReactNode } from 'react';
import { Box } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { Heading } from '@ui/components/Heading/Heading';
import { InfoRow } from '../report/shared/InfoRow';

interface UnitDetail {
  testId: string;
  label: ReactNode;
  value: number | string;
  icon: ReactNode;
  trailingContent?: ReactNode;
  hide?: boolean;
}
export function FlexItem({ children }: { children: ReactNode }) {
  return <Box sx={{ flex: '1 1 calc(50% - 16px)' }}>{children}</Box>;
}

function InfoRowsSection({ header, units }: { header: string; units: UnitDetail[] }) {
  return (
    <FlexItem>
      <Box sx={{ background: beige[200], p: 2, borderRadius: '16px 16px 0 0' }}>
        <Heading variant="body1Emphasis" level={3}>
          {header}
        </Heading>
      </Box>
      {units
        .filter((unit) => !unit.hide)
        .map((unit, index, arr) => (
          <InfoRow
            key={`${unit.label}-${unit.value}`}
            data-testid={unit.testId}
            icon={unit.icon}
            label={unit.label}
            value={unit.value}
            labelFontVariant="body1"
            trailingContent={unit.trailingContent}
            sx={{
              p: 2,
              borderBottom: 'none',
              background: index % 2 === 0 ? beige[100] : 'white',
              borderRadius: index === arr.length - 1 ? '0 0 16px 16px' : 0,
            }}
          />
        ))}
    </FlexItem>
  );
}

export default InfoRowsSection;
