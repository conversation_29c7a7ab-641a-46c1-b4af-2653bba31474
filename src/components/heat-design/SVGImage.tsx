import { ImgHTMLAttributes, memo } from 'react';

type SVGImageProps = Omit<ImgHTMLAttributes<HTMLImageElement>, 'src' | 'alt'> & {
  svgData: string;
  alt: string;
};

function SVGImageComponent({ svgData, alt, ...props }: SVGImageProps) {
  // Next wants to optimize our images, which is nice, but makes no sense for inline SVG:s.
  // Remove text-shadow from SVG to avoid performance issues (especially for Edge)
  const svgWithoutTextShadow = svgData.replace(/(<svg[^>]*\bstyle="[^"]*)text-shadow:.*?;([^"]*")/g, '$1$2');
  // eslint-disable-next-line @next/next/no-img-element
  return <img src={`data:image/svg+xml;utf8,${encodeURIComponent(svgWithoutTextShadow)}`} alt={alt} {...props} />;
}

export const SVGImage = memo(SVGImageComponent);
