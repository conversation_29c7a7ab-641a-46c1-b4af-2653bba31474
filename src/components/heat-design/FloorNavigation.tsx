import { Stack, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { FloorProps } from './stores/types';
import FloorOverviewButton from './room-components/FloorOverviewButton';
import { formatSquareMeters, formatWatts } from '../../utils/helpers';
import { useFloors } from './stores/FloorsStore';
import { useDwellingHeatDesignResult } from './hooks/useDwellingHeatDesignResult';
import { RoomHeatDesignResult } from './stores/OutputsStore';

const getFloorArea = (rooms: RoomHeatDesignResult[]) => rooms.reduce((acc, room) => room.totalFloorArea + acc, 0);

const getHeatLossWatts = (rooms: RoomHeatDesignResult[]) =>
  rooms.reduce((acc, room) => room.totalRoom.heatLoss + acc, 0);

export function FloorNavigation({
  selectedFloor,
  selectFloor,
  displayFloorArea,
  displayFloorWattage,
  validator,
}: {
  selectedFloor: FloorProps | undefined;
  selectFloor: (floor: FloorProps) => void;
  displayFloorArea?: boolean;
  displayFloorWattage?: boolean;
  validator?: (floor: FloorProps) => boolean;
}) {
  const floors = useFloors();
  const { locale } = useRouter();

  const { floorsResults } = useDwellingHeatDesignResult();

  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const getAreaForFloor = (floorId: string) =>
    getFloorArea(roomHeatDesignResults.filter((rhdr) => rhdr.floorId === floorId));

  const getHeatLossForFloorWatts = (floorId: string) =>
    getHeatLossWatts(roomHeatDesignResults.filter((rhdr) => rhdr.floorId === floorId));

  return (
    <Stack direction="row" sx={{ gap: 2, mb: 2 }}>
      {floors.map((floor) => {
        const isValid = validator ? validator(floor) : true;
        return (
          <FloorOverviewButton
            key={floor.uid}
            isValid={isValid}
            floor={floor}
            isSelected={floor.uid === selectedFloor?.uid}
            selectFloor={() => selectFloor(floor)}
          >
            {(displayFloorArea || displayFloorWattage) && (
              <>
                {displayFloorArea && (
                  <Typography
                    data-testid={`${floor.floorName}-area-label`}
                    component="p"
                    variant="body1Emphasis"
                    sx={{ color: 'inherit' }}
                  >
                    {formatSquareMeters(getAreaForFloor(floor.uid), 2)}
                  </Typography>
                )}
                {displayFloorWattage && (
                  <Typography
                    data-testid={`${floor.floorName}-wattage-label`}
                    component="p"
                    variant="body1Emphasis"
                    sx={{ color: 'inherit' }}
                  >
                    {formatWatts(getHeatLossForFloorWatts(floor.uid), locale, 0, true)}
                  </Typography>
                )}
              </>
            )}
          </FloorOverviewButton>
        );
      })}
    </Stack>
  );
}
