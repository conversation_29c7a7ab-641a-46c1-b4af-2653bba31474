import { HeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { createSampleRoom } from 'tests/utils/testUtils';
import { isHeatDesignValid } from './Validator';
import { AdjacentKind } from './stores/types';
import { BASE_U_VALUES } from './u-values/global-u-values';

const VALID_HEAT_DESIGN = {
  dataSourceReferences: [],
  climate: {
    baseOutdoorDesignTemperatureCelsius: 14,
    heatingDegreeDays: 100,
    localAnnualAverageExternalAirTemperatureCelsius: 10,
    adjustedOutdoorDesignTemperatureCelsius: 14,
  },
  dwelling: {
    constructionYear: 2000,
    numberOfResidents: 1,
    dataSourceReferences: [],
    exposedLocation: false,
    floors: [],
    id: { value: '0' },
    systemDesign: { flowReturnDeltaT: 20, flowTemperatureCelsius: 55 },
    temperatureAdjustmentCelsius: 0,
  },
  customUValues: [],
  dwellingUValueDefaults: { defaultEntries: [] },
  id: { value: '0' },
  auxiliaryData: {},
} as HeatDesign;

describe('isHeatDesignValid', () => {
  test('returns false for invalid inputs', () => {
    expect(
      isHeatDesignValid({ dwelling: { constructionYear: -100, numberOfResidents: 2 } } as HeatDesign, [], [], {}),
    ).toEqual(false);

    expect(
      isHeatDesignValid({ dwelling: { constructionYear: 2020, numberOfResidents: -1 } } as HeatDesign, [], [], {}),
    ).toEqual(false);

    expect(
      isHeatDesignValid(
        VALID_HEAT_DESIGN,
        [
          {
            floorName: 'Basement',
            floorNr: -1,
            imageSvgData: '',
            simplifiedImageSvgData: '',
            soilPercentageDefault: null, // This should not be null
            uid: '0',
          },
        ],
        [],
        {},
      ),
    ).toEqual(false);

    expect(
      isHeatDesignValid(
        VALID_HEAT_DESIGN,
        [
          {
            floorName: 'Ground floor',
            floorNr: 0,
            imageSvgData: '',
            simplifiedImageSvgData: '',
            soilPercentageDefault: null,
            uid: 'floor-0',
          },
        ],
        [
          createSampleRoom({
            id: 'room-0',
            floorId: 'floor-0',
            surfaces: {
              doors: [
                {
                  area: 9,
                  height: 3,
                  width: 3,
                  imageMap: { coordinates: [] },
                  surfaceType: 'doors',
                  uid: 'u-value',
                  // Missing u-value here
                },
              ],
              floors: [],
              roofGlazings: [],
              roofsOrCeilings: [],
              walls: [],
              windows: [],
            },
          }),
        ],
        {},
      ),
    ).toEqual(false);
  });

  test('returns true for valid inputs', () => {
    expect(isHeatDesignValid(VALID_HEAT_DESIGN, [], [], {})).toEqual(true);

    expect(
      isHeatDesignValid(
        VALID_HEAT_DESIGN,
        [
          {
            floorName: 'Basement',
            floorNr: -1,
            imageSvgData: '',
            simplifiedImageSvgData: '',
            soilPercentageDefault: 0.5,
            uid: '0',
          },
        ],
        [],
        {},
      ),
    ).toEqual(true);
  });

  test('return true for any temp if space above is not heated', () => {
    const room = createSampleRoom({
      id: 'room-0',
      floorId: 'floor-0',
      surfaces: {
        roofsOrCeilings: [
          {
            surfaceType: 'roofsOrCeilings',
            uid: '0',
            uValue: BASE_U_VALUES.roofsOrCeilings[0],
            area: 10,
            spaceAbove: {
              type: AdjacentKind.Unheated,
              tempOfSpaceAbove: 0,
            },
          },
        ],
        doors: [],
        floors: [],
        roofGlazings: [],
        walls: [],
        windows: [],
      },
    });

    expect(
      isHeatDesignValid(VALID_HEAT_DESIGN, [], [room], {
        roof: BASE_U_VALUES.roofsOrCeilings[0],
      }),
    ).toEqual(true);
  });
});
