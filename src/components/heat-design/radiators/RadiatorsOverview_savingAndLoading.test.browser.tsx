import React from 'react';
import { cleanup, fireEvent, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { vi } from 'vitest';
import { v4 as uuidv4 } from 'uuid';
import {
  addExistingRadiator,
  addNewRadiator,
  chooseRadiatorFromCatalogue,
  editRadiator,
  expectRadiator,
  filterDifficultRooms,
  getExistingRadiatorCards,
  getMuiSliderValue,
  getNewRadiatorCards,
  goToNextHeatDesignStep,
  mockedSlider,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  saveRadiator,
  setIndividualTemperatureAdjustment,
  setRadiatorEnabled,
  setRadiatorTableFiltersToValues,
  setRadiatorValues,
  setSwitchToValue,
  trpcMsw,
} from 'tests/utils/testUtils';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { userEvent } from '@testing-library/user-event';

import { mockGetServerEnvironment, mocks } from 'tests/utils/mockedTrpcCalls';
import { radiators } from 'tests/heat-loss/fixtures/catalogueTestData';
import { setupWorker } from 'msw/browser';
import { resetRouterMock } from '@mocks/next/router';
import { UUID } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.contract.common.v1';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';

/**
 * This test was originally included in RadiatorsOverview.test.tsx, but as Jest executes tests within a
 * test file sequentially, it ended up taking quite some time to run that test file. We investigated
 * various ways to improve the performance of those slow-running tests, and none had much impact whilst
 * we remained in the Jest + React + MUI ecosystem. So a simple, if not-so-elegant solution is this, to
 * split the slower tests into their own files, so Jest can run them in parallel.
 */

const server = setupWorker(
  mockGetServerEnvironment(),
  mocks.getGrpcEnergySolution.asa,
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.energySolutionDiff,
  trpcMsw.AiraBackend.getTechnicalSpecifications.query(() => ({ productTechnicalSpecifications: [] })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => ['admin']),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({ surveyForms: [] })),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => ({ reports: [] })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => ({ radiators })),
  ...sharedHeatDesignHandlers,
);

// Helpers
vi.mock('@mui/material/Slider', () => ({ default: mockedSlider }));
vi.mock('../utils/saveToProtobuf', () => ({
  serializeProjectToProtobuf: vi.fn(),
  serializeHeatDesignResult: vi.fn(),
}));

function heatDesignComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
const electricalRadiatorID = UUID.create({ value: uuidv4() });
beforeEach(async () => {
  const getAsaHouse = (): ProtoHeatDesign => {
    const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
    const filtered: ProtoHeatDesign = filterDifficultRooms(asaRawHouse);
    return {
      ...filtered,
      ...sampleDefaultDwellingUValueDefaults,
      dwelling: {
        ...filtered.dwelling!,
        floors: filtered.dwelling!.floors.map((floor) => ({
          ...floor,
          rooms: floor.rooms.map((room) => ({
            ...room,
            // Add an electric radiator to the test data
            radiators:
              room.name === 'Living Room'
                ? [
                    ...room.radiators,
                    {
                      id: electricalRadiatorID,
                      dataSourceReferences: [],
                      imageMap: undefined,
                      floorImageMap: undefined,
                      widthM: 0.6,
                      heightM: 0.8,
                      toBeInstalled: false,
                      enabled: false,
                      comment: 'Electrical radiator',
                      radiatorDetails: {
                        details: {
                          $case: 'electricRadiatorDetails',
                          electricRadiatorDetails: {
                            outputWatt: 0,
                          },
                        },
                      },
                    },
                  ]
                : room.radiators,
          })),
        })),
      },
    };
  };
  let currentHeatDesign = getAsaHouse();
  mockRouterForHeatDesign();
  server.use(
    trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
      heatDesign: currentHeatDesign,
      isLocked: false,
      result: undefined,
      updatedAt: new Date(),
      events: [],
    })),
    trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(async (input) => {
      currentHeatDesign = input.input.heatDesign;
      return {
        result: undefined,
        updatedAt: new Date(),
      };
    }),
  );
  renderWithProviders(heatDesignComponent());
  await reload();

  // Advance to radiator overview page
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
});
afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});
afterAll(() => server.stop());

vi.setConfig({ testTimeout: 60_000 });

describe('Verify saving and loading works for radiator page', async () => {
  it('check initial state', async () => {
    // Check initial state ------------------------------------------------------

    // Water temperatures
    expect(await getMuiSliderValue('flow-temperature-slider')).toBe('45');
    expect(await getMuiSliderValue('flow-return-delta-t-slider')).toBe('10');
    expect(screen.getByTestId('return-temperature-info-block').textContent).toContain('35');
    expect(screen.getByTestId('emitter-average-temperature').textContent).toContain('40');
  });

  // TODO: Also check the house totals

  it('verify saving and loading', async () => {
    // Open room radiator detail modal
    await userEvent.click(screen.getByTestId(`radiator-room-living-room-area`));
    expect(await screen.findByText('heatDesign.radiatorRenderer.AddUnderfloorHeating')).toBeVisible();

    // Room radiator detail - header items
    expect(await screen.findByTestId('flow-temperature-slider')).toBeVisible();
    expect(await getMuiSliderValue('flow-temperature-slider')).toBe('45');
    expect(await screen.findByTestId('return-temperature-info-block')).toBeVisible();
    expect(screen.getByTestId('return-temperature-info-block')).toHaveTextContent('35.0 °C');
    expect(screen.getByTestId('flow-return-delta-t-slider')).toBeVisible();
    expect(await getMuiSliderValue('flow-return-delta-t-slider')).toBe('10');

    expect(screen.getByTestId('emitter-average-temperature')).toBeVisible();
    expect(screen.getByTestId('emitter-average-temperature')).toHaveTextContent('40 °C');

    // Room radiator detail - table
    let newRadiatorRows = getNewRadiatorCards({
      isQuery: true,
      includeCreating: true,
    });
    let existingRadiatorRows = getExistingRadiatorCards();

    expect(newRadiatorRows.length).toBe(0);
    expect(existingRadiatorRows.length).toBe(2);
    expectRadiator(
      existingRadiatorRows[0]!,
      {
        enabled: false,
        comment: '',
        height: 600,
        width: 960,
        nominalOutput: null,
        calculatedOutput: '0 W',
      },
      true,
    );
    expectRadiator(
      existingRadiatorRows[1]!,
      {
        enabled: false,
        comment: 'Electrical radiator',
        height: 800,
        width: 600,
        nominalOutput: null,
        calculatedOutput: '0 W',
      },
      true,
    );

    // Change -------------------------------------------------------------------

    // Add underfloor heating
    await userEvent.click(screen.getByText('heatDesign.radiatorRenderer.AddUnderfloorHeating'));
    await setSwitchToValue('match-room-heat-loss-checkbox', false);
    const input = screen.getByTestId('underfloor-heating-nominal-output').querySelector('input');
    expect(input).toBeVisible();
    expect(input).toHaveValue(0); // When unchecking automatic mode, we should set the value to 0
    const infoBlock = await screen.findByTestId('room-heat-balance-label');
    expect(infoBlock).toBeVisible();
    expect(infoBlock).toHaveTextContent('-2174 W');
    await userEvent.clear(input!);
    fireEvent.change(input!, {
      target: { value: '1000' },
    });
    await setIndividualTemperatureAdjustment(true);

    expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('-1174 W');
    // Add existing radiator
    addExistingRadiator();
    existingRadiatorRows = getExistingRadiatorCards(true);
    const newlyAddedExistingRadiatorRow = existingRadiatorRows[existingRadiatorRows.length - 1]!;
    await setRadiatorValues(newlyAddedExistingRadiatorRow, {
      description: 'Existing K2 rad',
      height: 200,
      width: 400,
      nominalOutput: 1000,
      deltaT: 50,
      deltaTAdjustment: 0,
    });
    await saveRadiator();
    existingRadiatorRows = getExistingRadiatorCards();
    expect(existingRadiatorRows.length).toBe(3);
    expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('-890 W');

    // Add new radiator
    await addNewRadiator();
    await setRadiatorTableFiltersToValues({
      height: {
        min: 200,
        max: 1900,
      },
      width: {
        min: 200,
        max: 1900,
      },
      output: {
        min: 569,
        max: 569,
      },
    });
    await chooseRadiatorFromCatalogue({
      index: 0,
    });
    await saveRadiator();

    newRadiatorRows = getNewRadiatorCards();
    expect(newRadiatorRows.length).toBe(1);
    const newlyAddedNewRadiatorRow = newRadiatorRows[newRadiatorRows.length - 1]!;
    editRadiator(newlyAddedNewRadiatorRow);
    await setRadiatorValues(newlyAddedNewRadiatorRow, {
      deltaTAdjustment: 0,
    });
    await saveRadiator();
    expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('-321 W');

    // Change existing radiator
    const existingRadiatorRow = getExistingRadiatorCards()[0]!;
    setRadiatorEnabled({
      parentContainer: existingRadiatorRow,
      isEnabled: true,
    });
    await setRadiatorValues(existingRadiatorRow, {
      nominalOutput: 3000,
      deltaT: 50,
    });
    expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('532 W');

    // Change electrical radiator
    const existingElectricalRadiatorRow = screen.getByTestId(`existing-radiator-card-${electricalRadiatorID.value}`);
    setRadiatorEnabled({
      parentContainer: existingElectricalRadiatorRow,
      isEnabled: true,
    });
    await setRadiatorValues(existingElectricalRadiatorRow, {
      nominalOutput: 1000,
    });
    expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('1532 W');
  });
});
