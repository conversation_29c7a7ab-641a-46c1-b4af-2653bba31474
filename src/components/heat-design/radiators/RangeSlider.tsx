import { Stack } from '@mui/material';
import Slider from '@mui/material/Slider';
import Typography from '@mui/material/Typography';
import { Card } from '@ui/components/Card/Card';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { beige } from '@ui/theme/colors';
import { theme } from '@ui/theme/theme';
import * as React from 'react';

enum ValueType {
  MIN = 0,
  MAX = 1,
}

export type RangeInputSliderProps = {
  name: string;
  label?: React.ReactNode;
  value: number[];
  onChange: (values: number[]) => void;
  min?: number;
  max?: number;
  step?: number;
  'data-testid'?: string;
};

export function RangeSlider({
  name,
  label,
  value,
  onChange,
  min,
  max,
  step,
  'data-testid': dataTestId,
}: RangeInputSliderProps): React.JSX.Element {
  const [isFocused, setIsFocused] = React.useState(false);

  const handleSliderChange = (newValues: number[]) => {
    onChange(newValues);
  };

  const handleInputChange = (valueType: ValueType, newValue: number) => {
    const newValueArray = [...value];
    newValueArray[valueType] = newValue;
    onChange(newValueArray);
  };

  return (
    <Card
      sx={{
        flexGrow: 1,
        borderRadius: theme.spacing(2),
        backgroundColor: beige[100],
        '.MuiCardContent-root': {
          padding: `${theme.spacing(2)} !important`,
          justifyContent: 'space-between',
          height: '100%',
        },
      }}
      data-testid={dataTestId}
    >
      <Typography variant="inputLabel">{label}</Typography>
      <Slider
        step={step || 1}
        min={min}
        max={max}
        value={value ?? 0}
        onChange={(_, v) => handleSliderChange(v as number[])}
        aria-labelledby="input-slider"
        valueLabelDisplay={isFocused ? 'on' : 'auto'}
        sx={{ width: '85%', alignSelf: 'center' }}
      />
      <Stack direction="row" gap={1}>
        <NumericTextField
          fullWidth
          name={name}
          value={value[ValueType.MIN] ?? min ?? 0}
          size="small"
          onChange={(newValue) => handleInputChange(ValueType.MIN, newValue)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          min={min}
          max={value[1]}
          inputProps={{
            'aria-labelledby': 'input-slider',
            sx: { textAlign: 'left' },
          }}
        />
        <NumericTextField
          fullWidth
          name={name}
          containerProps={{ width: '100%' }}
          value={value[ValueType.MAX] ?? max ?? 0}
          size="small"
          onChange={(newValue) => handleInputChange(ValueType.MAX, newValue)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          min={value[0]}
          max={max}
          inputProps={{
            type: 'number',
            'aria-labelledby': 'input-slider',
            sx: { textAlign: 'right' },
          }}
        />
      </Stack>
    </Card>
  );
}
