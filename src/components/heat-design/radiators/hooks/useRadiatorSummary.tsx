import { calculateEmitterDetailsSummary, calculateRadiatorDetailsForFloor } from '../../utils/radiatorHelpers';
import { useFlowReturnDeltaT, useFlowTemp } from '../../stores/HeatSourceStore';
import { useGetRoomsByFloor } from '../../stores/RoomsStore';
import { useConstructionYear } from '../../stores/HouseInputsStore';
import { useClimateDataStore } from '../../stores/ClimateDataStore';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useMemo } from 'react';
import { useFloors } from '../../stores/FloorsStore';
import { RadiatorEmitterOverviewDetails } from '../../Emitters/types';

export function useRadiatorSummary(): {
  actualCircuitReturnTemperature: number;
  totalFlowNeeded: number;
  radiatorDetailsByFloor: Record<string, RadiatorEmitterOverviewDetails[]>;
} {
  const flowTemperature = useFlowTemp();
  const floors = useFloors();
  const constructionYear = useConstructionYear();
  const climateDataStore = useClimateDataStore();
  const { countryCode } = useGroundwork();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const roomsByFloor = useGetRoomsByFloor();

  const data = useMemo(() => {
    const radiatorDetailsByFloor = floors.reduce(
      (acc, floor) => {
        acc[floor.uid] = calculateRadiatorDetailsForFloor({
          floor,
          roomsByFloor,
          constructionYear,
          countryCode,
          flowTemperature,
          flowReturnDeltaT,
          climateDataStore,
        });
        return acc;
      },
      {} as Record<string, RadiatorEmitterOverviewDetails[]>,
    );
    const summary = calculateEmitterDetailsSummary(
      Object.values(radiatorDetailsByFloor).flat(),
      flowTemperature,
      flowReturnDeltaT,
    );
    return {
      radiatorDetailsByFloor,
      actualCircuitReturnTemperature: summary.actualCircuitReturnTemperature,
      totalFlowNeeded: summary.totalFlowNeeded,
    };
  }, [floors, flowTemperature, flowReturnDeltaT, roomsByFloor, constructionYear, countryCode, climateDataStore]);
  return data;
}
