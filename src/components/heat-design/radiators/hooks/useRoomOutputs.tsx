import { useClimateDataStore } from 'components/heat-design/stores/ClimateDataStore';
import { calculateNewHeatOutput, RoomHeatOutput } from '../../utils/radiatorHelpers';
import { useFlowReturnDeltaT, useFlowTemp } from '../../stores/HeatSourceStore';
import { useGroundwork } from 'context/groundwork-context';
import { useConstructionYear } from 'components/heat-design/stores/HouseInputsStore';
import { useSelectedRoom } from './useSelectedRoom';
import { useDwellingHeatDesignResult } from '../../hooks/useDwellingHeatDesignResult';

export function useRoomOutputs() {
  const room = useSelectedRoom();
  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const roomHeatDesignResult = roomHeatDesignResults.find((ro) => ro.roomId === room?.id);
  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const flowTemperature = useFlowTemp();
  const climateDataStore = useClimateDataStore();
  const roomHeatLoss = roomHeatDesignResult?.totalRoom.heatLoss ?? 0;

  return room
    ? calculateNewHeatOutput({
        room,
        constructionYear,
        countryCode,
        roomHeatLoss,
        flowReturnDeltaT,
        flowTemperature,
        climateDataStore,
      })
    : ({
        underfloorHeating: 0,
        radiators: 0,
      } satisfies RoomHeatOutput);
}
