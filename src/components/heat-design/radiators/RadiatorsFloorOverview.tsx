import { Stack } from '@mui/material';
import { useGroundwork } from '../../../context/groundwork-context';
import { useState } from 'react';
import { HEAT_DESIGN_FLOOR_PLAN_DIMENSION } from '@ui/theme/constants';
import InteractableFloorPlan from '../components/InteractableFloorPlan';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { useDefaultFloor, useFloors } from '../stores/FloorsStore';
import { useHeatDesignUIActions } from '../stores/HeatDesignUIStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../stores/HeatSourceStore';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useGetRoomsByFloor } from '../stores/RoomsStore';
import { FloorProps } from '../stores/types';
import { FloorRoomRadiatorWattPills, InvalidHeatSources } from './RadiatorsOverviewPieces';
import { floorHeatSourcesAreValid } from '../Validator';
import { FloorNavigationWithOutputs } from '../FloorNavigationWithOutputs';
import RadiatorRenderer from './RadiatorRenderer';
import { useSelectedRoom } from './hooks/useSelectedRoom';

export default function RadiatorsFloorOverview() {
  const floors = useFloors();
  const constructionYear = useConstructionYear();
  const flowTemp = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();

  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const { countryCode } = useGroundwork();
  const climateDataStore = useClimateDataStore();
  const [selectedFloor, setSelectedFloor] = useState<FloorProps | undefined>(useDefaultFloor());
  const selectedRoom = useSelectedRoom();
  const heatDesignUIActions = useHeatDesignUIActions();

  const roomsByFloor = useGetRoomsByFloor();

  if (!selectedFloor) return null;

  const roomValidator = (floor: FloorProps) => floorHeatSourcesAreValid(roomsByFloor[floor.uid] ?? []);

  return (
    <Stack direction="row" spacing={2} justifyContent="flex-start" alignItems="flex-start">
      <Stack direction="column" spacing={2} width="100%">
        {selectedRoom ? (
          <RadiatorRenderer />
        ) : (
          <>
            <Stack direction="row" alignItems="center" spacing={2} pb={3}>
              <FloorNavigationWithOutputs
                selectedFloor={selectedFloor}
                selectFloor={setSelectedFloor}
                validator={roomValidator}
              />
            </Stack>
            {floors.map((floor) => {
              const roomsInFloor = roomsByFloor[floor.uid];
              if (!roomsInFloor || floor.uid !== selectedFloor.uid) return null;
              return (
                <InteractableFloorPlan floor={floor} key={floor.uid}>
                  <Stack
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                    }}
                  >
                    <svg width={HEAT_DESIGN_FLOOR_PLAN_DIMENSION} height={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}>
                      {roomsInFloor.map((room) => {
                        const coords = room?.imageMap?.coordinates.toString();
                        return (
                          <polygon
                            key={room.id + room.floor}
                            points={coords}
                            className="overview-room"
                            onClick={(e) => {
                              if (!e.defaultPrevented) {
                                e.stopPropagation();
                                e.preventDefault();
                                heatDesignUIActions.selectRoom(room);
                              }
                            }}
                            data-testid={`radiator-room-${room.name.toLowerCase().replace(' ', '-')}-area`}
                          />
                        );
                      })}
                      <svg
                        pointerEvents="none"
                        width={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
                        height={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
                      >
                        <FloorRoomRadiatorWattPills
                          roomsInSelectedFloor={roomsInFloor}
                          roomHeatDesignResults={roomHeatDesignResults}
                          constructionYear={constructionYear}
                          countryCode={countryCode}
                          flowReturnDeltaT={flowReturnDeltaT}
                          flowTemperature={flowTemp}
                          climateDataStore={climateDataStore}
                        />
                        <InvalidHeatSources roomsInSelectedFloor={roomsInFloor} />
                      </svg>
                    </svg>
                  </Stack>
                </InteractableFloorPlan>
              );
            })}
          </>
        )}
      </Stack>
    </Stack>
  );
}
