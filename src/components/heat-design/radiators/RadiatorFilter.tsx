import { memo, useEffect, useMemo, useState } from 'react';
import {
  PanelRadiatorType,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { Checkbox, ListItemText, MenuItem, Select, SelectChangeEvent, Stack, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { MagnifyingGlassOutlinedIcon } from '@ui/components/StandardIcons/MagnifyingGlassOutlinedIcon';
import { TextField } from '@ui/components/TextField/TextField';
import { beige } from '@ui/theme/colors';
import { FormattedMessage, useIntl } from 'react-intl';
import { RadiatorData, RadiatorMode } from '../stores/types';
import { columnRadiatorOptions, filterColumnRadiator, filterPanelRadiator, panelRadiatorOptions } from './utils';
import { RangeSlider } from './RangeSlider';
import { CatalogueRadiator, ColumnCatalogueRadiator, Filter, PanelCatalogueRadiator } from './types';
import useDebounceEffect from '@ui/components/AddressInputLoqate/useDebounceEffect';
import clamp from 'lodash/clamp';
import { Radiator } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.radiator.v1';
import { getCalculatedRadiatorOutput } from '../utils/radiatorHelpers';
import { useSelectedRoom } from './hooks/useSelectedRoom';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../stores/HeatSourceStore';
import { useGroundwork } from '../../../context/groundwork-context';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { useRadiatorContext } from './contexts/RadiatorContext';

const filterRadiator = (mode: RadiatorMode, radiator: CatalogueRadiator, filter: Filter<CatalogueRadiator>) => {
  if (mode === RadiatorMode.Column) {
    if (radiator.radiatorDetail?.category?.$case !== 'columnRadiator') return false;

    return filterColumnRadiator(radiator as ColumnCatalogueRadiator, filter);
  }

  if (mode === RadiatorMode.Panel) {
    if (radiator.radiatorDetail?.category?.$case !== 'panelRadiator') return false;

    return filterPanelRadiator(radiator as PanelCatalogueRadiator, filter);
  }

  return false;
};

export type RadiatorFilterProps = {
  mode: RadiatorMode;
  setMode: (mode: RadiatorMode) => void;
  radiatorForFilterSetup: RadiatorData;
  replacingRadiator?: RadiatorData;
  onFilteredRadiatorsChange: (radiators: CatalogueRadiator[] | undefined) => void;
};
const defaultFilterConfiguration = {
  brand: 'All',
  model: '',
  height: { min: 0, max: 1000 },
  width: { min: 0, max: 1000 },
  output: { min: 0, max: 1000 },
  types: [],
  numColumns: { min: 1, max: 10 },
  materials: [RadiatorMaterial.RADIATOR_MATERIAL_STEEL],
};

function RadiatorFilterComponent({
  setMode,
  mode,
  radiatorForFilterSetup,
  replacingRadiator,
  onFilteredRadiatorsChange,
}: RadiatorFilterProps) {
  const selectedRoom = useSelectedRoom();
  const constructionYear = useConstructionYear();
  const climateDataStore = useClimateDataStore();
  const flowTemperature = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const { countryCode } = useGroundwork();
  const { floorsResults } = useDwellingHeatDesignResult();
  const { catalogueRadiators } = useRadiatorContext();
  const [debouncedFilters, setDebouncedFilters] = useState<Filter<CatalogueRadiator> | undefined>(undefined);
  const [filterDefaults, setFilterDefaults] = useState<Filter<CatalogueRadiator> | undefined>(undefined);
  const [filterOverrides, setFilterOverrides] = useState<Partial<Filter<CatalogueRadiator>> | undefined>(undefined);
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const roomHeatDesignResult = roomHeatDesignResults.find((ro) => ro.roomId === selectedRoom?.id);

  const totalRoomHeatOutput = roomHeatDesignResult?.totalOutputOfHeatEmittersWatt ?? 0;
  const totalRoomHeatLoss = roomHeatDesignResult?.totalRoom.heatLoss ?? 0;
  const setFilters = (values: Partial<Filter<CatalogueRadiator>>) => {
    setFilterOverrides((prev) => ({
      ...prev,
      ...values,
    }));
  };

  const usedFilters = useMemo(() => {
    return filterDefaults && filterOverrides
      ? {
          ...filterDefaults,
          ...filterOverrides,
        }
      : undefined;
  }, [filterDefaults, filterOverrides]);

  useDebounceEffect(
    () => {
      setDebouncedFilters(usedFilters);
    },
    300,
    [usedFilters],
  );

  const wattDifference = useMemo(() => {
    if (!selectedRoom) {
      return 0;
    }
    const nonNewReplacingRadiator = replacingRadiator?.uid !== 'new' ? replacingRadiator : undefined;
    const existingRadiatorCalculatedOutput = radiatorForFilterSetup?.enabled
      ? getCalculatedRadiatorOutput({
          room: selectedRoom,
          radiatorOutput: radiatorForFilterSetup,
          constructionYear,
          countryCode,
          flowReturnDeltaT,
          flowTemperature,
          climateDataStore,
        })
      : 0;
    const replacingRadiatorCalculatedOutput = nonNewReplacingRadiator?.enabled
      ? getCalculatedRadiatorOutput({
          room: selectedRoom,
          radiatorOutput: nonNewReplacingRadiator,
          constructionYear,
          countryCode,
          flowReturnDeltaT,
          flowTemperature,
          climateDataStore,
        })
      : 0;
    return (
      totalRoomHeatOutput -
      (nonNewReplacingRadiator ? replacingRadiatorCalculatedOutput : existingRadiatorCalculatedOutput) -
      totalRoomHeatLoss
    );
  }, [
    selectedRoom,
    replacingRadiator,
    radiatorForFilterSetup,
    constructionYear,
    countryCode,
    flowReturnDeltaT,
    flowTemperature,
    climateDataStore,
    totalRoomHeatOutput,
    totalRoomHeatLoss,
  ]);

  useEffect(() => {
    if (!filterOverrides && filterDefaults) {
      // We want to clamp the min value to the max value - 100
      // because the filterDefaults.output.max is the maximum output of all catalogue radiators + 100W
      const neededOutput = Math.min(Math.round(Math.abs(Math.min(wattDifference, 0))), filterDefaults.output.max - 100);

      if (radiatorForFilterSetup) {
        if (radiatorForFilterSetup.uid === 'new') {
          const overrides = {
            height: {
              min: filterDefaults?.height.min,
              max: filterDefaults?.height.max,
            },
            width: {
              min: filterDefaults?.width.min,
              max: filterDefaults?.width.max,
            },
            output: {
              min: Math.max(neededOutput, 0),
              max: filterDefaults?.output.max,
            },
          };
          setFilterOverrides(overrides);
        } else if (radiatorForFilterSetup.isExisting) {
          // Configure filters to match radiator being replaced
          // 150 hardcoded value due to us setting the filterDefault.max to up to 150mm
          // above the largest radiator height/width in the catalogue
          const overrides = {
            height: {
              min: clamp(
                (radiatorForFilterSetup.height || 0) - 50,
                filterDefaults?.height.min,
                filterDefaults?.height.max - 150,
              ),
              max: clamp(
                (radiatorForFilterSetup.height || 0) + 200,
                filterDefaults?.height.min,
                filterDefaults?.height.max,
              ),
            },
            width: {
              min: clamp(
                (radiatorForFilterSetup.width || 0) - 50,
                filterDefaults?.width.min,
                filterDefaults?.width.max - 150,
              ),
              max: clamp(
                (radiatorForFilterSetup.width || 0) + 200,
                filterDefaults?.width.min,
                filterDefaults?.width.max,
              ),
            },
            output: {
              min: clamp(neededOutput, filterDefaults?.output.min, filterDefaults?.output.max),
              max: filterDefaults?.output.max,
            },
          };

          setFilterOverrides(overrides);
        } else {
          setFilterOverrides({
            output: {
              min: Math.max(neededOutput, 0),
              max: filterDefaults?.output.max,
            },
          });
        }
      } else {
        setFilterOverrides({});
      }
    }
  }, [filterDefaults, radiatorForFilterSetup, wattDifference, filterOverrides]);

  useEffect(() => {
    onFilteredRadiatorsChange(
      (debouncedFilters
        ? catalogueRadiators?.filter((radiator) => filterRadiator(mode, radiator, debouncedFilters))
        : []) ?? [],
    );
  }, [catalogueRadiators, mode, debouncedFilters, onFilteredRadiatorsChange]);

  useEffect(() => {
    if (!catalogueRadiators) {
      return;
    }
    let parameters: Pick<Filter<Radiator>, 'height' | 'width' | 'output'> = {
      height: { min: 0, max: 1000 },
      width: { min: 0, max: 1000 },
      output: { min: 0, max: 1000 },
    };
    if (catalogueRadiators.length > 0 && !filterDefaults) {
      // Configure filters based on existing radiators
      const radiatorHeights = catalogueRadiators.map((radiator) => radiator.heightMm);
      const radiatorLengths = catalogueRadiators.map((radiator) => radiator.lengthMm);
      const radiatorOutputs = catalogueRadiators.map((radiator) => radiator.calculatedOutput || 0);

      const height = { min: Math.min(...radiatorHeights), max: Math.max(...radiatorHeights) };
      const width = { min: Math.min(...radiatorLengths), max: Math.max(...radiatorLengths) };
      const output = { min: Math.min(...radiatorOutputs), max: Math.max(...radiatorOutputs) };

      parameters = {
        height: {
          min: Math.max((Math.round(height.min / 100) - 1) * 100, 0),
          max: (Math.round(height.max / 100) + 1) * 100,
        },
        width: {
          min: Math.max((Math.round(width.min / 100) - 1) * 100, 0),
          max: (Math.round(width.max / 100) + 1) * 100,
        },
        output: {
          min: Math.max((Math.round(output.min / 100) - 1) * 100, 0),
          max: (Math.round(output.max / 100) + 1) * 100,
        },
      };
      setFilterDefaults({
        ...defaultFilterConfiguration,
        ...parameters,
      });
    }
  }, [catalogueRadiators, filterDefaults]);

  const hasPanel = catalogueRadiators?.some((rad) => rad.radiatorDetail?.category?.$case === 'panelRadiator');
  const hasColumn = catalogueRadiators?.some((rad) => rad.radiatorDetail?.category?.$case === 'columnRadiator');

  const { formatMessage } = useIntl();

  const handleChangeType = (event: SelectChangeEvent<PanelRadiatorType[]>) => {
    setFilters({
      types:
        typeof event.target.value === 'string'
          ? event.target.value.split(',').map((s) => Number(s))
          : event.target.value,
    });
  };

  const handleChangeMaterial = (event: SelectChangeEvent<RadiatorMaterial[]>) => {
    setFilters({
      types:
        typeof event.target.value === 'string'
          ? event.target.value.split(',').map((s) => Number(s))
          : event.target.value,
    });
  };
  const brands = useMemo(() => {
    return Array.from(new Set(catalogueRadiators?.map((radiator) => radiator.brand) ?? []));
  }, [catalogueRadiators]);
  if (!usedFilters || !filterDefaults) {
    return null;
  }

  return (
    <Stack
      spacing={3}
      direction="row"
      display="flex"
      sx={{
        backgroundColor: beige[100],
        borderRadius: '16px 16px 0 0',
      }}
    >
      <Card
        sx={{
          width: 600,
          borderRadius: '0 !important',
          '.MuiCardContent-root': {
            padding: `0 !important`,
          },
        }}
      >
        <Stack spacing={2} sx={{ padding: '16px' }}>
          <Stack direction="row" spacing={2}>
            {hasPanel && hasColumn ? (
              <Stack style={{ width: '50%' }}>
                <Typography variant="inputLabel">
                  {formatMessage({ id: 'heatDesign.radiatorModal.catalogue.category' })}
                </Typography>
                <Select
                  size="small"
                  name="select-rad-category"
                  onChange={(event) => setMode(event.target.value as RadiatorMode)}
                  value={mode}
                  style={{ marginTop: 8 }}
                  data-testid="catalogue-select-mode"
                >
                  <MenuItem value={RadiatorMode.Panel}>Panel</MenuItem>
                  <MenuItem value={RadiatorMode.Column}>Column</MenuItem>
                </Select>
              </Stack>
            ) : null}

            {mode === RadiatorMode.Panel ? (
              <Stack style={{ width: '50%' }}>
                <Typography variant="inputLabel">
                  {formatMessage({ id: 'heatDesign.radiatorModal.catalogue.type' })}
                </Typography>
                <Select
                  size="small"
                  value={usedFilters.types || []}
                  displayEmpty
                  style={{ marginTop: 8 }}
                  multiple
                  renderValue={(selected) => {
                    if (selected.length === 0) {
                      return <Typography>All</Typography>;
                    }

                    return selected.map((s) => panelRadiatorOptions.find((o) => o.value === s)?.label).join(', ');
                  }}
                  onChange={handleChangeType}
                  data-testid="catalogue-select-type"
                >
                  {panelRadiatorOptions.map((o) => (
                    <MenuItem key={`filter-type-${o.value}`} value={o.value}>
                      <Checkbox checked={(usedFilters.types || []).includes(o.value)} />
                      <ListItemText primary={o.label} />
                    </MenuItem>
                  ))}
                </Select>
              </Stack>
            ) : (
              <Stack style={{ width: '50%' }}>
                <Typography variant="inputLabel">Material</Typography>
                <Select
                  size="small"
                  value={usedFilters.materials || [RadiatorMaterial.RADIATOR_MATERIAL_STEEL]}
                  displayEmpty
                  disabled // Only support steel radiators for the time being
                  style={{ marginTop: 8 }}
                  renderValue={(selected) => {
                    if (selected.length === 0) {
                      return <Typography>All</Typography>;
                    }

                    return selected.map((s) => columnRadiatorOptions.find((o) => o.value === s)?.label).join(', ');
                  }}
                  onChange={handleChangeMaterial}
                  data-testid="catalogue-select-material"
                >
                  {columnRadiatorOptions.map((o) => (
                    <MenuItem key={`filter-material-${o.value}`} value={o.value}>
                      <Checkbox checked={(usedFilters.materials || []).includes(o.value)} />
                      <ListItemText primary={o.label} />
                    </MenuItem>
                  ))}
                </Select>
              </Stack>
            )}

            <Stack style={{ width: '50%' }}>
              <Typography variant="inputLabel">
                {formatMessage({ id: 'heatDesign.radiatorModal.catalogue.brand' })}
              </Typography>
              <Select
                size="small"
                name="select-rad-catalog"
                onChange={(event) => setFilters({ brand: event.target.value })}
                value={usedFilters.brand}
                style={{ marginTop: 8 }}
              >
                <MenuItem value="All">
                  <FormattedMessage id="heatDesign.radiatorModal.shared.all" />
                </MenuItem>
                {brands.map((brand: string) => (
                  <MenuItem key={`radiator-market-${brand}`} value={brand}>
                    {brand}
                  </MenuItem>
                ))}
              </Select>
            </Stack>
          </Stack>
          <TextField
            name="search"
            size="small"
            sx={{ width: '100%' }}
            icon={<MagnifyingGlassOutlinedIcon />}
            inputProps={{ 'data-testid': 'catalogue-search' }}
            placeholder={formatMessage({ id: 'heatDesign.radiatorModal.shared.search' })}
            value={usedFilters.model}
            onChange={(event) => {
              setFilters({ model: event.target.value });
            }}
          />
        </Stack>
      </Card>
      {mode === RadiatorMode.Column ? (
        <RangeSlider
          label={formatMessage({ id: 'heatDesign.radiatorModal.shared.columns' })}
          name="numColumns"
          value={
            usedFilters.numColumns
              ? [usedFilters.numColumns.min, usedFilters.numColumns.max]
              : [filterDefaults.numColumns.min, filterDefaults.numColumns.max]
          }
          min={filterDefaults.numColumns.min}
          max={filterDefaults.numColumns.max}
          step={1}
          onChange={(values: number[]) => {
            setFilters({
              numColumns: {
                min: values[0] || filterDefaults.numColumns.min,
                max: values[1] || filterDefaults.numColumns.max,
              },
            });
          }}
        />
      ) : undefined}
      <RangeSlider
        label={formatMessage({ id: 'heatDesign.radiatorModal.shared.height' })}
        name="height"
        value={
          usedFilters.height
            ? [usedFilters.height.min, usedFilters.height.max]
            : [filterDefaults.height.min, filterDefaults.height.max]
        }
        data-testid="radiator-filter-height-slider"
        min={filterDefaults.height.min}
        max={filterDefaults.height.max}
        step={50}
        onChange={(values: number[]) => {
          setFilters({
            height: { min: values[0] || filterDefaults.height.min, max: values[1] || filterDefaults.height.max },
          });
        }}
      />
      <RangeSlider
        label={formatMessage({ id: 'heatDesign.radiatorModal.shared.length' })}
        name="width"
        data-testid="radiator-filter-width-slider"
        value={
          usedFilters.width
            ? [usedFilters.width.min, usedFilters.width.max]
            : [filterDefaults.width.min, filterDefaults.width.max]
        }
        min={filterDefaults.width.min}
        max={filterDefaults.width.max}
        step={50}
        onChange={(values: number[]) => {
          setFilters({
            width: { min: values[0] || filterDefaults.width.min, max: values[1] || filterDefaults.width.max },
          });
        }}
      />
      <RangeSlider
        label={formatMessage({ id: 'heatDesign.radiatorModal.shared.calculatedOutput.static' })}
        name="output"
        data-testid="radiator-filter-output-slider"
        value={[usedFilters.output.min, usedFilters.output.max]}
        min={filterDefaults.output.min}
        max={filterDefaults.output.max}
        step={50}
        onChange={(values: number[]) => {
          setFilters({
            output: { min: values[0] || filterDefaults.output.min, max: values[1] || filterDefaults.output.max },
          });
        }}
      />
    </Stack>
  );
}

export const RadiatorFilter = memo(RadiatorFilterComponent);
