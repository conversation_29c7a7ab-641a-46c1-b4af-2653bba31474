import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Accordion, AccordionDetails, AccordionSummary, Box, Stack, Typography } from '@mui/material';
import { beige, surface } from '@ui/theme/colors';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { ColumnDef } from '@tanstack/react-table';
import { TanstackTable } from '@ui/components/TanstackTable/TanstackTable';
import styled from '@emotion/styled';
import { RadiatorDetail } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.radiator.v1';
import {
  ColumnRadiator,
  PanelRadiator,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { CatalogueRadiator } from './types';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { useFlowReturnDeltaT, useFlowTemp } from '../stores/HeatSourceStore';
import { getDesignRoomTemp } from '../utils/heatCalculations';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { useSelectedRoom } from './hooks/useSelectedRoom';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { calculateReturnAndMeanWaterTemperature } from '../utils/radiatorHelpers';
import { RadiatorData, RadiatorMode } from '../stores/types';
import { useGroundwork } from '../../../context/groundwork-context';
import { useRadiatorCardContext } from './contexts/RadiatorCardContext';
import { panelTypeLabelMapping } from './utils';
import { RADIATOR_DELTA_T_OPTIONS } from './radiatorConstants';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';

const COLUMN_RADIATOR_COLUMN_WIDTHS = '50px 170px 1fr 150px 150px 150px 150px 250px 100px';
const PANEL_RADIATOR_COLUMN_WIDTHS = '50px 170px 1fr 120px 150px 150px 250px 100px';

const StyledAccordion = styled(Accordion)({
  backgroundColor: 'white',
  transition: 'background-color 0.2s, box-shadow 0.2s',
  padding: 0,
  borderRadius: '16px !important',
  width: '100%',

  '.check-wrapper': {
    outline: '1px solid transparent',
    transition: 'outline 0.2s',
  },
  '.details-controls': {
    opacity: 0,
    transition: 'opacity 0.2s',
  },
  '&:hover': {
    backgroundColor: '#2222260F',

    '.check-wrapper': {
      outline: '1px solid #ccc',
    },
    '.details-controls': {
      opacity: 1,
    },
  },
  '&.selected': {
    backgroundColor: '#2222261F',

    '&:hover': {
      '.check-wrapper': {
        outline: '1px solid transparent',
      },
    },
  },
  '&.Mui-expanded': {
    boxShadow: '0 4px 4px 0 #00000040',
    '.details-controls': {
      opacity: 1,
    },
  },
});

function Row({
  radiator,
  isSelected,
  onSelect,
}: {
  radiator: CatalogueRadiator;
  isSelected: boolean;
  onSelect: (radiator: CatalogueRadiator | undefined) => void;
}) {
  const { formatMessage } = useIntl();
  const [expanded, setExpanded] = useState(false);

  const isColumnRadiatorDetail = (
    radiatorDetail: RadiatorDetail | undefined,
  ): radiatorDetail is {
    category: {
      $case: 'columnRadiator';
      columnRadiator: ColumnRadiator;
    };
  } => {
    return radiatorDetail?.category?.$case === 'columnRadiator';
  };
  const isPanelRadiatorDetail = (
    radiatorDetail: RadiatorDetail | undefined,
  ): radiatorDetail is {
    category: {
      $case: 'panelRadiator';
      panelRadiator: PanelRadiator;
    };
  } => {
    return radiatorDetail?.category?.$case === 'panelRadiator';
  };
  const columnRadiatorDetail = isColumnRadiatorDetail(radiator.radiatorDetail) ? radiator.radiatorDetail : undefined;
  const panelRadiatorDetail = isPanelRadiatorDetail(radiator.radiatorDetail) ? radiator.radiatorDetail : undefined;
  const columnWidths = isColumnRadiatorDetail(radiator.radiatorDetail)
    ? COLUMN_RADIATOR_COLUMN_WIDTHS
    : PANEL_RADIATOR_COLUMN_WIDTHS;

  const deltaTValue = useMemo(() => {
    return (
      RADIATOR_DELTA_T_OPTIONS.find((option) => option.value === radiator.deltaTCelsius)?.label?.replace('ΔT ', '') ??
      radiator.deltaTCelsius
    );
  }, [radiator]);
  return (
    <StyledAccordion
      className={`${isSelected ? 'selected' : ''}`}
      expanded={expanded}
      onClick={() => onSelect(radiator)}
      data-testid="catalogue-radiator-row"
    >
      <AccordionSummary style={{ padding: '0px', height: 'auto' }}>
        <Box
          width="100%"
          style={{
            display: 'grid',
            wordBreak: 'break-all',
            gridTemplateColumns: columnWidths,
          }}
        >
          <Box
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 10px',
            }}
          >
            <Box
              className="check-wrapper"
              style={{
                borderRadius: '50%',
                width: '20px',
                height: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
              }}
            >
              <CheckCircleFilledIcon
                style={{
                  opacity: isSelected ? 1 : 0,
                  transition: 'opacity 0.2s',
                  position: 'absolute',
                  top: '-2px',
                  left: '-2px',
                  width: '24px',
                  height: '24px',
                }}
              />
            </Box>
          </Box>
          <Box
            data-testid="catalogue-radiator-row-brand-column"
            style={{ display: 'flex', alignItems: 'center', padding: '8px' }}
          >
            <Typography variant="body2">{radiator.brand}</Typography>
          </Box>
          <Box
            data-testid="catalogue-radiator-row-model-column"
            style={{ display: 'flex', alignItems: 'center', padding: '8px' }}
          >
            <Typography variant="body2">{radiator.model}</Typography>
          </Box>

          {columnRadiatorDetail && (
            <>
              <Box style={{ display: 'flex', alignItems: 'center', padding: '8px' }}>
                <Typography variant="body2">{columnRadiatorDetail.category.columnRadiator.radiatorMaterial}</Typography>
              </Box>
              <Box style={{ display: 'flex', alignItems: 'center', padding: '8px' }}>
                <Typography variant="body2">{columnRadiatorDetail.category.columnRadiator.numberOfColumns}</Typography>
              </Box>
            </>
          )}
          {panelRadiatorDetail && (
            <Box
              style={{ display: 'flex', alignItems: 'center', padding: '8px' }}
              data-testid="catalogue-radiator-row-type-column"
            >
              <Typography variant="body2">
                {panelTypeLabelMapping[panelRadiatorDetail.category.panelRadiator.type]}
              </Typography>
            </Box>
          )}

          <Box style={{ display: 'flex', alignItems: 'center', padding: '8px' }}>
            <Typography variant="body2">{radiator.heightMm}</Typography>
          </Box>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '8px' }}>
            <Typography variant="body2">{radiator.lengthMm}</Typography>
          </Box>
          <Box style={{ display: 'flex', alignItems: 'center', padding: '8px' }}>
            <Typography variant="body2">{radiator.calculatedOutput}</Typography>
          </Box>
          <Stack
            className="details-controls"
            gap="8px"
            sx={{ padding: '8px' }}
            alignItems="center"
            direction="row"
            justifyContent="flex-end"
            onClick={(e) => {
              e.stopPropagation();
              setExpanded(!expanded);
            }}
          >
            <Chevron transitionDuration="0.2s" direction={expanded ? 'up' : 'down'} height={20} width={20} />
            <Typography variant="body2">
              <FormattedMessage id="billOfMaterials.itemCatalogue.table.details" />
            </Typography>
          </Stack>
        </Box>
      </AccordionSummary>
      <AccordionDetails>
        <Stack flexWrap="wrap" direction="row" gap={2} sx={{ borderTop: `1px solid ${surface[200]}`, padding: '8px' }}>
          <LabelValueDisplay
            sx={{ flex: '0 1 200px' }}
            label={formatMessage({ id: 'heatDesign.radiatorTable.nominalOutput.title' })}
            value={radiator.nominalOutputWatt}
          />
          <LabelValueDisplay
            sx={{ flex: '0 1 250px' }}
            label={formatMessage({ id: 'heatDesign.radiatorTable.nominalOutput.deltaT' })}
            value={deltaTValue}
          />
          {radiator.vendorId && (
            <LabelValueDisplay
              sx={{ flex: '0 1 250px' }}
              label={formatMessage({ id: 'heatDesign.radiators.externalVendorId' })}
              value={radiator.vendorId}
            />
          )}
          <LabelValueDisplay
            sx={{ flex: '0 1 250px' }}
            label={`Aira ${formatMessage({ id: 'heatDesign.common.erpId' })}`}
            value={radiator.erpId}
          />
          <LabelValueDisplay
            sx={{ flex: '0 1 250px' }}
            label={formatMessage({ id: 'heatDesign.radiators.conversionFactor' })}
            value={radiator.conversionFactor ?? ' - '}
          />
          <LabelValueDisplay
            sx={{ flex: '0 1 250px' }}
            label={formatMessage({ id: 'heatDesign.radiators.waterContent' })}
            value={radiator.waterVolumeLitres ?? ' - '}
          />
        </Stack>
      </AccordionDetails>
    </StyledAccordion>
  );
}

const MemoizedRow = memo(Row);

export default function RadiatorCatalogueTable({
  radiators,
  mode,
  radiator,
}: {
  mode: RadiatorMode;
  radiators: CatalogueRadiator[];
  radiator: RadiatorData;
}) {
  const { formatMessage } = useIntl();
  const room = useSelectedRoom();
  const constructionYear = useConstructionYear();
  const climateDataStore = useClimateDataStore();
  const { countryCode } = useGroundwork();
  const { setSelectedCatalogueRadiator, selectedCatalogueRadiator, setIsCustom } = useRadiatorCardContext();
  const isInitialised = useRef(false);

  // Initial setting of selected catalogue radiator when editing an existing radiator
  useEffect(() => {
    if (radiators?.length && !isInitialised.current) {
      if (radiator.specificationReferenceId) {
        const catalogueRadiator = radiators.find(
          (singleCatalogueRadiator) => singleCatalogueRadiator.radiatorId?.value === radiator.specificationReferenceId,
        );
        setSelectedCatalogueRadiator(catalogueRadiator);
      }
      isInitialised.current = true;
    }
  }, [radiator, radiators, setSelectedCatalogueRadiator]);

  const roomTemperature = useMemo(
    () => (room ? getDesignRoomTemp(room, constructionYear, countryCode, climateDataStore) : 0),
    [room, constructionYear, countryCode, climateDataStore],
  );

  const flowTemperature = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();

  const meanWaterTemperature = calculateReturnAndMeanWaterTemperature({
    flowTemperature,
    flowReturnDeltaT,
  }).meanWaterTemp;
  const meanWaterAirTemperature =
    roomTemperature !== undefined && meanWaterTemperature !== undefined
      ? meanWaterTemperature - roomTemperature
      : undefined;

  const columns = useMemo<ColumnDef<CatalogueRadiator>[]>(() => {
    if (mode === RadiatorMode.Panel) {
      return [
        {
          header: () => <CheckCircleFilledIcon />,
          id: 'isSelected',
          enableSorting: false,
          accessorKey: 'id',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.catalogue.brand" />,
          id: 'brand',
          enableSorting: true,
          accessorKey: 'brand',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.model" />,
          id: 'model',
          enableSorting: true,
          accessorKey: 'model',
        },

        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.type" />,
          id: 'type',
          enableSorting: true,
          accessorFn: (item) => {
            return item.radiatorDetail?.category?.$case === 'panelRadiator'
              ? item.radiatorDetail.category.panelRadiator.type
              : undefined;
          },
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.height" />,
          id: 'height',
          enableSorting: true,
          accessorKey: 'heightMm',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.length" />,
          id: 'length',
          enableSorting: true,
          accessorKey: 'lengthMm',
        },
        {
          header: () => {
            return (
              <Stack direction="row" alignItems="center" gap={2}>
                <FormattedMessage id="heatDesign.radiatorTable.calculated.output" />
                <TooltipAira title={formatMessage({ id: 'heatDesign.radiators.calculatedOutputTooltip' })} />
              </Stack>
            );
          },
          id: 'calculatedOutput',
          enableSorting: true,
          accessorKey: 'calculatedOutput',
        },
        {
          id: 'expand',
          header: () => <></>,
          enableSorting: false,
          accessorKey: 'id',
        },
      ];
    } else {
      return [
        {
          header: () => <CheckCircleFilledIcon />,
          id: 'isSelected',
          enableSorting: false,
          accessorKey: 'id',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.catalogue.brand" />,
          id: 'brand',
          enableSorting: true,
          accessorKey: 'brand',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.model" />,
          id: 'model',
          enableSorting: true,
          accessorKey: 'model',
        },

        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.material" />,
          id: 'material',
          enableSorting: true,
          accessorFn: (item) => {
            return item.radiatorDetail?.category?.$case === 'columnRadiator'
              ? item.radiatorDetail.category.columnRadiator.radiatorMaterial
              : undefined;
          },
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.columns" />,
          id: 'columns',
          enableSorting: false,
          accessorFn: (item) => {
            return item.radiatorDetail?.category?.$case === 'columnRadiator'
              ? item.radiatorDetail.category.columnRadiator.numberOfColumns
              : undefined;
          },
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.height" />,
          id: 'height',
          enableSorting: true,
          accessorKey: 'heightMm',
        },
        {
          header: () => <FormattedMessage id="heatDesign.radiatorModal.shared.length" />,
          id: 'length',
          enableSorting: true,
          accessorKey: 'lengthMm',
        },
        {
          header: () => (
            <FormattedMessage
              id="heatDesign.radiatorModal.shared.calculatedOutput.dynamic"
              values={{ deltaT: meanWaterAirTemperature }}
            />
          ),
          id: 'calculatedOutput',
          enableSorting: true,
          accessorKey: 'calculatedOutput',
        },
        {
          id: 'expand',
          header: () => <></>,
          enableSorting: false,
          accessorKey: 'id',
        },
      ];
    }
  }, [formatMessage, meanWaterAirTemperature, mode]);

  const selectRow = useCallback(
    (radiator: CatalogueRadiator | undefined) => {
      setSelectedCatalogueRadiator((prev) => {
        if (prev?.radiatorId?.value === radiator?.radiatorId?.value) {
          setIsCustom(true);
          return undefined;
        }
        setIsCustom(false);
        return radiator;
      });
    },
    [setIsCustom, setSelectedCatalogueRadiator],
  );

  return (
    <>
      {radiators.length > 0 ? (
        <TanstackTable
          data={radiators}
          columns={columns}
          initialState={{
            sorting: [{ id: 'calculatedOutput', desc: false }],
          }}
          virtualizationOptions={{
            measureElement:
              typeof window !== 'undefined' && navigator.userAgent.indexOf('Firefox') === -1
                ? (element) => element?.getBoundingClientRect().height
                : undefined,
            estimateSize: () => 53,
          }}
          customRowRenderer={(item) => (
            <MemoizedRow
              radiator={item}
              isSelected={selectedCatalogueRadiator?.radiatorId?.value === item.radiatorId?.value}
              onSelect={selectRow}
            />
          )}
          styles={{
            columnWidths: mode === RadiatorMode.Column ? COLUMN_RADIATOR_COLUMN_WIDTHS : PANEL_RADIATOR_COLUMN_WIDTHS,
            bodyColumnWidths: 'minmax(0, 1fr)',
            tableContainer: () => ({
              padding: '0 16px',
              overflow: 'hidden',
              overflowY: 'auto',
              backgroundColor: beige[100],
            }),
            tableBodyRow: () => ({
              paddingBottom: '8px',
            }),
            tableHeadCell: () => ({
              border: 'none',
            }),
          }}
        />
      ) : (
        <Box m={4} display="flex" flexGrow={1} justifyContent="center">
          <Typography>
            <FormattedMessage id="heatDesign.radiatorModal.noRadiators" />
          </Typography>
        </Box>
      )}
    </>
  );
}
