import { Stack } from '@mui/material';
import RadiatorsRendererHeader from './RadiatorsRendererHeader';
import RoomRadiatorOverview from './RoomRadiatorOverview';
import { RadiatorContextProvider } from './contexts/RadiatorContext';

function RadiatorRenderer() {
  return (
    <RadiatorContextProvider>
      <Stack sx={{ backgroundColor: '#fff', padding: '30px' }}>
        <Stack direction="column" justifyContent="flex-start" alignItems="center">
          <RadiatorsRendererHeader />
          <RoomRadiatorOverview />
        </Stack>
      </Stack>
    </RadiatorContextProvider>
  );
}

export default RadiatorRenderer;
