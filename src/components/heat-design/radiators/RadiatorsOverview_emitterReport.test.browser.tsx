import React from 'react';
import { cleanup, screen } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { vi } from 'vitest';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { userEvent } from '@testing-library/user-event';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { mockGetServerEnvironment, mocks } from 'tests/utils/mockedTrpcCalls';
import { radiators } from 'tests/heat-loss/fixtures/catalogueTestData';
import {
  addNewRadiator,
  chooseRadiatorFromCatalogue,
  filterDifficultRooms,
  getNewRadiatorCards,
  goToNextHeatDesignStep,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  returnToRadiatorOverview,
  sampleDefaultDwellingUValueDefaults,
  saveRadiator,
  setIndividualTemperatureAdjustment,
  setRadiatorTableFiltersToValues,
  setRadiatorValues,
  setUnderfloorHeatingType,
  trpcMsw,
} from '../../../tests/utils/testUtils';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { setupWorker } from 'msw/browser';
import { SystemType } from '../stores/types';
import { resetRouterMock } from '@mocks/next/router';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';

/**
 * This test was originally included in RadiatorsOverview.test.tsx, but as Jest executes tests within a
 * test file sequentially, it ended up taking quite some time to run that test file. We investigated
 * various ways to improve the performance of those slow-running tests, and none had much impact whilst
 * we remained in the Jest + React + MUI ecosystem. So a simple, if not-so-elegant solution is this, to
 * split the slower tests into their own files, so Jest can run them in parallel.
 */

const server = setupWorker(
  mockGetServerEnvironment(),
  mocks.getGrpcEnergySolution.asa,
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.energySolutionDiff,
  trpcMsw.AiraBackend.getTechnicalSpecifications.query(() => ({ productTechnicalSpecifications: [] })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => ['admin']),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [],
  })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(async () => ({ radiators })),
  ...sharedHeatDesignHandlers,
);

function heatDesignComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
beforeEach(async () => {
  const getAsaHouse = (): ProtoHeatDesign => {
    const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
    const filtered: ProtoHeatDesign = filterDifficultRooms(asaRawHouse);
    return {
      ...filtered,
      ...sampleDefaultDwellingUValueDefaults,
    };
  };
  const currentHeatDesign = getAsaHouse();
  mockRouterForHeatDesign();
  server.use(
    trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
      heatDesign: currentHeatDesign,
      isLocked: false,
      result: undefined,
      updatedAt: new Date(),
      events: [],
    })),
  );
  renderWithProviders(heatDesignComponent());
  await reload();

  // Advance to radiator overview page
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
});
afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});
afterAll(() => server.stop());

vi.setConfig({ testTimeout: 60_000 });

test('Verify that the emitter list outputs the correct values', async () => {
  // Add radiator to some rooms
  // Add radiator to the living room
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 702,
      max: 702,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();

  let newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  let newlyAddedNewRadiatorRow = newRadiatorRows[newRadiatorRows.length - 1]!;
  await setIndividualTemperatureAdjustment(true);
  await setRadiatorValues(newlyAddedNewRadiatorRow, {
    deltaTAdjustment: 2,
  });
  returnToRadiatorOverview();
  // Add radiator to the kitchen
  await userEvent.click(screen.getByTestId('radiator-room-kitchen-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 637,
      max: 637,
    },
  });
  await chooseRadiatorFromCatalogue({ index: 0 });
  await saveRadiator();
  newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  newlyAddedNewRadiatorRow = newRadiatorRows[newRadiatorRows.length - 1]!;
  await setIndividualTemperatureAdjustment(true);
  await setRadiatorValues(newlyAddedNewRadiatorRow, {
    deltaTAdjustment: -1,
  });
  returnToRadiatorOverview();
  // Add radiator to the porch
  await userEvent.click(screen.getByTestId('radiator-room-porch-area'));
  await addNewRadiator();
  await setIndividualTemperatureAdjustment(true);
  await setRadiatorTableFiltersToValues({
    output: {
      min: 351,
      max: 351,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();

  newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  newlyAddedNewRadiatorRow = newRadiatorRows[newRadiatorRows.length - 1]!;
  await setRadiatorValues(newlyAddedNewRadiatorRow, {
    deltaTAdjustment: 0,
  });
  returnToRadiatorOverview();
  // Add water-based underfloor heating to toilet
  await userEvent.click(screen.getByTestId('radiator-room-toilet-area'));
  await userEvent.click(screen.getByTestId('add-ufh-heating'));
  const matchHeatingCheckboxToilet = screen.getByTestId('match-room-heat-loss-checkbox');
  const checkBoxInputToilet = matchHeatingCheckboxToilet.querySelector('input') as HTMLInputElement;
  expect(checkBoxInputToilet).toBeChecked();
  await setUnderfloorHeatingType(SystemType.WATER);
  returnToRadiatorOverview();
  // Add electric underfloor heating to the porch (should be filtered out)
  await userEvent.click(screen.getByTestId('radiator-room-porch-area'));
  await userEvent.click(screen.getByText('heatDesign.radiatorRenderer.AddUnderfloorHeating'));
  const matchHeatingCheckboxPorch = screen.getByTestId('match-room-heat-loss-checkbox');
  const checkBoxInputPorch = matchHeatingCheckboxPorch.querySelector('input') as HTMLInputElement;
  expect(checkBoxInputPorch).toBeChecked();
  await setUnderfloorHeatingType(SystemType.ELECTRIC);
  // Go into emitter list
  await userEvent.click(screen.getByText('heatDesign.radiatorsOverview.emitterList.title'));

  // Check the radiator values (living room, kitchen, porch)
  expect(screen.getByTestId('heatDesign-emitterList-emitterDeltaT-living-room')).toHaveTextContent('12*');
  expect(screen.getByTestId('heatDesign-emitterList-emitterDeltaT-kitchen')).toHaveTextContent('9*');
  expect(screen.getByTestId('heatDesign-emitterList-emitterDeltaT-porch')).toHaveTextContent('10');

  expect(screen.getByTestId('heatDesign-emitterList-flowRate-living-room')).toHaveTextContent('47.4');
  expect(screen.getByTestId('heatDesign-emitterList-flowRate-kitchen')).toHaveTextContent('63.3');
  expect(screen.getByTestId('heatDesign-emitterList-flowRate-porch')).toHaveTextContent('30.5');

  // Check the underfloor heating values (toilet)
  expect(screen.getByTestId('heatDesign-emitterList-emitterDeltaT-toilet')).toHaveTextContent('10');
  expect(screen.getByTestId('heatDesign-emitterList-flowRate-toilet')).toHaveTextContent('22.3');

  // Check the summary values
  const [radiatorflowTemp, underfloorHeatingFlowTemp] = await screen.findAllByTestId(
    'heatDesign-emitterList-summary-flowTemp',
  );

  expect(radiatorflowTemp).toHaveTextContent('45.0 °C');
  expect(underfloorHeatingFlowTemp).toHaveTextContent('45.0 °C');

  const [actualRadiatorReturnTemperature, underfloorHeatingReturnTemp] = await screen.findAllByTestId(
    'heatDesign-emitterList-summary-actualCircuitReturnTemperature',
  );
  expect(actualRadiatorReturnTemperature).toHaveTextContent('34.8 °C');
  expect(underfloorHeatingReturnTemp).toHaveTextContent('35.0 °C');

  expect(screen.getByTestId('heatDesign-emitterList-summary-totalFlowNeededForHouse')).toHaveTextContent('163.4 l/h');

  // Check that radiator adjustment note is there
  expect(screen.getByText('* heatDesign.report.roomDetails.emitterDetails.note')).toBeVisible();
});
