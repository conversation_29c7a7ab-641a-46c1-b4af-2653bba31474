import { Box, Stack, SxProps, Theme, Typography } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { HTMLAttributes, ReactNode } from 'react';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { WarningTooltip } from 'components/bill-of-materials/components/common/WarningTooltip';

type InfoBlockProps = {
  icon?: ReactNode;
  value: string;
  label: ReactNode;
  verticalLayout?: boolean;
  id?: string;
  valueStyle?: {
    color?: string;
    background?: string;
    px?: number;
  };
  variant?: 'default' | 'dark';
  tooltip?: ReactNode;
  sx?: SxProps<Theme>;
  warning?: string;
} & HTMLAttributes<HTMLDivElement>;

export default function InfoBlock({
  id,
  icon,
  value,
  label,
  verticalLayout = false,
  valueStyle,
  variant = 'default',
  tooltip,
  sx,
  warning,
}: InfoBlockProps) {
  return (
    <Stack
      direction={verticalLayout ? 'column' : 'row'}
      alignItems={verticalLayout ? 'start' : 'center'}
      spacing={2}
      sx={{
        backgroundColor: variant === 'default' ? 'initial' : beige[200],
        ...(sx ?? {}),
      }}
    >
      <Stack
        direction="row"
        gap={1}
        justifyContent="space-between"
        alignItems="center"
        sx={{
          '@media print': {
            gap: 0.5,
          },
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          gap={1}
          sx={{
            '@media print': {
              gap: 0.5,
            },
          }}
        >
          {icon && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                '@media print': {
                  width: '20px',
                  height: '20px',
                },
              }}
            >
              {icon}
            </Box>
          )}
          <Typography
            variant="number2"
            bgcolor={valueStyle?.background || 'transparent'}
            color={valueStyle?.color || 'inherit'}
            borderRadius={1}
            data-testid={id}
            px={valueStyle?.px || 0}
            lineHeight="150%"
            sx={{
              whiteSpace: 'nowrap',
              '@media print': {
                fontSize: 12,
                fontWeight: 'normal',
              },
            }}
          >
            {value}
          </Typography>
        </Stack>

        {tooltip && <TooltipAira placement="top-start" title={tooltip} />}
        {warning && <WarningTooltip placement="top-start" title={warning} />}
      </Stack>
      <Typography
        variant="body1"
        data-testid={`${id}-label`}
        sx={{
          marginTop: 0,
          '@media print': {
            fontSize: 12,
            fontWeight: 'normal',
          },
        }}
      >
        {label}
      </Typography>
    </Stack>
  );
}
