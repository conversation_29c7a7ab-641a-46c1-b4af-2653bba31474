import { memo, useEffect } from 'react';
import { RadiatorDataWithCatalogueProperties } from '../../stores/types';
import { Stack } from '@mui/material';
import { RadiatorCardView } from './RadiatorCardView';
import { RadiatorCardEdit } from './RadiatorCardEdit';
import { useRadiatorContext } from '../contexts/RadiatorContext';
import { useSelectedRoom } from '../hooks/useSelectedRoom';
import { createEmptyRadiatorData, getCatalogueRadiatorProperties } from '../utils';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useRadiatorCardContext } from '../contexts/RadiatorCardContext';

export type RadiatorCardProps = {
  radiator: RadiatorDataWithCatalogueProperties;
  isEditing: boolean;
};

function RadiatorCardComponent({ radiator, isEditing }: RadiatorCardProps) {
  const { editedRadiators, catalogueRadiatorMap } = useRadiatorContext();
  const { setIsCustom } = useRadiatorCardContext();
  const radiatorBeingReplaced = editedRadiators[radiator.uid]?.radiatorBeingReplaced;
  const selectedRoom = useSelectedRoom();
  const { countryCode } = useGroundwork();
  const roomRadiators = selectedRoom?.radiators ?? [];

  useEffect(() => {
    const isNewExisting = radiator.isExisting && radiator.uid === 'new';
    const isCustomNew = !radiator.specificationReferenceId && radiator.uid !== 'new' && !radiator.isExisting;
    // We want to set the isCustom flag if:
    // 1. The radiator is new and existing
    // 2. The radiator is custom and not being replaced (so only when editing a custom new radiator)
    setIsCustom(isNewExisting || isCustomNew);
  }, [setIsCustom, radiator.isExisting, radiator.uid, radiator.specificationReferenceId, editedRadiators]);

  const getCardContent = () => {
    if (!selectedRoom) {
      return;
    }
    let usedRadiator = radiator;
    // If we have a radiator being replaced, two cases can happen:
    //  1. We are replacing an existing radiator with no current replacement
    //    - In this case we need to create an empty radiator data
    //  2. We are replacing an existing radiator with a current replacement
    //    - In this case we need to use the current replacement's data combined with the catalogue data
    //      if one had been selected before
    if (radiatorBeingReplaced) {
      const replacingRadiator =
        roomRadiators.find((r) => r.uid === radiatorBeingReplaced.replacedBy) ??
        createEmptyRadiatorData({
          roomId: selectedRoom.id,
          countryCode,
        });
      const specificationReferenceId = replacingRadiator.specificationReferenceId;
      const catalogueRadiator = specificationReferenceId ? catalogueRadiatorMap[specificationReferenceId] : undefined;

      usedRadiator = replacingRadiator
        ? {
            ...replacingRadiator,
            ...(catalogueRadiator ? getCatalogueRadiatorProperties(catalogueRadiator) : {}),
          }
        : usedRadiator;
    }
    usedRadiator ??= createEmptyRadiatorData({
      roomId: selectedRoom.id,
      countryCode,
    });

    return isEditing ? (
      <RadiatorCardEdit radiatorBeingReplaced={radiatorBeingReplaced} originalRadiator={usedRadiator} />
    ) : (
      <RadiatorCardView radiator={radiator} />
    );
  };
  return <Stack>{getCardContent()}</Stack>;
}

export const RadiatorCard = memo(RadiatorCardComponent);
