import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { green, grey, surface } from '@ui/theme/colors';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import {
  getCalculatedRadiatorOutput,
  getRadiatorFlowRate,
  hasRadiatorBeenRemovedFromErp,
} from '../../utils/radiatorHelpers';
import { TextField } from '@ui/components/TextField/TextField';
import { FormattedMessage, useIntl } from 'react-intl';
import { RadiatorDataWithCatalogueProperties, RadiatorMode, SystemType } from '../../stores/types';
import { useConstructionYear } from '../../stores/HouseInputsStore';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useClimateDataStore } from '../../stores/ClimateDataStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../../stores/HeatSourceStore';
import {
  PanelRadiatorType,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { columnRadiatorOptions, getRadiatorDataOutputs, panelRadiatorOptions, wrapPropertyIsValid } from '../utils';
import { getDesignRoomTemp } from '../../utils/heatCalculations';
import { useSelectedRoom } from '../hooks/useSelectedRoom';
import { ArrowUpDownOutlinedIcon } from '@ui/components/StandardIcons/ArrowUpDownOutlinedIcon';
import { useRadiatorContext } from '../contexts/RadiatorContext';
import { useRoomsStore } from '../../stores/RoomsStore';
import { MenuItem, Slider, useMediaQuery } from '@mui/material';
import { Switch } from '@ui/components/Switch/Switch';
import { Select } from '@ui/components/Select/Select';
import { validateRadiatorDeltaTAdjustment } from '../../Validator';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { formatDegreeCelsius } from '../../../../utils/helpers';
import isNil from 'lodash/isNil';
import useSliderDebounce from '../../hooks/useDebounce';
import { sendGTMEvent } from '@next/third-parties/google';
import { ConfirmationPopover } from '../../../bill-of-materials/components/common/ConfirmationPopover';
import { BookmarkIcon } from '@ui/components/StandardIcons/BookmarkIcon';
import { BookmarkBorderIcon } from '@ui/components/StandardIcons/BookmarkHollowIcon';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Autocomplete } from '@ui/components/Autocomplete/Autocomplete';
import { RADIATOR_DELTA_T_OPTIONS } from '../radiatorConstants';
import { WarningIcon } from '../../components/WarningIcon';
import { CatalogueRadiator } from '../types';
import { useRadiatorCardContext } from '../contexts/RadiatorCardContext';
import { WarningBox } from 'components/heat-design/components/WarningBox';

export type RadiatorCardDataProps = {
  radiator: RadiatorDataWithCatalogueProperties;
  setRadiator: (radiator: RadiatorDataWithCatalogueProperties) => void;
  isDisabled: boolean;
  areActionsEnabled: boolean;
};

function RadiatorCardDataComponent({ radiator, isDisabled, setRadiator, areActionsEnabled }: RadiatorCardDataProps) {
  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const climateDataStore = useClimateDataStore();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const flowTemperature = useFlowTemp();
  const selectedRoom = useSelectedRoom();
  const { formatMessage } = useIntl();
  const { editedRadiators, startRadiatorEdit } = useRadiatorContext();
  const { setIsCustom, setSelectedCatalogueRadiator, selectedCatalogueRadiator, isCustom } = useRadiatorCardContext();
  const updateRoomById = useRoomsStore((s) => s.actions.updateRoomById);
  const [isDeletePopoverVisible, setIsDeletePopoverVisible] = useState(false);
  const previousSelectedCatalogueRadiator = useRef<CatalogueRadiator | undefined>(selectedCatalogueRadiator);
  const [deleteRadiatorButton, setDeleteRadiatorButton] = useState<HTMLButtonElement | null>(null);

  const onDeleteRadiatorButtonRefChange = useCallback(
    (node: HTMLButtonElement) => {
      setDeleteRadiatorButton(node);
    },
    [setDeleteRadiatorButton],
  );

  const panelType = useMemo(() => {
    return radiator.typeOfHeatEmitter === RadiatorMode.Panel
      ? (radiator.panelType ?? PanelRadiatorType.UNRECOGNIZED)
      : PanelRadiatorType.UNRECOGNIZED;
  }, [radiator]);
  const material = useMemo(() => {
    return radiator.typeOfHeatEmitter === RadiatorMode.Column
      ? (radiator.material ?? RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED)
      : RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED;
  }, [radiator]);
  const radiatorOutputAndDeltaT = getRadiatorDataOutputs(radiator.radiatorDetails);
  const isMediumScreen = useMediaQuery('(max-width: 1860px)');

  const panelTypeLabel = useMemo(() => {
    return panelRadiatorOptions.find((option) => option.value === panelType)?.label;
  }, [panelType]);
  const materialLabel = useMemo(() => {
    return columnRadiatorOptions.find((option) => option.value === material)?.label;
  }, [material]);

  const roomTemperature = useMemo(
    () => (selectedRoom ? getDesignRoomTemp(selectedRoom, constructionYear, countryCode, climateDataStore) : undefined),
    [selectedRoom, constructionYear, countryCode, climateDataStore],
  );
  const isEditing = !!editedRadiators[radiator?.uid] || radiator.uid === 'new';

  const output = useMemo(() => {
    return selectedRoom
      ? getCalculatedRadiatorOutput({
          room: selectedRoom,
          radiatorOutput: radiator,
          constructionYear,
          flowTemperature,
          flowReturnDeltaT,
          climateDataStore,
          countryCode,
        })
      : 0;
  }, [climateDataStore, constructionYear, countryCode, flowReturnDeltaT, flowTemperature, radiator, selectedRoom]);

  useEffect(() => {
    if (selectedCatalogueRadiator) {
      previousSelectedCatalogueRadiator.current = selectedCatalogueRadiator;
    }
  }, [selectedCatalogueRadiator]);
  const deleteRadiator = () => {
    if (!selectedRoom) {
      return;
    }
    const currentRadiators = selectedRoom.radiators;
    const replacedRadiator = currentRadiators.find(
      (singleRadiator: RadiatorDataWithCatalogueProperties) => singleRadiator.replacedBy === radiator.uid,
    );
    if (replacedRadiator) {
      replacedRadiator.replacedBy = undefined;
    }
    const newRadiators = currentRadiators.filter(
      (singleRadiator: RadiatorDataWithCatalogueProperties) => singleRadiator.uid !== radiator.uid,
    );
    updateRoomById(selectedRoom.id, {
      radiators: newRadiators,
    });
  };
  const handleDeltaTAdjustmentChange = (value: number) => {
    if (radiator.radiatorDetails.systemType === SystemType.WATER) {
      setRadiator({
        ...radiator,
        radiatorDetails: {
          systemType: SystemType.WATER,
          nominalOutput: {
            outputWatt: radiator.radiatorDetails.nominalOutput?.outputWatt ?? 0,
            deltaT: radiator.radiatorDetails.nominalOutput?.deltaT ?? 0,
          },
          deltaTAdjustmentCelsius: value,
        },
      });
      debouncedSliderInteractionEvent();
    }
  };

  const debouncedSliderInteractionEvent = useSliderDebounce(
    () =>
      sendGTMEvent({
        event: 'slider_interaction',
        slider_name: 'emitter adjustment',
        room: selectedRoom?.name,
      }),
    [selectedRoom?.name],
  );
  const replaceIndex =
    selectedRoom?.radiators.findIndex((singleRadiator) => singleRadiator.replacedBy === radiator.uid) ?? -1;
  const replacedByIndex =
    selectedRoom?.radiators.findIndex((singleRadiator) => radiator.replacedBy === singleRadiator.uid) ?? -1;
  let index = selectedRoom?.radiators.findIndex((singleRadiator) => singleRadiator.uid === radiator.uid);
  index = !isNil(index) && index > -1 ? index : (selectedRoom?.radiators?.length ?? 0);
  if (!selectedRoom || isNil(roomTemperature)) {
    return null;
  }
  const editedRadiator = editedRadiators[radiator.uid];
  const radiatorBeingReplaced = editedRadiator?.radiatorBeingReplaced;
  const flowRate = getRadiatorFlowRate(radiator, selectedRoom, flowTemperature, flowReturnDeltaT, output).toFixed(2);
  const isCustomToggleVisible =
    !areActionsEnabled && radiatorBeingReplaced?.uid !== radiator.uid && !radiator.isExisting;

  const onIsCustomChange = (value: boolean) => {
    setIsCustom(value);
    if (value) {
      setSelectedCatalogueRadiator(undefined);
    } else {
      setSelectedCatalogueRadiator(previousSelectedCatalogueRadiator.current);
    }
  };
  const isThisRadiatorBeingReplaced = radiatorBeingReplaced?.uid === radiator.uid;
  const propertyIsValid = wrapPropertyIsValid(
    isThisRadiatorBeingReplaced ? { enabled: false } : { enabled: radiator.enabled },
  );

  // Pretty ugly and complicated, fixing this would probably need a bigger restructuring
  // We want to only show the form fields if:
  //  - It's an existing radiator that's not currently being replaced
  //  - It's a non-existing radiator that's currently being edited, and set to `custom`
  const areFormFieldsEnabled =
    (isCustom && !radiator.isExisting && isEditing) ||
    (radiator.isExisting && radiator.uid !== radiatorBeingReplaced?.uid);

  return (
    <Stack
      sx={{
        borderRadius: '16px',
        overflow: 'hidden',
        width: '100%',
        padding: '16px',

        backgroundColor: !!(radiator.replacedBy || radiatorBeingReplaced?.uid === radiator.uid || !radiator.enabled)
          ? surface[200]
          : surface[100],
      }}
      gap={1}
    >
      <Stack
        sx={{
          borderRadius: '16px',
          width: '100%',
          cursor: 'default',
          '&:hover': {
            cursor: 'default',
          },
        }}
        gap={2}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{ width: '100%', paddingRight: '16px' }}
        >
          <Stack direction="row" gap={3} sx={{ height: '32px' }} alignItems="center">
            <Box
              sx={{
                backgroundColor: grey[300],
                borderRadius: '50%',
                width: '32px',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Typography variant="body2" color={grey[700]}>{`R${index + 1}`}</Typography>
            </Box>
            {areActionsEnabled && (
              <>
                {!radiator.isExisting && (
                  <Stack sx={{ height: '100%' }} direction="row" alignItems="center">
                    <IconButton
                      data-testid="edit-radiator-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        const replacedRadiator = selectedRoom?.radiators.find(
                          (singleRadiator: RadiatorDataWithCatalogueProperties) =>
                            singleRadiator.replacedBy === radiator.uid,
                        );
                        startRadiatorEdit(radiator.uid, replacedRadiator);
                      }}
                      sx={{
                        marginRight: '8px',
                        border: `1px solid ${grey[900]}`,
                        width: '32px',
                        height: '100%',
                        borderRadius: '50%',
                        padding: 0,
                      }}
                    >
                      <PenOutlinedIcon height={18} width={18} color={grey[900]} />
                    </IconButton>
                    <Typography variant="body3" color={grey[900]}>
                      <FormattedMessage id="common.label.edit" />
                    </Typography>
                  </Stack>
                )}
                {!radiator.replacedBy && radiator.isExisting && (
                  <Stack sx={{ height: '100%' }} direction="row" alignItems="center">
                    <IconButton
                      data-testid="radiator-row-replace"
                      onClick={(e) => {
                        e.stopPropagation();
                        startRadiatorEdit(radiator.uid, radiator);
                      }}
                      sx={{
                        marginRight: '8px',
                        border: `1px solid ${grey[900]}`,
                        width: '32px',
                        height: '100%',
                        transform: 'rotate(90deg)',
                        borderRadius: '50%',
                      }}
                    >
                      <ArrowUpDownOutlinedIcon color={grey[900]} />
                    </IconButton>
                    <Typography variant="body3" color={grey[900]}>
                      <FormattedMessage id="heatDesign.replaceRadiator" />
                    </Typography>
                  </Stack>
                )}
              </>
            )}

            {replacedByIndex > -1 && (
              <Box
                sx={{
                  borderRadius: '16px',
                  backgroundColor: grey[700],
                  padding: '8px',
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Typography color="#fff" variant="body3">
                  <FormattedMessage
                    id="heatDesign.radiatorTable.replacedBy"
                    values={{ id: `R${replacedByIndex + 1}` }}
                  />
                </Typography>
              </Box>
            )}

            {replaceIndex > -1 && (
              <Box
                sx={{
                  borderRadius: '16px',
                  backgroundColor: grey[700],
                  padding: '8px',
                  height: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Typography color="#fff" variant="body3">
                  <FormattedMessage id="heatDesign.radiatorTable.replaces" values={{ id: `R${replaceIndex + 1}` }} />
                </Typography>
              </Box>
            )}
          </Stack>
          {hasRadiatorBeenRemovedFromErp(radiator) && (
            <WarningBox contentId="heatDesign.radiatorModal.removedFromErp" sx={{ maxWidth: '40%' }} />
          )}
          {areActionsEnabled && (
            <Stack direction="row" sx={{ height: '100%' }} gap={3}>
              <Stack direction="row" alignItems="center" gap={1}>
                <IconButton
                  data-testid={`radiator-enabled-toggle-${radiator.enabled ? 'enabled' : 'disabled'}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setRadiator({
                      ...radiator,
                      enabled: !radiator.enabled,
                    });
                  }}
                  sx={{ border: `1px solid ${grey[900]}`, height: '32px', width: '32px', padding: 0 }}
                >
                  {radiator.enabled ? (
                    <BookmarkIcon height={16} width={16} color={grey[900]} />
                  ) : (
                    <BookmarkBorderIcon height={16} width={16} color={grey[900]} />
                  )}
                </IconButton>
                <Typography variant="body2">
                  <FormattedMessage id="heatDesign.keep" />
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" gap={1}>
                <IconButton
                  ref={onDeleteRadiatorButtonRefChange}
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDeletePopoverVisible(true);
                  }}
                  sx={{ border: `1px solid ${grey[900]}`, height: '32px', width: '32px', padding: 0 }}
                >
                  <BinOutlinedIcon height={20} width={20} color={grey[900]} />
                </IconButton>
                <Typography variant="body2">
                  <FormattedMessage id="common.label.delete" />
                </Typography>
              </Stack>
            </Stack>
          )}
          {isCustomToggleVisible && (
            <Stack direction="row" alignItems="center" gap={2}>
              <Switch
                disabled={isDisabled}
                checked={isCustom}
                onChange={(value) => {
                  onIsCustomChange(value.target.checked);
                }}
                data-testid="is-custom-switch"
              />
              <Typography variant="body1">
                <FormattedMessage id="heatDesign.customRadiator" />
              </Typography>
              <TooltipAira title={formatMessage({ id: 'heatDesign.isCustomTooltip' })} />
            </Stack>
          )}
        </Stack>
        <Stack direction="row" alignItems="center" gap={3} sx={{ position: 'relative' }} justifyContent="space-between">
          <Stack
            sx={{
              height: isMediumScreen ? '150px' : '85px',
              justifyContent: isMediumScreen ? 'space-between' : 'center',
            }}
            direction={isMediumScreen ? 'column' : 'row'}
            alignItems={isMediumScreen ? 'start' : 'center'}
            flexWrap="wrap"
            gap={2}
          >
            {areFormFieldsEnabled ? (
              <Stack direction="row" alignItems="center" gap={2}>
                <Select
                  label={formatMessage({ id: 'heatDesign.radiatorModal.catalogue.category' })}
                  size="small"
                  data-testid="radiator-category-select"
                  name="select-rad-mode"
                  onChange={(event) => {
                    setRadiator({
                      ...radiator,
                      typeOfHeatEmitter: event.target.value as RadiatorMode,
                    });
                  }}
                  disabled={isDisabled}
                  value={radiator.typeOfHeatEmitter ?? undefined}
                  sx={{
                    display: 'flex',
                    gap: 1,
                    minWidth: 'unset',
                    width: '180px',
                    '.MuiInputBase-root': {
                      minWidth: '100px',
                      width: 'auto',
                      flex: 1,
                      margin: '0 !important',
                    },
                    '.MuiInputLabel-root': {
                      margin: '0 !important',
                      width: 'auto',
                    },
                  }}
                >
                  <MenuItem value={RadiatorMode.Panel}>Panel</MenuItem>
                  <MenuItem value={RadiatorMode.Column}>Column</MenuItem>
                </Select>
                {radiator.typeOfHeatEmitter === RadiatorMode.Panel && (
                  <LabelValueDisplay
                    orientation="vertical"
                    label={formatMessage({ id: 'common.label.type' })}
                    value={panelTypeLabel}
                  />
                )}
                {radiator.typeOfHeatEmitter === RadiatorMode.Column && (
                  <Stack direction="row" alignItems="center" gap={2}>
                    <>
                      <LabelValueDisplay
                        orientation="vertical"
                        label={formatMessage({ id: 'heatDesign.radiatorModal.shared.material' })}
                        value="Unknown"
                      />
                      <LabelValueDisplay
                        orientation="vertical"
                        label={formatMessage({ id: 'heatDesign.radiatorModal.shared.columns' })}
                        value="Unknown"
                      />
                    </>
                  </Stack>
                )}
              </Stack>
            ) : (
              <Stack direction="row" gap={2}>
                <LabelValueDisplay
                  orientation="vertical"
                  sx={{ width: '170px', '.label-value-display-value': { height: '40px' } }}
                  label={formatMessage({ id: 'heatDesign.radiatorModal.catalogue.category' })}
                  value={radiator.typeOfHeatEmitter ?? 'N/A'}
                />
                {radiator.typeOfHeatEmitter === RadiatorMode.Panel && (
                  <LabelValueDisplay
                    orientation="vertical"
                    sx={{ width: '170px' }}
                    label={formatMessage({ id: 'common.label.type' })}
                    value={panelTypeLabel ?? 'N/A'}
                  />
                )}
                {radiator.typeOfHeatEmitter === RadiatorMode.Column && (
                  <LabelValueDisplay
                    orientation="vertical"
                    sx={{ width: '170px' }}
                    label={formatMessage({ id: 'heatDesign.radiatorModal.shared.material' })}
                    value={materialLabel ?? 'N/A'}
                  />
                )}
              </Stack>
            )}
            <Stack direction="row" alignItems="center" gap={2}>
              <Stack
                direction="row"
                alignItems="center"
                sx={{
                  width: '170px',
                  height: '100%',
                }}
                justifyContent="space-between"
              >
                {areFormFieldsEnabled ? (
                  <NumericTextField
                    sx={{ width: '100%' }}
                    name="catalogue-radiator-height"
                    type="number"
                    size="small"
                    disabled={isDisabled}
                    data-testid="radiator-height-input"
                    value={radiator.height}
                    onChange={(value) => {
                      setRadiator({
                        ...radiator,
                        height: value,
                      });
                    }}
                    error={!propertyIsValid('radiator', 'height', radiator.height)}
                    label={
                      <Typography sx={{ textWrap: 'nowrap' }} variant="body2Emphasis">
                        <FormattedMessage id="heatDesign.radiatorModal.shared.height" />
                      </Typography>
                    }
                  />
                ) : (
                  <LabelValueDisplay
                    orientation="vertical"
                    hasError={!propertyIsValid('radiator', 'height', radiator.height)}
                    data-testid="radiator-height-label"
                    label={formatMessage({ id: 'heatDesign.radiatorModal.shared.height' })}
                    value={radiator.height || 'N/A'}
                  />
                )}
              </Stack>
              <Stack
                direction="row"
                alignItems="center"
                sx={{
                  height: '100%',
                  width: '170px',
                }}
                justifyContent="space-between"
              >
                {areFormFieldsEnabled ? (
                  <NumericTextField
                    name="catalogue-radiator-height"
                    type="number"
                    size="small"
                    disabled={isDisabled}
                    data-testid="radiator-length-input"
                    value={radiator.width}
                    onChange={(value) => {
                      setRadiator({
                        ...radiator,
                        width: value,
                      });
                    }}
                    error={!propertyIsValid('radiator', 'width', radiator.width)}
                    label={
                      <Typography sx={{ textWrap: 'nowrap' }} variant="body2Emphasis">
                        <FormattedMessage id="heatDesign.radiatorModal.shared.length" />
                      </Typography>
                    }
                  />
                ) : (
                  <LabelValueDisplay
                    orientation="vertical"
                    hasError={!propertyIsValid('radiator', 'width', radiator.width)}
                    data-testid="radiator-length-label"
                    label={formatMessage({ id: 'heatDesign.radiatorModal.shared.length' })}
                    value={radiator.width || 'N/A'}
                  />
                )}
              </Stack>
            </Stack>
          </Stack>
          <Stack direction="row" alignItems="center" gap={3} sx={{ height: isMediumScreen ? '150px' : '85px' }}>
            <Stack
              direction={isMediumScreen ? 'column' : 'row'}
              alignItems={isMediumScreen ? 'flex-start' : 'center'}
              gap={2}
              flexWrap="wrap"
              sx={{
                height: isMediumScreen ? '150px' : '85px',
                justifyContent: isMediumScreen ? 'space-between' : 'center',
              }}
            >
              {areFormFieldsEnabled ? (
                <>
                  <TextField
                    name="catalogue-radiator-nominal-output"
                    type="number"
                    size="small"
                    label={
                      <Typography variant="body2" fontWeight={500} sx={{ textWrap: 'nowrap', lineHeight: 'normal' }}>
                        {formatMessage({ id: 'heatDesign.radiatorModal.table.nominalOutput.generic' })}
                      </Typography>
                    }
                    data-testid="radiator-output-input"
                    disabled={isDisabled}
                    sx={{ width: isMediumScreen ? '250px' : 'auto' }}
                    value={radiatorOutputAndDeltaT.output || ''}
                    onChange={(event) => {
                      if (radiator.radiatorDetails.systemType === SystemType.WATER) {
                        setRadiator({
                          ...radiator,
                          radiatorDetails: {
                            ...radiator.radiatorDetails,
                            nominalOutput: {
                              deltaT: radiator.radiatorDetails.nominalOutput?.deltaT ?? 0,
                              outputWatt: Number(event.target.value),
                            },
                          },
                        });
                      } else {
                        setRadiator({
                          ...radiator,
                          radiatorDetails: {
                            ...radiator.radiatorDetails,
                            outputWatt: Number(event.target.value),
                          },
                        });
                      }
                    }}
                    error={!propertyIsValid('radiator', 'output', radiatorOutputAndDeltaT.output ?? 0)}
                  />
                  {radiator.radiatorDetails.systemType === SystemType.WATER && (
                    <Autocomplete
                      name="catalogue-radiator-deltaT"
                      data-testid="radiator-deltaT-select"
                      label={
                        <Typography
                          sx={{ textWrap: 'nowrap', lineHeight: 'normal', marginBottom: '2px' }}
                          variant="body2"
                          fontWeight={500}
                        >
                          ΔT (°C)
                        </Typography>
                      }
                      sx={{ width: '250px' }}
                      disabled={isDisabled}
                      size="small"
                      error={!propertyIsValid('radiator', 'deltaT', radiatorOutputAndDeltaT.deltaT ?? 0)}
                      defaultValue={RADIATOR_DELTA_T_OPTIONS.find(
                        (option) => option.value === radiatorOutputAndDeltaT.deltaT,
                      )}
                      options={RADIATOR_DELTA_T_OPTIONS}
                      disableClearable
                      disablePortal={false}
                      onChange={(option) => {
                        if (radiator.radiatorDetails.systemType === SystemType.WATER) {
                          if (radiator.radiatorDetails.nominalOutput) {
                            setRadiator({
                              ...radiator,
                              radiatorDetails: {
                                ...radiator.radiatorDetails,
                                nominalOutput: {
                                  ...radiator.radiatorDetails.nominalOutput,
                                  deltaT: Number(option?.value ?? 0),
                                },
                              },
                            });
                          } else {
                            setRadiator({
                              ...radiator,
                              radiatorDetails: {
                                ...radiator.radiatorDetails,
                                nominalOutput: {
                                  outputWatt: 0,
                                  deltaT: Number(option?.value ?? 0),
                                },
                              },
                            });
                          }
                        }
                      }}
                    />
                  )}
                </>
              ) : (
                <>
                  <LabelValueDisplay
                    orientation="vertical"
                    data-testid="radiator-output-label"
                    hasError={!propertyIsValid('radiator', 'output', radiatorOutputAndDeltaT.output ?? 0)}
                    label={formatMessage({ id: 'heatDesign.radiatorModal.table.nominalOutput.generic' })}
                    value={radiatorOutputAndDeltaT.output || 'N/A'}
                  />
                  <LabelValueDisplay
                    orientation="vertical"
                    data-testid="radiator-deltaT-label"
                    hasError={!propertyIsValid('radiator', 'deltaT', radiatorOutputAndDeltaT.deltaT ?? 0)}
                    label={formatMessage({ id: 'heatDesign.radiatorTable.nominalOutput.deltaT' })}
                    value={
                      RADIATOR_DELTA_T_OPTIONS.find((value) => value.value === radiatorOutputAndDeltaT.deltaT)?.label ??
                      'N/A'
                    }
                  />
                </>
              )}
            </Stack>

            <Stack justifyContent="center" sx={{ height: '100%' }}>
              <Typography sx={{ marginBottom: '6px' }} variant="body2" fontWeight={500}>
                <FormattedMessage id="heatDesign.radiatorTable.calculated.output" />
              </Typography>
              <Stack
                direction="row"
                alignItems="center"
                gap={2}
                sx={{
                  flex: 1,
                  padding: '8px',
                  borderRadius: '8px',
                  minWidth: '175px',
                  height: isMediumScreen ? 'auto' : '40px',
                  maxHeight: isMediumScreen ? 'none' : '40px',
                  backgroundColor: radiator.enabled && radiatorOutputAndDeltaT.output > 0 ? green[200] : surface[100],
                }}
              >
                <Stack
                  alignItems="center"
                  justifyContent="center"
                  sx={{
                    opacity:
                      radiator.enabled && !propertyIsValid('radiator', 'radiatorDetails', radiator.radiatorDetails)
                        ? '1'
                        : '0',
                    flex: '0 0 25px',
                    height: '25px',
                    width: '25px',
                  }}
                >
                  <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
                </Stack>
                <Stack sx={{ flex: '1' }} direction="row" justifyContent="flex-end" alignItems="center">
                  <Typography
                    variant="number2"
                    sx={{
                      color: radiator.enabled && radiatorOutputAndDeltaT.output > 0 ? green[700] : grey[700],
                      textWrap: 'nowrap',
                    }}
                    data-testid="calculatedRadiatorOutput"
                  >
                    {selectedRoom
                      ? getCalculatedRadiatorOutput({
                          room: selectedRoom,
                          radiatorOutput: radiator,
                          constructionYear,
                          countryCode,
                          flowReturnDeltaT,
                          flowTemperature,
                          climateDataStore,
                        }) + ' W'
                      : 0}
                  </Typography>
                </Stack>

                <Stack
                  sx={{ flex: '0 0 25px', width: '25px', height: '25px' }}
                  alignItems="center"
                  justifyContent="center"
                >
                  <TooltipAira title={formatMessage({ id: 'heatDesign.radiators.calculatedOutputTooltip' })} />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Stack>

        <ConfirmationPopover
          anchorEl={deleteRadiatorButton}
          open={isDeletePopoverVisible}
          onClose={() => setIsDeletePopoverVisible(false)}
          title={formatMessage({
            id: 'heatDesign.deleteRadiator.title',
          })}
          description={formatMessage({
            id: 'heatDesign.deleteRadiator.detail',
          })}
          confirmText={formatMessage({ id: 'common.label.delete' })}
          onConfirm={() => {
            deleteRadiator();
            setIsDeletePopoverVisible(false);
          }}
          confirmIcon={<BinOutlinedIcon width={20} height={20} />}
        />
      </Stack>
      <Stack direction="row" alignItems="center" sx={{ width: '100%' }}>
        <TextField
          data-testid="radiator-comment"
          name={`radiatorComment+${radiator.uid}`}
          type="textarea"
          value={radiator.comment}
          size="small"
          containerProps={{
            sx: { width: '100%', '.MuiInputBase-root': { flex: '1' } },
          }}
          onChange={(e) =>
            setRadiator({
              ...radiator,
              comment: e.target.value,
            })
          }
          label={<FormattedMessage id="common.label.comment" />}
        />
      </Stack>

      {selectedRoom?.enableRadiatorDeltaTAdjustment &&
        !isThisRadiatorBeingReplaced &&
        radiator.radiatorDetails.systemType === SystemType.WATER && (
          <Stack
            sx={{
              width: '100%',
              gap: 1,
              flex: '1',
            }}
            direction="row"
          >
            <Stack
              gap={4}
              direction="row"
              sx={{
                alignItems: 'center',
                height: '55px',
                width: '100%',
                padding: '8px',
                borderRadius: '8px',
                flex: '1',
              }}
            >
              <Stack gap={2} sx={{ height: '100%', alignItems: 'center' }} direction="row">
                <Typography
                  variant="body2Emphasis"
                  component="label"
                  htmlFor={`radiatorDeltaT-adjustment+${radiator.uid}`}
                  sx={{ textWrap: 'nowrap' }}
                >
                  <FormattedMessage id="heatDesign.radiatorTable.flowRateAdjustment.label" />
                </Typography>
                <Slider
                  id="emitterTemperatureAdjustmentSlider"
                  name="emitter temperature adjustment slider"
                  valueLabelFormat={(v) => formatDegreeCelsius(v, 0)}
                  value={radiator.radiatorDetails.deltaTAdjustmentCelsius}
                  onChange={(_event, v) => handleDeltaTAdjustmentChange(v as number)}
                  min={-flowReturnDeltaT + 1}
                  max={roomTemperature}
                  step={1}
                  disabled={isDisabled}
                  valueLabelDisplay="auto"
                  sx={{ flex: '1', width: '100px' }}
                />
                <NumericTextField
                  disabled={isDisabled}
                  id="emitterTemperatureAdjustment"
                  data-testid="emitter-temperature-adjustment-input"
                  name={`radiatorDeltaT-adjustment+${radiator.uid}`}
                  value={radiator.radiatorDetails.deltaTAdjustmentCelsius}
                  size="small"
                  onChange={(value) => handleDeltaTAdjustmentChange(value)}
                  error={!validateRadiatorDeltaTAdjustment(radiator, selectedRoom, roomTemperature, flowReturnDeltaT)}
                  sx={{ maxWidth: '70px', alignSelf: 'flex-end', height: '100%' }}
                />
              </Stack>
              <Stack
                sx={{
                  width: '170px',
                  height: '100%',
                  flex: '0 0 auto',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <LabelValueDisplay
                  label={formatMessage({ id: 'heatDesign.newEmitterDeltaT' })}
                  value={flowReturnDeltaT + radiator.radiatorDetails.deltaTAdjustmentCelsius + '°C'}
                />
              </Stack>
              <Stack
                sx={{
                  width: '170px',
                  height: '100%',
                  flex: '0 0 auto',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <LabelValueDisplay label={formatMessage({ id: 'heatDesign.flowRate' })} value={flowRate + 'l/h'} />
              </Stack>
            </Stack>
          </Stack>
        )}
    </Stack>
  );
}

export const RadiatorCardData = memo(RadiatorCardDataComponent);
