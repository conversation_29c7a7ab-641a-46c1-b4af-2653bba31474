import { memo } from 'react';
import { RadiatorCardContextProvider } from '../contexts/RadiatorCardContext';
import { RadiatorCard } from './RadiatorCard';
import { RadiatorDataWithCatalogueProperties } from '../../stores/types';

export type RadiatorCardWrapperProps = {
  radiator: RadiatorDataWithCatalogueProperties;
  isEditing: boolean;
};

function RadiatorCardWrapperComponent(props: RadiatorCardWrapperProps) {
  return (
    <RadiatorCardContextProvider>
      <RadiatorCard isEditing={props.isEditing} radiator={props.radiator} />
    </RadiatorCardContextProvider>
  );
}

export const RadiatorCardWrapper = memo(RadiatorCardWrapperComponent);
