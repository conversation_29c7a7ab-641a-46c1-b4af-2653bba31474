import { memo } from 'react';
import { RadiatorDataWithCatalogueProperties } from '../../stores/types';
import { RadiatorCardData } from './RadiatorCardData';
import { Box } from '@mui/material';
import { useRoomsStore } from '../../stores/RoomsStore';
import { useSelectedRoom } from '../hooks/useSelectedRoom';

export type RadiatorCardViewProps = {
  radiator: RadiatorDataWithCatalogueProperties;
};

function RadiatorCardViewComponent({ radiator }: RadiatorCardViewProps) {
  const updateRoomById = useRoomsStore((s) => s.actions.updateRoomById);
  const selectedRoom = useSelectedRoom();
  // Hacky handling for existing radiators until we have automatic filling of output etc.
  // This done like so because exceptionally for existing radiators we need to let the users edit the fields without
  // entering edit-mode (except if the radiator is disabled)
  const isEnabledExistingRadiator = radiator.isExisting && radiator.enabled;
  return (
    <Box sx={{ borderRadius: '16px' }}>
      <RadiatorCardData
        areActionsEnabled={true}
        setRadiator={(radiator) => {
          if (selectedRoom) {
            const index = selectedRoom.radiators.findIndex((r) => r.uid === radiator.uid);
            if (index > -1) {
              updateRoomById(selectedRoom.id, {
                radiators: selectedRoom.radiators.with(index, radiator),
              });
            }
          }
        }}
        isDisabled={!isEnabledExistingRadiator} // Until we've implemented automatic filling of fields we want to enable faster editing for existing radiators
        radiator={radiator}
      />
    </Box>
  );
}

export const RadiatorCardView = memo(RadiatorCardViewComponent);
