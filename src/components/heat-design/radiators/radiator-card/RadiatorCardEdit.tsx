import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { RadiatorData, RadiatorDataWithCatalogueProperties } from '../../stores/types';
import Stack from '@mui/material/Stack';
import { RadiatorCardData } from './RadiatorCardData';
import { AddSquareOutlinedIcon } from '@ui/components/StandardIcons/AddSquareOutlinedIcon';
import { Box, IconButton, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { brandYellow, grey, surface } from '@ui/theme/colors';
import { catalogueRadiatorToRadiatorData, createEmptyRadiatorData } from '../utils';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { useRoomsStore } from '../../stores/RoomsStore';
import { useRadiatorContext } from '../contexts/RadiatorContext';
import { useSelectedRoom } from '../hooks/useSelectedRoom';
import { useGroundwork } from '../../../../context/groundwork-context';
import { RadiatorCatalogue } from '../RadiatorCatalogue';
import { useRadiatorCardContext } from '../contexts/RadiatorCardContext';
import { ConfirmationPopover } from '../../../bill-of-materials/components/common/ConfirmationPopover';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { radiatorIsValid } from '../../Validator';
import { v4 as uuidv4 } from 'uuid';

export type RadiatorCardEditProps = {
  radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties;
  originalRadiator: RadiatorDataWithCatalogueProperties;
};

function RadiatorCardEditComponent({ originalRadiator, radiatorBeingReplaced }: RadiatorCardEditProps) {
  const selectedRoom = useSelectedRoom();
  const { countryCode } = useGroundwork();
  const { formatMessage } = useIntl();
  const [cancelButton, setCancelButton] = useState<HTMLButtonElement | null>(null);
  const [isCancelPopoverVisible, setIsCancelPopoverVisible] = useState(false);
  const [editableCustomRadiator, setEditableCustomRadiator] = useState<RadiatorDataWithCatalogueProperties>(
    originalRadiator?.specificationReferenceId || !originalRadiator
      ? createEmptyRadiatorData({
          roomId: selectedRoom?.id ?? '',
          countryCode: countryCode,
          id: originalRadiator?.uid,
          overrides: {
            isExisting: originalRadiator.isExisting,
          },
        })
      : originalRadiator,
  );
  const [editableCatalogueRadiator, setEditableCatalogueRadiator] = useState<RadiatorDataWithCatalogueProperties>(
    originalRadiator?.specificationReferenceId
      ? originalRadiator
      : createEmptyRadiatorData({
          roomId: selectedRoom?.id ?? '',
          countryCode: countryCode,
          id: originalRadiator?.uid,
          overrides: {
            isExisting: originalRadiator.isExisting,
          },
        }),
  );
  const updateRoomById = useRoomsStore((s) => s.actions.updateRoomById);
  const { setNewRadiator, stopRadiatorEdit, setNewExistingRadiator, newExistingRadiator, newRadiator } =
    useRadiatorContext();
  const { selectedCatalogueRadiator, isCustom } = useRadiatorCardContext();

  const usedRadiator = useMemo(() => {
    return isCustom ? editableCustomRadiator : editableCatalogueRadiator;
  }, [isCustom, editableCustomRadiator, editableCatalogueRadiator]);

  const onCancelButtonRefChange = useCallback(
    (node: HTMLButtonElement) => {
      setCancelButton(node);
    },
    [setCancelButton],
  );

  useEffect(() => {
    if (!selectedRoom) {
      return;
    }
    if (selectedCatalogueRadiator) {
      const newRadiator = catalogueRadiatorToRadiatorData(selectedCatalogueRadiator, selectedRoom, originalRadiator);
      setEditableCatalogueRadiator(newRadiator);
    }
  }, [setEditableCatalogueRadiator, selectedCatalogueRadiator, selectedRoom, usedRadiator.uid, originalRadiator]);
  const onStopRadiatorEdit = () => {
    stopRadiatorEdit(usedRadiator.uid);
    if (radiatorBeingReplaced) {
      stopRadiatorEdit(radiatorBeingReplaced.uid);
    }
    if (usedRadiator.uid === newExistingRadiator?.uid) {
      setNewExistingRadiator(undefined);
    }
    if (usedRadiator.uid === newRadiator?.uid) {
      setNewRadiator(undefined);
    }
    setIsCancelPopoverVisible(false);
  };
  const save = () => {
    if (!selectedRoom) {
      throw new Error('No selected room');
    }
    const radiatorToSave = { ...usedRadiator };
    // If there was an original radiator we need to make sure to retain the UUID - it can change
    // for example when calling createEmptyRadiatorData()
    if (originalRadiator?.uid) {
      radiatorToSave.uid = originalRadiator.uid;
    }
    // To know if we are editing a new radiator or an existing one, we need to check if the uid is 'new'
    // if so we override with an actual ID
    if (radiatorToSave.uid === 'new') {
      radiatorToSave.uid = uuidv4();
    }
    const indexToReplace = selectedRoom.radiators.findIndex(
      (radiatorData: RadiatorData) => radiatorData.uid === radiatorToSave.uid,
    );
    // For editing a new radiator, we need to replace the one in the room with the new/edited one
    if (indexToReplace > -1) {
      const newRadiators = selectedRoom.radiators.with(indexToReplace, radiatorToSave);
      updateRoomById(selectedRoom.id, {
        radiators: newRadiators,
      });
    } else {
      if (radiatorBeingReplaced) {
        const existingRadiatorBeingReplaced = selectedRoom.radiators.find(
          (radiatorData: RadiatorData) => radiatorData.uid === radiatorBeingReplaced.uid,
        );
        if (existingRadiatorBeingReplaced) {
          existingRadiatorBeingReplaced.enabled = false;
          existingRadiatorBeingReplaced.replacedBy = radiatorToSave.uid;
        }
      }
      const radiators = [...selectedRoom.radiators, radiatorToSave];
      updateRoomById(selectedRoom.id, {
        radiators: radiators,
      });
    }
    onStopRadiatorEdit();
  };

  const setEditableRadiator = (radiator: RadiatorDataWithCatalogueProperties) => {
    if (isCustom) {
      setEditableCustomRadiator(radiator);
    } else {
      setEditableCatalogueRadiator(radiator);
    }
  };

  const cardTitle = radiatorBeingReplaced
    ? 'heatDesign.radiatorModal.title.replace'
    : originalRadiator.isExisting
      ? 'heatDesign.existingRadiator'
      : 'heatDesign.newRadiator';

  const isSaveDisabled = !radiatorIsValid(isCustom ? editableCustomRadiator : editableCatalogueRadiator);
  return (
    <Stack gap={2} sx={{ boxShadow: '0 8px 36px #00000040', padding: '16px', borderRadius: '16px' }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" alignItems="center" gap={1}>
          <AddSquareOutlinedIcon />
          <Typography variant="headline2">
            <FormattedMessage id={cardTitle} />
          </Typography>
        </Stack>
        <Stack direction="row" gap={2}>
          <IconButton
            disabled={isSaveDisabled}
            onClick={() => {
              save();
            }}
            sx={{
              width: '40px',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '50%',
              pointerEvents: 'auto',
              cursor: 'pointer',
              opacity: '1',
              backgroundColor: brandYellow[400],
              '&:disabled': {
                backgroundColor: surface[100],
                pointerEvents: 'none',
              },
              '&:not(:disabled):hover': {
                backgroundColor: brandYellow[500],
              },
              transition: 'background-color 0.2s',
            }}
            data-testid="save-radiator-button"
          >
            <CheckIconThin color={isSaveDisabled ? grey[300] : grey[900]} />
          </IconButton>
          <IconButton
            sx={{
              width: '40px',
              height: '40px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '8px',
              borderRadius: '50%',
              color: 'black',
              backgroundColor: 'transparent',
              cursor: 'pointer',
              '&:hover': { backgroundColor: grey[300] },
              transition: 'background-color 0.2s',
            }}
            ref={onCancelButtonRefChange}
            data-testid="close-edit-bundle-button"
            onClick={() => {
              setIsCancelPopoverVisible(true);
            }}
          >
            <CrossOutlinedIcon color="black" />
          </IconButton>
        </Stack>
      </Stack>
      <Box
        sx={{
          backgroundColor: '#fff',
        }}
      >
        <Stack gap={2}>
          {radiatorBeingReplaced && (
            <RadiatorCardData
              areActionsEnabled={false}
              isDisabled={true}
              setRadiator={() => {}}
              radiator={radiatorBeingReplaced}
            />
          )}
          <RadiatorCardData
            isDisabled={false}
            areActionsEnabled={false}
            setRadiator={setEditableRadiator}
            radiator={usedRadiator}
          />
        </Stack>
      </Box>
      {!originalRadiator.isExisting && (
        <Box sx={{ overflowX: 'auto' }}>
          <RadiatorCatalogue editedRadiator={usedRadiator} radiatorBeingReplaced={radiatorBeingReplaced} />
        </Box>
      )}
      <ConfirmationPopover
        anchorEl={cancelButton}
        open={isCancelPopoverVisible}
        onClose={() => setIsCancelPopoverVisible(false)}
        title={formatMessage({
          id: 'heatDesign.cancelRadiatorEdit.title',
        })}
        description={formatMessage({
          id: 'heatDesign.cancelRadiatorEdit.detail',
        })}
        confirmText={formatMessage({ id: 'common.label.discard' })}
        onConfirm={() => {
          onStopRadiatorEdit();
        }}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
      />
    </Stack>
  );
}

export const RadiatorCardEdit = memo(RadiatorCardEditComponent);
