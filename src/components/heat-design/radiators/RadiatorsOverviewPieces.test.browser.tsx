import { mockRadiatorData } from 'tests/utils/testUtils';
import { InvalidHeatSources } from './RadiatorsOverviewPieces';
import { OutputType, Room, SystemType } from '../stores/types';

const room: Room = {
  id: '65685896.1ef297ff',
  name: 'Bathroom 1',
  roomType: 'Bathroom',
  isHeated: true,
  level: 0,
  floor: 'Ground Floor',
  floorId: '65685225.82fab3ff',
  designRoomTempOverride: 22,
  avgAirChangesPerHourOverride: 3,
  enableRadiatorDeltaTAdjustment: false,
  openFlue: 'none',
  totalVolume: 7.80074794,
  totalArea: 3.37,
  averageHeight: 2.314762,
  imageSvgData: 'image',
  simplifiedImageSvgData: 'image',
  imageMap: {
    coordinates: [721, 712, 579, 712, 579, 620, 721, 620],
  },
  surfaces: [{}] as unknown as Room['surfaces'],
  radiators: [],
};

describe('InvalidHeatSources', () => {
  it('should not show warnings when no radiators are found', () => {
    expect(InvalidHeatSources({ roomsInSelectedFloor: [] })).toEqual([]);
  });

  it('should show warnings when enabled and radiators output = 0', () => {
    expect(
      JSON.stringify(
        InvalidHeatSources({
          roomsInSelectedFloor: [
            {
              ...room,
              radiators: [
                mockRadiatorData({
                  uid: '656858cc.a8addfff',
                  enabled: true,
                  radiatorDetails: {
                    systemType: SystemType.WATER,
                    deltaTAdjustmentCelsius: 0,
                    nominalOutput: {
                      outputWatt: 0,
                      deltaT: 50,
                    },
                  },
                }),
              ],
            },
          ],
        }),
      ),
    ).toContain('warning-65685896.1ef297ff');
  });

  it('should show warnings when underfloor heating has a negative output', () => {
    expect(
      JSON.stringify(
        InvalidHeatSources({
          roomsInSelectedFloor: [
            {
              ...room,
              underfloorHeating: { outputType: OutputType.CUSTOM, systemType: SystemType.ELECTRIC, outputWatt: -100 },
            },
          ],
        }),
      ),
    ).toContain('warning-65685896.1ef297ff');
  });

  it('should not show warnings when radiators are disabled', () => {
    expect(
      JSON.stringify(
        InvalidHeatSources({
          roomsInSelectedFloor: [
            {
              ...room,
              radiators: [
                mockRadiatorData({
                  enabled: false,
                  radiatorDetails: {
                    systemType: SystemType.WATER,
                    deltaTAdjustmentCelsius: 0,
                    nominalOutput: {
                      outputWatt: 0,
                      deltaT: 50,
                    },
                  },
                }),
              ],
            },
          ],
        }),
      ),
    ).not.toContain('warning-65685896.1ef297ff');
  });

  it('should not show warnings when radiators outputAtRoomToMWTdeltaT50c is > 0', () => {
    expect(
      JSON.stringify(
        InvalidHeatSources({
          roomsInSelectedFloor: [
            {
              ...room,
              radiators: [
                mockRadiatorData({
                  enabled: false,
                  radiatorDetails: {
                    systemType: SystemType.WATER,
                    deltaTAdjustmentCelsius: 0,
                    nominalOutput: {
                      outputWatt: 500,
                      deltaT: 50,
                    },
                  },
                }),
              ],
            },
          ],
        }),
      ),
    ).not.toContain('warning-65685896.1ef297ff');
  });
});
