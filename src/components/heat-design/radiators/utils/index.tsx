import {
  PanelRadiatorType,
  Radiator,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import {
  PanelRadiatorStyle,
  RadiatorDetail,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.radiator.v1';
import { v4 as uuidv4 } from 'uuid';
import { marketConfiguration } from '../../utils/marketConfigurations';
import { CountryCode } from '../../../../utils/marketConfigurations';
import {
  CatalogueRadiator,
  ColumnCatalogueRadiator,
  ColumnRadiatorMenuOption,
  Filter,
  PanelCatalogueRadiator,
  PanelRadiatorMenuOption,
} from '../types';
import {
  EmitterDetails,
  RadiatorData,
  RadiatorDataWithCatalogueProperties,
  RadiatorMode,
  Room,
  SystemType,
} from '../../stores/types';
import { propertyIsValid, PropertyIsValidFn } from '../../Validator';
import { Item } from 'components/bill-of-materials/types';

export const panelRadiatorOptions: PanelRadiatorMenuOption[] = [
  {
    label: '10', // Also known as P1
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_10,
  },
  {
    label: '11', // Also known as K1
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_11,
  },
  {
    label: '20', // Also known as P2
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_20,
  },
  {
    label: '21', // Also known as P+
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_21,
  },
  {
    label: '22', // Also known as K2
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_22,
  },
  {
    label: '33', // Also known as K3
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_33,
  },
  {
    label: 'Other',
    value: PanelRadiatorType.PANEL_RADIATOR_TYPE_UNSPECIFIED,
  },
  {
    label: 'Unknown',
    value: PanelRadiatorType.UNRECOGNIZED,
  },
];

export const panelTypeLabelMapping = panelRadiatorOptions.reduce(
  (acc, item) => {
    acc[item.value] = item.label;
    return acc;
  },
  {} as Record<number, string>,
);

export const columnRadiatorOptions: ColumnRadiatorMenuOption[] = [
  {
    label: 'Steel',
    value: RadiatorMaterial.RADIATOR_MATERIAL_STEEL,
  },
];

export const panelRadiatorLabels = Object.fromEntries(
  panelRadiatorOptions.map((type: PanelRadiatorMenuOption) => [type.value, type.label]),
);

export const columnRadiatorLabels = Object.fromEntries(
  columnRadiatorOptions.map((type: ColumnRadiatorMenuOption) => [type.value, type.label]),
);

function filterRadiator(element: CatalogueRadiator, filter: Filter<CatalogueRadiator>): boolean {
  const { model, brand, heightMm, lengthMm, calculatedOutput } = element;
  let success: boolean = true;

  let nameMatch = false;
  nameMatch =
    (model || '').toLowerCase().includes(filter.model.toLowerCase()) ||
    (element.vendorId || '').toLowerCase().includes(filter.model.toLowerCase()) ||
    (element.manufacturerId || '').toLowerCase().includes(filter.model.toLowerCase());

  success = success && nameMatch;

  success =
    success &&
    (filter.brand !== 'All' ? brand === filter.brand : true) &&
    heightMm <= filter.height.max &&
    heightMm >= filter.height.min &&
    lengthMm <= filter.width.max &&
    lengthMm >= filter.width.min &&
    (calculatedOutput ? calculatedOutput <= filter.output.max && calculatedOutput >= filter.output.min : true);

  return success;
}

export function filterPanelRadiator(element: PanelCatalogueRadiator, filter: Filter<CatalogueRadiator>): boolean {
  const { radiatorDetail } = element;
  let success: boolean = filterRadiator(element, filter);

  let categoryMatch = false;

  if (radiatorDetail?.category !== undefined) {
    if (filter.types.length > 0) {
      if (
        radiatorDetail.category.panelRadiator.type === PanelRadiatorType.UNRECOGNIZED ||
        radiatorDetail.category.panelRadiator.type === PanelRadiatorType.PANEL_RADIATOR_TYPE_UNSPECIFIED
      ) {
        categoryMatch = filter.types.includes(PanelRadiatorType.PANEL_RADIATOR_TYPE_UNSPECIFIED);
      } else {
        categoryMatch = filter.types.includes(radiatorDetail.category.panelRadiator.type);
      }
    } else {
      categoryMatch = true;
    }
  }
  success = success && categoryMatch;

  return success;
}

export function filterColumnRadiator(element: ColumnCatalogueRadiator, filter: Filter<CatalogueRadiator>): boolean {
  const { radiatorDetail } = element;
  let success: boolean = filterRadiator(element, filter);

  success =
    success &&
    radiatorDetail.category.columnRadiator.numberOfColumns <= filter.numColumns.max &&
    radiatorDetail.category.columnRadiator.numberOfColumns >= filter.numColumns.min;

  let materialMatch = false;
  if (filter.materials.length > 0) {
    if (
      radiatorDetail.category.columnRadiator.radiatorMaterial === RadiatorMaterial.UNRECOGNIZED ||
      radiatorDetail.category.columnRadiator.radiatorMaterial === RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED
    ) {
      materialMatch = filter.materials.includes(RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED);
    } else {
      materialMatch = filter.materials.includes(radiatorDetail.category.columnRadiator.radiatorMaterial);
    }
  } else {
    materialMatch = true;
  }

  success = success && materialMatch;

  return success;
}

export const generateEmitterDescription = (rad?: Partial<Radiator>) => {
  if (rad?.radiatorId) {
    if (
      rad.radiatorDetail?.category?.$case === 'panelRadiator' &&
      rad.radiatorDetail?.category?.panelRadiator?.type !== undefined
    ) {
      return `${rad.brand} ${rad.model}, type ${panelRadiatorLabels[rad.radiatorDetail.category.panelRadiator.type]}`;
    }
    if (
      rad.radiatorDetail?.category?.$case === 'columnRadiator' &&
      rad.radiatorDetail?.category?.columnRadiator?.radiatorMaterial !== undefined
    ) {
      const details = rad.radiatorDetail.category.columnRadiator;
      const strVal = columnRadiatorLabels[details.radiatorMaterial];

      return `${rad.brand} ${rad.model}, ${strVal || details.radiatorMaterial}, ${details.numberOfColumns} column(s)`;
    }

    return [rad.brand, rad.model].join(' ');
  }

  return rad?.model || '';
};

/**
 *
 * @param uuid A unique UUID
 * @returns the last four characters of the input UUID
 * @example 1234-abdc-5678-efgh => efgh
 */
export const truncateUUID = (uuid: string) => uuid.slice(0, 4);

function getPanelStyle(style?: PanelRadiatorStyle) {
  switch (style) {
    case PanelRadiatorStyle.PANEL_RADIATOR_STYLE_PLANAR:
      return 'PLANAR';
    case PanelRadiatorStyle.PANEL_RADIATOR_STYLE_COMPACT:
      return 'COMPACT';
    case PanelRadiatorStyle.PANEL_RADIATOR_STYLE_UNSPECIFIED:
    case PanelRadiatorStyle.UNRECOGNIZED:
    case undefined:
      return undefined;
    default:
      // For exhaustiveness
      return style satisfies never;
  }
}

function getNumberOfRadiatorColumns(radiator?: Partial<Radiator>) {
  return radiator?.radiatorDetail?.category?.$case === 'columnRadiator'
    ? radiator?.radiatorDetail.category.columnRadiator.numberOfColumns
    : 1;
}

function getRadiatorMaterial(radiator?: Partial<Radiator>) {
  if (radiator?.radiatorDetail?.category?.$case === 'columnRadiator') {
    return radiator.radiatorDetail.category?.columnRadiator?.radiatorMaterial;
  }
  return RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED;
}

function getTypeOfHeatEmitter(category?: RadiatorDetail['category']) {
  switch (category?.$case) {
    case 'columnRadiator':
      return RadiatorMode.Column;
    case 'panelRadiator':
      return RadiatorMode.Panel;
    case undefined:
      return undefined;
    default:
      // For exhaustiveness
      return category satisfies never;
  }
}

export const getRadiatorDataOutputs = (radiatorDetails?: EmitterDetails) =>
  radiatorDetails?.systemType === SystemType.WATER
    ? {
        output: radiatorDetails.nominalOutput?.outputWatt || 0,
        deltaT: radiatorDetails.nominalOutput?.deltaT || 0,
      }
    : {
        output: radiatorDetails?.outputWatt ?? 0,
        deltaT: 0,
      };

const getPanelType = (radiator: CatalogueRadiator) => {
  if (radiator.radiatorDetail?.category?.$case === 'panelRadiator') {
    return radiator.radiatorDetail.category.panelRadiator.type;
  }
  return undefined;
};

/**
 * Gets properties from a catalogue radiator that aren't persisted on RadiatorData in a form that can be used in the frontend
 * This done so we don't have to persist these properties for RadiatorData, but we can still display these properties
 * for a given RadiatorData object based on the matching catalogue radiator
 * @param radiator
 */
export const getCatalogueRadiatorProperties = (radiator: CatalogueRadiator) => ({
  material: getRadiatorMaterial(radiator),
  numberOfColumns: getNumberOfRadiatorColumns(radiator),
  panelType: getPanelType(radiator),
  erpId: radiator.erpId,
});

export const catalogueRadiatorToRadiatorData: (
  radiator: CatalogueRadiator,
  room: Room,
  oldRadiator?: RadiatorDataWithCatalogueProperties,
) => RadiatorDataWithCatalogueProperties = (
  radiator: Radiator,
  room: Room,
  oldRadiator?: RadiatorDataWithCatalogueProperties,
) => {
  const isChanged = oldRadiator?.specificationReferenceId !== radiator.radiatorId?.value;
  const generatedComment = radiator.radiatorId ? generateEmitterDescription(radiator) : radiator?.model || '';
  const comment = isChanged ? generatedComment : oldRadiator?.comment || generatedComment;
  return {
    uid: oldRadiator?.uid ?? uuidv4(),
    erpId: radiator.erpId,
    specificationReferenceId: radiator.radiatorId?.value,
    width: radiator.lengthMm || 0,
    height: radiator.heightMm || 0,
    isExisting: oldRadiator?.isExisting ?? false,
    enabled: true,
    typeOfHeatEmitter: getTypeOfHeatEmitter(radiator.radiatorDetail?.category),
    radiatorDetails: {
      deltaTAdjustmentCelsius: 0,
      nominalOutput: {
        outputWatt: radiator.nominalOutputWatt || 0,
        deltaT: radiator.deltaTCelsius || 0,
      },
      systemType: SystemType.WATER,
    },
    style: getPanelStyle(
      radiator.radiatorDetail?.category?.$case === 'panelRadiator'
        ? radiator.radiatorDetail.category.panelRadiator.style
        : undefined,
    ),
    panelType: getPanelType(radiator),
    comment: comment,
    area: (radiator.lengthMm || 0) * (radiator?.heightMm || 0),
    floorImageMap: { coordinates: [] },
    info: {},
    roomImageMap: { coordinates: [] },
    roomId: room.id,
    material: getRadiatorMaterial(radiator),
    numberOfColumns: getNumberOfRadiatorColumns(radiator),
  };
};

export const createEmptyRadiatorData: (data: {
  roomId: string;
  countryCode: CountryCode | undefined;
  id?: string;
  overrides?: Partial<RadiatorDataWithCatalogueProperties>;
}) => RadiatorDataWithCatalogueProperties = (data) => ({
  uid: data.id ?? 'new', //If id is not provided, we use 'new' to make a difference between new and existing radiators
  area: 0,
  width: 0,
  height: 0,
  isExisting: false,
  enabled: true,
  comment: '',
  radiatorDetails: {
    deltaTAdjustmentCelsius: 0,
    nominalOutput: {
      outputWatt: 0,
      deltaT: data.countryCode !== undefined ? marketConfiguration[data.countryCode].radiatorDeltaT : 0,
    },
    systemType: SystemType.WATER,
  },
  typeOfHeatEmitter:
    data.countryCode !== undefined ? marketConfiguration[data.countryCode].radiatorPanelType : RadiatorMode.Panel,
  floorImageMap: {
    coordinates: [],
  },
  roomImageMap: {
    coordinates: [],
  },
  info: {},
  roomId: data.roomId,
  material: RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED,
  numberOfColumns: 1,
  ...(data.overrides ?? {}),
});

/**
 * Wraps the propertyIsValid function to check if the radiator is enabled
 * If radiator is not enabled, always returns true for validity.
 * @param radiator
 */
export function wrapPropertyIsValid(radiator: { enabled: boolean }): PropertyIsValidFn {
  return (typeName, propertyName, ...propertyValues) => {
    return radiator.enabled ? propertyIsValid(typeName, propertyName, ...propertyValues) : true;
  };
}

export const getCatalogueRadiatorLabel = (radiator: CatalogueRadiator | undefined) => {
  if (!radiator) {
    return '';
  }
  const catalogueRadiatorBrandAndModel = radiator ? `${radiator.brand} ${radiator.model}` : undefined;
  if (radiator?.radiatorDetail?.category?.$case === 'panelRadiator') {
    const panelValue = radiator.radiatorDetail?.category?.panelRadiator?.type;
    const panelType = panelRadiatorOptions.find((option) => option.value === panelValue)?.label;
    return `${catalogueRadiatorBrandAndModel}${panelType ? `, ${panelType}` : ''}`;
  } else {
    return `${catalogueRadiatorBrandAndModel}, ${radiator.radiatorDetail?.category?.columnRadiator?.radiatorMaterial}`;
  }
};

export const getRadiatorDisplayName = (
  catalogueRadiator?: Radiator,
  erpItem?: Item,
  radiator?: RadiatorData,
): string => {
  return erpItem?.description || radiator?.comment || catalogueRadiator?.model || ' - ';
};
