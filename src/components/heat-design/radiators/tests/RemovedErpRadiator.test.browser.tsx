import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { resetRouterMock } from '@mocks/next/router';
import { cleanup, screen } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { setupWorker } from 'msw/browser';
import { IntlProvider } from 'react-intl';
import { vi } from 'vitest';
import { GroundworkContextProvider } from '../../../../context/groundwork-context';
import untypedAsaHouse from '../../../../tests/heat-loss/asa_house_response.json';
import { radiators } from '../../../../tests/heat-loss/fixtures/catalogueTestData';
import { mockGetServerEnvironment, mocks } from '../../../../tests/utils/mockedTrpcCalls';
import {
  chooseRadiatorFromCatalogue,
  editRadiator,
  filterDifficultRooms,
  getNewRadiatorCards,
  goToNextHeatDesignStep,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  saveRadiator,
  trpcMsw,
} from '../../../../tests/utils/testUtils';
import HeatDesign from '../../HeatDesign';
import { sharedHeatDesignHandlers } from '../../../../tests/utils/heatDesignTestSetup';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';

const getAsaHouse = (): ProtoHeatDesign => {
  const filtered = filterDifficultRooms(untypedAsaHouse as unknown as ProtoHeatDesign);
  return {
    ...filtered,
    ...sampleDefaultDwellingUValueDefaults,
    dwelling: {
      ...filtered.dwelling!,
      floors: filtered.dwelling!.floors.map((floor) => ({
        ...floor,
        rooms: floor.rooms.map((room) => ({
          ...room,
          // Add a 'removed' ERP radiator project
          radiators:
            room.name === 'Living Room'
              ? [
                  ...room.radiators,
                  {
                    id: { value: '0cf10dfc-763a-42b8-aa64-03cce37f665e' },
                    specificationReferenceId: { value: 'mock-rad-no-longer-in-erp' },
                    dataSourceReferences: [],
                    imageMap: undefined,
                    floorImageMap: undefined,
                    widthM: 1.4,
                    heightM: 0.7,
                    toBeInstalled: true, // i.e. it is a 'new' radiator, not 'existing'
                    enabled: true,
                    comment: 'Stelrad Classic Compact, type 11',
                    radiatorDetails: {
                      details: {
                        $case: 'waterRadiatorDetails',
                        waterRadiatorDetails: {
                          nominalOutput: {
                            outputWatt: 1000,
                            deltaT: 50,
                          },
                          deltaTAdjustmentCelsius: 0,
                        },
                      },
                    },
                  },
                ]
              : room.radiators,
        })),
      })),
    },
  };
};

const server = setupWorker(
  mockGetServerEnvironment(),
  mocks.getGrpcEnergySolution.asa,
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.energySolutionDiff,
  trpcMsw.AiraBackend.getTechnicalSpecifications.query(() => Promise.resolve({ productTechnicalSpecifications: [] })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() =>
    Promise.resolve({
      climate: {
        heatingDegreeDays: 2255,
        externalDesignTemperature: -3.2,
        averageExternalTemperature: 10.2,
      },
    }),
  ),
  trpcMsw.HeatLossCalculator.getRoles.query(() => Promise.resolve(['admin'])),
  trpcMsw.AiraBackend.getSurveyForms.query(() =>
    Promise.resolve({
      surveyForms: [],
    }),
  ),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => Promise.resolve({ reports: [] })),
  trpcMsw.HeatLossCalculator.renderPreview.mutation(() => Promise.resolve({ pdf: new Uint8Array([1, 2, 3, 4]) })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => Promise.resolve({ radiators })),
  ...sharedHeatDesignHandlers,
);

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
beforeEach(async () => {
  const currentHeatDesign = getAsaHouse();
  mockRouterForHeatDesign();
  server.use(
    trpcMsw.HeatLossCalculator.loadHeatDesign.query(() =>
      Promise.resolve({
        heatDesign: currentHeatDesign,
        isLocked: false,
        result: undefined,
        updatedAt: new Date(),
        events: [],
      }),
    ),
  );

  renderWithProviders(heatDesignComponent());
  await reload();

  // Advance to radiator overview page
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});
afterAll(() => server.stop());

vi.setConfig({ testTimeout: 60_000 });

test('Verify that the "removed from ERP" warning is not shown in the emitter floor view', async () => {
  expect(screen.queryByText('heatDesign.radiatorModal.removedFromErp')).toBeNull();

  // And that the floor is not shown as invalid
  const groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');
  expect(groundFloorButton.getAttribute('data-valid')).toBe('true');
});

test('Verify that the "removed from ERP" warning is shown on the emitter room view', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  expect(screen.getByText('heatDesign.radiatorModal.removedFromErp')).toBeVisible();
});

test('Verify that the "removed from ERP" warning is shown on the emitter catalogue view', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  const newRadiatorCards = getNewRadiatorCards();
  expect(newRadiatorCards.length).toEqual(1);
  editRadiator(newRadiatorCards[0]!);
  expect(screen.getByText('heatDesign.radiatorModal.removedFromErp')).toBeVisible();
});

test('Verify that the "removed from ERP" warning is shown on the product selection page', async () => {
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  expect(screen.getByText('heatDesign.radiatorModal.removedFromErp')).toBeVisible();
  expect(screen.getAllByTestId('warning-icon')).toHaveLength(2); // 1 for the warning box, 1 inline in the table
});

test('Verify that the "removed from ERP" warning is removed when choosing an available ERP radiator', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  const newRadiatorCards = getNewRadiatorCards();
  expect(newRadiatorCards.length).toEqual(1);
  editRadiator(newRadiatorCards[0]!);

  // Emitter catalogue view
  await chooseRadiatorFromCatalogue({ index: 0 });
  expect(screen.queryByText('heatDesign.radiatorModal.removedFromErp')).toBeNull();

  // Emitter floor view
  await saveRadiator();
  expect(screen.queryByText('heatDesign.radiatorModal.removedFromErp')).toBeNull();

  // Product selection page
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  expect(screen.queryByText('heatDesign.radiatorModal.removedFromErp')).toBeNull();
});
