import { PanelRadiatorType } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { resetRouterMock } from '@mocks/next/router';
import { cleanup, fireEvent, screen, waitFor, within } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { setupWorker } from 'msw/browser';
import { IntlProvider } from 'react-intl';
import { vi } from 'vitest';
import { GroundworkContextProvider } from '../../../../context/groundwork-context';
import untypedAsaHouse from '../../../../tests/heat-loss/asa_house_response.json';
import { radiators } from '../../../../tests/heat-loss/fixtures/catalogueTestData';
import { mockGetServerEnvironment, mocks } from '../../../../tests/utils/mockedTrpcCalls';
import {
  addNewRadiator,
  chooseRadiatorFromCatalogue,
  editRadiator,
  expectRadiator,
  filterDifficultRooms,
  getNewRadiatorCards,
  goToNextHeatDesignStep,
  mockRouterForHeatDesign,
  reload,
  renderWithProviders,
  sampleDefaultDwellingUValueDefaults,
  saveRadiator,
  selectRadiatorFromCatalogue,
  setIndividualTemperatureAdjustment,
  setRadiatorTableFiltersToValues,
  setRadiatorValues,
  trpcMsw,
} from '../../../../tests/utils/testUtils';
import HeatDesign from '../../HeatDesign';
import { sharedHeatDesignHandlers } from '../../../../tests/utils/heatDesignTestSetup';

const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';

const getAsaHouse = (): ProtoHeatDesign => {
  const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
  const filtered: ProtoHeatDesign = filterDifficultRooms(asaRawHouse);
  return {
    ...filtered,
    ...sampleDefaultDwellingUValueDefaults,
  };
};

const server = setupWorker(
  mockGetServerEnvironment(),
  mocks.getGrpcEnergySolution.asa,
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.energySolutionDiff,
  trpcMsw.AiraBackend.getTechnicalSpecifications.query(() => Promise.resolve({ productTechnicalSpecifications: [] })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() =>
    Promise.resolve({
      climate: {
        heatingDegreeDays: 2255,
        externalDesignTemperature: -3.2,
        averageExternalTemperature: 10.2,
      },
    }),
  ),
  trpcMsw.HeatLossCalculator.getRoles.query(() => Promise.resolve(['admin'])),
  trpcMsw.AiraBackend.getSurveyForms.query(() =>
    Promise.resolve({
      surveyForms: [],
    }),
  ),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => Promise.resolve({ reports: [] })),
  trpcMsw.HeatLossCalculator.renderPreview.mutation(() => Promise.resolve({ pdf: new Uint8Array([1, 2, 3, 4]) })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => Promise.resolve({ radiators })),
  ...sharedHeatDesignHandlers,
);

function heatDesignComponent() {
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
beforeEach(async () => {
  const currentHeatDesign = getAsaHouse();
  mockRouterForHeatDesign();
  server.use(
    trpcMsw.HeatLossCalculator.loadHeatDesign.query(() =>
      Promise.resolve({
        heatDesign: currentHeatDesign,
        isLocked: false,
        result: undefined,
        updatedAt: new Date(),
        events: [],
      }),
    ),
  );

  renderWithProviders(heatDesignComponent());
  await reload();

  // Advance to radiator overview page
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();

  // Open the catalogue
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});
afterAll(() => server.stop());

vi.setConfig({ testTimeout: 60_000 });

test('Verify that no specified types yields all types of radiators', async () => {
  window.Element.prototype.getBoundingClientRect = vi.fn().mockReturnValue({ height: 50, width: 500 });
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 0,
      max: 99999,
    },
  });
  await waitFor(() => {
    const rows = screen.getAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(5); // The test data contains 5 panel radiators (and 2 column radiators)
  });
});

test('Verify that filtering on model name works', async () => {
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    model: 'search',
    output: {
      min: 0,
      max: 99999,
    },
  });
  await waitFor(
    async () => {
      const rows = await screen.findAllByTestId('catalogue-radiator-row');
      expect(rows.length).toEqual(1);
    },
    {
      timeout: 1000,
    },
  );
});

test('Verify that filtering on vendor ID works', async () => {
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    model: 'vendo',
    output: {
      min: 0,
      max: 9999,
    },
  });
  await waitFor(
    () => {
      const rows = screen.getAllByTestId('catalogue-radiator-row');
      expect(rows.length).toEqual(2);
    },
    { timeout: 1000 },
  );
});

test('Verify that filtering on manufacturer ID works', async () => {
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    model: 'rer-id-2',
    output: {
      min: 0,
      max: 9999,
    },
  });
  await waitFor(
    () => {
      const rows = screen.getAllByTestId('catalogue-radiator-row');
      expect(rows.length).toEqual(1);
    },
    { timeout: 1000 },
  );
});

test('Verify that filtering on one type works', async () => {
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 0,
      max: 9999,
    },
    types: [PanelRadiatorType.PANEL_RADIATOR_TYPE_11],
  });

  await waitFor(() => {
    const rows = screen.getAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(1);
    const typeFields = screen.getAllByTestId('catalogue-radiator-row-type-column');

    typeFields.forEach((typeField) => {
      expect(typeField).toHaveTextContent('11');
    });
  });
});

test('Verify that the emitter panel exhibits correct values upon manually entering values but then selecting a radiator', async () => {
  await addNewRadiator();
  const newRadiatorCard = screen.getByTestId('new-radiator-card-creating');
  await setRadiatorValues(newRadiatorCard, { isCustom: true });
  const deltaTSelect = await within(newRadiatorCard).findByTestId('radiator-deltaT-select');
  const selectElement = within(deltaTSelect).getByRole('combobox');
  expect(selectElement).toHaveValue('ΔT 50⁰C (75/65/20)');

  const descriptionInput = within(newRadiatorCard)
    .getByTestId('radiator-comment')
    .querySelector('textarea') as HTMLTextAreaElement;
  const heightInput = within(newRadiatorCard)
    .getByTestId('radiator-height-input')
    .querySelector('input') as HTMLInputElement;
  const lengthInput = within(newRadiatorCard)
    .getByTestId('radiator-length-input')
    .querySelector('input') as HTMLInputElement;
  const outputInput = within(newRadiatorCard)
    .getByTestId('radiator-output-input')
    .querySelector('input') as HTMLInputElement;
  await userEvent.type(descriptionInput, 'Custom radiator description');
  await userEvent.type(heightInput, '200');
  await userEvent.type(lengthInput, '400');
  await userEvent.type(outputInput, '900');

  await userEvent.click(selectElement);

  const dropdown = await screen.findByRole('listbox');
  const items = within(dropdown).getAllByRole('option');
  await userEvent.click(items[2]!);

  // Assert that the emitter panel shows the input values
  await waitFor(
    () => {
      expect(descriptionInput).toHaveValue(`Custom radiator description`);
      expect(heightInput).toHaveValue(200);
      expect(lengthInput).toHaveValue(400);
      expect(outputInput).toHaveValue(900);
      expect(selectElement).toHaveValue('ΔT 30⁰C (55/45/20)');
    },
    { timeout: 5_000 },
  );
  await setRadiatorTableFiltersToValues({
    output: {
      min: 0,
      max: 99999,
    },
    model: 'Compact',
    types: [PanelRadiatorType.PANEL_RADIATOR_TYPE_11],
  });

  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  expect(heightInput).not.toBeInTheDocument();
  expect(lengthInput).not.toBeInTheDocument();
  expect(outputInput).not.toBeInTheDocument();
  expect(selectElement).not.toBeInTheDocument();
  const heightLabel = within(newRadiatorCard).getByTestId('radiator-height-label');
  const lengthLabel = within(newRadiatorCard).getByTestId('radiator-length-label');
  const outputLabel = within(newRadiatorCard).getByTestId('radiator-output-label');
  const deltaTLabel = within(newRadiatorCard).getByTestId('radiator-deltaT-label');
  // Assert that the emitter panel now instead shows the values based on the selected radiator

  await waitFor(() => {
    expect(descriptionInput!).toHaveValue(`CenterRad Compact, type 11`); // Model
    expect(heightLabel).toHaveTextContent('300');
    expect(lengthLabel).toHaveTextContent('700');
    expect(outputLabel).toHaveTextContent('2000');
    expect(deltaTLabel).toHaveTextContent('ΔT 42.5⁰C (70/55/20)');
  });
});

test('Verify that custom radiators are saved', async () => {
  await addNewRadiator();
  const newRadiatorCard = screen.getByTestId('new-radiator-card-creating');
  await setRadiatorValues(newRadiatorCard, { isCustom: true });
  const deltaTDropdown = await within(newRadiatorCard).findByTestId('radiator-deltaT-select');
  const deltaTSelectElement = within(deltaTDropdown).getByRole('combobox');
  expect(deltaTSelectElement).toHaveValue('ΔT 50⁰C (75/65/20)');

  const descriptionInput = within(newRadiatorCard)
    .getByTestId('radiator-comment')
    .querySelector('textarea') as HTMLTextAreaElement;
  const heightInput = within(newRadiatorCard)
    .getByTestId('radiator-height-input')
    .querySelector('input') as HTMLInputElement;
  const lengthInput = within(newRadiatorCard)
    .getByTestId('radiator-length-input')
    .querySelector('input') as HTMLInputElement;
  const outputInput = within(newRadiatorCard)
    .getByTestId('radiator-output-input')
    .querySelector('input') as HTMLInputElement;
  await userEvent.type(descriptionInput, 'Custom radiator description');
  await userEvent.type(heightInput, '200');
  await userEvent.type(lengthInput, '400');
  await userEvent.type(outputInput, '900');

  await userEvent.click(deltaTSelectElement);
  const dropdown = await screen.findByRole('listbox');
  const items = within(dropdown).getAllByRole('option');
  await userEvent.click(items[2]!);

  // Assert that the emitter panel shows the input values
  await waitFor(
    () => {
      expect(descriptionInput!).toHaveValue(`Custom radiator description`); // Model
      expect(heightInput).toHaveValue(200);
      expect(lengthInput).toHaveValue(400);
      expect(outputInput).toHaveValue(900);
      expect(deltaTSelectElement).toHaveValue('ΔT 30⁰C (55/45/20)');
    },
    { timeout: 5_000 },
  );
  const newRadiatorRowsBefore = getNewRadiatorCards({ includeCreating: false, isQuery: true });
  expect(newRadiatorRowsBefore.length).toBe(0);
  await saveRadiator();
  // Check that the new radiator is shown in the radiator list
  const newRadiatorRowsAfter = getNewRadiatorCards();
  expect(newRadiatorRowsAfter.length).toBe(1);
  const newRadiatorForm = screen.queryByTestId('new-radiator-card-creating');
  expect(newRadiatorForm).not.toBeInTheDocument();
  expectRadiator(
    newRadiatorRowsAfter[0]!,
    {
      enabled: true,
      comment: 'Custom radiator description',
      height: 200,
      width: 400,
      nominalOutput: 900,
      calculatedOutput: 497,
    },
    false,
  );
});

test('Verify that the catalogue supports column radiators', async () => {
  await addNewRadiator();
  const dropdownButton = within(await screen.findByTestId('catalogue-select-mode')).getByRole('combobox');
  await userEvent.click(dropdownButton);

  const dropdown = await screen.findByRole('listbox');
  const items = within(dropdown).getAllByRole('option');

  await userEvent.click(items[1]!); // Columns

  await waitFor(() => {
    const rows = screen.getAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(2);
  });
});

test('Verify that the catalogue supports selecting different types of radiators', async () => {
  await addNewRadiator();

  const isCustomSwitch = (await screen.findByTestId('is-custom-switch')).querySelector('input') as HTMLInputElement;
  expect(isCustomSwitch).not.toBeChecked();

  const dropdownButton = within(await screen.findByTestId('catalogue-select-mode')).getByRole('combobox');
  await userEvent.click(dropdownButton);

  let dropdown = await screen.findByRole('listbox');
  let items = within(dropdown).getAllByRole('option');

  await userEvent.click(items[1]!); // Columns
  let rows = [];
  await waitFor(() => {
    rows = screen.getAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(2);
  });
  rows = screen.getAllByTestId('catalogue-radiator-row');
  await userEvent.click(rows[0]!);

  await userEvent.click(dropdownButton);

  dropdown = await screen.findByRole('listbox');
  items = within(dropdown).getAllByRole('option');

  await userEvent.click(items[0]!); // Panels
  await setRadiatorTableFiltersToValues({
    output: {
      min: 0,
      max: 99999,
    },
  });

  await waitFor(() => {
    rows = screen.getAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(5);
  });
  rows = screen.getAllByTestId('catalogue-radiator-row');
  await userEvent.click(rows[0]!);

  const typeDropdown = within(screen.getByTestId('catalogue-select-mode')).getByRole('combobox');

  expect(typeDropdown.textContent).toBe('Panel');
});

test('Verify that filtering on multiple types works', async () => {
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    types: [PanelRadiatorType.PANEL_RADIATOR_TYPE_11, PanelRadiatorType.PANEL_RADIATOR_TYPE_20],
  });
  await waitFor(async () => {
    const rows = await screen.findAllByTestId('catalogue-radiator-row');
    expect(rows.length).toEqual(1);
    rows.forEach((row) => {
      const cell = within(row).getByTestId('catalogue-radiator-row-type-column');

      expect(['11', '20']).toContain(cell!.textContent);
    });
  });
});

test('Verify that outputs of radiators match expected values in emitter catalogue and emitter overview list', async () => {
  await addNewRadiator();
  await selectRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  const newRadiatorCards = getNewRadiatorCards();
  expect(newRadiatorCards.length).toEqual(1);
  const outputLabel = within(newRadiatorCards[0]!).getByTestId('calculatedRadiatorOutput');
  expect(outputLabel).toHaveTextContent('2217');
  fireEvent.click(screen.getByTestId('emitter-list-tab'));
  verifyEmitterListOutput('2217');

  fireEvent.click(screen.getByTestId('floor-overview-tab'));
  await setIndividualTemperatureAdjustment(true);
  editRadiator(newRadiatorCards[0]!);
  await setRadiatorValues(getNewRadiatorCards()[0]!, {
    deltaTAdjustment: 10,
  });
  await saveRadiator(newRadiatorCards[0]!);
  expect(within(newRadiatorCards[0]!).getByTestId('calculatedRadiatorOutput')).toHaveTextContent('1491');
  fireEvent.click(screen.getByTestId('emitter-list-tab'));
  verifyEmitterListOutput('1491');

  function verifyEmitterListOutput(output: string) {
    const emitterListRows = screen.getAllByTestId(/emitter-list-row/);
    expect(emitterListRows.length).toEqual(1);
    const emitterListRow = emitterListRows[0];
    const outputCell = within(emitterListRow!).getByTestId('emitter-list-output-cell');
    expect(outputCell).toHaveTextContent(output);
  }
});
