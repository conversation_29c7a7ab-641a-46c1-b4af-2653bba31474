import {
  ColumnRadiator,
  PanelRadiator,
  PanelRadiatorType,
  Radiator,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';

export type Emitter = {
  id: number;
  model: string;
  market: 'Cosmo' | 'Stelrad';
};

export type CatalogueRadiator = Radiator & {
  calculatedOutput?: number;
};

export type PanelCatalogueRadiator = CatalogueRadiator & {
  radiatorDetail: {
    category: {
      $case: 'panelRadiator';
      panelRadiator: PanelRadiator;
    };
  };
};

export type ColumnCatalogueRadiator = CatalogueRadiator & {
  radiatorDetail: {
    category: {
      $case: 'columnRadiator';
      panelRadiator: ColumnRadiator;
    };
  };
};

export type EmitterProperties = {
  radiator: Partial<Radiator>;
  calculatedOutput?: number;
};

export type Filter<T> = T extends Radiator
  ? {
      brand: string;
      model: string;
      height: { min: number; max: number };
      width: { min: number; max: number };
      output: { min: number; max: number };
      types: PanelRadiatorType[];
      numColumns: { min: number; max: number };
      materials: RadiatorMaterial[];
    }
  : never;

export type MenuOption = {
  label: string;
  value: string;
};

export type PanelRadiatorMenuOption = {
  label: string;
  value: PanelRadiatorType;
};

export type ColumnRadiatorMenuOption = {
  label: string;
  value: RadiatorMaterial;
};
