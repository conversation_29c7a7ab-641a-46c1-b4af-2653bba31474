import { vi } from 'vitest';
import React from 'react';
import { cleanup, fireEvent, screen, within } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { v4 as uuidv4 } from 'uuid';
import {
  addNewRadiator,
  chooseRadiatorFromCatalogue,
  editRadiator,
  filterDifficultRooms,
  getExistingRadiatorCards,
  getNewRadiatorCards,
  goToNextHeatDesignStep,
  mockedSlider,
  mockRouterForHeatDesign,
  renderWithProviders,
  returnToRadiatorOverview,
  sampleDefaultDwellingUValueDefaults,
  saveRadiator,
  selectRadiatorFromCatalogue,
  setIndividualTemperatureAdjustment,
  setRadiatorEnabled,
  setRadiatorTableFiltersToValues,
  setRadiatorValues,
  setSwitchToValue,
  setUnderfloorHeatingType,
  trpcMsw,
  waitForHeatDesignLoaded,
} from 'tests/utils/testUtils';
import HeatDesign from 'components/heat-design/HeatDesign';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { userEvent } from '@testing-library/user-event';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { UUID } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.contract.common.v1';
import { mockGetServerEnvironment, mocks } from 'tests/utils/mockedTrpcCalls';
import { radiators } from '../../../tests/heat-loss/fixtures/catalogueTestData';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { setupWorker } from 'msw/browser';
import { SystemType } from '../stores/types';
import { resetRouterMock } from '@mocks/next/router';
import { sharedHeatDesignHandlers } from '../../../tests/utils/heatDesignTestSetup';

mockRouterForHeatDesign();
const electricalRadiatorID = UUID.create({ value: uuidv4() });

const getAsaHouse = (): ProtoHeatDesign => {
  const asaRawHouse = untypedAsaHouse as unknown as ProtoHeatDesign;
  const filtered: ProtoHeatDesign = filterDifficultRooms(asaRawHouse);
  return {
    ...filtered,
    ...sampleDefaultDwellingUValueDefaults,
    dwelling: {
      ...filtered.dwelling!,
      floors: filtered.dwelling!.floors.map((floor) => ({
        ...floor,
        rooms: floor.rooms.map((room) => ({
          ...room,
          // Add an electric radiator to the test data
          radiators:
            room.name === 'Living Room'
              ? [
                  ...room.radiators,
                  {
                    id: electricalRadiatorID,
                    dataSourceReferences: [],
                    imageMap: undefined,
                    floorImageMap: undefined,
                    widthM: 0.65,
                    heightM: 0.25,
                    toBeInstalled: false,
                    enabled: false,
                    comment: 'Electrical radiator',
                    radiatorDetails: {
                      details: {
                        $case: 'electricRadiatorDetails',
                        electricRadiatorDetails: {
                          outputWatt: 0,
                        },
                      },
                    },
                  },
                ]
              : room.radiators,
        })),
      })),
    },
  };
};

let currentHeatDesign = getAsaHouse();

const server = setupWorker(
  mockGetServerEnvironment(),
  mocks.getGrpcEnergySolution.asa,
  mocks.getProducts.asa,
  mocks.getGroundworkForSolution.asa,
  mocks.energySolutionDiff,
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: currentHeatDesign,
    isLocked: false,
    result: undefined,
    updatedAt: new Date(),
    events: [],
  })),
  trpcMsw.HeatLossCalculator.saveHeatDesign.mutation(async (input) => {
    currentHeatDesign = input.input.heatDesign;
    return {};
  }),
  trpcMsw.AiraBackend.getTechnicalSpecifications.query(() => ({ productTechnicalSpecifications: [] })),
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.getRoles.query(() => ['admin']),
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({ surveyForms: [] })),
  trpcMsw.HeatLossCalculator.fetchLockedTechnicalReports.query(() => ({ reports: [] })),
  trpcMsw.HeatLossCalculator.renderPreview.mutation(async () => ({ pdf: new Uint8Array([1, 2, 3, 4]) })),
  trpcMsw.InstallationGroundwork.getRadiatorByCountry.query(() => ({ radiators })),
  mocks.energySolutionDiff,
  ...sharedHeatDesignHandlers,
);

// Helpers
vi.mock('@mui/material/Slider', () => ({ default: mockedSlider }));
vi.mock('../utils/saveToProtobuf', () => ({
  serializeProjectToProtobuf: vi.fn(),
  serializeHeatDesignResult: vi.fn(),
}));

function heatDesignComponent() {
  const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';
  return (
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <HeatDesign includeReload />
      </GroundworkContextProvider>
    </IntlProvider>
  );
}

// Test lifecycle
beforeAll(() =>
  server.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  }),
);
beforeEach(async () => {
  mockRouterForHeatDesign();
  currentHeatDesign = getAsaHouse();
  renderWithProviders(heatDesignComponent());
  await waitForHeatDesignLoaded();

  // Advance to radiator overview page
  expect(await screen.findByText('heatDesign.title.propertyDetails', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.floorOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.heatLossOverview', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.radiatorsOverview', { selector: 'h1' })).toBeInTheDocument();
});
afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
  resetRouterMock();
});
afterAll(() => server.stop());

vi.setConfig({ testTimeout: 60_000 });

test('Verify that new radiators appear in the report', async () => {
  // Add radiators to the living room
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 702,
      max: 702,
    },
  });

  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  await setIndividualTemperatureAdjustment(true);
  const newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  const newlyAddedNewRadiatorRow = newRadiatorRows[newRadiatorRows.length - 1]!;

  editRadiator(newlyAddedNewRadiatorRow);
  await setRadiatorValues(newlyAddedNewRadiatorRow, {
    deltaTAdjustment: 0,
  });

  // Go to the Customer Report page
  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();

  goToNextHeatDesignStep();

  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();
  await userEvent.click(screen.getByText('heatDesign.customerReport.title'));

  const radiatorsTable = screen.getByTestId('new-radiators-table');
  expect(screen.queryByText('heatDesign.customerReport.newRadiators.title')).toBeVisible();
  expect(within(radiatorsTable).getByText('Living Room')).toBeVisible();
  expect(within(radiatorsTable).getByText('CenterRad Compact, type 11', { exact: false })).toBeVisible();
  expect(within(radiatorsTable).getByText('300x700mm', { exact: false })).toBeVisible();
});

test('Verify that replaced radiators appear in the report', async () => {
  // Replace radiator in the living room
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));

  // Select the first existing radiator
  let rows = getExistingRadiatorCards();
  let existingRadiator = rows[0];

  await setIndividualTemperatureAdjustment(true);

  // make sure the radiator is enabled
  setRadiatorEnabled({
    parentContainer: existingRadiator!,
    isEnabled: true,
  });
  const isEnabledButton = within(existingRadiator!).getByTestId('radiator-enabled-toggle-enabled');
  expect(isEnabledButton).toBeInTheDocument();

  await userEvent.click(within(existingRadiator!).getByTestId('radiator-row-replace'));

  await selectRadiatorFromCatalogue({
    index: 0,
    filters: {
      height: {
        min: 250,
        max: 250,
      },
      width: {
        min: 650,
        max: 650,
      },
      output: {
        min: 0,
        max: 8000,
      },
    },
  });
  await saveRadiator(existingRadiator);
  rows = getExistingRadiatorCards();
  existingRadiator = rows[0];

  // make sure the replaced radiator is disabled
  setRadiatorEnabled({
    parentContainer: existingRadiator!,
    isEnabled: false,
  });
  expect(existingRadiator!.textContent).toContain('heatDesign.radiatorTable.replacedBy');
  const newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  const newRadiatorRow = newRadiatorRows[0];
  expect(newRadiatorRow!.textContent).toContain('heatDesign.radiatorTable.replaces');
  returnToRadiatorOverview();
  const groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');

  expect(groundFloorButton).toBeInTheDocument();

  expect(groundFloorButton.getAttribute('data-valid')).toBe('true');

  // Go to the Customer Report page
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();

  await userEvent.click(screen.getByText('heatDesign.customerReport.title'));
  const radiatorsTable = screen.getByTestId('new-radiators-table');
  expect(screen.queryByText('heatDesign.customerReport.newRadiators.title')).toBeVisible();
  expect(within(radiatorsTable).getByText('Living Room')).toBeVisible();
  expect(within(radiatorsTable).getByText('CenterRad Compact', { exact: false })).toBeVisible();
  expect(within(radiatorsTable).getByText('250x650mm', { exact: false })).toBeVisible();
});

test('Verify that electrical radiator replacements appear in the report', async () => {
  // Replace radiator in the living room
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));

  let rows = getExistingRadiatorCards();
  let existingRadiator = rows[rows.length - 1];

  // make sure the radiator is enabled
  setRadiatorEnabled({
    parentContainer: existingRadiator!,
    isEnabled: true,
  });
  await userEvent.click(within(existingRadiator!).getByTestId('radiator-row-replace'));
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator(existingRadiator);

  rows = getExistingRadiatorCards();
  existingRadiator = rows[rows.length - 1];
  // make sure the replaced radiator is disabled
  setRadiatorEnabled({
    parentContainer: existingRadiator!,
    isEnabled: false,
  });
  const newRadiatorRows = getNewRadiatorCards();
  expect(newRadiatorRows.length).toBe(1);
  returnToRadiatorOverview();

  const groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');

  expect(groundFloorButton).toBeInTheDocument();
  expect(groundFloorButton.getAttribute('data-valid')).toBe('true');

  // Go to the Customer Report page
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.productSelection', { selector: 'h1' })).toBeInTheDocument();
  goToNextHeatDesignStep();
  expect(await screen.findByText('heatDesign.title.resultsExport', { selector: 'h1' })).toBeInTheDocument();

  await userEvent.click(screen.getByText('heatDesign.customerReport.title'));
  const radiatorsTable = screen.getByTestId('new-radiators-table');
  expect(screen.queryByText('heatDesign.customerReport.newRadiators.title')).toBeVisible();
  expect(within(radiatorsTable).getByText('Living Room')).toBeVisible();
  expect(within(radiatorsTable).getByText('CenterRad Compact', { exact: false })).toBeVisible();
  expect(within(radiatorsTable).getByText('250x650mm', { exact: false })).toBeVisible();
});

test('Verify the radiator validations', async () => {
  // Check the floor buttons, everything should be valid right now
  let groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');
  expect(groundFloorButton.getAttribute('data-valid')).toBe('true');
  expect(screen.queryByTestId(/invalid-radiator-plan-highlight/)).toBeNull();
  expect(await screen.findByRole('button', { name: '1st Floor' })).toBeInTheDocument();
  await userEvent.click(screen.getByTestId(`radiator-room-living-room-area`));
  const existingRadiatorCard = getExistingRadiatorCards()[0]!;
  setRadiatorEnabled({
    parentContainer: existingRadiatorCard,
    isEnabled: true,
  });
  await setRadiatorValues(existingRadiatorCard, {
    height: 0,
    nominalOutput: 0,
  });
  returnToRadiatorOverview();
  groundFloorButton = await screen.findByTestId('floor-overview-button-Ground Floor');
  expect(groundFloorButton.getAttribute('data-valid')).toBe('false');
  // There should be an invalid polygon on the floor plan highlighting the invalid radiator
  expect(screen.getByTestId(/invalid-radiator-plan-highlight/)).toBeVisible();

  // Clicking the next button should throw up the validation modal
  goToNextHeatDesignStep();
  expect(screen.getByRole('presentation', { name: 'heatDesign.emittersValidationModal.title' })).toBeInTheDocument();

  // And then clicking the problem room should open that room's modal
  await userEvent.click(screen.getByTestId('invalid-room-Living Room'));
  expect(await screen.findByText('heatDesign.radiatorTable.ExistingRadiators')).toBeVisible();
});

test('Verify the underfloor heating validations', async () => {
  // Check the floor buttons, everything should be valid right now
  let groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');
  expect(groundFloorButton).toBeInTheDocument();
  expect(groundFloorButton.getAttribute('data-valid')).toBe('true');
  const firstFloorButton = screen.getByTestId('floor-overview-button-1st Floor');

  expect(firstFloorButton).toBeInTheDocument();

  // Open room radiator detail modal
  await userEvent.click(screen.getByTestId(`radiator-room-living-room-area`));
  expect(await screen.findByText('heatDesign.radiatorRenderer.AddUnderfloorHeating')).toBeVisible();
  expect(await screen.findByText('heatDesign.radiatorTable.ExistingRadiators')).toBeVisible();

  // By default, underfloor heating in the UK and Italy has water type selected
  expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('-2174 W');

  await userEvent.click(screen.getByTestId('add-ufh-heating'));
  expect(screen.getByTestId('underfloor-heating-type-select')).toHaveTextContent('Water');
  // Try changing it to electric
  await setUnderfloorHeatingType(SystemType.ELECTRIC);
  expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('0 W');

  await setSwitchToValue('match-room-heat-loss-checkbox', false);
  // Add a negative (invalid) value for underfloor heating
  const outputInput = screen.getByTestId('underfloor-heating-nominal-output').querySelector('input');

  expect(outputInput).toBeVisible();
  fireEvent.change(outputInput!, {
    target: { value: '-500' },
  });

  expect(screen.getByTestId('room-heat-balance-label')).toHaveTextContent('-2674 W');
  returnToRadiatorOverview();
  // The "Ground Floor" button should now be invalid
  groundFloorButton = screen.getByTestId('floor-overview-button-Ground Floor');
  expect(groundFloorButton.getAttribute('data-valid')).toBe('false');

  // Clicking the next button should throw up the validation modal
  goToNextHeatDesignStep();
  expect(screen.getByRole('presentation', { name: 'heatDesign.emittersValidationModal.title' })).toBeInTheDocument();

  // And then clicking the problem room should open that room's modal
  await userEvent.click(screen.getByRole('button', { name: '! Living Room' }));

  expect(await screen.findByText('heatDesign.radiatorTable.ExistingRadiators')).toBeVisible();
});

it('Should have an edit button on a newly added radiator', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  await addNewRadiator();

  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  const newRadiatorRow = getNewRadiatorCards()[0];
  const browseButton = await within(newRadiatorRow!).findByTestId('edit-radiator-button');
  expect(browseButton).toBeInTheDocument();
});

it('Should replace newly added radiator with the edit functionality', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 702,
      max: 702,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  const newRadiatorRow = getNewRadiatorCards()[0];
  const oldNewRadiatorComment = within(newRadiatorRow!).getByTestId('radiator-comment');

  expect(within(oldNewRadiatorComment).getByText('Compact', { exact: false })).toBeInTheDocument();
  editRadiator(newRadiatorRow!);
  await setRadiatorTableFiltersToValues({
    height: {
      min: 200,
      max: 300,
    },
    width: {
      min: 600,
      max: 700,
    },
    output: {
      min: 2216,
      max: 2218,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  const newRadiatorRowAfterReplace = getNewRadiatorCards()[0];
  const newRadiatorComment = within(newRadiatorRowAfterReplace!).getByTestId('radiator-comment');
  expect(within(newRadiatorComment).getByText('searchable', { exact: false })).toBeInTheDocument();
});

it('Should replace replacing radiator with the browse functionality', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  const existingRadiatorRows = getExistingRadiatorCards();
  expect(existingRadiatorRows.length).toBeGreaterThan(0);
  const existingRadiatorRow = existingRadiatorRows[0];
  expect(existingRadiatorRow).toBeInTheDocument();
  (await within(existingRadiatorRow!).findByTestId('radiator-row-replace')).click();
  await setRadiatorTableFiltersToValues({
    height: {
      min: 200,
      max: 1900,
    },
    width: {
      min: 600,
      max: 3100,
    },
    output: {
      min: 702,
      max: 702,
    },
  });
  await selectRadiatorFromCatalogue({ index: 0 });
  await saveRadiator();
  let newRadiatorRow = getNewRadiatorCards()[0];
  let comment = within(newRadiatorRow!).getByTestId('radiator-comment');
  expect(within(comment).getByText('type 11', { exact: false })).toBeInTheDocument();
  editRadiator(newRadiatorRow!);
  await setRadiatorTableFiltersToValues({
    height: {
      min: 200,
      max: 1900,
    },
    width: {
      min: 600,
      max: 3100,
    },
    output: {
      min: 351,
      max: 351,
    },
  });
  await selectRadiatorFromCatalogue({ index: 0 });
  await saveRadiator();
  newRadiatorRow = getNewRadiatorCards()[0];
  comment = within(newRadiatorRow!).getByTestId('radiator-comment');
  expect(within(comment).getByText('Compact, type 20', { exact: false })).toBeInTheDocument();
  expect(within(comment).queryByText('Compact, type 11', { exact: false })).not.toBeInTheDocument();
});

test('Should show correct heatbalance in a room when replacing an existing radiator', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  const infoBlock = await screen.findByTestId('room-heat-balance-label');
  expect(infoBlock).toHaveTextContent('-2174 W');
  await setIndividualTemperatureAdjustment(true);
  const existingRadiators = getExistingRadiatorCards();
  expect(existingRadiators.length).toBeGreaterThan(0);
  const existingRadiatorRow = existingRadiators[0];
  expect(existingRadiatorRow).toBeInTheDocument();
  setRadiatorEnabled({
    parentContainer: existingRadiatorRow!,
    isEnabled: true,
  });
  await setRadiatorValues(existingRadiatorRow!, {
    deltaT: 42.5,
  });

  (await within(existingRadiatorRow!).findByTestId('radiator-row-replace')).click();
  await setRadiatorTableFiltersToValues({
    height: {
      min: 200,
      max: 1900,
    },
    width: {
      min: 200,
      max: 3100,
    },
    output: {
      min: 527,
      max: 527,
    },
  });
  const catalogueRow = await screen.findAllByTestId('catalogue-radiator-row');
  expect(catalogueRow.length).toBe(1);
  await selectRadiatorFromCatalogue({ index: 0 });
  await saveRadiator();
  expect(infoBlock).toHaveTextContent('-1647 W');
});

test('Should show correct heatbalance in a room when browsing a new radiator', async () => {
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  const infoBlock = await screen.findByTestId('room-heat-balance-label');
  expect(infoBlock).toBeVisible();

  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 702,
      max: 702,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  expect(infoBlock).toHaveTextContent('-1472 W');

  const newRadiators = getNewRadiatorCards();

  expect(newRadiators.length).toBe(1);
  const newlyAddedNewRadiatorRow = newRadiators[newRadiators.length - 1]!;
  editRadiator(newlyAddedNewRadiatorRow!);

  await setRadiatorTableFiltersToValues({
    height: {
      min: 200,
      max: 1900,
    },
    width: {
      min: 200,
      max: 3100,
    },
    output: {
      min: 351,
      max: 351,
    },
  });
  const catalogueRadiatorRows = await screen.findAllByTestId('catalogue-radiator-row');
  expect(catalogueRadiatorRows.length).toBe(1);
  await selectRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();

  expect(infoBlock).toHaveTextContent('-1823 W');
});

test('It should show the correct house total heat loss', async () => {
  let groundFloorOutput = screen.getByTestId('floor-navigation-wrapper-Ground Floor').querySelector('text');
  let firstFloorOutput = screen.getByTestId('floor-navigation-wrapper-1st Floor').querySelector('text');
  let houseTotalOutput = screen.getByTestId('house-total-output-wrapper').querySelector('text');
  expect(groundFloorOutput).toHaveTextContent('-4808 W');
  expect(firstFloorOutput).toHaveTextContent('-2503 W');
  expect(houseTotalOutput).toHaveTextContent('-7311 W');
  await userEvent.click(screen.getByTestId('radiator-room-living-room-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 351,
      max: 351,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  returnToRadiatorOverview();
  fireEvent.click(screen.getByTestId('floor-overview-button-1st Floor'));
  fireEvent.click(screen.getByTestId('radiator-room-bedroom-3-area'));
  await addNewRadiator();
  await setRadiatorTableFiltersToValues({
    output: {
      min: 637,
      max: 637,
    },
  });
  await chooseRadiatorFromCatalogue({
    index: 0,
  });
  await saveRadiator();
  returnToRadiatorOverview();
  groundFloorOutput = screen.getByTestId('floor-navigation-wrapper-Ground Floor').querySelector('text');
  firstFloorOutput = screen.getByTestId('floor-navigation-wrapper-1st Floor').querySelector('text');
  houseTotalOutput = screen.getByTestId('house-total-output-wrapper').querySelector('text');
  expect(groundFloorOutput).toHaveTextContent('-4457 W');
  expect(firstFloorOutput).toHaveTextContent('-1866 W');
  expect(houseTotalOutput).toHaveTextContent('-6323 W');
});
