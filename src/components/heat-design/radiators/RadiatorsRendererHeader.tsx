import { Box, IconButton, Popover, Stack, Typography, useMediaQuery } from '@mui/material';
import { green, grey, red, surface } from '@ui/theme/colors';
import { Switch } from '@ui/components/Switch/Switch';
import { FormattedMessage, useIntl } from 'react-intl';
import { useGroundwork } from '../../../context/groundwork-context';
import { OutputType } from '../stores/types';
import { getDesignRoomTemp } from '../utils/heatCalculations';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { ArrowLeftOutlinedIcon } from '@ui/components/StandardIcons/ArrowLeftOutlinedIcon';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { Button } from '@ui/components/Button/Button';
import { CalendarViewWeekOutlined } from '@ui/components/Icons/material';
import { UnderfloorHeatingIcon } from '@ui/components/StandardIcons/UnderfloorHeatingIcon';
import { marketConfiguration } from '../utils/marketConfigurations';
import { useRoomsStore } from '../stores/RoomsStore';
import { useCallback, useState } from 'react';
import { AddSquareOutlinedIcon } from '@ui/components/StandardIcons/AddSquareOutlinedIcon';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { createEmptyRadiatorData } from './utils';
import { useRadiatorContext } from './contexts/RadiatorContext';
import { useHeatDesignUIActions } from '../stores/HeatDesignUIStore';
import { useSelectedRoom } from './hooks/useSelectedRoom';
import isNil from 'lodash/isNil';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { StyledFormattedMessage } from '../../../utils/localization';

export default function RadiatorsRendererHeader() {
  const selectedRoom = useSelectedRoom();
  const { selectRoom } = useHeatDesignUIActions();
  const constructionYear = useConstructionYear();
  const { countryCode } = useGroundwork();
  const { formatMessage } = useIntl();
  const { setNewRadiator, setNewExistingRadiator, startRadiatorEdit } = useRadiatorContext();
  const climateDataStore = useClimateDataStore();
  const roomTemperature = selectedRoom
    ? getDesignRoomTemp(selectedRoom, constructionYear, countryCode, climateDataStore)
    : undefined;
  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const roomHeatDesignResult = roomHeatDesignResults.find((ro) => ro.roomId === selectedRoom?.id);
  const [isAddRadiatorPopoverVisible, setIsAddRadiatorPopoverVisible] = useState(false);
  const [addRadiatorButtonElement, setAddRadiatorButtonElement] = useState<HTMLDivElement | undefined>(undefined);
  const totalRoomHeatLoss = roomHeatDesignResult?.totalRoom.heatLoss ?? 0;
  const totalRoomHeatOutput = roomHeatDesignResult?.totalOutputOfHeatEmittersWatt ?? 0;

  const wattDifference = totalRoomHeatOutput - totalRoomHeatLoss;
  const updateRoomById = useRoomsStore((s) => s.actions.updateRoomById);

  const onAddRadiatorButtonRefChange = useCallback(
    (node: HTMLDivElement) => {
      setAddRadiatorButtonElement(node);
    },
    [setAddRadiatorButtonElement],
  );

  const handleAddUnderfloorHeating = () => {
    if (!selectedRoom) {
      return;
    }
    updateRoomById(selectedRoom.id, {
      underfloorHeating: {
        outputType: OutputType.AUTOMATIC,
        systemType: marketConfiguration[countryCode].defaultUnderfloorHeatingType,
      },
    });
  };

  const toggleRadiatorDeltaTAdjustment = () => {
    if (!selectedRoom) {
      return;
    }
    updateRoomById(selectedRoom.id, { enableRadiatorDeltaTAdjustment: !selectedRoom.enableRadiatorDeltaTAdjustment });
  };
  const addExistingRadiator = () => {
    if (!selectedRoom) {
      return;
    }
    const radiator = createEmptyRadiatorData({
      roomId: selectedRoom.id,
      countryCode: countryCode,
      overrides: {
        isExisting: true,
      },
    });
    setNewExistingRadiator(radiator);
  };
  const addNewRadiator = () => {
    if (!selectedRoom) {
      return;
    }
    const emptyRadiatorData = createEmptyRadiatorData({
      roomId: selectedRoom.id,
      countryCode: countryCode,
    });
    setNewRadiator(emptyRadiatorData);
    startRadiatorEdit(emptyRadiatorData.uid);
  };
  const isMobile = useMediaQuery('(max-width: 1025px)');

  if (!selectedRoom || isNil(roomTemperature)) {
    return null;
  }
  return (
    <Stack gap={4} sx={{ width: '100%', position: 'sticky' }}>
      <Stack
        sx={{ width: '100%', height: isMobile ? 'auto' : '30px' }}
        alignItems={isMobile ? 'flex-start' : 'center'}
        direction={isMobile ? 'column' : 'row'}
        justifyContent="space-between"
        gap={2}
      >
        <Stack direction="row" alignItems="center" gap={1}>
          <IconButton data-testid="back-to-floor-overview-button" onClick={() => selectRoom(undefined)}>
            <ArrowLeftOutlinedIcon height={30} width={30} />
          </IconButton>

          <Typography
            sx={{ cursor: 'pointer' }}
            onClick={() => selectRoom(undefined)}
            color={grey[500]}
            variant="headline2"
          >
            <FormattedMessage id="heatDesign.radiatorsOverview.floorOverview.title" />
          </Typography>
          <Chevron direction="right" height={24} width={24} />
          <Typography variant="headline2">{selectedRoom?.name}</Typography>
        </Stack>
        <Stack direction="row" gap={4}>
          <Stack direction="row" alignItems="center" gap={1}>
            <FormattedMessage id="heatDesign.radiatorRenderer.RoomTemp" />
            <Box
              sx={{
                boxSizing: 'content-box',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '8px',
                padding: '4px 16px',
                backgroundColor: grey[200],
              }}
            >
              <Typography data-testid="room-temperature-label" color={grey[900]} variant="number2" fontWeight={500}>
                {(roomTemperature ?? 0).toFixed(0)} ⁰C
              </Typography>
            </Box>
          </Stack>
          <Stack direction="row" alignItems="center" gap={1}>
            <FormattedMessage id="heatDesign.radiatorModal.heatBalance.title" />
            <TooltipAira title={formatMessage({ id: 'heatDesign.radiatorModal.heatBalance.tooltip' })} />
            <Box
              sx={{
                boxSizing: 'content-box',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '8px',
                padding: '4px 16px',
                backgroundColor: wattDifference < 0 ? red[200] : green[200],
              }}
            >
              <Typography
                data-testid="room-heat-balance-label"
                color={wattDifference < 0 ? red[600] : green[700]}
                variant="number2"
                fontWeight={500}
              >
                {wattDifference.toFixed(0)} W
              </Typography>
            </Box>
          </Stack>
        </Stack>
      </Stack>
      <Stack sx={{ width: '100%' }} direction="row" alignItems="center" justifyContent="space-between">
        <Stack direction="row" gap={2}>
          <Box ref={onAddRadiatorButtonRefChange}>
            <Button
              variant="outlined"
              startIcon={<CalendarViewWeekOutlined />}
              onClick={() => setIsAddRadiatorPopoverVisible(!isAddRadiatorPopoverVisible)}
              sx={{
                borderRadius: '16px',
                height: '40px',
                padding: '8px 12px',
                backgroundColor: surface[100],
                borderColor: 'transparent',
              }}
              data-testid="heat-design-add-radiator-button"
            >
              <Typography noWrap={true} variant="body1" fontWeight={500} sx={{ textWrap: 'nowrap' }}>
                <FormattedMessage id="heatDesign.radiatorRenderer.AddRadiator" />
              </Typography>
            </Button>
          </Box>

          <Button
            variant="outlined"
            onClick={handleAddUnderfloorHeating}
            sx={{
              borderRadius: '16px',
              height: '40px',
              padding: '8px 12px',
              backgroundColor: surface[100],
              borderColor: 'transparent',
            }}
            startIcon={<UnderfloorHeatingIcon />}
            data-testid="add-ufh-heating"
          >
            <Typography noWrap={true} variant="body1" fontWeight={500} sx={{ textWrap: 'nowrap' }}>
              <FormattedMessage id="heatDesign.radiatorRenderer.AddUnderfloorHeating" />
            </Typography>
          </Button>
        </Stack>
        <Stack
          direction="row"
          alignItems="center"
          style={{
            borderRadius: 12,
            paddingRight: 12,
          }}
          gap={2}
        >
          <Switch
            data-testid="individual-temperature-adjustment-switch"
            id="enableEmitterTemperatureAdjustment"
            checked={selectedRoom.enableRadiatorDeltaTAdjustment ?? false}
            onChange={toggleRadiatorDeltaTAdjustment}
            label={
              <Typography
                component="label"
                style={{
                  padding: '8px 0',
                  cursor: 'pointer',
                }}
                variant="body1"
                htmlFor="enableEmitterTemperatureAdjustment"
              >
                <FormattedMessage id="heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.label" />
              </Typography>
            }
          />

          <TooltipAira
            title={<StyledFormattedMessage id="heatDesign.radiatorModal.enableEmitterTemperatureAdjustment.tooltip" />}
          />
        </Stack>
      </Stack>
      <Popover
        onClose={() => setIsAddRadiatorPopoverVisible(false)}
        open={isAddRadiatorPopoverVisible}
        anchorEl={addRadiatorButtonElement}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Stack sx={{ padding: '16px', borderRadius: '16px' }} gap={2}>
          <Stack direction="row" gap={1} alignItems="center">
            <CalendarViewWeekOutlined />
            <Typography noWrap={true} variant="body1" fontWeight={500} sx={{ textWrap: 'nowrap' }}>
              <FormattedMessage id="heatDesign.radiatorRenderer.AddRadiator" />
            </Typography>
          </Stack>

          <Typography variant="body3">
            <FormattedMessage id="heatDesign.radiatorRenderer.AddRadiatorDetail" />
          </Typography>
          <Stack direction="row" gap={1} justifyContent="space-between">
            <Button
              data-testid="confirm-add-radiator-button"
              onClick={() => {
                addNewRadiator();
                setIsAddRadiatorPopoverVisible(false);
              }}
              sx={{ padding: '16px', borderRadius: '16px', height: '40px' }}
            >
              <Stack direction="row" gap={1} alignItems="center">
                <AddSquareOutlinedIcon />
                <Typography fontWeight={500} color="#fff">
                  <FormattedMessage id="heatDesign.radiatorTable.addNew" />
                </Typography>
              </Stack>
            </Button>
            <Button
              data-testid="confirm-add-existing-radiator-button"
              sx={{ padding: '16px', borderRadius: '16px', height: '40px' }}
              onClick={() => {
                addExistingRadiator();
                setIsAddRadiatorPopoverVisible(false);
              }}
            >
              <Stack direction="row" gap={1} alignItems="center">
                <AddOutlinedIcon />
                <Typography fontWeight={500} color="#fff">
                  <FormattedMessage id="heatDesign.radiatorTable.addExisting" />
                </Typography>
              </Stack>
            </Button>
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}
