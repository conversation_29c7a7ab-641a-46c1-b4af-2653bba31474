import { Stack } from '@mui/material';
import { useIntl } from 'react-intl';
import { useCallback, useMemo } from 'react';
import { useDwellingHeatDesignResult } from '../hooks/useDwellingHeatDesignResult';
import { Accordion } from '@ui/components/Accordion/Accordion';
import { useSelectedRoom } from './hooks/useSelectedRoom';
import { UnderfloorHeatingCard } from './underfloor-heating-card/UnderfloorHeatingCard';
import { useRadiatorContext } from './contexts/RadiatorContext';
import { EmitterDetails, RadiatorData, SystemType } from '../stores/types';
import { getCatalogueRadiatorProperties } from './utils';
import {
  PanelRadiatorType,
  RadiatorMaterial,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/radiator/v1/model';
import { marketConfiguration } from '../utils/marketConfigurations';
import { useGroundwork } from '../../../context/groundwork-context';
import { RadiatorCardWrapper } from './radiator-card/RadiatorCardWrapper';

export default function RoomRadiatorOverview() {
  const selectedRoom = useSelectedRoom();
  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const { formatMessage } = useIntl();
  const { countryCode } = useGroundwork();
  const { newRadiator, editedRadiators, newExistingRadiator } = useRadiatorContext();
  const { catalogueRadiatorMap } = useRadiatorContext();
  const selectedRoomHeatDesignResult = roomHeatDesignResults.find((r) => r.roomId === selectedRoom?.id);

  // Whenever a new radiator is added, we want to scroll to it
  const onNewCardChange = useCallback((node: HTMLDivElement | null) => {
    if (node !== null) {
      node.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);
  const combineWithCatalogueRadiatorData = useCallback(
    (radiatorData: RadiatorData) => {
      if (radiatorData.isExisting) {
        const radiatorDetails: EmitterDetails =
          radiatorData.radiatorDetails.systemType === SystemType.WATER
            ? {
                ...radiatorData.radiatorDetails,
                nominalOutput: {
                  outputWatt: radiatorData.radiatorDetails.nominalOutput?.outputWatt ?? 0,
                  deltaT:
                    radiatorData.radiatorDetails.nominalOutput?.deltaT ??
                    marketConfiguration[countryCode].radiatorDeltaT,
                },
              }
            : {
                ...radiatorData.radiatorDetails,
              };
        return {
          ...radiatorData,
          radiatorDetails,
          material: RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED,
          numberOfColumns: undefined,
          panelType: radiatorData.panelType ?? PanelRadiatorType.UNRECOGNIZED,
        };
      } else {
        const catalogueRadiator = radiatorData?.specificationReferenceId
          ? catalogueRadiatorMap[radiatorData?.specificationReferenceId]
          : undefined;
        return catalogueRadiator
          ? {
              ...radiatorData,
              ...getCatalogueRadiatorProperties(catalogueRadiator),
            }
          : {
              ...radiatorData,
              material: RadiatorMaterial.RADIATOR_MATERIAL_UNSPECIFIED,
              numberOfColumns: undefined,
              panelType: radiatorData.panelType ?? PanelRadiatorType.PANEL_RADIATOR_TYPE_UNSPECIFIED,
            };
      }
    },
    [catalogueRadiatorMap, countryCode],
  );
  const newRadiators = useMemo(() => {
    return selectedRoom?.radiators.filter((r) => !r.isExisting).map(combineWithCatalogueRadiatorData) ?? [];
  }, [combineWithCatalogueRadiatorData, selectedRoom?.radiators]);
  if (!selectedRoom || !selectedRoomHeatDesignResult) return null;

  const existingRadiators =
    selectedRoom?.radiators.filter((r) => r.isExisting).map(combineWithCatalogueRadiatorData) ?? [];

  return (
    <Stack gap={4} sx={{ width: '100%' }}>
      {(existingRadiators.length > 0 || newExistingRadiator || selectedRoom.underfloorHeating) && (
        <Accordion
          sx={{ width: '100%', backgroundColor: '#fff' }}
          header={formatMessage({ id: 'heatDesign.radiatorTable.ExistingRadiators' })}
        >
          <Stack gap={2}>
            {existingRadiators.map((radiator) => {
              return (
                <Stack id={radiator.uid} key={radiator.uid} data-testid={`existing-radiator-card-${radiator.uid}`}>
                  <RadiatorCardWrapper isEditing={!!editedRadiators[radiator.uid]} radiator={radiator} />
                </Stack>
              );
            })}
            {newExistingRadiator && (
              <Stack
                data-testid="existing-radiator-card-creating"
                sx={{ width: '100%', scrollMarginTop: '15vh' }}
                ref={onNewCardChange}
              >
                <RadiatorCardWrapper radiator={newExistingRadiator} isEditing={true} />
              </Stack>
            )}
            {selectedRoom.underfloorHeating && (
              <UnderfloorHeatingCard underfloorHeating={selectedRoom.underfloorHeating} />
            )}
          </Stack>
        </Accordion>
      )}

      {(newRadiators?.length > 0 || newRadiator) && (
        <Accordion
          sx={{ width: '100%', backgroundColor: '#fff' }}
          header={formatMessage({ id: 'heatDesign.newRadiator.NewRadiators' })}
        >
          <Stack gap={2}>
            {newRadiators.map((radiator) => {
              return (
                <Stack id={radiator.uid} key={radiator.uid} data-testid="new-radiator-card">
                  <RadiatorCardWrapper isEditing={!!editedRadiators[radiator.uid]} radiator={radiator} />
                </Stack>
              );
            })}
            {newRadiator && (
              <Stack
                data-testid="new-radiator-card-creating"
                sx={{ width: '100%', scrollMarginTop: '15vh' }}
                ref={onNewCardChange}
              >
                <RadiatorCardWrapper radiator={newRadiator} isEditing={true} />
              </Stack>
            )}
          </Stack>
        </Accordion>
      )}
    </Stack>
  );
}
