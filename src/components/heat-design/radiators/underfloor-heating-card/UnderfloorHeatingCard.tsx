import { memo, useCallback, useState } from 'react';
import { CustomUnderfloorHeatingOutput, OutputType, SystemType, UnderfloorHeatingOutput } from '../../stores/types';
import { MenuItem } from '@mui/material';
import { grey, surface } from '@ui/theme/colors';
import { useSelectedRoom } from '../hooks/useSelectedRoom';
import { useRoomsStore } from '../../stores/RoomsStore';
import { UNDERFLOOR_HEATING_MEAN_DELTA_T } from '../../constants';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { propertyIsValid } from '../../Validator';
import { FormattedMessage, useIntl } from 'react-intl';
import { useRoomOutputs } from '../hooks/useRoomOutputs';
import { useDwellingHeatDesignResult } from '../../hooks/useDwellingHeatDesignResult';
import { Select } from '@ui/components/Select/Select';
import { ConfirmationPopover } from '../../../bill-of-materials/components/common/ConfirmationPopover';
import Box from '@mui/material/Box';
import { Switch } from '@ui/components/Switch/Switch';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';

export type UnderfloorHeatingCardProps = {
  underfloorHeating: UnderfloorHeatingOutput;
};

function UnderfloorHeatingCardComponent({ underfloorHeating }: UnderfloorHeatingCardProps) {
  const { formatMessage } = useIntl();
  const [editableUnderfloorHeating, setEditableUnderfloorHeating] =
    useState<UnderfloorHeatingOutput>(underfloorHeating);
  const selectedRoom = useSelectedRoom();
  const updateRoomById = useRoomsStore((s) => s.actions.updateRoomById);
  const roomOutputs = useRoomOutputs();
  const { floorsResults } = useDwellingHeatDesignResult();
  const roomHeatDesignResults = floorsResults.flatMap((floor) => floor.roomsResults);
  const roomHeatDesignResult = roomHeatDesignResults.find((ro) => ro.roomId === selectedRoom?.id);
  const totalRoomHeatLoss = roomHeatDesignResult?.totalRoom.heatLoss ?? 0;
  const underfloorAutomaticOutput = Math.max(0, totalRoomHeatLoss - roomOutputs.radiators);
  const [deleteUnderfloorHeatingButton, setDeleteUnderfloorHeatingButton] = useState<HTMLDivElement | null>(null);
  const [isDeletePopoverVisible, setIsDeletePopoverVisible] = useState(false);

  const onDeleteUnderfloorHeatingButtonRefChange = useCallback(
    (node: HTMLDivElement) => {
      setDeleteUnderfloorHeatingButton(node);
    },
    [setDeleteUnderfloorHeatingButton],
  );
  const mapToCustomUnderfloorHeatingOutput = (
    outputWatt: number,
    systemType?: SystemType,
  ): CustomUnderfloorHeatingOutput => {
    switch (systemType) {
      case SystemType.ELECTRIC:
        return {
          outputType: OutputType.CUSTOM,
          systemType,
          outputWatt,
        };

      case SystemType.WATER:
        return {
          outputType: OutputType.CUSTOM,
          systemType,
          nominalOutput: {
            deltaT: UNDERFLOOR_HEATING_MEAN_DELTA_T,
            outputWatt,
          },
        };
      default:
        // If we don't know the system type, we can't know where to store the output exactly,
        // So we store the output in both places
        return {
          outputType: OutputType.CUSTOM,
          nominalOutput: {
            deltaT: UNDERFLOOR_HEATING_MEAN_DELTA_T,
            outputWatt,
          },
          outputWatt,
        };
    }
  };
  const save = (newUnderfloorHeating: UnderfloorHeatingOutput) => {
    if (!selectedRoom) {
      return;
    }
    updateRoomById(selectedRoom.id, {
      underfloorHeating: newUnderfloorHeating,
    });
  };

  const onTypeUpdate = (type: SystemType) => {
    if (!selectedRoom) {
      return;
    }
    let newUnderfloorHeating = editableUnderfloorHeating;
    if (newUnderfloorHeating.outputType === OutputType.CUSTOM) {
      newUnderfloorHeating = {
        ...newUnderfloorHeating,
        ...mapToCustomUnderfloorHeatingOutput(getOutputs(newUnderfloorHeating).output, type),
        systemType: type,
      };
    } else {
      newUnderfloorHeating = {
        ...newUnderfloorHeating,
        systemType: type,
      };
    }

    setEditableUnderfloorHeating(newUnderfloorHeating);
    save(newUnderfloorHeating);
  };

  const toggleMatchRoomHeatLoss = () => {
    if (!selectedRoom) {
      return;
    }
    let newUnderfloorHeating: UnderfloorHeatingOutput;
    switch (editableUnderfloorHeating.outputType) {
      case OutputType.CUSTOM:
        newUnderfloorHeating = {
          ...editableUnderfloorHeating,
          outputType: OutputType.AUTOMATIC,
        };
        break;
      case OutputType.AUTOMATIC:
        newUnderfloorHeating = {
          ...editableUnderfloorHeating,
          ...mapToCustomUnderfloorHeatingOutput(0, editableUnderfloorHeating.systemType),
        };
        break;
    }
    setEditableUnderfloorHeating(newUnderfloorHeating);
    save(newUnderfloorHeating);
  };

  const handleUpdateUnderfloorHeatingOutput = (newValue: number) => {
    if (!editableUnderfloorHeating || editableUnderfloorHeating.outputType === OutputType.AUTOMATIC || !selectedRoom) {
      return;
    }
    const newUnderfloorHeating = {
      ...editableUnderfloorHeating,
      ...mapToCustomUnderfloorHeatingOutput(newValue, editableUnderfloorHeating.systemType),
    };
    save(newUnderfloorHeating);
    setEditableUnderfloorHeating(newUnderfloorHeating);
  };

  const isCustom = editableUnderfloorHeating.outputType !== OutputType.AUTOMATIC;
  const getOutputs = (underfloorHeating: UnderfloorHeatingOutput) => {
    if (underfloorHeating.outputType === OutputType.CUSTOM) {
      if (underfloorHeating.systemType === SystemType.WATER) {
        return {
          output: underfloorHeating.nominalOutput?.outputWatt ?? 0,
          deltaT: underfloorHeating.nominalOutput?.deltaT ?? 0,
        };
      } else if (underfloorHeating.systemType === SystemType.ELECTRIC) {
        return {
          output: underfloorHeating.outputWatt ?? 0,
          deltaT: 0,
        };
      } else {
        return {
          output: 0,
          deltaT: 0,
        };
      }
    } else {
      return {
        output: underfloorAutomaticOutput,
        deltaT: 0,
      };
    }
  };

  const deleteUnderfloorHeating = () => {
    if (!selectedRoom) {
      return;
    }
    updateRoomById(selectedRoom.id, {
      underfloorHeating: undefined,
    });
  };

  const isOutputInvalid =
    editableUnderfloorHeating.outputType !== OutputType.AUTOMATIC &&
    !propertyIsValid('underfloorHeating', 'outputWatt', getOutputs(editableUnderfloorHeating).output);

  return (
    <Stack
      sx={{
        borderRadius: '16px',
        padding: '16px',
        boxShadow: 'none',
        transition: 'background-color 0.2s, box-shadow 0.2s',
        backgroundColor: surface[100],
      }}
      gap={2}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{ width: '100%', paddingRight: '16px' }}
      >
        <Stack gap={2} sx={{ flex: '1' }}>
          <Stack direction="row" justifyContent="space-between" sx={{ height: '32px' }}>
            <Stack direction="row" gap={3}>
              <Box
                sx={{
                  backgroundColor: grey[700],
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body2" color="#fff">
                  U1
                </Typography>
              </Box>
            </Stack>
            <Stack ref={onDeleteUnderfloorHeatingButtonRefChange} direction="row" alignItems="center" gap={1}>
              <IconButton
                onClick={() => setIsDeletePopoverVisible(true)}
                sx={{ border: `1px solid ${grey[900]}`, height: '32px', width: '32px', padding: 0 }}
              >
                <BinOutlinedIcon height={20} width={20} color={grey[900]} />
              </IconButton>
              <Typography variant="body2">
                <FormattedMessage id="common.label.delete" />
              </Typography>
            </Stack>
          </Stack>

          <Stack
            direction="row"
            alignItems="center"
            gap={3}
            sx={{ position: 'relative', width: '100%' }}
            justifyContent="space-between"
          >
            <Stack
              flexWrap="wrap"
              direction="row"
              alignItems="start"
              gap={3}
              sx={{ padding: '10px 0', height: '100%' }}
            >
              <LabelValueDisplay label="Category" orientation="vertical" value="Underfloor heating" />
              <Select
                label={formatMessage({ id: 'common.label.type' })}
                size="small"
                name="select-rad-mode"
                onChange={(event) => {
                  onTypeUpdate(event.target.value as SystemType);
                }}
                value={editableUnderfloorHeating.systemType}
                data-testid="underfloor-heating-type-select"
                sx={{
                  display: 'flex',
                  gap: 1,
                  minWidth: '150px',
                  width: 'auto',
                  '.MuiInputLabel-root': {
                    margin: 0,
                  },
                  '.MuiInputBase-root': {
                    minWidth: '100px',
                    margin: '0 !important',
                  },
                }}
                error={!propertyIsValid('underfloorHeating', 'systemType', editableUnderfloorHeating.systemType)}
              >
                <MenuItem value={SystemType.WATER}>Water</MenuItem>
                <MenuItem value={SystemType.ELECTRIC}>Electric</MenuItem>
              </Select>
            </Stack>
            <Stack alignItems="center" direction="row" gap={3} sx={{ height: '100%', flex: '0', padding: '10px 0' }}>
              <Switch
                sx={{ width: '250px', marginTop: '20px' }}
                checked={editableUnderfloorHeating.outputType === OutputType.AUTOMATIC}
                onChange={toggleMatchRoomHeatLoss}
                data-testid="match-room-heat-loss-checkbox"
                label={
                  <Typography variant="body2">
                    <FormattedMessage id="heatDesign.radiatorTable.underfloorHeating.matchRoomHeatLossLabel" />
                  </Typography>
                }
              />
              {isCustom ? (
                <NumericTextField
                  name="catalogue-radiator-nominal-output"
                  type="number"
                  size="small"
                  orientation="vertical"
                  data-testid="underfloor-heating-nominal-output"
                  label={
                    <Typography variant="body2Emphasis" sx={{ textWrap: 'nowrap' }}>
                      {formatMessage({ id: 'heatDesign.radiatorModal.table.nominalOutput.generic' })}
                    </Typography>
                  }
                  value={Math.round(getOutputs(editableUnderfloorHeating).output) || 0}
                  onChange={(event) => {
                    handleUpdateUnderfloorHeatingOutput(event);
                  }}
                  sx={{ width: 150 }}
                  error={isOutputInvalid}
                />
              ) : (
                <LabelValueDisplay
                  orientation="vertical"
                  hasError={isOutputInvalid}
                  sx={{ width: '150px' }}
                  label={formatMessage({ id: 'heatDesign.radiatorTable.calculated.output' })}
                  value={Math.round(getOutputs(editableUnderfloorHeating).output)}
                />
              )}
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <ConfirmationPopover
        anchorEl={deleteUnderfloorHeatingButton}
        open={isDeletePopoverVisible}
        onClose={() => setIsDeletePopoverVisible(false)}
        title={formatMessage({
          id: 'heatDesign.deleteUnderfloorHeating.title',
        })}
        description={formatMessage({
          id: 'heatDesign.deleteUnderfloorHeating.detail',
        })}
        confirmText={formatMessage({ id: 'common.label.delete' })}
        onConfirm={() => {
          deleteUnderfloorHeating();
          setIsDeletePopoverVisible(false);
        }}
        confirmIcon={<BinOutlinedIcon width={20} height={20} />}
      />
    </Stack>
  );
}

export const UnderfloorHeatingCard = memo(UnderfloorHeatingCardComponent);
