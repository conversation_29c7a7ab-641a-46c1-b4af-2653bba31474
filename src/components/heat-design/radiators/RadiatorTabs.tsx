import { Stack } from '@mui/material';
import { FloorIcon } from '@ui/components/StandardIcons/FloorIcon';
import { ListIcon } from '@ui/components/StandardIcons/ListIcon';
import { TabButton, TabButtonsWrapper } from '@ui/components/Tabs/Tabs';
import { FormattedMessage } from 'react-intl';
import { floorHeatSourcesAreValid } from '../Validator';
import { WarningIcon } from '../components/WarningIcon';
import { useRooms } from '../stores/RoomsStore';

export enum RadiatorTab {
  FloorOverview,
  EmitterList,
}

type Props = {
  selectedTab: RadiatorTab;
  onChangeTab: (tab: RadiatorTab) => void;
};

export default function RadiatorTabs({ selectedTab, onChangeTab }: Props) {
  const rooms = useRooms();
  const showFloorOverviewWarning = !floorHeatSourcesAreValid(rooms);

  return (
    <TabButtonsWrapper>
      <TabButton
        data-testid="floor-overview-tab"
        isSelected={selectedTab === RadiatorTab.FloorOverview}
        onClick={() => onChangeTab(RadiatorTab.FloorOverview)}
      >
        <Stack direction="row" gap={1} alignItems="center">
          {showFloorOverviewWarning && (
            <WarningIcon x={2.5} y={2.5} iconWidth={25} iconHeight={25} canvasWidth={25} canvasHeight={25} />
          )}
          <FloorIcon />
          <span>
            <FormattedMessage id="heatDesign.radiatorsOverview.floorOverview.title" />
          </span>
        </Stack>
      </TabButton>
      <TabButton
        isSelected={selectedTab === RadiatorTab.EmitterList}
        onClick={() => onChangeTab(RadiatorTab.EmitterList)}
        data-testid="emitter-list-tab"
      >
        <Stack direction="row" gap={1} alignItems="center">
          <ListIcon />
          <FormattedMessage id="heatDesign.radiatorsOverview.emitterList.title" />
        </Stack>
      </TabButton>
    </TabButtonsWrapper>
  );
}
