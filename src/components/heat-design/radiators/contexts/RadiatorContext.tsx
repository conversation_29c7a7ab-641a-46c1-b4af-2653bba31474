import { createContext, FC, ReactNode, useContext, useMemo, useState } from 'react';
import { RadiatorDataWithCatalogueProperties } from '../../stores/types';
import { CatalogueRadiator } from '../types';
import { getDesignRoomTemp } from '../../utils/heatCalculations';
import { calculateReturnAndMeanWaterTemperature, getOutputAtDeltaT } from '../../utils/radiatorHelpers';
import { useConstructionYear } from '../../stores/HouseInputsStore';
import { useClimateDataStore } from '../../stores/ClimateDataStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../../stores/HeatSourceStore';
import { useRadiatorsForCountry } from '../../hooks/useRadiators';
import { useGroundwork } from '../../../../context/groundwork-context';
import { useSelectedRoom } from '../hooks/useSelectedRoom';

const RadiatorContext = createContext<
  | {
      areCatalogueRadiatorsPending: boolean;
      catalogueRadiators: CatalogueRadiator[] | undefined;
      catalogueRadiatorMap: Record<string, CatalogueRadiator>;
      newRadiator?: RadiatorDataWithCatalogueProperties;
      newExistingRadiator?: RadiatorDataWithCatalogueProperties;
      setNewRadiator: (value: RadiatorDataWithCatalogueProperties | undefined) => void;
      setNewExistingRadiator: (value: RadiatorDataWithCatalogueProperties | undefined) => void;
      editedRadiators: Record<string, { radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties }>;
      startRadiatorEdit: (
        editedRadiatorId: string,
        radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties,
      ) => void;
      stopRadiatorEdit: (editedRadiatorId: string) => void;
    }
  | undefined
>(undefined);
const calculateOutput = (
  nominalOutputWatt: number,
  deltaTCelsius: number,
  meanWaterTemperature: number,
  roomTemperature: number,
) => {
  if (!roomTemperature) return undefined;

  return getOutputAtDeltaT({
    manufacturersDeltaT: deltaTCelsius || 0,
    outputAtManufacturersDeltaT: nominalOutputWatt || 0,
    meanWaterTemp: meanWaterTemperature,
    designRoomTemp: roomTemperature,
  });
};

export const useRadiatorContext = () => {
  const context = useContext(RadiatorContext);
  if (!context) {
    throw new Error('useRadiatorContext must be used within a RadiatorContextProvider');
  }
  return context;
};

export const RadiatorContextProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [newRadiator, setNewRadiator] = useState<RadiatorDataWithCatalogueProperties | undefined>(undefined);
  const [newExistingRadiator, setNewExistingRadiator] = useState<RadiatorDataWithCatalogueProperties | undefined>(
    undefined,
  );
  const { countryCode } = useGroundwork();

  const { data, isPending } = useRadiatorsForCountry(countryCode);
  const flowTemperature = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const constructionYear = useConstructionYear();
  const climateDataStore = useClimateDataStore();
  const [editedRadiators, setEditedRadiators] = useState<
    Record<string, { radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties }>
  >({});
  const selectedRoom = useSelectedRoom();

  if (!selectedRoom) {
    throw new Error('No room selected');
  }
  const roomTemperature = useMemo(
    () => getDesignRoomTemp(selectedRoom, constructionYear, countryCode, climateDataStore),
    [selectedRoom, constructionYear, countryCode, climateDataStore],
  );

  const meanWaterTemperature = calculateReturnAndMeanWaterTemperature({
    flowTemperature,
    flowReturnDeltaT,
  }).meanWaterTemp;

  const radiatorsWithOutput = useMemo<CatalogueRadiator[] | undefined>(
    () =>
      data?.radiators?.map((radiator) => ({
        ...radiator,
        calculatedOutput: calculateOutput(
          radiator.nominalOutputWatt,
          radiator.deltaTCelsius,
          meanWaterTemperature,
          roomTemperature,
        ),
      })) || undefined,
    [meanWaterTemperature, roomTemperature, data?.radiators],
  );

  const catalogueRadiatorMap = useMemo(() => {
    return (
      radiatorsWithOutput?.reduce((acc: Record<string, CatalogueRadiator>, radiator) => {
        if (radiator.radiatorId?.value) {
          acc[radiator.radiatorId.value] = radiator;
        }
        return acc;
      }, {}) ?? {}
    );
  }, [radiatorsWithOutput]);
  const startRadiatorEdit = (editedRadiatorId: string, radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties) => {
    setEditedRadiators((prev) => ({
      ...prev,
      [editedRadiatorId]: { radiatorBeingReplaced: radiatorBeingReplaced },
    }));
  };
  const stopRadiatorEdit = (editedRadiatorId: string) => {
    setEditedRadiators((prev) => {
      const updatedSet = { ...prev };
      delete updatedSet[editedRadiatorId];
      return updatedSet;
    });
  };

  return (
    <RadiatorContext.Provider
      value={{
        areCatalogueRadiatorsPending: isPending,
        catalogueRadiators: radiatorsWithOutput,
        catalogueRadiatorMap,
        newRadiator,
        newExistingRadiator,
        setNewRadiator,
        setNewExistingRadiator,
        editedRadiators,
        startRadiatorEdit,
        stopRadiatorEdit,
      }}
    >
      {children}
    </RadiatorContext.Provider>
  );
};
