import { CatalogueRadiator } from '../types';
import { createContext, Dispatch, FC, ReactNode, SetStateAction, useContext, useState } from 'react';

type RadiatorCardContext = {
  selectedCatalogueRadiator: CatalogueRadiator | undefined;
  setSelectedCatalogueRadiator: Dispatch<SetStateAction<CatalogueRadiator | undefined>>;
  isCustom: boolean;
  setIsCustom: Dispatch<SetStateAction<boolean>>;
};
const RadiatorCardContext = createContext<RadiatorCardContext>({
  selectedCatalogueRadiator: undefined,
  setSelectedCatalogueRadiator: () => {},
  isCustom: false,
  setIsCustom: () => {},
});

export const useRadiatorCardContext = () => {
  const context = useContext(RadiatorCardContext);
  if (!context) {
    throw new Error('useRadiatorCardContext must be used within a RadiatorCardContextProvider');
  }
  return context;
};

export const RadiatorCardContextProvider: FC<{ children: ReactNode; isInitialCustom?: boolean }> = ({ children }) => {
  const [selectedCatalogueRadiator, setSelectedCatalogueRadiator] = useState<CatalogueRadiator | undefined>(undefined);
  const [isCustom, setIsCustom] = useState<boolean>(false);
  return (
    <RadiatorCardContext.Provider
      value={{
        selectedCatalogueRadiator,
        setSelectedCatalogueRadiator,
        isCustom,
        setIsCustom,
      }}
    >
      {children}
    </RadiatorCardContext.Provider>
  );
};
