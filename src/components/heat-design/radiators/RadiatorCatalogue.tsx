import { CircularProgress, Stack } from '@mui/material';
import { memo, useEffect, useState } from 'react';
import { RadiatorDataWithCatalogueProperties, RadiatorMode } from '../stores/types';
import { RadiatorFilter } from './RadiatorFilter';
import RadiatorCatalogueTable from './RadiatorCatalogueTable';
import { CatalogueRadiator } from './types';
import isNil from 'lodash/isNil';
import { useRadiatorContext } from './contexts/RadiatorContext';

export type RadiatorCatalougeProps = {
  editedRadiator: RadiatorDataWithCatalogueProperties;
  radiatorBeingReplaced?: RadiatorDataWithCatalogueProperties;
};

function RadiatorCatalogueComponent({ editedRadiator, radiatorBeingReplaced }: RadiatorCatalougeProps) {
  const { areCatalogueRadiatorsPending, catalogueRadiators } = useRadiatorContext();
  const [filteredRadiators, setFilteredRadiators] = useState<CatalogueRadiator[] | undefined>(undefined);
  const [mode, setMode] = useState<RadiatorMode>(RadiatorMode.Panel);

  useEffect(() => {
    const hasPanel = catalogueRadiators?.some((rad) => rad.radiatorDetail?.category?.$case === 'panelRadiator');
    const hasColumn = catalogueRadiators?.some((rad) => rad.radiatorDetail?.category?.$case === 'columnRadiator');
    if (hasPanel) {
      setMode(RadiatorMode.Panel);
    } else if (hasColumn) {
      setMode(RadiatorMode.Column);
    }
  }, [catalogueRadiators, setMode]);
  return (
    <Stack data-testid="radiator-catalogue-container" sx={{ minWidth: '1050px' }}>
      <RadiatorFilter
        setMode={setMode}
        mode={mode}
        replacingRadiator={radiatorBeingReplaced ? editedRadiator : undefined}
        radiatorForFilterSetup={radiatorBeingReplaced ?? editedRadiator}
        onFilteredRadiatorsChange={setFilteredRadiators}
      />
      {areCatalogueRadiatorsPending || isNil(filteredRadiators) ? (
        <Stack sx={{ padding: '32px' }} direction="row" alignItems="center" justifyContent="center">
          <CircularProgress />
        </Stack>
      ) : (
        <RadiatorCatalogueTable radiator={editedRadiator} mode={mode} radiators={filteredRadiators} />
      )}
    </Stack>
  );
}

export const RadiatorCatalogue = memo(RadiatorCatalogueComponent);
