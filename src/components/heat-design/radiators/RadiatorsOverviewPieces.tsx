import { green, red } from '@ui/theme/colors';
import { HEAT_DESIGN_FLOOR_PLAN_DIMENSION } from '@ui/theme/constants';
import { CountryCode } from 'utils/marketConfigurations';
import { RoomHeatDesignResult } from '../stores/OutputsStore';
import { calculateNetHeatOutputForRoom } from '../utils/radiatorHelpers';
import { Room } from '../stores/types';
import { invalidPlanSurface } from '../HeatDesignTheme';
import { radiatorIsValid, underfloorHeatingIsValid } from '../Validator';
import { generateWarningIconForRoom } from '../components/WarningIcon';
import { TextPill } from '@ui/components/TextPill/TextPill';
import { ClimateDataStore } from '../stores/ClimateDataStore';

export const InvalidHeatSources = ({ roomsInSelectedFloor }: { roomsInSelectedFloor: Room[] }) =>
  roomsInSelectedFloor.map((room) => {
    let invalidNewRadiator = false;
    const outputs = room.radiators.flatMap((radiator) => {
      const magicPlanRadiator = room.radiators.find((r) => r.uid === radiator.uid);

      if (!radiatorIsValid(radiator)) {
        if (magicPlanRadiator === undefined) {
          // This is where a radiator that has been added through our heat loss calculator is invalid.
          // In that case, we cannot highlight it on the plan, but the room should still show a warning icon.
          invalidNewRadiator = true;
        } else {
          const coords = (magicPlanRadiator.floorImageMap.coordinates || []).map((coord) => {
            if (Number.isNaN(coord)) return coord;
            return coord;
          });
          return (
            <polygon
              key={magicPlanRadiator.uid}
              points={coords.toString()}
              style={invalidPlanSurface}
              data-testid={`invalid-radiator-plan-highlight-${magicPlanRadiator.uid}`}
            />
          );
        }
      }
      return [];
    });

    const validUnderfloorHeating = underfloorHeatingIsValid(room);

    if (outputs.length > 0 || invalidNewRadiator || !validUnderfloorHeating) {
      outputs.push(generateWarningIconForRoom(room));
    }

    return outputs;
  });

export const FloorRoomRadiatorWattPills = ({
  roomsInSelectedFloor,
  roomHeatDesignResults,
  constructionYear,
  countryCode,
  flowReturnDeltaT,
  flowTemperature,
  climateDataStore,
}: {
  roomsInSelectedFloor: Room[];
  roomHeatDesignResults: RoomHeatDesignResult[];
  constructionYear: number;
  countryCode: CountryCode;
  flowReturnDeltaT: number;
  flowTemperature: number;
  climateDataStore: ClimateDataStore;
}) =>
  roomsInSelectedFloor.map((room) => {
    const netHeatOutput = Math.round(
      calculateNetHeatOutputForRoom({
        room,
        roomHeatDesignResults,
        constructionYear,
        countryCode,
        flowReturnDeltaT,
        flowTemperature,
        climateDataStore,
      }),
    );

    const xs = room.imageMap.coordinates.flatMap((v, i) => (i % 2 === 0 ? v : []));
    const ys = room.imageMap.coordinates.flatMap((v, i) => (i % 2 !== 0 ? v : []));
    const x = xs.reduce((p, n) => p + n) / xs.length;
    const y = ys.reduce((p, n) => p + n) / ys.length;

    return (
      <g key={room.id}>
        <polygon
          key={`room-shading-${room.id}`}
          points={room.imageMap.coordinates.toString()}
          className={netHeatOutput >= 0 ? 'room' : 'room not-enough-heat'}
        />
        <TextPill
          key={`room-net-heat-${room.id}`}
          text={`${netHeatOutput > 0 ? '+' : ''}${netHeatOutput} W`}
          backgroundColour={netHeatOutput >= 0 ? green[800] : red[800]}
          x={x}
          y={y}
          canvasWidth={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
          canvasHeight={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
        />
      </g>
    );
  });
