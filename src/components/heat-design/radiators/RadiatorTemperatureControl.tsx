import { Input<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography, useMediaQuery } from '@mui/material';
import { calculateReturnAndMeanWaterTemperature, getMaximumFlowTemperature } from '../utils/radiatorHelpers';
import { TextField } from '@ui/components/TextField/TextField';
import InfoBlock from './RadiatorRenderModalInfoBlock';
import { Wave2Icon } from '@ui/components/StandardIcons/Wave/Wave2Icon';
import { ThermometerIcon } from '@ui/components/StandardIcons/ThermometerIcon';
import { getHighestDesignRoomTemp } from '../utils/calculations';
import { useIntl } from 'react-intl';
import { useFlowReturnDeltaT, useFlowTemp, useHeatSourceActions } from '../stores/HeatSourceStore';
import { useRooms } from '../stores/RoomsStore';
import { useConstructionYear } from '../stores/HouseInputsStore';
import { useGroundwork } from '../../../context/groundwork-context';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import useSliderDebounce from '../hooks/useDebounce';
import { sendGTMEvent } from '@next/third-parties/google';
import { surface } from '@ui/theme/colors';
import { useState } from 'react';
import useDebounceEffect from '@ui/components/AddressInputLoqate/useDebounceEffect';
import { useRadiatorSummary } from './hooks/useRadiatorSummary';
import { formatDegreeCelsius } from '../../../utils/helpers';

export function RadiatorTemperatureControl() {
  const intl = useIntl();
  const heatSourceActions = useHeatSourceActions();
  const rooms = useRooms();
  const flowTemp = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const constructionYear = useConstructionYear();
  const [localFlowTemp, setLocalFlowTemp] = useState(flowTemp);
  const [localFlowReturnDeltaT, setLocalFlowReturnDeltaT] = useState(flowReturnDeltaT);

  const { countryCode } = useGroundwork();
  const climateDataStore = useClimateDataStore();
  const radiatorSummary = useRadiatorSummary();
  useDebounceEffect(
    () => {
      heatSourceActions.updateWaterTemps({
        flowTemp: localFlowTemp,
        flowReturnDeltaT: localFlowReturnDeltaT,
      });
    },
    100,
    [localFlowTemp, localFlowReturnDeltaT],
  );
  const debouncedFlowTempSliderInteractionEvent = useSliderDebounce(
    () =>
      sendGTMEvent({
        event: 'slider_interaction',
        slider_name: 'flow temperature',
      }),
    [],
  );

  const debouncedDeltaTSliderInteractionEvent = useSliderDebounce(
    () =>
      sendGTMEvent({
        event: 'slider_interaction',
        slider_name: 'flow/return deltat',
      }),
    [],
  );
  const { meanWaterTemp } = calculateReturnAndMeanWaterTemperature({
    flowTemperature: flowTemp,
    flowReturnDeltaT,
  });

  const updateFlowTemp = ({ value }: { value: number }) => {
    setLocalFlowTemp(value);
    debouncedFlowTempSliderInteractionEvent();
  };

  const updateFlowReturnDeltaT = ({ value }: { value: number }) => {
    setLocalFlowReturnDeltaT(value);
    debouncedDeltaTSliderInteractionEvent();
  };

  const highestRoomTemp = getHighestDesignRoomTemp(rooms, constructionYear, countryCode, climateDataStore);

  const meanWaterTempIsInvalid = meanWaterTemp < highestRoomTemp;
  const isMedium = useMediaQuery('(max-width: 1700px)');
  const isSmall = useMediaQuery('(max-width: 1300px)');
  const isSmallest = useMediaQuery('(max-width: 1000px)');
  return (
    <Stack
      gap={4}
      sx={{ width: '100%', height: '100%', zIndex: 2, position: 'relative', padding: '8px' }}
      direction="row"
      justifyContent="space-between"
    >
      <Stack direction={isMedium ? 'column' : 'row'} sx={{ height: '100%' }} gap={isMedium ? 0 : 3}>
        <Stack sx={{ padding: '8px 8px' }} gap={2} flex={1} direction="row" alignItems="center">
          <InputLabel htmlFor="flowTemp">
            <Typography variant="inputLabel">
              {intl.formatMessage({ id: 'heatDesign.radiator.FlowTemperature' })}
            </Typography>
          </InputLabel>
          <Stack flex={1} direction="row" spacing={2} justifyContent="flex-start" alignItems="center">
            <Slider
              sx={{ minWidth: '150px', padding: 0 }}
              data-testid="flow-temperature-slider"
              id="flowTemp"
              name="flowTemp"
              valueLabelFormat={(v) => `${v}°C`}
              value={localFlowTemp}
              onChange={(_event, v) => updateFlowTemp({ value: v as number })}
              min={20}
              max={getMaximumFlowTemperature({ countryCode })}
              step={1}
              valueLabelDisplay="auto"
            />
            <TextField
              name="flow-temperature"
              value={localFlowTemp}
              suffix="°C"
              sx={{ width: '75px', maxHeight: '36px', display: 'flex', alignItems: 'center' }}
              onChange={(e) => updateFlowTemp({ value: parseFloat(e.target.value) })}
            />
          </Stack>
        </Stack>
        <Stack sx={{ padding: '12px 8px' }} gap={2} flex={1} direction="row" alignItems="center">
          <InputLabel htmlFor="flowReturnDeltaT">
            <Typography variant="inputLabel">
              {intl.formatMessage({ id: 'heatDesign.radiator.FlowReturnDeltaT' })}
            </Typography>
          </InputLabel>
          <Stack flex={1} direction="row" spacing={2} justifyContent="flex-start" alignItems="center">
            <Slider
              sx={{ minWidth: '150px', padding: 0 }}
              data-testid="flow-return-delta-t-slider"
              id="flowReturnDeltaT"
              name="flowReturnDeltaT"
              valueLabelFormat={(v) => `${v}°C`}
              value={localFlowReturnDeltaT}
              onChange={(_event, v) => updateFlowReturnDeltaT({ value: v as number })}
              min={3}
              max={20}
              step={1}
              valueLabelDisplay="auto"
            />
            <Tooltip
              arrow={true}
              title={
                <Typography color="#fff" variant="body2" mt={1}>
                  {intl.formatMessage(
                    { id: 'heatDesign.radiator.heatOutput.totals.meanWaterAirTransferInvalid' },
                    { highestRoomTemp },
                  )}
                </Typography>
              }
            >
              <Stack>
                <TextField
                  name="flow-temperature"
                  value={localFlowReturnDeltaT}
                  suffix="°C"
                  error={meanWaterTempIsInvalid}
                  sx={{ width: '75px', maxHeight: '36px', display: 'flex', alignItems: 'center' }}
                  onChange={(e) => updateFlowReturnDeltaT({ value: parseFloat(e.target.value) })}
                />
              </Stack>
            </Tooltip>
          </Stack>
        </Stack>
      </Stack>
      <Stack direction={isSmall ? 'column' : 'row'} flex="0 1 auto" gap={1}>
        <Stack sx={{ padding: '0px 8px' }} flex={1} direction="row">
          <InfoBlock
            label={intl.formatMessage({
              id: 'heatDesign.emitterReport.calculationResults.returnTemperature',
            })}
            tooltip={intl.formatMessage({
              id: 'heatDesign.returnTemperatureTooltip',
            })}
            id="return-temperature-info-block"
            value={formatDegreeCelsius(radiatorSummary.actualCircuitReturnTemperature)}
            verticalLayout
            sx={{
              flex: '1 1 300px',
              minWidth: '150px',
              maxWidth: '300px',
              background: surface[100],
              padding: '8px',
              borderRadius: '8px',
              maxHeight: '75px',
              height: '75px',
              '.MuiTypography-root': { marginTop: 0, textWrap: isSmallest ? 'wrap' : 'nowrap' },
            }}
            icon={<Wave2Icon />}
          />
        </Stack>
        <Stack sx={{ padding: '0px 8px' }} flex={1} direction="row">
          <InfoBlock
            id="emitter-average-temperature"
            label={intl.formatMessage({
              id: 'heatDesign.emitterReport.calculationResults.emitterAverageTemperature',
            })}
            value={`${meanWaterTemp} °C`}
            verticalLayout
            sx={{
              flex: '1 1 300px',
              minWidth: '150px',
              maxWidth: '300px',
              background: surface[100],
              padding: '8px',
              maxHeight: '75px',
              height: '75px',
              borderRadius: '8px',
              '.MuiTypography-root': { marginTop: 0, textWrap: isSmallest ? 'wrap' : 'nowrap' },
            }}
            icon={<ThermometerIcon />}
          />
        </Stack>
      </Stack>
    </Stack>
  );
}
