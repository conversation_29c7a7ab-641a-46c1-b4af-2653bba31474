import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Box, useMediaQuery } from '@mui/material';
import { RadiatorTemperatureControl } from './RadiatorTemperatureControl';
import { beige } from '@ui/theme/colors';

export function StickyRadiatorTemperatureControl() {
  const positionRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const parentRef = useRef<HTMLDivElement>(null);

  const [isStuck, setIsStuck] = useState(false);

  const [initialPosition, setInitialPosition] = useState({
    width: 0,
    height: 0,
    offsetTop: 0,
    left: 0,
    top: 0,
  });
  const measure = useCallback(() => {
    const positionRect = positionRef.current!.getBoundingClientRect();
    const containerRect = containerRef.current!.getBoundingClientRect();
    if (isStuck || window.scrollY >= positionRect.top + window.scrollY) {
      // We don't want to change the height of the initial position
      // as otherwise we make the element stuck forever to the top of the screen
      // However, we still want to update the width, so if
      // the user resizes the screen while the header is sticky, we adjust
      // the header as well.
      setInitialPosition((prev) => {
        return {
          ...prev,
          width: positionRect.width,
        };
      });
      return;
    }

    setInitialPosition({
      width: positionRect.width,
      height: containerRect.height,
      offsetTop: positionRect.top + window.scrollY,
      left: positionRect.left,
      top: positionRect.top,
    });
  }, [isStuck]);

  useEffect(() => {
    if (!positionRef.current) return;

    // Initial measurement
    measure();

    // Observe for size changes
    const resizeObserver = new ResizeObserver(() => {
      measure();
    });
    resizeObserver.observe(parentRef.current!);
    // Cleanup
    return () => {
      resizeObserver.disconnect();
    };
  }, [measure]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY >= initialPosition.offsetTop) {
        setIsStuck(true);
      } else {
        setIsStuck(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [initialPosition.offsetTop]);
  const isMedium = useMediaQuery('(max-width: 1700px)');
  const isSmall = useMediaQuery('(max-width: 1300px)');
  const height = isSmall ? '180px' : isMedium ? '140px' : '100px';
  return (
    <Box sx={{ padding: '16px 0', display: 'flex', maxWidth: '100%' }} ref={parentRef}>
      <div
        ref={positionRef}
        style={{
          opacity: 0,
          pointerEvents: 'none',
          width: '100%',
          height: '0',
          position: 'absolute',
          top: 0,
        }}
      ></div>
      <Box
        ref={containerRef}
        sx={{
          position: isStuck ? 'fixed' : 'relative',
          top: 0,
          left: isStuck ? initialPosition.left : 0,
          zIndex: 10,
          width: isStuck ? initialPosition.width : 'auto',
          flex: 1,
          right: 0,
          display: 'flex',
          height: isStuck ? height : 'auto',
          justifyContent: 'space-evenly',
          alignItems: 'center',
        }}
      >
        <Box
          sx={{
            zIndex: 1,
            background: beige[100],
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            padding: isStuck ? '16px' : '0',
            opacity: isStuck ? 1 : 1,
            boxShadow: isStuck ? '0 8px 36px #00000040' : 'none',
            height: '100%',
            borderRadius: '0 0 16px 16px',
            transition: `box-shadow 0.3s${isStuck ? ', left 1s, width 1s' : ''}`,
          }}
        />
        <Box
          sx={{
            transition: isStuck ? 'margin 1s' : '',
            width: '100%',
            zIndex: 2,
            height: '100%',
          }}
        >
          <RadiatorTemperatureControl />
        </Box>
      </Box>

      {isStuck && <Box sx={{ height: initialPosition.height }} />}
    </Box>
  );
}
