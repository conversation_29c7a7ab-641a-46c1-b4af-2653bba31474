import { Stack } from '@mui/material';
import { TabPanel, TabPanelsWrapper } from '@ui/components/Tabs/Tabs';
import { useState } from 'react';
import EmitterOverview from '../Emitters/EmitterOverview';
import RadiatorTabs, { RadiatorTab } from './RadiatorTabs';
import RadiatorsFloorOverview from './RadiatorsFloorOverview';
import { StickyRadiatorTemperatureControl } from './StickyRadiatorTemperatureControl';

export default function RadiatorsOverview() {
  const [selectedTab, setSelectedTab] = useState<RadiatorTab>(RadiatorTab.FloorOverview);
  return (
    <Stack direction="row" spacing={2} justifyContent="flex-start" alignItems="flex-start">
      <Stack direction="column" flexGrow={1} sx={{ width: '100%' }}>
        <RadiatorTabs selectedTab={selectedTab} onChangeTab={setSelectedTab} />
        <TabPanelsWrapper sx={{ padding: '0 32px 32px 32px' }}>
          <TabPanel isSelected={selectedTab === RadiatorTab.FloorOverview} data-testid="customer-report-tab">
            <Stack sx={{ position: 'relative' }}>
              <StickyRadiatorTemperatureControl />
              <RadiatorsFloorOverview />
            </Stack>
          </TabPanel>
          <TabPanel isSelected={selectedTab === RadiatorTab.EmitterList} data-testid="technical-report-tab">
            <Stack mt={2}>
              <EmitterOverview showUnderfloorHeatingDisclaimer />
            </Stack>
          </TabPanel>
        </TabPanelsWrapper>
      </Stack>
    </Stack>
  );
}
