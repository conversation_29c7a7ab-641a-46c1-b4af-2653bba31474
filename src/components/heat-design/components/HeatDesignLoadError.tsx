import { Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { getAerospaceErrorDetails } from 'utils/errors/aerospaceError';
import { StyledFormattedMessage } from 'utils/localization';
import { linkText } from '../navigation/SaveButton';

const MAGICPLAN_FAQ_HOW_TO_SUBMIT = 'https://wiki.airahome.com/s/aira/p/magicplan-faq-tD1uuoF5fp';

export function HeatDesignLoadError({
  error,
  formattedAddress,
}: {
  error: ReturnType<typeof api.HeatLossCalculator.loadHeatDesign.useQuery>['error'];
  formattedAddress: string | undefined;
}) {
  const details = getAerospaceErrorDetails(error);
  if (details && details.detailType === 'HEAT_DESIGN_NOT_FOUND_ERROR') {
    switch (details.notFound) {
      case 'survey_form':
        return (
          <>
            <Typography>
              <FormattedMessage id="heatDesign.error.noFormFound" />
            </Typography>
            <br />
            <Typography fontWeight="bold" variant="body1">
              <FormattedMessage id="heatDesign.error.dwellingAddress" /> {formattedAddress}
            </Typography>
          </>
        );

      case 'heat_design':
        return (
          <>
            <Typography>
              <FormattedMessage id="heatDesign.error.checkMagicplanProjectNotDeleted" />
            </Typography>
            <br />
            <Typography fontWeight="bold" variant="body1">
              <FormattedMessage id="heatDesign.error.dwellingAddress" /> {formattedAddress}
            </Typography>
          </>
        );

      case 'submission':
        return (
          <>
            <Typography>
              <FormattedMessage id="heatDesign.error.noMagicplanSubmission" />
            </Typography>
            <br />
            <Typography>
              <StyledFormattedMessage
                id="heatDesign.error.noMagicplanSubmission.howTo"
                values={{ linkToFAQ: (chunks) => linkText(chunks, MAGICPLAN_FAQ_HOW_TO_SUBMIT) }}
              />
            </Typography>
          </>
        );

      default:
      // fall through
    }
  }
  return (
    <>
      <Typography>
        <FormattedMessage id="heatDesign.error.cannotRetrieveMagicplanProject" />
      </Typography>
      <br />
      <Typography>
        <FormattedMessage id="common.error.contactDevelopmentTeam" />
      </Typography>
    </>
  );
}
