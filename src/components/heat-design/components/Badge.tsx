import { Box, SxProps } from '@mui/material';
import { brandYellow } from '@ui/theme/colors';

export default function Badge({ children, sx }: { children: React.ReactNode; sx?: SxProps }) {
  return (
    <Box
      sx={{
        color: 'black',
        height: '2rem',
        padding: '5px',
        margin: '0 10px',
        fontSize: '14px',
        borderRadius: '5px',
        textTransform: 'uppercase',
        backgroundColor: brandYellow[400],
        '@media print': {
          display: 'none',
        },
        ...sx,
      }}
    >
      {children}
    </Box>
  );
}
