import type { IAutocompleteOption } from '@ui/components/shared/types';
import { UValue, getApplicableYearsAsString } from 'components/heat-design/models/UValue';

type UValueId = string;

export interface UValueOption {
  uValueName: string;
  uValueId: UValueId;
  uValueValue: number;
  group: UValueRatingGroup;
  disabled: boolean;
}

export enum UValueRatingGroup {
  Default,
  WorseThanConstructionYear,
}

export function formatUValueValue(value: number): string {
  return UValue.formatValue(value);
}

export function getUValueOptionLabel(option: UValueOption): string {
  return `${formatUValueValue(option.uValueValue)} | ${option.uValueName}`;
}

export function uValueToOption(uValue: UValue): UValueOption {
  return {
    uValueName: `${getApplicableYearsAsString(uValue)}${uValue.name}`,
    uValueId: uValue.id,
    uValueValue: uValue.value,
    group: UValueRatingGroup.Default,
    disabled: uValue.metadata.isDeprecated ?? false,
  };
}

export function uValueToGroupedOption(uValue: UValue, worstUValue: UValue): UValueOption {
  return {
    ...uValueToOption(uValue),
    group: uValue.value <= worstUValue.value ? UValueRatingGroup.Default : UValueRatingGroup.WorseThanConstructionYear,
  };
}

export function formatSelectedValueOptionForInput(
  selectedValue: UValueOption | null,
): IAutocompleteOption<string> | null {
  if (selectedValue == null) return null;

  return {
    label: getUValueOptionLabel(selectedValue),
    value: selectedValue.uValueId,
  };
}
