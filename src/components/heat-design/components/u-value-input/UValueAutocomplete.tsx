import { Box, FormControl, InputProps } from '@mui/material';
import MuiAutocomplete, { AutocompleteInputChangeReason } from '@mui/material/Autocomplete';
import React, { useState } from 'react';
import { AutocompleteInputComponent } from '@ui/components/shared/AutocompleteInputComponent';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { UValueOption, UValueRatingGroup, formatSelectedValueOptionForInput, getUValueOptionLabel } from './helpers';
import { AutocompleteUValueOptionBox } from './UValueOptionBox';
import { UValueGroup } from './UValueGroup';

function isOptionEqualToValue(optionA: UValueOption, optionB: UValueOption) {
  return optionA.uValueId === optionB.uValueId;
}

type OnChangeEvent = UValueOption | null;

interface Props {
  error: boolean;
  options: UValueOption[];
  isOptionDisabled: (option: UValueOption) => boolean;
  disabled?: boolean;
  placeholder?: string;
  onChange: (e: OnChangeEvent | null) => void;
  name: string;
  value: UValueOption | null;
  disableClearable?: boolean;
  filterOptions?(options: UValueOption[], state: { inputValue: string }): UValueOption[];
  PaperComponent?: React.JSXElementConstructor<any>;
  startInputAdornment?: React.ReactNode;
  inputProps?: InputProps['inputProps'];
  boldOptionTextMatcher?: (userInput: string) => RegExp;
  constructionYear?: number;
}

/**
 * A dropdown specifically designed for picking a U-Value.
 */
export function UValueAutocomplete({
  error,
  options,
  isOptionDisabled,
  placeholder,
  disabled,
  onChange,
  name,
  value,
  filterOptions,
  disableClearable,
  PaperComponent,
  startInputAdornment,
  inputProps,
  boldOptionTextMatcher,
  constructionYear,
  ...props
}: Props) {
  const [userInput, setUserInput] = useState('');

  const updateSelectedOption = (val: UValueOption | null) => {
    onChange(val);
  };

  const userInputChange = (newInputValue: string, reason: AutocompleteInputChangeReason) => {
    if (reason === 'reset') {
      setUserInput('');
      return;
    }
    setUserInput(newInputValue);
  };

  const scrollToSelectedOption = () => {
    setTimeout(() => {
      if (value == null) return;
      const list = document.getElementById(`${name}-select-listbox`);
      const targetLi = document.getElementById(value.uValueId);
      if (list && targetLi) {
        list.scrollTop = targetLi.offsetTop - 50;
      }
    }, 1);
  };

  return (
    <FormControl>
      <MuiAutocomplete
        disablePortal
        disableClearable={disableClearable}
        clearOnBlur={false}
        isOptionEqualToValue={isOptionEqualToValue}
        disabled={disabled}
        getOptionLabel={getUValueOptionLabel}
        id={`${name}-select`}
        value={value}
        size="small"
        forcePopupIcon
        onChange={(_, val) => updateSelectedOption(val)}
        options={options}
        getOptionDisabled={isOptionDisabled}
        onInputChange={(_, newValue, reason) => userInputChange(newValue, reason)}
        filterOptions={filterOptions}
        PaperComponent={PaperComponent}
        popupIcon={
          !disabled && (
            <Box width={24} height={24} sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <ChevronDown height={10} width={20} />
            </Box>
          )
        }
        onOpen={scrollToSelectedOption}
        groupBy={(option) => option.group.toString()}
        renderGroup={(params) => (
          <UValueGroup
            key={params.key}
            showWarning={params.group === UValueRatingGroup.WorseThanConstructionYear.toString()}
            constructionYear={constructionYear}
          >
            {params.children}
          </UValueGroup>
        )}
        renderOption={(params, option) => (
          <AutocompleteUValueOptionBox
            key={option.uValueId}
            params={params}
            uValueLabel={option.uValueName}
            uValueValue={option.uValueValue}
            uValueId={option.uValueId}
            isSelected={value?.uValueId === option.uValueId}
            isDisabled={option.disabled}
            userInput={userInput}
            boldTextMatcher={boldOptionTextMatcher}
          />
        )}
        renderInput={(params) => (
          <AutocompleteInputComponent
            selectedValue={formatSelectedValueOptionForInput(value)}
            params={params}
            inputProps={inputProps}
            error={error}
            placeholder={placeholder}
            startInputAdornment={!disabled && startInputAdornment}
          />
        )}
        {...props}
      />
    </FormControl>
  );
}
