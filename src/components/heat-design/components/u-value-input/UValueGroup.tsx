import { Typography } from '@mui/material';
import { red } from '@ui/theme/colors';
import { ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
  children: ReactNode;
  showWarning: boolean;
  constructionYear?: number;
};

export function UValueGroup({ showWarning, constructionYear, children }: Props) {
  if (showWarning && constructionYear !== undefined) {
    return (
      <div
        style={{
          backgroundColor: red[100],
          color: red[600],
        }}
      >
        <div style={{ padding: '16px 8px', margin: '0 8px', borderTop: `1px solid ${red[600]}` }}>
          <Typography variant="body2" color={red[600]}>
            <FormattedMessage id="heatDesign.uValues.worseThanBuildingRegulations.v2" values={{ constructionYear }} />
          </Typography>
        </div>
        <div>{children}</div>
      </div>
    );
  }

  return children;
}
