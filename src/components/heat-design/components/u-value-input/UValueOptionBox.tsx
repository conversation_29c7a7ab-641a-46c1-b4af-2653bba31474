import { HTMLAttributes } from 'react';
import { Box } from '@mui/system';
import { grey } from '@ui/theme/colors';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { stringWithBoldedInput } from '@ui/components/shared/AutocompleteOptionBox';
import { FormattedMessage } from 'react-intl';
import { formatUValueValue } from './helpers';

export function AutocompleteUValueOptionBox({
  params,
  uValueId,
  uValueValue,
  uValueLabel,
  isSelected,
  isDisabled,
  userInput,
  boldTextMatcher,
}: {
  params: HTMLAttributes<HTMLLIElement>;
  uValueId: string;
  uValueValue: number;
  uValueLabel: string;
  isSelected: boolean;
  isDisabled: boolean;
  userInput: string;
  boldTextMatcher?: (inputs: string) => RegExp;
}) {
  // Extract the key from the params. It shouldn't be there, but it is, and it
  // sends errors to the console. Thanks, MUI.
  const { key: _key, ...otherParams } = params as HTMLAttributes<HTMLLIElement> & { key: string };

  return (
    <Box {...otherParams} component="li" display="flex" id={uValueId} justifyContent="space-between !important">
      <Box display="flex" alignItems="center">
        <span
          style={{
            fontVariantNumeric: 'tabular-nums',
            display: 'inline-block',
            marginRight: '1ch',
            paddingRight: '1ch',
            borderRight: `1px solid ${grey[200]}`,
          }}
        >
          {formatUValueValue(uValueValue)}
        </span>
        <span>
          {stringWithBoldedInput(uValueLabel, userInput, boldTextMatcher).map((part, i) => {
            const Component = part.isBold ? 'strong' : 'span';
            // Some parts may contain the same text and weight, so we need to use the order (index) of the part to make it unique

            return <Component key={part.text + part.isBold + i}>{part.text}</Component>;
          })}
        </span>
        {isDisabled && (
          <span style={{ marginLeft: '1ch' }}>
            (<FormattedMessage id="common.deprecated" />)
          </span>
        )}
      </Box>
      {isSelected && (
        <Box alignItems="center" display="flex">
          <CheckIconThin />
        </Box>
      )}
    </Box>
  );
}
