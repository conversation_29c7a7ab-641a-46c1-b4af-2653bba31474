import { Button } from '@ui/components/Button/Button';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { TextField } from '@ui/components/TextField/TextField';
import { UValueAutocomplete } from 'components/heat-design/components/u-value-input/UValueAutocomplete';
import { red } from '@ui/theme/colors';
import { IconButton, InputLabel, InputProps, Paper, Stack, Typography } from '@mui/material';
import { useCallback, useMemo, useState } from 'react';
import { Search } from '@ui/components/Icons/material';
import { escapeRegex } from 'utils/strings/regexp';
import { FormattedMessage, useIntl } from 'react-intl';
import { UValue } from '../../models/UValue';
import { ProjectFabricTypes, RoomFabricTypes } from '../../stores/types';
import { useUValues, useUValuesActions } from '../../stores/UValuesStore';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { getWorstUValueForBuildingConstructionYear } from '../../uValues';
import { getUValueOptionLabel, UValueOption, uValueToGroupedOption, uValueToOption } from './helpers';

function CustomUValueInput({
  surfaceType,
  onSave,
  onClose,
}: {
  surfaceType: ProjectFabricTypes | RoomFabricTypes;
  onSave: (uValue?: UValue) => void;
  onClose: () => void;
}) {
  const intl = useIntl();
  const [customUValue, setCustomUValue] = useState({
    value: 0,
    name: '',
  });

  const [error, setError] = useState(false);

  const handleSave = () => {
    if (customUValue.name.trim() === '' || customUValue.value <= 0 || Number.isNaN(customUValue.value)) {
      setError(true);
      return;
    }
    const newUValue = new UValue(customUValue.name, Number(customUValue.value), undefined, { isCustom: true });
    onSave(newUValue);
    setError(false);
  };

  const handleSaveCustomUValue = (customValues: typeof customUValue) => {
    setError(false);
    setCustomUValue(customValues);
  };

  return (
    <Stack>
      <Typography variant="inputLabel" mb={1}>
        {intl.formatMessage({ id: 'heatDesign.uValues.addCustomUValue' })}
      </Typography>
      <Stack direction="row" justifyContent="space-between" alignItems="flex-end">
        <Stack flexBasis="20%" mr="1em">
          <NumericTextField
            label={intl.formatMessage({ id: 'heatDesign.uValues.valueLabel' })}
            name={intl.formatMessage({ id: 'heatDesign.uValues.name' }, { surfaceType })}
            onChange={(n) => handleSaveCustomUValue({ ...customUValue, value: n })}
            value={customUValue.value}
            inputProps={{ 'data-testid': `heat-design-custom-uvalue-${surfaceType}-value` }}
            type="number"
            size="small"
            error={customUValue.value <= 0 || Number.isNaN(customUValue.value)}
          />
        </Stack>
        <Stack flexBasis="70%" mr="1em">
          <TextField
            label={intl.formatMessage({ id: 'common.label.name' }, { surfaceType })}
            name={`${surfaceType} Name`}
            onChange={(e: { target: { value: any } }) =>
              handleSaveCustomUValue({ ...customUValue, name: e.target.value })
            }
            value={customUValue.name}
            inputProps={{ 'data-testid': `heat-design-custom-uvalue-${surfaceType}-name` }}
            size="small"
            error={customUValue.name === ''}
          />
        </Stack>
        <Stack flexBasis="10%">
          <IconButton
            sx={{
              height: '40px',
              width: '40px',
            }}
            onClick={onClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Button
          variant="outlined"
          onClick={handleSave}
          size="small"
          data-testid={`heat-design-custom-uvalue-${surfaceType}-save`}
        >
          {intl.formatMessage({ id: 'heatDesign.uValues.addCustomUValue.save' })}
        </Button>
      </Stack>
      {error && (
        <Typography fontSize={16} color="error" mt={1}>
          <FormattedMessage id="heatDesign.validationModal.customValue.missingOrInvalid" />
        </Typography>
      )}
    </Stack>
  );
}

function AddCustomUValue({
  children,
  onClick,
  surfaceType,
}: {
  children: React.ReactNode;
  onClick: () => void;
  surfaceType: ProjectFabricTypes | RoomFabricTypes;
}) {
  return (
    <Paper>
      {children}
      <Button
        color="primary"
        fullWidth
        sx={{ justifyContent: 'flex-start', pl: 2 }}
        onMouseDown={onClick}
        data-testid={`heat-design-custom-uvalue-${surfaceType}-add`}
      >
        + <FormattedMessage id="heatDesign.uValues.addCustomUValue" tagName="span" />
      </Button>
    </Paper>
  );
}

type UValueInputProps = {
  surfaceType: ProjectFabricTypes | RoomFabricTypes;
  placeholder: string;
  label?: string;
  error: boolean;
  onChange: (e?: UValue) => void;
  value?: UValue;
  disabled?: boolean;
  inputProps?: InputProps['inputProps'];
  disableClearable?: boolean;
  'data-testid'?: string;
  /** If true, shows a red asterisk to indicate that the field is required */
  required?: boolean;
  constructionYear?: number;
};

function boldOptionTextMatcher(userInput: string) {
  const userInputs = userInput.trim().split(/\s+/);
  return RegExp(`(${userInputs.map((input) => escapeRegex(input)).join('|')})`, 'gi');
}

function sortUValues(a: UValue, b: UValue) {
  // Sort by value first and by name second
  if (a.value === b.value) {
    return a.name.localeCompare(b.name);
  }
  return Math.sign(a.value - b.value);
}

function filterOptionsByAllWords(opts: UValueOption[], { inputValue }: { inputValue: string }): UValueOption[] {
  const inputWords = inputValue.trim().toLowerCase().split(/\s+/);
  return opts.filter((option) => {
    const lowercaseUValueLabel = getUValueOptionLabel(option).toLowerCase();
    return inputWords.every((word) => lowercaseUValueLabel.includes(word));
  });
}

export function UValueInput({
  surfaceType,
  placeholder,
  label = 'U Value',
  error,
  onChange,
  value,
  disabled = false,
  inputProps,
  disableClearable = false,
  'data-testid': dataTestId,
  required,
  constructionYear,
}: UValueInputProps) {
  const uValues = useUValues();
  const uValuesActions = useUValuesActions();
  const [showCustomUValue, setShowCustomUValue] = useState(false);

  const options = useMemo(() => {
    const sortedUValues = uValues[surfaceType].toSorted(sortUValues);
    if (constructionYear != null) {
      const worstUValue = getWorstUValueForBuildingConstructionYear(uValues[surfaceType], constructionYear);
      if (worstUValue != null) {
        return sortedUValues.map((uValue) => uValueToGroupedOption(uValue, worstUValue));
      }
    }

    return sortedUValues.map(uValueToOption);
  }, [uValues, surfaceType, constructionYear]);

  const selectedOption = useMemo(() => (value ? uValueToOption(value) : null), [value]);

  function handleUValueSelect(newUValueId: string | null) {
    if (newUValueId === null) {
      onChange(undefined);
      return;
    }
    const uValue = uValues[surfaceType].find((u) => u.id === newUValueId);
    onChange(uValue);
  }

  const handleSaveCustomUValue = (uValue?: UValue) => {
    if (!uValue) return;
    // Check if the uValue already exists
    const existingUValue = uValues[surfaceType].find((u) => u.name === uValue.name && u.value === uValue.value);
    if (existingUValue) {
      handleUValueSelect(existingUValue.id);
      setShowCustomUValue(false);
      return;
    }
    uValuesActions.addUValue({ fabricType: surfaceType, uValue });
    handleUValueSelect(uValue.id);
    setShowCustomUValue(false);
  };

  const AddCustomUValuePaper = useCallback(
    (properties: any) => (
      <AddCustomUValue {...properties} onClick={() => setShowCustomUValue(true)} surfaceType={surfaceType} />
    ),
    [surfaceType],
  );

  return (
    <Stack>
      <InputLabel>
        <Typography variant="inputLabel">
          {label}
          {required && <span style={{ color: red[500], marginLeft: '.15em' }}>*</span>}
        </Typography>
      </InputLabel>
      {showCustomUValue ? (
        <CustomUValueInput
          surfaceType={surfaceType}
          onSave={handleSaveCustomUValue}
          onClose={() => setShowCustomUValue(false)}
        />
      ) : (
        <UValueAutocomplete
          disabled={disabled}
          options={options}
          isOptionDisabled={(uValueOption) => uValueOption.disabled}
          data-testid={dataTestId}
          value={selectedOption}
          onChange={(val: UValueOption | null) => handleUValueSelect(val?.uValueId ?? null)}
          PaperComponent={AddCustomUValuePaper}
          filterOptions={filterOptionsByAllWords}
          boldOptionTextMatcher={boldOptionTextMatcher}
          placeholder={placeholder}
          error={!disabled && error}
          name={`uValue-${surfaceType}`}
          startInputAdornment={<Search />}
          inputProps={inputProps}
          disableClearable={disableClearable}
          constructionYear={constructionYear}
        />
      )}
    </Stack>
  );
}
