import { Typography } from '@mui/material';
import { ReactNode, useEffect } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useGroundwork } from 'context/groundwork-context';
import { api } from 'utils/api';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { MagicplanIcon } from '@ui/components/Icons/Magicplan/MagicplanIcon';
import { Sha256 } from '@aws-crypto/sha256-js';
import { useHeatDesignGtmActions } from 'components/stores/heatDesignGtmStore';
import { useRoomsStore } from '../stores/RoomsStore';
import { useFloorsStore } from '../stores/FloorsStore';
import { getUrlQueryParameter } from '../utils/helpers';
import { useUValuesStore } from '../stores/UValuesStore';
import {
  HouseInputVentilationDesign,
  SimpleHouseInputVentilationDesign,
  StandardHouseInputVentilationDesign,
  useSetHouseInputs,
} from '../stores/HouseInputsStore';
import { useHeatSourceStore } from '../stores/HeatSourceStore';
import { findMagicplanAddress, findUrlFor3DModel, loadProjectFromProtobuf } from '../utils/loadFromProtobuf';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import { useSetAuxiliaryData } from '../stores/HeatDesignAuxiliaryDataStore';
import { HeatDesignLoadError } from './HeatDesignLoadError';
import { ErrorLayout } from './ErrorLayout';
import { HeatDesignErrorInfo } from './errors/HeatDesignErrorInfo';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { getAerospaceErrorDetails } from 'utils/errors/aerospaceError';
import { MissingMagicplanSubmissionFooter } from './errors/MissingMagicplanSubmissionFooter';
import {
  ACPHDefaults,
  AirPermeabilityOption,
  Dwelling,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { ACPHDefaultValue, VentilationCalculationMethod } from '../stores/types';
import { getStandardizedDefaultsForCountry, STANDARDIZED_ACPH_DEFAULTS_MAP } from '../utils/averageAirChangePerHour';
import { CountryCode } from 'utils/marketConfigurations';
import { DwellingExposureOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

async function hashSolutionId(solutionId?: string): Promise<string | undefined> {
  if (!solutionId) {
    return undefined;
  }

  const hash = new Sha256();
  hash.update(solutionId);
  const result = await hash.digest();
  const hashArray = Array.from(result);
  return hashArray.map((item) => item.toString(16).padStart(2, '0')).join('');
}

function mapACPHDefaultFromProtobuf(acphDefault: ACPHDefaults): ACPHDefaultValue {
  const defaults = acphDefault.defaults!;
  switch (defaults.$case) {
    case 'defaultAverageAirChangesPerHour': {
      return { type: 'custom', value: defaults.defaultAverageAirChangesPerHour };
    }
    case 'standardizedAcphDefaults': {
      const standard = STANDARDIZED_ACPH_DEFAULTS_MAP[defaults.standardizedAcphDefaults];
      if (!standard) {
        throw new Error(`Unknown standardized ACPH default: ${defaults.standardizedAcphDefaults}`);
      }
      return { type: 'standardized', standard };
    }
    default:
      return defaults satisfies never;
  }
}

function getVentilationDesignFromDwelling(
  dwelling: Dwelling,
  countryCode: CountryCode,
  isLocked: boolean,
): HouseInputVentilationDesign {
  let mappedVentilationDesign: HouseInputVentilationDesign | undefined = undefined;
  const method = dwelling.ventilationDesign?.ventilationMethod?.method;
  if (!method) {
    if (countryCode === CountryCode.GB && !isLocked) {
      mappedVentilationDesign = {
        calculationMethod: VentilationCalculationMethod.STANDARD,
        airPermeability: AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
        airPermeabilityOverride: undefined,
        dwellingExposure: DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
        acphDefault: getACPHDefaultFromDwelling(dwelling, countryCode),
      } satisfies StandardHouseInputVentilationDesign;
    } else {
      mappedVentilationDesign = {
        calculationMethod: VentilationCalculationMethod.SIMPLE,
        acphDefault: getACPHDefaultFromDwelling(dwelling, countryCode),
      } satisfies SimpleHouseInputVentilationDesign;
    }
  } else {
    switch (method?.$case) {
      case 'simpleVentilation':
        mappedVentilationDesign = {
          calculationMethod: VentilationCalculationMethod.SIMPLE,
          acphDefault: getACPHDefaultFromDwelling(dwelling, countryCode),
        };
        break;
      case 'standardVentilation':
        mappedVentilationDesign = {
          calculationMethod: VentilationCalculationMethod.STANDARD,
          airPermeability:
            method.standardVentilation.airPermeability?.airPermeabilityOption ??
            AirPermeabilityOption.AIR_PERMEABILITY_OPTION_EN_12831,
          airPermeabilityOverride:
            method.standardVentilation.airPermeability?.airPermeabilityOption ===
            AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST
              ? method.standardVentilation.airPermeability.airPermeabilityValue
              : undefined,
          dwellingExposure:
            method.standardVentilation.dwellingExposure?.dwellingExposureOption ??
            DwellingExposureOption.DWELLING_EXPOSURE_OPTION_PARTIALLY_SHIELDED,
          acphDefault: getACPHDefaultFromDwelling(dwelling, countryCode),
        };
        break;
      default:
        return method satisfies never;
    }
  }
  return mappedVentilationDesign;
}

function getACPHDefaultFromDwelling(dwelling: Dwelling, countryCode: CountryCode): ACPHDefaultValue {
  let protoAcphDefaultValue;
  const ventilationMethod = dwelling.ventilationDesign?.ventilationMethod?.method;
  if (ventilationMethod) {
    switch (ventilationMethod.$case) {
      case 'simpleVentilation':
        protoAcphDefaultValue = ventilationMethod.simpleVentilation.acphDefaults;
        break;
      case 'standardVentilation':
        protoAcphDefaultValue = ventilationMethod.standardVentilation.acphDefaults;
        break;
      default:
        return ventilationMethod satisfies never;
    }
  }
  if (!protoAcphDefaultValue) {
    return {
      type: 'standardized',
      standard: getStandardizedDefaultsForCountry(countryCode, dwelling.constructionYear),
    };
  }
  return mapACPHDefaultFromProtobuf(protoAcphDefaultValue);
}

type Props = {
  children: ReactNode;
};

/**
 * This component must be nested under <GroundworkContextProvider>. Its
 * responsibilities are to load the heat design data, render a loading screen,
 * and render loading errors.
 */
function HeatDesignLoader({ children }: Props) {
  const energySolutionId = useEnergySolutionId();
  const { groundwork, countryCode } = useGroundwork();
  const climateDataStore = useClimateDataStore();
  const { id: groundworkId, location } = groundwork;
  const { formattedAddress } = (location?.$case === 'exactAddress' && location.exactAddress) || {};
  const { formatMessage } = useIntl();

  const isDemoData = getUrlQueryParameter({ key: 'demo-data' });

  const updatedAt = useProtobufHeatDesignStore((s) => s.updatedAt);

  // Actions used to initialize the various Zustand stores
  const setProjectUValues = useUValuesStore((s) => s.actions.setProjectUValues);
  const setUValues = useUValuesStore((s) => s.actions.setUValues);
  const setHouseInputs = useSetHouseInputs();
  const setRooms = useRoomsStore((s) => s.actions.setRooms);
  const setHeatDesign = useProtobufHeatDesignStore((s) => s.actions.setHeatDesign);
  const setResult = useProtobufHeatDesignStore((s) => s.actions.setResult);
  const setEvents = useProtobufHeatDesignStore((s) => s.actions.setEvents);
  const setIsLocked = useProtobufHeatDesignStore((s) => s.actions.setIsLocked);
  const setUpdatedAt = useProtobufHeatDesignStore((s) => s.actions.setUpdatedAt);
  const setFloors = useFloorsStore((s) => s.actions.setFloors);
  const setAuxiliaryData = useSetAuxiliaryData();
  const updateWaterTemps = useHeatSourceStore((s) => s.actions.updateWaterTemps);
  const { setHashedSolutionId } = useHeatDesignGtmActions();

  hashSolutionId(energySolutionId).then((hashedSolutionId) => {
    setHashedSolutionId(hashedSolutionId);
  });

  // Load the heat design data
  const {
    data: heatDesignData,
    error: heatDesignLoadError,
    isLoading: isLoadingHeatDesign,
  } = api.HeatLossCalculator.loadHeatDesign.useQuery(
    { energySolutionId: energySolutionId!, installationGroundworkId: groundworkId!.value!, demoData: isDemoData },
    {
      enabled: !!energySolutionId,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
    },
  );

  useEffect(() => {
    if (heatDesignData && heatDesignData.updatedAt?.getTime() !== updatedAt?.getTime()) {
      // Set the data in the various Zustand stores
      const { heatDesign, isLocked, result, events, updatedAt: protoUpdatedAt } = heatDesignData;
      loadProjectFromProtobuf({
        countryCode,
        heatDesign,
        updateWaterTemps,
        setProjectUValues,
        setUValues,
        setRooms,
        setFloors,
        climateDataStore,
      });

      if (heatDesign.dwelling) {
        setHouseInputs({
          embed3dUrl: findUrlFor3DModel(heatDesign.dataSourceReferences),
          magicplanAddress: findMagicplanAddress(heatDesign.dataSourceReferences),
          constructionYear: heatDesign.dwelling.constructionYear,
          numberOfResidents: heatDesign.dwelling.numberOfResidents,
          isDwellingInExposedLocation: heatDesign.dwelling.exposedLocation,
          temperatureCompensation: heatDesign.dwelling.temperatureAdjustmentCelsius,
          ventilationDesign: getVentilationDesignFromDwelling(heatDesign.dwelling, countryCode, isLocked),
        });
      }

      setAuxiliaryData(heatDesign.auxiliaryData ?? {});
      setHeatDesign(heatDesign);
      setResult(result);
      setEvents(events);
      setIsLocked(isLocked);
      setUpdatedAt(protoUpdatedAt);
    }
  }, [
    // Make sure all of these dependencies are stable! Otherwise, we run the
    // risk of resetting the project in Zustand accidentally.
    heatDesignData,
    setHouseInputs,
    countryCode,
    updateWaterTemps,
    setProjectUValues,
    setUValues,
    setRooms,
    setFloors,
    setHeatDesign,
    setAuxiliaryData,
    updatedAt,
    setUpdatedAt,
    setIsLocked,
    setResult,
    climateDataStore,
    setEvents,
  ]);

  if (isLoadingHeatDesign) {
    return (
      <HeatPumpLoader>
        <Typography variant="h6">Importing house data... </Typography>
        <Typography variant="body1">This can take up to 60 seconds</Typography>
      </HeatPumpLoader>
    );
  }

  if (heatDesignLoadError && !isDemoData) {
    const errorDetails = getAerospaceErrorDetails(heatDesignLoadError);
    const isInformationalError =
      errorDetails?.detailType === 'HEAT_DESIGN_NOT_FOUND_ERROR' && errorDetails.level === 'informational';

    if (isInformationalError) {
      let footer: React.ReactNode = undefined;
      if (errorDetails.notFound === 'submission') {
        footer = groundworkId && <MissingMagicplanSubmissionFooter installationGroundworkId={groundworkId} />;
      }

      return (
        <ErrorLayout
          heading={formatMessage({ id: 'heatDesign.error.noMagicplanSubmission.title' })}
          icon={MagicplanIcon}
          level="informational"
          footer={footer}
        >
          <HeatDesignLoadError error={heatDesignLoadError} formattedAddress={formattedAddress} />
        </ErrorLayout>
      );
    } else {
      return (
        <ErrorLayout
          heading={<FormattedMessage id="heatDesign.error.unableToLoadMagicplanProject" />}
          icon={MagicplanIcon}
          details={
            <HeatDesignErrorInfo
              description={formatMessage({ id: 'heatDesign.error.magicplan.errorRetrievingProject' })}
              solutionId={energySolutionId}
              groundworkId={groundworkId?.value}
            />
          }
        >
          <HeatDesignLoadError error={heatDesignLoadError} formattedAddress={formattedAddress} />
        </ErrorLayout>
      );
    }
  }

  return children;
}

export default HeatDesignLoader;
