import { Stack, SxProps, Theme, Typography } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { memo, ReactNode } from 'react';

type Props = {
  icon: ReactNode;
  title: ReactNode;
  subtitle: ReactNode;
  sx?: SxProps<Theme>;
};

function InfoCardComponent({ icon, title, subtitle, sx }: Props) {
  return (
    <Stack
      sx={{
        background: beige[150],
        borderRadius: 1.5,
        '@media print': {
          background: 'white',
          border: `1px solid ${grey[500]}`,
          padding: 2,
          gap: 1,
        },
        height: '100%',
        width: '100%',
        padding: '8px 12px',
        ...(sx ?? {}),
      }}
      justifyContent="space-between"
    >
      <Stack direction="row" gap={1} alignItems="center">
        {icon}
        <Typography component="span" variant="number3" textAlign="center">
          {title}
        </Typography>
      </Stack>
      <Typography variant="body2">{subtitle}</Typography>
    </Stack>
  );
}

export const InfoCard = memo(InfoCardComponent);
