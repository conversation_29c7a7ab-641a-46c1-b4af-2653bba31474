import { Box, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';

type Props = {
  children: ReactNode;
  isOpen: boolean;
  onConfirmChanges: () => void;
  onRequestClose: () => void;
};

export default function UpdateUValuesModal({ children, isOpen, onConfirmChanges, onRequestClose }: Props) {
  return (
    <Modal
      isModalOpen={isOpen}
      handleClose={onRequestClose}
      heading={<FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.title" />}
      sx={{
        width: 'calc(100% - 40px)',
        maxWidth: 1200,
        height: 'unset',
        minHeight: 600,
        maxHeight: 'calc(100% - 40px)',
      }}
    >
      <Box>
        <Typography mb={2}>
          <FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.helper" />
        </Typography>
        {children}
      </Box>
      <Stack flexDirection="row" gap={2} justifyContent="flex-end" mt={4}>
        <Button variant="outlined" onClick={onRequestClose}>
          <FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.cancelButton" />
        </Button>
        <Button onClick={onConfirmChanges}>
          <FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.confirmButton" />
        </Button>
      </Stack>
    </Modal>
  );
}
