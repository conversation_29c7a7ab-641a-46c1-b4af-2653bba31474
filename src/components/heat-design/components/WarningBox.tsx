import { Typography } from '@mui/material';
import { Stack, SxProps, Theme } from '@mui/system';
import { brandYellow } from '@ui/theme/colors';
import React from 'react';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import { WarningIcon } from './WarningIcon';

export function WarningBox({
  contentId,
  sx,
}: {
  contentId: MessageDescriptor['id'];
  sx?: SxProps<Theme>;
}): React.JSX.Element {
  return (
    <Stack
      sx={{
        border: `2px solid ${brandYellow[500]}`,
        backgroundColor: brandYellow[100],
        padding: '4px 8px',
        borderRadius: '5px',
        ...sx,
      }}
      direction="row"
      alignItems="center"
      gap={1}
    >
      <Stack>
        <WarningIcon x={1.8} y={1.8} iconWidth={18} iconHeight={18} canvasWidth={18} canvasHeight={18} />
      </Stack>
      <Typography sx={{ marginTop: 0 }} variant="body2" mt={1}>
        <FormattedMessage id={contentId} />
      </Typography>
    </Stack>
  );
}
