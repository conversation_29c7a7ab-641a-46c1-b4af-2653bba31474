import { Box, Stack } from '@mui/system';
import { ReactNode, SVGProps } from 'react';
import { beige } from '@ui/theme/colors';
import { ErrorHeading } from './errors/ErrorHeading';
import { ErrorContent } from './errors/ErrorContent';
import { HeatDesignErrorLevel } from 'utils/errors/aerospaceError';

type Variant = 'full' | 'compact';

interface ErrorLayoutProps {
  variant?: Variant;
  heading: ReactNode;
  children: React.ReactNode;
  icon?: (props: SVGProps<SVGSVGElement>) => React.JSX.Element;
  sx?: any;
  details?: ReactNode | string;
  level?: HeatDesignErrorLevel;
  footer?: ReactNode;
}

export function ErrorLayout({
  heading,
  children,
  details,
  icon,
  variant = 'full',
  sx,
  level = 'error',
  footer,
}: ErrorLayoutProps) {
  return (
    <Box
      sx={{
        marginTop: variant === 'full' ? '100px' : '0px',
        padding: variant === 'full' ? '40px' : '0px',
        display: 'flex',
        justifyContent: 'center',
        overflowY: 'auto',
        position: 'relative', // Ensures the close button is positioned relative to the modal box
        outline: 'none',
        ...sx,
      }}
    >
      <Stack
        p={6}
        sx={{ background: beige[100], borderRadius: '22px', width: '50%', display: 'flex', flexDirection: 'column' }}
      >
        <div id="modal-modal-title">
          <Stack mb={2} justifyContent="flex-start" alignContent="center" direction="row" gap={2}>
            <ErrorHeading icon={icon} level={level}>
              {heading}
            </ErrorHeading>
          </Stack>
        </div>
        <ErrorContent details={details} footer={footer}>
          {children}
        </ErrorContent>
      </Stack>
    </Box>
  );
}
