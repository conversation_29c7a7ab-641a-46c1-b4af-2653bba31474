import { api } from 'utils/api';
import { useGroundwork } from 'context/groundwork-context';
import AccessDenied from 'components/access/AccessDenied';
import { canAccessHeatLossCalculator } from 'server/api/helpers/roles';
import { ReactNode } from 'react';

type Props = {
  children: ReactNode;
};

export default function HeatDesignAccessRestrictions({ children }: Props) {
  const { data: roles } = api.HeatLossCalculator.getRoles.useQuery();
  const { country } = useGroundwork();
  const canAccessPage = canAccessHeatLossCalculator(roles, country);

  if (!canAccessPage) {
    return <AccessDenied />;
  }

  return children;
}
