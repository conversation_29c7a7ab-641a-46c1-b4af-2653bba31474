import { beige } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { ArrowForward } from '@ui/components/Icons/material';
import { UValueDiff } from '../uValues';

type Props = {
  uValueDiffs: UValueDiff[];
};

export default function UValueDiffTable({ uValueDiffs }: Props) {
  return (
    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
      <thead style={{}}>
        <tr style={{ textAlign: 'left' }}>
          <th style={{ borderBottom: `1px solid ${beige[300]}`, padding: '8px 0' }}>
            <FormattedMessage id="common.label.fabric" />
          </th>
          <th style={{ borderBottom: `1px solid ${beige[300]}`, padding: '8px 0' }}>
            <FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.currentUValueHeader" />
          </th>
          {}
          <th
            style={{
              borderBottom: `1px solid ${beige[300]}`,
              padding: '8px 0',
              background: beige[100],
              borderRadius: '8px 0 0 0',
            }}
          >
            &nbsp;
          </th>
          <th
            style={{
              borderBottom: `1px solid ${beige[300]}`,
              background: beige[100],
              borderRadius: '0 8px 0 0',
              padding: '8px 0 8px 8px',
            }}
          >
            <FormattedMessage id="heatDesign.propertyDetails.updateUValuesModal.suggestedUValueHeader" />
          </th>
        </tr>
      </thead>
      <tbody>
        {uValueDiffs.map(({ before, after, fabricType }, index) => (
          <tr key={fabricType} style={{ verticalAlign: 'middle' }}>
            <td style={{ padding: '8px 0' }}>
              <FormattedMessage id={`heatDesign.roomSurfaceTypes.${fabricType}`} />
            </td>
            <td style={{ padding: '8px 0' }}>
              {before === null ? (
                <i>
                  <FormattedMessage id="common.label.none" />
                </i>
              ) : (
                before.display()
              )}
            </td>
            {}
            <td
              style={{
                padding: '0 16px',
                background: beige[100],
                borderRadius: index === uValueDiffs.length - 1 ? '0 0 0 8px' : 0,
                color: beige[300],
              }}
            >
              <ArrowForward style={{ display: 'block' }} />
            </td>
            <td
              style={{
                padding: 8,
                background: beige[100],
                borderRadius: index === uValueDiffs.length - 1 ? '0 0 8px 0' : 0,
              }}
            >
              {after === null ? (
                <i>
                  <FormattedMessage id="common.label.none" />
                </i>
              ) : (
                after.display()
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
