import { HousePersonOutsideOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutsideOutlinedIcon';
import { HeatDesignSidebarCategory, HeatDesignSidebarModules } from './HeatDesignSidebarModules';
import { OtherHousesIcon } from '@ui/components/Icons/OtherHouses/OtherHousesIcon';
import { HouseUValues } from './HouseUValues';
import { WindOutlinedIcon } from '@ui/components/StandardIcons/WindOutlinedIcon';
import { VentilationForm } from '../../dwelling/VentilationForm';
import { FloorPlanIcon } from '@ui/components/Icons/FloorPlan/FloorPlanIcon';
import { GrassIcon } from '@ui/components/Icons/Grass/GrassIcon';
import { SoilLevel } from './SoilLevel';
import { FloorUValues } from './FloorUValues';
import { useSidebarContext } from './SidebarContext';

export function HeatDesignSidebarContent() {
  const { errors, warnings } = useSidebarContext();

  const categories: HeatDesignSidebarCategory[] = [
    {
      id: 'house-defaults',
      title: 'House Defaults',
      icon: <HousePersonOutsideOutlinedIcon />,
      hasWarning: warnings.hasHouseDefaultWarning,
      isDefaultExpanded: warnings.hasHouseDefaultWarning,
      subcategories: [
        {
          id: 'house-u-values',
          title: 'U-values',
          icon: <OtherHousesIcon />,
          component: <HouseUValues />,
          hasWarning: warnings.hasUValuesWarning,
          isDefaultExpanded: warnings.hasUValuesWarning,
        },
        {
          id: 'ventilation',
          title: 'Ventilation',
          icon: <WindOutlinedIcon />,
          component: <VentilationForm />,
          hasWarning: warnings.hasHouseVentilationWarning,
          isDefaultExpanded: warnings.hasHouseVentilationWarning,
        },
      ],
    },
    {
      id: 'floor-defaults',
      title: 'Floor defaults',
      icon: <FloorPlanIcon />,
      hasWarning: warnings.hasFloorDefaultsWarning,
      isDefaultExpanded: warnings.hasFloorDefaultsWarning,
      subcategories: [
        {
          id: 'soil-level',
          title: 'Soil level',
          icon: <GrassIcon />,
          component: <SoilLevel />,
          hasWarning: errors.soilPercentage,
          isDefaultExpanded: errors.soilPercentage,
        },
        {
          id: 'floor-u-values',
          title: 'U-values',
          icon: <OtherHousesIcon />,
          component: <FloorUValues />,
          hasWarning: warnings.hasFloorUValuesWarning,
          isDefaultExpanded: warnings.hasFloorUValuesWarning,
        },
      ],
    },
  ];
  return <HeatDesignSidebarModules categories={categories} />;
}
