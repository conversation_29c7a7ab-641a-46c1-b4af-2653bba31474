import { Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { ROOM_FABRIC_TYPES, RoomFabricTypes } from '../../stores/types';
import { aRoomHasUValueForFabricType, lookupUValueForSurfaceType } from '../../utils/helpers';
import { UValueInput } from '../u-value-input/UValueInput';
import { StyledFormattedMessage } from '../../../../utils/localization';
import { memo } from 'react';
import { useFloors, useFloorsActions } from '../../stores/FloorsStore';
import { useGetRoomsByFloor } from '../../stores/RoomsStore';
import { useHeatDesignUI } from '../../stores/HeatDesignUIStore';
import { useProjectUValues } from '../../stores/UValuesStore';
import { UValue } from '../../models/UValue';
import { isNotNullish } from '../../../../utils/isNotNullish';
import { useSidebarContext } from './SidebarContext';

function FloorUValuesComponent() {
  const { formatMessage } = useIntl();
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floors = useFloors();
  const floor = floors.find((f) => f.uid === selectedFloorId);
  const roomsInFloor = useGetRoomsByFloor()[floor?.uid ?? ''];
  const projectUValues = useProjectUValues();
  const floorActions = useFloorsActions();
  const { errors } = useSidebarContext();

  if (!floor || !roomsInFloor) {
    return null;
  }
  const handleUValueSelection = (fabricType: RoomFabricTypes, uValue?: UValue) => {
    if (isNotNullish(selectedFloorId)) {
      floorActions.setFloorUValue({ floorUID: selectedFloorId, fabricType, uValue });
    }
  };
  const isLowestLevel = floor.floorNr === Math.min(...floors.map((f) => f.floorNr));

  return (
    <Stack direction="column" gap={2}>
      {ROOM_FABRIC_TYPES.map((fabricType) => {
        const aRoomHasUValueForFabric = aRoomHasUValueForFabricType(fabricType, roomsInFloor);
        const uValueLookup = lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors);
        let allowUserToChangeValue = true;
        if (fabricType === 'roofsOrCeilings') {
          allowUserToChangeValue = false;
        } else if (fabricType === 'floors' && isLowestLevel) {
          allowUserToChangeValue = false;
        }

        const disabled = !aRoomHasUValueForFabric || !allowUserToChangeValue;
        let placeholderMessage = formatMessage({ id: 'heatDesign.uValues.placeholder' });

        if (!allowUserToChangeValue) {
          placeholderMessage = formatMessage({ id: 'heatDesign.uValues.placeholder.setAtDwelling' });
        } else if (!aRoomHasUValueForFabric) {
          placeholderMessage = formatMessage(
            { id: 'heatDesign.uValues.placeholder.noFabricTypeInProject' },
            { fabricType: formatMessage({ id: `heatDesign.surfaces.${fabricType}` }) },
          );
        }

        return (
          <Stack direction="column" key={fabricType}>
            <UValueInput
              surfaceType={fabricType}
              placeholder={placeholderMessage}
              value={uValueLookup?.uValue}
              label={formatMessage({ id: `heatDesign.roomSurfaceTypes.${fabricType}` })}
              disabled={disabled}
              disableClearable
              error={errors.floorUValueErrors[fabricType] || false}
              onChange={(newUValue) => handleUValueSelection(fabricType, newUValue)}
            />
            {(uValueLookup?.source === 'dwelling' || uValueLookup?.source === 'floorLevelAbove') && (
              <Typography variant="body2" mt={0.5}>
                <StyledFormattedMessage id={`heatDesign.usingDefaultUValue.${uValueLookup.source}`} />
              </Typography>
            )}
          </Stack>
        );
      })}
    </Stack>
  );
}
export const FloorUValues = memo(FloorUValuesComponent);
