import { ButtonOwnProps, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { SOIL_PERCENTAGE_VALUES } from '../../constants';
import { Button } from '@ui/components/Button/Button';
import { useFloors, useFloorsActions } from '../../stores/FloorsStore';
import { memo } from 'react';
import { useHeatDesignUI } from '../../stores/HeatDesignUIStore';

function getSoilButtonVariant(storedSoilValue: null | number, buttonValue: number): ButtonOwnProps['variant'] {
  if (storedSoilValue === null) return 'invalid';
  if (buttonValue === storedSoilValue) return 'contained';
  return 'outlined';
}

function SoilLevelComponent() {
  const floors = useFloors();

  const floorActions = useFloorsActions();
  const { formatMessage } = useIntl();
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floor = floors.find((f) => f.uid === selectedFloorId);
  if (!floor) {
    return null;
  }
  const updateSoilPercentageDefault = (newValue: number) => {
    floorActions.updateFloor({ ...floor, soilPercentageDefault: newValue });
  };

  return (
    <Stack gap={1}>
      <Typography
        mb={2}
        component="label"
        id="surface-contact-with-soil-label"
        display="inline-flex"
        gap={1}
        variant="body1"
      >
        <FormattedMessage id="heatDesign.floorDefaultsModal.soilInput.label" />
        <TooltipAira
          anchorProps={{
            style: { alignItems: 'flex-start' },
          }}
          placement="top-start"
          title={formatMessage({
            id: 'heatDesign.floorDefaultsModal.soilInput.tooltip',
          })}
        />
      </Typography>
      <Stack gap={2} direction="row" flexWrap="wrap" justifyContent="center">
        {SOIL_PERCENTAGE_VALUES.map((value) => (
          <Button
            key={value}
            label={`${value}%`}
            sx={{ padding: 1, borderRadius: 2, minWidth: '75px', height: '40px', flex: 1 }}
            onClick={() => updateSoilPercentageDefault(value)}
            size="medium"
            variant={getSoilButtonVariant(floor.soilPercentageDefault, value)}
            aria-labelledby="surface-contact-with-soil-label"
          />
        ))}
      </Stack>
    </Stack>
  );
}

export const SoilLevel = memo(SoilLevelComponent);
