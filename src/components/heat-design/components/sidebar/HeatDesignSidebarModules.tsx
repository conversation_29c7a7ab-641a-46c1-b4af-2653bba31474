import React, { ReactNode, useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Stack, Typography } from '@mui/material';
import { Chevron } from '@ui/components/Icons/Chevron/Chevron';
import { WarningIcon } from '../WarningIcon';
import { beige, surface } from '@ui/theme/colors';

export interface HeatDesignSidebarSubcategory {
  id: string;
  title: string;
  icon: ReactNode;
  component: ReactNode;
  hasWarning?: boolean;
  isDefaultExpanded?: boolean;
}

export interface HeatDesignSidebarCategory {
  id: string;
  title: string;
  icon: ReactNode;
  subcategories: HeatDesignSidebarSubcategory[];
  hasWarning?: boolean;
  isDefaultExpanded?: boolean;
}

export interface HeatDesignSidebarModulesProps {
  categories: HeatDesignSidebarCategory[];
}

export function HeatDesignSidebarModules({ categories }: HeatDesignSidebarModulesProps) {
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    categories.forEach((category) => {
      initial[category.id] = category.isDefaultExpanded ?? false;
    });
    return initial;
  });

  const [expandedSubcategories, setExpandedSubcategories] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    categories.forEach((category) => {
      category.subcategories.forEach((subcategory) => {
        initial[subcategory.id] = subcategory.isDefaultExpanded ?? false;
      });
    });
    return initial;
  });

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  const toggleSubcategory = (subcategoryId: string) => {
    setExpandedSubcategories((prev) => ({
      ...prev,
      [subcategoryId]: !prev[subcategoryId],
    }));
  };

  return (
    <Stack gap={3} sx={{ backgroundColor: beige[100], p: 0 }}>
      {categories.map((category) => (
        <Accordion
          sx={{ backgroundColor: '#fff', borderRadius: 2, padding: 1 }}
          key={category.id}
          expanded={expandedCategories[category.id]}
        >
          <AccordionSummary sx={{ padding: 0 }} onClick={() => toggleCategory(category.id)}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
              <Stack gap={2} direction="row" alignItems="center">
                {category.hasWarning && (
                  <WarningIcon x={2.5} y={2.5} iconWidth={24} iconHeight={24} canvasWidth={24} canvasHeight={24} />
                )}
                {category.icon}
                <Typography variant="body2">{category.title}</Typography>
              </Stack>
              <Chevron
                direction={expandedCategories[category.id] ? 'up' : 'down'}
                transitionDuration="200ms"
                height={24}
                width={24}
              />
            </Stack>
          </AccordionSummary>
          <AccordionDetails sx={{ mt: 2, p: 0 }}>
            <Stack gap={1}>
              {category.subcategories.map((subcategory) => (
                <Accordion
                  sx={{ backgroundColor: surface[100], borderRadius: 2, padding: 1 }}
                  key={subcategory.id}
                  expanded={expandedSubcategories[subcategory.id]}
                >
                  <AccordionSummary sx={{ padding: 0 }} onClick={() => toggleSubcategory(subcategory.id)}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
                      <Stack gap={2} direction="row" alignItems="center">
                        {subcategory.hasWarning && (
                          <WarningIcon
                            x={2.5}
                            y={2.5}
                            iconWidth={24}
                            iconHeight={24}
                            canvasWidth={24}
                            canvasHeight={24}
                          />
                        )}
                        {subcategory.icon}
                        <Typography variant="body2">{subcategory.title}</Typography>
                      </Stack>
                      <Chevron
                        direction={expandedSubcategories[subcategory.id] ? 'up' : 'down'}
                        transitionDuration="200ms"
                        height={24}
                        width={24}
                      />
                    </Stack>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 0, mt: 2 }}>{subcategory.component}</AccordionDetails>
                </Accordion>
              ))}
            </Stack>
          </AccordionDetails>
        </Accordion>
      ))}
    </Stack>
  );
}
