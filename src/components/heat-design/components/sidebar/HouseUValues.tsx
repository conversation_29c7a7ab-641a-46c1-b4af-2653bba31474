import { memo } from 'react';
import { useIntl } from 'react-intl';
import { useConstructionYear } from '../../stores/HouseInputsStore';
import { useProjectUValues, useUValuesActions } from '../../stores/UValuesStore';
import { PROJECT_FABRIC_TYPES, ProjectFabricTypes } from '../../stores/types';
import { UValue } from '../../models/UValue';
import { UValueInput } from '../u-value-input/UValueInput';
import { Stack } from '@mui/material';
import { useSidebarContext } from './SidebarContext';

function UValuesComponent() {
  const { formatMessage } = useIntl();
  const constructionYear = useConstructionYear();
  const projectUValues = useProjectUValues();
  const uValuesActions = useUValuesActions();
  const { errors } = useSidebarContext();

  const handleUValueSelection = (fabricType: ProjectFabricTypes, uValue?: UValue) => {
    if (!uValue) return;
    uValuesActions.setProjectUValue({ fabricType, uValue });
  };

  return (
    <Stack gap={2}>
      {PROJECT_FABRIC_TYPES.map((fabricType) => {
        return (
          <UValueInput
            key={fabricType}
            surfaceType={fabricType}
            placeholder={formatMessage({ id: 'heatDesign.uValues.placeholder' })}
            value={projectUValues[fabricType]}
            label={formatMessage({ id: `heatDesign.roomSurfaceTypes.${fabricType}` })}
            disableClearable
            error={errors.houseUValueErrors.has(fabricType)}
            onChange={(newUValue) => handleUValueSelection(fabricType, newUValue)}
            data-testid={`heat-design-uvalue-modal-select-${fabricType}`}
            inputProps={{ 'data-testid': `heat-design-uvalue-modal-input-${fabricType}` }}
            constructionYear={constructionYear}
          />
        );
      })}
    </Stack>
  );
}

export const HouseUValues = memo(UValuesComponent);
