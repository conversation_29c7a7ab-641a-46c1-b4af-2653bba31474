import { createContext, ReactNode, useContext, useMemo } from 'react';
import { lookupUValueForSurfaceType, projectUValuesDefaultsAreValid, aRoomHasUValueForProjectFabricType, aRoomHasUValueForFabricType } from '../../utils/helpers';
import { propertyIsValid } from '../../Validator';
import { useHeatDesignHouseInputsStore } from '../../stores/HouseInputsStore';
import { useUValuesStore } from '../../stores/UValuesStore';
import { useFloors } from '../../stores/FloorsStore';
import { useRooms, useGetRoomsByFloor } from '../../stores/RoomsStore';
import { useHeatDesignUI } from '../../stores/HeatDesignUIStore';
import { ROOM_FABRIC_TYPES, PROJECT_FABRIC_TYPES, ProjectFabricTypes, RoomFabricTypes } from '../../stores/types';
import { isNotNullish } from '../../../../utils/isNotNullish';

export interface SidebarErrors {
  uValues: boolean;
  acphDefault: boolean;
  pulseTest: boolean;
  soilPercentage: boolean;
  floorUValues: boolean;
  houseUValueErrors: { [key: string]: boolean };
  floorUValueErrors: { [key: string]: boolean };
}

export interface SidebarWarnings {
  hasHouseDefaultWarning: boolean;
  hasHouseVentilationWarning: boolean;
  hasFloorDefaultsWarning: boolean;
  hasAnyWarning: boolean;
  hasUValuesWarning: boolean;
  hasSoilWarning: boolean;
  hasFloorUValuesWarning: boolean;
}

export interface SidebarContextType {
  errors: SidebarErrors;
  warnings: SidebarWarnings;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: { children: ReactNode }) {
  const rooms = useRooms();
  const floors = useFloors();
  const { projectUValues } = useUValuesStore();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floor = floors.find((f) => f.uid === selectedFloorId);
  const roomsByFloor = useGetRoomsByFloor();
  const roomsInFloor = roomsByFloor[floor?.uid ?? ''];

  const errors = useMemo((): SidebarErrors => {
    // Calculate house UValue errors
    const houseUValueErrors: { [key: string]: boolean } = {};
    PROJECT_FABRIC_TYPES.forEach((fabricType) => {
      const fabricTypeIsPresent = aRoomHasUValueForProjectFabricType(fabricType, rooms, floors);
      houseUValueErrors[fabricType] = fabricTypeIsPresent && !projectUValues[fabricType];
    });

    // Calculate floor UValue errors
    const floorUValueErrors: { [key: string]: boolean } = {};
    if (floor && roomsInFloor) {
      ROOM_FABRIC_TYPES.forEach((fabricType) => {
        const uValueLookup = lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors);
        floorUValueErrors[fabricType] = !uValueLookup?.uValue;
      });
    }

    return {
      uValues: !projectUValuesDefaultsAreValid(projectUValues, rooms, floors),
      acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
      pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
      soilPercentage: !isNotNullish(floor?.soilPercentageDefault),
      floorUValues: !ROOM_FABRIC_TYPES.every((fabricType) => {
        if (!floor) {
          return false;
        }
        return !!lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors)?.uValue;
      }),
      houseUValueErrors,
      floorUValueErrors,
    };
  }, [floor, floors, projectUValues, rooms, roomsInFloor, ventilationDesign]);

  const warnings = useMemo((): SidebarWarnings => {
    const hasUValuesWarning = Object.values(errors.houseUValueErrors).some(Boolean);
    const hasFloorUValuesWarning = Object.values(errors.floorUValueErrors).some(Boolean);
    const hasHouseDefaultWarning = hasUValuesWarning || errors.acphDefault || errors.pulseTest;
    const hasHouseVentilationWarning = errors.acphDefault || errors.pulseTest;
    const hasFloorDefaultsWarning = errors.soilPercentage || hasFloorUValuesWarning;
    const hasSoilWarning = errors.soilPercentage;
    const hasAnyWarning =
      hasHouseDefaultWarning ||
      hasFloorDefaultsWarning ||
      hasUValuesWarning ||
      hasSoilWarning ||
      hasFloorUValuesWarning;

    return {
      hasHouseDefaultWarning,
      hasHouseVentilationWarning,
      hasFloorDefaultsWarning,
      hasAnyWarning,
      hasUValuesWarning,
      hasSoilWarning,
      hasFloorUValuesWarning,
    };
  }, [errors]);

  const contextValue = useMemo(
    (): SidebarContextType => ({
      errors,
      warnings,
    }),
    [errors, warnings],
  );

  return <SidebarContext.Provider value={contextValue}>{children}</SidebarContext.Provider>;
}

export function useSidebarContext(): SidebarContextType {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebarContext must be used within a SidebarProvider');
  }
  return context;
}
