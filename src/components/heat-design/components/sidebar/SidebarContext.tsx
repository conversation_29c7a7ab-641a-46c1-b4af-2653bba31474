import { createContext, ReactNode, useContext, useMemo } from 'react';
import {
  aRoomHasUValueForProjectFabricType,
  lookupUValueForSurfaceType,
  projectUValuesDefaultsAreValid,
} from '../../utils/helpers';
import { propertyIsValid } from '../../Validator';
import { useHeatDesignHouseInputsStore } from '../../stores/HouseInputsStore';
import { useUValuesStore } from '../../stores/UValuesStore';
import { useFloors } from '../../stores/FloorsStore';
import { useGetRoomsByFloor, useRooms } from '../../stores/RoomsStore';
import { useHeatDesignUI } from '../../stores/HeatDesignUIStore';
import { PROJECT_FABRIC_TYPES, ROOM_FABRIC_TYPES } from '../../stores/types';
import { isNotNullish } from '../../../../utils/isNotNullish';

export interface SidebarErrors {
  acphDefault: boolean;
  pulseTest: boolean;
  soilPercentage: boolean;
  houseUValueErrors: Set<string>;
  floorUValueErrors: { [key: string]: boolean };
}

export interface SidebarWarnings {
  hasHouseDefaultWarning: boolean;
  hasHouseVentilationWarning: boolean;
  hasFloorDefaultsWarning: boolean;
  hasAnyWarning: boolean;
  hasUValuesWarning: boolean;
  hasSoilWarning: boolean;
  hasFloorUValuesWarning: boolean;
}

export interface SidebarContextType {
  errors: SidebarErrors;
  warnings: SidebarWarnings;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: { children: ReactNode }) {
  const rooms = useRooms();
  const floors = useFloors();
  const { projectUValues } = useUValuesStore();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floor = floors.find((f) => f.uid === selectedFloorId);
  const roomsByFloor = useGetRoomsByFloor();
  const roomsInFloor = roomsByFloor[floor?.uid ?? ''];

  const errors = useMemo((): SidebarErrors => {
    // Calculate house UValue errors as a Set
    const houseUValueErrors = new Set<string>();
    PROJECT_FABRIC_TYPES.forEach((fabricType) => {
      const fabricTypeIsPresent = aRoomHasUValueForProjectFabricType(fabricType, rooms, floors);
      if (fabricTypeIsPresent && !projectUValues[fabricType]) {
        houseUValueErrors.add(fabricType);
      }
    });

    // Calculate floor UValue errors
    const floorUValueErrors: { [key: string]: boolean } = {};
    if (floor && roomsInFloor) {
      ROOM_FABRIC_TYPES.forEach((fabricType) => {
        const uValueLookup = lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors);
        floorUValueErrors[fabricType] = !uValueLookup?.uValue;
      });
    }

    return {
      acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
      pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
      soilPercentage: !isNotNullish(floor?.soilPercentageDefault),
      houseUValueErrors,
      floorUValueErrors,
    };
  }, [floor, floors, projectUValues, rooms, roomsInFloor, ventilationDesign]);

  const warnings = useMemo((): SidebarWarnings => {
    const hasUValuesWarning = errors.houseUValueErrors.size > 0;
    const hasFloorUValuesWarning = Object.values(errors.floorUValueErrors).some(Boolean);
    const hasHouseDefaultWarning = hasUValuesWarning || errors.acphDefault || errors.pulseTest;
    const hasHouseVentilationWarning = errors.acphDefault || errors.pulseTest;
    const hasFloorDefaultsWarning = errors.soilPercentage || hasFloorUValuesWarning;
    const hasSoilWarning = errors.soilPercentage;
    const hasAnyWarning =
      hasHouseDefaultWarning ||
      hasFloorDefaultsWarning ||
      hasUValuesWarning ||
      hasSoilWarning ||
      hasFloorUValuesWarning;

    return {
      hasHouseDefaultWarning,
      hasHouseVentilationWarning,
      hasFloorDefaultsWarning,
      hasAnyWarning,
      hasUValuesWarning,
      hasSoilWarning,
      hasFloorUValuesWarning,
    };
  }, [errors]);

  const contextValue = useMemo(
    (): SidebarContextType => ({
      errors,
      warnings,
    }),
    [errors, warnings],
  );

  return <SidebarContext.Provider value={contextValue}>{children}</SidebarContext.Provider>;
}

export function useSidebarContext(): SidebarContextType {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebarContext must be used within a SidebarProvider');
  }
  return context;
}
