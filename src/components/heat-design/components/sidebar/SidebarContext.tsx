import { createContext, ReactNode, useContext, useMemo } from 'react';
import { lookupUValueForSurfaceType, projectUValuesDefaultsAreValid } from '../../utils/helpers';
import { propertyIsValid } from '../../Validator';
import { useHeatDesignHouseInputsStore } from '../../stores/HouseInputsStore';
import { useUValuesStore } from '../../stores/UValuesStore';
import { useFloors } from '../../stores/FloorsStore';
import { useRooms } from '../../stores/RoomsStore';
import { useHeatDesignUI } from '../../stores/HeatDesignUIStore';
import { ROOM_FABRIC_TYPES } from '../../stores/types';
import { isNotNullish } from '../../../../utils/isNotNullish';

export interface SidebarErrors {
  uValues: boolean;
  acphDefault: boolean;
  pulseTest: boolean;
  soilPercentage: boolean;
  floorUValues: boolean;
}

export interface SidebarWarnings {
  hasHouseDefaultWarning: boolean;
  hasHouseVentilationWarning: boolean;
  hasFloorDefaultsWarning: boolean;
  hasAnyWarning: boolean;
  hasUValuesWarning: boolean;
  hasSoilWarning: boolean;
  hasFloorUValuesWarning: boolean;
}

export interface SidebarContextType {
  errors: SidebarErrors;
  warnings: SidebarWarnings;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export function SidebarProvider({ children }: { children: ReactNode }) {
  const rooms = useRooms();
  const floors = useFloors();
  const { projectUValues } = useUValuesStore();
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const selectedFloorId = useHeatDesignUI((s) => s.selectedFloorId);
  const floor = floors.find((f) => f.uid === selectedFloorId);

  const errors = useMemo((): SidebarErrors => {
    return {
      uValues: !projectUValuesDefaultsAreValid(projectUValues, rooms, floors),
      acphDefault: !propertyIsValid('dwelling', 'acphDefault', ventilationDesign.acphDefault),
      pulseTest: !propertyIsValid('dwelling', 'pulseTest', ventilationDesign),
      soilPercentage: !isNotNullish(floor?.soilPercentageDefault),
      floorUValues: !ROOM_FABRIC_TYPES.every((fabricType) => {
        if (!floor) {
          return false;
        }
        return !!lookupUValueForSurfaceType(fabricType, projectUValues, floor, floors)?.uValue;
      }),
    };
  }, [floor, floors, projectUValues, rooms, ventilationDesign]);

  const warnings = useMemo((): SidebarWarnings => {
    const hasHouseDefaultWarning = errors.uValues || errors.acphDefault || errors.pulseTest;
    const hasHouseVentilationWarning = errors.acphDefault || errors.pulseTest;
    const hasFloorDefaultsWarning = errors.soilPercentage || errors.floorUValues;
    const hasSoilWarning = errors.soilPercentage;
    const hasFloorUValuesWarning = errors.floorUValues;
    const hasUValuesWarning = errors.uValues;
    const hasAnyWarning =
      hasHouseDefaultWarning ||
      hasFloorDefaultsWarning ||
      hasUValuesWarning ||
      hasSoilWarning ||
      hasFloorUValuesWarning;

    return {
      hasHouseDefaultWarning,
      hasHouseVentilationWarning,
      hasFloorDefaultsWarning,
      hasAnyWarning,
      hasUValuesWarning,
      hasSoilWarning,
      hasFloorUValuesWarning,
    };
  }, [errors]);

  const contextValue = useMemo(
    (): SidebarContextType => ({
      errors,
      warnings,
    }),
    [errors, warnings],
  );

  return <SidebarContext.Provider value={contextValue}>{children}</SidebarContext.Provider>;
}

export function useSidebarContext(): SidebarContextType {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebarContext must be used within a SidebarProvider');
  }
  return context;
}
