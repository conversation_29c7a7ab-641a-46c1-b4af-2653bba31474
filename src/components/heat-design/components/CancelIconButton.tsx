import { grey, surface } from '@ui/theme/colors';
import { IconButton } from '@mui/material';
import { IconButtonProps } from '@mui/material/IconButton/IconButton';
import { memo } from 'react';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';

function CancelIconButtonComponent(props: IconButtonProps) {
  return (
    <IconButton
      {...props}
      sx={{
        width: '40px',
        height: '40px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px',
        borderRadius: '50%',
        color: 'black',
        backgroundColor: surface[100],
        cursor: 'pointer',
        '&:hover': { backgroundColor: grey[300] },
        transition: 'background-color 0.2s',
        ...(props.sx ?? {}),
      }}
    >
      <CrossOutlinedIcon color="black" />
    </IconButton>
  );
}

export const CancelIconButton = memo(CancelIconButtonComponent);
