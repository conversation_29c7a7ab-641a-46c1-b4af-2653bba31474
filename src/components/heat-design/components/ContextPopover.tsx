import { Box, ClickAwayListener, Paper, Popper, PopperPlacementType, styled } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { theme } from '@ui/theme/theme';
import React from 'react';

interface Props {
  anchorElement: null | HTMLElement;
  children: React.ReactNode;
  placement?: PopperPlacementType;
  onClose: () => void;
}

const PopoverRootPaper = styled(Paper)({
  backgroundColor: beige[100],
  maxWidth: 400,
});

const ContentBox = styled(Box)({
  padding: theme.spacing(0),
});

// The plan for this component was to be a popover with an arrow that points to the anchor element. The best
// base component to build this on top of appears to be <PERSON><PERSON>. However, I didn't succeed in making the arrow
// style nicely. Future people could take inspiration from this Stack Overflow answer (which is based on a
// previous version of MUI): https://stackoverflow.com/a/64045289

export function ContextPopover({ anchorElement, children, onClose, placement = 'bottom' }: Props) {
  return (
    <Popper
      sx={{ zIndex: theme.zIndex.modal }}
      open={Boolean(anchorElement)}
      anchorEl={anchorElement}
      placement={placement}
      modifiers={[
        {
          name: 'preventOverflow',
          options: {
            rootBoundary: 'window',
          },
        },
        {
          name: 'offset',
          options: {
            offset: [0, 8],
          },
        },
      ]}
    >
      <ClickAwayListener onClickAway={onClose}>
        <Box>
          <PopoverRootPaper>
            <ContentBox>{children}</ContentBox>
          </PopoverRootPaper>
        </Box>
      </ClickAwayListener>
    </Popper>
  );
}
