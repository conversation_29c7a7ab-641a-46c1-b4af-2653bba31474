import { brandYellow, grey } from '@ui/theme/colors';
import { HEAT_DESIGN_FLOOR_PLAN_DIMENSION } from '@ui/theme/constants';
import React from 'react';
import { Room } from '../stores/types';
import { coordinateListToCoordinatePairs } from '../utils/helpers';

export function WarningIcon({
  x,
  y,
  iconWidth,
  iconHeight,
  canvasWidth,
  canvasHeight,
  containerFill = brandYellow[400],
  exclamationFill = grey[900],
}: {
  x: number;
  y: number;
  iconWidth: number;
  iconHeight: number;
  canvasWidth: number;
  canvasHeight: number;
  containerFill?: string;
  exclamationFill?: string;
}) {
  const paddingX = iconWidth / 5;
  const paddingY = iconHeight / 5;

  const paddedIconWidth = iconWidth - paddingX;
  const paddedIconHeight = iconHeight - paddingY;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox={`0 0 ${canvasWidth} ${canvasHeight}`}
      width={canvasWidth}
      height={canvasHeight}
      preserveAspectRatio="xMidYMid meet"
      data-testid="warning-icon"
    >
      <rect
        x={x}
        y={y}
        width={paddedIconWidth}
        height={paddedIconHeight}
        rx="5"
        ry="5"
        fill={containerFill}
        transform={`rotate(45 ${x + paddedIconWidth / 2} ${y + paddedIconHeight / 2})`}
      />
      <text
        x={x + paddedIconWidth / 2}
        y={y + paddedIconHeight / 2}
        fontSize={(paddedIconWidth + paddedIconHeight) / 3}
        fontWeight="bold"
        textAnchor="middle"
        dominantBaseline="middle"
        fill={exclamationFill}
      >
        !
      </text>
    </svg>
  );
}

/**
 * Given a room, calculates the positioning of an SVG in the lower left corner
 * of the room on a floor plan.
 */
export function generateWarningIconForRoom(room: Room): React.JSX.Element {
  const coordinateList = room?.imageMap?.coordinates;
  const coordinatePairs = coordinateListToCoordinatePairs(coordinateList);

  // Maximum y and minimum x = bottom left corner
  coordinatePairs.sort(([x1, y1], [x2, y2]) => y2 - y1 + x1 - x2);

  const idealCoordinate: [number, number] = coordinatePairs[0]!;
  const padding: [number, number] = [5, -25]; // [x, y]

  const x = idealCoordinate[0] + padding[0];
  const y = idealCoordinate[1] + padding[1];

  return (
    <WarningIcon
      key={`warning-${room.id}`}
      x={x}
      y={y}
      iconWidth={25}
      iconHeight={25}
      canvasWidth={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
      canvasHeight={HEAT_DESIGN_FLOOR_PLAN_DIMENSION}
    />
  );
}
