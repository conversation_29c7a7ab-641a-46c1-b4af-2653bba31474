import React, { ReactNode, useEffect } from 'react';
import { useGroundwork } from 'context/groundwork-context';
import { getCountryEnum } from 'utils/marketConfigurations';
import { useSetClimateData } from 'components/heat-design/stores/ClimateDataStore';
import { api } from 'utils/api';
import ClimateDataLoadingCard from './ClimateDataLoadingCard';

export function ClimateDataLoaderPostalCode({ children }: { children: ReactNode }) {
  const { groundwork, countryCode } = useGroundwork();
  const setClimateData = useSetClimateData();
  const { location } = groundwork;
  const { postalCode } = (location?.$case === 'exactAddress' && location.exactAddress) || {};

  const { data, isLoading, error } = api.AiraBackend.getPostalCodeClimate.useQuery(
    { country: getCountryEnum(countryCode), postalCode: postalCode! },
    { enabled: !!countryCode && !!postalCode },
  );

  const hasError = !data && !!error;

  useEffect(() => {
    if (data?.climate) {
      setClimateData({
        altitude: data.climate.altitude,
        degreeDays: data.climate.heatingDegreeDays,
        baseOutdoorDesignTemperature: data.climate.externalDesignTemperature,
        localAnnualAverageExternalAirTemperature: data.climate.averageExternalTemperature,
        zone: data.climate.climateZone,
      });
    }
  }, [data, setClimateData]);

  return (
    <ClimateDataLoadingCard loading={isLoading} error={hasError} errorMsg={error?.message}>
      {children}
    </ClimateDataLoadingCard>
  );
}
