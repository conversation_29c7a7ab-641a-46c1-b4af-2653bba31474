import React, { ReactNode } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { Typography } from '@mui/material';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useGroundwork } from 'context/groundwork-context';
import { HeatDesignErrorInfo } from '../errors/HeatDesignErrorInfo';
import { ErrorLayout } from '../ErrorLayout';

type LoadingErrorProps = {
  loading: boolean;
  error: boolean;
  errorMsg?: string;
  children: ReactNode;
};

export default function ClimateDataLoadingCard({ loading, error, errorMsg, children }: LoadingErrorProps) {
  const { formatMessage } = useIntl();
  const energySolutionId = useEnergySolutionId();
  const {
    groundwork: { id: groundworkId },
  } = useGroundwork();
  if (loading) {
    return (
      <HeatPumpLoader>
        <Typography variant="h6">Loading climate data...</Typography>
        <Typography variant="body1">This can take a few seconds</Typography>
      </HeatPumpLoader>
    );
  }

  if (error) {
    return (
      <ErrorLayout
        heading={<FormattedMessage id="heatDesign.climate.data.error.title" />}
        details={
          <p>
            <HeatDesignErrorInfo
              description={formatMessage({ id: 'heatDesign.climate.data.error.title' })}
              solutionId={energySolutionId}
              groundworkId={groundworkId?.value}
            />
            {errorMsg && (
              <Typography mt={2} sx={{ overflowWrap: 'break-word' }}>
                Error Message: {errorMsg}
              </Typography>
            )}
          </p>
        }
      >
        <StyledFormattedMessage id="heatDesign.climate.data.error" />
      </ErrorLayout>
    );
  }
  return children;
}
