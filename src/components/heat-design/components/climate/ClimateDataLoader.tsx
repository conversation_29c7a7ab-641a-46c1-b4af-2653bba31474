import React, { ReactNode } from 'react';
import { useGroundwork } from 'context/groundwork-context';
import { CountryCode } from 'utils/marketConfigurations';
import { ClimateDataLoaderPostalCode } from './ClimateDataLoaderPostalCode';
import ClimateDataLoaderAreaCode from './ClimateDataLoaderAreaCode';

export function ClimateDataLoader({ children }: { children: ReactNode }) {
  const { countryCode } = useGroundwork();

  return countryCode === CountryCode.IT ? (
    <ClimateDataLoaderAreaCode>{children}</ClimateDataLoaderAreaCode>
  ) : (
    <ClimateDataLoaderPostalCode>{children}</ClimateDataLoaderPostalCode>
  );
}
