import { ReactNode, useEffect, useRef, useState } from 'react';
import { useGroundwork } from 'context/groundwork-context';
import { useServerEnvironment } from 'context/server-environment-context';
import { api } from 'utils/api';
import { getCountryEnum } from 'utils/marketConfigurations';
import { useSetClimateData } from 'components/heat-design/stores/ClimateDataStore';
import { useFilterUValuesByClimateZone } from 'components/heat-design/stores/UValuesStore';
import { ExactAddress } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.address.v1';
import ClimateDataLoadingCard from './ClimateDataLoadingCard';

const loadGoogleMapsScript = (googleMapsApiKey: string) =>
  new Promise<void>((resolve) => {
    if (typeof window !== 'undefined' && !document.querySelector('#google-maps') && googleMapsApiKey) {
      const script = document.createElement('script');
      script.id = 'google-maps';
      script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
      script.onload = () => {
        resolve();
      };
      document.querySelector('head')?.appendChild(script);
    } else {
      resolve();
    }
  });

function getPlaceId(address?: ExactAddress) {
  if (address?.identifier?.identifier?.$case === 'googlePlace') {
    return address.identifier.identifier.googlePlace.placeId;
  }
  return undefined;
}

export default function ClimateDataLoaderAreaCode({ children }: { children: ReactNode }) {
  const { groundwork, countryCode } = useGroundwork();
  const { location } = groundwork;
  const setClimateData = useSetClimateData();
  const filterUValuesByClimateZone = useFilterUValuesByClimateZone();
  const address = location?.$case === 'exactAddress' ? location.exactAddress : undefined;
  const [areaCode, setAreaCode] = useState<string>();
  const [placeId, setPlaceId] = useState<string | undefined>(getPlaceId(address));
  const [placesServiceInitialized, setPlacesServiceInitialized] = useState(false);

  const serverEnvironment = useServerEnvironment();
  const googleMapsApiKey = serverEnvironment?.googleApiKey;
  const loaded = useRef(false);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    if (!loaded.current && googleMapsApiKey) {
      loadGoogleMapsScript(googleMapsApiKey).then(() => {
        loaded.current = true;
        if (window?.google) {
          placesService.current = new window.google.maps.places.PlacesService(document.createElement('div'));
          setPlacesServiceInitialized(true);
        }
      });
    }
  }, [googleMapsApiKey]);

  useEffect(() => {
    if (placesServiceInitialized && placesService.current && placeId) {
      placesService.current.getDetails(
        {
          placeId,
          fields: ['name', 'address_component', 'geometry.location', 'formatted_address'],
        },
        (place, status) => {
          if (status === window.google.maps.places.PlacesServiceStatus.NOT_FOUND && address) {
            // Try refetching place ID
            placesService.current?.findPlaceFromQuery(
              {
                query: address.formattedAddress,
                fields: ['place_id'],
              },
              (places) => {
                if (places && places[0]) {
                  setPlaceId(places[0].place_id);
                }
              },
            );
          } else if (place) {
            const province = place.address_components?.find((component) =>
              component.types.includes('administrative_area_level_2'),
            )?.short_name;
            // In some situations (e.g. Ozzano Dell'Emilia), Google returns a backtick instead of an
            // apostrophe for the municipality. We initially tried replacing this by adding a climate
            // data entry with a backtick https://github.com/airahome/aira-web-backend/pull/2302, but
            // that did not work. So now we "correct" Google's response here. All our Italian climate
            // data in the database uses ' and not ` (apart from the one we just added).
            const municipality = place.address_components
              ?.find((component) => component.types.includes('administrative_area_level_3'))
              ?.short_name.replace('`', "'");
            setAreaCode(`${municipality}, ${province}`);
          }
        },
      );
    }
  }, [placesServiceInitialized, address, placeId]);

  const { data, isLoading, error } = api.AiraBackend.getAreaCodeClimate.useQuery(
    { country: getCountryEnum(countryCode), areaCode: areaCode! },
    { enabled: !!countryCode && !!areaCode },
  );

  useEffect(() => {
    if (data?.climate) {
      setClimateData({
        altitude: data.climate.altitude,
        degreeDays: data.climate.heatingDegreeDays,
        baseOutdoorDesignTemperature: data.climate.externalDesignTemperature,
        localAnnualAverageExternalAirTemperature: data.climate.averageExternalTemperature,
        zone: data.climate.climateZone,
      });

      if (data.climate.climateZone) {
        filterUValuesByClimateZone(data.climate.climateZone);
      }
    }
  }, [data, setClimateData, filterUValuesByClimateZone]);

  const hasError = !data && !!error;
  return (
    <ClimateDataLoadingCard loading={isLoading} error={hasError} errorMsg={error?.message}>
      {children}
    </ClimateDataLoadingCard>
  );
}
