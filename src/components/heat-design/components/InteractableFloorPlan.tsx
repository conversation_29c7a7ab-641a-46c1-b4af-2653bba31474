import { I<PERSON><PERSON><PERSON><PERSON>, Stack, SxProps } from '@mui/material';
import { grey } from '@mui/material/colors';
import { Box } from '@mui/system';
import { Fullscreen, ZoomIn, ZoomOut } from '@ui/components/Icons/material';
import { SVGImage } from 'components/heat-design/SVGImage';
import React from 'react';
import { MapInteractionCSS } from 'react-map-interaction';
import { FloorProps } from '../stores/types';

export type InteractableFloorPlanProps = {
  floor: FloorProps;
  children?: React.ReactNode;
  sx?: SxProps;
};

const DEFAULT_SCALE = 0.8;
const DEFAULT_TRANSLATION = { x: 0, y: 40 };
const FLOOR_PLAN_BUTTON_COMMON_STYLING = {
  ml: 1,
  mt: 1,
  zIndex: 1,
  borderRadius: 1,
  backgroundColor: grey[200],
  border: 0,
  '&:hover': {
    backgroundColor: grey[300],
  },
};

export default function InteractableFloorPlan({ floor, children, sx }: InteractableFloorPlanProps) {
  const [scale, setScale] = React.useState(DEFAULT_SCALE);
  const [translation, setTranslation] = React.useState(DEFAULT_TRANSLATION);
  return (
    <Box
      sx={{
        flexGrow: 1,
        borderRadius: 2,
        background: 'white',
        mt: 4,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        position: 'relative',
        width: '100%',
        minHeight: '80vh',
        ...sx,
        '> div': {
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
        },
        '> div > div': {
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
        },
        '> div > div > div': {
          // This ensures that the SVG is centred. We have to use !important as this is otherwise
          // controlled by the react-map-interaction package that we use for this functionality.
          height: `${100 / DEFAULT_SCALE - DEFAULT_TRANSLATION.y}% !important`,
          width: `${100 / DEFAULT_SCALE - DEFAULT_TRANSLATION.x}% !important`,
          display: 'flex !important',
          justifyContent: 'center !important',
        },
        '.controls': {
          display: 'flex',
          flexDirection: 'row-reverse',
          gap: '55px',
          top: '0px',
          position: 'absolute',
        },
        '.floorPlanButton': {
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '40px',
          width: '40px',
          ...FLOOR_PLAN_BUTTON_COMMON_STYLING,
        },
      }}
    >
      <IconButton
        sx={{
          position: 'absolute',
          left: `50px`,
          ...FLOOR_PLAN_BUTTON_COMMON_STYLING,
        }}
        onClick={() => {
          setScale(DEFAULT_SCALE);
          setTranslation(DEFAULT_TRANSLATION);
        }}
        color="primary"
        disableRipple
      >
        <Fullscreen />
      </IconButton>
      <MapInteractionCSS
        showControls
        controlsClass="controls"
        minusBtnClass="floorPlanButton"
        plusBtnClass="floorPlanButton"
        minusBtnContents={<ZoomOut />}
        plusBtnContents={<ZoomIn />}
        value={{ scale, translation }}
        onChange={({ scale: newScale, translation: newTranslation }) => {
          setScale(newScale);
          setTranslation(newTranslation);
        }}
      >
        <Stack sx={{ position: 'absolute', overflow: 'auto' }}>
          <SVGImage svgData={floor.imageSvgData} alt="floor" />
          {children}
        </Stack>
      </MapInteractionCSS>
    </Box>
  );
}
