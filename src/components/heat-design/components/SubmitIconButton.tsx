import { brandYellow, grey, surface } from '@ui/theme/colors';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { IconButton } from '@mui/material';
import { IconButtonProps } from '@mui/material/IconButton/IconButton';
import { memo } from 'react';

function SubmitIconButtonComponent(props: IconButtonProps) {
  return (
    <IconButton
      {...props}
      sx={{
        width: '40px',
        height: '40px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: '50%',
        pointerEvents: 'auto',
        cursor: 'pointer',
        opacity: '1',
        backgroundColor: brandYellow[400],
        '&:disabled': {
          backgroundColor: surface[100],
          pointerEvents: 'none',
        },
        '&:not(:disabled):hover': {
          backgroundColor: brandYellow[500],
        },
        transition: 'background-color 0.2s',
      }}
    >
      <CheckIconThin color={props.disabled ? grey[300] : grey[900]} />
    </IconButton>
  );
}

export const SubmitIconButton = memo(SubmitIconButtonComponent);
