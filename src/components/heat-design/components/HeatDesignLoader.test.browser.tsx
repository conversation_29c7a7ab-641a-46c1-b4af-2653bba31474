import { JobStatus, SkillLevel } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import { SurveyForm } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.form.v1';
import { SurveyRole } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { Skill } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { resetRouterMock } from '@mocks/next/router';
import { Typography } from '@mui/material';
import { cleanup, screen } from '@testing-library/react';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { RequestHandler } from 'msw';
import { SetupWorker, setupWorker } from 'msw/browser';
import { IntlProvider } from 'react-intl';
import { mocks } from 'tests/utils/mockedTrpcCalls';
import { mockRouterForHeatDesign, renderWithProviders, trpcMsw } from 'tests/utils/testUtils';
import { AerospaceError } from 'utils/errors/aerospaceError';
import untypedAsaHouse from '../../../tests/heat-loss/asa_house_response.json';
import { heatDesignTheme } from '../HeatDesignTheme';
import HeatDesignLoader from './HeatDesignLoader';

const SOLUTION_ID = '7bef655f-30f4-4299-8876-f9e5b1a695b5';
const FORM_INSTANCE_ID = '91797ffc-f0bf-475c-a9d5-e050d909cd68';
const EXTERNAL_PROJECT_ID = 'f8a1b9de-08e4-4ba2-855f-846920992473';

const surveyForm = {
  deeplinkToMobile: `magicplanstd://project/${EXTERNAL_PROJECT_ID}`,
  webUrl: `https://cloud.magicplan.app/estimator/projects/${EXTERNAL_PROJECT_ID}/overview`,
  formInstanceId: { value: FORM_INSTANCE_ID },
  referenceCode: '2wNTF1lNPp',
  submissionReports: [
    {
      url: `https://on-site-survey-reports-systest.s3.eu-north-1.amazonaws.com/${FORM_INSTANCE_ID}.pdf`,
      submittedAt: new Date(),
    },
  ],
} satisfies SurveyForm;

const pastTechnicalSurvey = {
  assignedResources: [
    {
      email: '<EMAIL>',
      name: 'Surveyor name for past technical survey',
      roles: [],
      skillLevel: SkillLevel.SKILL_LEVEL_SPECIALIST,
      skills: [],
      userId: { value: '7f206f35-6552-427a-b608-e4c961bee401' },
    },
  ],
  durationMinutes: 120,
  end: new Date('2025-07-31T10:00:00Z'),
  jobType: undefined,
  notes: undefined,
  requiredSkills: [Skill.SKILL_HEAT_PUMP],
  roles: [SurveyRole.SURVEY_ROLE_TECHNICAL],
  scheduled: true,
  start: new Date('2025-07-31T08:00:00Z'),
  status: JobStatus.JOB_STATUS_READY,
  surveyId: 'ee3c0fbc-24f3-432c-a879-e27a4331b3c9',
  timeConstraint: undefined,
  webLinkToJob: 'https://aira-uat.my.skedulo.com/job/81b85234-3291-4081-a9fe-6c2ecff7d15d/details',
};

const futureTechnicalSurvey = {
  assignedResources: [
    {
      email: '<EMAIL>',
      name: 'Surveyor name for future technical survey',
      roles: [],
      skillLevel: SkillLevel.SKILL_LEVEL_SPECIALIST,
      skills: [],
      userId: { value: 'cf6f4dca-d6c0-449b-bc2a-36bb31dc2960' },
    },
  ],
  durationMinutes: 120,
  end: new Date('2025-08-15T10:00:00Z'),
  jobType: undefined,
  notes: undefined,
  requiredSkills: [Skill.SKILL_HEAT_PUMP],
  roles: [SurveyRole.SURVEY_ROLE_TECHNICAL],
  scheduled: true,
  start: new Date('2025-08-15T08:00:00Z'),
  status: JobStatus.JOB_STATUS_READY,
  surveyId: '0d36faf4-368b-4bb2-8922-2c28e8367fba',
  timeConstraint: undefined,
  webLinkToJob: 'https://aira-uat.my.skedulo.com/job/a7aa0185-e822-43dc-88ba-41880e6b981e/details',
};

const BASE_WORKER = setupWorker(
  mocks.getGroundworkForSolution.asa,
  trpcMsw.AiraBackend.getSurveyForms.query(() => ({
    surveyForms: [surveyForm],
  })),
  trpcMsw.Survey.listSurveyBookings.query(() => [pastTechnicalSurvey, futureTechnicalSurvey]),
);

const LOAD_HEAT_DESIGN_SUCCESS = trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
  heatDesign: untypedAsaHouse as unknown as ProtoHeatDesign,
  isLocked: false,
  result: undefined,
  updatedAt: new Date(),
  events: [],
}));

const LOAD_HEAT_DESIGN_NO_MAGICPLAN_SUBMISSION = trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => {
  throw new AerospaceError({
    code: 'NOT_FOUND',
    message: 'No Magicplan project submission',
    details: {
      detailType: 'HEAT_DESIGN_NOT_FOUND_ERROR',
      notFound: 'submission',
      level: 'informational',
    },
  });
});

const worker: SetupWorker = BASE_WORKER;
const setupTest = async (...extraHandlers: Array<RequestHandler>) => {
  worker.use(...extraHandlers);

  renderWithProviders(
    <IntlProvider locale="en-GB" defaultLocale="en-GB" onError={() => {}}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <AiraThemeProvider theme={heatDesignTheme}>
          <HeatDesignLoader>
            <Typography data-testid="childContent" variant="h1">
              Hello, world!
            </Typography>
          </HeatDesignLoader>
        </AiraThemeProvider>
      </GroundworkContextProvider>
    </IntlProvider>,
  );
};

beforeAll(async () => {
  await worker.start({
    onUnhandledRequest: (req, print) => {
      if (req.url.includes('/fonts/')) {
        return;
      }
      print.warning();
    },
    quiet: true,
  });
});

beforeEach(() => {
  mockRouterForHeatDesign(SOLUTION_ID);
});

afterEach(() => {
  worker.resetHandlers();
  cleanup();
  vi.clearAllMocks();
  resetRouterMock();
});

afterAll(() => worker.stop());

describe('HeatDesignLoader', async () => {
  it('should show an informational message if the Magicplan has not been submitted', async () => {
    await setupTest(LOAD_HEAT_DESIGN_NO_MAGICPLAN_SUBMISSION);

    expect(await screen.findByText('heatDesign.error.noMagicplanSubmission.title')).toBeVisible();
    expect(await screen.findByText('heatDesign.magicplan.links.title')).toBeVisible();
    expect(await screen.findByText('heatDesign.error.noMagicplanSubmission.latestSurvey')).toBeVisible();
    expect(await screen.findByText(futureTechnicalSurvey.assignedResources[0]!.name)).toBeVisible();

    expect(screen.queryByTestId('childContent')).toBeNull();
  });

  it('should show the child content if the heat design loaded successfully', async () => {
    await setupTest(LOAD_HEAT_DESIGN_SUCCESS);
    expect(await screen.findByTestId('childContent')).toBeVisible();
  });
});
