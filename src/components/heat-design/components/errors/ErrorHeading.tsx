import { Box, Stack } from '@mui/material';
import { CloudExclamationOutlinedIcon } from '@ui/components/StandardIcons/CloudExclamationOutlinedIcon';
import { Heading } from '@ui/components/Heading/Heading';
import { red } from '@ui/theme/colors';
import { SVGProps } from 'react';
import { HeatDesignErrorLevel } from 'utils/errors/aerospaceError';

interface ErrorHeadingProps {
  icon?: (props: SVGProps<SVGSVGElement>) => React.JSX.Element;
  level?: HeatDesignErrorLevel;
  children: React.ReactNode;
}

export function ErrorHeading({ icon, level = 'error', children }: ErrorHeadingProps) {
  const Icon = icon || CloudExclamationOutlinedIcon;

  return (
    <Stack mb={2} justifyContent="center" alignContent="center" direction="row" gap={2}>
      <Box style={{ flex: '0 0 auto' }}>
        <Icon width={48} height={48} color={level === 'error' ? red[600] : undefined} />
      </Box>
      <Heading variant="headline1" level={1}>
        {children}
      </Heading>
    </Stack>
  );
}
