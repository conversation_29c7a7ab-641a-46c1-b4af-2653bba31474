import { Button, Typography } from '@mui/material';
import { Box, Stack } from '@mui/system';
import { ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';
import { AppError, isTRPCClientError } from 'utils/api';
import { getGrpcErrorDetails } from 'utils/errors/grpcErrorDetails';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { ErrorLayout } from '../ErrorLayout';

interface Props {
  handleRetry?: (() => void) | undefined;
  heading: ReactNode;
  error: unknown;
}

function ErrorDescription({ error }: { error: AppError }) {
  return <Typography>{error.message}</Typography>;
}

function ErrorSupportDetails({ error }: { error: AppError }) {
  const energySolutionId = useEnergySolutionId();
  const grpcErrorDetails = getGrpcErrorDetails(error);
  return (
    <Typography>
      Energy solution ID: {energySolutionId}
      <br />
      Path: {error.data.path}
      <br />
      Code: {error.data.code}
      <br />
      {grpcErrorDetails && (
        <>
          gRPC path: {grpcErrorDetails.path}
          <br />
          gRPC message: {grpcErrorDetails.details}
        </>
      )}
    </Typography>
  );
}

/**
 * This component is meant to be used in situations where some individual tRPC query fails,
 * but the rest of the page is still functional. So you can just hand it the error from the
 * tRPC query or mutation and it will display an appropriate error for the situation.
 *
 * @param {param0.handleRetry} handleRetry - If you wish to include a retry button, pass a function here to perform the retry.
 * @param {param0.error} error - The error object from the tRPC query or mutation.
 * @param {param0.heading} heading - The heading to display at the top of the error, giving the user some context.
 */
export function InlineQueryError({ handleRetry = undefined, error, heading }: Props) {
  return (
    <ErrorLayout
      variant="compact"
      heading={heading}
      details={isTRPCClientError(error) ? <ErrorSupportDetails error={error} /> : null}
    >
      <Stack direction="column" gap={1}>
        {isTRPCClientError(error) ? (
          <Box>
            <ErrorDescription error={error} />
          </Box>
        ) : (
          <FormattedMessage id="common.error.unknown" />
        )}
        {handleRetry && (
          <Button size="small" variant="outlined" sx={{ width: '90px' }} onClick={() => handleRetry()}>
            <FormattedMessage id="common.error.retry" />
          </Button>
        )}
      </Stack>
    </ErrorLayout>
  );
}
