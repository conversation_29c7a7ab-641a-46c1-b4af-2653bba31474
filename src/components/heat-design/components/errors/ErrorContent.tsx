import React, { useRef } from 'react';
import { Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { MsTeamsIcon } from '@ui/components/StandardIcons/MsTeamsIcon';
import ContentCopyIcon from '@ui/components/Icons/material/ContentCopy';
import HeatDesignCard from 'components/heat-design/HeatDesignCard';
import { useIntl } from 'react-intl';
import toast from 'react-hot-toast';
import { grey, purple } from '@ui/theme/colors';

type ErrorContentProps = {
  children?: React.ReactNode;
  details?: React.ReactNode | string;
  footer?: React.ReactNode;
};

export function ErrorContent({ children, details, footer }: ErrorContentProps) {
  const { formatMessage } = useIntl();

  const textRef = useRef<HTMLDivElement>(null);

  const handleCopy = () => {
    if (textRef === null || textRef.current === null) return;

    navigator.clipboard.writeText(textRef.current.innerText);
    toast.success(formatMessage({ id: 'common.notify.copySuccess' }));
  };

  const handleReportInTeams = () => {
    const teamsUrl = // this we should probably set in the env file? or a config file?
      'https://teams.microsoft.com/l/channel/19%3A805856c443824cfab54216bc07cd0708%40thread.tacv2/Support?groupId=1bcd3613-23dd-4049-ba88-f657923918c6&tenantId=0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4';
    window.open(teamsUrl, '_blank');
  };

  return (
    <>
      {children && (
        <HeatDesignCard variant="light" sx={{ whiteSpace: 'pre' }}>
          {children}
        </HeatDesignCard>
      )}
      {details && (
        <>
          <Typography mt={2} mb={1} variant="body2" sx={{ fontWeight: 700 }}>
            {formatMessage({ id: 'error.support.card.subTitle' })}:
          </Typography>
          <Stack
            gap={1}
            p={2}
            sx={{
              background: 'white',
              borderRadius: '10px',
              border: '1px solid',
              borderColor: grey[400],
              overflowWrap: 'break-word',
            }}
          >
            <Stack ref={textRef}>{details}</Stack>

            <Button
              size="small"
              variant="outlined"
              sx={{ background: 'white', width: '90px', fontSize: '14px', fontWeight: 400, lineHeight: '21px' }}
              onClick={handleCopy}
              startIcon={<ContentCopyIcon />}
            >
              {formatMessage({ id: 'common.label.copyText' })}
            </Button>
          </Stack>
        </>
      )}
      {footer ?? (
        <Button
          sx={{
            backgroundColor: purple[600],
            ':hover': {
              backgroundColor: purple[700],
            },
            fontWeight: 400,
            fontSize: '16px',
            lineHeight: '22.4px',
            gap: '20px',
            mt: 2,
          }}
          onClick={handleReportInTeams}
          startIcon={<MsTeamsIcon />}
        >
          {formatMessage({ id: 'error.support.card.askHelpText' })}
        </Button>
      )}
    </>
  );
}
