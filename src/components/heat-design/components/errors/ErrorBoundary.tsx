import React from 'react';

type ErrorBoundaryProps = {
  renderFallback: (message: string) => React.ReactNode;
  children: React.ReactNode;
};

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean; message: string }> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, message: 'Unknown application error.' };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, message: error.stack };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
  }

  render() {
    const { renderFallback, children } = this.props;
    const { hasError, message } = this.state;

    if (hasError) {
      return renderFallback(message);
    }

    return children;
  }
}
