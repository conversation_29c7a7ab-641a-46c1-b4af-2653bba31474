import { SurveyRole } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { UUID } from '@aira/grpc-api/build/ts_out/index.com.aira.contract.common.v1';
import { Stack, Typography } from '@mui/material';
import { Box } from '@mui/system';
import { theme } from '@ui/theme/theme';
import HeatDesignCard from 'components/heat-design/HeatDesignCard';
import { useHeatDesignSurveyForm } from 'components/heat-design/hooks/useHeatDesignSurveyForm';
import { MagicplanLinks } from 'components/heat-design/modals/MagicplanLinks';
import { useRouter } from 'next/router';
import { useIntl } from 'react-intl';
import { api } from 'utils/api';

export function MissingMagicplanSubmissionFooter({ installationGroundworkId }: { installationGroundworkId: UUID }) {
  const intl = useIntl();
  const { locale } = useRouter();
  const surveyForms = useHeatDesignSurveyForm({ installationGroundworkId: installationGroundworkId.value });
  const { data: surveyBookings } = api.Survey.listSurveyBookings.useQuery({
    installationGroundworkId: installationGroundworkId.value,
  });

  const latestTechnicalSurvey = surveyBookings
    ?.filter(
      (surveyBooking) => surveyBooking.roles.includes(SurveyRole.SURVEY_ROLE_TECHNICAL) && surveyBooking.scheduled,
    )
    .toSorted((a, b) => (b.start?.getTime() ?? 0) - (a.start?.getTime() ?? 0))
    .at(0);

  return (
    <>
      <Box mt={4}>
        <MagicplanLinks surveyLinks={surveyForms} />
      </Box>
      {latestTechnicalSurvey && latestTechnicalSurvey.start && (
        <HeatDesignCard sx={{ marginTop: theme.spacing(4) }} variant="light" data-testId="latest-technical-survey-info">
          <Stack>
            <Typography mb={1} variant="headline4">
              {intl.formatMessage({ id: 'heatDesign.error.noMagicplanSubmission.latestSurvey' })}
            </Typography>
            <Typography>
              <b>{intl.formatMessage({ id: 'common.label.date' })}</b>:{' '}
              <span>
                {latestTechnicalSurvey.start.toLocaleString(locale ?? 'en-GB', {
                  timeStyle: 'short',
                  dateStyle: 'long',
                })}
              </span>
            </Typography>
            <Typography>
              <b>{intl.formatMessage({ id: 'common.label.surveyor' })}</b>:{' '}
              <span>{latestTechnicalSurvey.assignedResources.map((resource) => resource.name).join(', ')}</span>
            </Typography>
          </Stack>
        </HeatDesignCard>
      )}
    </>
  );
}
