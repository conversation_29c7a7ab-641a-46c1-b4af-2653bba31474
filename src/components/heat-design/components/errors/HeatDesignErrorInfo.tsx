import { Typography } from '@mui/material';

type HeatDesignErrorInfoProps = {
  description?: string;
  groundworkId?: string;
  solutionId?: string;
};

export function HeatDesignErrorInfo({ description, groundworkId, solutionId }: HeatDesignErrorInfoProps) {
  return (
    <Typography>
      {description && (
        <>
          <b>{description}</b>
          <br />
        </>
      )}
      {groundworkId && (
        <>
          {description && <br />}
          Groundwork ID: {groundworkId}
        </>
      )}
      {solutionId && (
        <>
          <br />
          Solution ID: {solutionId}
        </>
      )}
    </Typography>
  );
}
