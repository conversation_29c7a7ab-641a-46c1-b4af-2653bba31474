import {
  AuxiliaryData,
  HeatDesign,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import {
  ACPHDefaultValue,
  AdjacentKind,
  BELOW_FLOOR_TYPES,
  BelowFloorType,
  Door,
  EmitterDetails,
  Floor,
  FloorProps,
  OPEN_FLUE_TYPES,
  OpenFlueType,
  OutputType,
  ProjectFallbackUValues,
  RadiatorData,
  RoofGlazing,
  RoofOrCeiling,
  Room,
  ROOM_TYPES,
  RoomFabricTypes,
  RoomType,
  SPACE_ABOVE_TYPES,
  SpaceAboveRoofOrCeiling,
  SystemType,
  Wall,
  WALL_TYPES,
  Window,
} from './stores/types';
import { isWithinLimits, lookupUValueForSurfaceType, projectUValuesDefaultsAreValid } from './utils/helpers';
import { HouseInputVentilationDesign, isStandardHouseInputVentilationDesign } from './stores/HouseInputsStore';
import { AirPermeabilityOption } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/groundwork/heatdesign/v2/model';

type Arguments<T> = T extends (...args: infer U) => any ? U : any;
export type PropertyIsValidFn = <
  Type extends keyof typeof validators,
  Property extends keyof (typeof validators)[Type],
  Values extends Arguments<(typeof validators)[Type][Property]>,
>(
  typeName: Type,
  propertyName: Property,
  ...propertyValues: Values
) => boolean;
/**
 * @param typeName The name of the type whose property should be validated
 * @param propertyName The name of the type property to validate
 * @param propertyValues The value of the type property to validate
 * @returns True if the property value is valid, false otherwise
 */
export const propertyIsValid: PropertyIsValidFn = (typeName, propertyName, ...propertyValues) => {
  return (validators[typeName][propertyName] as (...v: typeof propertyValues) => boolean)(...propertyValues);
};

/**
 * @param typeName The name of the type whose properties should be validated
 * @param typeName The value of the type to validate
 * @returns True if the type's properties are valid, false otherwise
 */
export function typeIsValid(typeName: keyof typeof validators, typeValue: any): boolean {
  const typeValidator = validators[typeName];
  let result = true;

  Object.entries(typeValidator).forEach(([propertyName, propertyValidator]) => {
    let propertyValue: any;
    let typeValuePart = typeValue;
    // Needed to handle nested objects
    const propertyNameParts = propertyName.split('.');
    propertyNameParts.forEach((propertyNamePart) => {
      propertyValue = typeValuePart[propertyNamePart];
      typeValuePart = propertyValue;
    });

    result = result && propertyValidator(propertyValue);
  });
  return result;
}

/**
 * A helper to validate walls
 */
export function wallIsValid(wall: Wall): boolean {
  return wall.surfaceType === 'internalWalls'
    ? typeIsValid('internalWalls', wall)
    : typeIsValid('externalOrPartyWalls', wall);
}

export function underfloorHeatingIsValid(room: Room): boolean {
  if (!room.underfloorHeating) return true;
  if (room.underfloorHeating.systemType === undefined) return false;
  if (room.underfloorHeating.outputType === OutputType.AUTOMATIC) return true;

  switch (room.underfloorHeating.systemType) {
    case SystemType.WATER:
      return propertyIsValid('underfloorHeating', 'nominalOutput', room.underfloorHeating.nominalOutput);
    case SystemType.ELECTRIC:
      return propertyIsValid('underfloorHeating', 'outputWatt', room.underfloorHeating.outputWatt);
    default:
      return false;
  }
}

export function radiatorIsValid(radiator: RadiatorData): boolean {
  if (!radiator.enabled) {
    // if radiator is disabled there is no need to validate its props
    return true;
  }
  return (
    propertyIsValid('radiator', 'radiatorDetails', radiator.radiatorDetails) &&
    propertyIsValid('radiator', 'height', radiator.height) &&
    propertyIsValid('radiator', 'width', radiator.width)
  );
}

/**
 * @param typeName The name of the single type of the array objects
 * @param typeValue An array of objects of the same type whos properties should be validated
 * @returns True if all the objects are valid, false otherwise
 */
export function arrayOfTypesIsValid(typeName: keyof typeof validators, typeValues: any[]): boolean {
  return typeValues.map((typeValue) => typeIsValid(typeName, typeValue)).reduce((a, b) => a && b, true);
}

export function roomHeatSourcesAreValid(room: Room): boolean {
  const radiatorsAreValid = room.radiators.filter((r) => r.enabled).every(radiatorIsValid);
  if (!radiatorsAreValid) return false;

  const underfloorValid = underfloorHeatingIsValid(room);
  if (!underfloorValid) return false;

  return true;
}

/**
 * Validates that values of a floor. Returns false when:
 * - any room has enabled radiators that are invalid
 * - any room has underfloor heating with a negative output
 */
export function floorHeatSourcesAreValid(rooms: Room[]): boolean {
  return rooms.every((room) => roomHeatSourcesAreValid(room));
}

const dwellingValidators = {
  constructionYear: (v: number) => isWithinLimits(v, 1500, new Date().getFullYear()),
  numberOfResidents: (v: number) => isWithinLimits(v, 1, 19),
  acphDefault: (d: ACPHDefaultValue) => (d.type === 'custom' ? isWithinLimits(d.value, 0.01, 19) : true),
  pulseTest: (v: HouseInputVentilationDesign) => {
    if (isStandardHouseInputVentilationDesign(v)) {
      if (v.airPermeability === AirPermeabilityOption.AIR_PERMEABILITY_OPTION_PULSE_TEST) {
        return v.airPermeabilityOverride !== undefined && v.airPermeabilityOverride > 0;
      } else {
        return true;
      }
    } else {
      return true;
    }
  },
};

const roomValidators = {
  // Top-level properties
  name: (v: string) => v.length > 0,
  roomType: (v: RoomType) => ROOM_TYPES.includes(v),
  openFlue: (v: OpenFlueType) => OPEN_FLUE_TYPES.includes(v),
  averageHeight: (v: number) => isWithinLimits(v, 0.1, 99), // metres
  avgAirChangesPerHourOverride: (v?: number) => v === undefined || isWithinLimits(v, 0, 99),

  // Complex types
  'surfaces.roofsOrCeilings': (vs: RoofOrCeiling[]) => arrayOfTypesIsValid('roofsOrCeilings', vs),
  'surfaces.roofGlazings': (vs: RoofGlazing[]) => arrayOfTypesIsValid('roofGlazings', vs),
  'surfaces.floors': (vs: Floor[]) => arrayOfTypesIsValid('floors', vs),
  'surfaces.windows': (vs: Window[]) => arrayOfTypesIsValid('windows', vs),
  'surfaces.doors': (vs: Door[]) => arrayOfTypesIsValid('doors', vs),
  'surfaces.walls': (vs: Wall[]) =>
    arrayOfTypesIsValid(
      'internalWalls',
      vs.filter((v) => v.surfaceType === 'internalWalls'),
    ) &&
    arrayOfTypesIsValid(
      'externalOrPartyWalls',
      vs.filter((v) => v.surfaceType === 'externalWalls' || v.surfaceType === 'partyWalls'),
    ),
};

export function tempOfSpaceAboveIsValid(spaceAbove: SpaceAboveRoofOrCeiling): boolean {
  if (spaceAbove.type !== AdjacentKind.Heated) return true;
  return spaceAbove.tempOfSpaceAbove !== 0 && isWithinLimits(spaceAbove.tempOfSpaceAbove, -20, 40); // Celsius
}

export function typeOfSpaceAboveIsValid(spaceAbove: SpaceAboveRoofOrCeiling): boolean {
  return SPACE_ABOVE_TYPES.includes(spaceAbove.type);
}

const roofOrCeilingValidators = {
  spaceAbove: (v?: SpaceAboveRoofOrCeiling) =>
    v !== undefined && typeOfSpaceAboveIsValid(v) && tempOfSpaceAboveIsValid(v),
};

const roofGlazingValidators = {
  width: (v?: number) => isWithinLimits(v, 0.1, 99), // metres
  length: (v?: number) => isWithinLimits(v, 0.1, 99), // metres
};

const floorValidators = {
  belowFloor: (v?: BelowFloorType) => v != null && BELOW_FLOOR_TYPES.includes(v),
};

const windowValidators = {
  // Currently not editable
};

const doorValidators = {
  // Currently not editable
};

const wallValidators = {
  length: (v: number) => isWithinLimits(v, 0, 99), // metres
  surfaceType: (v: RoomFabricTypes) => v != null && WALL_TYPES.includes(v),
};

const internalWallValidators = {
  ...wallValidators,
  adjoiningRoomUID: (v?: string) => v !== undefined,
};

const externalOrPartyWallValidators = {
  ...wallValidators,
};

const radiatorValidators = {
  width: (v: number) => isWithinLimits(v, 50, 49_999), // mm (5 cm -> ~50 m)
  height: (v: number) => isWithinLimits(v, 50, 49_999), // mm (5 cm -> ~50 m)
  radiatorDetails: (r?: EmitterDetails) => {
    if (!r) return false;
    if (r.systemType === SystemType.ELECTRIC) {
      return isWithinLimits(r.outputWatt, 1, 19_999); // Watts
    }
    return (
      isWithinLimits(r.nominalOutput?.deltaT, 1, Infinity) && isWithinLimits(r.nominalOutput?.outputWatt, 1, 19_999) // Watts
    );
  },
  output: (v: number) => isWithinLimits(v, 1, 19_999), // Watts
  deltaT: (v: number) => isWithinLimits(v, 1, 100), // Celsius
};

export const validateRadiatorDeltaTAdjustment = (
  radiator: RadiatorData,
  room: Room,
  roomTemperature: number,
  globalDeltaT: number,
) => {
  if (!radiator.enabled || !room.enableRadiatorDeltaTAdjustment) {
    return true;
  }
  const deltaTAdjustment =
    radiator.radiatorDetails.systemType === SystemType.WATER ? radiator.radiatorDetails.deltaTAdjustmentCelsius : 0;

  if (globalDeltaT + deltaTAdjustment <= 0) {
    return false;
  }

  // deltaTAdjustment cannot be higher than the room temperature
  if (roomTemperature !== undefined) {
    if (deltaTAdjustment > roomTemperature) {
      return false;
    }
  }
  return true;
};

const heatPumpConfigZoneValidators = {
  coolingCurveTargetFlowTemperature: (v: number) => isWithinLimits(v, 5, 70),
  outdoorDesignTemperature: (v: number) => isWithinLimits(v, -30, 50),
};

const underfloorHeatingValidators = {
  outputWatt: (v?: number) => v !== undefined && v > 0,
  deltaTAdjustmentCelsius: (v?: number) => v !== undefined,
  nominalOutput: (v?: { deltaT: number; outputWatt: number }) =>
    v !== undefined && isWithinLimits(v.deltaT, 1, Infinity) && isWithinLimits(v.outputWatt, 1, 99_999), // Watts
  type: (v?: SystemType) => v !== undefined,
  systemType: (v?: SystemType) => v !== undefined,
};

export function floorIsValid(floor: FloorProps): boolean {
  // Floors that are at least partly below ground must have a non-null soilPercentageDefault
  if (floor.floorNr < 0 && floor.soilPercentageDefault == null) {
    return false;
  }

  // The soilPercentageDefault must be a positive number
  if (typeof floor.soilPercentageDefault === 'number' && floor.soilPercentageDefault < 0) {
    return false;
  }

  return true;
}

const validators = {
  dwelling: dwellingValidators,
  room: roomValidators,

  // Surface validators
  roofsOrCeilings: roofOrCeilingValidators,
  roofGlazings: roofGlazingValidators,
  floors: floorValidators,
  internalWalls: internalWallValidators,
  externalOrPartyWalls: externalOrPartyWallValidators,
  windows: windowValidators,
  doors: doorValidators,

  radiator: radiatorValidators,
  underfloorHeating: underfloorHeatingValidators,
  heatPumpConfigZone: heatPumpConfigZoneValidators,
};

/**
 * Validates that a room's data is valid by running our validation system AND
 * checking that every UValue is set or has a valid default value.
 */
function roomIsValid(
  allFloors: FloorProps[],
  floor: FloorProps,
  room: Room,
  projectUValues: ProjectFallbackUValues,
): boolean {
  if (!typeIsValid('room', room)) return false;

  const allSurfacesAreValid = Object.keys(room.surfaces).every((key) => {
    const surfaces = room.surfaces[key as keyof typeof room.surfaces];
    return surfaces.every(
      (surface) =>
        !!(surface.uValue || lookupUValueForSurfaceType(surface.surfaceType, projectUValues, floor, allFloors)),
    );
  });

  return allSurfacesAreValid;
}

/**
 * Validates all the rooms in a dwelling by running our validation system and
 * checking that all the UValues are either set or have a fallback. Returns a
 * list of invalid rooms.
 */
export function getInvalidRooms(floors: FloorProps[], rooms: Room[], projectUValues: ProjectFallbackUValues): Room[] {
  const floorsById = floors.reduce((acc: { [key: string]: FloorProps }, floor: FloorProps) => {
    acc[floor.uid] = floor;
    return acc;
  }, {});

  return rooms.filter((room) => !roomIsValid(floors, floorsById[room.floorId]!, room, projectUValues));
}

export function getInvalidFloors(floors: FloorProps[]): FloorProps[] {
  return floors.filter((f) => !floorIsValid(f));
}

export function customerReportIsValid(auxiliaryData: AuxiliaryData) {
  const { installationCommentForCustomer } = auxiliaryData;
  if (installationCommentForCustomer === undefined || installationCommentForCustomer.trim().length === 0) {
    return false;
  }
  return true;
}

export function isHeatDesignValid(
  heatDesign: HeatDesign,
  floors: FloorProps[],
  rooms: Room[],
  projectUValues: ProjectFallbackUValues,
): boolean {
  // Validate the dwelling
  if (!heatDesign.dwelling) return false;
  if (!propertyIsValid('dwelling', 'constructionYear', heatDesign.dwelling.constructionYear)) return false;
  if (!propertyIsValid('dwelling', 'numberOfResidents', heatDesign.dwelling.numberOfResidents)) return false;
  if (!projectUValuesDefaultsAreValid(projectUValues, rooms, floors)) return false;

  // Validate all the floors
  if (!floors.every(floorIsValid)) return false;

  // Validate all the rooms and their radiators
  if (getInvalidRooms(floors, rooms, projectUValues).length > 0) return false;
  if (!rooms.every(roomHeatSourcesAreValid)) return false;

  return true;
}
