import { Stack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { useRouter } from 'next/router';
import { useIntl } from 'react-intl';
import { formatSquareMeters, formatWatts, formatWattsPerSquareMeter } from 'utils/helpers';
import { useDwellingHeatDesignResult } from './hooks/useDwellingHeatDesignResult';
import RoomOutputsRenderer from './room-components/RoomOutputsRenderer';
import { toLocalisedDecimalPlaces } from './utils/helpers';

function HeatLossOverview() {
  const { locale } = useRouter();
  const { formatMessage } = useIntl();
  const {
    totalHeatLoss,
    totalFabricHeatLoss,
    totalFabricEnergyDemand,
    totalVentilationHeatLoss,
    totalVentilationEnergyDemand,
    totalAdditionalHeatLoss,
    totalAdditionalEnergyDemand,
    yearlyHotWaterEnergyDemand,
    dailyHotWaterEnergyDemand,
    heatingEnergyDemand,
    totalEnergyDemand,
    totalAreaSqm,
    avgWattsPerMeterSquared,
    averageAirChangesPerHour,
  } = useDwellingHeatDesignResult();

  return (
    <Stack gap={{ mobile: 4, desktop: 6 }}>
      <RoomOutputsRenderer />

      <Stack direction={{ mobile: 'column', desktop: 'row' }} gap={{ mobile: 4, desktop: 6 }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ backgroundColor: beige[200], borderTopLeftRadius: '16px' }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.heatLossSummary' })}
                </TableCell>
                <TableCell align="right" sx={{ backgroundColor: beige[200], borderTopRightRadius: '16px' }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.value' })}
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.TotalFabricHeatLoss' })}</TableCell>
                <TableCell align="right">{formatWatts(totalFabricHeatLoss, locale, 0, true)}</TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.TotalVentilationHeatLoss' })}</TableCell>
                <TableCell align="right">{formatWatts(totalVentilationHeatLoss, locale, 0, true)}</TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.TotalAdditionalHeatLoss' })}</TableCell>
                <TableCell align="right">{formatWatts(totalAdditionalHeatLoss, locale, 0, true)}</TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>
                  <b>{formatMessage({ id: 'heatDesign.tableHeaders.TotalHeatLoss' })}</b>
                </TableCell>
                <TableCell align="right">
                  <b>{formatWatts(totalHeatLoss, locale, 0, true)}</b>
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'common.label.measurement.area' })}</TableCell>
                <TableCell align="right">{formatSquareMeters(totalAreaSqm, 1)}</TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.HouseAcph' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({
                    num: averageAirChangesPerHour,
                    decimalPlaces: 2,
                    locale,
                  })}{' '}
                  {formatMessage({ id: 'heatDesign.room.avgAirChangesPerHourSuffix' })}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell sx={{ backgroundColor: beige[100], borderBottomLeftRadius: '16px', borderBottom: 0 }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.wattPerSqM' })}
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    backgroundColor: beige[100],
                    fontVariantNumeric: 'tabular-nums',
                    borderBottomRightRadius: '16px',
                    borderBottom: 0,
                  }}
                >
                  {Number.isNaN(avgWattsPerMeterSquared)
                    ? '–'
                    : formatWattsPerSquareMeter(avgWattsPerMeterSquared, locale, 2, true)}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ backgroundColor: beige[200], borderTopLeftRadius: '16px' }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.energyDemand' })}
                </TableCell>
                <TableCell align="right" sx={{ backgroundColor: beige[200], borderTopRightRadius: '16px' }}>
                  {formatMessage({ id: 'heatDesign.tableHeaders.value' })}
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.dailyWaterEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: dailyHotWaterEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.annualWaterEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: yearlyHotWaterEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.annualFabricEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: totalFabricEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.annualVentilationEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: totalVentilationEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.annualAdditionalEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: totalAdditionalEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: beige[100], fontVariantNumeric: 'tabular-nums' }}>
                <TableCell>{formatMessage({ id: 'heatDesign.tableHeaders.annualHeatingEnergyDemand' })}</TableCell>
                <TableCell align="right">
                  {toLocalisedDecimalPlaces({ num: heatingEnergyDemand, locale })} kWh
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell sx={{ backgroundColor: beige[100], borderBottomLeftRadius: '16px', borderBottom: 0 }}>
                  <b>{formatMessage({ id: 'heatDesign.tableHeaders.totalAnnualEnergyDemand' })}</b>
                </TableCell>
                <TableCell
                  align="right"
                  sx={{
                    backgroundColor: beige[100],
                    fontVariantNumeric: 'tabular-nums',
                    borderBottomRightRadius: '16px',
                    borderBottom: 0,
                  }}
                >
                  <b>{toLocalisedDecimalPlaces({ num: totalEnergyDemand, locale })} kWh</b>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </Stack>
    </Stack>
  );
}

export default HeatLossOverview;
