import { Box, SxProps, Theme } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { ReactNode } from 'react';

type Props = {
  children: ReactNode;
  variant?: 'default' | 'light';
  sx?: SxProps<Theme>;
};

export default function HeatDesignCard({ children, variant = 'default', sx }: Props) {
  return (
    <Box
      sx={{
        backgroundColor: variant === 'default' ? beige[100] : 'white !important',
        borderRadius: 2,
        padding: 4,
        overflowWrap: 'break-word',
        ...sx,
      }}
    >
      {children}
    </Box>
  );
}
