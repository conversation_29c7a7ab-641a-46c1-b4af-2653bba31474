import { Typography } from '@mui/material';
import { LockFilledIcon } from '@ui/components/StandardIcons/LockFilledIcon';
import { grey } from '@ui/theme/colors';
import { FormattedMessage, MessageDescriptor } from 'react-intl';
import Badge from './components/Badge';

export function LockedPill({ textKey }: Readonly<{ textKey: MessageDescriptor['id'] }>) {
  return (
    <Badge
      sx={{
        px: 1,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textTransform: 'none',
        backgroundColor: grey[200],
        height: 'auto',
        width: '175px',
        gap: '8px',
      }}
    >
      <LockFilledIcon />
      <Typography variant="body1Emphasis">
        <FormattedMessage id={textKey} />
      </Typography>
    </Badge>
  );
}
