import { Typography } from '@mui/material';
import { Stack, Box } from '@mui/system';
import { FormattedMessage } from 'react-intl';
import { toTwoDecimalPlaces } from './utils/helpers';
import { useRooms } from './stores/RoomsStore';

export default function RoomRendererHeader({ roomId, hoveredSurface }: { roomId: string; hoveredSurface?: string }) {
  const rooms = useRooms();
  const currentRoom = rooms.find((r) => r.id === roomId);
  if (!currentRoom) return null;

  return (
    <Stack direction="column" justifyContent="center" alignItems="center">
      <Stack direction="row" spacing={2} justifyContent="center">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography variant="h6" component="div">
            <FormattedMessage id="heatDesign.title.totalArea" />
          </Typography>
          <Typography variant="body1Emphasis" component="div">
            {`${toTwoDecimalPlaces(currentRoom.totalArea)} `}m<sup>2</sup>
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Typography variant="h6" component="div">
            <FormattedMessage id="heatDesign.title.totalVolume" />
          </Typography>
          <Typography variant="body1Emphasis" component="div">
            {`${toTwoDecimalPlaces(currentRoom.totalVolume)} `}m<sup>3</sup>
          </Typography>
        </Box>
      </Stack>
      <Typography variant="headline3" component="div">
        {hoveredSurface ?? <FormattedMessage id="heatDesign.title.selectASurface" />}
      </Typography>
    </Stack>
  );
}
