import { Stack, Typography } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { useGroundwork } from 'context/groundwork-context';
import { FormattedMessage } from 'react-intl';
import { canSeeHLCBetaBadge } from 'server/api/helpers/roles';
import Badge from './components/Badge';
import DwellingOverview from './dwelling/DwellingOverview';
import FloorOverview from './FloorOverview';
import HeatLossOverview from './HeatLossOverview';
import { LockedPill } from './LockedPill';
import ProductSelection from './ProductSelection';
import RadiatorsOverview from './radiators/RadiatorsOverview';
import Report from './report/Report';
import { useEmbed3dUrl } from './stores/HouseInputsStore';
import { HlcPage, usePageNavigationStore } from './stores/PageNavigationStore';
import { useProtobufHeatDesignStore } from './stores/ProtobufHeatDesignStore';
import { LabelValueDisplay } from '@ui/components/LabelValueDisplay/LabelValueDisplay';
import { QuoteOutlinedIcon } from '@ui/components/StandardIcons/QuoteOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { useHeatPumpStore } from '../quotation/stores/HeatPumpPackageStore';
import { useInstallationAddonsStore } from '../quotation/stores/InstallationAddonsStore';
import { useAddonsStore } from '../quotation/stores/AddonsStore';
import { PackageItem } from '../../types/types';

export default function PageComponent() {
  const { groundwork, country } = useGroundwork();
  const { location } = groundwork;
  const { formattedAddress } = (location?.$case === 'exactAddress' && location.exactAddress) || {};
  const { page } = usePageNavigationStore();
  const embed3dUrl = useEmbed3dUrl();
  const isLocked = useProtobufHeatDesignStore((s) => s.isLocked);
  const { selectedHeatPumpPackages } = useHeatPumpStore();
  const { selectedInstallationAddonsPackages } = useInstallationAddonsStore();
  const { selectedAddons } = useAddonsStore();
  const hpProducts: PackageItem[] = Object.values(selectedHeatPumpPackages).flat();
  const installationAddonsProducts: PackageItem[] = Object.values(selectedInstallationAddonsPackages).flat();
  const allProducts = [...hpProducts, ...installationAddonsProducts, ...selectedAddons];
  const areProductPricesIncreased = allProducts.filter((product) => product.overrideLockedPrice).length > 0;
  const isProductPriceAdjustmentFlagDisplayed = page === HlcPage.PRODUCT_SELECTION && areProductPricesIncreased;

  const getPageComponent = (currentPage: HlcPage) => {
    switch (currentPage) {
      case HlcPage.PROPERTY_DETAILS:
        return <DwellingOverview formattedAddress={formattedAddress || ''} embed3dUrl={embed3dUrl} />;
      case HlcPage.FLOOR_OVERVIEW:
        return <FloorOverview />;
      case HlcPage.HEAT_LOSS_OVERVIEW:
        return <HeatLossOverview />;
      case HlcPage.RADIATORS_OVERVIEW:
        return <RadiatorsOverview />;
      case HlcPage.PRODUCT_SELECTION:
        return <ProductSelection />;
      case HlcPage.REPORT:
        return <Report isLocked={isLocked} />;
      default:
        throw new Error(`Unknown page: ${currentPage}`);
    }
  };

  return (
    <>
      <Stack direction="row" gap={1}>
        <Heading
          level={1}
          variant="headline2"
          sx={{
            ...(page === HlcPage.REPORT && {
              displayPrint: 'none',
            }),
          }}
        >
          <FormattedMessage id={page} />
        </Heading>
        {canSeeHLCBetaBadge(country) && (
          <Badge>
            <FormattedMessage id="common.label.beta" />
          </Badge>
        )}
        {isLocked && <LockedPill textKey="heatDesign.notify.projectLocked" />}
        {isProductPriceAdjustmentFlagDisplayed && (
          <LabelValueDisplay
            sx={{
              backgroundColor: grey[200],
              gap: 2,
              width: 'auto',
              padding: '0px 16px',
            }}
            label={
              <Stack gap={1} direction="row" alignItems="center">
                <QuoteOutlinedIcon />
                <Typography sx={{ whiteSpace: 'nowrap' }} variant="body2Emphasis">
                  Price adjusted
                </Typography>
              </Stack>
            }
            value={<></>}
          />
        )}
      </Stack>
      {getPageComponent(page)}
    </>
  );
}
