import { createTheme } from '@mui/material';
import { beige, brandYellow } from '@ui/theme/colors';
import { foregroundThemes } from '@ui/theme/componentsThemes';
import { theme } from '@ui/theme/theme';

// For now, these invalid styles are just to be applied to the heat design calculator.
// Later on, they should be applied to the whole of Aerospace, but the styles have
// not yet been tested to see if they are appropriate for the rest of Aerospace.

export const INVALID_INPUT_BACKGROUND = brandYellow[100];
export const INVALID_INPUT_BORDER = `2px solid ${brandYellow[500]}`;

export const heatDesignTheme = createTheme(theme, foregroundThemes.dark, {
  palette: {
    background: {
      default: beige[200],
    },
  },
  components: {
    MuiInputBase: {
      styleOverrides: {
        root: {
          '&.Mui-error': {
            background: `${INVALID_INPUT_BACKGROUND} !important`,
            fieldset: {
              // Ideally we need not use the !important override here, but I could
              // not find a way to make this CSS rule more specific than others, so
              // had to use it. If you can do better, please do.
              border: `${INVALID_INPUT_BORDER} !important`,
            },
          },
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          '&.Mui-error': {
            background: INVALID_INPUT_BACKGROUND,
            border: INVALID_INPUT_BORDER,
            borderRadius: '5px',
            padding: '5px 10px',
          },
        },
      },
    },
  },
});

export const invalidPlanSurface = {
  fill: brandYellow[300],
  stroke: brandYellow[500],
  strokeWidth: '3px',
  fillOpacity: 0.5,
};
