import { useGroundwork } from 'context/groundwork-context';
import { useFloors } from '../stores/FloorsStore';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { useRooms } from '../stores/RoomsStore';
import { countBedrooms } from '../utils/helpers';
import { computeDwellingExternalEnvelopeArea, getEnvelopeAirPermeabilityAt50Pa } from '../utils/calculations';
import { VentilationCalculationMethod } from '../stores/types';

export function useAirPermeabilityValue() {
  const rooms = useRooms();
  const floors = useFloors();
  const numberOfResidents = useHeatDesignHouseInputsStore((s) => s.numberOfResidents);
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const { countryCode } = useGroundwork();
  const numberOfBedrooms = countBedrooms(rooms);
  const dwellingExternalEnvelopeAreaInSquareMeters = computeDwellingExternalEnvelopeArea({
    rooms,
    floors,
    countryCode,
  });

  const dwellingInternalFloorAreaInSquareMeters = rooms.reduce((acc, r) => acc + r.totalArea, 0);

  if (ventilationDesign.calculationMethod === VentilationCalculationMethod.STANDARD) {
    const airPermeabilityValue = getEnvelopeAirPermeabilityAt50Pa({
      ventilationDesign,
      numberOfBedrooms,
      numberOfResidents,
      dwellingExternalEnvelopeAreaInSquareMeters,
      dwellingInternalFloorAreaInSquareMeters,
    });
    return airPermeabilityValue.toFixed(2);
  }
  return undefined;
}
