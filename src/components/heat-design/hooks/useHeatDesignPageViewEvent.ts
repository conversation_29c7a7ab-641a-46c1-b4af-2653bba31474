import { sendGTMEvent } from '@next/third-parties/google';
import { HlcPage, usePageNavigationStore } from 'components/heat-design/stores/PageNavigationStore';
import {
  useHashedSolutionId,
  useHeatDesignGtmActions,
  usePrevPageViewEvent,
} from 'components/stores/heatDesignGtmStore';
import { maskSolutionId } from 'hooks/usePageViewEvent';
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { api } from 'utils/api';

// Follows the format specified at: https://www.notion.so/aerospace-tracking-heat-design-134ad582c409803a8801c56e343ae423
function pageToEvent(page?: HlcPage): {
  pageSuffix: string;
  pageTitle: string;
} {
  switch (page) {
    case HlcPage.PROPERTY_DETAILS:
      return {
        pageSuffix: 'property-overview',
        pageTitle: 'property overview',
      };
    case HlcPage.FLOOR_OVERVIEW:
      return {
        pageSuffix: 'room-details',
        pageTitle: 'room details',
      };
    case HlcPage.HEAT_LOSS_OVERVIEW:
      return {
        pageSuffix: 'heat-loss-results',
        pageTitle: 'heat loss results',
      };
    case HlcPage.RADIATORS_OVERVIEW:
      return {
        pageSuffix: 'emitter-details',
        pageTitle: 'emitter details',
      };
    case HlcPage.PRODUCT_SELECTION:
      return {
        pageSuffix: 'product-selection',
        pageTitle: 'product selection',
      };
    case HlcPage.REPORT:
      return {
        pageSuffix: 'results-&-reports',
        pageTitle: 'results & reports',
      };
    default:
      return {
        pageSuffix: '',
        pageTitle: '',
      };
  }
}

export function useHeatDesignPageViewEvent() {
  const { locale } = useRouter();
  const page = usePageNavigationStore((s) => s.page);
  const previousHeatDesignPage = usePageNavigationStore((s) => s.previouslyVisitedPage);
  const prevPageViewEvent = usePrevPageViewEvent();
  const { setPageViewEvent } = useHeatDesignGtmActions();
  const lastSentPages = useRef({
    previousPage: prevPageViewEvent?.prevPage,
    currentPage: prevPageViewEvent?.currentPage,
  });
  const hashedSolutionId = useHashedSolutionId();
  const user = api.AiraBackend.whoAmI.useQuery().data;
  const userid = user?.userId?.value;

  useEffect(() => {
    if (!hashedSolutionId || !userid) return;
    if (lastSentPages.current.previousPage === previousHeatDesignPage && lastSentPages.current.currentPage === page) {
      return;
    }
    if (typeof window !== 'undefined' && window.dataLayer) {
      const { pageSuffix, pageTitle } = pageToEvent(page);
      const { pageSuffix: previousPageSuffix } = pageToEvent(previousHeatDesignPage);
      let pageLocation = `${window.location.href}/${pageSuffix}`;
      let previousPage = previousHeatDesignPage ? `${window.location.href}/${previousPageSuffix}` : undefined;

      // Mask solution IDs to avoid sending PII
      if (pageLocation.includes('solution/')) pageLocation = maskSolutionId(pageLocation);
      if (previousPage && previousPage.includes('solution/')) previousPage = maskSolutionId(previousPage);

      sendGTMEvent({
        event: 'page_view',
        page_location: pageLocation,
        page_title: pageTitle,
        previous_page: previousPage,
        locale,
        energy_solution_id: hashedSolutionId,
        userid: userid,
      });
      setPageViewEvent(page, previousHeatDesignPage);
      lastSentPages.current = { previousPage: previousHeatDesignPage, currentPage: page };
    }
  }, [locale, page, previousHeatDesignPage, hashedSolutionId, setPageViewEvent, userid]);
}
