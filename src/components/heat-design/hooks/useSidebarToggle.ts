import { api } from 'utils/api';
import { getUrlQueryParameter } from '../utils/helpers';

export function useIsSidebarEnabled(): boolean {
  const { data: serverEnvironment } = api.AiraBackend.getServerEnvironment.useQuery();
  // Temporary pretty dirty way of checking if we're in a test environment
  const isTest = serverEnvironment?.googleApiKey === 'google-api-key';
  const isEnabled = getUrlQueryParameter({ key: 'sidebar' });

  if (isTest) {
    return false;
  }
  if (isEnabled) {
    return true;
  }

  if (serverEnvironment?.environmentName === 'development') {
    return !getUrlQueryParameter({ key: 'disable-sidebar' });
  }

  return false;
}
