import {
  HeatDesign,
  HeatDesignResult,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { useIntl } from 'react-intl';
import { useMemo } from 'react';
import { useProtobufHeatDesignStore } from '../stores/ProtobufHeatDesignStore';
import { useHeatDesignHouseInputsStore } from '../stores/HouseInputsStore';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import { useProjectUValues, useUValues } from '../stores/UValuesStore';
import { useFloors } from '../stores/FloorsStore';
import { serializeHeatDesignResult, serializeProjectToProtobuf } from '../utils/saveToProtobuf';
import { useRooms } from '../stores/RoomsStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../stores/HeatSourceStore';
import { useAuxiliaryData } from '../stores/HeatDesignAuxiliaryDataStore';
import { useDwellingHeatDesignResult } from './useDwellingHeatDesignResult';
import { useGroundwork } from 'context/groundwork-context';

export interface SerializedHeatDesignAndResults {
  heatDesign: HeatDesign;
  result: HeatDesignResult | undefined;
}

export function useSerializedHeatDesignAndResults(): SerializedHeatDesignAndResults | undefined {
  const intl = useIntl();
  const protobufHeatDesignStore = useProtobufHeatDesignStore();
  const heatDesignInputsStore = useHeatDesignHouseInputsStore();
  const climateDataStore = useClimateDataStore();
  const uValues = useUValues();
  const projectUValues = useProjectUValues();
  const floors = useFloors();
  const rooms = useRooms();
  const flowTemperatureCelsius = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const auxiliaryData = useAuxiliaryData();
  const dwellingHeatDesignResult = useDwellingHeatDesignResult();
  const { countryCode } = useGroundwork();

  return useMemo(() => {
    const previousHeatDesign = protobufHeatDesignStore.heatDesign;
    if (!previousHeatDesign) {
      return undefined;
    }
    const heatDesign = serializeProjectToProtobuf({
      previousHeatDesign,
      heatDesignInputsStore,
      climateDataStore,
      uValues,
      projectUValues,
      floors,
      rooms,
      flowTemperatureCelsius,
      flowReturnDeltaT,
      auxiliaryData,
      countryCode,
    });
    const result = serializeHeatDesignResult(
      dwellingHeatDesignResult,
      heatDesign,
      floors,
      rooms,
      projectUValues,
      intl.formatMessage,
    );
    return { heatDesign, result };
  }, [
    protobufHeatDesignStore,
    heatDesignInputsStore,
    climateDataStore,
    uValues,
    projectUValues,
    floors,
    rooms,
    flowTemperatureCelsius,
    flowReturnDeltaT,
    auxiliaryData,
    dwellingHeatDesignResult,
    intl.formatMessage,
    countryCode,
  ]);
}
