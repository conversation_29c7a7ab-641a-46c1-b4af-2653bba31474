import { api } from 'utils/api';
import { CountryCode, getCountryEnum } from 'utils/marketConfigurations';

export function useRadiators() {
  const { data } = api.InstallationGroundwork.getRadiators.useQuery();
  return data;
}

export function useRadiator(id: string) {
  const { data } = api.InstallationGroundwork.getRadiator.useQuery({ id });
  return data;
}

export function useRadiatorsForCountry(country: CountryCode) {
  const { data, isPending } = api.InstallationGroundwork.getRadiatorByCountry.useQuery(
    {
      country: getCountryEnum(country),
    },
    {
      refetchOnMount: false,
    },
  );
  return { data, isPending };
}
