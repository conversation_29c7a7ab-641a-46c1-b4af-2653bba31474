import { useGroundwork } from 'context/groundwork-context';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useMemo } from 'react';
import { getHouseHeatDesignResult } from '../utils/calculations';
import { useRooms } from '../stores/RoomsStore';
import { useFloors } from '../stores/FloorsStore';
import { useProjectUValues } from '../stores/UValuesStore';
import { useClimateDataStore } from '../stores/ClimateDataStore';
import {
  useAdjustedOutdoorDesignTemperature,
  useConstructionYear,
  useHeatDesignHouseInputsStore,
} from '../stores/HouseInputsStore';
import { HeatDesignResult } from '../stores/OutputsStore';
import { useFlowReturnDeltaT, useFlowTemp } from '../stores/HeatSourceStore';
import { useTechnicalSpecifications } from '../utils/heatPumpPerformanceCharacteristics';

/**
 * Derive the dwelling's heat design result.
 */
export function useDwellingHeatDesignResult(): HeatDesignResult {
  const { countryCode } = useGroundwork();
  const numberOfResidents = useHeatDesignHouseInputsStore((s) => s.numberOfResidents);
  const ventilationDesign = useHeatDesignHouseInputsStore((s) => s.ventilationDesign);
  const rooms = useRooms();
  const floors = useFloors();
  const projectUValues = useProjectUValues();
  const climateDataStore = useClimateDataStore();
  const adjustedOutdoorDesignTemperature = useAdjustedOutdoorDesignTemperature();
  const constructionYear = useConstructionYear();
  const flowTemperature = useFlowTemp();
  const flowReturnDeltaT = useFlowReturnDeltaT();
  const heatPumpPackages = useHeatPumpStore((s) => s.selectedHeatPumpPackages);
  const technicalSpecifications = useTechnicalSpecifications();

  return useMemo(
    () =>
      getHouseHeatDesignResult({
        rooms,
        floors,
        projectUValues,
        outdoorTemperature: adjustedOutdoorDesignTemperature,
        constructionYear,
        countryCode,
        numberOfResidents,
        flowTemperature,
        flowReturnDeltaT,
        heatPumpPackages,
        technicalSpecifications,
        climateDataStore,
        ventilationDesign,
      }),
    [
      rooms,
      floors,
      projectUValues,
      adjustedOutdoorDesignTemperature,
      constructionYear,
      countryCode,
      numberOfResidents,
      flowTemperature,
      flowReturnDeltaT,
      heatPumpPackages,
      technicalSpecifications,
      climateDataStore,
      ventilationDesign,
    ],
  );
}
