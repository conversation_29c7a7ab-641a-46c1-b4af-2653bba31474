import { useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

export default function useSliderDebounce(effect: () => void, deps: any[]) {
  // eslint-disable-next-line react-compiler/react-compiler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const callback = useCallback(effect, deps);

  return useMemo(() => debounce(callback, 500, { leading: false, trailing: true }), [callback]);
}
