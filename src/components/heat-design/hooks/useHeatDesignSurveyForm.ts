import { api } from 'utils/api';
import { getLatestSurveyForm } from 'utils/helpers';

/**
 * Multiple survey forms can be attached to a single installation groundwork.
 * There is one that gets separate treatment though, the latest one.
 * This is one on which heat design is based.
 *
 * TODO: However, it's actually more complicated than that. Check how it is done on the BFF side
 * when loading heat design ("findAppropriateSurveyForm"). We should use the same logic.
 * But also, we also shouldn't keep refetching this form data; it involves a heavy lookup
 * on Magicplan. But I'm saving that for a future PR: ESD-1018
 *
 *
 * @param param0.installationGroundworkId - The id of the installation groundwork
 * @returns The latest survey form, or undefined if there are none or data has not yet been loaded.
 */
export function useHeatDesignSurveyForm({
  installationGroundworkId,
}: {
  installationGroundworkId: string | undefined;
}) {
  const { data: surveyFormsResponse } = api.AiraBackend.getSurveyForms.useQuery(
    { installationGroundworkId: installationGroundworkId! },
    { enabled: !!installationGroundworkId },
  );
  return getLatestSurveyForm(surveyFormsResponse);
}
