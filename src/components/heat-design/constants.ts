import { CountryCode } from 'utils/marketConfigurations';
import { brandYellow, green, red, teal } from '@ui/theme/colors';
import { MessageKey } from 'messageType';
import { RoomType } from './stores/types';
import { marketConfiguration } from './utils/marketConfigurations';

export const SECONDS_IN_HOUR = 3600;
export const CUBIC_METRES_IN_LITRE = 0.001;

export const TEMPERATURE_ADJUSTMENT_EXPOSED_LOCATION = -1;

export const STANDARD_DOOR_AREA = 1.98;
/**
 * This is the air change factor that we use when calculating the old-simple ventilation heat loss.
 * We keep this for legacy reasons, as it was used in the old ventilation heat loss calculation.
 */
export const AIR_CHANGE_FACTOR = 0.33;
/**
 * B.2.8 Specific properties of air (BS EN 12831-1:2017)
 * It may be assumed that the density and heat capacity of air are constants with
 * ρ * cp = 0,34 Wh/(m3∙K).
 */
export const AIR_PROPERTY_FACTOR = 0.34;
/**
 * B.2.9 Volume flow ratio between room (i) and zone (z) (BS EN 12831-1:2017)
 * Where no national data on the ratio between the minimum air volume flows of all single rooms (i) being part
 * of a zone (z) and the resulting air volume flow of the zone (z) fi-z is available:
 * Number of rooms in zone > 1 ? 0.5 : 1
 * We can assume we always have more than one room in a zone.
 */
export const VOLUME_RATIO_BETWEEN_ROOM_AND_ZONE = 0.5;

export const UNDERFLOOR_HEATING_FLOW_TEMP = 45;
export const UNDERFLOOR_HEATING_RETURN_TEMP = 35;
export const UNDERFLOOR_HEATING_MEAN_DELTA_T = 20; // ΔT 20⁰C (45/35/20)
export const UNDERFLOOR_HEATING_DELTA_T = UNDERFLOOR_HEATING_FLOW_TEMP - UNDERFLOOR_HEATING_RETURN_TEMP;

export const COLD_WATER_TEMPERATURE = 10;

export const ACPH_DEFAULT_CUSTOM_VALUE = 1;

/**
 * Sources:
 * - https://www.buildtestsolutions.com/tools/air-leakage-pressure-converter
 * - (SAP) Version 10.2, section 2.3 Air permeability measurements.
 */
export const SAP_INFILTRATION_TO_AIR_PERMEABILITY_RATIO = 20;

/**
 * When calculating dwelling heat loss, we add this amount to all U-values to
 * account for thermal bridging.
 */
export const THERMAL_BRIDGING_UVALUE_ADJUSTMENT = 0.1;

export const DAYS_PER_YEAR = 365.2422;

/**
 * When calculating external wall heat loss, we increase wall area by a certain percentage to account for
 * the difference between inner measured area and outer area.
 */
export const externalWallAreaAdjustmentFactor = (countryCode: CountryCode): number | undefined =>
  marketConfiguration[countryCode].externalWallAreaAdjustmentFactor;

export const SOIL_PERCENTAGE_VALUES = [0, 25, 50, 75, 100] as const;

type MagicplanSymbol = {
  color: string;
  key: MessageKey;
};

export const MAGICPLAN_SYMBOL_OUTDOOR_UNIT: MagicplanSymbol = {
  color: teal[500],
  key: 'floorPlans.symbols.outdoorUnit',
};
export const MAGICPLAN_SYMBOL_INDOOR_UNIT: MagicplanSymbol = {
  color: brandYellow[500],
  key: 'floorPlans.symbols.indoorUnit',
};
export const MAGICPLAN_SYMBOL_CYLINDER: MagicplanSymbol = {
  color: brandYellow[500],
  key: 'floorPlans.symbols.cylinder',
};
export const MAGICPLAN_SYMBOL_BUFFER_TANK: MagicplanSymbol = {
  color: green[500],
  key: 'floorPlans.symbols.bufferTank',
};
export const MAGICPLAN_SYMBOL_EXPANSION_VESSEL: MagicplanSymbol = {
  color: red[500],
  key: 'floorPlans.symbols.expansionVessel',
};
export const MAGICPLAN_SYMBOL_THERMOSTAT: MagicplanSymbol = {
  color: '#91a63d',
  key: 'floorPlans.symbols.thermostat',
};

/**
 * Get the country-specific default design room temperature based on room type.
 *
 * UK Reference: DHDG2021 3.5.3.2 Indoor Design Temperatures / DHDG2021 Table 3.6 Indoor design temperatures
 * DE Reference: DIN EN 12831-1:2017-09 B.4.2 Table B.14 Internal design temperature, default values. And also
 *   from asking the German design engineers in the Design Engineer Community -> Germany Microsoft Teams team.
 */
export const ROOM_DESIGN_TEMPERATURES_BY_COUNTRY: {
  [key in RoomType]: { [countryKey in CountryCode]: number };
} = {
  Bathroom: {
    GB: 22,
    DE: 24,
    IT: 24,
  },
  Bedroom: {
    GB: 18,
    DE: 20,
    IT: 20,
  },
  'Bedroom, including en suite bathroom': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Bedroom/study': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Bedsitting room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Breakfast room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Cloakroom/WC': {
    GB: 18,
    DE: 20,
    IT: 20,
  },
  'Dining room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Dressing room': {
    GB: 18,
    DE: 20,
    IT: 20,
  },
  'Family/breakfast room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Games room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  Hall: {
    GB: 18,
    DE: 18,
    IT: 20,
  },
  'Internal room or corridor': {
    GB: 18,
    DE: 18,
    IT: 20,
  },
  Kitchen: {
    GB: 18,
    DE: 20,
    IT: 20,
  },
  Landing: {
    GB: 18,
    DE: 18,
    IT: 20,
  },
  'Living room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Lounge/sitting room': {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  'Shower room': {
    GB: 22,
    DE: 24,
    IT: 20,
  },
  'Store room': {
    GB: 18,
    DE: 16,
    IT: 20,
  },
  Study: {
    GB: 21,
    DE: 20,
    IT: 20,
  },
  Toilet: {
    GB: 18,
    DE: 20,
    IT: 20,
  },
  'Utility room': {
    GB: 18,
    DE: 16,
    IT: 20,
  },
  Other: {
    GB: 21,
    DE: 20,
    IT: 20,
  },
};
