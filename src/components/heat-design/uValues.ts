import { CountryCode } from 'utils/marketConfigurations';
import { UValue } from './models/UValue';
import { ProjectFabricTypes, UValues } from './stores/types';
import { GERMANY_U_VALUES } from './u-values/germany-u-values';
import { BASE_U_VALUES } from './u-values/global-u-values';
import { UK_U_VALUES } from './u-values/uk-u-values';
import { ITALY_U_VALUES } from './u-values/italy-u-values';

/**
 * If the U-Value has no date data, it's never within the range
 */
export function isYearWithinUValueRange(year: number, uValue: UValue): boolean {
  const { fromYearInclusive, toYearInclusive } = uValue.metadata;

  if (fromYearInclusive === undefined && toYearInclusive === undefined) return false;
  if (fromYearInclusive !== undefined && year < fromYearInclusive) return false;
  if (toYearInclusive !== undefined && year > toYearInclusive) return false;

  return true;
}

export function getWorstUValueForBuildingConstructionYear(uValues: UValue[], constructionYear: number): UValue | null {
  let worstValue = -1;
  let worstUValue: UValue | null = null;

  uValues
    .filter((uValue) => !uValue.metadata.isDeprecated)
    .forEach((uValue) => {
      if (isYearWithinUValueRange(constructionYear, uValue)) {
        if (uValue.value > worstValue) {
          worstValue = uValue.value;
          worstUValue = uValue;
        }
      }
    });

  return worstUValue;
}

export function getUValuesForCountryCode(countryCode: CountryCode): UValues {
  switch (countryCode) {
    case CountryCode.DE:
      return GERMANY_U_VALUES;

    case CountryCode.GB:
      return UK_U_VALUES;

    case CountryCode.IT:
      return ITALY_U_VALUES;

    default:
      return BASE_U_VALUES;
  }
}

export function filterUValuesByClimateZone(uValues: UValue[], climateZone: string): UValue[] {
  return uValues.filter(({ metadata }) => metadata.climateZone === undefined || metadata.climateZone === climateZone);
}

export function getUValuesForClimateZone(projectUValues: UValues, climateZone: string): UValues {
  return {
    doors: filterUValuesByClimateZone(projectUValues.doors, climateZone),
    externalWalls: filterUValuesByClimateZone(projectUValues.externalWalls, climateZone),
    floors: filterUValuesByClimateZone(projectUValues.floors, climateZone),
    foundation: filterUValuesByClimateZone(projectUValues.foundation, climateZone),
    intermediateFloors: filterUValuesByClimateZone(projectUValues.intermediateFloors, climateZone),
    internalWalls: filterUValuesByClimateZone(projectUValues.internalWalls, climateZone),
    partyWalls: filterUValuesByClimateZone(projectUValues.partyWalls, climateZone),
    roof: filterUValuesByClimateZone(projectUValues.roof, climateZone),
    roofGlazings: filterUValuesByClimateZone(projectUValues.roofGlazings, climateZone),
    roofsOrCeilings: filterUValuesByClimateZone(projectUValues.roofsOrCeilings, climateZone),
    windows: filterUValuesByClimateZone(projectUValues.windows, climateZone),
  };
}

export type UValueDiff = {
  before: UValue | null;
  after: UValue | null;
  fabricType: ProjectFabricTypes;
};
