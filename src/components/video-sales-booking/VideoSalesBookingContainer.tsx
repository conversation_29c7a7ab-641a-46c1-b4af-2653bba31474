import { Stack, useMediaQuery } from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { enGB } from 'date-fns/locale';
import { Groundwork } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/api/gateway/groundwork/v1/model';
import { GroundworkContext } from 'context/groundwork-context';
import BookingToolContent from '../booking-shared/BookingToolContent';

export default function VideoSalesBookingContainer({ groundwork }: { groundwork: Groundwork }) {
  const isMobile = useMediaQuery('(max-width: 1500px)');
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGB}>
      <GroundworkContext.Provider value={groundwork}>
        <Stack
          direction="row"
          sx={{
            height: '100%',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
          }}
        >
          <Stack
            sx={{
              height: '100%',
              width: isMobile ? '100%' : '560px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <BookingToolContent installationGroundworkId={groundwork.id!.value} bookingType="videoSales" />
          </Stack>
        </Stack>
      </GroundworkContext.Provider>
    </LocalizationProvider>
  );
}
