import {
  ServiceVisit,
  ServiceVisitCancellationReason_CancellationReasonCategory,
} from '@aira/service-visit-grpc-api/build/ts_out/index.com.aira.acquisition.contract.service.visit.v1';
import { Stack, Typography, Button, CircularProgress, MenuItem, Select, TextField } from '@mui/material';
import { useState } from 'react';
import { Modal } from '@ui/components/Modal/Modal';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { QueryObserverResult } from '@tanstack/react-query';

export default function UnscheduleConfirmation({
  isPopupOpen,
  handleClose,
  serviceVisitId,
  refetchBookings,
  setShowTimeBooker,
}: {
  isPopupOpen: boolean;
  handleClose: () => void;
  serviceVisitId: string;
  refetchBookings: () => Promise<QueryObserverResult<ServiceVisit[], unknown>>;
  setShowTimeBooker: (show: boolean) => void;
}) {
  const [isSaving, setIsSaving] = useState(false);
  const [reason, setReason] = useState<ServiceVisitCancellationReason_CancellationReasonCategory>(
    ServiceVisitCancellationReason_CancellationReasonCategory.CANCELLATION_REASON_CATEGORY_UNCATEGORIZED,
  );
  const [description, setDescription] = useState<string>('');
  const { mutateAsync: unScheduleMaintenance } = api.ServiceVisits.unScheduleServiceVisit.useMutation({
    onSuccess() {
      refetchBookings();
      setShowTimeBooker(false);
      handleClose();
    },
  });

  const handleConfirm = async () => {
    setIsSaving(true);
    await unScheduleMaintenance({
      serviceVisitId,
      reason,
      description,
    });
    setIsSaving(false);
  };

  return (
    <Modal isModalOpen={isPopupOpen} handleClose={() => {}} width="500px">
      <Stack spacing={3}>
        <Stack spacing={1}>
          <Typography variant="headline3">
            <FormattedMessage
              id="serviceBookingTool.popup.unscheduleHeader"
              defaultMessage="Unschedule service visit"
            />
          </Typography>
          <Typography variant="body1">
            <FormattedMessage
              id="serviceBookingTool.popup.unscheduleDescription"
              defaultMessage="This unschedules the booking time and removes any assigned resource."
            />
          </Typography>
        </Stack>
        <Stack spacing={3}>
          <Select
            value={reason}
            onChange={(e) => setReason(e.target.value as ServiceVisitCancellationReason_CancellationReasonCategory)}
          >
            <MenuItem
              value={
                ServiceVisitCancellationReason_CancellationReasonCategory.CANCELLATION_REASON_CATEGORY_UNCATEGORIZED
              }
            >
              <FormattedMessage
                id="serviceBookingTool.popup.unscheduleReason.uncategorized"
                defaultMessage="Uncategorized"
              />
            </MenuItem>
            <MenuItem
              value={
                ServiceVisitCancellationReason_CancellationReasonCategory.CANCELLATION_REASON_CATEGORY_CUSTOMER_CANCELLED
              }
            >
              <FormattedMessage
                id="serviceBookingTool.popup.unscheduleReason.customerCancelled"
                defaultMessage="Customer Cancelled"
              />
            </MenuItem>
            <MenuItem
              value={
                ServiceVisitCancellationReason_CancellationReasonCategory.CANCELLATION_REASON_CATEGORY_CUSTOMER_NO_SHOW
              }
            >
              <FormattedMessage
                id="serviceBookingTool.popup.unscheduleReason.customerNoShow"
                defaultMessage="Customer No Show"
              />
            </MenuItem>
            <MenuItem
              value={
                ServiceVisitCancellationReason_CancellationReasonCategory.CANCELLATION_REASON_CATEGORY_NO_LONGER_NEEDED
              }
            >
              <FormattedMessage
                id="serviceBookingTool.popup.unscheduleReason.noLongerNeeded"
                defaultMessage="No Longer Needed"
              />
            </MenuItem>
          </Select>

          <TextField
            label={
              <FormattedMessage
                id="serviceBookingTool.popup.unscheduleReason.description"
                defaultMessage="Description"
              />
            }
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            multiline
            rows={3}
          />

          <Stack direction="row" spacing={2}>
            <Button onClick={handleConfirm} disabled={isSaving} variant="contained">
              {isSaving ? <CircularProgress size={20} /> : <FormattedMessage id="common.label.confirm" />}
            </Button>
            <Button
              onClick={() => {
                handleClose();
              }}
              disabled={isSaving}
              variant="outlined"
            >
              <FormattedMessage id="common.label.cancel" />
            </Button>
          </Stack>
        </Stack>
      </Stack>
    </Modal>
  );
}
