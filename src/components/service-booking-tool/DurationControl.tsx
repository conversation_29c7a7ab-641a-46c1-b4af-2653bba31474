import { FormGroup, Typography, FormControlLabel, Radio } from '@mui/material';
import { Box } from '@mui/system';
import { useIntl, FormattedMessage } from 'react-intl';

const MAINTENANCE_DURATION_MINUTES = [
  { key: 1, value: 1 * 60 },
  { key: 2, value: 4 * 60 },
  { key: 3, value: 6 * 60 },
  { key: 4, value: 8 * 60 },
] as const;
export const defaultDurationMinutes = 60;
export type DurationMinutes = (typeof MAINTENANCE_DURATION_MINUTES)[number]['value'];

export default function DurationControl({
  durationMinutes,
  setDurationMinutes,
}: {
  durationMinutes: DurationMinutes;
  setDurationMinutes: (durationMinutes: DurationMinutes) => void;
}) {
  const intl = useIntl();

  return (
    <FormGroup>
      <Typography variant="body1">{intl.formatMessage({ id: 'serviceBookingTool.label.duration' })}</Typography>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        {MAINTENANCE_DURATION_MINUTES.map((surveyDuration) => (
          <FormControlLabel
            key={surveyDuration.key}
            label={
              <FormattedMessage
                id="bookingTool.radio.duration"
                values={{ hours: Math.floor(surveyDuration.value / 60), minutes: surveyDuration.value % 60 }}
              />
            }
            checked={surveyDuration?.value === durationMinutes}
            control={<Radio />}
            onChange={() => setDurationMinutes(surveyDuration.value)}
          />
        ))}
      </Box>
    </FormGroup>
  );
}
