import { <PERSON>, Chip, Link, Stack, Typography } from '@mui/material';
import Public from '@ui/components/Icons/material/Public';
import { FormattedMessage, useIntl } from 'react-intl';
import { Button } from '@ui/components/Button/Button';
import { Card } from '@ui/components/Card/Card';
import { useState } from 'react';
import { MessageKey } from 'messageType';
import TimeRange from 'components/booking-shared/TimeRange';
import {
  ServiceVisit,
  ServiceVisitJobStatus,
} from '@aira/service-visit-grpc-api/build/ts_out/com/aira/acquisition/contract/service/visit/v1/model';
import { QueryObserverResult } from '@tanstack/react-query';
import { useRegionContext } from 'components/booking-shared/RegionContext';
import { api } from 'utils/api';
import Notes from './Notes';
import TimeBooker from './TimeBooker';
import UnscheduleConfirmation from './UnscheduleConfirmation';
import { JobType } from './types';

interface ScheduledBookingsProps {
  serviceVisitBookings: ServiceVisit[];
  refetchBookings: () => Promise<QueryObserverResult<ServiceVisit[], unknown>>;
}
interface ScheduledBookingProps {
  serviceVisitBooking: ServiceVisit;
  refetchBookings: () => Promise<QueryObserverResult<ServiceVisit[], unknown>>;
}

function getStatusKey({ status, plannedStartAt }: { status: ServiceVisitJobStatus; plannedStartAt?: Date }) {
  switch (status) {
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_UNSPECIFIED:
      return 'JOB_STATUS_UNSPECIFIED';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_NOT_STARTED:
      if (plannedStartAt) {
        return 'JOB_STATUS_READY';
      }
      return 'JOB_STATUS_NOT_SCHEDULED';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_IN_PROGRESS:
      return 'JOB_STATUS_IN_PROGRESS';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_COMPLETED:
      return 'JOB_STATUS_FINISHED';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_CANCELLED:
      return 'JOB_STATUS_CANCELLED';
    default:
      return 'JOB_STATUS_UNSPECIFIED';
  }
}
function getStatusColor({ status, plannedStartAt }: { status: ServiceVisitJobStatus; plannedStartAt?: Date }) {
  switch (status) {
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_IN_PROGRESS:
      return '#FFAF51';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_COMPLETED:
      return '#358267';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_CANCELLED:
      return '#8F2D2D';
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_UNSPECIFIED:
    case ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_NOT_STARTED:
      if (plannedStartAt) {
        return '#FFAF51';
      }
      return '#D9D9D9';
    default:
      return '#D9D9D9';
  }
}

function ScheduledBooking({ serviceVisitBooking, refetchBookings }: ScheduledBookingProps) {
  const region = useRegionContext();
  const { notes, purpose, jobConstraints, jobState, externalJobUrl } = serviceVisitBooking;
  const { duration } = jobConstraints!;
  const { assignees, startTime: plannedStartAt } = jobState!;
  const plannedEndAt = plannedStartAt ? new Date(plannedStartAt.getTime() + duration!.seconds * 1000) : undefined;

  const serviceVisitJobStatus = jobState!.status;
  const jobType = purpose?.purpose!.$case as JobType;

  const { data: resourceForJobType } = api.ServiceVisits.getServiceVisitResources.useQuery({
    operationalUnitId: region.id!.value,
    jobType,
  });
  const assigneesNames = assignees?.map((assignee) => {
    const userId = assignee.user?.$case === 'knownUser' ? assignee.user.knownUser.userId?.value : undefined;
    const assigneeUser = resourceForJobType?.find((resource) => resource.userId?.value === userId);
    return assigneeUser ? `${assigneeUser.firstName} ${assigneeUser.lastName}` : null;
  });

  const [showTimeBooker, setShowTimeBooker] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleScheduleMaintenance = () => {
    setShowTimeBooker(true);
  };
  const { formatMessage } = useIntl();
  const handleHideTimeBooker = () => {
    setShowTimeBooker(false);
  };

  const initialDurationMinutes = duration?.seconds ? duration.seconds / 60 : undefined;

  const enableScheduling =
    !plannedStartAt && serviceVisitJobStatus === ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_NOT_STARTED;

  const enableUnscheduling =
    plannedStartAt && serviceVisitJobStatus === ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_NOT_STARTED;

  const statusKey = getStatusKey({ status: serviceVisitJobStatus, plannedStartAt });
  const statusColor = getStatusColor({ status: serviceVisitJobStatus, plannedStartAt });
  return (
    <Card
      sx={{
        overflow: 'visible',
        background: '#fff',
      }}
    >
      <Stack direction="row" sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
        <Stack direction="row" sx={{ alignItems: 'center', gap: '4px' }}>
          <div
            style={{
              height: '12px',
              width: '12px',
              borderRadius: '50%',
              backgroundColor: statusColor,
            }}
          />
          <Typography variant="body2">
            {formatMessage({
              id: `serviceBookingTool.jobStatus.${statusKey}` as MessageKey,
            })}
          </Typography>
        </Stack>
        {externalJobUrl && (
          <Link href={externalJobUrl} target="_blank">
            <Chip
              label="Skedulo"
              icon={<Public sx={{ height: '18px' }} />}
              clickable
              sx={{
                borderRadius: '8px',
                fontSize: '12px',
                height: '26px',
                fontFamily: 'AiraText',
                '.MuiChip-label': {
                  padding: '4px 8px 4px 4px',
                  lineHeight: '18px',
                },
              }}
            />
          </Link>
        )}
      </Stack>
      <Typography variant="h6" sx={{ fontSize: '24px' }}>
        <FormattedMessage id={`serviceBookingTool.jobType.${jobType}` as MessageKey} />
      </Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginTop: '8px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: '4px',
          }}
        >
          {plannedStartAt && plannedEndAt ? (
            <>
              <Typography variant="body1">
                {serviceVisitJobStatus === ServiceVisitJobStatus.SERVICE_VISIT_JOB_STATUS_COMPLETED
                  ? formatMessage({ id: 'bookingTool.body.completedOn' })
                  : formatMessage({ id: 'bookingTool.body.scheduledForDateAndTime' })}
              </Typography>
              <Typography variant="body1Emphasis">
                <TimeRange start={plannedStartAt} end={plannedEndAt} variant="long" />
              </Typography>
            </>
          ) : (
            <Typography>{formatMessage({ id: 'bookingTool.body.surveyUnscheduled' })}</Typography>
          )}
        </Box>
      </Box>
      {plannedStartAt && (
        <Box component="span">
          <Typography display="inline" variant="body1">
            <FormattedMessage id="serviceBookingTool.body.resourceName" defaultMessage="Resource" />:
          </Typography>
          <Typography display="inline" variant="body1" marginLeft="4px">
            {(assigneesNames ?? [])
              .map(
                (name: string | null) =>
                  name ?? formatMessage({ id: 'bookingTool.deactivatedUser', defaultMessage: 'Deactivated user' }),
              )
              .join(', ')}
          </Typography>
        </Box>
      )}

      {enableUnscheduling && (
        <Stack alignItems="start" paddingBlock={2}>
          <Button onClick={() => setIsPopupOpen(true)} size="small" sx={{ paddingInline: 4 }}>
            {formatMessage({ id: 'bookingTool.button.unschedule' })}
          </Button>
        </Stack>
      )}
      <Notes serviceVisitId={serviceVisitBooking.id!.value} notes={notes} />
      {enableScheduling &&
        (!showTimeBooker ? (
          <Button
            variant="outlined"
            sx={{
              marginTop: '16px',
            }}
            onClick={handleScheduleMaintenance}
          >
            {formatMessage({ id: 'serviceBookingTool.button.bookServiceJob' })}
          </Button>
        ) : (
          <>
            <Button
              variant="outlined"
              sx={{
                marginTop: '16px',
              }}
              onClick={handleHideTimeBooker}
            >
              {formatMessage({ id: 'bookingTool.button.hideScheduler' })}
            </Button>
            <Box
              sx={{
                marginTop: '16px',
              }}
            >
              <TimeBooker
                serviceVisitId={serviceVisitBooking.id!.value}
                initialNotes={notes}
                initialDurationMinutes={initialDurationMinutes}
                initialJobType={jobType}
                setManuallyShowScheduler={setShowTimeBooker}
                refetchBookings={refetchBookings}
              />
            </Box>
          </>
        ))}
      <UnscheduleConfirmation
        isPopupOpen={isPopupOpen}
        handleClose={() => setIsPopupOpen(false)}
        serviceVisitId={serviceVisitBooking.id!.value}
        refetchBookings={refetchBookings}
        setShowTimeBooker={setShowTimeBooker}
      />
    </Card>
  );
}

export default function ScheduledBookings({ serviceVisitBookings, refetchBookings }: ScheduledBookingsProps) {
  return serviceVisitBookings.map((serviceVisitBooking) => (
    <ScheduledBooking
      key={serviceVisitBooking.id!.value!}
      serviceVisitBooking={serviceVisitBooking}
      refetchBookings={refetchBookings}
    />
  ));
}
