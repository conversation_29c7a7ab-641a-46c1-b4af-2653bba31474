import {
  <PERSON><PERSON>,
  <PERSON>,
  FormControlLabel,
  FormGroup,
  Radio,
  Stack,
  Typography,
  TextField,
  Tabs,
  Tab,
  Checkbox,
} from '@mui/material';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { startOfTomorrow, addMonths, endOfMonth, startOfMonth, isWithinInterval, max, min, endOfDay } from 'date-fns';
import { FormattedMessage, useIntl } from 'react-intl';
import nextHalfHourBoundary from 'utils/dates/nextHalfHourBoundary';
import { groupBy } from 'utils/groupBy';
import { mapValues } from 'utils/mapValues';
import { useRegionContext } from 'components/booking-shared/RegionContext';
import CalendarSection from 'components/booking-shared/CalendarSection';
import { SelectSpecificTimeSection } from 'components/booking-tool/SelectSpecificTimeSection';
import { formatDate } from 'components/booking-shared/timeUtils';
import { Autocomplete } from '@ui/components/Autocomplete/Autocomplete';
import type { OnChangeEvent } from '@ui/components/Autocomplete/Autocomplete';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { QueryObserverResult } from '@tanstack/react-query';
import { ServiceVisit } from '@aira/service-visit-grpc-api/build/ts_out/index.com.aira.acquisition.contract.service.visit.v1';
import { SummarySection } from './SummarySection';
import { api } from '../../utils/api';
import DurationControl, { DurationMinutes, defaultDurationMinutes } from './DurationControl';
import { JobType, JobTypeSpec } from './types';
import { useGroundwork } from 'context/groundwork-context';

enum SelectedTab {
  SPECIFIC_TIME = 0,
  SET_TIME_LATER = 2,
}

const SERVICE_VISIT_JOB_TYPES: JobTypeSpec[] = [
  {
    type: JobType.SERVICE_HEAT_PUMP,
    labelKey: 'serviceBookingTool.jobType.JOB_TYPE_HEAT_PUMP_MAINTENANCE',
  },
  {
    type: JobType.SERVICE_ELECTRICAL_SYSTEM,
    labelKey: 'serviceBookingTool.jobType.JOB_TYPE_ELECTRICAL_MAINTENANCE',
  },
  {
    type: JobType.INSTALL_THERMOSTAT,
    labelKey: 'serviceBookingTool.jobType.JOB_TYPE_THERMOSTAT_MAINTENANCE',
  },
];
const DEFAULT_JOB_TYPE: JobType = JobType.SERVICE_HEAT_PUMP;

export interface TimeSlot {
  start: Date;
  end: Date;
}

export interface AvailabilitiesForDate {
  availabilities: { start: Date; end: Date }[];
}

export interface ApiResource {
  name: string;
  email: string;
}

export type AvailabilitiesGroupedByDate = Map<string, AvailabilitiesForDate | null>;

export default function TimeBooker({
  initialDurationMinutes,
  initialJobType,
  scheduleOnly,
  initialNotes,
  setManuallyShowScheduler,
  refetchBookings,
  serviceVisitId,
}: {
  initialJobType?: JobType;
  initialDurationMinutes?: number;
  scheduleOnly?: boolean;
  initialNotes?: string;
  setManuallyShowScheduler: (show: boolean) => void;
  refetchBookings: () => Promise<QueryObserverResult<ServiceVisit[], unknown>>;
  serviceVisitId?: string;
}): React.JSX.Element {
  const [selectedTab, setSelectedTab] = useState<SelectedTab>(SelectedTab.SPECIFIC_TIME);
  const minDate = nextHalfHourBoundary(new Date());
  const maxDate = addMonths(minDate, 3);
  const [from, setFrom] = useState(minDate);
  const [to, setTo] = useState(endOfMonth(minDate));
  const [selectedDate, setSelectedDate] = useState<Date>(minDate);
  const [selectedTime, setSelectedTime] = useState<{ start: Date; end: Date } | null>(null);
  const [durationMinutes, setDurationMinutes] = useState<DurationMinutes>(
    initialDurationMinutes ?? defaultDurationMinutes,
  );
  const [notes, setNotes] = useState<string>(initialNotes ?? '');
  const [assignSpecificResourceChecked, setAssignSpecificResourceChecked] = useState(
    initialJobType === 'installThermostat',
  );
  const [selectedResourceId, setSelectedResourceId] = useState<string>('');
  const schedulingEnabled = selectedTab === SelectedTab.SPECIFIC_TIME;
  const resourceIdToFilterFor = selectedResourceId && schedulingEnabled ? selectedResourceId : '';
  const [jobType, setJobType] = useState<JobType>(initialJobType ?? DEFAULT_JOB_TYPE);
  const intl = useIntl();
  const region = useRegionContext();
  const { timeZone } = region;
  const { groundwork } = useGroundwork();
  const facilityId = groundwork!.facility!.id!.value!;

  const { isLoading, data: availabilitiesGroupedByDate } = api.ServiceVisits.getServiceVisitAvailability.useQuery(
    {
      facilityId,
      from,
      to,
      durationMinutes,
      jobType: jobType as JobType,
      operationalUnitId: region.id?.value ?? '',
      selectedResourceIds: resourceIdToFilterFor.length > 0 ? [resourceIdToFilterFor] : [],
    },
    {
      enabled: schedulingEnabled && Boolean(!assignSpecificResourceChecked || selectedResourceId) && !!facilityId,
      select: (results) => {
        const timeSlots = results.availableTimeSlots.map(({ startsAt, endsAt }) => ({
          start: startsAt!,
          end: endsAt!,
        }));
        const groupedByDate = groupBy(timeSlots, ({ start }) => formatDate(start, timeZone));
        return mapValues(
          groupedByDate,
          (availabilitiesOnDay) => availabilitiesOnDay && { availabilities: availabilitiesOnDay },
        );
      },
    },
  );
  const dayAvailability = useMemo<AvailabilitiesForDate | null>(
    () =>
      availabilitiesGroupedByDate &&
      selectedDate &&
      isWithinInterval(selectedDate, { start: startOfMonth(from), end: to })
        ? (availabilitiesGroupedByDate.get(formatDate(selectedDate, timeZone)) ?? null)
        : null,
    [availabilitiesGroupedByDate, selectedDate, timeZone, from, to],
  );

  const handleMonthChange = (monthStart: Date) => {
    setFrom(max([monthStart, startOfTomorrow()]));
    setTo(min([endOfMonth(monthStart), endOfDay(maxDate)]));
  };

  const handleDateChange = (newValue: Date | null) => {
    if (newValue) setSelectedDate(newValue);
    setSelectedTime(null);
  };

  const isSelectedTime = ({ start, end }: { start: Date; end: Date }) =>
    start === selectedTime?.start && end === selectedTime?.end;

  const [timeGridError, setTimeGridError] = useState<string | null>(null);
  const [specificResourceError, setSpecificResourceError] = useState<string | undefined>(undefined);

  useEffect(() => setTimeGridError(null), [selectedDate, selectedTime]);

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: SelectedTab) => {
    setSelectedTab(newValue);
    setSelectedTime(null);
    setSelectedResourceId('');
    setAssignSpecificResourceChecked(false);
  };

  const handleChangeJobType = (type: JobType) => {
    setJobType(type);
    setSelectedResourceId('');
    if (selectedTab === SelectedTab.SPECIFIC_TIME) {
      setAssignSpecificResourceChecked(type === 'installThermostat');
    }
  };

  const handleAssignSpecificResourceCheckboxChanged = (_event: ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setAssignSpecificResourceChecked(checked);
    setSelectedResourceId('');
  };

  const { data: allResources } = api.ServiceVisits.getServiceVisitResources.useQuery(
    {
      operationalUnitId: region.id!.value,
      jobType,
    },
    {
      initialData: [] as UserIdentity[],
    },
  );

  const selectedResource = allResources.find((resource) => resource.userId?.value === selectedResourceId);

  const { data: selfData } = api.AiraBackend.whoAmI.useQuery();
  const selfEmail = selfData?.email?.toLowerCase();

  const optionsForSelf = allResources
    .filter((resource) => resource.email.toLowerCase() === selfEmail)
    .map((resource) => ({
      label: `Myself (${resource.firstName} ${resource.lastName})`,
      value: resource.email,
    }));
  const optionsForOtherReources = allResources
    .filter((resource) => resource.email !== selfEmail)
    .toSorted((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`))
    .map((resource) => ({
      label: `${resource.firstName} ${resource.lastName}`,
      value: resource.email,
    }));
  const optionsForSpecificResource = optionsForSelf.concat(optionsForOtherReources);

  const resourceNotAvailableMessage =
    selectedResourceId && !optionsForSpecificResource.some((option) => option.value === selectedResource?.email)
      ? intl.formatMessage({ id: 'bookingTool.error.resourceNotAvailable' })
      : undefined;
  const handleChangeSelectedResource = (e: OnChangeEvent<string>) => {
    const selectedEmail = e?.value ?? null;
    const resourceId = allResources.find((resource) => resource.email === selectedEmail)?.userId?.value;
    setSelectedResourceId(resourceId ?? '');
    setSpecificResourceError(undefined);
  };

  return (
    <Stack spacing={4}>
      {!scheduleOnly && (
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs variant="fullWidth" value={selectedTab} onChange={handleChangeTab}>
            <Tab label={intl.formatMessage({ id: 'bookingTool.tab.specificTime' })} value={SelectedTab.SPECIFIC_TIME} />
            <Tab
              label={intl.formatMessage({ id: 'bookingTool.tab.setTimeLater' })}
              value={SelectedTab.SET_TIME_LATER}
            />
          </Tabs>
        </Box>
      )}

      <FormGroup>
        <Typography variant="body1">
          {intl.formatMessage({ id: 'serviceBookingTool.jobType.serviceJobType' })}
        </Typography>

        <Box display="grid" gridTemplateColumns="1fr 1fr" justifyItems="center">
          {SERVICE_VISIT_JOB_TYPES.map(({ type, labelKey }) => (
            <FormControlLabel
              key={type}
              label={<FormattedMessage id={labelKey} />}
              checked={type === jobType}
              control={<Radio />}
              onChange={() => handleChangeJobType(type)}
            />
          ))}
        </Box>
      </FormGroup>

      <DurationControl durationMinutes={durationMinutes} setDurationMinutes={setDurationMinutes} />
      {schedulingEnabled && (
        <>
          <FormGroup>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-start',
              }}
            >
              <Typography variant="body1">
                {intl.formatMessage({ id: 'bookingTool.label.assignSpecificPerson' })}
              </Typography>
              <Checkbox
                disabled={jobType === 'installThermostat'}
                checked={assignSpecificResourceChecked}
                onChange={handleAssignSpecificResourceCheckboxChanged}
              />
            </Box>
            {assignSpecificResourceChecked && (
              <Autocomplete
                disableTyping={false}
                label=""
                placeholder={intl.formatMessage({ id: 'bookingTool.placeholder.selectPerson' })}
                name="specificResource"
                options={optionsForSpecificResource}
                error={Boolean(specificResourceError ?? resourceNotAvailableMessage)}
                errorText={specificResourceError ?? resourceNotAvailableMessage}
                onChange={handleChangeSelectedResource}
                fullWidth
              />
            )}
          </FormGroup>
          <CalendarSection
            isLoading={isLoading}
            minDate={minDate}
            maxDate={maxDate}
            availabilitiesGroupedByDate={availabilitiesGroupedByDate ?? null}
            selectedDate={selectedDate}
            handleDateChange={handleDateChange}
            handleMonthChange={handleMonthChange}
            timeWindowsEnabled={false}
          />
          <SelectSpecificTimeSection
            selectedDate={selectedDate}
            availableTimes={dayAvailability?.availabilities ?? []}
            setSelectedTime={setSelectedTime}
            isSelectedTime={isSelectedTime}
          />
          {timeGridError && <Alert severity="error">{timeGridError}</Alert>}
        </>
      )}
      <FormGroup>
        <Typography variant="headline3" sx={{ mb: 1 }}>
          {intl.formatMessage({ id: 'bookingTool.label.notes' })}
        </Typography>
        <TextField
          name="notes"
          type="textarea"
          multiline
          value={notes}
          onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNotes(e.target.value)}
        />
      </FormGroup>
      <SummarySection
        serviceVisitId={serviceVisitId}
        withDateTime={selectedTab === SelectedTab.SPECIFIC_TIME}
        selectedTime={selectedTime}
        selectedDate={selectedDate}
        durationMinutes={durationMinutes}
        jobType={jobType}
        setManuallyShowScheduler={setManuallyShowScheduler}
        refetchBookings={refetchBookings}
        setTimeGridError={setTimeGridError}
        notes={notes}
        assignSpecificResource={assignSpecificResourceChecked}
        selectedResourceId={selectedResource?.userId?.value ?? null}
        setSpecificResourceError={(message) => setSpecificResourceError(message)}
      />
    </Stack>
  );
}
