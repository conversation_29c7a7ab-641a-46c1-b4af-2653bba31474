import { Card } from '@ui/components/Card/Card';
import { Button, Container, Stack, Typography, useMediaQuery } from '@mui/material';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { api } from 'utils/api';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import TimeBooker from './TimeBooker';
import ScheduledBookings from './ScheduledBookings';
import { useGroundwork } from 'context/groundwork-context';

export type JobType = 'serviceHeatPump' | 'serviceElectricalSystem' | 'installThermostat';

export default function BookingTool() {
  const [manuallyShowScheduler, setManuallyShowScheduler] = useState(false);
  const { groundwork } = useGroundwork();
  const facilityId = groundwork!.facility!.id!.value!;
  const {
    data: serviceVisitBookings,
    isLoading,
    refetch: refetchBookings,
  } = api.ServiceVisits.getServiceVisitsForFacility.useQuery(
    { facilityId },
    {
      enabled: !!facilityId,
    },
  );

  const intl = useIntl();

  const isMobile = useMediaQuery('(max-width: 700px)');
  const showScheduler =
    manuallyShowScheduler || (serviceVisitBookings !== undefined && serviceVisitBookings.length === 0);

  if (isLoading) {
    return <HeatPumpLoader />;
  }

  return (
    <Container
      sx={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'center',
        '&.MuiContainer-root': {
          width: '100%',
          maxWidth: '100%',
        },
      }}
    >
      <Stack
        spacing={2}
        mb={4}
        sx={{
          maxWidth: isMobile ? '100%' : '560px',
          width: '100%',
        }}
      >
        <Typography variant="headline1" mt={4} mb={2} textAlign="left">
          {intl.formatMessage({ id: 'serviceBookingTool.title.homeService' })}
        </Typography>
        {serviceVisitBookings && serviceVisitBookings.length > 0 && (
          <ScheduledBookings serviceVisitBookings={serviceVisitBookings} refetchBookings={refetchBookings} />
        )}
        {showScheduler && (
          <Card
            sx={{
              background: '#fff',
            }}
          >
            <TimeBooker setManuallyShowScheduler={setManuallyShowScheduler} refetchBookings={refetchBookings} />
          </Card>
        )}
        {!showScheduler && (
          <Stack alignItems="center" pt={2}>
            {serviceVisitBookings && serviceVisitBookings.length === 0 && (
              <Typography variant="body1" mb={2}>
                {intl.formatMessage({ id: 'booking.no.survey.scheduled' })}
              </Typography>
            )}
            <Button color="primary" variant="contained" onClick={() => setManuallyShowScheduler(true)}>
              {intl.formatMessage({ id: 'serviceBookingTool.button.schedule.maintenance' })}
            </Button>
          </Stack>
        )}
      </Stack>
    </Container>
  );
}
