import { ReactNode, createContext, useContext } from 'react';
import { api } from 'utils/api';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { Box, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { InstallationProject } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

export const InstallationProjectContext = createContext<InstallationProject | null>(null);

type ContextProviderProps = {
  solutionId?: string;
  children: ReactNode;
};

export function InstallationProjectContextProvider({ solutionId, children }: ContextProviderProps) {
  const { data: installationProjectResponse, isLoading: isLoadingInstallationProject } =
    api.InstallationProject.getInstallationProject.useQuery(
      { solutionId: solutionId! },
      {
        enabled: !!solutionId,
      },
    );

  if (isLoadingInstallationProject) {
    return (
      <HeatPumpLoader>
        <Typography variant="h6" px={3}>
          Importing installation project...
        </Typography>
      </HeatPumpLoader>
    );
  }

  if (!installationProjectResponse) {
    return (
      <Box>
        <Typography variant="body1">
          <FormattedMessage
            id="common.error.installationProjectNotFound.title"
            defaultMessage="Installation project not found"
          />
        </Typography>
        {children}
      </Box>
    );
  }

  const installationProjectWithResourceDetails = installationProjectResponse as unknown as InstallationProject;

  return (
    <InstallationProjectContext.Provider value={installationProjectWithResourceDetails}>
      {children}
    </InstallationProjectContext.Provider>
  );
}

export function useInstallationProject() {
  const installationProject = useContext(InstallationProjectContext);
  if (InstallationProject == null) {
    throw new Error('Installation project context not initialised');
  }

  return installationProject;
}
