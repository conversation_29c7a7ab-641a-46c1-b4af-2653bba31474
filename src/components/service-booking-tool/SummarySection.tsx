import { Button } from '@ui/components/Button/Button';
import { Al<PERSON>, Stack, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import TimeRange from 'components/booking-shared/TimeRange';
import { useGroundwork } from 'context/groundwork-context';
import { api } from 'utils/api';
import { useRegionContext } from 'components/booking-shared/RegionContext';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import {
  ServiceVisit,
  ServiceVisitResourceSkill,
} from '@aira/service-visit-grpc-api/build/ts_out/index.com.aira.acquisition.contract.service.visit.v1';
import { QueryObserverResult } from '@tanstack/react-query';
import { JobType } from './types';

export function SummarySection({
  serviceVisitId,
  withDateTime,
  selectedTime,
  selectedDate,
  notes,
  setTimeGridError,
  assignSpecificResource,
  selectedResourceId,
  setSpecificResourceError,
  setManuallyShowScheduler,
  refetchBookings,
  durationMinutes,
  jobType,
}: {
  serviceVisitId?: string;
  withDateTime: boolean;
  selectedTime: { start: Date; end: Date } | null;
  selectedDate: Date | null;
  notes: string;
  setTimeGridError: (message: string) => void;
  assignSpecificResource: boolean;
  selectedResourceId: string | null;
  setSpecificResourceError: (message: string) => void;
  setManuallyShowScheduler: (show: boolean) => void;
  refetchBookings: () => Promise<QueryObserverResult<ServiceVisit[], unknown>>;
  durationMinutes: number;
  jobType: JobType;
}) {
  const intl = useIntl();
  const {
    groundwork: { location },
  } = useGroundwork();
  const region = useRegionContext();
  const formattedAddress = location?.$case === 'exactAddress' ? location.exactAddress.formattedAddress : undefined;
  const [isBookJobRequestPending, setIsBookJobRequestPending] = useState(false);
  const [hasErrors, setHasErrors] = useState(false);
  const energySolutionId = useEnergySolutionId();
  const enabled = !!energySolutionId;
  const { data: energySolution } = api.AiraBackend.getGrpcEnergySolution.useQuery(
    { solution: energySolutionId! },
    { enabled },
  );

  const contactId = energySolution?.solution?.presentation?.contactId?.value;
  const name = `${energySolution?.solution?.presentation?.customer?.firstName} ${energySolution?.solution?.presentation?.customer?.lastName}`;
  const facilityId = energySolution?.solution?.presentation?.facilityId?.value;
  const { mutateAsync: createServiceVisit } = api.ServiceVisits.createServiceVisit.useMutation();

  const { mutateAsync: scheduleServiceVisit } = api.ServiceVisits.scheduleServiceVisit.useMutation();

  const { mutateAsync: updateServiceVisitWithoutSchedule } =
    api.ServiceVisits.updateServiceVisitWithoutSchedule.useMutation();
  const handleScheduleButtonClicked = async () => {
    if (assignSpecificResource && !selectedResourceId) {
      setSpecificResourceError(intl.formatMessage({ id: 'bookingTool.error.noResourceSelected' }));
      return;
    }
    if (withDateTime && !selectedTime) {
      setTimeGridError(intl.formatMessage({ id: 'bookingTool.error.noTimeSlotSelected' }));
      return;
    }
    setIsBookJobRequestPending(true);
    try {
      if (!facilityId) throw new Error('No facility id');
      if (!contactId) throw new Error('No contact id');
      if (!region.id) throw new Error('No region id');
      let serviceVisitIdToUse = serviceVisitId;
      if (!serviceVisitId) {
        const createServiceVisitResponse = await createServiceVisit({
          facilityId,
          jobType,
          durationMinutes,
          notes: notes.length > 0 ? notes : undefined,
          specificResourceId: selectedResourceId ?? undefined,
          operationalUnitId: region.id!.value,
          requiredResourcesCount: 1,
          requiredSkills:
            jobType === JobType.INSTALL_THERMOSTAT
              ? []
              : [ServiceVisitResourceSkill.SERVICE_VISIT_RESOURCE_SKILL_HEAT_PUMP],
          contactId,
          summary: `Service visit | ${name}`,
        });
        if (!createServiceVisitResponse?.serviceVisit) throw new Error('No service visit created');
        serviceVisitIdToUse = createServiceVisitResponse.serviceVisit.id!.value;
        if (!serviceVisitIdToUse) throw new Error('No service visit id');
      }
      if (withDateTime) {
        if (selectedTime) {
          const scheduledServiceVisit = await scheduleServiceVisit({
            serviceVisitId: serviceVisitIdToUse!,
            startsAt: selectedTime.start,
            jobType,
            durationMinutes,
            notes: notes.length > 0 ? notes : undefined,
            specificResourceId: selectedResourceId ?? undefined,
            requiredResourcesCount: 1,
            requiredSkills:
              jobType === JobType.INSTALL_THERMOSTAT
                ? []
                : [ServiceVisitResourceSkill.SERVICE_VISIT_RESOURCE_SKILL_HEAT_PUMP],
          });
          if (!scheduledServiceVisit) throw new Error('No service visit scheduled');
        } else if (!selectedDate) throw new Error('No date selected');
      } else {
        const updatedServiceVisit = await updateServiceVisitWithoutSchedule({
          serviceVisitId: serviceVisitIdToUse!,
          notes: notes.length > 0 ? notes : undefined,
          jobType,
          durationMinutes,
          requiredSkills:
            jobType === JobType.INSTALL_THERMOSTAT
              ? []
              : [ServiceVisitResourceSkill.SERVICE_VISIT_RESOURCE_SKILL_HEAT_PUMP],
        });
        if (!updatedServiceVisit) throw new Error('No service visit updated');
      }
    } catch (_error) {
      setHasErrors(true);
    } finally {
      await refetchBookings();
      setManuallyShowScheduler(false);
      setIsBookJobRequestPending(false);
    }
  };

  // Reset any error on selected time change
  useEffect(() => setHasErrors(false), [selectedTime]);

  return (
    <Stack spacing={4}>
      {hasErrors ? 'has errors' : 'no errors'}
      <Stack spacing={4}>
        <Typography variant="headline2">{intl.formatMessage({ id: 'bookingTool.title.summary' })}</Typography>
        {withDateTime && (
          <Stack>
            <Typography variant="subtitle1">{intl.formatMessage({ id: 'bookingTool.label.dateAndTime' })}</Typography>
            {selectedTime && (
              <Typography variant="body1Emphasis">
                <TimeRange {...selectedTime} variant="normal" />
              </Typography>
            )}
            {!selectedTime && <Typography variant="body1Emphasis">-</Typography>}
          </Stack>
        )}
        <Stack>
          <Typography variant="subtitle1">{intl.formatMessage({ id: 'common.label.address' })}</Typography>
          <Typography variant="body1">{formattedAddress}</Typography>
        </Stack>
      </Stack>
      <Button
        variant="contained"
        sx={{ width: '100%', ':disabled': { color: '#888' } }}
        disabled={isBookJobRequestPending}
        onClick={handleScheduleButtonClicked}
      >
        {intl.formatMessage({ id: 'serviceBookingTool.button.bookServiceJob' })}
      </Button>
      {hasErrors && (
        <Alert severity="error">
          {intl.formatMessage({ id: 'serviceBookingTool.error.maintenanceSchedulingFailure' })}
        </Alert>
      )}
    </Stack>
  );
}
