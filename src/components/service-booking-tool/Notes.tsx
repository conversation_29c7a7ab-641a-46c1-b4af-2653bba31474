import { useEffect, useState } from 'react';
import { Box, IconButton, TextField, Typography } from '@mui/material';
import Edit from '@ui/components/Icons/material/Edit';
import { Button } from '@ui/components/Button/Button';
import { useIntl } from 'react-intl';
import { api } from '../../utils/api';

export default function Notes({
  serviceVisitId,
  notes: initialNotesValue,
}: {
  serviceVisitId: string;
  notes?: string;
}) {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>(initialNotesValue ?? '');
  const intl = useIntl();
  const { mutateAsync: updateServiceVisitNotes } = api.ServiceVisits.updateServiceVisitNotes.useMutation();

  useEffect(() => {
    setNotes(initialNotesValue ?? '');
  }, [initialNotesValue]);

  const handleSaveNotes = async () => {
    await updateServiceVisitNotes({
      serviceVisitId,
      notes,
    });
    setIsEditMode((state) => !state);
  };

  const handleCancelChanges = () => {
    setNotes(initialNotesValue ?? '');
    setIsEditMode(false);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0,
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginTop: 0,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Typography variant="body1Emphasis" sx={{ lineHeight: '40px' }}>
          {intl.formatMessage({ id: 'bookingTool.label.notes' })}
        </Typography>
        {!isEditMode && (
          <IconButton
            onClick={(event) => {
              event.stopPropagation();
              setIsEditMode(true);
            }}
            title="Edit notes"
            sx={{ marginRight: '-6px' }}
          >
            <Edit />
          </IconButton>
        )}
      </Box>
      {isEditMode ? (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '18px',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            width: '100%',
            position: 'relative',
          }}
        >
          <TextField
            fullWidth
            value={notes}
            multiline
            onChange={(event) => {
              setNotes(event.target.value);
            }}
          />
          <Box sx={{ display: 'flex', flexDirection: 'row', gap: '14px' }}>
            <Button onClick={handleSaveNotes}>Save</Button>
            <Button color="error" onClick={handleCancelChanges}>
              Cancel
            </Button>
          </Box>
          <Typography
            variant="body1Emphasis"
            sx={{
              position: 'absolute',
              bottom: '72px',
              right: '8px',
              color: (theme) => theme.palette.error.main,
            }}
          >
            {intl.formatMessage({ id: 'bookingTool.body.unsavedChanges' })}
          </Typography>
        </Box>
      ) : (
        <Typography variant="body1" sx={{ marginTop: '0' }}>
          {notes}
        </Typography>
      )}
    </Box>
  );
}
