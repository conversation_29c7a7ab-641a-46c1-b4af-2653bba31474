import { createTheme } from '@mui/material';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { foregroundThemes } from '@ui/theme/componentsThemes';
import { theme } from '@ui/theme/theme';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { useIsSchedulingSupported } from 'hooks/useIsSchedulingSupported';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { enGB } from 'date-fns/locale';
import { RegionContext } from 'components/booking-shared/RegionContext';
import { GroundworkContext } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import BookingTool from './BookingTool';
import Alert from './Alert';

export default function ServicePage() {
  const solutionTheme = createTheme(theme, foregroundThemes.dark);
  const intl = useIntl();
  const energySolutionId = useEnergySolutionId();
  const enabled = !!energySolutionId;

  const { data } = api.AiraBackend.getGrpcEnergySolution.useQuery({ solution: energySolutionId! }, { enabled });
  const region = data?.solution?.region;
  const { data: groundwork } = api.AiraBackend.getGroundworkForSolution.useQuery(
    { solutionId: energySolutionId! },
    { enabled: !!energySolutionId },
  );

  const {
    isSchedulingSupported,
    loading: schedulingSupportLoading,
    isError: schedulingSupportError,
    schedulingNotSupportedErrorMessage,
  } = useIsSchedulingSupported(energySolutionId);

  if (schedulingSupportLoading) {
    return null;
  }

  if (!groundwork || !region?.id?.value) {
    return (
      <Alert message={`${intl.formatMessage({ id: 'common.error.unableToFindEnergySolution' })} ${energySolutionId}`} />
    );
  }

  if (schedulingSupportError) {
    return <Alert message={intl.formatMessage({ id: 'common.notify.error' })} />;
  }

  if (!isSchedulingSupported) {
    return <Alert message={schedulingNotSupportedErrorMessage} />;
  }

  if (groundwork.location?.$case === 'partialAddress') {
    return <Alert message={<FormattedMessage id="serviceBookingTool.error.partialAddress" />} />;
  }

  return (
    <AiraThemeProvider theme={solutionTheme}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGB}>
        <GroundworkContext.Provider value={groundwork}>
          <RegionContext.Provider value={region}>
            <BookingTool />
          </RegionContext.Provider>
        </GroundworkContext.Provider>
      </LocalizationProvider>
    </AiraThemeProvider>
  );
}
