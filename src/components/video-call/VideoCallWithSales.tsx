// import { Box, CircularProgress, Paper, Stack, Typography, Button } from '@mui/material';
// import { useRef, useEffect, useState, useCallback, useMemo } from 'react';
// import { beige, grey } from '@ui/theme/colors';
// import { BackgroundEffect } from './BackgroundEffectsControl';

// import { useConfigurationAddonStore } from 'components/video-call/sales-visit-tool/src/store/useConfigurationAddonStore';
// import { useScreenStore } from 'components/video-call/sales-visit-tool/src/store/useScreenStore';
// import Timeline from './sales-visit-tool/src/components/atoms/Timeline/Timeline';
// import { Card } from './sales-visit-tool/src/components/atoms/Card/Card';
// import Controls from './Controls';
// import SalesVisitContainer from './SalesVisitContainer';
// import ChatSidebar from './ChatSidebar';
// import type { TimeFrame as EnergyBillsTimeFrame } from './sales-visit-tool/src/components/screens/EnergyBills/TimeFrameContext';
// import type { TimeFrame as SavingsTimeFrame } from './sales-visit-tool/src/components/screens/Savings/TimeFrameContext';
// import { FullSolution } from './sales-visit-tool/src/utils/getFullSolution';
// import { sendRefetchSolution, sendScreenStoreState } from './dataChannelMessages';
// import { ScreenShareLayout } from './ScreenShareLayout';

// interface ChatMessage {
//   id: string;
//   content: string;
//   timestamp: number;
//   sender: 'local' | 'remote';
//   senderName: string;
// }

interface VideoCallProps {
  // meetingType: 'with-sales-flow' | 'only-video';
  roomId: string;
  // energySolution: FullSolution;
  // processedLocalStream: MediaStream | null;
  // selectedVideoDevice: string;
  // selectedAudioDevice: string;
  // videoDevices: MediaDeviceInfo[];
  // audioDevices: MediaDeviceInfo[];
  // setVideoDevices: (devices: MediaDeviceInfo[]) => void;
  // setAudioDevices: (devices: MediaDeviceInfo[]) => void;
  // setSelectedVideoDevice: (deviceId: string) => void;
  // setSelectedAudioDevice: (deviceId: string) => void;
  // setBackgroundEffect: (backgroundEffect: BackgroundEffect) => void;
  // backgroundEffect: BackgroundEffect;
  // setIsHangup: (isHangup: boolean) => void;
}

export function VideoCallWithSales(
  {
    // meetingType,
    // roomId,
    // energySolution,
    // processedLocalStream,
    // selectedVideoDevice,
    // selectedAudioDevice,
    // videoDevices,
    // audioDevices,
    // setVideoDevices,
    // setAudioDevices,
    // setSelectedVideoDevice,
    // setSelectedAudioDevice,
    // setBackgroundEffect,
    // backgroundEffect,
    // setIsHangup,
  }: VideoCallProps,
) {
  return null;
}
//   const remoteVideoRef = useRef<HTMLVideoElement>(null);
//   const screenshareRef = useRef<HTMLVideoElement>(null);
//   const remoteScreenshareRef = useRef<HTMLVideoElement>(null);
//   // Hidden video element for VideoProcessor source (always shows original stream)
//   const sourceVideoRef = useRef<HTMLVideoElement>(null);
//   // Background video while waiting for connection
//   const backgroundVideoRef = useRef<HTMLVideoElement>(null);
//   const backgroundVideo2Ref = useRef<HTMLVideoElement>(null);
//   const { getVideoElement } = useSharedVideoElement();
//   const chatMessagesEndRef = useRef<HTMLDivElement>(null);

//   const [activeBackgroundVideo, setActiveBackgroundVideo] = useState(0); // 0 for first video, 1 for second

//   // Device management state

//   const [isCameraEnabled, setIsCameraEnabled] = useState(true);
//   const [isMicrophoneEnabled, setIsMicrophoneEnabled] = useState(true);
//   const [isScreensharing, setIsScreensharing] = useState(false);
//   const [remoteScreensharing, _setRemoteScreensharing] = useState(false);

//   // Settings popup state
//   const [settingsAnchorEl, setSettingsAnchorEl] = useState<HTMLButtonElement | null>(null);
//   const settingsOpen = Boolean(settingsAnchorEl);
//   const [screenshareMenuAnchorEl, setScreenshareMenuAnchorEl] = useState<HTMLButtonElement | null>(null);
//   const screenshareMenuOpen = Boolean(screenshareMenuAnchorEl);

//   // Chat state
//   const [isChatOpen, setIsChatOpen] = useState(false);
//   const [messages, setMessages] = useState<ChatMessage[]>([]);
//   const [newMessage, setNewMessage] = useState('');
//   const [unreadCount, setUnreadCount] = useState(0);

//   // Collaborative CurrentSetup logic
//   const currentSetupRemoteHandlerRef = useRef<null | ((update: { field: string; value: any }) => void)>(null);

//   // Synced timeframe state for EnergyBills and Savings
//   const [energyBillsTimeFrame, setEnergyBillsTimeFrame] = useState<EnergyBillsTimeFrame>('MONTHLY');
//   const [savingsTimeFrame, setSavingsTimeFrame] = useState<SavingsTimeFrame>('TWENTY_YEARS');

//   // New config-addon-update handler
//   const configAddonRemoteHandlerRef = useRef<null | ((update: { field: string; value: any }) => void)>(null);

//   const [hasHungUp, setHasHungUp] = useState(false);

//   /**
//    * State for remote hover over photo index (controlled by remote peer)
//    */
//   const [remoteHoveredPhotoIndex, setRemoteHoveredPhotoIndex] = useState<number | null>(null);
//   const [connectionStatus, _setConnectionStatus] = useState('new');
//   const [connectionError, setConnectionError] = useState<string | null>(null);

//   const {
//     controller,
//     participants,
//     combinedAudio: _combinedAudio,
//     sendData,
//     onDataMessage,
//     onDataOpen: _onDataOpen,
//     onDataClose: _onDataClose,
//     dataIsOpen,
//   } = useTopologyCall(roomId, {
//     role: 'sales',
//     combineAllAudio: false, // set false for per-user audio
//     localStream: processedLocalStream || new MediaStream(),
//   });

//   // Extract streams from participants
//   const remoteStream = useMemo(() => {
//     const participant = participants[0];
//     if (!participant?.media?.cam) return null;
//     return participant.media.cam;
//   }, [participants]);

//   const remoteScreenshareStream = useMemo(() => {
//     const participant = participants[0];
//     if (!participant?.media?.share) return null;
//     return participant.media.share;
//   }, [participants]);

//   const isConnected = useMemo(() => participants.length > 0, [participants]);

//   // Device enumeration
//   const enumerateDevices = useCallback(async () => {
//     try {
//       const devices = await navigator.mediaDevices.enumerateDevices();
//       const videoDevs = devices.filter((d) => d.kind === 'videoinput');
//       const audioDevs = devices.filter((d) => d.kind === 'audioinput');

//       setVideoDevices(videoDevs);
//       setAudioDevices(audioDevs);
//     } catch (error) {
//       console.error('Error enumerating devices:', error);
//     }
//   }, [setAudioDevices, setVideoDevices]);

//   // Initialize devices on mount
//   useEffect(() => {
//     enumerateDevices();
//   }, [enumerateDevices]);

//   // Camera and microphone controls
//   const toggleCamera = useCallback(() => {
//     const videoTrack = processedLocalStream?.getVideoTracks()[0];
//     if (videoTrack) {
//       videoTrack.enabled = !videoTrack.enabled;
//       setIsCameraEnabled(videoTrack.enabled);
//     }
//   }, [processedLocalStream]);

//   const toggleMicrophone = useCallback(() => {
//     const audioTrack = processedLocalStream?.getAudioTracks()[0];
//     if (audioTrack) {
//       audioTrack.enabled = !audioTrack.enabled;
//       setIsMicrophoneEnabled(audioTrack.enabled);
//     }
//   }, [processedLocalStream]);

//   // Screenshare functions
//   const startScreenshare = useCallback(async () => {
//     try {
//       await controller.startShare();
//       setIsScreensharing(true);
//     } catch (error) {
//       console.error('Error starting screenshare:', error);
//     }
//   }, [controller]);

//   const stopScreenshare = useCallback(async () => {
//     try {
//       await controller.stopShare();
//       setIsScreensharing(false);
//     } catch (error) {
//       console.error('Error stopping screenshare:', error);
//     }
//   }, [controller]);

//   // Leave room function
//   const leaveRoom = useCallback(() => {
//     controller.close();
//   }, [controller]);

//   // Define handleHangup before it's used in useEffect
//   const handleHangup = useCallback(() => {
//     if (hasHungUp) return;
//     setHasHungUp(true);

//     // First: Send call-ended message to remote peer
//     if (dataIsOpen()) {
//       try {
//         sendData(JSON.stringify({ type: 'call-ended' }));
//       } catch (_err) {
//         // Ignore errors if channel is already closed
//       }
//     }

//     // Second: Clean up all media streams
//     if (processedLocalStream) {
//       processedLocalStream.getTracks().forEach((track) => track.stop());
//     }
//     if (remoteStream) {
//       remoteStream.getTracks().forEach((track) => track.stop());
//     }
//     if (remoteScreenshareStream) {
//       remoteScreenshareStream.getTracks().forEach((track) => track.stop());
//     }

//     // Third: Clean up video elements
//     const video = getVideoElement();
//     if (video) {
//       video.srcObject = null;
//     }
//     if (remoteVideoRef.current) {
//       remoteVideoRef.current.srcObject = null;
//     }
//     if (screenshareRef.current) {
//       screenshareRef.current.srcObject = null;
//     }
//     if (remoteScreenshareRef.current) {
//       remoteScreenshareRef.current.srcObject = null;
//     }
//     if (sourceVideoRef.current) {
//       sourceVideoRef.current.srcObject = null;
//     }
//     if (backgroundVideoRef.current) {
//       backgroundVideoRef.current.srcObject = null;
//     }
//     if (backgroundVideo2Ref.current) {
//       backgroundVideo2Ref.current.srcObject = null;
//     }

//     // LAST: Clean up the room after all other cleanup is done
//     leaveRoom();

//     setIsHangup(true);
//   }, [
//     dataIsOpen,
//     sendData,
//     processedLocalStream,
//     remoteStream,
//     remoteScreenshareStream,
//     setIsHangup,
//     hasHungUp,
//     leaveRoom,
//     getVideoElement,
//   ]);

//   // Set up data message handler for all data channel messages
//   useEffect(() => {
//     const handleDataMessage = (event: MessageEvent) => {
//       try {
//         const data = JSON.parse(event.data);

//         if (data.type === 'chat-message') {
//           const chatMessage: ChatMessage = {
//             id: data.id,
//             content: data.content,
//             timestamp: data.timestamp,
//             sender: 'remote',
//             senderName: data.senderName || 'Remote User',
//           };
//           setMessages((prev) => [...prev, chatMessage]);

//           // Increment unread count if chat is closed
//           if (!isChatOpen) {
//             setUnreadCount((prev) => prev + 1);
//           }
//         } else if (data.type === 'current-setup-update') {
//           // Forward to CurrentSetup if handler is registered
//           if (currentSetupRemoteHandlerRef.current) {
//             currentSetupRemoteHandlerRef.current({ field: data.field, value: data.value });
//           }
//         } else if (data.type === 'timeframe-update') {
//           if (data.target === 'energy-bills') {
//             setEnergyBillsTimeFrame(data.value);
//           } else if (data.target === 'savings') {
//             setSavingsTimeFrame(data.value);
//           }
//           return;
//         } else if (data.type === 'config-addon-update') {
//           console.log('[VideoCall] Received config-addon-update:', data);
//           // Forward to ConfigurationBox if handler is registered
//           if (configAddonRemoteHandlerRef.current) {
//             configAddonRemoteHandlerRef.current({ field: data.field, value: data.value });
//           }
//         } else if (data.type === 'quote-url-update') {
//           // Only update if value is different
//           if (data.value !== useScreenStore.getState().quoteUrl) {
//             useScreenStore.getState().setQuoteUrl(data.value);
//           }
//         } else if (data.type === 'call-ended') {
//           // Prevent recursion: only handle if not already hung up
//           if (!hasHungUp) {
//             handleHangup();
//           }
//         } else if (data.type === 'table-row') {
//           // Handle table row click from remote peer
//           console.log('[VideoCall] Received table-row click:', data.titleRow);
//           const element = document.querySelector(`[data-field="${data.titleRow}"]`) as HTMLElement;
//           if (!element) {
//             return;
//           }
//           element.scrollIntoView({
//             behavior: 'smooth',
//             block: 'center',
//             inline: 'nearest',
//           });

//           // Add visual indicator that this field is being hovered by remote user
//           element.style.background = 'linear-gradient(to right,#FF981F 10%, #9A908D 50%, #FF981F 60%)';
//           element.style.backgroundSize = '200% auto';
//           element.style.backgroundClip = 'text';
//           element.style.webkitTextFillColor = 'transparent';
//           element.style.animation = 'textclip 1.5s linear infinite';
//           element.style.borderLeft = '2px solid #FF981F';

//           // Remove the indicator after a delay
//           setTimeout(() => {
//             if (element) {
//               element.style.background = '';
//               element.style.backgroundSize = '';
//               element.style.backgroundClip = '';
//               element.style.webkitTextFillColor = '';
//               element.style.animation = '';
//               element.style.borderLeft = '2px solid transparent';
//             }
//           }, 2000);
//         } else if (data.type === 'photo-hover-customer') {
//           // Set remote hover photo index from remote peer
//           setRemoteHoveredPhotoIndex(typeof data.index === 'number' ? data.index : null);
//         } else if (data.type === 'presentation-video-control') {
//           // Handle remote play/pause event for presentation video
//           // For now, just log it
//           console.log('[VideoCall] Received presentation-video-control:', data);
//         }
//       } catch (error) {
//         console.error('Error parsing data channel message:', error);
//       }
//     };

//     // Register the data message handler
//     const unsubscribe = onDataMessage(handleDataMessage);

//     return unsubscribe;
//   }, [onDataMessage, isChatOpen, hasHungUp, handleHangup]);

//   const solutionId = roomId;
//   const { setNonHeatingConsumption } = useConfigurationAddonStore();
//   const currentScreen = useScreenStore((state) => state.currentScreen);

//   useEffect(() => {
//     if (!solutionId) return;
//     const keyExists = sessionStorage.getItem(solutionId);
//     useScreenStore.persist.setOptions({ name: solutionId });
//     if (!keyExists) {
//       useScreenStore.setState(useScreenStore.getInitialState());
//     }

//     void useScreenStore.persist.rehydrate();

//     setNonHeatingConsumption(energySolution.facility.nonHeatingConsumptionKwh);
//   }, [solutionId, setNonHeatingConsumption, energySolution.facility.nonHeatingConsumptionKwh]);

//   // Use the data channel from useTopologyCall
//   const _activeDataChannel = dataIsOpen() ? { send: sendData, readyState: 'open' } : null;

//   // Listen for screen changes and sync via data channel
//   useEffect(() => {
//     if (dataIsOpen()) {
//       const message = {
//         type: 'screen-update',
//         screen: currentScreen,
//       };
//       sendData(JSON.stringify(message));
//     }
//   }, [currentScreen, dataIsOpen, sendData]);

//   /**
//    * Toggle chat visibility and reset unread count
//    */
//   const toggleChat = useCallback(() => {
//     setIsChatOpen((prev) => {
//       const newState = !prev;
//       if (newState) {
//         setUnreadCount(0);
//       }
//       return newState;
//     });
//   }, []);

//   /**
//    * Send a chat message to the remote peer
//    */
//   const sendChatMessage = useCallback(
//     (content: string) => {
//       if (!content.trim() || !dataIsOpen()) {
//         return;
//       }

//       const message: ChatMessage = {
//         id: `${Date.now()}-${Math.random()}`,
//         content: content.trim(),
//         timestamp: Date.now(),
//         sender: 'local',
//         senderName: 'Aira Expert',
//       };

//       // Add to local messages
//       setMessages((prev) => [...prev, message]);

//       // Send to remote peer
//       const messageData = {
//         type: 'chat-message',
//         id: message.id,
//         content: message.content,
//         timestamp: message.timestamp,
//         senderName: message.senderName,
//       };

//       try {
//         sendData(JSON.stringify(messageData));
//       } catch (error) {
//         console.error('VideoCall: Error sending chat message:', error);
//       }
//     },
//     [dataIsOpen, sendData],
//   );

//   /**
//    * Handle chat message submission
//    */
//   const handleSendMessage = useCallback(() => {
//     if (newMessage.trim()) {
//       sendChatMessage(newMessage);
//       setNewMessage('');
//     }
//   }, [newMessage, sendChatMessage]);

//   /**
//    * Scroll chat messages to bottom
//    */
//   const scrollChatToBottom = useCallback(() => {
//     if (chatMessagesEndRef.current) {
//       chatMessagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
//     }
//   }, []);

//   // Scroll chat to bottom when new messages arrive
//   useEffect(() => {
//     scrollChatToBottom();
//   }, [messages, scrollChatToBottom]);

//   // Reset unread count when chat is opened
//   useEffect(() => {
//     if (isChatOpen) {
//       setUnreadCount(0);
//     }
//   }, [isChatOpen]);

//   useEffect(() => {
//     if (remoteVideoRef.current && remoteStream) {
//       remoteVideoRef.current.srcObject = remoteStream;
//       // Attempt to autoplay remote stream; some browsers require explicit play()
//       requestAnimationFrame(() => {
//         remoteVideoRef.current?.play().catch((err) => {
//           console.warn('remote video play() failed', err);
//         });
//       });
//     } else if (remoteVideoRef.current && !remoteStream) {
//       remoteVideoRef.current.srcObject = null;
//     }
//   }, [remoteStream]);

//   useEffect(() => {
//     if (remoteScreenshareRef.current && remoteScreenshareStream) {
//       remoteScreenshareRef.current.srcObject = remoteScreenshareStream;
//     } else if (remoteScreenshareRef.current && !remoteScreenshareStream) {
//       remoteScreenshareRef.current.srcObject = null;
//     }
//   }, [remoteScreenshareStream]);

//   const hasScreenshare = isScreensharing || remoteScreensharing;

//   const handleScreenshareToggle = (event: React.MouseEvent<HTMLButtonElement>) => {
//     if (isScreensharing) {
//       setScreenshareMenuAnchorEl(event.currentTarget);
//     } else {
//       startScreenshare();
//     }
//   };

//   // Set up background video cycling
//   useEffect(() => {
//     if (!isConnected && (backgroundVideoRef.current || backgroundVideo2Ref.current)) {
//       const video1 = backgroundVideoRef.current;
//       const video2 = backgroundVideo2Ref.current;

//       // Configure both videos
//       [video1, video2].forEach((video) => {
//         if (video) {
//           video.muted = true;
//           video.loop = true;
//           video.playsInline = true;
//         }
//       });

//       // Start playing the active video
//       const activeVideo = activeBackgroundVideo === 0 ? video1 : video2;
//       const inactiveVideo = activeBackgroundVideo === 0 ? video2 : video1;

//       if (activeVideo) {
//         activeVideo.play().catch((error) => {
//           console.log('Background video autoplay prevented:', error);
//         });
//       }

//       if (inactiveVideo) {
//         inactiveVideo.pause();
//       }

//       // Set up cycling interval (switch every 10 seconds)
//       const interval = setInterval(() => {
//         setActiveBackgroundVideo((prev) => (prev === 0 ? 1 : 0));
//       }, 10000);

//       return () => clearInterval(interval);
//     } else {
//       // Pause both videos when connected
//       if (backgroundVideoRef.current) {
//         backgroundVideoRef.current.pause();
//       }
//       if (backgroundVideo2Ref.current) {
//         backgroundVideo2Ref.current.pause();
//       }
//     }
//   }, [isConnected, activeBackgroundVideo]);

//   const handleSettingsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
//     setSettingsAnchorEl(event.currentTarget);
//   };

//   const handleSettingsClose = () => {
//     setSettingsAnchorEl(null);
//   };

//   const handleScreenshareMenuClose = () => {
//     setScreenshareMenuAnchorEl(null);
//   };

//   const handleStopScreenshare = () => {
//     stopScreenshare();
//     setScreenshareMenuAnchorEl(null);
//   };

//   const handleChangeScreenshare = async () => {
//     // Stop current screenshare and start new one
//     stopScreenshare();
//     setScreenshareMenuAnchorEl(null);
//     // Small delay to ensure cleanup is complete
//     setTimeout(() => {
//       startScreenshare();
//     }, 100);
//   };

//   // Update renderLocalVideo to accept a fullHeight prop
//   const renderLocalVideo = useCallback(
//     (fullHeight = false, _hasRef = false) => {
//       return (
//         <VideoContainer
//           style={{
//             width: '100%',
//             height: fullHeight ? '100%' : isConnected ? 'unset' : '100%',
//             objectFit: hasScreenshare ? 'contain' : 'cover',
//             borderRadius: meetingType === 'with-sales-flow' ? '0px' : '16px 16px 0px 0px',
//           }}
//           onVideoReady={(video) => {
//             // Set up the video element when it's ready
//             if (!video.srcObject && processedLocalStream) {
//               console.log('Setting srcObject in onVideoReady');
//               video.srcObject = processedLocalStream;
//             }
//           }}
//         />
//       );
//     },
//     [isConnected, processedLocalStream, meetingType, hasScreenshare],
//   );

//   // Before rendering the Carousel, log the currentScreen
//   if (isConnected) {
//     // Rendering Carousel with currentScreen
//   }

//   const registerConfigAddonRemoteHandler = useCallback((handler: (update: { field: string; value: any }) => void) => {
//     configAddonRemoteHandlerRef.current = handler;
//   }, []);

//   // Track when peer joins to trigger data resending
//   const [peerRejoined, setPeerRejoined] = useState(false);

//   // Send all data when connection is established
//   const sendAllDataOnConnection = useCallback(() => {
//     if (dataIsOpen()) {
//       // Set peer rejoined flag to trigger data resending in components
//       setPeerRejoined(true);

//       // Send solution data
//       if (energySolution) {
//         sendRefetchSolution({ send: sendData, readyState: 'open' } as any, energySolution);
//       }

//       // Send current screen store state
//       const currentState = useScreenStore.getState();
//       sendScreenStoreState({ send: sendData, readyState: 'open' } as any, currentState);

//       // Note: Savings and impact savings data will be sent by their respective components
//       // when the queries complete, so we don't need to send them here

//       // Products data will be sent by the Configuration component when available
//     }
//   }, [dataIsOpen, sendData, energySolution]);

//   // Log when the data channel is opened and send all data
//   useEffect(() => {
//     if (dataIsOpen()) {
//       // Send all data when connection is established
//       sendData(JSON.stringify({ type: 'meeting-type', value: meetingType }));
//       sendAllDataOnConnection();
//     }
//   }, [dataIsOpen, sendData, sendAllDataOnConnection, meetingType]);

//   // Reset peerRejoined flag after a short delay to allow components to process it
//   useEffect(() => {
//     if (peerRejoined) {
//       const timer = setTimeout(() => {
//         setPeerRejoined(false);
//       }, 1000); // Reset after 1 second
//       return () => clearTimeout(timer);
//     }
//   }, [peerRejoined]);

//   return (
//     <Box
//       sx={{
//         height: '100dvh',
//         maxHeight: '100dvh',
//         width: '100dvw',
//         bgcolor: beige[150],
//         display: 'flex',
//         flexDirection: 'column',
//         overflow: 'hidden',
//       }}
//     >
//       {/* Video containers */}
//       <Box
//         sx={{
//           display: 'flex',
//           gap: 2,
//           p: 2,
//           height: '100%',
//           position: 'relative',
//           overflow: 'hidden',
//         }}
//       >
//         {hasScreenshare ? (
//           <ScreenShareLayout
//             isChatOpen={isChatOpen}
//             remoteScreensharing={remoteScreensharing}
//             remoteScreenshareRef={remoteScreenshareRef}
//             screenshareRef={screenshareRef}
//             remoteVideoRef={remoteVideoRef}
//             isConnected={isConnected}
//             energySolution={energySolution}
//             renderLocalVideo={renderLocalVideo}
//           >
//             <Controls
//               meetingType={meetingType}
//               toggleCamera={toggleCamera}
//               isCameraEnabled={isCameraEnabled}
//               toggleMicrophone={toggleMicrophone}
//               isMicrophoneEnabled={isMicrophoneEnabled}
//               handleScreenshareToggle={handleScreenshareToggle}
//               isScreensharing={isScreensharing}
//               screenshareMenuOpen={screenshareMenuOpen}
//               screenshareMenuAnchorEl={screenshareMenuAnchorEl}
//               handleScreenshareMenuClose={handleScreenshareMenuClose}
//               handleStopScreenshare={handleStopScreenshare}
//               handleChangeScreenshare={handleChangeScreenshare}
//               unreadCount={unreadCount}
//               toggleChat={toggleChat}
//               isChatOpen={isChatOpen}
//               settingsOpen={settingsOpen}
//               settingsAnchorEl={settingsAnchorEl}
//               handleSettingsClose={handleSettingsClose}
//               backgroundEffect={backgroundEffect}
//               setBackgroundEffect={setBackgroundEffect}
//               selectedVideoDevice={selectedVideoDevice}
//               switchVideoDevice={setSelectedVideoDevice}
//               selectedAudioDevice={selectedAudioDevice}
//               switchAudioDevice={setSelectedAudioDevice}
//               videoDevices={videoDevices}
//               audioDevices={audioDevices}
//               handleSettingsClick={handleSettingsClick}
//               handleHangup={handleHangup}
//             />
//           </ScreenShareLayout>
//         ) : (
//           <>
//             {isConnected && meetingType === 'with-sales-flow' && dataIsOpen() && (
//               <SalesVisitContainer
//                 activeDataChannel={{ send: sendData, readyState: 'open' } as any}
//                 isChatOpen={isChatOpen}
//                 energyBillsTimeFrame={energyBillsTimeFrame}
//                 savingsTimeFrame={savingsTimeFrame}
//                 registerConfigAddonRemoteHandler={registerConfigAddonRemoteHandler}
//                 peerRejoined={peerRejoined}
//                 remoteHoveredPhotoIndex={remoteHoveredPhotoIndex}
//                 setEnergyBillsTimeFrame={setEnergyBillsTimeFrame}
//                 setSavingsTimeFrame={setSavingsTimeFrame}
//               />
//             )}

//             <Paper
//               elevation={3}
//               sx={{
//                 flex: 1,
//                 display: 'flex',
//                 flexDirection: 'column',
//                 gap: 0,
//                 justifyContent: 'flex-start',
//                 position: 'relative',
//                 overflow: 'hidden',
//                 borderRadius: 2,
//                 bgcolor: grey[100],
//                 marginRight: isChatOpen ? 2 : 0,
//                 transition: 'margin-right 0.3s ease-in-out',
//                 height: '100%',
//                 minHeight: 0, // Allow flex children to shrink vertically
//                 visibility: isConnected ? 'visible' : 'hidden',
//               }}
//             >
//               <Stack
//                 sx={{
//                   height: '100%',
//                   minHeight: meetingType === 'only-video' ? '100%' : 0, // Allow video to shrink vertically with window
//                   flex: '1 1 0%', // Take available space, shrink as needed
//                 }}
//               >
//                 <video
//                   ref={remoteVideoRef}
//                   autoPlay
//                   playsInline
//                   style={{
//                     height: '100%',
//                     width: '100%',
//                     objectFit: 'cover',
//                     backgroundColor: remoteStream ? 'transparent' : '#f0f0f0', // Visual indicator
//                   }}
//                 />
//                 {!remoteStream && (
//                   <Box
//                     sx={{
//                       position: 'absolute',
//                       top: '50%',
//                       left: '50%',
//                       transform: 'translate(-50%, -50%)',
//                       color: 'text.secondary',
//                       textAlign: 'center',
//                     }}
//                   >
//                     <Typography variant="body2">
//                       {isConnected ? 'Connected but no video stream' : 'Waiting for connection...'}
//                     </Typography>
//                   </Box>
//                 )}
//               </Stack>
//               <Stack
//                 sx={{
//                   backgroundColor: '#22222680',
//                   p: '4px 12px',
//                   position: 'relative',
//                   bottom: 42,
//                   left: 12,
//                   width: 'fit-content',
//                   borderRadius: 1,
//                   color: grey[100],
//                 }}
//               >
//                 <Typography variant="body1Emphasis" color={grey[100]}>
//                   {energySolution.customer.name}
//                 </Typography>
//               </Stack>
//               <Stack
//                 sx={{
//                   position: meetingType === 'with-sales-flow' ? 'relative' : 'absolute',
//                   bottom: meetingType === 'with-sales-flow' ? 'unset' : 20,
//                   right: meetingType === 'with-sales-flow' ? 'unset' : 20,
//                   flex: '0 0 auto', // Controls take only as much space as needed, never shrink to zero
//                   boxShadow: meetingType === 'with-sales-flow' ? 'none' : '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
//                   borderRadius: meetingType === 'with-sales-flow' ? '0px' : '16px',
//                 }}
//               >
//                 <Box
//                   sx={{
//                     display: 'flex',
//                     position: 'relative',
//                     width: meetingType === 'with-sales-flow' ? '100%' : '440px',
//                   }}
//                 >
//                   {renderLocalVideo(false, isConnected)}
//                   <Box
//                     sx={{
//                       backgroundColor: '#22222680',
//                       p: '8px 16px',
//                       position: 'absolute',
//                       bottom: 10,
//                       left: 6,
//                       width: 'fit-content',
//                       borderRadius: 1,
//                       color: grey[100],
//                     }}
//                   >
//                     <Typography variant="body1Emphasis" color={grey[100]}>
//                       You
//                     </Typography>
//                   </Box>
//                 </Box>

//                 <Controls
//                   meetingType={meetingType}
//                   toggleCamera={toggleCamera}
//                   isCameraEnabled={isCameraEnabled}
//                   toggleMicrophone={toggleMicrophone}
//                   isMicrophoneEnabled={isMicrophoneEnabled}
//                   handleScreenshareToggle={handleScreenshareToggle}
//                   isScreensharing={isScreensharing}
//                   screenshareMenuOpen={screenshareMenuOpen}
//                   screenshareMenuAnchorEl={screenshareMenuAnchorEl}
//                   handleScreenshareMenuClose={handleScreenshareMenuClose}
//                   handleStopScreenshare={handleStopScreenshare}
//                   handleChangeScreenshare={handleChangeScreenshare}
//                   unreadCount={unreadCount}
//                   toggleChat={toggleChat}
//                   isChatOpen={isChatOpen}
//                   settingsOpen={settingsOpen}
//                   settingsAnchorEl={settingsAnchorEl}
//                   handleSettingsClose={handleSettingsClose}
//                   backgroundEffect={backgroundEffect}
//                   setBackgroundEffect={setBackgroundEffect}
//                   selectedVideoDevice={selectedVideoDevice}
//                   switchVideoDevice={setSelectedVideoDevice}
//                   selectedAudioDevice={selectedAudioDevice}
//                   switchAudioDevice={setSelectedAudioDevice}
//                   videoDevices={videoDevices}
//                   audioDevices={audioDevices}
//                   handleSettingsClick={handleSettingsClick}
//                   handleHangup={handleHangup}
//                 />
//               </Stack>
//             </Paper>
//             {!isConnected && (
//               <Box
//                 sx={{
//                   position: 'absolute',
//                   top: 0,
//                   left: 0,
//                   right: 0,
//                   bottom: 0,
//                   borderRadius: 2,
//                   overflow: 'hidden',
//                 }}
//               >
//                 {/* Background video */}
//                 <video
//                   ref={backgroundVideoRef}
//                   muted
//                   loop
//                   playsInline
//                   style={{
//                     width: '100%',
//                     height: '100%',
//                     objectFit: 'cover',
//                     opacity: activeBackgroundVideo === 0 ? 1 : 0,
//                     transition: 'opacity 1s ease-in-out',
//                   }}
//                 >
//                   <source src="/videos/bg-video.mp4" type="video/mp4" />
//                   Your browser does not support the video tag.
//                 </video>

//                 {/* Second background video */}
//                 <video
//                   ref={backgroundVideo2Ref}
//                   muted
//                   loop
//                   playsInline
//                   style={{
//                     position: 'absolute',
//                     top: 0,
//                     left: 0,
//                     width: '100%',
//                     height: '100%',
//                     objectFit: 'cover',
//                     opacity: activeBackgroundVideo === 1 ? 1 : 0,
//                     transition: 'opacity 1s ease-in-out',
//                   }}
//                 >
//                   <source src="/videos/compressed-heatpump.mp4" type="video/mp4" />
//                   Your browser does not support the video tag.
//                 </video>

//                 {/* Overlay with waiting message */}
//                 <Box
//                   sx={{
//                     position: 'absolute',
//                     top: 0,
//                     left: 0,
//                     right: 0,
//                     bottom: 0,
//                     display: 'flex',
//                     alignItems: 'center',
//                     justifyContent: 'center',
//                     bgcolor: 'rgba(0, 0, 0, 0.4)',
//                   }}
//                 >
//                   <Box
//                     sx={{
//                       textAlign: 'center',
//                       bgcolor: grey[100],
//                       color: grey[900],
//                       px: 3,
//                       py: 2,
//                       borderRadius: 2,
//                     }}
//                   >
//                     <Typography variant="headline3" gutterBottom>
//                       Waiting for {energySolution.customer.name} to join...
//                     </Typography>
//                     <Typography variant="body2" color={grey[500]}>
//                       Share the room link to start the call
//                     </Typography>
//                   </Box>
//                 </Box>
//                 {/* Local video */}

//                 <Paper
//                   elevation={3}
//                   sx={{
//                     width: 240,
//                     height: 180,
//                     position: 'absolute',
//                     bottom: 24,
//                     right: isChatOpen ? 394 : 24,
//                     overflow: 'hidden',
//                     borderRadius: 2,
//                     bgcolor: 'grey.900',
//                     transition: 'right 0.3s ease-in-out',
//                   }}
//                 >
//                   {renderLocalVideo(false, true)}
//                 </Paper>
//               </Box>
//             )}
//           </>
//         )}

//         {isChatOpen && (
//           <ChatSidebar
//             toggleChat={toggleChat}
//             messages={messages}
//             newMessage={newMessage}
//             setNewMessage={setNewMessage}
//             handleSendMessage={handleSendMessage}
//             chatMessagesEndRef={chatMessagesEndRef}
//           />
//         )}
//       </Box>

//       {/* Hidden source video element for VideoProcessor */}
//       <video
//         ref={sourceVideoRef}
//         autoPlay
//         playsInline
//         muted
//         style={{
//           position: 'absolute',
//           top: '-9999px',
//           left: '-9999px',
//           width: '1px',
//           height: '1px',
//           opacity: 0,
//           pointerEvents: 'none',
//         }}
//         onLoadedMetadata={() => {
//           if (sourceVideoRef.current) {
//             sourceVideoRef.current.play().catch(console.error);
//           }
//         }}
//         onPause={() => {
//           if (sourceVideoRef.current) {
//             sourceVideoRef.current.play().catch(console.error);
//           }
//         }}
//       />

//       {isConnected && meetingType === 'with-sales-flow' && (
//         <Card
//           sx={{
//             backgroundColor: '#fff',
//             borderRadius: '16px 16px 0px 0px',
//             margin: '0 16px',
//             boxShadow:
//               '0px 3px 3px -2px rgba(0, 0, 0, 0.2), 0px 3px 4px 0px rgba(0, 0, 0, 0.14), 0px 1px 8px 0px rgba(0, 0, 0, 0.12)',
//           }}
//         >
//           <Timeline />
//         </Card>
//       )}
//       {!isConnected && (
//         <Controls
//           meetingType={meetingType}
//           toggleCamera={toggleCamera}
//           isCameraEnabled={isCameraEnabled}
//           toggleMicrophone={toggleMicrophone}
//           isMicrophoneEnabled={isMicrophoneEnabled}
//           handleScreenshareToggle={handleScreenshareToggle}
//           isScreensharing={isScreensharing}
//           screenshareMenuOpen={screenshareMenuOpen}
//           screenshareMenuAnchorEl={screenshareMenuAnchorEl}
//           handleScreenshareMenuClose={handleScreenshareMenuClose}
//           handleStopScreenshare={handleStopScreenshare}
//           handleChangeScreenshare={handleChangeScreenshare}
//           unreadCount={unreadCount}
//           toggleChat={toggleChat}
//           isChatOpen={isChatOpen}
//           settingsOpen={settingsOpen}
//           settingsAnchorEl={settingsAnchorEl}
//           handleSettingsClose={handleSettingsClose}
//           backgroundEffect={backgroundEffect}
//           setBackgroundEffect={setBackgroundEffect}
//           selectedVideoDevice={selectedVideoDevice}
//           switchVideoDevice={setSelectedVideoDevice}
//           selectedAudioDevice={selectedAudioDevice}
//           switchAudioDevice={setSelectedAudioDevice}
//           videoDevices={videoDevices}
//           audioDevices={audioDevices}
//           handleSettingsClick={handleSettingsClick}
//           handleHangup={handleHangup}
//         />
//       )}
//       {connectionStatus === 'connecting' && (
//         <Box
//           sx={{
//             position: 'absolute',
//             top: 0,
//             left: 0,
//             right: 0,
//             bottom: 0,
//             display: 'flex',
//             justifyContent: 'center',
//             alignItems: 'center',
//             zIndex: 1000,
//             backgroundColor: 'rgba(0, 0, 0, 0.5)',
//           }}
//         >
//           <Stack direction="row" alignItems="center" justifyContent="center" gap={2}>
//             <CircularProgress />
//             <Typography variant="body1" color="white">
//               Connecting...
//             </Typography>
//           </Stack>
//         </Box>
//       )}

//       {connectionError && (
//         <Box
//           sx={{
//             position: 'absolute',
//             top: 0,
//             left: 0,
//             right: 0,
//             bottom: 0,
//             display: 'flex',
//             justifyContent: 'center',
//             alignItems: 'center',
//             zIndex: 1000,
//             backgroundColor: 'rgba(0, 0, 0, 0.5)',
//           }}
//         >
//           <Box
//             sx={{
//               textAlign: 'center',
//               bgcolor: 'white',
//               color: 'error.main',
//               px: 3,
//               py: 2,
//               borderRadius: 2,
//               maxWidth: 400,
//             }}
//           >
//             <Typography variant="h6" gutterBottom>
//               Connection Error
//             </Typography>
//             <Typography variant="body2" color="text.secondary">
//               {connectionError}
//             </Typography>
//             <Box sx={{ mt: 2 }}>
//               <Typography variant="body2" color="text.secondary">
//                 If the problem persists, try refreshing the page or check your network connection.
//               </Typography>
//               <Button
//                 variant="contained"
//                 onClick={() => {
//                   setConnectionError(null);
//                   // The WebRTC hook will automatically attempt to reconnect
//                   // when the peer rejoins or when a new connection is initiated
//                 }}
//                 sx={{ mt: 2 }}
//               >
//                 Try Again
//               </Button>
//             </Box>
//           </Box>
//         </Box>
//       )}
//     </Box>
//   );
// }
