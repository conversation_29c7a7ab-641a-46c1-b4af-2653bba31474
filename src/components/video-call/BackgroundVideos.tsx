import { useRef, useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';

export default function BackgroundVideos({ customerName }: { customerName: string }) {
  const [activeBackgroundVideo, setActiveBackgroundVideo] = useState(0); // 0 for first video, 1 for second
  // Background video while waiting for connection
  const backgroundVideoRef = useRef<HTMLVideoElement>(null);
  const backgroundVideo2Ref = useRef<HTMLVideoElement>(null);
  // Set up background video cycling
  useEffect(() => {
    if (backgroundVideoRef.current || backgroundVideo2Ref.current) {
      const video1 = backgroundVideoRef.current;
      const video2 = backgroundVideo2Ref.current;

      // Configure both videos
      [video1, video2].forEach((video) => {
        if (video) {
          video.muted = true;
          video.loop = true;
          video.playsInline = true;
        }
      });

      // Start playing the active video
      const activeVideo = activeBackgroundVideo === 0 ? video1 : video2;
      const inactiveVideo = activeBackgroundVideo === 0 ? video2 : video1;

      if (activeVideo) {
        activeVideo.play().catch((error) => {
          console.log('Background video autoplay prevented:', error);
        });
      }

      if (inactiveVideo) {
        inactiveVideo.pause();
      }

      // Set up cycling interval (switch every 10 seconds)
      const interval = setInterval(() => {
        setActiveBackgroundVideo((prev) => (prev === 0 ? 1 : 0));
      }, 10000);

      return () => clearInterval(interval);
    }
  }, [activeBackgroundVideo]);
  return (
    <>
      {/* Background video */}
      <video
        ref={backgroundVideoRef}
        muted
        loop
        playsInline
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: activeBackgroundVideo === 0 ? 1 : 0,
          transition: 'opacity 1s ease-in-out',
        }}
      >
        <source src="/videos/bg-video.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Second background video */}
      <video
        ref={backgroundVideo2Ref}
        muted
        loop
        playsInline
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: activeBackgroundVideo === 1 ? 1 : 0,
          transition: 'opacity 1s ease-in-out',
        }}
      >
        <source src="/videos/compressed-heatpump.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Overlay with waiting message */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'rgba(0, 0, 0, 0.4)',
        }}
      >
        <Box
          sx={{
            textAlign: 'center',
            bgcolor: grey[100],
            color: grey[900],
            px: 3,
            py: 2,
            borderRadius: 2,
          }}
        >
          <Typography variant="headline3" gutterBottom>
            Waiting for {customerName} to join...
          </Typography>
          <Typography variant="body2" color={grey[500]}>
            Share the room link to start the call
          </Typography>
        </Box>
      </Box>
    </>
  );
}
