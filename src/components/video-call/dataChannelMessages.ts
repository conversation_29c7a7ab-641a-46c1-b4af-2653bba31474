import { TimeFrame } from './sales-visit-tool/src/components/screens/EnergyBills/TimeFrameContext';
import { TimeFrame as SavingsTimeFrame } from './sales-visit-tool/src/components/screens/Savings/TimeFrameContext';
import { ScreenStoreState } from './sales-visit-tool/src/store/useScreenStore';

export const sendMeetingType = (activeDataChannel: RTCDataChannel, meetingType: 'with-sales-flow' | 'only-video') => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'meeting-type', value: meetingType }));
  }
};

// Function to send local updates to peer
export const sendCurrentSetupUpdate = (activeDataChannel: RTCDataChannel, update: { field: string; value: any }) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'current-setup-update', ...update }));
  }
};

// Add sendConfigAddonUpdate and registerConfigAddonRemoteHandler
export const sendConfigAddonUpdate = (activeDataChannel: RTCDataChannel, update: { field: string; value: any }) => {
  console.log('[VideoCall] Sending config-addon-update:', update);
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'config-addon-update', ...update }));
  }
};

// Function to send solution data to peer
export const sendRefetchSolution = (activeDataChannel: RTCDataChannel, solutionData?: any) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open' && solutionData) {
    // Send the actual solution data
    activeDataChannel.send(JSON.stringify({ type: 'solution-update', solution: solutionData }));
  }
};

// Function to send savings data to peer
export const sendSavingsData = (activeDataChannel: RTCDataChannel, savingsData?: any) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open' && savingsData) {
    // Send the savings data
    activeDataChannel.send(JSON.stringify({ type: 'savings-update', savings: savingsData }));
  }
};

// Function to send impact savings data to peer
export const sendImpactSavingsData = (activeDataChannel: RTCDataChannel, impactSavingsData?: any) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open' && impactSavingsData) {
    // Send the impact savings data
    activeDataChannel.send(JSON.stringify({ type: 'impact-savings-update', impactSavings: impactSavingsData }));
  }
};

export const sendProductsData = (activeDataChannel: RTCDataChannel, productsData?: any) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open' && productsData) {
    // Send the products data
    activeDataChannel.send(JSON.stringify({ type: 'products-update', products: productsData }));
  }
};

// Function to send current screen store state to peer
export const sendScreenStoreState = (activeDataChannel: RTCDataChannel, currentState: ScreenStoreState) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'screen-store-state', state: currentState }));
  }
};

// Handler to change slide and sync via data channel
export const sendPresentationSlideIndex = (activeDataChannel: RTCDataChannel, index: number) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'presentation-slide', index }));
  }
};

// Function to send quoteUrl update to peer
export const sendQuoteUrlUpdate = (url: string | null, activeDataChannel: RTCDataChannel) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'quote-url-update', value: url }));
  }
};

/**
 * Send photo hover update to remote peer
 */
export const sendPhotoHover = (activeDataChannel: RTCDataChannel, photoIndex: number | null) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'photo-hover-sales', index: photoIndex }));
  }
};

/**
 * Send input field hover update to remote peer
 */
export const sendInputHover = (activeDataChannel: RTCDataChannel, fieldName: string | null) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'input-hover', fieldName }));
  }
};

/**
 * Send table row click to remote peer
 */
export const sendTableRowClick = (activeDataChannel: RTCDataChannel, titleRow: string) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'table-row', titleRow }));
  }
};

// Handler to send maximized photo index over data channel
export const handleMaximizePhotoChange = (activeDataChannel: RTCDataChannel, index: number | null) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'photo-maximized', index }));
  }
};

// --- Presentation video control sync ---
/**
 * Send play/pause events for presentation video to peer
 */
export const sendPresentationVideoControl = (
  activeDataChannel: RTCDataChannel,
  event: { action: 'play' | 'pause'; timestamp: number },
) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'presentation-video-control', ...event }));
  }
};

// Send selected image over data channel
export const handlePackageSelectImage = (activeDataChannel: RTCDataChannel, src: string, index: number) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    console.log('Sending selected-image:', src, index);
    activeDataChannel.send(JSON.stringify({ type: 'selected-image', src, index }));
  }
};

// Function to send showPromotions update to peer
export const sendShowPromotionsUpdate = (activeDataChannel: RTCDataChannel, value: boolean) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'show-promotions', value }));
  }
};

export const sendShowFinancingUpdate = (activeDataChannel: RTCDataChannel, value: boolean) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'show-financing', value }));
  }
};

// Handlers to sync timeframe changes to peer
export const handleEnergyBillsTimeFrameChange = (activeDataChannel: RTCDataChannel, newValue: TimeFrame) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'timeframe-update', target: 'energy-bills', value: newValue }));
  }
};

export const handleSavingsTimeFrameChange = (activeDataChannel: RTCDataChannel, newValue: SavingsTimeFrame) => {
  if (activeDataChannel && activeDataChannel.readyState === 'open') {
    activeDataChannel.send(JSON.stringify({ type: 'timeframe-update', target: 'savings', value: newValue }));
  }
};
