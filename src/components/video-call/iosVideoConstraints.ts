/**
 * iOS-friendly video constraints to prevent rotation and aspect ratio issues
 * on iPad and iPhone devices.
 */

/**
 * Detects if the current device is iOS (iPad, iPhone, iPod) using multiple methods
 */
export const isIOS = (): boolean => {
  // Check if we're in a browser environment
  if (typeof navigator === 'undefined') return false;

  // Method 1: User agent check (still works for most cases)
  const userAgent = navigator.userAgent;
  const isIOSUserAgent = /iPad|iPhone|iPod/.test(userAgent);

  // Method 2: Check for iOS-specific features
  const hasIOSFeatures =
    'webkitAudioContext' in window || 'webkitRequestAnimationFrame' in window || 'webkitIndexedDB' in window;

  // Method 3: Check for touch capabilities + iOS-like behavior
  const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const isStandalone = (window.navigator as any).standalone === true; // iOS-specific

  // Method 4: Check for iOS-specific CSS properties
  const hasIOSCSS = CSS.supports('-webkit-touch-callout', 'none');

  // Combine checks for more reliable detection
  return isIOSUserAgent || (hasIOSFeatures && hasTouch && hasIOSCSS) || isStandalone;
};

/**
 * Creates iOS-friendly video constraints that prevent rotation issues
 * @param deviceId - Optional specific video device ID
 * @returns MediaTrackConstraints optimized for iOS devices
 */
export const createIOSVideoConstraints = (deviceId?: string): MediaTrackConstraints => {
  const ios = isIOS();

  if (ios) {
    return {
      deviceId: deviceId ? { exact: deviceId } : undefined,
      // iOS-friendly constraints - don't force aspect ratio or specific dimensions
      width: { ideal: 1280, max: 1920 },
      height: { ideal: 720, max: 1080 },
      // For iOS, use facingMode to ensure proper orientation
      facingMode: { ideal: 'user' },
      // Remove aspectRatio constraint for iOS as it can cause issues
      // iOS will use the device's native aspect ratio
    };
  }

  // Non-iOS devices can use standard constraints
  return {
    deviceId: deviceId ? { exact: deviceId } : undefined,
    aspectRatio: { ideal: 16 / 9 },
    width: { ideal: 1280 },
    height: { ideal: 720 },
  };
};

/**
 * Creates iOS-friendly MediaStreamConstraints
 * @param videoDeviceId - Optional specific video device ID
 * @param audioDeviceId - Optional specific audio device ID
 * @returns MediaStreamConstraints optimized for iOS devices
 */
export const createIOSMediaStreamConstraints = (
  videoDeviceId?: string,
  audioDeviceId?: string,
): MediaStreamConstraints => {
  return {
    video: createIOSVideoConstraints(videoDeviceId),
    audio: audioDeviceId ? { deviceId: { exact: audioDeviceId } } : true,
  };
};

/**
 * Adds iOS-specific video element attributes to prevent rotation issues
 * @param videoElement - The video element to configure
 */
export const configureIOSVideoElement = (videoElement: HTMLVideoElement): void => {
  if (!isIOS()) return;

  // Set iOS-specific attributes
  videoElement.setAttribute('webkit-playsinline', 'true');
  videoElement.setAttribute('x-webkit-airplay', 'allow');

  // Ensure playsInline is set
  videoElement.playsInline = true;

  // Force video to maintain orientation
  videoElement.style.transform = videoElement.style.transform || '';
};
