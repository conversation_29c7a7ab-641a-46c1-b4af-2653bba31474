import { VideoCall } from './VideoCall';
import WaitingRoom from './WaitingRoom';
import { useRemoteParticipants, useRoomContext } from '@livekit/components-react';
import { FullSolution } from './sales-visit-tool/src/utils/getFullSolution';
import { BackgroundEffect } from './BackgroundEffectsControl';

import { LocalAudioTrack, LocalVideoTrack } from 'livekit-client';
import { useEffect } from 'react';

export default function ConnectedContainer({
  solutionId,
  energySolution,
  selectedVideoDevice,
  selectedAudioDevice,
  setBackgroundEffect,
  backgroundEffect,
  setIsHangup,
  setSelectedVideoDevice,
  setSelectedAudioDevice,
  videoDevices,
  audioDevices,
  localVideoTrack,
  videoEnabled,
  audioEnabled,
  setVideoEnabled,
  setAudioEnabled,
}: {
  solutionId: string;
  energySolution: FullSolution;
  selectedVideoDevice: string;
  selectedAudioDevice: string;
  setBackgroundEffect: (backgroundEffect: BackgroundEffect) => void;
  backgroundEffect: BackgroundEffect;
  setIsHangup: (isHangup: boolean) => void;
  setSelectedVideoDevice: (selectedVideoDevice: string) => void;
  setSelectedAudioDevice: (selectedAudioDevice: string) => void;
  videoDevices: MediaDeviceInfo[];
  audioDevices: MediaDeviceInfo[];
  localAudioTrack: LocalAudioTrack;
  localVideoTrack: LocalVideoTrack;
  videoEnabled: boolean;
  audioEnabled: boolean;
  setVideoEnabled: (videoEnabled: boolean) => void;
  setAudioEnabled: (audioEnabled: boolean) => void;
}) {
  const room = useRoomContext();
  const remoteParticipants = useRemoteParticipants();

  useEffect(() => {
    console.log({ room });
  }, [room]);
  return remoteParticipants.length > 0 ? (
    <VideoCall
      roomId={solutionId}
      selectedVideoDevice={selectedVideoDevice}
      selectedAudioDevice={selectedAudioDevice}
      backgroundEffect={backgroundEffect}
      setBackgroundEffect={setBackgroundEffect}
      setIsHangup={setIsHangup}
      setSelectedVideoDevice={setSelectedVideoDevice}
      setSelectedAudioDevice={setSelectedAudioDevice}
      videoDevices={videoDevices}
      audioDevices={audioDevices}
      videoEnabled={videoEnabled}
      audioEnabled={audioEnabled}
      setVideoEnabled={setVideoEnabled}
      setAudioEnabled={setAudioEnabled}
    />
  ) : (
    <WaitingRoom
      localVideoTrack={localVideoTrack}
      customerName={energySolution.customer.name}
      backgroundEffect={backgroundEffect}
      setBackgroundEffect={setBackgroundEffect}
      selectedVideoDevice={selectedVideoDevice}
      setSelectedVideoDevice={setSelectedVideoDevice}
      selectedAudioDevice={selectedAudioDevice}
      setSelectedAudioDevice={setSelectedAudioDevice}
      handleHangup={() => setIsHangup(true)}
      videoDevices={videoDevices}
      audioDevices={audioDevices}
    />
  );
}
