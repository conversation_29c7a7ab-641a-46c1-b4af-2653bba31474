import { useTracks, VideoTrack } from '@livekit/components-react';
import { grey } from '@mui/material/colors';
import { Box, Typography } from '@mui/material';
import { Stack } from '@mui/material';
import { Track } from 'livekit-client';

export default function CustomersVideo() {
  const tracks = useTracks([Track.Source.Camera]);

  return (
    <Stack
      sx={{ height: '100%', minHeight: '100%', flex: '1 1 0%', flexDirection: 'row', backgroundColor: 'grey.900' }}
    >
      {tracks.map((track) => {
        if (track.participant.isLocal) return null;
        return (
          <Box key={track.participant.identity} sx={{ height: '100%', width: '100%', flex: '1 1 0%' }}>
            {track.publication.isEnabled ? (
              <VideoTrack trackRef={track} style={{ height: '100%', width: '100%', objectFit: 'contain' }} />
            ) : (
              <Box
                sx={{
                  height: '100%',
                  width: '100%',
                  backgroundColor: '#f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  Connected but no video stream
                </Typography>
              </Box>
            )}
            <Stack
              sx={{
                backgroundColor: '#22222680',
                p: '4px 12px',
                position: 'relative',
                bottom: 42,
                left: 12,
                width: 'fit-content',
                borderRadius: 1,
                color: grey[100],
              }}
            >
              <Typography variant="body1Emphasis" color={grey[100]}>
                {track.participant.name || 'Customer'}
              </Typography>
            </Stack>
          </Box>
        );
      })}
    </Stack>
  );
}
