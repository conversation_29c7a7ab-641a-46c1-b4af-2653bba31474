import { TrackReference, VideoTrack } from '@livekit/components-react';
import { useRef, useEffect, useState, useMemo } from 'react';

interface RemoteVideoPiPProps {
  remoteTracks: TrackReference[];
}

/**
 * Picture-in-Picture component for remote video stream.
 * Allows users to pop out the remote participant's video into a floating PiP window
 * while screen sharing or presenting.
 */
export const RemoteVideoPiP = ({ remoteTracks }: RemoteVideoPiPProps) => {
  const videoRef = useRef<HTMLDivElement | HTMLVideoElement>(null);
  const [isPiPSupported, setIsPiPSupported] = useState(false);
  const [isDocumentInPictureSupported, setIsDocumentInPictureSupported] = useState(false);

  const nrOfRemoteTracks = useMemo(() => remoteTracks.length, [remoteTracks]);

  // Check if Picture-in-Picture is supported
  useEffect(() => {
    const supported = 'pictureInPictureEnabled' in document && 'requestPictureInPicture' in HTMLDivElement.prototype;
    const documentInPictureSupported = 'documentPictureInPicture' in window;
    console.log('Picture-in-Picture supported:', supported);
    console.log('Document Picture-in-Picture supported:', documentInPictureSupported);
    setIsDocumentInPictureSupported(documentInPictureSupported);
    setIsPiPSupported(supported);
  }, []);

  // Automatically enter PiP when stream is ready
  useEffect(() => {
    const enterPiP = async () => {
      if (!videoRef.current || (!isPiPSupported && !isDocumentInPictureSupported) || nrOfRemoteTracks === 0) {
        return;
      }

      try {
        if (isDocumentInPictureSupported && window.documentPictureInPicture.window) {
          const videoRefCurrent = videoRef.current;
          await window.documentPictureInPicture.window.close();
          const pipWindow = await window.documentPictureInPicture.requestWindow({
            width: 400,
            height: 230 * nrOfRemoteTracks,
          });
          pipWindow.document.body.append(videoRefCurrent);
          pipWindow.document.body.style.backgroundColor = '#000000';
          pipWindow.document.body.style.overflow = 'hidden';
          pipWindow.document.body.style.padding = '4px';
          return;
        }
        if (isPiPSupported && document.pictureInPictureElement === videoRef.current) {
          return;
        }

        // Ensure video is playing before entering PiP
        const videoRefCurrent = videoRef.current;

        if (isDocumentInPictureSupported) {
          const pipWindow = await window.documentPictureInPicture.requestWindow({
            width: 400,
            height: 230 * nrOfRemoteTracks,
          });
          pipWindow.document.body.append(videoRefCurrent);
          pipWindow.document.body.style.backgroundColor = '#000000';
          pipWindow.document.body.style.overflow = 'hidden';
          pipWindow.document.body.style.padding = '4px';
        } else if (isPiPSupported) {
          await (videoRefCurrent as HTMLVideoElement).requestPictureInPicture();
        }

        // Enter PiP mode
      } catch (error) {
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            console.warn('PiP request was aborted');
          } else if (error.name === 'NotAllowedError') {
            console.warn('PiP not allowed by browser policy');
          } else if (error.name === 'InvalidStateError') {
            console.warn('PiP not available in current state');
          } else {
            console.error('Failed to enter Picture-in-Picture:', error);
          }
        } else {
          console.error('Failed to enter Picture-in-Picture:', error);
        }
      }
    };

    // Small delay to ensure everything is ready
    const timeoutId = setTimeout(enterPiP, 100);
    return () => clearTimeout(timeoutId);
  }, [isPiPSupported, isDocumentInPictureSupported, nrOfRemoteTracks]);

  // Cleanup on unmount
  useEffect(() => {
    const cleanup = async () => {
      try {
        if (isDocumentInPictureSupported) {
          await window.documentPictureInPicture.window.close();
        } else if (isPiPSupported) {
          await document.exitPictureInPicture();
        }
      } catch {
        // Ignore errors during cleanup
      }
    };
    return () => {
      void cleanup();
    };
  }, [isPiPSupported, isDocumentInPictureSupported]);

  if (!remoteTracks) {
    console.log('No remote tracks');
    return null;
  }

  if (isDocumentInPictureSupported) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          height: nrOfRemoteTracks * 219,
          width: '378px',
          flex: '1 1 0%',
          backgroundColor: '#000000',
        }}
        ref={videoRef as React.RefObject<HTMLDivElement>}
      >
        {remoteTracks.map((track) => (
          <div key={track.participant.identity} style={{ height: '100%', width: '100%', flex: '1 1 0%' }}>
            <VideoTrack
              trackRef={track}
              autoPlay
              playsInline
              muted={false} // Keep audio for PiP
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
                borderRadius: '8px',
              }}
            />
          </div>
        ))}
      </div>
    );
  }
  if (isPiPSupported) {
    const firstTrack = remoteTracks[0];
    return (
      <VideoTrack
        ref={videoRef as React.RefObject<HTMLVideoElement>}
        trackRef={firstTrack}
        autoPlay
        playsInline
        muted={false} // Keep audio for PiP
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          display: 'none', // Hidden since this is just for PiP
        }}
      />
    );
  }
  // Don't render if PiP is not supported or no remote stream
  if (!isPiPSupported && !isDocumentInPictureSupported) {
    console.log('PiP not supported or no remote track');
    return null;
  }
};
