import { Box, Paper, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { RemoteVideoPiP } from './RemoteVideoPiP';
import { useTracks, VideoTrack } from '@livekit/components-react';
import { Track } from 'livekit-client';

interface ScreenShareLayoutProps {
  isChatOpen: boolean;
  children: React.ReactNode;
}

export const ScreenShareLayout = ({ isChatOpen, children }: ScreenShareLayoutProps) => {
  const tracks = useTracks([Track.Source.ScreenShare, Track.Source.Camera]);
  const remoteCameraTracks = tracks.filter(
    (track) => !track.participant.isLocal && track.publication.source === Track.Source.Camera,
  );
  const localCameraTrack = tracks.find(
    (track) => track.participant.isLocal && track.publication.source === Track.Source.Camera,
  );
  const screenShareTrack = tracks.find((track) => track.publication.source === Track.Source.ScreenShare);

  return (
    // Screenshare layout: screenshare on left (75%), videos on right (25%)
    <>
      {/* Screenshare area */}
      <Paper
        elevation={3}
        sx={{
          width: isChatOpen ? '65%' : '75%',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: 2,
          bgcolor: 'grey.900',
          transition: 'width 0.3s ease-in-out',
        }}
      >
        <Box sx={{ width: '100%', height: '100%' }}>
          {screenShareTrack && (
            <VideoTrack trackRef={screenShareTrack} style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
          )}
        </Box>
        <Box
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: '#22222680',
            color: grey[900],
            p: '8px 16px',
            borderRadius: 1,
          }}
        >
          <Typography variant="body1Emphasis" color={grey[100]}>
            {screenShareTrack?.participant.isLocal ? 'Your Screen Share' : 'Remote Screen Share'}
          </Typography>
        </Box>

        {/* Picture-in-Picture component for remote video */}
        {screenShareTrack?.participant.isLocal && (
          <Box
            sx={{
              position: 'absolute',
              top: 16,
              right: 16,
              zIndex: 10,
            }}
          >
            <RemoteVideoPiP remoteTracks={remoteCameraTracks} />
          </Box>
        )}
      </Paper>

      {/* Video column on the right */}
      <Box
        sx={{
          width: '25%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          height: '100%',
          gap: 2,
        }}
      >
        {/* Remote video */}
        {remoteCameraTracks.map((track) => {
          return (
            <Paper
              key={track.participant.identity}
              elevation={3}
              sx={{
                flex: 1,
                minHeight: 200, // Ensure minimum visible height
                position: 'relative',
                overflow: 'hidden',
                borderRadius: 2,
                bgcolor: 'grey.900',
              }}
            >
              {track.publication.isEnabled && (
                <VideoTrack trackRef={track} style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
              )}
              <Box
                sx={{
                  backgroundColor: '#22222680',
                  p: '8px 16px',
                  position: 'relative',
                  bottom: 86,
                  left: 12,
                  width: 'fit-content',
                  borderRadius: 1,
                  color: grey[100],
                }}
              >
                <Typography variant="body1Emphasis" color={grey[100]}>
                  {track.participant.name}
                </Typography>
              </Box>
            </Paper>
          );
        })}

        {/* Local video */}
        <Paper
          elevation={3}
          sx={{
            flex: 1,
            minHeight: 200, // Ensure minimum visible height
            position: 'relative',
            overflow: 'hidden',
            borderRadius: '16px',
            bgcolor: 'grey.900',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              position: 'relative',
              width: '100%',
              flexDirection: 'column',
              justifyContent: 'center',
              flex: 1,
            }}
          >
            {localCameraTrack && (
              <VideoTrack
                trackRef={localCameraTrack}
                style={{ width: '100%', height: '100%', objectFit: 'contain', transform: 'scaleX(-1)' }}
              />
            )}
            <Box
              sx={{
                backgroundColor: '#22222680',
                p: '8px 16px',
                position: 'absolute',
                bottom: 6,
                left: 6,
                width: 'fit-content',
                borderRadius: 1,
                color: grey[100],
              }}
            >
              <Typography variant="body1Emphasis" color={grey[100]}>
                You
              </Typography>
            </Box>
          </Box>
          <Box>{children}</Box>
        </Paper>
      </Box>
    </>
  );
};
