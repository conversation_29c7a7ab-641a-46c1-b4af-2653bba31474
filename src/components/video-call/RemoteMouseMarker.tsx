import { MarkerOutlinedIcon } from '@ui/components/StandardIcons/MarkerOutlinedIcon';
import { useScreenStore } from './sales-visit-tool/src/store/useScreenStore';

interface RemoteMouseMarkerProps {
  remoteMousePosition: { x: number; y: number };
  containerSize: { width: number; height: number };
  containerScroll: { left: number; top: number };
}

// Clamp utility
function clamp(val: number, min: number, max: number) {
  return Math.max(min, Math.min(max, val));
}

export function RemoteMouseMarker({ remoteMousePosition, containerSize, containerScroll }: RemoteMouseMarkerProps) {
  const { currentScreen } = useScreenStore();
  if (currentScreen === 'survey') return null;
  if (!remoteMousePosition) return null;
  // Place marker at the same fraction of the visible area
  const px = containerScroll.left + remoteMousePosition.x * containerSize.width;
  const py = containerScroll.top + remoteMousePosition.y * containerSize.height;
  // Clamp to bounds so marker never leaves the container
  const markerRadius = 12;
  const clampedX = clamp(px, markerRadius, containerSize.width - markerRadius);
  const clampedY = clamp(py, markerRadius, containerSize.height - markerRadius);
  return (
    <div
      style={{
        position: 'absolute',
        left: clampedX,
        top: clampedY,
        width: 24,
        height: 24,
        background: 'rgba(0,0,0,0.7)',
        borderRadius: '50%',
        pointerEvents: 'none',
        transform: 'translate(-50%, -50%)',
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#fff',
        fontSize: 12,
        fontWeight: 600,
        border: '2px solid red',
        boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
      }}
    >
      <MarkerOutlinedIcon />
    </div>
  );
}
