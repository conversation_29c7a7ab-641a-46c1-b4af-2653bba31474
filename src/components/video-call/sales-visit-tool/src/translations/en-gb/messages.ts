// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = {
  'common.20-years': '20 years',
  'common.aira': 'Aira',
  'common.aira-zero-tariff': 'Aira Zero tariff',
  'common.boiler': 'Boiler',
  'common.cancel': 'Cancel',
  'common.close': 'Close',
  'common.co2-emissions': 'CO₂ emissions',
  'common.co2-savings-table.disclaimer':
    'The comparison for saved CO₂ emissions is based on a flight between London and New York City emitting approximately 993 kg of CO₂.\n\n Source: C Level, Flight Carbon Calculator.',
  'common.consumption-yearly': 'Consumption (yearly)',
  'common.cost-breakdown.assessment-system-design': 'Home assessment and system design',
  'common.cost-breakdown.battery-package': 'Battery package',
  'common.cost-breakdown.financing.representative.example': 'Financing · Representative example:',
  'common.cost-breakdown.gas-boiler-management': 'Disposal and recycling of old gas boiler',
  'common.cost-breakdown.heat-pump-system': 'Heat pump system',
  'common.cost-breakdown.installation-add-ons': 'Installation add-ons',
  'common.cost-breakdown.labour': 'Installation and labour',
  'common.cost-breakdown.monthly-repayments-of': ' monthly repayments of',
  'common.cost-breakdown.radiator-cost': 'Radiators',
  'common.cost-breakdown.radiator-system': 'Radiator system upgrades & labour',
  'common.cost-breakdown.representative-APR': 'Representative APR\n',
  'common.cost-breakdown.solar-and-battery-system': 'Solar and battery system',
  'common.cost-breakdown.solar-system': 'Solar panel system',
  'common.cost-breakdown.subsidy': 'Boiler Upgrade Scheme grant',
  'common.cost-breakdown.subtitle': 'Summary',
  'common.cost-breakdown.technical-design': 'Technical design',
  'common.cost-breakdown.technical-survey': 'Technical survey',
  'common.cost-breakdown.title': 'Cost breakdown',
  'common.cost-breakdown.total': 'System total',
  'common.cost-breakdown.warranty': '15 year installation & product warranty',
  'common.current': 'Current',
  'common.day': 'day',
  'common.description': 'description',
  'common.duration': 'Duration',
  'common.electricity': 'electricity',
  'common.email': 'Email',
  'common.ends-at': 'Ends at:',
  'common.energy-prices-table.disclaimer':
    'The comparison is an estimate of all energy related costs over the expected lifetime of a heat pump. Gas and electricity bills are calculated using Ofgem’s energy price caps, multiplied by 20 years, implicitly assuming constant gas and electricity costs for 20 years. Actual bills will vary based on factors that are not known today, e.g. future energy consumption, future energy price caps and future tariff rates.',
  'common.energy-prices-table.electricity-price': 'Electricity price',
  'common.energy-prices-table.electricity-price-sold': 'Electricity price sold',
  'common.energy-prices-table.gas-boiler-standing-charge': 'Gas standing charge',
  'common.energy-prices-table.gas-price': 'Gas price',
  'common.energy-prices-table.header': 'Energy prices',
  'common.energy-prices-table.oil-price': 'Oil price',
  'common.energy-prices-table.price-estimate': 'Your estimate is based on the average of current energy prices.',
  'common.energy-savings-table.co2-emissions': 'CO₂ emissions',
  'common.energy-savings-table.co2-savings': 'CO₂ savings',
  'common.energy-savings-table.ev-charger': 'EV charger',
  'common.energy-savings-table.header': 'Energy savings',
  'common.energy-savings-table.heating-bills': 'Heating bills',
  'common.energy-savings-table.heating-cover': 'Complete boiler + heating system cover',
  'common.energy-savings-table.heating-solution': 'Heating system (heat pump + radiators)',
  'common.energy-savings-table.insulation': 'Insulation',
  'common.energy-savings-table.net-energy-bills': 'Net energy bills',
  'common.energy-savings-table.product-cost': 'Product cost',
  'common.energy-savings-table.savings': 'Total',
  'common.energy-savings-table.solar-and-battery': 'Solar + Battery',
  'common.energy.electricity-bills': 'Other \nelectricity bills',
  'common.energy.energy-bills': 'Energy bills',
  'common.energy.gas-bills': 'Gas bills',
  'common.error.offline.header': 'You are offline',
  'common.error.offline.text': 'Make sure you have connected the iPad to the internet and have a good connection',
  'common.error.timeout.header': 'Your connection timed out',
  'common.error.timeout.text': 'Please try again, or reconnect this device to Wi-Fi or data.',
  'common.ev-milage': 'miles',
  'common.financing': 'Financing',
  'common.first-year': 'First year',
  'common.five': 'Five',
  'common.four': 'Four',
  'common.free': 'Free',
  'common.gas': 'Gas',
  'common.generated-electricity': 'Generated electricity',
  'common.house-type.apartment': 'Apartment',
  'common.house-type.bungalow': 'Bungalow',
  'common.house-type.detached': 'Detached',
  'common.house-type.semi-detached': 'Semi-detached',
  'common.house-type.terraced': 'Terraced',
  'common.installation-address': 'Installation Address',
  'common.kwh': 'kWh',
  'common.loading': 'Loading...',
  'common.minutes': 'minutes',
  'common.monthly': 'Monthly',
  'common.months': 'Months',
  'common.name': 'Name',
  'common.no': 'No',
  'common.one': 'One',
  'common.other-electricity-consumption': 'Other electricity',
  'common.phone': 'Phone number',
  'common.price': 'Price',
  'common.price-locked-message':
    'To make changes, unlock the price. Any changes may affect the final price once it is locked and a new quote is created. ',
  'common.price-locked-success': 'The price is successfully locked ',
  'common.price-locked-title': 'Price locked',
  'common.price-unlocked-success': 'Price unlocked!',
  'common.price-unlocked-title': 'Price unlocked',
  'common.prices': 'prices',
  'common.quantity': 'Quantity',
  'common.reference': 'Reference',
  'common.required.fields': 'Missing required inputs',
  'common.save': 'Save',
  'common.save-and-continue': 'Save & continue',
  'common.saving': 'Saving...',
  'common.savings.battery': 'Battery',
  'common.savings.include-other-electricity': 'Include other electricity',
  'common.savings.include-gas-standing-charge': 'include gas standing charge',
  'common.savings.graph-label.heat-pump-installation': 'Heat pump &\ninstallation',
  'common.savings.graph-label.heat-pump-installation-maintenance':
    'Heat pump,\ninstallation,\nall-inclusive plan\n& maintenance',
  'common.savings.graph-label.solar-installation': 'System &\ninstallation',
  'common.savings.graph-label.solar-installation-maintenance':
    'System,\ninstallation,\nall-inclusive plan\n& maintenance',
  'common.savings.solar-panels': 'Solar panels',
  'common.sign-out': 'Sign out',
  'common.solution-save-error': 'Error saving solution',
  'common.solution-saved': 'Saved!',
  'common.solution-savings': 'Savings',
  'common.starts-at': 'Starts at:',
  'common.three': 'Three',
  'common.today': 'Today',
  'common.tonnes': 'tonnes',
  'common.total': 'total',
  'common.two': 'Two',
  'common.unlock': 'Unlock',
  'common.upcoming': 'Upcoming',
  'common.yearly': 'Yearly',
  'common.yes': 'Yes',
  'configuration.expiry-date-success': 'Expiry date set',
  'configuration.save-success': 'Configuration saved successfully',
  'configuration.send-quote-error': 'Error sending quote',
  'configuration.send-quote-loading': 'Sending quote...',
  'configuration.send-quote-success': 'Quote sent!',
  'consumption.generated-electricity.description': 'Estimated generated electricity from solar panels: ',
  'current-setup.battery-system': 'Battery system',
  'current-setup.battery-system-installed': 'Battery system installed',
  'current-setup.battery-system-size': 'Size of existing battery',
  'current-setup.boiler-type': 'Type of boiler',
  'current-setup.boiler-type.gas-boiler': 'Gas boiler',
  'current-setup.boiler-type.oil-boiler': 'Oil boiler',
  'current-setup.cavity-wall-insulation-installed': 'Cavity wall insulation installed',
  'current-setup.current-heating': 'Heating',
  'current-setup.current-loft-insulation-level': 'Current loft insulation level',
  'current-setup.current-system-age': 'Age of current system',
  'current-setup.electric-vehicle': 'Electric vehicle (EV)',
  'current-setup.electric-vehicle-consumption': 'Annual mileage for EV (if applicable)?',
  'current-setup.electric-vehicle-mileage': 'Annual mileage for EV (if applicable)?',
  'current-setup.electricity': 'Electricity usage ',
  'current-setup.electricity-consumption': 'Electricity consumption',
  'current-setup.evaluation-scenario': 'Evaluation scenario',
  'current-setup.evaluation-scenario.new-boiler': 'New boiler vs. New HP',
  'current-setup.evaluation-scenario.old-boiler': 'Existing boiler vs. New HP',
  'current-setup.gas-consumption': 'Consumption',
  'current-setup.has-electric-vehicle': 'Do you have an electric vehicle?',
  'current-setup.home-construction-year': 'Home construction year',
  'current-setup.home-type': 'Home type',
  'current-setup.house-size': 'Home size',
  'current-setup.house-size.placeholder': 'e.g. 100',
  'current-setup.house-type': 'Home type',
  'current-setup.housing-units': 'Housing units',
  'current-setup.housing-units.placeholder': 'Units',
  'current-setup.insulation-measures': 'Insulation',
  'current-setup.location': 'Location',
  'current-setup.modal.description':
    'You haven’t saved your progress. Save now to continue, or cancel to continue editing this page',
  'current-setup.modal.title': 'Save your progress?',
  'current-setup.other': 'Other',
  'current-setup.other-electricity-consumption': 'Other electricity consumption',
  'current-setup.other-include-appliances': 'Include applicances in calculations',
  'current-setup.required-fields.description': 'In order to continue, please input',
  'current-setup.save-error': 'Something went wrong when saving current setup',
  'current-setup.save-success': 'Current setup saved successfully',
  'current-setup.sedbuk-rating': 'SEDBUK rating',
  'current-setup.solar-system': 'Solar panel system',
  'current-setup.solar-system-installed': 'Solar panel system installed',
  'current-setup.solar-system-size': 'Size of existing solar panel system',
  'current-setup.title': 'Current setup',
  'customer-configuration.add-battery': 'Add battery?',
  'customer-configuration.add-ev-charger': 'Add EV charger?',
  'customer-configuration.add-loft-insulation': 'Add loft insulation?',
  'customer-configuration.add-solar-panels': 'Add solar panels?',
  'customer-configuration.add-wall-insulation': 'Add wall insulation?',
  'customer-configuration.aira-zero-tariff': 'Aira Zero electricity tariff',
  'customer-configuration.aira-zero-unlock-savings': 'Unlock more savings with Aira Zero',
  'customer-configuration.battery': 'Battery',
  'customer-configuration.brand': 'Brand',
  'customer-configuration.buffer-tank': 'Buffer tank',
  'customer-configuration.epc': 'EPC Renewal',
  'customer-configuration.ev-charger': 'EV charger',
  'customer-configuration.extras': 'Extras',
  'customer-configuration.extras.label': 'extras',
  'customer-configuration.extras.price': 'Price',
  'customer-configuration.heat-survey': 'Room-by-room heat loss survey',
  'customer-configuration.include-subsidy': 'Include subsidy?',
  'customer-configuration.indoor-unit': 'Indoor unit',
  'customer-configuration.indoor-units': 'Indoor units',
  'customer-configuration.installation-add-ons': 'Installation add-ons',
  'customer-configuration.installation-add-ons.Scaffolding': 'Scaffolding',
  'customer-configuration.installation-addons.electrical-bonding': 'Electrical bonding',
  'customer-configuration.installation-addons.isolator-fittings': 'Isolator fittings',
  'customer-configuration.installation-addons.lifting-equipment': 'Lifting equipment',
  'customer-configuration.installation-addons.radiator-system-flush': 'Radiator system flush',
  'customer-configuration.installation-addons.re-piping': 'Re-piping',
  'customer-configuration.installation-addons.stone-wall-drilling': 'Stone wall drilling',
  'customer-configuration.installation-addons.underfloor-heating-commissioning': 'Underfloor heating commissioning',
  'customer-configuration.installation-addons.underground-piping': 'Underground piping',
  'customer-configuration.insulation': 'Insulation',
  'customer-configuration.mcs-system-design': 'MCS certified system design',
  'customer-configuration.model': 'Model',
  'customer-configuration.outdoor-unit': 'Outdoor unit',
  'customer-configuration.outdoor-units': 'Outdoor units',
  'customer-configuration.payment-plan': 'Payment plan',
  'customer-configuration.payment-plan.monthly': '12 months',
  'customer-configuration.payment-plan.upfront': 'Upfront',
  'customer-configuration.planning-permission': 'Planning permission',
  'customer-configuration.preferred-financing-option': 'Preferred financing option',
  'customer-configuration.quantity': 'Quantity',
  'customer-configuration.radiators-to-change': 'Radiators',
  'customer-configuration.radiators.design': 'Design',
  'customer-configuration.radiators.large': 'Large',
  'customer-configuration.radiators.medium': 'Medium',
  'customer-configuration.radiators.small': 'Small',
  'customer-configuration.select-extra.description': 'Description',
  'customer-configuration.solar-panels-and-battery': 'Solar panels',
  'customer-configuration.subsidy': '£7,500 grant',
  'customer-configuration.subsidy.title': 'Government grant',
  'customer-configuration.technical-survey-and-system-design': 'Technical survey & system design',
  'customer-configuration.title': 'System configuration',
  'customer-configuration.type': 'Type',
  'customer-configuration.update-epc-rating': 'Update EPC rating',
  'energy-bills.title': 'Energy bills',
  'error.number.positiveOrZero': 'Number must be 0 or positive',
  'house-tour.indoor-unit': 'Indoor unit',
  'house-tour.indoor-units': 'Indoor units',
  'house-tour.outdoor-unit': 'Outdoor unit',
  'house-tour.outdoor-units': 'Outdoor units',
  'house-tour.title': 'Home tour / AR',
  'house-tour.vaillaint-units.description': 'Open Vaillant app',
  'house-tour.vaillaint-units.subtitle': 'Vaillant products',
  'house-tour.vaillaint-units.title': 'Vaillant units',
  'modal.payment-method.financing': 'Financing',
  'modal.payment-method.message': 'Select preferred payment method.',
  'modal.payment-method.pay-in-full': 'Pay in full',
  'modal.payment-method.title': 'Present quote',
  'overview.home': 'Home',
  'overview.home-energy-assessment': 'Home Energy Assessment',
  'package-cost.present-quote': 'Present quote',
  'package-cost.preview-quote': 'Preview quote',
  'package-cost.send-quote': 'Send quote',
  'package-cost.title': 'System summary',
  'package.quote.settings.title': 'Quote settings',
  'package.settings.financing-term': 'Financing term',
  'package.settings.promotions': 'Promotions',
  'product.aira-12kw': 'Aira 12kW',
  'product.aira-8kw': 'Aira 6-8kW',
  'product.aira-buffer-tank-100l': 'Aira buffer tank (100L)',
  'product.aira-buffer-tank-40l': 'Aira buffer tank (40L)',
  'product.aira-indoor-100l': 'Aira All in One (100L)',
  'product.aira-indoor-250l': 'Aira All in One (250L)',
  'start-page.no-upcoming-work': 'No upcoming visits!',
  'start-page.no-work-today': 'No scheduled visits today!',
} as const;
