import { messages as UKTranslations } from './en-gb/messages';
import { messages as DETranslations } from './de/messages';
import { messages as ITTranslations } from './it/messages';

export const t = (key: keyof typeof UKTranslations, locale = 'en-gb') => {
  if (locale === 'en-gb') {
    return UKTranslations[key];
  }
  if (locale === 'de') {
    return DETranslations[key];
  }

  if (locale === 'it') {
    return ITTranslations[key];
  }
  return 'Translation not found';
};
