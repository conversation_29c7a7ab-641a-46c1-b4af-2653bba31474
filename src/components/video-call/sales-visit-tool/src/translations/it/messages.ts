// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = {
  'common.20-years': '',
  'common.aira': '',
  'common.aira-zero-tariff': '',
  'common.boiler': '',
  'common.cancel': '',
  'common.close': '',
  'common.co2-emissions': '',
  'common.co2-savings-table.disclaimer': '',
  'common.consumption-yearly': '',
  'common.cost-breakdown.assessment-system-design': '',
  'common.cost-breakdown.battery-package': '',
  'common.cost-breakdown.financing.representative.example': '',
  'common.cost-breakdown.gas-boiler-management': '',
  'common.cost-breakdown.heat-pump-system': '',
  'common.cost-breakdown.installation-add-ons': '',
  'common.cost-breakdown.labour': '',
  'common.cost-breakdown.monthly-repayments-of': '',
  'common.cost-breakdown.radiator-cost': '',
  'common.cost-breakdown.radiator-system': '',
  'common.cost-breakdown.representative-APR': '',
  'common.cost-breakdown.solar-and-battery-system': '',
  'common.cost-breakdown.solar-system': '',
  'common.cost-breakdown.subsidy': '',
  'common.cost-breakdown.subtitle': '',
  'common.cost-breakdown.technical-design': '',
  'common.cost-breakdown.technical-survey': '',
  'common.cost-breakdown.title': '',
  'common.cost-breakdown.total': '',
  'common.cost-breakdown.warranty': '',
  'common.current': '',
  'common.day': '',
  'common.description': '',
  'common.duration': '',
  'common.electricity': '',
  'common.email': '',
  'common.ends-at': '',
  'common.energy-prices-table.disclaimer': '',
  'common.energy-prices-table.electricity-price': '',
  'common.energy-prices-table.electricity-price-sold': '',
  'common.energy-prices-table.gas-boiler-standing-charge': '',
  'common.energy-prices-table.gas-price': '',
  'common.energy-prices-table.header': '',
  'common.energy-prices-table.oil-price': '',
  'common.energy-prices-table.price-estimate': '',
  'common.energy-savings-table.co2-emissions': '',
  'common.energy-savings-table.co2-savings': '',
  'common.energy-savings-table.ev-charger': '',
  'common.energy-savings-table.header': '',
  'common.energy-savings-table.heating-bills': '',
  'common.energy-savings-table.heating-cover': '',
  'common.energy-savings-table.heating-solution': '',
  'common.energy-savings-table.insulation': '',
  'common.energy-savings-table.net-energy-bills': '',
  'common.energy-savings-table.product-cost': '',
  'common.energy-savings-table.savings': '',
  'common.energy-savings-table.solar-and-battery': '',
  'common.energy.electricity-bills': '',
  'common.energy.energy-bills': '',
  'common.energy.gas-bills': '',
  'common.error.offline.header': '',
  'common.error.offline.text': '',
  'common.error.timeout.header': '',
  'common.error.timeout.text': '',
  'common.ev-milage': '',
  'common.financing': '',
  'common.first-year': '',
  'common.five': '',
  'common.four': '',
  'common.free': '',
  'common.gas': '',
  'common.generated-electricity': '',
  'common.house-type.apartment': '',
  'common.house-type.bungalow': '',
  'common.house-type.detached': '',
  'common.house-type.semi-detached': '',
  'common.house-type.terraced': '',
  'common.installation-address': '',
  'common.kwh': '',
  'common.loading': '',
  'common.minutes': '',
  'common.monthly': '',
  'common.months': '',
  'common.name': '',
  'common.no': '',
  'common.one': '',
  'common.other-electricity-consumption': '',
  'common.phone': '',
  'common.price': '',
  'common.price-locked-message': '',
  'common.price-locked-success': '',
  'common.price-locked-title': '',
  'common.price-unlocked-success': '',
  'common.price-unlocked-title': '',
  'common.prices': '',
  'common.quantity': '',
  'common.reference': '',
  'common.required.fields': '',
  'common.save': '',
  'common.save-and-continue': '',
  'common.saving': '',
  'common.savings.battery': '',
  'common.savings.include-other-electricity': '',
  'common.savings.include-gas-standing-charge': '',
  'common.savings.graph-label.heat-pump-installation': '',
  'common.savings.graph-label.heat-pump-installation-maintenance': '',
  'common.savings.graph-label.solar-installation': '',
  'common.savings.graph-label.solar-installation-maintenance': '',
  'common.savings.solar-panels': '',
  'common.sign-out': '',
  'common.solution-save-error': '',
  'common.solution-saved': '',
  'common.solution-savings': '',
  'common.starts-at': '',
  'common.three': '',
  'common.today': '',
  'common.tonnes': '',
  'common.total': '',
  'common.two': '',
  'common.unlock': '',
  'common.upcoming': '',
  'common.yearly': '',
  'common.yes': '',
  'configuration.expiry-date-success': '',
  'configuration.save-success': '',
  'configuration.send-quote-error': '',
  'configuration.send-quote-loading': '',
  'configuration.send-quote-success': '',
  'consumption.generated-electricity.description': '',
  'current-setup.battery-system': '',
  'current-setup.battery-system-installed': '',
  'current-setup.battery-system-size': '',
  'current-setup.boiler-type': '',
  'current-setup.boiler-type.gas-boiler': '',
  'current-setup.boiler-type.oil-boiler': '',
  'current-setup.cavity-wall-insulation-installed': '',
  'current-setup.current-heating': '',
  'current-setup.current-loft-insulation-level': '',
  'current-setup.current-system-age': '',
  'current-setup.electric-vehicle': '',
  'current-setup.electric-vehicle-consumption': '',
  'current-setup.electric-vehicle-mileage': '',
  'current-setup.electricity': '',
  'current-setup.electricity-consumption': '',
  'current-setup.evaluation-scenario': '',
  'current-setup.evaluation-scenario.new-boiler': '',
  'current-setup.evaluation-scenario.old-boiler': '',
  'current-setup.gas-consumption': '',
  'current-setup.has-electric-vehicle': '',
  'current-setup.home-construction-year': '',
  'current-setup.home-type': '',
  'current-setup.house-size': '',
  'current-setup.house-size.placeholder': '',
  'current-setup.house-type': '',
  'current-setup.housing-units': '',
  'current-setup.housing-units.placeholder': '',
  'current-setup.insulation-measures': '',
  'current-setup.location': '',
  'current-setup.modal.description': '',
  'current-setup.modal.title': '',
  'current-setup.other': '',
  'current-setup.other-electricity-consumption': '',
  'current-setup.other-include-appliances': '',
  'current-setup.required-fields.description': '',
  'current-setup.save-error': '',
  'current-setup.save-success': '',
  'current-setup.sedbuk-rating': '',
  'current-setup.solar-system': '',
  'current-setup.solar-system-installed': '',
  'current-setup.solar-system-size': '',
  'current-setup.title': '',
  'customer-configuration.add-battery': '',
  'customer-configuration.add-ev-charger': '',
  'customer-configuration.add-loft-insulation': '',
  'customer-configuration.add-solar-panels': '',
  'customer-configuration.add-wall-insulation': '',
  'customer-configuration.aira-zero-tariff': '',
  'customer-configuration.aira-zero-unlock-savings': '',
  'customer-configuration.battery': '',
  'customer-configuration.brand': '',
  'customer-configuration.buffer-tank': '',
  'customer-configuration.epc': '',
  'customer-configuration.ev-charger': '',
  'customer-configuration.extras': '',
  'customer-configuration.extras.label': '',
  'customer-configuration.extras.price': '',
  'customer-configuration.heat-survey': '',
  'customer-configuration.include-subsidy': '',
  'customer-configuration.indoor-unit': '',
  'customer-configuration.indoor-units': '',
  'customer-configuration.installation-add-ons': '',
  'customer-configuration.installation-add-ons.Scaffolding': '',
  'customer-configuration.installation-addons.electrical-bonding': '',
  'customer-configuration.installation-addons.isolator-fittings': '',
  'customer-configuration.installation-addons.lifting-equipment': '',
  'customer-configuration.installation-addons.radiator-system-flush': '',
  'customer-configuration.installation-addons.re-piping': '',
  'customer-configuration.installation-addons.stone-wall-drilling': '',
  'customer-configuration.installation-addons.underfloor-heating-commissioning': '',
  'customer-configuration.installation-addons.underground-piping': '',
  'customer-configuration.insulation': '',
  'customer-configuration.mcs-system-design': '',
  'customer-configuration.model': '',
  'customer-configuration.outdoor-unit': '',
  'customer-configuration.outdoor-units': '',
  'customer-configuration.payment-plan': '',
  'customer-configuration.payment-plan.monthly': '',
  'customer-configuration.payment-plan.upfront': '',
  'customer-configuration.planning-permission': '',
  'customer-configuration.preferred-financing-option': '',
  'customer-configuration.quantity': '',
  'customer-configuration.radiators-to-change': '',
  'customer-configuration.radiators.design': '',
  'customer-configuration.radiators.large': '',
  'customer-configuration.radiators.medium': '',
  'customer-configuration.radiators.small': '',
  'customer-configuration.select-extra.description': '',
  'customer-configuration.solar-panels-and-battery': '',
  'customer-configuration.subsidy': '',
  'customer-configuration.subsidy.title': '',
  'customer-configuration.technical-survey-and-system-design': '',
  'customer-configuration.title': '',
  'customer-configuration.type': '',
  'customer-configuration.update-epc-rating': '',
  'energy-bills.title': '',
  'error.number.positiveOrZero': '',
  'house-tour.indoor-unit': '',
  'house-tour.indoor-units': '',
  'house-tour.outdoor-unit': '',
  'house-tour.outdoor-units': '',
  'house-tour.title': '',
  'house-tour.vaillaint-units.description': '',
  'house-tour.vaillaint-units.subtitle': '',
  'house-tour.vaillaint-units.title': '',
  'modal.payment-method.financing': '',
  'modal.payment-method.message': '',
  'modal.payment-method.pay-in-full': '',
  'modal.payment-method.title': '',
  'overview.home': '',
  'overview.home-energy-assessment': '',
  'package-cost.present-quote': '',
  'package-cost.preview-quote': '',
  'package-cost.send-quote': '',
  'package-cost.title': '',
  'package.quote.settings.title': '',
  'package.settings.financing-term': '',
  'package.settings.promotions': '',
  'product.aira-12kw': '',
  'product.aira-8kw': '',
  'product.aira-buffer-tank-100l': '',
  'product.aira-buffer-tank-40l': '',
  'product.aira-indoor-100l': '',
  'product.aira-indoor-250l': '',
  'start-page.no-upcoming-work': '',
  'start-page.no-work-today': '',
} as const;
