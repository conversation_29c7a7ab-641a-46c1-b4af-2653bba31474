import { Form } from '../../../common/Form';

import { useProductSuspenseQuery } from '../../../../hooks/products';
import { configurationTranslations } from '../translations/translations';
import { Controller, useFormContext } from 'react-hook-form';
import { useTranslation } from 'components/video-call/sales-visit-tool/src/hooks/translation';
import { ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { CarBoltOutlinedIcon } from '@ui/components/StandardIcons/CarBoltOutlinedIcon';
import { Box, Typography } from '@mui/material';

export const EvCharger = ({
  priceListId,
  onMouseOver,
}: {
  priceListId: string;
  onMouseOver: (name: string) => void;
}) => {
  const [evChargers] = useProductSuspenseQuery({ priceListId, select: (data) => data.products.system.evChargers });
  const { control } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);

  if (!evChargers || evChargers.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mb: 6, display: 'flex', flexDirection: 'column', gap: 5 }}>
      <CarBoltOutlinedIcon height={40} width={40} style={{ position: 'absolute', marginLeft: -78, marginTop: -8 }} />
      <Typography variant="headline2">{t.config.evCharger}</Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
        <Controller
          name="evCharger"
          control={control}
          render={({ field: { onChange, value, name } }) => (
            <Form.ButtonGroup
              label={evChargers.length === 1 ? evChargers[0]?.name : t.config.evCharger}
              onSelect={onChange}
              options={[
                ...evChargers.map((x) => ({
                  label: evChargers.length === 1 ? t.common.yes : x.name,
                  value: x.sku,
                  selected: value === x.sku,
                })),
                {
                  label: t.common.no,
                  value: null,
                  selected: !value,
                },
              ]}
              dataField={name}
              onMouseOver={onMouseOver}
            />
          )}
        />
      </Box>
    </Box>
  );
};
