import { Form } from '../../../common/Form';
import { Controller, useFormContext } from 'react-hook-form';
import { type ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { configurationTranslations } from '../translations/translations';
import { RulerOutlinedIcon } from '@ui/components/StandardIcons/RulerOutlinedIcon';
import { useProductSuspenseQuery } from '../../../../hooks/products';
import { useTranslation } from '../../../../hooks/translation';
import { Box, Typography } from '@mui/material';

export const InstallationAddons = ({
  priceListId,
  onMouseOver,
}: {
  priceListId: string;
  onMouseOver: (name: string) => void;
}) => {
  const { control } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);
  const [{ installationAddons }] = useProductSuspenseQuery({
    priceListId,
    select: (data) => ({
      installationAddons: data.products.installationAddons,
    }),
  });

  const hasInstallationAddons = Object.values(installationAddons).some((x) => x.products.length > 1);
  if (!hasInstallationAddons) {
    return null;
  }
  return (
    <Box sx={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: 5 }}>
      <RulerOutlinedIcon height={40} width={40} style={{ position: 'absolute', marginLeft: -78, marginTop: -8 }} />
      <Typography variant="headline2">{t.config.installationAddonsTitle}</Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
        {installationAddons.map((addon, index) => {
          console.log({ addon });
          if (addon.key === 'unknown') {
            return null;
          }
          return (
            <Controller
              key={addon.key}
              control={control}
              name={`installationAddons.${index}`}
              render={({ field: { onChange, value, name } }) => {
                const isYesNoOption = addon.products.length === 2 && addon.products.some((product) => product.ghost);

                return (
                  <Form.ButtonGroup
                    label={t.config.installationAddons[addon.key]}
                    onSelect={(e) => {
                      onChange(e);
                    }}
                    options={addon.products.map((product) => {
                      const label = product.ghost ? t.common.no : isYesNoOption ? t.common.yes : product.name;
                      return {
                        label: label,
                        value: product.sku,
                        selected: value === product.sku,
                      };
                    })}
                    dataField={name}
                    onMouseOver={onMouseOver}
                  />
                );
              }}
            />
          );
        })}
      </Box>
    </Box>
  );
};
