import { Controller, useFormContext } from 'react-hook-form';
import { Form } from '../../../common/Form';
import { Wave3VerticalOutlinedIcon } from '@ui/components/StandardIcons/Wave3VerticalOutlinedIcon';
import { useProductSuspenseQuery } from '../../../../hooks/products';
import { useTranslation } from '../../../../hooks/translation';
import { type ConfigurationFormData } from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { configurationTranslations } from '../translations/translations';
import { Box, Typography } from '@mui/material';

export const Radiators = ({
  priceListId,
  onMouseOver,
}: {
  priceListId: string;
  onMouseOver: (name: string) => void;
}) => {
  const [{ radiators }] = useProductSuspenseQuery({
    priceListId,
    select: (data) => ({
      radiators: data.products.system.radiators,
    }),
  });

  const { control } = useFormContext<ConfigurationFormData>();
  const t = useTranslation(configurationTranslations);

  if (!radiators.length) {
    return null;
  }

  return (
    <Box sx={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: 5 }}>
      <Wave3VerticalOutlinedIcon
        height={40}
        width={40}
        style={{ position: 'absolute', marginLeft: -78, marginTop: -8 }}
      />
      <Typography variant="headline2">{t.config.radiatorsToChange}</Typography>
      <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 4 }}>
        {radiators.map((radiator, index) => (
          <Controller
            control={control}
            key={radiator.sku}
            name={`radiators.${index}`}
            render={({ field: { onChange, value, name } }) => {
              return (
                <div onMouseOver={() => onMouseOver(`radiators.${index}`)}>
                  <Form.TextField
                    name={`radiators.${index}`}
                    placeholder="0"
                    label={radiator.name}
                    type="number"
                    min={0}
                    value={value?.quantity}
                    data-clarity-unmask="true"
                    dataField={name}
                    onChange={(e) =>
                      onChange({
                        sku: radiator.sku,
                        quantity: parseInt(e.target.value ?? 0, 10),
                      })
                    }
                  />
                </div>
              );
            }}
          />
        ))}
      </Box>
    </Box>
  );
};
