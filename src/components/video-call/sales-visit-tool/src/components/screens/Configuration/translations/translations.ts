// English translations
const enConfig = {
  title: 'System configuration',
  addSolarPanels: 'Add solar panels?',
  addBattery: 'Add battery?',
  addEvCharger: 'Add EV charger?',
  addLoftInsulation: 'Add loft insulation?',
  addWallInsulation: 'Add wall insulation?',
  radiatorsToChange: 'Radiators',
  bufferTank: 'Buffer tank',
  outdoorUnit: 'Outdoor unit',
  outdoorUnits: 'Outdoor units',
  indoorUnit: 'Indoor unit',
  indoorUnits: 'Indoor units',
  model: 'Model',
  type: 'Type',
  brand: 'Brand',
  brands: 'Brands',
  quantity: 'Quantity',
  saveSuccess: 'Configuration saved successfully',
  epc: 'EPC Renewal',
  updateEpcRating: 'Update EPC rating',
  technicalSurveyAndSystemDesign: 'Technical survey & system design',
  airaZeroTariff: 'Aira Zero electricity tariff',
  airaZeroUnlockSavings: 'Unlock more savings with Aira Zero',
  battery: 'Battery',
  evCharger: 'EV charger',
  extras: 'Extras',
  extrasLabel: 'extras',
  extrasPrice: 'Price',
  heatSurvey: 'Room-by-room heat loss survey',
  insulation: 'Insulation',
  mcsSystemDesign: 'MCS certified system design',
  planningPermission: 'Planning permission',
  preferredFinancingOption: 'Preferred financing option',
  solarPanelsAndBattery: 'Solar panels',
  selectExtraDescription: 'Description',
  solar: 'Solar',
  solarAndBattery: 'Solar + Battery',
  subsidy: {
    title: 'Government grant',
    inputTitle: '£7,500 grant',
    housingUnits: 'Housing units',
    housingUnit: 'housing unit',
    noSubsidy: 'No subsidy',
  },

  installationAddonsTitle: 'Installation add-ons',
  installationAddons: {
    scaffolding: 'Scaffolding',
    electricalBonding: 'Electrical bonding',
    isolatorFittings: 'Isolator fittings',
    liftingEquipment: 'Lifting equipment',
    systemFlush: 'Radiator system flush',
    rePiping: 'Re-piping',
    stonewallDrilling: 'Stone wall drilling',
    underfloorHeatingCommission: 'Underfloor heating commissioning',
    undergroundPiping: 'Underground piping',
    installationPackages: 'Installation packages',
    manifold: 'Manifold',
    meterBoxChange: 'Meter box change',
    digging: 'Digging',
    oilTankRemoval: 'Oil tank removal',
    radiatorControl: 'Radiator control',
    outdoorUnitWall: 'Outdoor unit wall mount',
    unknown: 'Unknown installation addon',
  },

  // Radiators converted to camelCase
  radiatorsDesign: 'Design',
  radiatorsLarge: 'Large',
  radiatorsMedium: 'Medium',
  radiatorsSmall: 'Small',

  // Payment plan converted to camelCase
  paymentPlanTitle: 'Payment plan',
  paymentPlanMonthly: '12 months',
  paymentPlanUpfront: 'Upfront',

  // Modal converted to camelCase
  modalTitle: 'Save your progress?',
  modalDescription: "You haven't saved your progress. Save now to continue, or cancel to continue editing this page",

  // Quote sending converted to camelCase
  sendQuoteLoading: 'Loading...',
  sendQuoteSuccess: 'Quote sent successfully',
  sendQuoteError: 'Error sending quote',
};

// German translations
const deConfig = {
  title: 'Systemkonfiguration',
  addSolarPanels: 'Solarpanels hinzufügen?',
  addBattery: 'Batterie hinzufügen?',
  addEvCharger: 'EV-Ladestation hinzufügen?',
  addLoftInsulation: 'Dachdämmung hinzufügen?',
  addWallInsulation: 'Wanddämmung hinzufügen?',
  radiatorsToChange: 'Heizkörper',
  bufferTank: 'Pufferspeicher',
  outdoorUnit: 'Außengerät',
  outdoorUnits: 'Außengeräte',
  indoorUnit: 'Innengerät',
  indoorUnits: 'Innengeräte',
  model: 'Modell',
  type: 'Typ',
  brand: 'Marke',
  brands: 'Marken',
  quantity: 'Menge',
  saveSuccess: 'Konfiguration erfolgreich gespeichert',
  epc: 'EPC-Erneuerung',
  updateEpcRating: 'EPC-Bewertung aktualisieren',
  technicalSurveyAndSystemDesign: 'Technische Begutachtung & Systemdesign',
  airaZeroTariff: 'Aira Zero Stromtarif',
  airaZeroUnlockSavings: 'Entsperren Sie mehr Einsparungen mit Aira Zero',
  battery: 'Batterie',
  evCharger: 'EV-Ladestation',
  extras: 'Extras',
  extrasLabel: 'extras',
  extrasPrice: 'Preis',
  heatSurvey: 'Raumweise Wärmeverlustanalyse',
  insulation: 'Dämmung',
  mcsSystemDesign: 'MCS-zertifiziertes Systemdesign',
  planningPermission: 'Baugenehmigung',
  preferredFinancingOption: 'Bevorzugte Finanzierungsoption',
  solarPanelsAndBattery: 'Solarpanels',
  selectExtraDescription: 'Beschreibung',
  solar: 'Solar',
  solarAndBattery: 'Solar + Batterie',
  subsidy: {
    title: 'Staatliche Förderung',
    inputTitle: 'Staatliche Förderung',
    housingUnits: 'Wohneinheiten',
    housingUnit: 'wohneinheit',
    noSubsidy: 'Kein Zuschuss',
  },

  // Installation add-ons converted to camelCase
  installationAddonsTitle: 'Installationszusätze',
  installationAddons: {
    scaffolding: 'Gerüstbau',
    electricalBonding: 'Elektrische Verbindung',
    isolatorFittings: 'Isolatorarmaturen',
    liftingEquipment: 'Hebeausrüstung',
    systemFlush: 'Heizkörpersystemspülung',
    rePiping: 'Neuverrohrung',
    stonewallDrilling: 'Steinwandbohrung',
    underfloorHeatingCommission: 'Inbetriebnahme der Fußbodenheizung',
    undergroundPiping: 'Unterirdische Verrohrung',
    installationPackages: 'Installationspakete',
    manifold: 'Erneuerung Fußbodenheizkreisverteiler',
    meterBoxChange: 'Zählerkastenwechsel',
    digging: 'Grabungen (Gala)',
    oilTankRemoval: 'Öltankentfernung',
    radiatorControl: 'Heizkörpersteuerung',
    outdoorUnitWall: 'Wandmontage für Außengeräte',
    unknown: 'Unbekannter Installationszusatz',
  },

  // Radiators converted to camelCase
  radiatorsDesign: 'Design',
  radiatorsLarge: 'Groß',
  radiatorsMedium: 'Mittel',
  radiatorsSmall: 'Klein',

  // Payment plan converted to camelCase
  paymentPlanTitle: 'Zahlungsplan',
  paymentPlanMonthly: '12 Monate',
  paymentPlanUpfront: 'Vorauszahlung',

  // Modal converted to camelCase
  modalTitle: 'Fortschritt speichern?',
  modalDescription:
    'Sie haben Ihren Fortschritt nicht gespeichert. Jetzt speichern, um fortzufahren, oder abbrechen, um diese Seite weiter zu bearbeiten',

  // Quote sending converted to camelCase
  sendQuoteLoading: 'Wird geladen...',
  sendQuoteSuccess: 'Angebot erfolgreich gesendet',
  sendQuoteError: 'Fehler beim Senden des Angebots',
};

// Italian translations
const itConfig = {
  title: 'Configurazione del sistema',
  addSolarPanels: 'Aggiungere pannelli solari?',
  addBattery: 'Aggiungere batteria?',
  addEvCharger: 'Aggiungere caricatore EV?',
  addLoftInsulation: 'Aggiungere isolamento del sottotetto?',
  addWallInsulation: 'Aggiungere isolamento delle pareti?',
  radiatorsToChange: 'Radiatori',
  bufferTank: 'Serbatoio di accumulo',
  outdoorUnit: 'Unità esterna',
  outdoorUnits: 'Unità esterne',
  indoorUnit: 'Unità interna',
  indoorUnits: 'Unità interne',
  model: 'Modello',
  type: 'Tipo',
  brand: 'Marca',
  brands: 'Marche',
  quantity: 'Quantità',
  saveSuccess: 'Configurazione salvata con successo',
  epc: 'Rinnovo EPC',
  updateEpcRating: 'Aggiorna classificazione EPC',
  technicalSurveyAndSystemDesign: 'Sopralluogo tecnico e progettazione del sistema',
  airaZeroTariff: 'Tariffa elettrica Aira Zero',
  airaZeroUnlockSavings: 'Sblocca più risparmi con Aira Zero',
  battery: 'Batteria',
  evCharger: 'Caricatore EV',
  extras: 'Extra',
  extrasLabel: 'extra',
  extrasPrice: 'Prezzo',
  heatSurvey: 'Analisi della perdita di calore stanza per stanza',
  insulation: 'Isolamento',
  mcsSystemDesign: 'Progettazione del sistema certificata MCS',
  planningPermission: 'Permesso di pianificazione',
  preferredFinancingOption: 'Opzione di finanziamento preferita',
  solarPanelsAndBattery: 'Pannelli solari',
  selectExtraDescription: 'Descrizione',
  solar: 'Solare',
  solarAndBattery: 'Solare + Batteria',
  subsidy: {
    title: 'Government grant',
    inputTitle: '£7,500 grant',
    housingUnits: 'Housing units',
    housingUnit: 'housing unit',
    noSubsidy: 'Nessun sussidio',
  },

  installationAddonsTitle: "Componenti aggiuntivi per l'installazione",
  installationAddons: {
    scaffolding: 'Impalcature',
    electricalBonding: 'Collegamento elettrico',
    isolatorFittings: 'Raccordi isolanti',
    liftingEquipment: 'Attrezzatura di sollevamento',
    systemFlush: 'Lavaggio sistema radiatori',
    rePiping: 'Ritubazione',
    stonewallDrilling: 'Perforazione di pareti in pietra',
    underfloorHeatingCommission: 'Messa in servizio del riscaldamento a pavimento',
    undergroundPiping: 'Tubazioni sotterranee',
    installationPackages: 'Pacchetti di installazione',
    manifold: 'Collettore',
    meterBoxChange: 'Cambio del contatore',
    digging: 'Scavo',
    oilTankRemoval: 'Rimozione del serbatoio di olio',
    radiatorControl: 'Controllo del radiatore',
    outdoorUnitWall: "Montaggio a parete dell'unità esterna",
    unknown: 'Addon di installazione sconosciuto',
  },

  // Radiators converted to camelCase
  radiatorsDesign: 'Design',
  radiatorsLarge: 'Grande',
  radiatorsMedium: 'Medio',
  radiatorsSmall: 'Piccolo',

  // Payment plan converted to camelCase
  paymentPlanTitle: 'Piano di pagamento',
  paymentPlanMonthly: '12 mesi',
  paymentPlanUpfront: 'Pagamento anticipato',

  // Modal converted to camelCase
  modalTitle: 'Salvare i progressi?',
  modalDescription:
    'Non hai salvato i tuoi progressi. Salva ora per continuare, o annulla per continuare a modificare questa pagina',

  // Quote sending converted to camelCase
  sendQuoteLoading: 'Caricamento in corso...',
  sendQuoteSuccess: 'Preventivo inviato con successo',
  sendQuoteError: "Errore nell'invio del preventivo",
};

// Common translations shared across all locales
const commonTranslations = {
  saveAndContinue: 'Save & continue',
  cancel: 'Cancel',
  save: 'Save',
  saving: 'Saving...',
  no: 'No',
  yes: 'Yes',
  select: 'Select',
};

// German common translations
const deCommonTranslations = {
  saveAndContinue: 'Speichern & fortfahren',
  cancel: 'Abbrechen',
  save: 'Speichern',
  saving: 'Wird gespeichert...',
  no: 'Nein',
  yes: 'Ja',
  select: 'Auswählen',
};

// Italian common translations
const itCommonTranslations = {
  saveAndContinue: 'Salva e continua',
  cancel: 'Annulla',
  save: 'Salva',
  saving: 'Salvataggio in corso...',
  no: 'No',
  yes: 'Sì',
  select: 'Seleziona',
};

export const configurationTranslations = {
  gb: {
    config: enConfig,
    common: commonTranslations,
  },
  de: {
    config: deConfig,
    common: deCommonTranslations,
  },
  it: {
    config: itConfig,
    common: itCommonTranslations,
  },
} as const;
