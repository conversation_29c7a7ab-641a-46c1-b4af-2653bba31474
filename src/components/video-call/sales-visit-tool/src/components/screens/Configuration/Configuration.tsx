import { type EnergySolutionProductDetails } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';
import { useRouter } from 'next/router';
import React, { useEffect, useState, useRef } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useFormChangesProtection } from '../../../hooks/formChangesProtection';
import { useProductSuspenseQuery } from '../../../hooks/products';
import { useSolutionSuspenseQuery } from '../../../hooks/solution';
import { useConfigurationAddonStore } from 'components/video-call/sales-visit-tool/src/store/useConfigurationAddonStore';
import { useLockPriceStore } from 'components/video-call/sales-visit-tool/src/store/useLockPriceStore';
import { configurationTranslations } from './translations/translations';
import { useTranslation } from '../../../hooks/translation';
import {
  type ConfigurationFormData,
  getDefaultFormValues,
} from 'components/video-call/sales-visit-tool/src/utils/getDefaultConfigurationSetup';
import { EvCharger } from './forms/EvCharger';
import { Extras } from './forms/Extras';
import { HeatPump } from './forms/HeatPump';
import { InstallationAddons } from './forms/InstallationAddons';
import { PlanningAndEPC } from './forms/PlanningAndEPC';
import { Radiators } from './forms/Radiators';
import { SolarAndBattery } from './forms/SolarAndBattery';
import { Subsidy } from './forms/Subsidy';
import { TechnicalSurveyAndSystemDesign } from './forms/TechnicalSurveyAndSystemDesign';
import { TimeoutErrorModal } from '../../modals/TimeoutErrorModal/TimeoutErrorModal';
import { TRPCClientError } from '@trpc/client';
import { api } from 'utils/api';
import { Box, Stack, Typography } from '@mui/material';
import { ExclamationDiamondOutlinedIcon } from '@ui/components/StandardIcons/ExclamationDiamondOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { grey } from '@ui/theme/colors';
import get from 'lodash/get';
import { Modal } from '@ui/components/Modal/Modal';
import {
  sendConfigAddonUpdate,
  sendInputHover,
  sendProductsData,
  sendRefetchSolution,
} from 'components/video-call/dataChannelMessages';

interface ConfigurationProps {
  activeDataChannel: RTCDataChannel;
}

const Configuration: React.FC<ConfigurationProps> = ({ activeDataChannel }) => {
  const { query } = useRouter();
  const { solution: solutionId } = query as { solution: string };
  const utils = api.useUtils();
  const [solution, { refetch }] = useSolutionSuspenseQuery();
  const [products] = useProductSuspenseQuery({ priceListId: solution.priceListId, select: (data) => data });
  const defaultValues = getDefaultFormValues({ solution, products });
  const methods = useForm<ConfigurationFormData>({ defaultValues });
  const { mutateAsync: submitEnergySolution } = api.EnergySolution.saveEnergySolution.useMutation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currencyCode = solution?.subsidy?.currencyCode;
  const { setOpenModal } = useLockPriceStore();
  const { setBatteryKwh, setSolarPanelKwp, setHasSolarPanels, setHasBattery } = useConfigurationAddonStore();
  const t = useTranslation(configurationTranslations);
  // Use a Set to track fields updated by remote, to prevent echo loops
  const skipNextChange = useRef<Set<string>>(new Set());

  useEffect(() => {
    if (solution.stage === 'priceLocked' && methods.formState.isDirty) {
      setOpenModal(true);
    }
  }, [solution.stage, methods.formState.isDirty, setOpenModal]);

  const [showTimeoutModal, setShowTimeoutModal] = useState(false);

  useEffect(() => {
    if (!activeDataChannel) return;
    sendProductsData(activeDataChannel, products);
  }, [products, activeDataChannel]);

  useEffect(() => {
    sendRefetchSolution(activeDataChannel, solution);
  }, [solution, activeDataChannel]);

  // Collaborative editing: send local changes
  useEffect(() => {
    if (!activeDataChannel) return;
    const subscription = methods.watch((value, { name }) => {
      if (!name) return;
      if (skipNextChange.current.has(name)) {
        skipNextChange.current.delete(name);
        return;
      }
      // Use lodash.get to support nested fields (e.g., 'subsidy.housingUnits')

      sendConfigAddonUpdate(activeDataChannel, { field: name, value: get(value, name) });
    });
    return () => subscription.unsubscribe();
  }, [methods, activeDataChannel]);

  const onSubmit = async (data: ConfigurationFormData) => {
    setIsSubmitting(true);
    const toastId = toast.loading(t.common.saving);

    const formKeys = Object.keys(data) as Array<keyof ConfigurationFormData>;
    const productsData = [] as {
      sku: string | null;
      quantity?: number;
      details?: EnergySolutionProductDetails;
      priceOverrideMinorAmountExclTax?: number;
    }[];

    formKeys.forEach((field) => {
      if (field === 'insulation' || field === 'subsidy') return;

      if (field === 'radiators') {
        data[field]
          .filter((radiator) => !!radiator.quantity)
          .forEach((radiator) => {
            productsData.push({
              sku: radiator.sku,
              quantity: radiator.quantity,
            });
          });
        return;
      }
      if (field === 'extras') {
        data[field].forEach((extra) => {
          productsData.push({
            sku: extra.sku,
            quantity: extra.quantity,
            priceOverrideMinorAmountExclTax: extra.priceOverrideMinorAmountExclTax,
            details: extra.details,
          });
        });
        return;
      }

      if (field === 'installationAddons') {
        data[field].forEach((addon) => {
          productsData.push({ sku: addon });
        });
        return;
      }

      productsData.push({ sku: data[field] });
    });

    const filteredProducts = productsData.filter((item) => item?.sku) as {
      sku: string;
      quantity?: number;
      details?: EnergySolutionProductDetails;
      priceOverrideMinorAmountExclTax?: number;
    }[];

    try {
      await submitEnergySolution({
        energySolutionId: solutionId,
        products: filteredProducts,
        subsidy: methods.formState.dirtyFields.subsidy ? data.subsidy : undefined,
        currencyCode: currencyCode,
        country: solution.country,
      });

      const { data: newSolution } = await refetch();
      // Send the updated solution data over WebRTC
      sendRefetchSolution(activeDataChannel, newSolution);
      const { system } = newSolution?.products ?? {};

      setHasSolarPanels(!!system?.solarPanel?.kwp || !!system?.solarAndBatteryPackage?.solar?.kwp);
      setHasBattery(!!system?.solarAndBatteryPackage?.battery?.kwh);
      if (system?.solarPanel?.kwp) {
        setSolarPanelKwp(system.solarPanel.kwp);
      }
      if (system?.solarAndBatteryPackage?.battery?.kwh && system.solarAndBatteryPackage.solar?.kwp) {
        setBatteryKwh(system.solarAndBatteryPackage.battery.kwh);
        setSolarPanelKwp(system.solarAndBatteryPackage.solar.kwp);
      }

      void utils.Savings.simulateSavings.refetch();

      if (newSolution) {
        methods.reset(getDefaultFormValues({ solution: newSolution, products }));
      }

      toast.success(t.config.saveSuccess, { id: toastId });
      return true;
    } catch (error) {
      if (error instanceof TRPCClientError && error.meta?.reason === 'timeout') {
        setShowTimeoutModal(true);
        toast.dismiss(toastId);
      } else {
        toast.error(t.common.save, { id: toastId, duration: 5000 });
      }
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  const { isModalOpen, setModalOpen, handleSaveAndNavigate } = useFormChangesProtection<ConfigurationFormData>({
    methods,
    onSave: onSubmit,
    hasRequiredFields: true,
    checkRequirements: false,
  });

  const handleFormMouseOver = (name: string) => {
    sendInputHover(activeDataChannel, name);
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <Box sx={{ pt: 8 }}>
          <Box sx={{ m: 'auto', maxWidth: 768 }}>
            <Typography variant="headline1" sx={{ mb: 8 }}>
              {t.config.title}
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 10, mt: 8 }}>
              <HeatPump priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <TechnicalSurveyAndSystemDesign priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <Radiators priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <InstallationAddons priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <Extras priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <PlanningAndEPC priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <SolarAndBattery onMouseOver={handleFormMouseOver} />
              <EvCharger priceListId={solution.priceListId} onMouseOver={handleFormMouseOver} />
              <Subsidy country={solution.country} onMouseOver={handleFormMouseOver} />
            </Box>
          </Box>
        </Box>
        <Button
          type="submit"
          variant="contained"
          size="small"
          sx={{
            position: 'sticky',
            bottom: 64,
            right: 0,
            float: 'right',
            minWidth: 80,
            borderRadius: 999,
            boxShadow: 6,
            bgcolor: grey[150],
            color: grey[900],
            opacity: !methods.formState.isDirty || isSubmitting ? 0.4 : 1,
            '&:hover': {
              bgcolor: grey[200],
            },
          }}
          disabled={!methods.formState.isDirty || isSubmitting}
        >
          {isSubmitting ? t.common.saving : t.common.save}
        </Button>
      </form>
      <Modal isModalOpen={isModalOpen} handleClose={() => setModalOpen(false)} height="auto" width="auto">
        <Stack>
          <Stack direction="row" spacing={2}>
            <ExclamationDiamondOutlinedIcon />
            <Typography variant="headline2">{t.config.modalTitle}</Typography>
          </Stack>
          <Typography variant="body1">{t.config.modalDescription}</Typography>
          <Stack direction="row" spacing={2}>
            <Button variant="contained" onClick={handleSaveAndNavigate} disabled={isSubmitting} sx={{ width: '50%' }}>
              {t.common.saveAndContinue}
            </Button>
            <Button
              variant="outlined"
              onClick={() => setModalOpen(false)}
              disabled={isSubmitting}
              sx={{ width: '50%' }}
            >
              {t.common.cancel}
            </Button>
          </Stack>
        </Stack>
      </Modal>
      <TimeoutErrorModal isOpen={showTimeoutModal} setOpen={setShowTimeoutModal} />
    </FormProvider>
  );
};

export default Configuration;
