import { type SupportedCountries } from 'components/video-call/sales-visit-tool/src/types/supportedCountries';

export type ButtonsPerRow = 2 | 3 | 4 | 6;

export interface ButtonRowConfig {
  indoorUnit: ButtonsPerRow;
  outdoorUnit: ButtonsPerRow;
}

export type ConfigurationFields = ReturnType<typeof buttonPerRowConfig>;
export const buttonPerRowConfig = (country: SupportedCountries): ButtonRowConfig => {
  const config: ButtonRowConfig = {
    indoorUnit: 2,
    outdoorUnit: 3,
  };

  switch (country) {
    case 'gb':
    case 'gb-sct':
      config.indoorUnit = 3;
      break;
    case 'de':
    case 'it':
    default:
      break;
  }
  return config;
};
