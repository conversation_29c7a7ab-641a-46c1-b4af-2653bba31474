import { useState } from 'react';
import { IconButton, Menu, MenuItem, ListItemIcon, ListItemText, Stack } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { Select } from '@ui/components/Select/Select';
import { CrossSmallOutlinedIcon } from '@ui/components/StandardIcons/CrossSmallOutlinedIcon';

export type BackgroundEffect = 'none' | 'blur' | 'replace';

// Icons (you may need to import these or use existing ones from your icon library)
export const BlurIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <circle cx="6" cy="12" r="2" opacity="0.4" />
    <circle cx="12" cy="12" r="2" opacity="0.7" />
    <circle cx="18" cy="12" r="2" opacity="0.4" />
    <circle cx="9" cy="6" r="1.5" opacity="0.3" />
    <circle cx="15" cy="6" r="1.5" opacity="0.5" />
    <circle cx="9" cy="18" r="1.5" opacity="0.3" />
    <circle cx="15" cy="18" r="1.5" opacity="0.5" />
  </svg>
);

const ReplaceBackgroundIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <defs>
      <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#667eea', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#764ba2', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <rect x="2" y="2" width="20" height="20" rx="2" fill="url(#bgGradient)" />
    <circle cx="8" cy="8" r="2" fill="white" opacity="0.9" />
    <path d="M6 16c0-2 2-4 4-4s4 2 4 4" stroke="white" strokeWidth="2" fill="none" opacity="0.9" />
  </svg>
);

interface BackgroundEffectsControlProps {
  currentEffect: BackgroundEffect;
  onEffectChange: (effect: BackgroundEffect) => void;
  disabled?: boolean;
  iconOrDropdown?: 'icon' | 'dropdown';
}

/**
 * BackgroundEffectsControl provides UI for managing video background effects.
 * Includes options for blur effects and intensity control.
 */
export const BackgroundEffectsControl = ({
  currentEffect,
  onEffectChange,
  disabled = false,
  iconOrDropdown = 'icon',
}: BackgroundEffectsControlProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEffectSelect = (effect: BackgroundEffect) => {
    onEffectChange(effect);
    handleClose();
  };

  const getIconForEffect = (effect: BackgroundEffect) => {
    switch (effect) {
      case 'blur':
        return <BlurIcon />;
      case 'replace':
        return <ReplaceBackgroundIcon />;
      case 'none':
      default:
        return <BlurIcon />;
    }
  };

  return iconOrDropdown === 'icon' ? (
    <>
      <IconButton
        onClick={handleClick}
        disabled={disabled}
        sx={{
          bgcolor: grey[100],
          color: 'white',
          '&:hover': {
            bgcolor: grey[150],
          },
        }}
        aria-label="Background effects"
      >
        {getIconForEffect(currentEffect)}
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            minWidth: 280,
            maxWidth: 320,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'top' }}
      >
        <MenuItem onClick={() => handleEffectSelect('none')} selected={currentEffect === 'none'}>
          <ListItemIcon>
            <CrossSmallOutlinedIcon color={grey[700]} />
          </ListItemIcon>
          <ListItemText primary="No Effect" secondary="Show your actual background" />
        </MenuItem>

        <MenuItem onClick={() => handleEffectSelect('blur')} selected={currentEffect === 'blur'}>
          <ListItemIcon>
            <BlurIcon />
          </ListItemIcon>
          <ListItemText primary="Background Blur" secondary="Blur your background" />
        </MenuItem>

        <MenuItem onClick={() => handleEffectSelect('replace')} selected={currentEffect === 'replace'}>
          <ListItemIcon>
            <ReplaceBackgroundIcon />
          </ListItemIcon>
          <ListItemText primary="Virtual Background" secondary="Replace with Aira background" />
        </MenuItem>
      </Menu>
    </>
  ) : (
    <Select
      name="background-effect-select"
      value={currentEffect}
      onChange={(e) => handleEffectSelect(e.target.value as BackgroundEffect)}
      size="small"
      fullWidth
      label=""
    >
      <MenuItem value="none" onClick={() => handleEffectSelect('none')} selected={currentEffect === 'none'}>
        <Stack direction="row" alignItems="center" justifyContent="center" gap={1}>
          <CrossSmallOutlinedIcon color={grey[700]} /> No effect
        </Stack>
      </MenuItem>

      <MenuItem value="blur" onClick={() => handleEffectSelect('blur')} selected={currentEffect === 'blur'}>
        <Stack direction="row" alignItems="center" justifyContent="center" gap={1}>
          <BlurIcon />
          Blur your background
        </Stack>
      </MenuItem>
      <MenuItem value="replace" onClick={() => handleEffectSelect('replace')} selected={currentEffect === 'replace'}>
        <Stack direction="row" alignItems="center" justifyContent="center" gap={1}>
          <ReplaceBackgroundIcon />
          Replace with Aira background
        </Stack>
      </MenuItem>
    </Select>
  );
};
