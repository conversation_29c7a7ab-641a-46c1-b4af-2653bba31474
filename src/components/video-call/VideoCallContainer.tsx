import { useState, useCallback, useEffect } from 'react';
import CallEnded from 'components/video-call/CallEnded';
import PhotoUpload from 'components/video-call/PhotoUpload';
import PermissionPreview from 'components/video-call/PermissionPreview';
import MeetingTypeSelection from 'components/video-call/MeetTypeSelection';
import { FullSolution } from './sales-visit-tool/src/utils/getFullSolution';
import { BackgroundEffect } from './BackgroundEffectsControl';

export { getServerSideProps } from 'server/server-environment';
import { RoomContext } from '@livekit/components-react';
import { Room } from 'livekit-client';
import { api } from 'utils/api';
import { useServerEnvironment } from 'context/server-environment-context';
import { useLiveKitToken } from './hooks/useLivekitToken';
import { useLocalTracks } from './hooks/useLocalTracks';
import { Box, Typography, Button } from '@mui/material';
import { beige } from '@ui/theme/colors';
import ConnectedContainer from './ConnectedContainer';

type MeetingType = 'with-sales-flow' | 'only-video';

export default function VideoCallContainer({
  solutionId,
  energySolution,
  selectedVideoDevice,
  selectedAudioDevice,
  setSelectedVideoDevice,
  setSelectedAudioDevice,
  setBackgroundEffect,
  backgroundEffect,
  videoDevices,
  audioDevices,
  setVideoDevices,
  setAudioDevices,
  setVideoEnabled,
  setAudioEnabled,
  videoEnabled,
  audioEnabled,
}: {
  solutionId: string;
  energySolution: FullSolution;
  selectedVideoDevice: string;
  selectedAudioDevice: string;
  setSelectedVideoDevice: (deviceId: string) => void;
  setSelectedAudioDevice: (deviceId: string) => void;
  setBackgroundEffect: (backgroundEffect: BackgroundEffect) => void;
  backgroundEffect: BackgroundEffect;
  videoDevices: MediaDeviceInfo[];
  audioDevices: MediaDeviceInfo[];
  setVideoDevices: (devices: MediaDeviceInfo[]) => void;
  setAudioDevices: (devices: MediaDeviceInfo[]) => void;
  setVideoEnabled: (enabled: boolean) => void;
  setAudioEnabled: (enabled: boolean) => void;
  videoEnabled: boolean;
  audioEnabled: boolean;
}) {
  const [meetingType, setMeetingType] = useState<MeetingType | null>('only-video');
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [photosUploaded, setPhotosUploaded] = useState(false);
  const [isHangup, setIsHangup] = useState(false);
  const [room] = useState<Room>(() => new Room({}));
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const { livekitUrl } = useServerEnvironment();
  const [roomConnectionError, setRoomConnectionError] = useState<Error | null>(null);
  const [hasPermissions, setHasPermissions] = useState(false);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'reconnecting'>(
    'disconnected',
  );

  const { token } = useLiveKitToken({
    solutionId,
    name: user?.firstName + ' ' + user?.lastName,
    showVideoCall,
  });

  const { audioTrack, videoTrack, publishTracks } = useLocalTracks({
    selectedVideoDevice,
    selectedAudioDevice,
    backgroundEffect,
    hasPermissions,
    room,
  });

  /**
   * Handle proceeding to the actual video call with selected devices and background settings
   */
  const handleJoinCall = useCallback(() => {
    setShowVideoCall(true);
  }, []);

  useEffect(() => {
    if (token && livekitUrl && connectionState === 'disconnected') {
      const connect = async () => {
        try {
          setConnectionState('connecting');
          setRoomConnectionError(null);
          console.log('Connecting to LiveKit room:', livekitUrl);
          await room.connect(livekitUrl, token);
          console.log('Successfully connected to LiveKit room');
          setConnectionState('connected');
          publishTracks(videoTrack, audioTrack);
        } catch (error) {
          console.error('Error connecting to LiveKit room:', error);
          setRoomConnectionError(error as Error);
          setConnectionState('disconnected');
        }
      };
      connect();
    }
  }, [token, livekitUrl, room, connectionState, videoTrack, audioTrack, publishTracks]);

  // Handle room state changes
  useEffect(() => {
    const handleRoomStateChange = (state: string) => {
      console.log('Room state changed to:', state);
      if (state === 'connected') {
        setConnectionState('connected');
        setRoomConnectionError(null);
      } else if (state === 'disconnected') {
        setConnectionState('disconnected');
      } else if (state === 'reconnecting') {
        setConnectionState('reconnecting');
      }
    };

    room.on('connectionStateChanged', handleRoomStateChange);
    return () => {
      room.off('connectionStateChanged', handleRoomStateChange);
    };
  }, [room]);

  useEffect(() => {
    if (isHangup) {
      if (audioTrack) audioTrack.stop();
      if (videoTrack) videoTrack.stop();
      room.disconnect();
    }
  }, [isHangup, room, audioTrack, videoTrack]);

  if (meetingType === null) {
    return <MeetingTypeSelection onMeetingTypeSelected={setMeetingType} />;
  }

  // Step 1: Show photo upload prompt before permission preview
  if (!photosUploaded && meetingType === 'with-sales-flow') {
    return <PhotoUpload onPhotosUploaded={setPhotosUploaded} />;
  }

  // Step 2: Show permission preview before video call
  if (!showVideoCall) {
    return (
      <PermissionPreview
        localVideoTrack={videoTrack!}
        onJoinCall={handleJoinCall}
        setHasPermissions={setHasPermissions}
        hasPermissions={hasPermissions}
        selectedVideoDevice={selectedVideoDevice}
        selectedAudioDevice={selectedAudioDevice}
        setSelectedVideoDevice={setSelectedVideoDevice}
        setSelectedAudioDevice={setSelectedAudioDevice}
        setBackgroundEffect={setBackgroundEffect}
        backgroundEffect={backgroundEffect}
        setVideoDevices={setVideoDevices}
        setAudioDevices={setAudioDevices}
        videoDevices={videoDevices}
        audioDevices={audioDevices}
        setVideoEnabled={setVideoEnabled}
        setAudioEnabled={setAudioEnabled}
        videoEnabled={videoEnabled}
        audioEnabled={audioEnabled}
      />
    );
  }

  if (isHangup) {
    return <CallEnded />;
  }

  // Show connection status if not connected
  if (connectionState === 'connecting' || connectionState === 'reconnecting') {
    return (
      <Box
        sx={{
          height: '100dvh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: beige[150],
        }}
      >
        <Typography variant="h6">
          {connectionState === 'connecting' ? 'Connecting to call...' : 'Reconnecting...'}
        </Typography>
      </Box>
    );
  }

  if (roomConnectionError) {
    return (
      <Box
        sx={{
          height: '100dvh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: beige[150],
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          Connection Error
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {roomConnectionError.message}
        </Typography>
        <Button
          variant="contained"
          onClick={() => {
            setRoomConnectionError(null);
            setConnectionState('disconnected');
          }}
        >
          Retry Connection
        </Button>
      </Box>
    );
  }

  return (
    <RoomContext.Provider value={room}>
      {connectionState === 'connected' && audioTrack && videoTrack && (
        <ConnectedContainer
          solutionId={solutionId}
          energySolution={energySolution}
          selectedVideoDevice={selectedVideoDevice}
          selectedAudioDevice={selectedAudioDevice}
          setBackgroundEffect={setBackgroundEffect}
          backgroundEffect={backgroundEffect}
          setIsHangup={setIsHangup}
          setSelectedVideoDevice={setSelectedVideoDevice}
          setSelectedAudioDevice={setSelectedAudioDevice}
          videoDevices={videoDevices}
          audioDevices={audioDevices}
          localAudioTrack={audioTrack}
          localVideoTrack={videoTrack}
          videoEnabled={videoEnabled}
          audioEnabled={audioEnabled}
          setVideoEnabled={setVideoEnabled}
          setAudioEnabled={setAudioEnabled}
        />
      )}
    </RoomContext.Provider>
  );
}
