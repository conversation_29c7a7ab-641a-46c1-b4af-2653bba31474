import { Paper } from '@mui/material';
import { motion } from 'framer-motion';
import { Carousel } from './sales-visit-tool/src/components/atoms/Carousel/Carousel';
// import Main from './sales-visit-tool/src/components/screens/Main/Main';
// import HouseTour from './sales-visit-tool/src/components/screens/HouseTour/HouseTour';
import CurrentSetup from './sales-visit-tool/src/components/screens/CurrentSetup/CurrentSetup';
import Configuration from './sales-visit-tool/src/components/screens/Configuration/Configuration';
import Consumption from './sales-visit-tool/src/components/screens/Consumption/Consumption';
import EnergyBills from './sales-visit-tool/src/components/screens/EnergyBills/EnergyBills';
import Savings from './sales-visit-tool/src/components/screens/Savings/Savings';
import Co2 from './sales-visit-tool/src/components/screens/Co2/Co2';
import Package from './sales-visit-tool/src/components/screens/Package/Package';
import { Loading } from './sales-visit-tool/src/components/atoms/Loading/Loading';
import { Suspense } from 'react';
import { Header } from './sales-visit-tool/src/components/global/Header';
import { TimeFrame as EnergyBillsTimeFrame } from './sales-visit-tool/src/components/screens/EnergyBills/TimeFrameContext';
import { TimeFrame as SavingsTimeFrame } from './sales-visit-tool/src/components/screens/Savings/TimeFrameContext';
import { PricedLockedModal } from './sales-visit-tool/src/components/common/PricedLocked/PricedLockedModal';
import Presentation from './sales-visit-tool/src/components/screens/Presentation/Presentation';
import QuoteContainer from './sales-visit-tool/src/components/screens/Quote/QuoteContainer';
import SurveyContainer from './sales-visit-tool/src/components/screens/Survey/SurveyContainer';
import HomePhotos from './sales-visit-tool/src/components/screens/HomePhotos/HomePhotos';
import { useRouter } from 'next/router';

interface SalesVisitContainerProps {
  activeDataChannel: RTCDataChannel;
  isChatOpen: boolean;

  energyBillsTimeFrame: EnergyBillsTimeFrame;

  savingsTimeFrame: SavingsTimeFrame;

  registerConfigAddonRemoteHandler: (handler: (update: { field: string; value: any }) => void) => void;

  peerRejoined?: boolean;

  remoteHoveredPhotoIndex?: number | null;

  setEnergyBillsTimeFrame: (timeFrame: EnergyBillsTimeFrame) => void;
  setSavingsTimeFrame: (timeFrame: SavingsTimeFrame) => void;
}

export default function SalesVisitContainer(props: SalesVisitContainerProps) {
  const router = useRouter();
  const { solution: solutionId } = router.query as { solution: string };
  const {
    activeDataChannel,
    isChatOpen,
    energyBillsTimeFrame,
    savingsTimeFrame,
    registerConfigAddonRemoteHandler,
    peerRejoined,
    remoteHoveredPhotoIndex,
    setEnergyBillsTimeFrame,
    setSavingsTimeFrame,
  } = props;

  return (
    <Paper
      elevation={3}
      sx={{
        width: isChatOpen ? '55%' : '75%',
        position: 'relative',
        overflow: 'auto',
        borderRadius: 2,
        bgcolor: '#fff',
        transition: 'width 0.3s ease-in-out',
        height: '100%',
      }}
    >
      <Header />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1, transition: { duration: 0.4 } }}
        style={{ height: 'calc(100% - 96px)' }}
      >
        <Carousel>
          {/* <Main /> */}
          {/* <HouseTour /> */}
          <HomePhotos
            activeDataChannel={activeDataChannel}
            solutionId={solutionId}
            remoteHoveredPhotoIndex={remoteHoveredPhotoIndex}
          />
          <Presentation activeDataChannel={activeDataChannel} />
          <CurrentSetup activeDataChannel={activeDataChannel} />
          <Suspense fallback={<Loading />}>
            <Configuration activeDataChannel={activeDataChannel} />
          </Suspense>
          <Consumption
            activeDataChannel={activeDataChannel}
            onRemoteConfigAddonUpdate={registerConfigAddonRemoteHandler}
          />
          {/* Provide synced timeframe state to EnergyBills and Savings */}
          <EnergyBills
            activeDataChannel={activeDataChannel}
            timeFrame={energyBillsTimeFrame}
            onRemoteConfigAddonUpdate={registerConfigAddonRemoteHandler}
            peerRejoined={peerRejoined}
            setEnergyBillsTimeFrame={setEnergyBillsTimeFrame}
          />
          <Savings
            activeDataChannel={activeDataChannel}
            timeFrame={savingsTimeFrame}
            onRemoteConfigAddonUpdate={registerConfigAddonRemoteHandler}
            peerRejoined={peerRejoined}
            setSavingsTimeFrame={setSavingsTimeFrame}
          />
          <Co2
            activeDataChannel={activeDataChannel}
            onRemoteConfigAddonUpdate={registerConfigAddonRemoteHandler}
            peerRejoined={peerRejoined}
          />
          <Package activeDataChannel={activeDataChannel} />
          <QuoteContainer />
          <SurveyContainer />
        </Carousel>
      </motion.div>
      <PricedLockedModal activeDataChannel={activeDataChannel} />
    </Paper>
  );
}
