import { Box, Paper, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';

interface MeetTypeSelectionProps {
  onMeetingTypeSelected: (meetingType: 'with-sales-flow' | 'only-video') => void;
}

export default function MeetTypeSelection({ onMeetingTypeSelected }: MeetTypeSelectionProps) {
  return (
    <Box
      sx={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 3,
      }}
    >
      <Paper
        sx={{
          maxWidth: 'fit-content',
          width: '100%',
          height: 'fit-content',
          p: 5,
          textAlign: 'center',
          borderRadius: 3,
          boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 2,
          }}
        >
          <Stack direction="column" gap={1} sx={{ maxWidth: '100%' }} alignItems="flex-start">
            <Typography variant="headline2" gutterBottom>
              Select meeting type
            </Typography>
            <Typography variant="body1" gutterBottom>
              Please select the type of meeting you want to have.
            </Typography>
          </Stack>
          <Stack direction="row" gap={2} sx={{ width: '100%', justifyContent: 'space-between' }}>
            <Button variant="outlined" onClick={() => onMeetingTypeSelected('with-sales-flow')} sx={{ width: '100%' }}>
              With sales flow
            </Button>
            <Button variant="contained" onClick={() => onMeetingTypeSelected('only-video')} sx={{ width: '100%' }}>
              Only video
            </Button>
          </Stack>
        </Stack>
      </Paper>
    </Box>
  );
}
