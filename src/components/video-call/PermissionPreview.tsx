import { Box, CircularProgress, Button, Typography, Paper, Alert, MenuItem, Stack, IconButton } from '@mui/material';
import { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { CameraVideoOutlinedIcon } from '@ui/components/StandardIcons/CameraVideoOutlinedIcon';
import { MicrophoneOutlinedIcon } from '@ui/components/StandardIcons/MicrophoneOutlinedIcon';
import { MicrophoneStrikethroughOutlinedIcon } from '@ui/components/StandardIcons/MicrophoneStrikethroughOutlinedIcon';
import { CameraVideoStrikethroughOutlinedIcon } from '@ui/components/StandardIcons/CameraVideoStrikethroughOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { Select } from '@ui/components/Select/Select';
import { BackgroundEffect, BackgroundEffectsControl, BlurIcon } from 'components/video-call/BackgroundEffectsControl';
import { enumerateDevices } from './media';
import { facingModeFromLocalTrack, LocalVideoTrack } from 'livekit-client';

export interface DeviceSelections {
  videoDeviceId: string;
  audioDeviceId: string;
}

export interface BackgroundSettings {
  effect: BackgroundEffect;
  blurAmount: number;
}

interface PermissionPreviewProps {
  onJoinCall: () => void;
  setHasPermissions: (hasPermissions: boolean) => void;
  hasPermissions: boolean;
  setBackgroundEffect: (backgroundEffect: BackgroundEffect) => void;
  backgroundEffect: BackgroundEffect;
  selectedVideoDevice: string;
  selectedAudioDevice: string;
  setSelectedVideoDevice: (deviceId: string) => void;
  setSelectedAudioDevice: (deviceId: string) => void;
  setVideoDevices: (devices: MediaDeviceInfo[]) => void;
  setAudioDevices: (devices: MediaDeviceInfo[]) => void;
  videoDevices: MediaDeviceInfo[];
  audioDevices: MediaDeviceInfo[];
  setVideoEnabled: (enabled: boolean) => void;
  setAudioEnabled: (enabled: boolean) => void;
  videoEnabled: boolean;
  audioEnabled: boolean;
  localVideoTrack: LocalVideoTrack;
}

/**
 * Component that handles camera/microphone permission requests and shows preview before joining call
 */
export default function PermissionPreview({
  onJoinCall,
  setHasPermissions,
  hasPermissions,
  setBackgroundEffect,
  backgroundEffect,
  selectedVideoDevice,
  selectedAudioDevice,
  setSelectedVideoDevice,
  setSelectedAudioDevice,
  setVideoDevices,
  setAudioDevices,
  videoDevices,
  audioDevices,
  setVideoEnabled,
  setAudioEnabled,
  videoEnabled,
  audioEnabled,
  localVideoTrack,
}: PermissionPreviewProps) {
  const [permissionError, setPermissionError] = useState<string>('');
  const [isRequestingPermissions, setIsRequestingPermissions] = useState(false);

  /**
   * Request camera and microphone permissions
   */
  const requestPermissions = useCallback(async () => {
    setIsRequestingPermissions(true);
    setPermissionError('');

    try {
      await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });

      // Enumerate devices after getting permissions for better device labels
      await enumerateDevices({
        setSelectedAudioDevice,
        setSelectedVideoDevice,
        selectedVideoDevice,
        selectedAudioDevice,
        setVideoDevices,
        setAudioDevices,
      });
      setHasPermissions(true);
    } catch (error) {
      console.error('Error requesting permissions:', error);
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          setPermissionError('Camera and microphone access was denied. Please allow access and try again.');
        } else if (error.name === 'NotFoundError') {
          setPermissionError('No camera or microphone found. Please check your devices and try again.');
        } else {
          setPermissionError('Error accessing camera or microphone. Please check your devices and try again.');
        }
      } else {
        setPermissionError('Unknown error occurred while accessing camera or microphone.');
      }
    } finally {
      setIsRequestingPermissions(false);
    }
  }, [
    setSelectedAudioDevice,
    setSelectedVideoDevice,
    selectedVideoDevice,
    selectedAudioDevice,
    setVideoDevices,
    setAudioDevices,
    setHasPermissions,
  ]);

  const videoEl = useRef(null);

  const facingMode = useMemo(() => {
    if (localVideoTrack) {
      const { facingMode } = facingModeFromLocalTrack(localVideoTrack);
      return facingMode;
    } else {
      return 'undefined';
    }
  }, [localVideoTrack]);

  useEffect(() => {
    if (videoEl.current && localVideoTrack && hasPermissions) {
      localVideoTrack.unmute();
      localVideoTrack.attach(videoEl.current);
    }
  }, [localVideoTrack, hasPermissions]);

  /**
   * Handle joining the call - stop preview stream and proceed with selected devices and background settings
   */
  const handleJoinCall = useCallback(() => {
    onJoinCall();
  }, [onJoinCall]);

  return (
    <Box
      sx={{
        height: '100dvh',
        width: '100dvw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 3,
      }}
    >
      <Paper
        sx={{
          maxWidth: hasPermissions ? 'fit-content' : 400,
          width: '100%',
          p: 5,
          textAlign: 'center',
          borderRadius: 3,
          boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        }}
      >
        {!hasPermissions ? (
          <Stack direction="column" gap={2} sx={{ maxWidth: '100%' }} alignItems="flex-start">
            <Typography variant="headline2" gutterBottom>
              Join Video Call
            </Typography>
            <Typography variant="body1" gutterBottom sx={{ mb: 3, textAlign: 'left' }}>
              To join the video call, we need access to your camera and microphone.
            </Typography>

            {permissionError && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {permissionError}
              </Alert>
            )}

            <Button
              variant="contained"
              size="large"
              onClick={requestPermissions}
              disabled={isRequestingPermissions}
              fullWidth
              startIcon={
                isRequestingPermissions ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <CameraVideoOutlinedIcon />
                    <MicrophoneOutlinedIcon />
                  </Box>
                )
              }
              sx={{
                py: 1.5,
                px: 4,
              }}
            >
              {isRequestingPermissions ? 'Requesting Permissions...' : 'Allow Camera & Microphone'}
            </Button>
          </Stack>
        ) : (
          <Stack direction="column" gap={3} sx={{ maxWidth: '100%', flex: 1, height: '100%' }} alignItems="flex-start">
            <Stack direction="column" gap={0} sx={{ maxWidth: '100%' }} alignItems="flex-start">
              <Typography variant="headline2" gutterBottom>
                Join Video Call
              </Typography>
              <Typography variant="body1" gutterBottom sx={{ textAlign: 'left' }}>
                Great! Here&apos;s your camera preview. You&apos;re ready to join the call.
              </Typography>
            </Stack>
            <Stack direction="row" gap={4} sx={{ maxWidth: '100%', height: '100%' }} alignItems="stretch">
              <Box
                sx={{
                  width: '100%',
                  minWidth: 400,
                  maxWidth: 700,
                  mx: 'auto',
                  borderRadius: 2,
                  overflow: 'hidden',
                  bgcolor: grey[900],
                  position: 'relative',
                }}
              >
                <video
                  ref={videoEl}
                  style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: 2,
                    overflow: 'hidden',
                    backgroundColor: grey[900],
                    transform: 'scaleX(-1)',
                  }}
                  data-lk-facing-mode={facingMode}
                />
              </Box>
              <Stack
                direction="column"
                gap={6}
                sx={{ maxWidth: '100%', flex: 1, height: '100%', flexShrink: 0 }}
                alignItems="flex-start"
                justifyContent="space-between"
              >
                {/* Device Selection Controls */}
                <Stack direction="column" gap={2} sx={{ width: '100%' }}>
                  <Stack
                    direction="column"
                    sx={{ mb: 2, width: '100%', gap: 1 }}
                    alignItems="flex-start"
                    justifyContent="flex-start"
                  >
                    <Stack
                      direction="row"
                      gap={0}
                      alignItems="center"
                      justifyContent="flex-start"
                      sx={{ width: '100%', marginLeft: '-6px' }}
                    >
                      <IconButton onClick={() => setVideoEnabled(!videoEnabled)}>
                        {videoEnabled ? (
                          <CameraVideoOutlinedIcon color={grey[900]} />
                        ) : (
                          <CameraVideoStrikethroughOutlinedIcon color={grey[900]} />
                        )}
                      </IconButton>
                      <Typography variant="body2Emphasis">Camera</Typography>
                    </Stack>
                    <Select
                      name="camera-select"
                      value={selectedVideoDevice}
                      onChange={(e) => setSelectedVideoDevice(e.target.value as string)}
                      size="small"
                      fullWidth
                      label=""
                    >
                      {videoDevices.map((device) => (
                        <MenuItem key={device.deviceId} value={device.deviceId}>
                          {device.label || `Camera ${device.deviceId.slice(0, 8)}...`}
                        </MenuItem>
                      ))}
                    </Select>
                  </Stack>

                  <Stack
                    direction="column"
                    sx={{ mb: 2, width: '100%', gap: 1 }}
                    alignItems="flex-start"
                    justifyContent="flex-start"
                  >
                    <Stack
                      direction="row"
                      gap={0}
                      alignItems="center"
                      justifyContent="flex-start"
                      sx={{ width: '100%', marginLeft: '-6px' }}
                    >
                      <IconButton onClick={() => setAudioEnabled(!audioEnabled)}>
                        {audioEnabled ? (
                          <MicrophoneOutlinedIcon color={grey[900]} />
                        ) : (
                          <MicrophoneStrikethroughOutlinedIcon color={grey[900]} />
                        )}
                      </IconButton>
                      <Typography variant="body2Emphasis">Microphone</Typography>
                    </Stack>
                    <Select
                      name="microphone-select"
                      value={selectedAudioDevice}
                      onChange={(e) => setSelectedAudioDevice(e.target.value as string)}
                      size="small"
                      fullWidth
                      label=""
                    >
                      {audioDevices.map((device) => (
                        <MenuItem key={device.deviceId} value={device.deviceId}>
                          {device.label || `Microphone ${device.deviceId.slice(0, 8)}...`}
                        </MenuItem>
                      ))}
                    </Select>
                  </Stack>

                  <Stack
                    direction="column"
                    sx={{ width: '100%' }}
                    alignItems="flex-start"
                    justifyContent="flex-start"
                    gap={1}
                  >
                    <Stack
                      direction="row"
                      gap={0}
                      alignItems="center"
                      justifyContent="flex-start"
                      sx={{ width: '100%', marginLeft: '-6px' }}
                    >
                      <IconButton>
                        <BlurIcon />
                      </IconButton>
                      <Typography variant="body2Emphasis">Background Effects</Typography>
                    </Stack>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <BackgroundEffectsControl
                        currentEffect={backgroundEffect}
                        onEffectChange={(effect) => {
                          setBackgroundEffect(effect);
                        }}
                        iconOrDropdown="dropdown"
                      />
                    </Box>
                  </Stack>
                </Stack>

                <Box sx={{ width: '100%', alignSelf: 'flex-end' }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleJoinCall}
                    fullWidth
                    sx={{
                      py: 1.5,
                      px: 4,
                      alignSelf: 'flex-end',
                    }}
                  >
                    Join Call
                  </Button>
                </Box>
              </Stack>
            </Stack>
          </Stack>
        )}
      </Paper>
    </Box>
  );
}
