import { useState } from 'react';
import { acceptedFilters, statusFilters } from './filters';
import { type ISolution } from './OngoingWorkList';

export const useFilters = () => {
  const [selectedStatusFilter, setSelectedStatusFilter] = useState<string | null>(null);
  const [selectedAcceptedFilter, setSelectedAcceptedFilter] = useState<string | null>(null);

  const changeFilter = (filter: string) => {
    if (filter === 'all') {
      setSelectedStatusFilter(null);
      setSelectedAcceptedFilter(null);
    } else if (statusFilters.includes(filter) && selectedStatusFilter === filter) {
      setSelectedStatusFilter(null);
    } else if (acceptedFilters.includes(filter) && selectedAcceptedFilter === filter) {
      setSelectedAcceptedFilter(null);
    } else if (statusFilters.includes(filter)) {
      setSelectedStatusFilter(filter);
    } else if (acceptedFilters.includes(filter)) {
      setSelectedAcceptedFilter(filter);
    }
  };

  const matchesFilters = (solution: ISolution) => {
    const matchesFilterAccepted = selectedAcceptedFilter === 'accepted' && solution.accepted;
    const matchesFilterNotaccepted = selectedAcceptedFilter === 'notAccepted' && !solution.accepted;
    const matchesAcceptedFilter = !selectedAcceptedFilter || matchesFilterAccepted || matchesFilterNotaccepted;
    const matchesStatusFilter = !selectedStatusFilter || solution.status === selectedStatusFilter; // Assuming status matches filter label
    return matchesStatusFilter && matchesAcceptedFilter;
  };

  const isSelectedFilter = (filter?: string) =>
    (filter === 'all' && !selectedStatusFilter && !selectedAcceptedFilter) ||
    filter === selectedStatusFilter ||
    filter === selectedAcceptedFilter;

  return {
    changeFilter,
    matchesFilters,
    isSelectedFilter,
  };
};
