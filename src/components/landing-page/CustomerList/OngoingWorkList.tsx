import React, { Fragment } from 'react';
import { Typo<PERSON>, Box, Stack, useMediaQuery, Chip } from '@mui/material';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { brandYellow, grey } from '@ui/theme/colors';
import { MessageKey } from 'messageType';
import { ArrowOutlinedIcon } from '@ui/components/Icons/ArrowOutlined/ArrowOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { CalendarCheckOutlinedIcon } from '@ui/components/StandardIcons/CalendarCheckOutlinedIcon';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { acceptedFilters, allFilters, statusFilters } from './filters';
import { useFilters } from './useFilters';
import { PinOutlinedIcon } from '@ui/components/StandardIcons/PinOutlinedIcon';

export interface ISolution {
  name?: string;
  postcode?: string;
  address?: string;
  status?: string;
  solutionId?: string;
  accepted: boolean;
}
export interface ICustomerListComponent {
  solutions: ISolution[];
}
const a11yProps = (index: number) => ({
  id: `simple-tab-${index}`,
  'aria-controls': `simple-tabpanel-${index}`,
});
export function OngoingWorkList({ solutions }: ICustomerListComponent) {
  const router = useRouter();
  const isNarrow = useMediaQuery('(max-width: 1080px)');

  const intl = useIntl();
  const { changeFilter, matchesFilters, isSelectedFilter } = useFilters();

  const getChipSx = (filter?: string) => ({
    fontFamily: 'AiraText',
    fontSize: '16px',
    border: 'none',
    borderRadius: '50px',
    height: '36px',
    padding: '8px 16px',
    ...(isSelectedFilter(filter)
      ? {
          background: brandYellow[400],
          color: grey[800],
        }
      : {
          background: '#22222608',
        }),
  });
  const onQuotationClick = (solution: ISolution) => {
    router.push(`/solution/${solution.solutionId}`);
  };
  const onInstallationProjectClick = (solution: ISolution) => {
    router.push(`/solution/${solution.solutionId}/installation-handover`);
  };

  return (
    <Box
      sx={{
        maxWidth: isNarrow ? 'calc(100vw - 40px)' : '1400px',
        mb: 4,
        mx: 'auto',
        '@media (max-width: 1400px)': {
          mx: 0,
        },
      }}
    >
      <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between" width="100%" mb={6}>
        <Stack
          mb={3}
          direction={isNarrow ? 'column' : 'row'}
          alignItems={isNarrow ? 'flex-start' : 'center'}
          flexWrap="wrap"
          justifyContent={isNarrow ? 'flex-start' : 'space-between'}
          width={isNarrow ? '100%' : '100%'}
          gap={2}
        >
          <Stack
            direction="row"
            alignItems="center"
            width={isNarrow ? '100%' : 'auto'}
            spacing={2}
            py={2}
            overflow="auto"
          >
            <Typography
              variant="body1"
              fontSize="14px"
              sx={{
                borderRight: `1px solid ${grey[900]}`,
                paddingRight: 2,
              }}
            >
              <FormattedMessage id="dashboard.filters.show" defaultMessage="Show" />
            </Typography>

            {[allFilters, statusFilters, acceptedFilters].map((filters) => (
              <Fragment key={filters.join('-')}>
                {filters.map((filter, filterIndex) => (
                  <Chip
                    key={filter}
                    label={intl.formatMessage({ id: `dashboard.filter.${filter}` as MessageKey })}
                    color="primary"
                    onClick={() => changeFilter(filter)}
                    variant={isSelectedFilter(filter) ? 'filled' : 'outlined'}
                    size="small"
                    sx={getChipSx(filter)}
                    {...a11yProps(filterIndex)}
                  />
                ))}
              </Fragment>
            ))}
          </Stack>
          <Stack direction="row" spacing={2} alignItems="center" justifyContent="flex-end">
            <Button
              variant="grey"
              fullWidth={isNarrow}
              onClick={() => {
                router.push('/surveys-planning');
              }}
            >
              <PinOutlinedIcon />
              <FormattedMessage id="dashboard.button.salesVisitMap" defaultMessage="Sales visit map" />
            </Button>
            <Button
              variant="contained"
              fullWidth={isNarrow}
              onClick={() => {
                router.push('/contact');
              }}
            >
              <FormattedMessage id="dashboard.button.contact" defaultMessage="Contact" />
            </Button>
          </Stack>
        </Stack>
      </Stack>
      <Stack>
        {solutions.length > 0 && !isNarrow && (
          <Stack direction="row" sx={{ width: '100%' }} flex={1} pb={1}>
            <Stack direction="row" sx={{ width: '100%' }} flex={1}>
              <Box flex={1} sx={{ paddingLeft: '60px' }}>
                <Typography variant="h6" fontSize="12px" sx={{ color: grey[500] }}>
                  <FormattedMessage id="dashboard.customerDetails" defaultMessage="Customer details" />
                </Typography>
              </Box>
              <Box width="270px">
                <Typography variant="h6" fontSize="12px" sx={{ color: grey[500] }}>
                  <FormattedMessage id="dashboard.status" defaultMessage="Status" />
                </Typography>
              </Box>
            </Stack>
            <div style={{ width: '270px' }} />
          </Stack>
        )}
        {solutions.length === 0 && (
          <Box
            py={3}
            sx={{
              width: '100%',
              background: '#22222608',
              borderRadius: '22px',
              height: '100%',
            }}
            px={2}
          >
            <Typography variant="body1">
              <FormattedMessage id="dashboard.noOngoingWork" defaultMessage="No ongoing work" />
            </Typography>
          </Box>
        )}
        {solutions.length > 0 && solutions.filter(matchesFilters).length === 0 && (
          <Box
            py={3}
            sx={{
              width: '100%',
              background: '#22222608',
              borderRadius: '22px',
              height: '100%',
            }}
            px={2}
          >
            <Typography variant="body1">
              <FormattedMessage
                id="dashboard.noOngoingWorkAccordingToFilters"
                defaultMessage="No ongoing work according to your chosen filters"
              />
            </Typography>
          </Box>
        )}
        <Box>
          <Stack spacing={1} gap={isNarrow ? 2 : 0}>
            {solutions?.filter(matchesFilters).map((solution) => (
              <Stack
                direction={isNarrow ? 'column' : 'row'}
                alignItems="center"
                justifyContent="space-between"
                py={isNarrow ? 3 : 2}
                sx={{
                  width: '100%',
                  background: '#22222608',
                  borderRadius: '22px',
                  height: '100%',
                }}
                px={isNarrow ? 2 : 0}
                key={solution.solutionId}
              >
                <Stack
                  direction={isNarrow ? 'column' : 'row'}
                  spacing={2}
                  alignItems="flex-start"
                  flex={1}
                  width={isNarrow ? '100%' : 'auto'}
                  pb={isNarrow ? 3 : 0}
                >
                  <Stack direction="row" spacing={2} alignItems="flex-start" px={3} flex={1} py={isNarrow ? 0 : 2}>
                    <Box width="20px" height="20px" display="flex" justifyContent="center" alignItems="center">
                      <HousePersonOutsideIcon />
                    </Box>
                    <Stack>
                      <Typography variant="body1Emphasis">{solution.name}</Typography>
                      <Typography variant="body2">{solution.address}</Typography>
                    </Stack>
                  </Stack>

                  <Stack
                    direction="row"
                    spacing={2}
                    alignItems="center"
                    width={isNarrow ? '100%' : '270px'}
                    py={isNarrow ? 0 : 2}
                    px={isNarrow ? 3 : 0}
                  >
                    <Box width="20px" height="20px" display="flex" justifyContent="center" alignItems="center">
                      {solution.accepted ? (
                        <CalendarCheckOutlinedIcon width="16px" height="16px" color="#53535A" />
                      ) : (
                        <CalendarOutlinedIcon width="16px" height="16px" color="#53535A" />
                      )}
                    </Box>
                    <Stack>
                      <Typography variant="body1Emphasis">
                        {intl.formatMessage({
                          id: solution.accepted
                            ? 'dashboard.filter.accepted'
                            : (`dashboard.solutionStatus.${solution.status}` as MessageKey),
                        })}
                      </Typography>
                    </Stack>
                  </Stack>
                </Stack>
                <Box
                  display="flex"
                  justifyContent="flex-end"
                  alignItems="center"
                  flexDirection={isNarrow ? 'column' : 'row'}
                  gap={2}
                  width={isNarrow ? '100%' : '270px'}
                  pl={isNarrow ? 2 : 0}
                  pr={2}
                >
                  <Box
                    flexDirection={isNarrow ? 'row' : 'column'}
                    display="flex"
                    justifyContent="space-between"
                    alignContent={isNarrow ? 'center' : 'flex-start'}
                    onClick={() => onQuotationClick(solution)}
                    sx={{
                      height: isNarrow ? '56px' : '112px',
                      width: isNarrow ? '100%' : '112px',
                      background: '#FEFEFF33',
                      borderRadius: isNarrow ? '12px' : '20px',
                      cursor: 'pointer',
                      paddingTop: isNarrow ? 2 : 0,
                    }}
                  >
                    <Box pl={2} pt={isNarrow ? 0 : 2}>
                      <Typography variant="body1Emphasis" component={Box}>
                        <FormattedMessage id="dashboard.button.quotation" defaultMessage="Quotation" />
                      </Typography>
                    </Box>
                    <Box sx={{ mx: 2, mb: 2 }}>
                      <ArrowOutlinedIcon />
                    </Box>
                  </Box>

                  <Box
                    flexDirection={isNarrow ? 'row' : 'column'}
                    display="flex"
                    justifyContent="space-between"
                    alignContent={isNarrow ? 'center' : 'flex-start'}
                    onClick={() => onInstallationProjectClick(solution)}
                    sx={{
                      height: isNarrow ? '56px' : '112px',
                      width: isNarrow ? '100%' : '112px',
                      background: '#FEFEFF33',
                      borderRadius: isNarrow ? '12px' : '20px',
                      cursor: 'pointer',
                      paddingTop: isNarrow ? 2 : 0,
                    }}
                  >
                    <Box pl={2} pt={isNarrow ? 0 : 2}>
                      <Typography variant="body1Emphasis">
                        <FormattedMessage
                          id="dashboard.button.installationProject"
                          defaultMessage="Installation project"
                        />
                      </Typography>
                    </Box>
                    <Box sx={{ mx: 2, mb: 2 }}>
                      <ArrowOutlinedIcon />
                    </Box>
                  </Box>
                </Box>
              </Stack>
            ))}
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
}
