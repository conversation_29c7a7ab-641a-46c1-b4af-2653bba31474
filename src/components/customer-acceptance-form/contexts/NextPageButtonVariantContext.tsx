import { createContext, useContext } from 'react';
import type { NextPageButtonVariant } from '../components/NextPageButton';

const NextPageButtonVariantContext = createContext<NextPageButtonVariant | null>(null);

export const useNextPageButtonVariantContext = () => {
  const variant = useContext(NextPageButtonVariantContext);
  if (!variant) {
    throw new Error('useNextPageButtonVariantContext must be used within a NextPageButtonVariantContextProvider');
  }
  return variant;
};

export const NextPageButtonVariantContextProvider = NextPageButtonVariantContext.Provider;

export default NextPageButtonVariantContext;
