import { createStore, useStore } from 'zustand';
import { createContext, useContext } from 'react';
import { FormResponseType, SignatureType, Signatures } from '../types';

interface FormStoreProps {
  responses: FormResponseType;
  signatures: Signatures;
}

interface FormStoreType extends FormStoreProps {
  actions: {
    setResponses: (responses: FormResponseType) => void;
    updateResponse: (questionSlug: string, value: string | number | boolean | string[] | undefined) => void;
    clearResponse: (questionSlug: string) => void;
    setSignature: (signatureType: SignatureType, signature: Signatures[SignatureType]) => void;
    resetSignatures: () => void;
  };
}

export const createFormStore = (initProps: Partial<FormStoreProps>) => {
  const defaultProps: FormStoreProps = {
    responses: {},
    signatures: {
      customer: undefined,
      installer: undefined,
    },
  };
  return createStore<FormStoreType>()((set) => ({
    ...defaultProps,
    ...initProps,
    actions: {
      updateResponse: (questionSlug, value) =>
        set((state) => ({
          responses: {
            ...state.responses,
            [questionSlug]: value,
          },
        })),
      clearResponse: (questionSlug) =>
        set(({ responses }) => ({
          responses: {
            ...responses,
            [questionSlug]: undefined,
          },
        })),
      setResponses: (responses) => set({ responses }),
      setSignature: (signatureType, signature) =>
        set((state) => ({
          signatures: {
            ...state.signatures,
            [signatureType]: signature,
          },
        })),
      resetSignatures: () => set({ signatures: defaultProps.signatures }),
    },
  }));
};

type FormStore = ReturnType<typeof createFormStore>;

const FormStoreContext = createContext<FormStore | null>(null);

export const FormStoreProvider = FormStoreContext.Provider;

export function useFormStore() {
  const store = useContext(FormStoreContext);
  if (!store) {
    throw new Error('useFormStore must be used within a FormStoreProvider');
  }
  return store;
}

export const useResponses = () => useStore(useFormStore(), (s) => s.responses);
export const useFormActions = () => useStore(useFormStore(), (s) => s.actions);
export const useSignatures = () => useStore(useFormStore(), (s) => s.signatures);
