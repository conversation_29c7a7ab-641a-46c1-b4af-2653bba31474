import { Box, Stack } from '@mui/material';
import Title from '../components/Title';
import { useResponses } from '../stores/FormStore';
import { NextPage } from '../types';
import { NextPageButton } from '../components/NextPageButton';

export default function ChecklistPage({
  children,
  nextPage,
  showNextButton = true,
  questionSlugs,
}: {
  children: React.ReactNode;
  nextPage: NextPage;
  showNextButton?: boolean;
  questionSlugs?: string[];
}) {
  const responses = useResponses();

  const handleNextPage = () => {
    nextPage({ responses });
  };

  const enableNextButton = questionSlugs?.every((slug) => responses[slug] !== undefined) ?? true;

  return (
    <>
      <Box px={5} pt={2}>
        <Title />
      </Box>
      <Stack spacing={5} pb={11} px={3}>
        <Stack spacing={2}>{children}</Stack>
        {showNextButton && <NextPageButton onClick={handleNextPage} disabled={!enableNextButton} />}
      </Stack>
    </>
  );
}
