import { Box, Slide, Stack, Step, <PERSON><PERSON><PERSON><PERSON>, Stepper, Typography } from '@mui/material';
import { AiraLogo } from '@ui/components/Branding/AiraLogo';
import { ArrowRightOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { PhoneOutlinedIcon } from '@ui/components/StandardIcons/PhoneOutlinedIcon';
import { beige, brandYellow, grey } from '@ui/theme/colors';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { FormattedMessage, useIntl } from 'react-intl';
import { CustomStepConnector, CustomStepIcon } from '../components/CustomStep';

function JourneyStep({ label, completed }: { label: string; completed: boolean }) {
  return (
    <Step key={label} completed={completed}>
      <StepLabel StepIconComponent={CustomStepIcon} sx={{ padding: 0 }}>
        <Typography variant="body1" sx={{ pl: '14px' }}>
          {label}
        </Typography>
      </StepLabel>
    </Step>
  );
}

function Journey({ accepted }: { accepted: boolean }) {
  const { formatMessage } = useIntl();
  return (
    <Stack spacing={5} mt={6}>
      <Typography variant="headline1">
        <FormattedMessage id="installationReport.confirmation.journey.header" defaultMessage="Your Aira Journey" />
      </Typography>
      <Box sx={{ mt: 7 }}>
        <Stepper orientation="vertical" connector={<CustomStepConnector />} sx={{ ml: '16px' }} activeStep={3}>
          <JourneyStep
            label={formatMessage({
              id: 'installationReport.confirmation.journey.homeEnergyAssessment',
              defaultMessage: 'Home energy assessment',
            })}
            completed
          />
          <JourneyStep
            label={formatMessage({
              id: 'installationReport.confirmation.journey.reviewAndAcceptQuote',
              defaultMessage: 'Review & accept quote',
            })}
            completed
          />
          <JourneyStep
            label={formatMessage({
              id: 'installationReport.confirmation.journey.technicalSurveyAndDesign',
              defaultMessage: 'Technical survey & design',
            })}
            completed
          />
          <JourneyStep
            label={formatMessage({
              id: 'installationReport.confirmation.journey.installation',
              defaultMessage: 'Installation',
            })}
            completed={accepted}
          />
        </Stepper>
      </Box>
    </Stack>
  );
}

export default function Confirmation({ accepted }: { accepted: boolean }) {
  const router = useRouter();
  const { solution: solutionId } = router.query;

  return (
    <Stack
      spacing={2}
      sx={{
        position: 'relative',
        background: beige[100],
        overflowX: 'hidden',
      }}
    >
      <Box>
        <Image
          src="/images/heatpump-background.png"
          sizes="(max-width: 900px) 100vw, 500px"
          alt="Heat pump background"
          layout="responsive"
          width={500}
          height={500 * 66}
          priority // for optimizing above-the-fold images
        />
        <Box
          sx={{
            position: 'absolute',
            top: 20,
            left: 24,
          }}
        >
          <AiraLogo color="#fff" height={40} width={92} />
        </Box>
      </Box>
      <Slide direction="up" in timeout={500}>
        <Stack
          sx={{
            background: beige[100],
            padding: 3,
            pt: 10,
            borderRadius: '44px 44px 0px 0px',
            position: 'relative',
            top: '-62px',
          }}
        >
          <Typography variant="headline1">
            <FormattedMessage id="installationReport.confirmation.title" defaultMessage="Thank you!" />
          </Typography>
          <Stack spacing={4} sx={{ background: '#22222608', borderRadius: '22px', padding: 3, mt: '64px' }}>
            <Typography variant="label" sx={{ fontSize: '24px' }}>
              {accepted ? (
                <FormattedMessage
                  id="installationReport.confirmation.nextSteps.accepted.heading"
                  defaultMessage="Your next steps"
                />
              ) : (
                <FormattedMessage
                  id="installationReport.confirmation.nextSteps.rejected.heading"
                  defaultMessage="What happens next?"
                />
              )}
            </Typography>

            <Stack direction="row" justifyContent="flex-start" alignItems="flex-start" spacing={2}>
              <PhoneOutlinedIcon height={24} width={24} />
              <Typography variant="body1" sx={{ color: grey[900] }}>
                {accepted ? (
                  <FormattedMessage
                    id="installationReport.confirmation.nextSteps.accepted.body"
                    defaultMessage="We have sent an email with a copy of the installation report."
                  />
                ) : (
                  <FormattedMessage
                    id="installationReport.confirmation.nextSteps.rejected.body"
                    defaultMessage="We will contact you soon about completing your installation."
                  />
                )}
              </Typography>
            </Stack>
          </Stack>

          <Journey accepted={accepted} />

          <Button
            component="a"
            href={`/solution/${solutionId}/installation-documentation`}
            variant="contained"
            sx={{ mt: 8, background: brandYellow[400], color: grey[900], borderRadius: '16px' }}
          >
            <FormattedMessage
              id="installationReport.confirmation.link.installationReport"
              defaultMessage="Go to installation report"
            />
            <ArrowRightOutlinedIcon height={24} width={24} style={{ marginLeft: 10 }} />
          </Button>
        </Stack>
      </Slide>
    </Stack>
  );
}
