import { Box, Stack, Typography } from '@mui/material';
import { ArrowSpinAnticlockwiseOutlinedIcon } from '@ui/components/StandardIcons/ArrowSpinAnticlockwiseOutlinedIcon';
import { PhoneMobileOutlinedIcon } from '@ui/components/StandardIcons/PhoneMobileOutlinedIcon';
import { brandYellow } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { useResponses } from '../stores/FormStore';
import { NextPage } from '../types';
import { NextPageButton } from '../components/NextPageButton';

export default function CustomerChecklistIntroPage({ nextPage }: { nextPage: NextPage }) {
  const responses = useResponses();
  const handleNextPage = () => {
    nextPage({ responses });
  };

  return (
    <Stack spacing={2} pt={4} pb={7} px={3} justifyContent="space-between" sx={{ flex: 1 }}>
      <Stack flexGrow={1} justifyContent="center">
        <Stack direction="row" alignItems="center" justifyContent="center" textAlign="center">
          <Typography variant="headline1" px={2} sx={{ fontSize: '34px !important' }}>
            <FormattedMessage
              id="installationReport.customerChecklistIntro.title"
              defaultMessage="Customer onboarding"
            />
          </Typography>
        </Stack>
        <Stack pt={10} px={2} spacing={7}>
          <Stack direction="row" spacing={3} alignItems="flex-start">
            <Box>
              <PhoneMobileOutlinedIcon height={34} width={34} color={brandYellow[500]} />
            </Box>
            <Stack spacing={1}>
              <Typography variant="body1Emphasis">
                <FormattedMessage
                  id="installationReport.customerChecklistIntro.shareScreen.heading"
                  defaultMessage="Share your screen"
                />
              </Typography>
              <Typography variant="body1">
                <FormattedMessage
                  id="installationReport.customerChecklistIntro.shareScreen.body"
                  defaultMessage="To complete this form, the customer must be present. Guide them through it, and remember that only they can give the final sign off."
                />
              </Typography>
            </Stack>
          </Stack>
          <Stack direction="row" spacing={3} alignItems="flex-start" width="100%">
            <Box>
              <ArrowSpinAnticlockwiseOutlinedIcon height={34} width={34} color={brandYellow[500]} />
            </Box>
            <Stack spacing={1}>
              <Typography variant="body1Emphasis">
                <FormattedMessage
                  id="installationReport.customerChecklistIntro.wantToKnowMore.heading"
                  defaultMessage="Want to know more?"
                />
              </Typography>
              <Typography variant="body1">
                <FormattedMessage
                  id="installationReport.customerChecklistIntro.wantToKnowMore.body"
                  defaultMessage="You can return to the technical sections if the customer wants details about their system or installation."
                />
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <NextPageButton onClick={handleNextPage} />
    </Stack>
  );
}
