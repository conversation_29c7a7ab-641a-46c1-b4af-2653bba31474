import { InstallationProjectAcceptanceQuestionnaire } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Box, Stack, Typography } from '@mui/material';
import { InstallationIcon } from '@ui/components/Icons/InstallationIcon/InstallationIcon';
import { ArrowRightOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightOutlinedIcon';
import { brandYellow, grey } from '@ui/theme/colors';
import Title from '../components/Title';
import { useFormActions, useResponses } from '../stores/FormStore';
import { FormResponseType, NextPage } from '../types';

function YesNoButton({
  onClick,
  state,
  label,
  'data-testid': dataTestId,
}: {
  onClick: (e: React.UIEvent<HTMLElement>) => void;
  state?: 'selected';
  label: string;
  'data-testid'?: string;
}) {
  return (
    <Box
      component="button"
      onClick={onClick}
      sx={{
        width: '100%',
        borderRadius: '22px',
        padding: '16px',
        background: 'transparent',
        color: '#222226',
        height: '88px',
        border: `1px solid ${grey[400]}`,
        '@media (hover: hover) and (pointer: fine)': {
          '&:hover': {
            background: brandYellow[400],
            border: `1px solid ${grey[400]}`,
          },
        },
        '@media (hover: none) and (pointer: coarse)': {
          '&:active': {
            background: brandYellow[400],
            border: `1px solid ${grey[400]}`,
          },
        },
        ...(state === 'selected' && {
          background: brandYellow[400],
          border: `1px solid ${grey[400]}`,
        }),
      }}
      data-testid={dataTestId}
    >
      <Stack direction="row" justifyContent="space-between" width="100%" alignItems="center" height="100%">
        <Typography variant="body1Emphasis" pl={1}>
          {label}
        </Typography>
        <Box pr={1} display="flex" alignItems="center">
          <ArrowRightOutlinedIcon height={20} width={20} />
        </Box>
      </Stack>
    </Box>
  );
}

export default function YesNoPage({
  form,
  slug,
  nextPage,
}: {
  form: InstallationProjectAcceptanceQuestionnaire;
  slug: string;
  nextPage: NextPage;
}) {
  const field = form.fields.find((f) => f.slug === slug);
  const label = field?.label;
  const fieldSlug = field?.slug;

  const responses = useResponses();
  const { setResponses } = useFormActions();
  const state = fieldSlug && responses[fieldSlug];

  if (field?.type?.$case !== 'yesOrNo') {
    return null;
  }
  const { yesLabel, noLabel } = field.type.yesOrNo;

  const handleClick = (value: boolean, e: React.UIEvent<HTMLElement>) => {
    if (!fieldSlug) {
      return;
    }

    const newResponses: FormResponseType = {
      ...responses,
      [fieldSlug]: value,
    };
    setResponses(newResponses);

    nextPage({ responses: newResponses });
    e.currentTarget.blur();
  };

  return (
    <Stack spacing={3} pt={3} px={3} data-testid={slug}>
      <Box px={2} py={0}>
        <Title />
        <Stack direction="row" alignItems="flex-start" spacing={1} pt={4}>
          <InstallationIcon height={24} width={24} />
          <Typography variant="body1">{label}</Typography>
        </Stack>
      </Box>
      <Stack spacing={2} width="100%">
        <YesNoButton
          data-testid={`${slug}-yes`}
          label={yesLabel}
          onClick={(evt) => handleClick(true, evt)}
          state={state === true ? 'selected' : undefined}
        />
        <YesNoButton
          data-testid={`${slug}-no`}
          label={noLabel}
          onClick={(evt) => handleClick(false, evt)}
          state={state === false ? 'selected' : undefined}
        />
      </Stack>
    </Stack>
  );
}
