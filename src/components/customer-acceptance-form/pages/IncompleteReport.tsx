import { List, ListItem, Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useResponses } from '../stores/FormStore';
import { FormResponseType, Questionnaire, QuestionnaireField } from '../types';
import { NextPageButton } from '../components/NextPageButton';

const INPUT_SURFACE_BGCOLOR = '#22222608';

function Value({ field, value }: { field: QuestionnaireField; value: FormResponseType[string] }) {
  const variant = 'body2Emphasis';
  switch (field.type?.$case) {
    case 'yesOrNo':
      return (
        <Typography variant={variant}>{value ? field.type.yesOrNo.yesLabel : field.type.yesOrNo.noLabel}</Typography>
      );
    case 'multipleChoice': {
      const { options } = field.type.multipleChoice;
      if (!Array.isArray(value)) {
        return null;
      }
      return (
        <List disablePadding>
          {value.map((v) => {
            const option = options.find((o) => o.slug === v);
            return (
              <ListItem disablePadding key={v}>
                <Typography variant={variant}>{option?.label}</Typography>
              </ListItem>
            );
          })}
        </List>
      );
    }
    case 'singleChoice': {
      const { options } = field.type.singleChoice;
      const option = options.find((o) => o.id?.value === value);
      return <Typography variant={variant}>{option?.label}</Typography>;
    }
    case 'text':
    case 'number':
      return <Typography variant={variant}>{value}</Typography>;
    default:
      return null;
  }
}

export default function IncompleteReport({ onClickSign, form }: { onClickSign: () => void; form: Questionnaire }) {
  const responses = useResponses();

  const categoriesToShow = ['system-components-installed', 'system-components-delivered', 'work-completed'];

  return (
    <Stack pt={3} pb={10} px={3} spacing={4} minHeight="calc(100dvh - 107px)">
      <Typography px={2} variant="headline1" fontSize="24px !important">
        <FormattedMessage id="installationReport.incomplete.title" defaultMessage="Installation incomplete report" />
      </Typography>

      <Stack spacing={3} sx={{ flex: 1 }}>
        {form.categories
          .filter((category) => categoriesToShow.includes(category.slug))
          .map((category) => {
            const fields = form.fields.filter(
              (field) => field.categoryId?.value === category.id?.value && responses[field.slug] !== undefined,
            );
            if (fields.length === 0) {
              return null;
            }
            const mainField = form.fields.find((field) => field.slug === category.slug);
            return (
              <Stack
                key={category.id?.value}
                sx={{ backgroundColor: INPUT_SURFACE_BGCOLOR, p: 3, borderRadius: 2 }}
                spacing={2}
              >
                <Stack direction="row" justifyContent="space-between" gap={4}>
                  <Typography variant="body1">{category.label}</Typography>
                  <Typography variant="body1Emphasis">
                    {mainField?.type?.$case === 'yesOrNo' &&
                      responses[mainField.slug] !== undefined &&
                      (responses[mainField.slug] ? mainField.type.yesOrNo.yesLabel : mainField.type.yesOrNo.noLabel)}
                  </Typography>
                </Stack>
                <>
                  {fields
                    .filter((field) => field.slug !== mainField?.slug)
                    .map((field) => (
                      <Stack spacing={1} key={field.id?.value}>
                        <Typography variant="body2">{field.label}</Typography>
                        <Value field={field} value={responses[field.slug]} />
                      </Stack>
                    ))}
                </>
              </Stack>
            );
          })}
      </Stack>
      <NextPageButton onClick={onClickSign} />
    </Stack>
  );
}
