import { Stack, Typography } from '@mui/material';
import { useResponses } from '../stores/FormStore';
import { FormResponseType, NextPage } from '../types';
import { NextPageButton } from '../components/NextPageButton';

export default function ReasonsPage({
  nextPage,
  label,
  children,
  responseValidator,
}: {
  nextPage: NextPage;
  label: string;
  children: any;
  responseValidator?: (responses: FormResponseType) => boolean;
}) {
  const responses = useResponses();

  const handleNextPage = () => {
    nextPage({ responses });
  };

  const enableNextButton = responseValidator ? responseValidator(responses) : true;

  return (
    <Stack spacing={5} pt={4} pb={7} px={3}>
      <Typography variant="headline1">{label}</Typography>
      {children}
      <NextPageButton onClick={handleNextPage} disabled={!enableNextButton} />
    </Stack>
  );
}
