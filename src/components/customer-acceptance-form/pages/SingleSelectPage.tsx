import { Box, Stack, Typography } from '@mui/material';
import { ArrowRightOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightOutlinedIcon';
import { InstallationIcon } from '@ui/components/Icons/InstallationIcon/InstallationIcon';
import { brandYellow, grey } from '@ui/theme/colors';
import { InstallationProjectAcceptanceQuestionnaire } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useFormActions, useResponses } from '../stores/FormStore';
import Title from '../components/Title';
import { FormResponseType, NextPage } from '../types';

function Button({
  onClick,
  label,
  state,
}: {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label: string;
  state?: 'selected';
}) {
  return (
    <Box
      component="button"
      onClick={onClick}
      sx={{
        width: '100%',
        borderRadius: '22px',
        padding: '16px',
        background: 'transparent',
        color: '#222226',
        height: '88px',
        border: `1px solid ${grey[400]}`,
        '@media (hover: hover) and (pointer: fine)': {
          '&:hover': {
            background: brandYellow[400],
            border: `1px solid ${grey[400]}`,
          },
        },
        '@media (hover: none) and (pointer: coarse)': {
          '&:active': {
            background: brandYellow[400],
            border: `1px solid ${grey[400]}`,
          },
        },
        ...(state === 'selected' && {
          background: brandYellow[400],
          border: `1px solid ${grey[400]}`,
        }),
      }}
    >
      <Stack direction="row" justifyContent="space-between" width="100%" alignItems="center" height="100%">
        <Typography variant="body1Emphasis" pl={1}>
          {label}
        </Typography>
        <Box pr={1} display="flex" alignItems="center">
          <ArrowRightOutlinedIcon height={20} width={20} />
        </Box>
      </Stack>
    </Box>
  );
}

export default function SingleSelectPage({
  form,
  slug,
  nextPage,
}: {
  form: InstallationProjectAcceptanceQuestionnaire;
  slug: string;
  nextPage: NextPage;
}) {
  const field = form.fields.find((f) => f.slug === slug);
  const label = field?.label;
  const responses = useResponses();
  const { setResponses } = useFormActions();

  if (field?.type?.$case !== 'singleChoice') {
    throw new Error(`Invalid field type for SingleSelectPage: ${field?.type?.$case}`);
  }
  const type = field.type.singleChoice;

  const handleClick = (optionId: string, e: React.MouseEvent<HTMLButtonElement>) => {
    const newResponses: FormResponseType = { ...responses };

    newResponses[slug] = optionId;

    setResponses(newResponses);

    nextPage({ responses: newResponses });
    e.currentTarget.blur();
  };
  return (
    <Stack spacing={3} pt={3} px={3}>
      <Box px={2} py={0}>
        <Title />
        <Stack direction="row" alignItems="flex-start" spacing={1} pt={4}>
          <InstallationIcon height={24} width={24} />
          <Typography variant="body1">{label}</Typography>
        </Stack>
      </Box>
      <Stack spacing={2} width="100%">
        {type.options.map((option) => (
          <Button
            key={option.id!.value}
            label={option.label}
            onClick={(event) => handleClick(option.id!.value, event)}
            state={responses[slug] === option.id!.value ? 'selected' : undefined}
          />
        ))}
      </Stack>
    </Stack>
  );
}
