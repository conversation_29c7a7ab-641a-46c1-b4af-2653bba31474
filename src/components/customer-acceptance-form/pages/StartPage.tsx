import { Box, List, ListItem, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { useEnergySolution } from 'context/energy-solution-context';
import { useInstallationProject } from 'context/installation-project-context';
import { FormattedMessage } from 'react-intl';
import Products from '../components/Products';
import Title from '../components/Title';
import { useResponses } from '../stores/FormStore';
import { NextPage } from '../types';
import { brandYellow } from '@ui/theme/colors';
import { LockOutlinedIcon } from '@ui/components/StandardIcons/LockOutlinedIcon';

export default function StartPage({ nextPage, locked }: { nextPage: NextPage; locked: boolean }) {
  const installationProject = useInstallationProject();
  const energySolution = useEnergySolution();
  const products = energySolution.energySolution.solution?.presentation?.products ?? [];

  const installerJob = installationProject?.jobs.find((job) => job.requiredRole === 1);
  const installers = installationProject?.assignedResourcesDetails.filter((identity) =>
    installerJob?.assignedResources.some((resource) => resource.userId?.value === identity.userId?.value),
  );
  const responses = useResponses();

  const handleNextPage = () => {
    nextPage({ responses });
  };

  return (
    <Stack spacing={3} pb={7} px={3}>
      <Box px={2} pt={2}>
        <Title />
      </Box>
      <Products products={products} />
      <Stack spacing={1} sx={{ background: '#22222608', borderRadius: '22px', p: '24px' }}>
        <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={2}>
          <PersonOutlinedIcon height={16} width={16} />
          <Typography variant="body1">
            <FormattedMessage id="installationReport.startPage.installers.title" defaultMessage="Installers" />
          </Typography>
        </Stack>
        <List sx={{ listStyle: 'none', display: 'flex', gap: 1, flexDirection: 'column' }}>
          {installers?.map((identity) => (
            <ListItem key={identity.userId?.value} sx={{ p: 0 }}>
              <Typography variant="body2">
                {identity.firstName} {identity.lastName}
              </Typography>
            </ListItem>
          ))}
        </List>
      </Stack>

      {locked ? (
        <Stack direction="row" spacing={3} alignItems="flex-start">
          <Box>
            <LockOutlinedIcon height={32} width={32} color={brandYellow[500]} />
          </Box>
          <Stack spacing={1}>
            <Typography variant="body1Emphasis">
              <FormattedMessage id="installationReport.startPage.locked.title" defaultMessage="Locked" />
            </Typography>
            <Typography variant="body1">
              <FormattedMessage
                id="installationReport.startPage.locked.body"
                defaultMessage="An accepted report has already been submitted for this customer."
              />
            </Typography>
          </Stack>
        </Stack>
      ) : null}
      <Button onClick={handleNextPage} data-testid="start-button" disabled={locked}>
        <FormattedMessage id="installationReport.startPage.start" defaultMessage="Start" />
      </Button>
    </Stack>
  );
}
