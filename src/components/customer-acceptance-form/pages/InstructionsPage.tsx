import { Box, Stack, Typography } from '@mui/material';
import { DeviceWaveOutlinedIcon } from '@ui/components/Icons/DeviceWaveOutlinedIcon/DeviceWaveOutlinedIcon';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { brandYellow } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { useResponses } from '../stores/FormStore';
import { NextPage } from '../types';
import { NextPageButton } from '../components/NextPageButton';

export default function InstructionsPage({ nextPage }: { nextPage: NextPage }) {
  const responses = useResponses();

  const handleNextPage = () => {
    nextPage({ responses });
  };

  return (
    <Stack spacing={7} pt={4} pb={7} px={3} justifyContent="space-between" sx={{ flex: 1 }}>
      <Stack flexGrow={1} justifyContent="center">
        <Stack direction="row" justifyContent="center" textAlign="center">
          <Typography variant="headline1" sx={{ fontSize: '34px !important' }}>
            <FormattedMessage id="installationReport.instructions.title" defaultMessage="Installation complete" />
          </Typography>
        </Stack>
        <Typography variant="body1" textAlign="center" px={2} mt={2}>
          <FormattedMessage
            id="installationReport.instructions.intro"
            defaultMessage="Complete these checklists after the heat pump installation has been completed. There are two parts:"
          />
        </Typography>
        <Stack pt={8} px={2} spacing={7}>
          <Stack direction="row" spacing={3} alignItems="flex-start">
            <Box>
              <DeviceWaveOutlinedIcon height={32} width={32} color={brandYellow[500]} />
            </Box>
            <Stack spacing={1}>
              <Typography variant="body1Emphasis">
                <FormattedMessage
                  id="installationReport.instructions.checklists.heading"
                  defaultMessage="Installation & system checklists"
                />
              </Typography>
              <Typography variant="body1">
                <FormattedMessage
                  id="installationReport.instructions.checklists.body"
                  defaultMessage="A set of technical questions about the system and its installation. You can complete these on your own."
                />
              </Typography>
            </Stack>
          </Stack>
          <Stack direction="row" spacing={3} alignItems="flex-start" width="100%">
            <Box>
              <HousePersonOutsideIcon height={32} width={32} color={brandYellow[500]} />
            </Box>
            <Stack spacing={1}>
              <Typography variant="body1Emphasis">
                <FormattedMessage
                  id="installationReport.instructions.onboarding.heading"
                  defaultMessage="Customer onboarding"
                />
              </Typography>
              <Typography variant="body1">
                <FormattedMessage
                  id="installationReport.instructions.onboarding.body"
                  defaultMessage="Onboard the customer on how to use their heat pump. For this part, the customer needs to be present."
                />
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
      <NextPageButton onClick={handleNextPage} />
    </Stack>
  );
}
