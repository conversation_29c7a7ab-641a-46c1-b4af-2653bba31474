import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { api } from 'utils/api';
import { useInstallationProject } from 'context/installation-project-context';
import { useRef } from 'react';
import ComissioningQuestionnaire from './ComissioningQuestionnaire';
import { createFormStore, FormStoreProvider } from './stores/FormStore';
import { toInitialFormState } from './form-state-util';
import { Questionnaire } from './types';

function FormStoreWrapper({ children, questionnaire }: { children: React.ReactNode; questionnaire: Questionnaire }) {
  const storeRef = useRef(
    createFormStore({
      responses: toInitialFormState(questionnaire),
    }),
  );

  return <FormStoreProvider value={storeRef.current}>{children}</FormStoreProvider>;
}

export default function CustomerAcceptanceContainer() {
  const project = useInstallationProject();
  const { data: questionnaire, isLoading: isLoadingForm } =
    api.InstallationProject.resolveAcceptanceQuestionnaire.useQuery(
      {
        projectId: project!.id!.value,
        version: 14,
      },
      {
        enabled: !!project?.id?.value,
      },
    );

  if (isLoadingForm) {
    return <HeatPumpLoader />;
  }

  if (!questionnaire) {
    return null;
  }

  return (
    <FormStoreWrapper questionnaire={questionnaire}>
      <ComissioningQuestionnaire form={questionnaire} />
    </FormStoreWrapper>
  );
}
