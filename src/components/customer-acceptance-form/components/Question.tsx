import {
  InstallationProjectAcceptanceQuestionnaire,
  InstallationProjectAcceptanceQuestionnaire_Field_FieldTypeYesOrNo_YesOrNoDisplayType as YesOrNoDisplayType,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Box, TextField as MuiTextField, Stack, Typography } from '@mui/material';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { grey } from '@ui/theme/colors';
import { useMemo } from 'react';
import { useIntl } from 'react-intl';
import { useFormActions, useResponses } from '../stores/FormStore';
import { FormResponseType } from '../types';
import NumericQuestion from './NumericQuestion/NumericQuestion';

const INPUT_SURFACE_BGCOLOR = '#22222608';

export default function Question({
  slug,
  form,
  showLabel = true,
  onChange,
}: {
  slug: string;
  form: InstallationProjectAcceptanceQuestionnaire;
  showLabel?: boolean;
  onChange?: (value: FormResponseType['string']) => void;
}) {
  const { formatMessage } = useIntl();
  const { updateResponse } = useFormActions();
  const responses = useResponses();

  const question = useMemo(() => form.fields.find((field) => field.slug === slug), [form, slug]);
  if (!question) {
    return null;
  }
  const { label, type } = question;
  if (!type) {
    return null;
  }

  const handleChange = (value: FormResponseType['string']) => {
    updateResponse(slug, value);
    onChange?.(value);
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => handleChange(e.target.value);

  const handleCheckboxChange = (checked: boolean) => handleChange(checked ? true : undefined);

  const handleSingleChoiceChange = (optionId: string | undefined, checked: boolean) => {
    if (!optionId) {
      return;
    }
    handleChange(checked ? optionId : undefined);
  };

  const handleMultipleChoiceChange = (optionId: string | undefined, checked: boolean) => {
    const currentValue = responses[slug];
    if (!optionId || !Array.isArray(currentValue)) {
      return;
    }
    handleChange(checked ? [...currentValue, optionId] : currentValue.filter((value) => value !== optionId));
  };

  const renderInput = () => {
    switch (type.$case) {
      case 'number': {
        return (
          <NumericQuestion
            slug={slug}
            value={responses[slug] as number}
            label={label}
            type={type.number}
            onChange={handleChange}
            bgColor={INPUT_SURFACE_BGCOLOR}
          />
        );
      }
      case 'yesOrNo':
        if (type.yesOrNo.displayAs === YesOrNoDisplayType.YES_OR_NO_DISPLAY_TYPE_TOGGLE)
          return (
            <Box
              sx={{
                background: INPUT_SURFACE_BGCOLOR,
                borderRadius: '22px',
                padding: '16px',
              }}
              onClick={() => handleCheckboxChange(!responses[slug] as boolean)}
              data-testid={question.slug}
            >
              <Checkbox
                label={<Typography variant="body2Emphasis">{type.yesOrNo.yesLabel}</Typography>}
                onChange={handleCheckboxChange}
                checked={responses[slug] as boolean}
                data-testid={`${question.slug}-yes`}
              />
            </Box>
          );
        return (
          <Stack direction="column" borderRadius="22px" overflow="hidden" data-testid={question.slug}>
            <Box mx={1}>
              <Checkbox
                labelSx={{
                  background: INPUT_SURFACE_BGCOLOR,
                  padding: '16px',
                  borderBottom: `1px solid ${grey[200]}`,
                }}
                label={<Typography variant="body2Emphasis">{type.yesOrNo.yesLabel}</Typography>}
                onChange={(checked) => handleChange(checked ? true : undefined)}
                checked={responses[slug] === true}
                data-testid={`${question.slug}-yes`}
              />
            </Box>
            <Box mx={1}>
              <Checkbox
                labelSx={{
                  background: INPUT_SURFACE_BGCOLOR,
                  padding: '16px',
                }}
                label={<Typography variant="body2Emphasis">{type.yesOrNo.noLabel}</Typography>}
                onChange={(checked) => handleChange(checked ? false : undefined)}
                checked={responses[slug] === false}
                data-testid={`${question.slug}-no`}
              />
            </Box>
          </Stack>
        );
      case 'singleChoice':
        return (
          <Stack direction="column" borderRadius="22px" overflow="hidden" data-testid={question.slug}>
            {type.singleChoice?.options.map((option, idx) => (
              <Box mx={1} key={option.id?.value}>
                <Checkbox
                  labelSx={{
                    background: INPUT_SURFACE_BGCOLOR,
                    padding: '16px',
                    borderBottom: idx < type.singleChoice.options.length - 1 ? `1px solid ${grey[200]}` : undefined,
                  }}
                  label={<Typography variant="body2Emphasis">{option.label}</Typography>}
                  onChange={(checked) => handleSingleChoiceChange(option.id?.value, checked)}
                  checked={responses[slug] === option.id?.value}
                  data-testid={`${question.slug}-${option.slug}`}
                />
              </Box>
            ))}
          </Stack>
        );
      case 'multipleChoice':
        return (
          <Box data-testid={question.slug}>
            {type.multipleChoice?.options.map((option) => (
              <Checkbox
                key={option.id?.value}
                label={option.label}
                onChange={(checked) => handleMultipleChoiceChange(option.id?.value, checked)}
                checked={responses[slug] as boolean}
                data-testid={`${question.slug}-${option.slug}`}
              />
            ))}
          </Box>
        );
      case 'text':
        return (
          <MuiTextField
            name={label}
            value={responses[slug] as string}
            onChange={handleTextChange}
            label=""
            placeholder={
              type.text.placeholder ??
              formatMessage({ id: 'installationReport.textInput.placeholder', defaultMessage: 'Type here' })
            }
            inputProps={{
              'data-testid': `${question.slug}-input`,
            }}
            multiline
            minRows={1}
            data-testid={question.slug}
            sx={{
              borderRadius: '8px',
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
              },
              '& .MuiInputBase-root': {
                background: INPUT_SURFACE_BGCOLOR,
              },
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Stack
      spacing={2}
      sx={{
        background: INPUT_SURFACE_BGCOLOR,
        borderRadius: '22px',
        padding: '24px',
      }}
      data-testid={`${question.slug}-container`}
    >
      {showLabel && (
        <Typography variant="body2" sx={{ px: '16px' }} data-testid={`${question.slug}-label`}>
          {label}
        </Typography>
      )}
      {renderInput()}
    </Stack>
  );
}
