import { InstallationProjectAcceptanceQuestionnaire } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { TextField as MuiTextField, Stack } from '@mui/material';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { useIntl } from 'react-intl';
import { useFormActions, useResponses } from '../stores/FormStore';

export default function ReasonsQuestion({
  slug,
  form,
  parentOptionSlug,
  dependentQuestionSlug,
}: {
  slug: string;
  form: InstallationProjectAcceptanceQuestionnaire;
  parentOptionSlug?: string; // If question is multiple choice and unselecting one of the options should clear the dependent question response then we need this two props
  dependentQuestionSlug?: string;
}) {
  const { formatMessage } = useIntl();
  const { updateResponse } = useFormActions();
  const responses = useResponses();
  const question = form.fields.find((field) => field.slug === slug);
  if (!question) {
    throw new Error(`Question with slug ${slug} not found`);
  }
  const { label, type } = question;
  if (!type) {
    throw new Error(`Question with slug ${slug} has no type`);
  }

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateResponse(slug, e.target.value);
  };

  const handleMultipleChoiceChange = (optionSlug: string, checked: boolean) => {
    const currentValue = (responses[slug] ?? []) as string[];
    let newValues: string[] | undefined;

    if (checked) {
      newValues = [...currentValue, optionSlug];
    } else {
      const filtered = currentValue.filter((value) => value !== optionSlug);
      newValues = filtered.length > 0 ? filtered : undefined;
    }

    updateResponse(slug, newValues ?? []);

    if (parentOptionSlug === optionSlug && dependentQuestionSlug && !checked) {
      updateResponse(dependentQuestionSlug, undefined);
    }
  };

  const renderInput = () => {
    switch (type.$case) {
      case 'multipleChoice':
        return (
          <Stack spacing={2}>
            {type.multipleChoice?.options.map((option) => (
              <Stack
                spacing={2}
                key={option.slug}
                sx={{
                  background: '#22222608',
                  borderRadius: '22px',
                  padding: '16px',
                }}
              >
                <Checkbox
                  labelSx={{
                    background: '#22222608',
                    borderRadius: '22px',
                    padding: '16px',
                  }}
                  label={option.label}
                  onChange={(checked) => handleMultipleChoiceChange(option.slug, checked)}
                  checked={(responses[slug] as string[])?.includes(option.slug)}
                  data-testid={`${question.slug}-${option.slug}`}
                />
              </Stack>
            ))}
          </Stack>
        );
      case 'text':
        return (
          <MuiTextField
            name={label}
            value={responses[slug] as string}
            onChange={handleTextChange}
            label=""
            placeholder={formatMessage({ id: 'installationReport.textInput.placeholder', defaultMessage: 'Type here' })}
            multiline
            minRows={5}
            data-testid={question.slug}
            sx={{
              borderRadius: '8px',
              '& .MuiOutlinedInput-notchedOutline': {
                border: 'none',
                background: '#22222608',
              },
              '& .MuiInputBase-root': {
                background: '#22222608',
                border: 'none',
                outline: 'none',
              },
              '&. MuiInputBase-inputMultiline': {
                outline: 'none',
              },
            }}
          />
        );
      default:
        return null;
    }
  };

  return <Stack spacing={2}>{renderInput()}</Stack>;
}
