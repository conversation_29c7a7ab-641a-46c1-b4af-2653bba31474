import { Button } from '@ui/components/Button/Button';
import { FormattedMessage } from 'react-intl';
import { useNextPageButtonVariantContext } from '../contexts/NextPageButtonVariantContext';

export type NextPageButtonVariant = 'sign' | 'next';

export function NextPageButton({ onClick, disabled }: { onClick: () => void; disabled?: boolean }) {
  const variant = useNextPageButtonVariantContext();
  return (
    <Button
      variant="contained"
      color="yellow"
      onClick={onClick}
      disabled={disabled}
      fullWidth
      data-testid={`${variant}-button`}
    >
      {variant === 'next' && <FormattedMessage id="common.label.next" />}
      {variant === 'sign' && <FormattedMessage id="installationReport.button.sign" />}
    </Button>
  );
}
