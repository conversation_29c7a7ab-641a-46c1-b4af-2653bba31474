import { EnergySolutionProduct } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { InstallationIcon } from '@ui/components/Icons/InstallationIcon/InstallationIcon';
import { grey } from '@ui/theme/colors';
import { MessageKey } from 'messageType';
import { useRouter } from 'next/router';
import { FormattedMessage } from 'react-intl';

function ProductListItem({
  messageKey,
  children,
}: {
  messageKey: MessageKey;
  children: React.ReactNode | React.ReactNode[];
}) {
  return (
    <Box minWidth="100px">
      <Typography variant="body2" sx={{ color: grey[600], pb: '4px' }}>
        <FormattedMessage id={messageKey} />
      </Typography>
      <Typography variant="body2">{children}</Typography>
    </Box>
  );
}

export default function Products({ products }: { products: EnergySolutionProduct[] }) {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const router = useRouter();
  const { solution: solutionId } = router.query;
  const radiators = products.filter(
    (product) =>
      product.productDetails?.details?.$case === 'addon' &&
      product.productDetails?.details?.addon?.category === 'RADIATOR',
  );
  const radiatorsQuantity = radiators?.reduce((acc, radiator) => acc + radiator.quantity, 0);

  const heatPumpOutdoorUnit = products.find(
    (product) => product.productDetails?.details?.$case === 'heatPumpOutdoorUnit',
  );

  const bufferTank = products.find(
    (product) =>
      product.productDetails?.details?.$case === 'installationOption' &&
      product.productDetails?.details?.installationOption?.category === 'BUFFER_TANK',
  );

  const heatPumpIndoorUnit = products.find(
    (product) => product.productDetails?.details?.$case === 'heatPumpIndoorUnit',
  );

  return (
    <Stack
      spacing={2}
      sx={{
        background: '#22222608',
        borderRadius: '22px',
        padding: '24px',
        flex: 4,
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={2}>
        <InstallationIcon height={16} width={16} />
        <Typography variant="label">
          <FormattedMessage id="installationReport.products.title" defaultMessage="Products installed" />
        </Typography>
      </Stack>
      <Box
        sx={{
          display: isMobile ? 'flex' : 'grid',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '16px',
          gridTemplateColumns: 'repeat(auto-fill, minmax(160px, 1fr))',
        }}
      >
        {heatPumpOutdoorUnit && (
          <ProductListItem messageKey="installationReport.products.outdoorUnit">
            {heatPumpOutdoorUnit.compatibilityGroup?.name} - {heatPumpOutdoorUnit.displayName}
          </ProductListItem>
        )}
        {heatPumpIndoorUnit && (
          <ProductListItem messageKey="installationReport.products.indoorUnit">
            {heatPumpIndoorUnit.compatibilityGroup?.name} - {heatPumpIndoorUnit.displayName}
          </ProductListItem>
        )}
        {bufferTank && (
          <ProductListItem messageKey="installationReport.products.bufferTank">
            {bufferTank.displayName}
          </ProductListItem>
        )}
        <ProductListItem messageKey="installationReport.products.radiators">{radiatorsQuantity}</ProductListItem>
        <ProductListItem messageKey="installationReport.products.solutionId">{solutionId}</ProductListItem>
      </Box>
    </Stack>
  );
}
