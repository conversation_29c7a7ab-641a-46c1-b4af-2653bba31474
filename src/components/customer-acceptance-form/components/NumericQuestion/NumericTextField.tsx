import { Stack } from '@mui/material';
import type { ITextField } from '@ui/components/TextField/TextField';
import { TextField } from '@ui/components/TextField/TextField';
import { useState } from 'react';
import { useIntl } from 'react-intl';

export interface INumericTextField extends Omit<ITextField, 'onChange' | 'skipNumericalRangeCheck' | 'onBlur'> {
  value: number;
  onChange: (newValue: number, rawValue: string) => void;
  onBlur?: (newValue: number) => void;
  showRangeLabel?: boolean;
}

function toOneDecimalPlace(num: number): number {
  return Math.round(num * 10) / 10;
}

/**
 * This is a wrapper around TextField that provides ergonomic handling of numeric inputs.
 */
export function NumericTextField({ value, onChange, showRangeLabel = false, ...props }: INumericTextField) {
  const intl = useIntl();
  // `state` is a string to ensure that intermediate states of the text input (such as the empty input) can be represented
  const [state, setState] = useState<string>(value?.toString() ?? '');
  const { min, max, fullWidth, onBlur } = props;

  const handleChange = (e: any) => {
    const newValue = e.target.value;
    setState(newValue);
    onChange(Number(newValue), newValue);

    // TODO if the input has a non-number value like "123abc", the value of
    // e.target.value will be an empty string! This is the default behavior of
    // <input type="number">. This means that Number(newValue) will return 0.

    // This can result in the following interaction, which is confusing for user:
    // 1. enter a value: 123
    // 2. add a letter:  123x
    // -> the input still displays "123x"
    // -> the actual value (which we probably save) is 0, but the user doesn't know that
  };

  const handleBlur = (e?: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!e) {
      return;
    }
    let newValue = Number(e.target.value);
    if (min !== undefined && newValue < min) {
      const minStr = min.toString();
      setState(minStr);
      onChange(min, minStr);
      newValue = min;
    } else if (max !== undefined && newValue > max) {
      const maxStr = max.toString();
      setState(maxStr);
      onChange(max, maxStr);
      newValue = max;
    }

    if (onBlur) {
      onBlur(newValue);
    }
  };

  // Ensure that external changes to the prop value are reflected in the state
  if (value && value !== Number(state)) {
    setState(value?.toString() ?? '');
  }

  const rangeLabel = () => {
    const parts = [];
    if (min !== undefined)
      parts.push(`${intl.formatMessage({ id: 'common.min', defaultMessage: 'Min' })}: ${toOneDecimalPlace(min)}.`);
    if (max !== undefined)
      parts.push(`${intl.formatMessage({ id: 'common.max', defaultMessage: 'Max' })}: ${toOneDecimalPlace(max)}.`);
    return parts.join(' ');
  };

  return (
    <Stack direction="column" {...(fullWidth && { width: '100%' })}>
      <TextField
        type="number"
        {...props}
        skipNumericalRangeCheck
        onBlur={handleBlur}
        value={state}
        onChange={handleChange}
        {...(showRangeLabel && { helperText: rangeLabel() })}
      />
    </Stack>
  );
}
