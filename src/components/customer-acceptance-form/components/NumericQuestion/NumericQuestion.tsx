import { InstallationProjectAcceptanceQuestionnaire_Field_FieldTypeNumber } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { NumericTextField } from './NumericTextField';

export default function NumericQuestion({
  slug,
  value,
  label,
  type,
  onChange,
  bgColor,
}: {
  slug: string;
  value: number;
  label: string;
  type: InstallationProjectAcceptanceQuestionnaire_Field_FieldTypeNumber;
  onChange: (value: number | undefined) => void;
  bgColor: string;
}) {
  const { formatMessage } = useIntl();
  const [error, setError] = useState<string | undefined>();

  const onValueChange = (newValue: number, rawValue: string) => {
    if (!rawValue.trim()) {
      // If the value is empty, we clear errors and set the value to undefined
      setError(undefined);
      onChange(undefined);
      return;
    }

    const validationError = validate(newValue, type.minValue, type.maxValue);
    setError(validationError);
    onChange(validationError ? undefined : newValue);
  };

  // TODO: Move this to a utility function to make it more reusable and testable
  const validate = (value: number, minValue?: number, maxValue?: number): string | undefined => {
    if (isNaN(value)) {
      return `Value must be a number (${value})`;
    }

    if (minValue !== undefined && maxValue !== undefined && (value < minValue || value > maxValue)) {
      return formatMessage(
        { id: 'installationReport.question.validation.greaterThanAndLessThan' },
        { minValue, maxValue },
      );
    }

    if (minValue !== undefined && value < minValue) {
      return formatMessage({ id: 'installationReport.question.validation.greaterThan' }, { minValue });
    }

    if (maxValue !== undefined && value > maxValue) {
      return formatMessage({ id: 'installationReport.question.validation.lessThan' }, { maxValue });
    }
  };

  return (
    <NumericTextField
      name={label}
      suffix={type.unit}
      error={error !== undefined}
      errorText={error}
      value={value}
      onChange={onValueChange}
      data-testid={slug}
      sx={{
        background: bgColor,
        borderRadius: '22px',
        pr: '20px',
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none',
        },
      }}
    />
  );
}
