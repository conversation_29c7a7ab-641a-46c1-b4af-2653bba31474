import styled from '@emotion/styled';
import { StepConnector, stepConnectorClasses, StepIconProps } from '@mui/material';
import { Box } from '@mui/system';
import { CheckIconThin } from '@ui/components/Icons/Check/CheckIconThin';
import { brandYellow, grey } from '@ui/theme/colors';

// Custom Step Connector to make lines touch circles
export const CustomStepConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 0,
    left: 'calc(-50% + 16px)',
    right: 'calc(50% + 16px)',
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: brandYellow[400], // Orange color for lines
    borderWidth: '3px',
    height: '48px', // Adjust to align with icons
    width: '3px',
  },
  [`&.${stepConnectorClasses.vertical}`]: {
    marginLeft: 12, // Adjust to align with icons
  },
}));

// Custom icon component for the checkmark
export function CustomStepIcon(props: StepIconProps) {
  const { completed } = props;
  return (
    <Box
      sx={{
        backgroundColor: completed ? brandYellow[400] : 'transparent',
        width: '28px',
        height: '28px',
        border: `3px solid ${brandYellow[400]}`,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      {completed && <CheckIconThin height="18px" width="18px" color={grey[800]} />}
    </Box>
  );
}
