import { Box, IconButton, Stack } from '@mui/material';
import { ChevronLeft } from '@ui/components/Icons/Chevron/Chevron';
import NameButton from 'components/layout/infobar/NameButton';
import { beige } from '@ui/theme/colors';

export default function TopRow({
  previousPage,
  hideNameButton,
  hideBackButton,
  hide,
}: {
  previousPage?: () => void;
  hideNameButton?: boolean;
  hideBackButton?: boolean;
  hide: boolean;
}) {
  const handlePreviousPage = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (previousPage) {
      previousPage();
    }
    e.currentTarget.blur();
  };

  return (
    <Stack
      sx={{
        display: hide ? 'none' : 'block',
        position: 'sticky',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        backgroundColor: beige[150],
        width: '100%',
        height: '77px',
        padding: '12px',
      }}
    >
      <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
        {!hideBackButton ? (
          <IconButton
            onClick={handlePreviousPage}
            sx={{
              background: '#22222206',
              borderRadius: '50%',
              padding: '12px',
            }}
            disableRipple // Disable ripple effects
          >
            <ChevronLeft height={24} width={24} />
          </IconButton>
        ) : (
          <Box width="40px" />
        )}
        {!hideNameButton && <NameButton />}
        <Box width="40px" />
      </Stack>
    </Stack>
  );
}
