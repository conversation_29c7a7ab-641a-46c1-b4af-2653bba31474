import { Box, CircularProgress, Icon<PERSON>utton, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { brandYellow, grey } from '@ui/theme/colors';
import { MessageKey } from 'messageType';
import { useEffect, useRef, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import SignatureCanvas from 'react-signature-canvas';
import { useStore } from 'zustand';
import { useFormActions, useFormStore } from '../stores/FormStore';
import { Signatures, SignatureType } from '../types';
import { useEnergySolution } from 'context/energy-solution-context';
import { api } from 'utils/api';

export function CustomerSignatureModal({
  setShowSignatureModal,
  onAccept,
  installationComplete,
  isSaving,
}: {
  setShowSignatureModal: (show: boolean) => void;
  onAccept?: (signatures: Signatures) => void;
  installationComplete: boolean;
  isSaving?: boolean;
}) {
  const {
    energySolution: { solution },
  } = useEnergySolution();

  const { firstName, lastName } = solution?.presentation?.customer ?? {};
  return (
    <SignatureModal
      titleKey="installationReport.signature.customer.title"
      signatureType="customer"
      setShowSignatureModal={setShowSignatureModal}
      onAccept={onAccept}
      installationComplete={installationComplete}
      isSaving={isSaving}
      signer={{ firstName, lastName }}
    />
  );
}

export function InstallerSignatureModal({
  setShowSignatureModal,
  onAccept,
  installationComplete,
  isSaving,
}: {
  setShowSignatureModal: (show: boolean) => void;
  onAccept?: (signatures: Signatures) => void;
  installationComplete: boolean;
  isSaving?: boolean;
}) {
  const { data: installer } = api.AiraBackend.whoAmI.useQuery(undefined, {
    select: ({ firstName, lastName }) => ({ firstName, lastName }),
  });
  return (
    <SignatureModal
      titleKey="installationReport.signature.installer.title"
      signatureType="installer"
      setShowSignatureModal={setShowSignatureModal}
      onAccept={onAccept}
      installationComplete={installationComplete}
      isSaving={isSaving}
      signer={installer}
    />
  );
}

function SignatureModal({
  titleKey,
  signatureType,
  setShowSignatureModal,
  onAccept,
  installationComplete,
  isSaving,
  signer,
}: {
  titleKey: MessageKey;
  signatureType: SignatureType;
  setShowSignatureModal: (show: boolean) => void;
  onAccept?: (signatures: Signatures) => void;
  installationComplete: boolean;
  isSaving?: boolean;
  signer?: { firstName?: string; lastName?: string };
}) {
  const [isCanvasEmpty, setIsCanvasEmpty] = useState(true);
  const { setSignature } = useFormActions();
  const signatures = useStore(useFormStore(), (store) => store.signatures);
  const padRef = useRef<SignatureCanvas>(null);

  const handleClose = () => {
    padRef.current?.clear();
    setShowSignatureModal(false);
  };

  // Function to check if the canvas is empty
  const checkCanvasEmpty = () => {
    if (padRef.current) {
      setIsCanvasEmpty(padRef.current.isEmpty());
    }
  };

  const handleAccept = () => {
    const base64Data = padRef.current?.toDataURL()?.split(',')[1];
    // Convert DataURL to Uint8Array
    if (!base64Data) {
      return;
    }
    const byteString = atob(base64Data); // Decode base64
    const byteNumbers = Array.from(byteString, (char) => char.charCodeAt(0));
    const uint8Array = new Uint8Array(byteNumbers);

    const signatureObject = {
      signedTime: new Date(),
      signatureImagePng: uint8Array,
    };
    setSignature(signatureType, signatureObject);
    padRef.current?.clear();
    onAccept?.({ ...signatures, [signatureType]: signatureObject });
  };
  // Listen for canvas changes
  useEffect(() => {
    const interval = setInterval(checkCanvasEmpty, 200); // Polling the canvas state every 200ms
    return () => clearInterval(interval); // Cleanup
  }, []);

  return (
    <Stack sx={{ padding: '32px 24px 40px 24px', height: '100%' }} spacing="40px">
      <Stack direction="row" justifyContent="flex-end">
        <IconButton onClick={handleClose} sx={{ padding: 0, m: 0 }}>
          <CrossOutlinedIcon height={32} width={32} color="#000" />
        </IconButton>
      </Stack>
      <Typography variant="headline2" sx={{ marginTop: '24px !important' }}>
        <FormattedMessage id={titleKey} />
      </Typography>
      <Stack direction="row" justifyContent="center">
        <Box position="relative">
          <SignatureCanvas
            ref={padRef}
            penColor="black"
            canvasProps={{
              height: '182px',
              width: '300px',
              style: {
                background: '#fff',
                borderRadius: '22px',
                height: '182px',
                width: '300px',
              },
            }}
          />
          {signer && (
            <Typography
              variant="subtitle1"
              sx={{ position: 'absolute', bottom: '16px', left: '16px', pointerEvents: 'none' }}
            >
              {signer.firstName} {signer.lastName}
            </Typography>
          )}
        </Box>
      </Stack>
      <Typography variant="body1">
        {installationComplete ? (
          <FormattedMessage
            id="installationReport.signatureModal.installationComplete.label"
            defaultMessage="By signing this document, you are confirming that your installation has been completed."
          />
        ) : (
          <FormattedMessage
            id="installationReport.signatureModal.installationIncomplete.label"
            defaultMessage="By signing this document, you are confirming that you have reported all outstanding work/materials needed to complete your installation."
          />
        )}
      </Typography>

      <Button
        onClick={handleAccept}
        fullWidth
        sx={{ color: grey[900], background: brandYellow[400], borderRadius: '16px' }}
        disabled={isCanvasEmpty}
        data-testid="accept-button"
      >
        {isSaving ? <CircularProgress size={24} /> : 'Accept'}
      </Button>
    </Stack>
  );
}
