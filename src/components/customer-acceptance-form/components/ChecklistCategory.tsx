import { Stack, Typography } from '@mui/material';

export default function ChecklistCategory({
  label,
  children,
  icon,
}: {
  label: string;
  children: any;
  icon: React.ReactNode;
}) {
  return (
    <Stack spacing={3} pt={2}>
      <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={1} pl={2}>
        {icon}
        <Typography variant="body1Emphasis">{label}</Typography>
      </Stack>
      {children}
    </Stack>
  );
}
