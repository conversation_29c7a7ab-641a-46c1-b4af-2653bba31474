import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { CheckCircleOutlinedIcon } from '@ui/components/StandardIcons/CheckCircleOutlinedIcon';
import { GearOutlinedIcon } from '@ui/components/StandardIcons/GearOutlinedIcon';
import { Modal } from '@ui/components/Modal/Modal';
import { useInstallationProject } from 'context/installation-project-context';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { api } from 'utils/api';
import ChecklistCategory from './components/ChecklistCategory';
import Question from './components/Question';
import ReasonsQuestion from './components/ReasonsQuestion';
import { CustomerSignatureModal, InstallerSignatureModal } from './components/SignatureModal';
import TopRow from './components/TopRow';
import {
  formStateToSubmissionAnswers,
  multipleChoiceCount,
  multipleChoiceIncludes,
  singleChoiceEquals,
} from './form-state-util';
import ChecklistPage from './pages/ChecklistPage';
import Confirmation from './pages/Confirmation';
import CustomerChecklistIntroPage from './pages/CustomerChecklistIntroPage';
import IncompleteReport from './pages/IncompleteReport';
import InstructionsPage from './pages/InstructionsPage';
import ReasonsPage from './pages/ReasonsPage';
import SingleSelectPage from './pages/SingleSelectPage';
import StartPage from './pages/StartPage';
import YesNoPage from './pages/YesNoPage';
import { useFormActions, useResponses } from './stores/FormStore';
import { FormResponseType, Questionnaire, Signatures } from './types';
import { NextPageButtonVariantContextProvider } from './contexts/NextPageButtonVariantContext';

function isWorkComplete(responses: FormResponseType) {
  const systemInstalled = responses['system-components-installed'] === true;
  const workCompleted = responses['work-completed'] === true;
  return systemInstalled && workCompleted;
}

type PageId =
  | 'start-page'
  | 'instructions'
  | 'system-components-installed'
  | 'system-components-installed-reasons'
  | 'system-components-delivered'
  | 'missing-components-reasons'
  | 'work-completed'
  | 'work-completed-reasons'
  | 'internet-connection'
  | 'internet-connection-reasons'
  | 'hot-water-cylinder-vented'
  | 'hot-water-cylinder-unvented-checklist'
  | 'pressure-reducer-needed'
  | 'pressure-reducer-needed-detail'
  | 'old-heating-system'
  | 'installer-checklists'
  | 'customer-present'
  | 'customer-checklist-intro'
  | 'customer-checklist'
  | 'installation-incomplete-report'
  | 'installation-complete-confirmation'
  | 'installation-incomplete-confirmation';

interface Page {
  id: PageId;
  condition?: (responses: FormResponseType, questionnaire: Questionnaire) => boolean;
  categories?: string[];
  noClearFieldsIfDisabled?: string[];
}

const PAGES: Page[] = [
  { id: 'start-page' },
  { id: 'instructions' },
  /* REASON PAGES */
  { id: 'system-components-installed', categories: ['system-components-installed'] },
  {
    id: 'system-components-installed-reasons',
    condition: (responses) => responses['system-components-installed'] === false,
    categories: ['system-components-installed'],
    noClearFieldsIfDisabled: ['system-components-installed'],
  },
  {
    id: 'system-components-delivered',
    condition: (responses) => responses['system-components-installed'] === false,
    categories: ['system-components-delivered'],
  },
  {
    id: 'missing-components-reasons',
    condition: (responses) => responses['system-components-delivered'] === false,
    categories: ['system-components-delivered'],
    noClearFieldsIfDisabled: ['system-components-delivered'],
  },
  { id: 'work-completed', categories: ['work-completed'] },
  {
    id: 'work-completed-reasons',
    condition: (responses) => responses['work-completed'] === false,
    categories: ['work-completed'],
    noClearFieldsIfDisabled: ['work-completed'],
  },
  { id: 'internet-connection', categories: ['heat-pump-connected-internet'] },
  {
    id: 'internet-connection-reasons',
    condition: (responses) => responses['heat-pump-connected-internet'] === false,
    categories: ['heat-pump-connected-internet'],
    noClearFieldsIfDisabled: ['heat-pump-connected-internet'],
  },
  {
    id: 'hot-water-cylinder-vented',
    condition: isWorkComplete,
    categories: ['hot-water-cylinder-vented'],
  },
  {
    id: 'hot-water-cylinder-unvented-checklist',
    condition: (responses, questionnaire) =>
      isWorkComplete(responses) &&
      singleChoiceEquals(responses, questionnaire, 'hot-water-cylinder-vented', 'unvented'),
    categories: ['hot-water-cylinder-vented'],
    noClearFieldsIfDisabled: ['hot-water-cylinder-vented'],
  },
  {
    id: 'pressure-reducer-needed',
    condition: isWorkComplete,
    categories: ['pressure-reducer-needed'],
  },
  {
    id: 'pressure-reducer-needed-detail',
    condition: (responses) => isWorkComplete(responses) && responses['pressure-reducer-needed'] === true,
    categories: ['pressure-reducer-needed'],
    noClearFieldsIfDisabled: ['pressure-reducer-needed'],
  },
  /* CHECKLIST PAGES */
  {
    id: 'old-heating-system',
    condition: isWorkComplete,
    categories: ['old-heating-system'],
  },
  {
    id: 'installer-checklists',
    condition: isWorkComplete,
    categories: ['technician-checklist', 'system-checklist', 'installation-checklist'],
  },
  { id: 'customer-present', categories: ['customer-present'] },
  { id: 'customer-checklist-intro', condition: (responses) => responses['customer-present'] !== false },
  {
    id: 'customer-checklist',
    categories: ['customer-checklist'],
    condition: (responses) => responses['customer-present'] !== false,
  },
  { id: 'installation-incomplete-report', condition: (responses) => !isWorkComplete(responses) },
];

export default function ComissioningQuestionnaire({ form }: { form: Questionnaire }) {
  const router = useRouter();
  const isMobile = useMediaQuery('max-width: 900px');
  const project = useInstallationProject();
  const responses = useResponses();
  const { setResponses, clearResponse, resetSignatures } = useFormActions();
  const [currentPage, setCurrentPage] = useState<PageId>('start-page');
  const [showCustomerSignatureModal, setShowCustomerSignatureModal] = useState(false);
  const [showInstallerSignatureModal, setShowInstallerSignatureModal] = useState(false);
  const locked = form.latestSurveyOutcome === 'accepted';

  const { mutate: submitAcceptanceSurvey, isPending: isSubmittingAcceptanceSurvey } =
    api.InstallationProject.submitAcceptanceSurvey.useMutation();

  useEffect(() => {
    const onHashChangeStart = (url: string) => {
      const [, hash] = url.split('#');
      const page = PAGES.find(({ id }) => id === hash);
      if (page && page.id !== currentPage) {
        setCurrentPage(page.id);
      } else if (!hash) {
        setCurrentPage('start-page');
      }
    };

    router.events.on('hashChangeStart', onHashChangeStart);

    return () => {
      router.events.off('hashChangeStart', onHashChangeStart);
    };
  }, [currentPage, router.events]);

  const handleSubmitResourceSignature = ({ installer: resourceSignature, customer: customerSignature }: Signatures) => {
    if (!resourceSignature || !resourceSignature.signatureImagePng || !resourceSignature.signedTime) {
      throw new Error('Resource signature is required');
    }
    submitAcceptanceSurvey(
      {
        projectId: project!.id!.value,
        questionnaireId: form!.id!.value,
        answers: formStateToSubmissionAnswers(responses, form),
        resourceSignature: {
          signatureImagePng: resourceSignature.signatureImagePng,
          signedTime: resourceSignature.signedTime,
        },
        customerSignature:
          customerSignature?.signatureImagePng && customerSignature.signedTime
            ? { signatureImagePng: customerSignature.signatureImagePng, signedTime: customerSignature.signedTime }
            : undefined,
        outcome: isWorkComplete(responses) ? 'ACCEPTED' : 'WORK_REQUIRED',
      },
      {
        onSuccess: () => {
          setShowInstallerSignatureModal(false);
          if (isWorkComplete(responses)) {
            setCurrentPage('installation-complete-confirmation');
            router.push({ hash: 'installation-complete-confirmation' });
          } else {
            setCurrentPage('installation-incomplete-confirmation');
            router.push({ hash: 'installation-incomplete-confirmation' });
          }
        },
      },
    );
  };

  const getNextPage = useCallback(
    (current: PageId, { responses: currentResponses }: { responses: FormResponseType }): PageId | undefined => {
      const currentPageIndex = PAGES.findIndex((page) => page.id === current);
      const formCategories = new Set(form.categories.map((category) => category.slug));
      const nextPage = PAGES.slice(currentPageIndex + 1).find(
        (page) =>
          (!page.condition || page.condition(currentResponses, form)) &&
          (!page.categories || page.categories.some((category) => formCategories.has(category))),
      )?.id;
      return nextPage;
    },
    [form],
  );

  const clearResponseFields = (fieldsToClear: string[], currentResponses: FormResponseType): FormResponseType =>
    fieldsToClear.reduce((acc, fieldSlug) => {
      if (acc[fieldSlug] !== undefined) {
        return { ...acc, [fieldSlug]: undefined };
      }
      return acc;
    }, currentResponses);

  const clearDisabledFields = (currentResponses: FormResponseType): FormResponseType =>
    PAGES.reduce((acc, page) => {
      // We evaluate conditions on the accumulated responses to ensure all
      // necessary fields are cleared, since changes to the responses in earlier
      // pages can affect the outcome of conditions in later pages
      if (page.condition && !page.condition(acc, form)) {
        const categoryIds = form.categories
          .filter((category) => page.categories?.includes(category.slug))
          .map((c) => c.id?.value);
        const fieldsToClear = form.fields
          .filter((field) => categoryIds.includes(field.categoryId?.value))
          .map((field) => field.slug)
          .filter((slug) => !page.noClearFieldsIfDisabled?.includes(slug));
        return clearResponseFields(fieldsToClear, acc);
      }
      return acc;
    }, currentResponses);

  const setNextPage = ({ responses: currentResponses }: { responses: FormResponseType }) => {
    // TODO: it might be better to apply this filtering immediately when state
    // changes are made instead of waiting for the page to change, to ensure
    // backwards navigation works as expected
    const filteredResponses = clearDisabledFields(currentResponses);
    const nextPage = getNextPage(currentPage, { responses: filteredResponses });
    if (!nextPage) {
      resetSignatures();
      if (responses['customer-present'] !== false) {
        setShowCustomerSignatureModal(true);
      } else {
        setShowInstallerSignatureModal(true);
      }
      return;
    }
    setResponses(filteredResponses);
    setCurrentPage(nextPage);
    router.push({ hash: nextPage });
  };

  /* Scroll up to the top of the page when navigating between screens */
  const containerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    containerRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentPage]);

  const setPreviousPage = () => {
    const currentPageIndex = PAGES.findIndex((page) => page.id === currentPage);
    const previousPage = PAGES.slice(0, currentPageIndex).findLast(
      (page) =>
        (!page.condition || page.condition(responses, form)) &&
        (!page.categories || page.categories.some((category) => form.categories.some((c) => c.slug === category))),
    )?.id;
    if (!previousPage) {
      throw new Error(`Could not find previous page. PageId = ${currentPage}`);
    }
    setCurrentPage(previousPage as PageId);
    router.push({ hash: previousPage });
  };

  const fieldSlugsInCategory = (categorySlug: string): string[] => {
    const category = form.categories.find((c) => c.slug === categorySlug);
    const categoryId = category?.id?.value;
    return form.fields.filter((field) => field.categoryId?.value === categoryId).map((field) => field.slug);
  };

  const nextPageButtonVariant = useMemo(
    () => (getNextPage(currentPage, { responses }) ? 'next' : 'sign'),
    // optimization: button state is only dependent on one question, so only recompute if that changes
    [getNextPage, currentPage, responses['customer-present']], // eslint-disable-line react-hooks/exhaustive-deps
  );

  return (
    <NextPageButtonVariantContextProvider value={nextPageButtonVariant}>
      <Box ref={containerRef}>
        <TopRow
          hide={
            currentPage === 'installation-complete-confirmation' ||
            currentPage === 'installation-incomplete-confirmation'
          }
          previousPage={setPreviousPage}
          hideNameButton={currentPage === 'instructions'}
          hideBackButton={currentPage === 'start-page'}
        />
        <Stack maxWidth={isMobile ? undefined : '500px'} mx="auto">
          {currentPage === 'start-page' && <StartPage nextPage={setNextPage} locked={locked} />}
          {currentPage === 'instructions' && <InstructionsPage nextPage={setNextPage} />}
          {currentPage === 'system-components-installed' && (
            <YesNoPage form={form} slug="system-components-installed" nextPage={setNextPage} />
          )}
          {currentPage === 'system-components-installed-reasons' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'not-installed-components')!.label}
              nextPage={setNextPage}
              responseValidator={(res) => {
                if (
                  multipleChoiceCount(responses, form, 'not-installed-components') === 0 &&
                  !res['not-installed-other']
                ) {
                  return false;
                }
                if (
                  multipleChoiceIncludes(responses, form, 'not-installed-components', 'pipe') &&
                  form.fields.some((field) => field.slug === 'not-installed-pipe-insulation') &&
                  multipleChoiceCount(responses, form, 'not-installed-pipe-insulation') === 0
                ) {
                  return false;
                }
                return true;
              }}
            >
              <ReasonsQuestion
                slug="not-installed-components"
                form={form}
                dependentQuestionSlug="not-installed-pipe-insulation"
                parentOptionSlug="pipe"
              />
              {((field) =>
                field &&
                Array.isArray(responses['not-installed-components']) &&
                responses['not-installed-components']?.includes('pipe') && (
                  <Stack spacing={2} sx={{ background: '#22222608', borderRadius: '22px', px: '16px', py: '24px' }}>
                    <Typography variant="body2">{field!.label}</Typography>
                    <ReasonsQuestion slug="not-installed-pipe-insulation" form={form} />
                  </Stack>
                ))(form.fields.find((field) => field.slug === 'not-installed-pipe-insulation'))}
              {((field) =>
                field?.slug === 'not-installed-other' && (
                  <Stack spacing={1} sx={{ background: '#22222608', borderRadius: '22px', px: '16px', py: '24px' }}>
                    <Typography variant="body2Emphasis" pl="12px">
                      {field!.label}
                    </Typography>
                    <ReasonsQuestion slug="not-installed-other" form={form} />
                  </Stack>
                ))(form.fields.find((field) => field.slug === 'not-installed-other'))}
            </ReasonsPage>
          )}
          {currentPage === 'system-components-delivered' && (
            <YesNoPage form={form} slug="system-components-delivered" nextPage={setNextPage} />
          )}
          {currentPage === 'missing-components-reasons' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'missing-components')!.label}
              nextPage={setNextPage}
              responseValidator={(res) =>
                multipleChoiceCount(responses, form, 'missing-components') > 0 || !!res['missing-components-other']
              }
            >
              <ReasonsQuestion slug="missing-components" form={form} />
              <Stack spacing={1} sx={{ background: '#22222608', borderRadius: '22px', px: '16px', py: '24px' }}>
                <Typography variant="body2Emphasis" pl="12px">
                  {form.fields.find((field) => field.slug === 'missing-components-other')!.label}
                </Typography>
                <ReasonsQuestion slug="missing-components-other" form={form} />
              </Stack>
            </ReasonsPage>
          )}
          {currentPage === 'work-completed' && <YesNoPage form={form} slug="work-completed" nextPage={setNextPage} />}
          {currentPage === 'work-completed-reasons' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'incomplete-work-details')!.label}
              nextPage={setNextPage}
              responseValidator={(res) => Boolean(res['incomplete-work-details'])}
            >
              <ReasonsQuestion slug="incomplete-work-details" form={form} />
            </ReasonsPage>
          )}
          {currentPage === 'internet-connection' && (
            <YesNoPage form={form} slug="heat-pump-connected-internet" nextPage={setNextPage} />
          )}
          {currentPage === 'internet-connection-reasons' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'heat-pump-not-connected-internet-reasons')!.label}
              nextPage={setNextPage}
            >
              <ReasonsQuestion slug="heat-pump-not-connected-internet-reasons" form={form} />
              <Question slug="heat-pump-not-connected-internet-reasons-other" form={form} />
              <Question slug="car-reception-booster-necessary" form={form} />
              <Question slug="connection-issue-solved" form={form} />
            </ReasonsPage>
          )}
          {currentPage === 'hot-water-cylinder-vented' && (
            <SingleSelectPage slug="hot-water-cylinder-vented" nextPage={setNextPage} form={form} />
          )}
          {currentPage === 'hot-water-cylinder-unvented-checklist' && (
            <ReasonsPage
              label="Unvented system checklist"
              nextPage={setNextPage}
              responseValidator={(res) =>
                res['tundish-and-discharge-pipework-installed'] === true && res['expansion-vessel-checked'] === true
              }
            >
              <Question slug="pressure-reducing-valve-location" form={form} />
              <Question slug="pressure-reducing-valve-setting" form={form} />
              <Question slug="tundish-and-discharge-pipework-installed" form={form} />
              <Question slug="expansion-vessel-checked" form={form} />
            </ReasonsPage>
          )}
          {currentPage === 'pressure-reducer-needed' && (
            <YesNoPage form={form} slug="pressure-reducer-needed" nextPage={setNextPage} />
          )}
          {currentPage === 'pressure-reducer-needed-detail' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'pressure-reducer-needed')!.label}
              nextPage={setNextPage}
              responseValidator={(res) => res['pressure-value-after-reducer'] !== undefined}
            >
              <Question slug="pressure-reducer-situated" form={form} />
              <Question slug="pressure-value-after-reducer" form={form} />
            </ReasonsPage>
          )}
          {currentPage === 'old-heating-system' && (
            <ChecklistPage nextPage={setNextPage} questionSlugs={fieldSlugsInCategory('old-heating-system')}>
              <ChecklistCategory
                label={form.categories.find((category) => category.slug === 'old-heating-system')!.label}
                icon={<CheckCircleOutlinedIcon height={16} width={16} />}
              >
                {fieldSlugsInCategory('old-heating-system').map((slug) => (
                  <Question key={slug} slug={slug} form={form} />
                ))}
              </ChecklistCategory>
            </ChecklistPage>
          )}
          {currentPage === 'installer-checklists' && (
            <ChecklistPage
              nextPage={setNextPage}
              questionSlugs={fieldSlugsInCategory('technician-checklist').concat(
                fieldSlugsInCategory('system-checklist'),
                fieldSlugsInCategory('installation-checklist'),
              )}
            >
              {form.categories.some((category) => category.slug === 'technician-checklist') && (
                <ChecklistCategory
                  label={form.categories.find((category) => category.slug === 'technician-checklist')!.label}
                  icon={<CheckCircleOutlinedIcon height={16} width={16} />}
                >
                  {fieldSlugsInCategory('technician-checklist').map((slug) => (
                    <Question key={slug} slug={slug} form={form} />
                  ))}
                </ChecklistCategory>
              )}
              {form.categories.some((category) => category.slug === 'system-checklist') && (
                <ChecklistCategory
                  label={form.categories.find((category) => category.slug === 'system-checklist')!.label}
                  icon={<GearOutlinedIcon height={16} width={16} />}
                >
                  {fieldSlugsInCategory('system-checklist').map((slug) => (
                    <Question key={slug} slug={slug} form={form} />
                  ))}
                </ChecklistCategory>
              )}
              {form.categories.some((category) => category.slug === 'installation-checklist') && (
                <ChecklistCategory
                  label={form.categories.find((category) => category.slug === 'installation-checklist')!.label}
                  icon={<CheckCircleOutlinedIcon height={16} width={16} />}
                >
                  {fieldSlugsInCategory('installation-checklist').map((slug) => (
                    <Question key={slug} slug={slug} form={form} />
                  ))}
                </ChecklistCategory>
              )}
            </ChecklistPage>
          )}
          {currentPage === 'customer-present' && (
            <ReasonsPage
              label={form.fields.find((field) => field.slug === 'customer-present')!.label}
              nextPage={setNextPage}
              responseValidator={(res) => res['customer-present'] !== undefined}
            >
              <Question
                slug="customer-present"
                form={form}
                onChange={(newValue) => {
                  if (newValue !== false) clearResponse('onboard-someone-else');
                }}
              />
              {responses['customer-present'] === false && <Question slug="onboard-someone-else" form={form} />}
            </ReasonsPage>
          )}
          {currentPage === 'customer-checklist-intro' && <CustomerChecklistIntroPage nextPage={setNextPage} />}
          {currentPage === 'customer-checklist' && (
            <ChecklistPage nextPage={setNextPage} questionSlugs={fieldSlugsInCategory('customer-checklist')}>
              <ChecklistCategory
                label={form.categories.find((category) => category.slug === 'customer-checklist')!.label}
                icon={<CheckCircleOutlinedIcon height={16} width={16} />}
              >
                {fieldSlugsInCategory('customer-checklist').map((slug) => (
                  <Question key={slug} slug={slug} form={form} />
                ))}
              </ChecklistCategory>
            </ChecklistPage>
          )}
          {currentPage === 'installation-incomplete-report' && (
            <IncompleteReport form={form} onClickSign={() => setNextPage({ responses })} />
          )}
          {currentPage === 'installation-complete-confirmation' && <Confirmation accepted />}
          {currentPage === 'installation-incomplete-confirmation' && <Confirmation accepted={false} />}
        </Stack>
        <Modal
          isModalOpen={showCustomerSignatureModal}
          handleClose={() => setShowCustomerSignatureModal(false)}
          width={isMobile ? '100dvw' : '500px'}
          height="fit-content"
          sx={{
            padding: 0,
            alignSelf: 'flex-end',
          }}
        >
          <CustomerSignatureModal
            installationComplete={isWorkComplete(responses)}
            setShowSignatureModal={setShowCustomerSignatureModal}
            onAccept={() => {
              setShowCustomerSignatureModal(false);
              setShowInstallerSignatureModal(true);
            }}
          />
        </Modal>
        <Modal
          isModalOpen={showInstallerSignatureModal}
          handleClose={() => setShowInstallerSignatureModal(false)}
          width={isMobile ? '100dvw' : '500px'}
          height="fit-content"
          sx={{
            padding: 0,
            alignSelf: 'flex-end',
          }}
        >
          <InstallerSignatureModal
            installationComplete={isWorkComplete(responses)}
            setShowSignatureModal={setShowInstallerSignatureModal}
            isSaving={isSubmittingAcceptanceSurvey}
            onAccept={handleSubmitResourceSignature}
          />
        </Modal>
      </Box>
    </NextPageButtonVariantContextProvider>
  );
}
