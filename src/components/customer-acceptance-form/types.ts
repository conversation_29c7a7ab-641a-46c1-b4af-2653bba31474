import { RouterOutputs } from 'utils/api';

export type FormResponseType = {
  [questionSlug: string]: string | number | boolean | string[] | undefined;
};

export type CurrentPageType = 'StartPage' | 'yesNo' | 'reasons' | 'customerChecklist' | 'submit' | 'summary';
export type SignatureType = 'customer' | 'installer';
export type Signatures = {
  [key in SignatureType]?: {
    signedTime?: Date;
    signatureImagePng?: Uint8Array<ArrayBuffer>;
  };
};

export type NextPage = (state: { responses: FormResponseType }) => void;

export type Questionnaire = RouterOutputs['InstallationProject']['resolveAcceptanceQuestionnaire'];
export type QuestionnaireField = Questionnaire['fields'][number];
