import type { RouterInputs } from 'utils/api';
import type { FormResponseType, Questionnaire } from './types';

type SubmissionAnswers = RouterInputs['InstallationProject']['submitAcceptanceSurvey']['answers'];

export const toInitialFormState = (questionnaire: Questionnaire): FormResponseType =>
  questionnaire.fields.reduce((acc: FormResponseType, field) => {
    const { slug: fieldSlug } = field;
    const answerValue = field.answer?.value;
    if (!answerValue) return acc;
    switch (answerValue.$case) {
      case 'text':
        acc[fieldSlug] = answerValue.text.value;
        break;
      case 'number':
        acc[fieldSlug] = answerValue.number.value;
        break;
      case 'yesOrNo':
        acc[fieldSlug] = answerValue.yesOrNo.value;
        break;
      case 'singleChoice':
        {
          const optionId = answerValue.singleChoice.value?.value;
          const option = answerValue.singleChoice.type?.options.find(({ id }) => id?.value === optionId);
          acc[fieldSlug] = option?.id?.value;
        }
        break;
      case 'multipleChoice':
        {
          const optionIds = answerValue.multipleChoice.values.map(({ value }) => value);
          const optionSlugs = answerValue.multipleChoice.type?.options
            .filter(({ id }) => optionIds.includes(id!.value))
            .map(({ slug }) => slug);
          acc[fieldSlug] = optionSlugs;
        }
        break;
      default: {
        const exhaustiveCheck: never = answerValue; // eslint-disable-line @typescript-eslint/no-unused-vars
      }
    }
    return acc;
  }, {});

export const formStateToSubmissionAnswers = (
  responses: FormResponseType,
  questionnaire: Questionnaire,
): SubmissionAnswers => {
  const responsesById: Record<string, string | number | boolean | string[]> = {};
  Object.entries(responses).forEach(([key, answer]) => {
    const responseId = questionnaire.fields.find((field) => field.slug === key)?.id?.value;
    if (!responseId) {
      return;
    }
    if (answer === undefined) {
      return;
    }
    if (Array.isArray(answer)) {
      const field = questionnaire.fields.find((f) => f.id?.value === responseId);
      if (!field) {
        return;
      }
      if (answer.length === 0) {
        return;
      }

      responsesById[responseId] = answer
        .map((slug) =>
          field?.type?.$case === 'multipleChoice'
            ? field.type.multipleChoice.options.find(
                (option: { slug: string; id?: { value: string } }) => option.slug === slug,
              )?.id?.value
            : null,
        )
        .filter(Boolean) as string[];
    } else {
      responsesById[responseId] = answer;
    }
  });
  return responsesById;
};

export const singleChoiceEquals = (
  responses: FormResponseType,
  questionnaire: Questionnaire,
  fieldSlug: string,
  expectedOptionSlug: string,
): boolean => {
  const optionId = responses[fieldSlug];
  const field = questionnaire.fields.find((f) => f.slug === fieldSlug);
  if (!field || !optionId || field.type?.$case !== 'singleChoice') {
    return false;
  }
  const { options } = field.type.singleChoice;
  const option = options.find((o) => o.id?.value === optionId);
  if (!option) {
    return false;
  }
  return option.slug === expectedOptionSlug;
};

export const multipleChoiceIncludes = (
  responses: FormResponseType,
  questionnaire: Questionnaire,
  fieldSlug: string,
  expectedOptionSlug: string,
): boolean => {
  const optionSlugs = responses[fieldSlug];
  const field = questionnaire.fields.find((f) => f.slug === fieldSlug);
  if (!field || !Array.isArray(optionSlugs) || field.type?.$case !== 'multipleChoice') {
    return false;
  }
  const { options } = field.type.multipleChoice;
  return options.some((option) => optionSlugs.includes(option.slug) && option.slug === expectedOptionSlug);
};

export const multipleChoiceCount = (
  responses: FormResponseType,
  questionnaire: Questionnaire,
  fieldSlug: string,
): number => {
  const optionSlugs = responses[fieldSlug];
  const field = questionnaire.fields.find((f) => f.slug === fieldSlug);
  if (!field || !Array.isArray(optionSlugs) || field.type?.$case !== 'multipleChoice') {
    return 0;
  }
  const { options } = field.type.multipleChoice;
  return options.filter((option) => optionSlugs.includes(option.slug)).length;
};
