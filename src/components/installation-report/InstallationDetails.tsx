import { List, ListItem, Stack, Typography, useMediaQuery } from '@mui/material';
import { useInstallationProject } from 'context/installation-project-context';
import { HousePersonOutsideOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutsideOutlinedIcon';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { useEnergySolution } from 'context/energy-solution-context';
import { FormattedMessage } from 'react-intl';
import Products from '../customer-acceptance-form/components/Products';

export default function InstallationDetails() {
  const isMobile = useMediaQuery('(max-width: 1268px)');
  const installationProject = useInstallationProject();
  const energySolution = useEnergySolution();
  const products = energySolution.energySolution.solution?.presentation?.products ?? [];

  const installerJob = installationProject?.jobs.find((job) => job.requiredRole === 1);
  const installers = installationProject?.assignedResourcesDetails.filter((identity) =>
    installerJob?.assignedResources.some((res) => res.userId?.value === identity.userId?.value),
  );

  const customer = energySolution.energySolution.solution?.presentation?.customer;
  const customerName = customer ? `${customer.firstName} ${customer.lastName}` : '';

  const location = energySolution.energySolution.solution?.presentation?.location;
  const address = location?.$case === 'exactAddress' ? location?.exactAddress.formattedAddress : '';

  return (
    <Stack
      spacing={3}
      sx={{
        alignSelf: 'stretch',
        width: '100%',
      }}
    >
      <Stack direction={isMobile ? 'column' : 'row'} spacing={2} sx={{ alignSelf: 'stretch', flex: 1 }}>
        <Stack
          spacing={2}
          sx={{
            background: '#22222608',
            borderRadius: '22px',
            padding: '24px',
            flex: 2,
          }}
        >
          <Stack
            direction="row"
            justifyContent="flex-start"
            alignItems="center"
            spacing={2}
            sx={{ alignSelf: 'stretch' }}
          >
            <HousePersonOutsideOutlinedIcon height={16} width={16} />
            <Typography variant="body1">
              <FormattedMessage id="installationReport.customer.title" defaultMessage="Customer" />
            </Typography>
          </Stack>
          <Typography variant="body2">{customerName}</Typography>
          <Typography variant="body2">{address}</Typography>
        </Stack>
        <Products products={products} />
        {installers && installers.length > 0 && (
          <Stack spacing={1} sx={{ background: '#22222608', borderRadius: '22px', p: '24px', flex: 1 }}>
            <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={2}>
              <PersonOutlinedIcon height={16} width={16} />
              <Typography variant="body1">Installers</Typography>
            </Stack>
            <List sx={{ listStyle: 'none', display: 'flex', gap: 1, flexDirection: 'column' }}>
              {installers.map((installer) => (
                <ListItem key={installer.userId?.value} sx={{ p: 0 }}>
                  <Typography variant="body2">
                    {installer.firstName} {installer.lastName}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}
