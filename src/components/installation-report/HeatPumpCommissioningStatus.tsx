import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { CalendarIcon } from '@ui/components/Icons/Calendar/CalendarIcon';
import { formatDate } from './date-util';

export default function HeatPumpCommissioningStatus({
  heatPumpCommissioningDate,
}: {
  heatPumpCommissioningDate?: Date;
}) {
  return (
    <Stack direction="row" gap={1} alignItems="center" py={8}>
      <CalendarIcon />
      <Typography variant="body1Emphasis" component="h2">
        {heatPumpCommissioningDate ? (
          <FormattedMessage
            id="installationReport.commissioningDate.label"
            defaultMessage="Commissioning finalised {date}"
            values={{ date: formatDate(heatPumpCommissioningDate) }}
          />
        ) : (
          <FormattedMessage
            id="installationReport.commissioningIncomplete.label"
            defaultMessage="Commissioning incomplete"
          />
        )}
      </Typography>
    </Stack>
  );
}
