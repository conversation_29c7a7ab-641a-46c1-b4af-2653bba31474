import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { useState } from 'react';
import { FormattedMessage } from 'react-intl';
import InstallationDetails from './InstallationDetails';
import InstallationReportAnswers from './InstallationReportAnswers';
import Signature from './Signature';
import type { InstallationReportSubmission } from './types';
import { formatDate } from './date-util';

function ReportSelector({
  reports,
  selectedReport,
  onSelectReport,
}: {
  reports: InstallationReportSubmission[];
  selectedReport: InstallationReportSubmission | null;
  onSelectReport: (report: InstallationReportSubmission) => void;
}) {
  return (
    <Stack>
      <Typography variant="inputLabel" component="h2">
        <FormattedMessage id="installationReport.reportSelector.label" defaultMessage="Report submitted" />
      </Typography>
      <Stack direction="row" spacing={2} sx={{ overflowX: 'auto', py: 2 }}>
        {reports.map((report) => (
          <Box
            key={formatDate(report.submittedTime!)}
            sx={{
              background: '#22222608',
              borderRadius: '22px',
              p: '16px',
              cursor: 'pointer',
              ...(selectedReport === report && reports.length > 1
                ? {
                    background: '#000',
                    color: '#fff',
                  }
                : null),
            }}
            onClick={() => onSelectReport(report)}
          >
            <Typography variant="body1Emphasis" sx={{ color: 'inherit' }}>
              {formatDate(report.submittedTime!)}
            </Typography>
          </Box>
        ))}
      </Stack>
    </Stack>
  );
}

export default function InstallationReport({ reports }: { reports: InstallationReportSubmission[] }) {
  const [selectedReport, setSelectedReport] = useState<InstallationReportSubmission | null>(reports[0] ?? null);
  const isMobile = useMediaQuery('(max-width: 1000px)');
  return (
    <Stack>
      <ReportSelector reports={reports} selectedReport={selectedReport} onSelectReport={setSelectedReport} />
      <Box px={2} pt={4} pb={isMobile ? 6 : 7} maxWidth="720px">
        <Typography variant="headline1">
          {selectedReport?.outcome === 'workRequired' ? (
            <FormattedMessage id="installationReport.incomplete.title" />
          ) : (
            <FormattedMessage id="installationReport.title" />
          )}
        </Typography>
      </Box>
      <InstallationDetails />
      {selectedReport && <InstallationReportAnswers report={selectedReport} />}
      <Stack direction={isMobile ? 'column' : 'row'} flexWrap="wrap" spacing={isMobile ? 6 : 12} sx={{ pt: '104px' }}>
        {selectedReport?.signatures.customer && (
          <Signature signature={selectedReport.signatures.customer} label="Customer signature" />
        )}
        {selectedReport?.signatures.resource && (
          <Signature signature={selectedReport.signatures.resource} label="Installer signature" />
        )}
      </Stack>
    </Stack>
  );
}
