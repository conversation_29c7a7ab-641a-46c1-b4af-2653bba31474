import { InstallationProjectAcceptanceSurvey_AnswerCategory_CategoryDisplayType as CategoryDisplayType } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Box, Divider, Stack, Typography, TypographyProps, useMediaQuery } from '@mui/material';
import { Answer, InstallationReportSubmission } from './types';

function AnswerValue({ value, variant: variantOverride }: { value: Answer; variant?: TypographyProps['variant'] }) {
  const variant = variantOverride ?? 'body1';
  switch (value.value?.$case) {
    case 'text':
      return (
        <Box sx={{ maxWidth: '720px' }}>
          {value.value.text.value.split('\n').map((line, i) => (
            <Typography key={`line-${i}`} variant={variant}>
              {line}
            </Typography>
          ))}
        </Box>
      );
    case 'number':
      return (
        <Typography component="span" variant="body1">
          {value.value.number.value}
          {value.field?.type?.$case === 'number' && value.field.type.number.unit && (
            <Typography component="span" variant={variant} ml={1}>
              {value.field.type.number.unit}
            </Typography>
          )}
        </Typography>
      );
    case 'yesOrNo': {
      const typeDetail = value.value.yesOrNo.type;
      return (
        <Typography variant={variant}>
          {value.value.yesOrNo.value ? typeDetail?.yesLabel : typeDetail?.noLabel}
        </Typography>
      );
    }
    case 'singleChoice': {
      const optionId = value.value.singleChoice.value?.value;
      const currentOption = value.value.singleChoice.type?.options.find((opt) => opt.id?.value === optionId);
      return <Typography variant={variant}>{currentOption?.label}</Typography>;
    }
    case 'multipleChoice': {
      const optionIds = value.value.multipleChoice.values.map((val) => val.value);
      const selectedOptions = value.value.multipleChoice.type?.options.filter((opt) =>
        optionIds.includes(opt.id!.value),
      );
      return (
        <Box component="ul" sx={{ listStyle: 'none', px: 0, my: 0 }}>
          {selectedOptions?.map((opt) => (
            <Typography component="li" key={opt.id!.value} variant={variant}>
              {opt.label}
            </Typography>
          ))}
        </Box>
      );
    }
    default:
      return null;
  }
}

export default function InstallationReportAnswers({ report }: { report: InstallationReportSubmission }) {
  const isMobile = useMediaQuery('(max-width: 1000px)');
  return (
    <Stack>
      {report.categories.map((category) =>
        category.displayType === CategoryDisplayType.CATEGORY_DISPLAY_TYPE_DETAIL ? (
          <Stack key={category.id?.value}>
            {category.answers.map((answer, answerIdx) => (
              <Box
                key={answer.field?.id?.value}
                sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}
              >
                {answerIdx === 0 ? (
                  <>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        justifyContent: 'space-between',
                        pt: isMobile ? 6 : 10,
                        pb: 2,
                        gap: 8,
                      }}
                    >
                      <Typography variant="headline4">{answer.field?.label}</Typography>
                      <AnswerValue value={answer} variant="headline3" />
                    </Box>
                    <Divider />
                  </>
                ) : (
                  <>
                    <Typography variant="body1" fontWeight="medium" pt={4} pb={1}>
                      {answer.field?.label}
                    </Typography>
                    <AnswerValue value={answer} />
                  </>
                )}
              </Box>
            ))}
          </Stack>
        ) : (
          <Box key={category.id?.value} pt={isMobile ? 6 : 10}>
            <Box pb={2}>
              <Typography variant="headline4">{category.label}</Typography>
            </Box>
            <Divider />
            <Stack spacing={4} pt={4}>
              {category.answers.map((answer) => (
                <Stack key={answer.field?.id?.value} direction="row" sx={{ justifyContent: 'space-between' }} gap={8}>
                  <Typography variant="body1" fontWeight="normal">
                    {answer.field?.label}
                  </Typography>
                  <AnswerValue value={answer} />
                </Stack>
              ))}
            </Stack>
          </Box>
        ),
      )}
    </Stack>
  );
}
