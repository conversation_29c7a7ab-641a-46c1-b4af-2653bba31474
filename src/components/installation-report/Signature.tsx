import { Box, Typography, useMediaQuery } from '@mui/material';
import { grey } from '@ui/theme/colors';

export default function Signature({
  signature: { png, signer },
  label,
}: {
  signature: { png?: string; signer?: { firstName?: string; lastName?: string } };
  label: string;
}) {
  const isMobile = useMediaQuery('(max-width: 1000px)');

  return (
    <Box sx={{ width: isMobile ? '100%' : '345px' }}>
      <Typography variant="body2" sx={{ pb: 1, color: grey[600] }}>
        {label}
      </Typography>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img
        alt="A signature"
        src={`data:image/png;base64, ${png}`}
        style={{
          maxHeight: '144px',
          width: '100%',
          borderRadius: '24px',
          backgroundColor: 'rgba(34, 34, 34, 0.03)',
          objectFit: 'contain',
          padding: '8px',
        }}
      />
      {signer && (
        <Typography variant="subtitle2" sx={{ color: grey[600], pt: 1 }}>
          {signer.firstName} {signer.lastName}
        </Typography>
      )}
    </Box>
  );
}
