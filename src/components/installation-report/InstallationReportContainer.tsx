import { Alert, Stack } from '@mui/material';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { useInstallationProject } from 'context/installation-project-context';
import { api } from 'utils/api';
import HeatPumpCommissioningStatus from './HeatPumpCommissioningStatus';
import InstallationReport from './InstallationReport';

export default function InstallationReportContainer() {
  const project = useInstallationProject();
  const { data: response, isLoading: isLoadingSurveys } = api.InstallationProject.listInstallationReports.useQuery(
    { projectId: project!.id!.value },
    { enabled: !!project?.id?.value },
  );

  if (isLoadingSurveys) {
    return <HeatPumpLoader />;
  }

  if (!response || response.reports.length === 0) {
    return (
      <Stack direction="row" justifyContent="center" alignItems="center" height="100%">
        <Alert severity="error" sx={{ fontSize: 24, alignItems: 'center' }}>
          No installation reports found
        </Alert>
      </Stack>
    );
  }
  const { reports, heatPumpCommissioningDate } = response;

  return (
    <>
      <HeatPumpCommissioningStatus heatPumpCommissioningDate={heatPumpCommissioningDate} />
      <InstallationReport reports={reports} />
    </>
  );
}
