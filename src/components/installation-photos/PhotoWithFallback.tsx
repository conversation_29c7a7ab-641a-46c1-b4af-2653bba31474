import styled from '@emotion/styled';
import { Skeleton, Stack } from '@mui/material';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { MagnifyingGlassOutlinedIcon } from '@ui/components/StandardIcons/MagnifyingGlassOutlinedIcon';
import { PhotoLoadStatus, usePhotoLoader } from './hooks/usePhotoLoader';

const StyledOverlay = styled(MagnifyingGlassOutlinedIcon)({
  position: 'absolute',
  top: '50%',
  left: '50%',
  translate: '-50% -50%',
  transform: 'scale(0.8)',
  transition: 'transform 0.3s cubic-bezier(0.9, 0, 0, 1), opacity 0.3s',
  opacity: 0,
  scale: 0.8,
  pointerEvents: 'none',
  color: 'white',
  width: '32px',
  height: '32px',
});

const StyledImg = styled('div')<{ $src: string }>(({ $src }) => ({
  width: '100%',
  height: '100%',
  background: `url(${$src})`,
  backgroundSize: 'cover',
  position: 'relative',
  borderRadius: '16px',

  '&:hover': {
    cursor: 'pointer',
    background: `url(${$src}) rgba(0, 0, 0, 0.30)`,
    backgroundBlendMode: 'multiply',
    backgroundSize: 'cover',
    '& > svg': {
      opacity: 1,
      scale: 1,
      pointerEvents: 'auto',
      transform: 'scale(1)',
    },
  },
}));

interface PhotoWithFallbackProps {
  src: string;
  dimensions?: Dimensions;
}

interface Dimensions {
  width?: number | string;
  height?: number | string;
}

export const PhotoWithFallback = ({ src, dimensions }: PhotoWithFallbackProps) => {
  const status = usePhotoLoader(src);

  if (status === PhotoLoadStatus.LOADING) {
    return (
      <Stack borderRadius={1} {...dimensions}>
        <Skeleton variant="rounded" width="100%" height="100%" />
      </Stack>
    );
  }

  if (status === PhotoLoadStatus.INVALID) {
    return (
      <Stack borderRadius={1} justifyContent="center" {...dimensions}>
        <HeatPump />
      </Stack>
    );
  }

  return (
    <Stack borderRadius={1} {...dimensions}>
      <StyledImg $src={src}>
        <StyledOverlay />
      </StyledImg>
    </Stack>
  );
};
