import { OnSitePhoto_Tag } from '@aira/on-site-dossier-grpc-api/build/ts_out/index.com.aira.acquisition.contract.on.site.dossier.v1';
import { Stack } from '@mui/material';
import { PhotoTags } from './PhotoTags';
import { PhotoWithFallback } from './PhotoWithFallback';

interface PhotoCardProps {
  photoUrl: string;
  tags: OnSitePhoto_Tag[];
  onClick: () => void;
}

export const PhotoCard = ({ photoUrl, tags, onClick }: PhotoCardProps) => {
  return (
    <Stack direction="column" spacing={1} sx={{ maxWidth: '256px' }} onClick={onClick}>
      <PhotoWithFallback src={photoUrl} dimensions={{ width: 256, height: 256 }} />
      <PhotoTags tags={tags} />
    </Stack>
  );
};
