import { OnSitePhoto } from '@aira/on-site-dossier-grpc-api/build/ts_out/index.com.aira.acquisition.contract.on.site.dossier.v1';
import styled from '@emotion/styled';
import { Stack } from '@mui/material';
import { Carousel } from '@ui/components/Carousel/Carousel';
import { Modal } from '@ui/components/Modal/Modal';
import { useState } from 'react';
import { PhotoComment } from './PhotoComment';
import { PhotoTags } from './PhotoTags';

const StyledImage = styled('img')({
  borderRadius: '16px',
  width: '100%',
  objectFit: 'contain',
  height: 'auto',
  maxHeight: 'calc(80vh - 218px)',
});

export const PhotoModal = ({
  photos,
  selectedIndex,
  onClose,
}: {
  photos: OnSitePhoto[];
  selectedIndex: number;
  onClose: () => void;
}) => {
  const [visibleIndex, setVisibleIndex] = useState<number>(selectedIndex);

  const handleSlideChange = (index: number) => {
    setVisibleIndex(index);
  };

  const visiblePhoto = photos[visibleIndex];

  return (
    <Modal isModalOpen={selectedIndex >= 0} handleClose={onClose} height="auto" darkBackground showCloseButton>
      <Stack direction="column" spacing={1} gap={3} pt={3}>
        <Carousel height="auto" initialSlide={selectedIndex} onSlideChange={handleSlideChange}>
          {photos.map((photo) => (
            <StyledImage key={photo.id?.value} alt={photo.id?.value} src={photo.signedUrl?.url} />
          ))}
        </Carousel>
        {visiblePhoto && (
          <>
            <PhotoTags tags={visiblePhoto.tags} darkMode />
            {visiblePhoto.comment && <PhotoComment comment={visiblePhoto.comment} />}
          </>
        )}
      </Stack>
    </Modal>
  );
};
