import { useEffect, useState } from 'react';

export enum PhotoLoadStatus {
  VALID = 'VALID',
  INVALID = 'INVALID',
  LOADING = 'LOADING',
}

/**
 * Hook to check if an image URL is valid
 * @param url - The image URL to validate
 * @returns isValid - Boolean indicating if the image URL is valid or undefined if not yet checked
 */
export const usePhotoLoader = (url: string): PhotoLoadStatus => {
  const [status, setStatus] = useState<PhotoLoadStatus>(PhotoLoadStatus.LOADING);

  useEffect(() => {
    if (!url) {
      setStatus(PhotoLoadStatus.LOADING);
      return;
    }

    const img = new Image();
    const handleLoad = () => setStatus(PhotoLoadStatus.VALID);
    const handleError = () => setStatus(PhotoLoadStatus.INVALID);

    img.addEventListener('load', handleLoad);
    img.addEventListener('error', handleError);
    img.src = url;

    return () => {
      img.removeEventListener('load', handleLoad);
      img.removeEventListener('error', handleError);
    };
  }, [url]);

  return status;
};
