import { Alert, Grid, Stack } from '@mui/material';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { useInstallationProject } from 'context/installation-project-context';
import { useState } from 'react';
import { api } from 'utils/api';
import { PhotoCard } from './PhotoCard';
import { PhotoModal } from './PhotoModal';

export const InstallationPhotosContainer = () => {
  const project = useInstallationProject();
  const { data: photos, isLoading: isLoadingPhotos } = api.OnSiteDossier.getPhotos.useQuery(
    { installationProjectId: project!.id!.value },
    { enabled: !!project?.id?.value },
  );
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number>(-1);

  const handleClickPhotoCard = (selectedIndex: number) => {
    setSelectedPhotoIndex(selectedIndex);
  };

  if (isLoadingPhotos) {
    return <HeatPumpLoader />;
  }

  if (!photos || photos.length === 0) {
    return (
      <Stack direction="row" justifyContent="center" alignItems="center" height="100%">
        <Alert severity="error" sx={{ fontSize: 24, alignItems: 'center' }}>
          No photos found
        </Alert>
      </Stack>
    );
  }

  return (
    <>
      <Grid container spacing={2} sx={{ maxWidth: '100%', mt: 1 }}>
        {photos.map((photo, i) => (
          <Grid sx={{ xs: 6, sm: 4, md: 3, lg: 2 }} key={photo.id?.value}>
            {photo.signedUrl?.url && (
              <PhotoCard photoUrl={photo.signedUrl?.url} tags={photo.tags} onClick={() => handleClickPhotoCard(i)} />
            )}
          </Grid>
        ))}
      </Grid>
      <PhotoModal photos={photos} selectedIndex={selectedPhotoIndex} onClose={() => setSelectedPhotoIndex(-1)} />
    </>
  );
};
