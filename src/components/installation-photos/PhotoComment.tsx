import { Stack, Typography } from '@mui/material';
import { ReadMore } from '@ui/components/ReadMore/ReadMore';
import { CommentOutlinedIcon } from '@ui/components/StandardIcons/CommentOutlinedIcon';

const LABEL_COLOR = '#BBBCBF';

export const PhotoComment = ({ comment }: { comment: string }) => (
  <Stack direction="column" spacing={1} sx={{ maxWidth: '100%' }}>
    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', color: LABEL_COLOR }}>
      <CommentOutlinedIcon color={LABEL_COLOR} />
      <Typography variant="body2" sx={{ color: LABEL_COLOR }}>
        Comment
      </Typography>
    </label>
    <ReadMore text={comment} maxLength={100} sx={{ color: '#F9FAFA', fontSize: '14px', maxHeight: '200px' }} />
  </Stack>
);
