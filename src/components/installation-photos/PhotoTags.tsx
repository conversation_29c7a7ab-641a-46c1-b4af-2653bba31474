import { OnSitePhoto_Tag } from '@aira/on-site-dossier-grpc-api/build/ts_out/index.com.aira.acquisition.contract.on.site.dossier.v1';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';

const tagLabels: Record<OnSitePhoto_Tag, string> = {
  [OnSitePhoto_Tag.TAG_UNSPECIFIED]: 'Unspecified',
  [OnSitePhoto_Tag.TAG_OLD_SYSTEM]: 'Old System',
  [OnSitePhoto_Tag.TAG_INDOOR_UNITS]: 'Indoor Unit',
  [OnSitePhoto_Tag.TAG_OUTDOOR_UNIT]: 'Outdoor Unit',
  [OnSitePhoto_Tag.TAG_THERMOSTAT]: 'Thermostat',
  [OnSitePhoto_Tag.TAG_ELECTRICS]: 'Electrics',
  [OnSitePhoto_Tag.TAG_LABELS_SERIAL_NUMBERS]: 'Labels & Serial Numbers',
  [OnSitePhoto_Tag.TAG_PIPEWORK]: 'Pipework',
  [OnSitePhoto_Tag.TAG_RADIATOR]: 'Radiator',
  [OnSitePhoto_Tag.TAG_CERTIFICATES]: 'Certificates',
  [OnSitePhoto_Tag.TAG_COMPONENTS]: 'Components',
  [OnSitePhoto_Tag.UNRECOGNIZED]: 'Unrecognized',
};

export const PhotoTags = ({ tags, darkMode = false }: { tags: OnSitePhoto_Tag[]; darkMode?: boolean }) => (
  <Stack direction="row" flexWrap="wrap" sx={{ maxWidth: '100%' }}>
    {tags.map((tag) => (
      <Chip
        key={tag}
        label={tagLabels[tag]}
        color={darkMode ? 'primary' : 'default'}
        sx={{
          fontFamily: 'AiraText',
          background: darkMode ? 'rgba(249, 250, 250, 0.06)' : 'rgba(34, 34, 38, 0.03)',
          padding: '8px',
          margin: '4px',
        }}
      />
    ))}
  </Stack>
);
