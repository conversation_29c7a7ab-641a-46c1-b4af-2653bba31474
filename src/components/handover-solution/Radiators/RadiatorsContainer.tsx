import { I<PERSON><PERSON><PERSON><PERSON>, Stack, Typography, useMediaQuery } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { useIntl } from 'react-intl';
import { ChevronRight } from '@ui/components/Icons/Chevron/Chevron';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { useState } from 'react';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { RoomPlanProps, useFloors } from '../queries/useHeatDesign';
import { useInstallationGroundworkIdContext } from '../Contexts/InstallationGroundworkIdContext';
import RoomPanel from './RoomPanel';
import { MOBILE_NAV_BAR_PLUS_TAB_BAR_HEIGHT, PADDING_AFTER_TAB_BAR, PADDING_AFTER_TAB_BAR_MOBILE } from '../constants';

const TAB_BAR_TO_START_OF_CARD = 30;
const FLOOR_HEADER_HEIGHT = 60;

export default function RadiatorsContainer() {
  const intl = useIntl();
  const solutionId = useEnergySolutionId();
  const installationGroundworkId = useInstallationGroundworkIdContext();
  const [selectedRoom, setSelectedRoom] = useState<RoomPlanProps | undefined>(undefined);
  const { data: floors, isLoading } = useFloors({
    installationGroundworkId,
    solutionId,
  });
  const isMobile = useMediaQuery('(max-width: 700px)');

  if (isLoading)
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );

  if (!floors) return null;

  const renderRooms = (rooms: RoomPlanProps[]) =>
    rooms.map((room) => (
      <Stack key={room.id}>
        <Stack
          flexDirection="row"
          justifyContent="space-between"
          alignItems="center"
          sx={{
            background: beige[150],
            padding: '16px 0',
            borderBottom: '1px solid #0000001f',
          }}
          onClick={() => setSelectedRoom(room)}
        >
          <Typography variant="headline3" pl={2}>
            {room.name}
          </Typography>
          <IconButton>
            <ChevronRight width={16} height={16} />
          </IconButton>
        </Stack>
      </Stack>
    ));

  const renderFloors = (level: number) => {
    const floor = floors[level];
    if (!floor) return null;
    return (
      <Stack key={floor.id}>
        <Stack
          sx={{
            position: isMobile ? 'sticky' : 'static',
            top: `${MOBILE_NAV_BAR_PLUS_TAB_BAR_HEIGHT + TAB_BAR_TO_START_OF_CARD + FLOOR_HEADER_HEIGHT * level}px`,
            background: beige[150],
            padding: '22px 0',
            borderBottom: '1px solid #0000001f',
            borderRadius: level === 0 ? '22px 22px 0 0' : '0',
            zIndex: 2,
          }}
        >
          <Typography variant="headline3" color="textSecondary">
            {floor.name}
          </Typography>
        </Stack>
        <Stack>{renderRooms(floor.rooms)}</Stack>
        <Stack>{renderFloors(level + 1)}</Stack>
      </Stack>
    );
  };

  return (
    <Stack
      sx={{
        paddingTop: `${isMobile ? PADDING_AFTER_TAB_BAR_MOBILE : PADDING_AFTER_TAB_BAR}px`,
      }}
    >
      {selectedRoom ? (
        <Stack
          sx={{
            overflow: 'hidden',
          }}
        >
          <RoomPanel room={selectedRoom} setSelectedRoom={() => setSelectedRoom(undefined)} />
        </Stack>
      ) : (
        <Stack
          sx={{
            background: beige[150],
            width: '100%',
            borderRadius: '22px',
            top: 0,
          }}
          px={3}
        >
          {isMobile && (
            <>
              <Stack
                sx={{
                  position: 'fixed',
                  top: `${MOBILE_NAV_BAR_PLUS_TAB_BAR_HEIGHT + TAB_BAR_TO_START_OF_CARD}px`,
                  height: '28px',
                  left: '24px',
                  background: beige[150],
                  borderRadius: '22px 22px 0 0',
                  zIndex: 5,
                  width: isMobile ? 'calc(100% - 48px)' : '756px',
                }}
              />
              <Stack
                sx={{
                  position: 'fixed',
                  top: `${MOBILE_NAV_BAR_PLUS_TAB_BAR_HEIGHT}px`,
                  left: 0,
                  display: 'block',
                  height: '44px',
                  width: '100%',
                  background: beige[150],
                  zIndex: 3,
                }}
              />
            </>
          )}
          {renderFloors(0)}
        </Stack>
      )}
    </Stack>
  );
}
