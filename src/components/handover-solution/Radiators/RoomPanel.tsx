import { IconButton, Stack, Typography, useMediaQuery } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { ChevronLeft } from '@ui/components/Icons/Chevron/Chevron';
import { FormattedMessage } from 'react-intl';
import { RoomPlanProps } from '../queries/useHeatDesign';
import RadiatorInfoRenderer from '../ObjectDetailsPanel/RadiatorInfoRenderer';

export default function RoomPanel({
  room,
  setSelectedRoom,
  isFloorPlanSelection = false,
}: {
  room: RoomPlanProps;
  setSelectedRoom: () => void;
  isFloorPlanSelection?: boolean;
}) {
  const isMobile = useMediaQuery('(max-width: 700px)');

  const enabledExistingRadiators = room.radiators.filter((radiator) => radiator.enabled && !radiator.toBeInstalled);
  const enabledNewRadiators = room.radiators.filter((radiator) => radiator.toBeInstalled && radiator.enabled);
  const disabledRadiators = room.radiators.filter((radiator) => !radiator.enabled && !radiator.toBeInstalled);

  return (
    <Stack
      key={room.id}
      sx={{
        width: isMobile ? 'calc(100vw - 48px)' : '100%',
        height: '100%',
        background: 'white',
        padding: '24px',
        borderRadius: '22px',
      }}
    >
      {!isFloorPlanSelection && (
        <Stack flexDirection="row" justifyContent="flex-start" alignItems="center" gap={2} mb={2} pb={2}>
          <IconButton
            onClick={setSelectedRoom}
            sx={{
              background: grey[150],
            }}
          >
            <ChevronLeft width={16} height={16} />
          </IconButton>
          <Typography variant="headline3">{room.name}</Typography>
        </Stack>
      )}

      <Stack
        sx={{
          background: 'white',
        }}
      >
        <Typography variant="h6">
          <FormattedMessage id="installationHandover.radiators.existingRadiators" defaultMessage="Existing radiators" />
        </Typography>
        {enabledExistingRadiators.length > 0 ? (
          <Stack px={2}>
            {enabledExistingRadiators.map((radiator, radiatorIndex) => (
              <Stack key={radiator.id}>
                <Typography
                  variant="headline3"
                  sx={{
                    borderBottom: '1px solid #0000001f',
                    padding: '16px 0',
                  }}
                >
                  Existing radiator {radiatorIndex + 1}
                </Typography>
                <Stack pl={2}>
                  <RadiatorInfoRenderer radiator={radiator} showingRoomRadiator />
                </Stack>
              </Stack>
            ))}
          </Stack>
        ) : (
          <Stack pt={4}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationHandover.radiators.noExisitingRadiatorsInRoom"
                defaultMessage="There are no existing radiators in this room"
              />
            </Typography>
          </Stack>
        )}
        <Typography variant="h6" mt={4}>
          <FormattedMessage
            id="installationHandover.radiators.newRadiators"
            defaultMessage="New radiators to be installed"
          />
        </Typography>
        {enabledNewRadiators.length > 0 ? (
          <Stack px={2}>
            {enabledNewRadiators.map((radiator, radiatorIndex) => (
              <Stack key={radiator.id}>
                <Typography
                  variant="headline3"
                  sx={{
                    borderBottom: '1px solid #0000001f',
                    padding: '16px 0',
                  }}
                >
                  New radiator {radiatorIndex + 1}
                </Typography>
                <Stack pl={2}>
                  <RadiatorInfoRenderer radiator={radiator} showingRoomRadiator />
                </Stack>
              </Stack>
            ))}
          </Stack>
        ) : (
          <Stack pt={4}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationHandover.radiators.noNewRadiatorsInRoom"
                defaultMessage="There are no new radiators to be installed in this room"
              />
            </Typography>
          </Stack>
        )}
        <Typography variant="h6" mt={4}>
          <FormattedMessage id="installationHandover.radiators.removedRadiators" defaultMessage="Removed radiators" />
        </Typography>
        {disabledRadiators.length > 0 ? (
          <Stack px={2}>
            {disabledRadiators.map((radiator, radiatorIndex) => (
              <Stack key={radiator.id}>
                <Typography
                  variant="headline3"
                  sx={{
                    borderBottom: '1px solid #0000001f',
                    padding: '16px 0',
                  }}
                >
                  Removed radiator {radiatorIndex + 1}
                </Typography>
                <Stack pl={2}>
                  <RadiatorInfoRenderer radiator={radiator} showingRoomRadiator />
                </Stack>
              </Stack>
            ))}
          </Stack>
        ) : (
          <Stack pt={4}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationHandover.radiators.noRadiatorsToRemoveInRoom"
                defaultMessage="There are no radiators to be removed from this room"
              />
            </Typography>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}
