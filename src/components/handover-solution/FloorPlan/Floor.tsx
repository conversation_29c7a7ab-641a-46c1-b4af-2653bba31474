import { Stack, useMediaQuery } from '@mui/material';
import { MapInteractionCSS } from 'react-map-interaction';
import { useEffect, useRef } from 'react';
import { ImageMap } from 'components/heat-design/stores/types';
import DOMPurify from 'dompurify';
import { useObjectActions, useSelectedObject, useSelectedObjectType } from '../stores/ObjectStore';
import FloorPlanFilter from './FloorPlanFilter';
import { FloorPlanProps, RadiatorProps, RoomPlanProps } from '../queries/useHeatDesign';

export default function Floor({
  floor,
  radiatorsOnFloor,
}: {
  floor: FloorPlanProps;
  radiatorsOnFloor: RadiatorProps[];
}) {
  const isMobile = useMediaQuery('(max-width: 700px)');
  const selectedObject = useSelectedObject();
  const selectedObjectType = useSelectedObjectType();
  const { setSelectedObject, clearSelectedObject, setSelectedObjectType } = useObjectActions();

  const svgWrapperRef = useRef(null);

  useEffect(() => {
    if (svgWrapperRef.current !== null) {
      // DomPurify is used to sanitize the SVG data to prevent xss attacks
      const cleanSVG = DOMPurify.sanitize(floor.svgImageData);
      (svgWrapperRef.current as HTMLDivElement).innerHTML = cleanSVG;
    }
  }, [floor.svgImageData]);

  const selectedObjectImageMap = (): ImageMap['coordinates'] => {
    if (!selectedObject) return [];
    if (selectedObjectType === 'Radiator') {
      const selectedRadiator = radiatorsOnFloor.find((radiator) => radiator.id === selectedObject?.id);
      if (!selectedRadiator) return [];
      return selectedObject.floorImageMap.coordinates;
    }

    const selectedRoom = floor.rooms.find((room: RoomPlanProps) => room.id === selectedObject?.id);
    if (selectedRoom) {
      return selectedRoom.imageMap.coordinates;
    }
    return [];
  };

  return (
    <Stack
      sx={{
        background: '#22222608',
        padding: '24px',
        borderRadius: '12px',
        minWidth: '260px',
        width: isMobile ? '100%' : '756px',
        maxWidth: '756px',
        overflow: 'hidden',
        boxShadow: '0px 15px 15.8px 0px #00000040',
      }}
      key={floor.id}
    >
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        sx={{
          padding: '0px',
          width: isMobile ? '100%' : '756px',
          margin: 'auto !important',
          height: isMobile ? '400px' : '500px',
          overflow: 'hidden',
        }}
        onClick={(e) => {
          if (!e.defaultPrevented) clearSelectedObject();
        }}
        onTouchEnd={(e) => {
          if (!e.defaultPrevented) clearSelectedObject();
        }}
      >
        <MapInteractionCSS
          defaultValue={{ scale: isMobile ? 0.4 : 0.5, translation: { x: isMobile ? -60 : 90, y: isMobile ? 20 : 0 } }}
        >
          <Stack
            sx={{
              alignItems: 'center',
              position: 'relative',
              width: '1024px',
              height: '1024px',
              overflow: 'hidden',
            }}
          >
            <div ref={svgWrapperRef} />

            {selectedObjectImageMap().length > 0 && (
              <Stack
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '1024px',
                  height: '1024px',
                }}
              >
                {FloorPlanFilter({ coordinates: selectedObjectImageMap() })}
              </Stack>
            )}
            <Stack
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                height: '1024px',
                width: '1024px',
              }}
            >
              <svg width={1024} height={1024}>
                {floor.rooms.map((room: RoomPlanProps) => {
                  const roomImageMap = room.imageMap;
                  const coords = (roomImageMap.coordinates || []).map((coord) => coord);
                  return (
                    <polygon
                      key={room.id}
                      id={room.id}
                      style={{
                        fill: '#00000000',
                      }}
                      points={coords.toString()}
                      onClick={(e) => {
                        if (!e.defaultPrevented) {
                          e.stopPropagation();
                          setSelectedObject(room);
                          setSelectedObjectType(room.name);
                        }
                      }}
                      onTouchEnd={(e) => {
                        if (!e.defaultPrevented && e.touches.length < 1) {
                          e.stopPropagation();
                          setSelectedObject(room);
                          setSelectedObjectType(room.name);
                        }
                      }}
                    />
                  );
                })}
                {radiatorsOnFloor.map((radiator) => {
                  const coords = radiator.floorImageMap.coordinates;
                  const isSelected = true;
                  return (
                    coords.length !== 0 && (
                      <polygon
                        key={radiator.id}
                        className={isSelected ? 'radiator selected' : 'radiator'}
                        points={coords.toString()}
                        onClick={(e) => {
                          if (!e.defaultPrevented) {
                            e.stopPropagation();
                            setSelectedObject(radiator);
                            setSelectedObjectType('Radiator');
                          }
                        }}
                        onTouchEnd={(e) => {
                          if (!e.defaultPrevented && e.touches.length < 1) {
                            e.stopPropagation();
                            setSelectedObject(radiator);
                            setSelectedObjectType('Radiator');
                          }
                        }}
                      />
                    )
                  );
                })}
              </svg>
            </Stack>
          </Stack>
        </MapInteractionCSS>
      </Stack>
    </Stack>
  );
}
