import { TRPC_ERROR_CODE_KEY } from '@trpc/server/rpc';
import React, { useState } from 'react';
import { TRPCClientError } from '@trpc/client';
import { Stack, Typography, useMediaQuery } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { FormattedMessage, useIntl } from 'react-intl';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useFloors } from '../queries/useHeatDesign';
import FloorPlan from './FloorPlan';
import ObjectDetailsPanel from '../ObjectDetailsPanel/ObjectDetailsPanel';
import { useSelectedObject, useShowingDetails } from '../stores/ObjectStore';
import { useInstallationGroundworkIdContext } from '../Contexts/InstallationGroundworkIdContext';
import { PADDING_AFTER_TAB_BAR, PADDING_AFTER_TAB_BAR_MOBILE } from '../constants';

export default function FloorPlanContainer() {
  const isMobile = useMediaQuery('(max-width: 700px)');
  const intl = useIntl();
  const selectedObject = useSelectedObject();
  const showObjectDetails = useShowingDetails();
  const installationGroundworkId = useInstallationGroundworkIdContext();
  const solutionId = useEnergySolutionId();
  const [loadError, setLoadError] = useState<TRPC_ERROR_CODE_KEY | undefined>(undefined);
  const {
    isLoading,
    error,
    data: floors,
  } = useFloors({
    installationGroundworkId,
    solutionId,
  });

  const loader = (
    <Stack
      sx={{
        width: '100%',
        height: 'calc(100vh - 240px)',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
      <HeatPump />
    </Stack>
  );

  if (error) {
    if (error instanceof TRPCClientError) {
      setLoadError(error.data.code as TRPC_ERROR_CODE_KEY);
    }
    setLoadError('INTERNAL_SERVER_ERROR');
  }

  return (
    <Stack
      position="relative"
      sx={{
        paddingTop: `${isMobile ? PADDING_AFTER_TAB_BAR_MOBILE : PADDING_AFTER_TAB_BAR}px`,
      }}
    >
      {isLoading && loader}
      {loadError && (
        <>
          <Typography>
            <FormattedMessage id="heatDesign.error.cannotRetrieveMagicplanProject" />
          </Typography>
          <Typography>
            <FormattedMessage id="common.error.contactDevelopmentTeam" />
          </Typography>
          <Card variant="outlined" style={{ marginTop: '2em', fontSize: '0.9em' }}>
            <code>Solution ID: {solutionId}</code>
            <br />
            <code>Installation Groundwork ID: {installationGroundworkId}</code>
          </Card>
        </>
      )}
      {selectedObject && showObjectDetails && <ObjectDetailsPanel />}
      {!showObjectDetails && floors && <FloorPlan />}
    </Stack>
  );
}
