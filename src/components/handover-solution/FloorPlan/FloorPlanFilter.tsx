import { ImageMap } from 'components/heat-design/stores/types';

export default function FloorPlanFilter({ coordinates }: { coordinates: ImageMap['coordinates'] }) {
  const points = coordinates.join(', ');
  return (
    <svg width="1024" height="1024" viewBox="0 0 1024 1024">
      <polygon points={points} fill="none" stroke="black" strokeWidth="20px" />
      <defs>
        <mask id="mask">
          <rect width="1024" height="1024" fill="#EDEFF0" />
          <polygon points={points} fill="black" />
        </mask>
        <filter id="transparentFilter" x="0" y="0" width="1" height="1">
          <feComponentTransfer in="SourceGraphic">
            <feFuncA type="table" tableValues="0 0.8" />
          </feComponentTransfer>
        </filter>
      </defs>
      <rect width="1024" height="1024" fill="#EDEFF0" mask="url(#mask)" filter="url(#transparentFilter)" />
    </svg>
  );
}
