import { Stack, Typography, useMediaQuery } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import {
  MAGICPLAN_SYMBOL_BUFFER_TANK,
  MAGICPLAN_SYMBOL_CYLINDER,
  MAGICPLAN_SYMBOL_EXPANSION_VESSEL,
  MAGICPLAN_SYMBOL_OUTDOOR_UNIT,
  MAGICPLAN_SYMBOL_THERMOSTAT,
} from 'components/heat-design/constants';

const SYMBOLS = [
  MAGICPLAN_SYMBOL_OUTDOOR_UNIT,
  MAGICPLAN_SYMBOL_CYLINDER,
  MAGICPLAN_SYMBOL_BUFFER_TANK,
  MAGICPLAN_SYMBOL_EXPANSION_VESSEL,
  MAGICPLAN_SYMBOL_THERMOSTAT,
];

export default function FloorSymbols() {
  const isMobile = useMediaQuery('(max-width: 700px)');

  return (
    <Stack
      direction="row"
      justifyContent={isMobile ? 'space-between' : 'center'}
      alignItems="center"
      sx={{
        background: '#22222608',
        padding: '16px 20px',
        borderRadius: '12px',
        width: '756px',
        maxWidth: 'calc(100vw - 40px)',
        minHeight: '40px',
        flexWrap: 'wrap',
        rowGap: '16px',
        boxShadow: '0px 15px 15.8px 0px #00000040',
      }}
      spacing={2}
      gap={2}
    >
      {SYMBOLS.map((symbol) => (
        <Stack
          direction="row"
          justifyContent="center"
          alignItems="center"
          gap={1}
          sx={{ marginLeft: '0 !important' }}
          key={symbol.color}
        >
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: symbol.color,
            }}
          />
          <Typography
            variant="body2"
            sx={{
              whiteSpace: 'nowrap',
            }}
          >
            <FormattedMessage id={symbol.key} />
          </Typography>
        </Stack>
      ))}
    </Stack>
  );
}
