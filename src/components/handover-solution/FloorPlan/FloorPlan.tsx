import { useEffect, useState } from 'react';
import {
  CircularProgress,
  Select,
  MenuItem,
  Stack,
  Typography,
  Box,
  useMediaQuery,
  type SelectChangeEvent,
} from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { LayersIcon } from '@ui/components/Icons/LayersIcon/LayersIcon';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import FloorSymbols from './FloorSymbols';
import { FloorPlanProps, RoomPlanProps, useFloors } from '../queries/useHeatDesign';
import Floor from './Floor';
import { useObjectActions, useSelectedObject } from '../stores/ObjectStore';
import InfoBadge from '../ObjectDetailsPanel/InfoBadge';
import { useInstallationGroundworkIdContext } from '../Contexts/InstallationGroundworkIdContext';

export default function FloorPlan() {
  const selectedObject = useSelectedObject();
  const { clearSelectedObject } = useObjectActions();
  const [selectedFloor, setSelectedFloor] = useState<FloorPlanProps | undefined>(undefined);
  const installationGroundworkId = useInstallationGroundworkIdContext();
  const solutionId = useEnergySolutionId();
  const { data: floors, isLoading: isLoadingFloors } = useFloors({
    installationGroundworkId,
    solutionId,
  });

  const radiatorsOnFloor = () => {
    if (!selectedFloor) return [];
    return selectedFloor?.rooms?.flatMap((room: RoomPlanProps) => room.radiators);
  };

  const isMobile = useMediaQuery('(max-width: 700px)');

  useEffect(() => {
    if (!selectedFloor && floors && floors.length > 0) {
      setSelectedFloor(floors[0]);
    }
  }, [floors, selectedFloor]);

  const handleSelectedFloorChange = (e: SelectChangeEvent<string>) => {
    setSelectedFloor(floors.find((floor: { id: any }) => floor.id === e.target.value));
    clearSelectedObject();
  };

  if (!floors || floors.length === 0) {
    return (
      <Typography variant="body1" color="error">
        <FormattedMessage id="floorPlans.error.noFloorsInProject" />
      </Typography>
    );
  }

  if (isLoadingFloors) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: '300px',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    );
  }

  if (!selectedFloor) {
    return null;
  }

  return (
    <Stack>
      <Stack direction="column" justifyContent="space-between" spacing={2} alignItems="center" width="100%">
        <Stack direction="row" mb={2} width={isMobile ? '100%' : '756px'} justifyContent="space-between">
          <Select
            labelId="custom-select-label"
            name="floor"
            id="floor-select"
            value={selectedFloor.id}
            displayEmpty
            onChange={handleSelectedFloorChange}
            size="small"
            sx={{
              display: 'flex',
              alignItems: 'center',
              borderRadius: '33px',
              whiteSpace: 'nowrap',
              flexShrink: 0,
              '&.MuiOutlinedInput-root': {
                backgroundColor: '#22222608',
                '& fieldset': {
                  border: 'none',
                },
              },
              '& .MuiSelect-icon': {
                display: 'none',
              },
            }}
            renderValue={(selected) => {
              if (!selected) {
                return <Typography>None</Typography>;
              }

              return (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      marginRight: '8px',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <LayersIcon />
                  </Box>
                  <Typography>{selectedFloor.name}</Typography>
                </Box>
              );
            }}
          >
            {floors.map((floor: FloorPlanProps) => (
              <MenuItem key={floor.id} value={floor.id}>
                {floor.name}
              </MenuItem>
            ))}
          </Select>

          {selectedObject && (
            <Stack
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <InfoBadge />
            </Stack>
          )}
        </Stack>
        {selectedFloor && <Floor floor={selectedFloor} radiatorsOnFloor={radiatorsOnFloor()} />}
      </Stack>
      <Stack direction="column" justifyContent="space-between" spacing={4} alignItems="center" width="100%" mt={2}>
        <FloorSymbols />
        <Typography
          variant="body2"
          color="textSecondary"
          sx={{
            paddingLeft: '8px',
            borderLeft: '1px solid #A0A2A6',
          }}
        >
          <FormattedMessage
            id="installationHandover.floorPlans.disclaimer"
            defaultMessage="Not all objects listed may be in the floor plans. Only objects added during the survey are shown."
          />
        </Typography>
      </Stack>
    </Stack>
  );
}
