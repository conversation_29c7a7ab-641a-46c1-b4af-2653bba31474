import { Stack } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { Modal } from '@ui/components/Modal/Modal';
import { useState } from 'react';
import { ImageWithSkeleton } from '../ImageWithSkeleton';

export default function ObjectPhotos({ photos }: { photos: { id: string }[] }) {
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  return (
    <Stack
      direction="row"
      sx={{
        padding: '12px',
        borderRadius: '22px',
        background: grey[150],
      }}
      gap={1}
      flexWrap="wrap"
      justifyContent="flex-start"
      mt={3}
    >
      {photos.map(({ id }) => (
        <div
          onClick={() => setSelectedPhoto(id)}
          onKeyDown={() => {}}
          role="button"
          tabIndex={0}
          key={id}
          aria-label="object photo"
        >
          <ImageWithSkeleton
            image={{
              src: id,
              width: 75,
              height: 75,
              alt: 'object photo',
            }}
          />
        </div>
      ))}
      <Modal
        isModalOpen={!!selectedPhoto}
        handleClose={() => setSelectedPhoto(null)}
        height="100dvh"
        width="100vw"
        darkBackground
        showCloseButton
        sx={{
          borderRadius: '0',
          padding: '0',
        }}
      >
        {selectedPhoto && (
          // This is the image that will be displayed in the modal
          // eslint-disable-next-line @next/next/no-img-element
          <img
            src={selectedPhoto}
            alt="object"
            style={{
              maxWidth: '100%',
              maxHeight: '100%',
              objectFit: 'contain',
            }}
          />
        )}
      </Modal>
    </Stack>
  );
}
