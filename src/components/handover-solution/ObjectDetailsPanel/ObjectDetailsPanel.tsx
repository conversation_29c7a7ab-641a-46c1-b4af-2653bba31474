import { Box, IconButton, Stack, Typography, useMediaQuery } from '@mui/material';
import { useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { ChevronLeft } from '@ui/components/Icons/Chevron/Chevron';
import { RadiatorIcon } from '@ui/components/Icons/RadiatorIcon/RadiatorIcon';
import { grey } from '@ui/theme/colors';
import { FormattedMessage } from 'react-intl';
import { useObjectActions, useSelectedObject, useSelectedObjectType } from '../stores/ObjectStore';
import RadiatorInfoPanel from './RadiatorInfoPanel';
import ObjectPhotos from './ObjectPhotos';
import RoomPanel from '../Radiators/RoomPanel';

export default function ObjectDetailsPanel() {
  const isMobile = useMediaQuery('(max-width: 700px)');
  const [tab, setTab] = useState('info');
  const selectedObjectType = useSelectedObjectType();
  const selectedObject = useSelectedObject();
  const { setShowDetails } = useObjectActions();

  const handleTabChange = (tabName: string) => {
    setTab(tabName);
  };

  return (
    <Stack
      sx={{
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: isMobile ? '100vw' : '100%',
        height: '100%',
        margin: '-24px -24px 16px -24px',
        padding: '0px 24px 24px 24px',
      }}
    >
      <Stack
        mb={2}
        sx={{
          width: '100%',
        }}
      >
        <Stack direction="row" justifyContent="flex-start" alignItems="center" width="100%" my={2} gap={2}>
          <IconButton
            aria-label="close object details"
            sx={{
              background: grey[200],
            }}
            onClick={() => setShowDetails(false)}
          >
            <ChevronLeft height={16} width={16} />
          </IconButton>
        </Stack>
        {selectedObjectType === 'Radiator' && (
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
            gap={1}
            sx={{
              background: 'transparent',
              padding: '6px 10px',
              width: '100%',
              borderRadius: '16px',
              border: '1px solid #222226',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '12px',

                background: 'black',
                position: 'absolute',
                top: '6px',
                left: tab === 'info' ? '10px' : 'calc(50% + 6px)',
                width: tab === 'info' ? 'calc(50% - 14px)' : 'calc(50% - 18px)',
                height: '40px',
                zIndex: 0,
              }}
            />
            <Button
              onClick={() => handleTabChange('info')}
              size="small"
              sx={{
                width: '50%',
                borderRadius: '12px',
                color: tab === 'info' ? '#fff' : 'black',
                background: 'transparent',
                zIndex: 1,
                '&:hover': {
                  backgroundColor: tab === 'info' ? 'black !important' : 'transparent',
                  textDecoration: tab === 'info' ? 'none' : 'underline',
                },
              }}
              color="white"
            >
              <FormattedMessage id="installationHandover.objectDetailsPanel.buttons.info" defaultMessage="Info" />
            </Button>
            <Button
              value="photos"
              size="small"
              onClick={() => handleTabChange('photos')}
              sx={{
                width: '50%',
                borderRadius: '12px',
                color: tab === 'photos' ? '#fff' : 'black',
                background: 'transparent',
                zIndex: 1,
                '&:hover': {
                  backgroundColor: tab === 'photos' ? 'black !important' : 'transparent',
                  textDecoration: tab === 'photos' ? 'none' : 'underline',
                },
              }}
              color="white"
            >
              <FormattedMessage id="installationHandover.objectDetailsPanel.buttons.photos" defaultMessage="Photos" />
            </Button>
          </Stack>
        )}

        {selectedObjectType === 'Radiator' && tab === 'info' && (
          <RadiatorInfoPanel>
            <Stack direction="row" justifyContent="space-between" alignItems="center" position="relative" gap={1}>
              <Typography variant="headline2" py={2}>
                {selectedObjectType === 'Radiator' ? (
                  <FormattedMessage id="installationHandover.objectDetailsPanel.radiator" defaultMessage="Radiator" />
                ) : (
                  selectedObjectType
                )}
              </Typography>
              <RadiatorIcon />
            </Stack>
          </RadiatorInfoPanel>
        )}
        {selectedObjectType !== 'Radiator' && tab === 'info' && (
          <>
            <Stack direction="row" justifyContent="space-between" alignItems="center" position="relative" gap={1}>
              <Typography variant="headline2" py={2}>
                {selectedObjectType === 'Radiator' ? (
                  <FormattedMessage id="installationHandover.objectDetailsPanel.radiator" defaultMessage="Radiator" />
                ) : (
                  selectedObjectType
                )}
              </Typography>
              <RadiatorIcon />
            </Stack>
            <RoomPanel room={selectedObject} setSelectedRoom={() => {}} isFloorPlanSelection />
          </>
        )}
        {tab === 'photos' && <ObjectPhotos photos={[]} />}
      </Stack>
    </Stack>
  );
}
