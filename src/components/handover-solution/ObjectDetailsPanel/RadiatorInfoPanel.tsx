import React, { ReactNode } from 'react';
import { Stack } from '@mui/material';
import { RadiatorProps } from '../queries/useHeatDesign';
import { useSelectedObject } from '../stores/ObjectStore';
import RadiatorInfoRenderer from './RadiatorInfoRenderer';

export default function RadiatorInfoPanel({ children }: { children: ReactNode }) {
  const radiator: RadiatorProps = useSelectedObject();

  return (
    <Stack
      mt={2}
      width="100%"
      sx={{
        background: 'white',
        padding: '24px',
        borderRadius: '22px',
      }}
    >
      {children}
      <RadiatorInfoRenderer radiator={radiator} />
    </Stack>
  );
}
