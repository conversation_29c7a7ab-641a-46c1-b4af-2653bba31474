import React from 'react';
import { Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { RadiatorProps } from '../queries/useHeatDesign';

export default function RadiatorInfoRenderer({
  radiator,
  showingRoomRadiator = false,
}: {
  radiator: RadiatorProps;
  showingRoomRadiator?: boolean;
}) {
  const intl = useIntl();

  const radiatorInfo = [
    {
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.comment',
        defaultMessage: 'Comment:',
      }),
      value: radiator.comment,
    },
    {
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.height',
        defaultMessage: 'Height:',
      }),
      value: radiator.heightM,
    },
    {
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.width',
        defaultMessage: 'Width:',
      }),
      value: radiator.widthM,
    },
  ];

  if (!showingRoomRadiator) {
    radiatorInfo.unshift({
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.inRoom',
        defaultMessage: 'In Room:',
      }),
      value: radiator.roomName,
    });
  }

  const radiatorType = radiator.radiatorDetails?.details?.$case;

  if (radiatorType === 'waterRadiatorDetails') {
    radiatorInfo.push({
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.radiatorType',
        defaultMessage: 'Radiator Type:',
      }),
      value: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.waterRadiator',
        defaultMessage: 'Water Radiator',
      }),
    });
    if (radiator?.radiatorDetails?.details?.waterRadiatorDetails?.nominalOutput) {
      radiatorInfo.push({
        formattedMessage: intl.formatMessage({
          id: 'installationHandover.radiatorInfoPanel.outputWatts',
          defaultMessage: 'Output[W]:',
        }),
        value: radiator.radiatorDetails.details.waterRadiatorDetails.nominalOutput.outputWatt,
      });
      radiatorInfo.push({
        formattedMessage: intl.formatMessage({
          id: 'installationHandover.radiatorInfoPanel.deltaT',
          defaultMessage: 'DeltaT:',
        }),
        value: radiator.radiatorDetails.details.waterRadiatorDetails.nominalOutput.deltaT,
      });
    }
  }

  if (radiatorType === 'electricRadiatorDetails') {
    radiatorInfo.push({
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.radiatorType',
        defaultMessage: 'Radiator Type:',
      }),
      value: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.electricRadiator',
        defaultMessage: 'Electric Radiator',
      }),
    });
    if (radiator?.radiatorDetails?.details?.electricRadiatorDetails?.outputWatt) {
      radiatorInfo.push({
        formattedMessage: intl.formatMessage({
          id: 'installationHandover.radiatorInfoPanel.outputWatts',
          defaultMessage: 'Output[W]:',
        }),
        value: radiator.radiatorDetails.details.electricRadiatorDetails.outputWatt,
      });
    }
  }

  if (!showingRoomRadiator) {
    radiatorInfo.unshift({
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.enabled',
        defaultMessage: 'Enabled?:',
      }),
      value: radiator.enabled
        ? intl.formatMessage({
            id: 'common.yes',
            defaultMessage: 'Yes',
          })
        : intl.formatMessage({
            id: 'common.no',
            defaultMessage: 'No',
          }),
    });
    radiatorInfo.unshift({
      formattedMessage: intl.formatMessage({
        id: 'installationHandover.radiatorInfoPanel.toBeInstalled',
        defaultMessage: 'To be Installed?:',
      }),
      value: radiator.toBeInstalled
        ? intl.formatMessage({
            id: 'common.yes',
            defaultMessage: 'Yes',
          })
        : intl.formatMessage({
            id: 'common.no',
            defaultMessage: 'No',
          }),
    });
  }

  return (
    <>
      {radiatorInfo.map((info) => (
        <Stack key={info.formattedMessage} spacing={0} py={2} sx={{ borderBottom: '1px solid #00000020' }}>
          <Typography variant="body1Emphasis">{info.formattedMessage}</Typography>
          <Typography variant="body1">{info.value}</Typography>
        </Stack>
      ))}
    </>
  );
}
