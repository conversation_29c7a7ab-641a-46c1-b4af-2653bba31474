import { Chip, Stack } from '@mui/material';
import React from 'react';
import { InfoIcon } from '@ui/components/StandardIcons/InfoIcon';
import { FormattedMessage } from 'react-intl';
import { grey } from '@ui/theme/colors';
import { useObjectActions, useSelectedObjectType } from '../stores/ObjectStore';

export default function InfoBadge() {
  const selectedObjectType = useSelectedObjectType();
  const { setShowDetails } = useObjectActions();

  const label =
    selectedObjectType === 'Radiator' ? (
      <FormattedMessage id="installationHandover.radiator" defaultMessage="Radiator" />
    ) : (
      selectedObjectType
    );
  return (
    <Stack onClick={() => setShowDetails(true)}>
      <Chip
        label={label}
        icon={<InfoIcon height={18} color={grey[900]} />}
        clickable
        sx={{
          borderRadius: '33px',
          fontSize: '16px',
          height: '32px',
          padding: '8px 14px 8px 12px',
          fontFamily: 'AiraText',
          backgroundColor: '#FFAF51',
          '& .MuiChip-icon': {
            marginRight: '5px',
            marginLeft: '0px',
          },
          '.MuiChip-label': {
            padding: '0px',
            lineHeight: '18px',
            color: grey[900],
          },
        }}
      />
    </Stack>
  );
}
