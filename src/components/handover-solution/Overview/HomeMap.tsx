import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-defaulticon-compatibility';
import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css';
import { Geometry } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';
import { useMediaQuery } from '@mui/system';

function HomeMap({ position, height, zoom }: { position: Geometry; height?: number; zoom?: number }) {
  const pos = { lat: position.lat, lng: position.long };
  const isMobile = useMediaQuery('(max-width: 700px)');

  return (
    <MapContainer
      key={`${pos.lat}-${pos.lng}`}
      center={pos}
      zoom={zoom || 16}
      zoomControl={false}
      style={{ height: `${height || 124}px`, width: isMobile ? 'calc(100vw - 50px' : '100%', borderRadius: '16px' }}
      attributionControl={false}
    >
      <TileLayer attribution="" url="https://www.google.com/maps/vt?lyrs=m@189&gl=cn&x={x}&y={y}&z={z}" />
      <Marker position={pos} />
    </MapContainer>
  );
}

export default HomeMap;
