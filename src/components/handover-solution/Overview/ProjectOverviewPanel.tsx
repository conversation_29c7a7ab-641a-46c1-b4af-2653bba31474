import { EnergySolutionProduct } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { Stack, Typography } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { FormattedMessage } from 'react-intl';

export default function ProjectOverviewPanel({ productData }: { productData: EnergySolutionProduct[] }) {
  return (
    <Stack
      spacing={2}
      sx={{
        width: '100%',
        padding: '24px',
        borderRadius: '22px',
        background: '#22222608',
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="flex-end" gap={1}>
        <HousePersonOutsideIcon width={20} height={20} />
        <Heading level={3} variant="headline4" color="textSecondary">
          <FormattedMessage
            id="installationHandover.projectOveriew.title.mainTitle"
            defaultMessage="Project Overview"
          />
        </Heading>
      </Stack>
      <Stack
        spacing={1}
        justifyContent="space-between"
        direction="row"
        sx={{
          marginBottom: '0px !important',
        }}
      >
        <Heading level={4} variant="body2" color="textSecondary">
          <FormattedMessage id="installationHandover.projectOveriew.title.product" defaultMessage="Product" />
        </Heading>
        <Heading level={4} variant="body2" color="textSecondary">
          <FormattedMessage id="installationHandover.projectOveriew.title.quantity" defaultMessage="Quantity" />
        </Heading>
      </Stack>

      {productData.map((product) => (
        <Stack
          key={product.productId?.value}
          justifyContent="space-between"
          direction="row"
          sx={{
            marginTop: '8px !important',
          }}
        >
          <Typography
            variant="body1"
            sx={{
              maxWidth: '230px',
            }}
          >
            {product.displayName}
          </Typography>
          <Typography variant="body1">{product.quantity}</Typography>
        </Stack>
      ))}
    </Stack>
  );
}
