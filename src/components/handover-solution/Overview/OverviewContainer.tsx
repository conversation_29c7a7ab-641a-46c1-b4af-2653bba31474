import { Stack, Typography, useMediaQuery } from '@mui/material';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { api } from 'utils/api';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useIntl } from 'react-intl';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import ProjectOverviewPanel from './ProjectOverviewPanel';
import { PADDING_AFTER_TAB_BAR, PADDING_AFTER_TAB_BAR_MOBILE } from '../constants';
import PeoplePanel from './PeoplePanel';

// Dynamically import the MapComponent without SSR
const HomeMap = dynamic(() => import('./HomeMap'), { ssr: false });

export default function OverviewContainer() {
  const intl = useIntl();
  const solutionId = useEnergySolutionId();
  const { data: energySolution, isLoading: isLoadingEnergySolution } = api.AiraBackend.getGrpcEnergySolution.useQuery({
    solution: solutionId!,
  });
  const { data: installationProject, isLoading: isLoadingInstallationProject } =
    api.InstallationProject.getInstallationProject.useQuery({
      solutionId: solutionId!,
      withResourceDetails: true,
    });

  const [isClient, setIsClient] = useState(false);
  const isMobile = useMediaQuery('(max-width: 700px)');

  const position =
    energySolution?.solution?.presentation?.location?.$case === 'exactAddress'
      ? energySolution.solution.presentation.location.exactAddress.geometry
      : undefined;
  const productData = energySolution?.solution?.presentation?.products;

  useEffect(() => {
    // Set to true when the component is mounted on the client
    setIsClient(true);
  }, []);

  if (!isClient || isLoadingEnergySolution || isLoadingInstallationProject) {
    // Render a loading state or nothing until the client-side rendering happens
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  const installationProjectWithResourceDetails = installationProject as unknown as InstallationProject & {
    assignedResourcesDetails: UserIdentity[];
  };

  return (
    <Stack
      spacing={4}
      sx={{
        paddingTop: `${isMobile ? PADDING_AFTER_TAB_BAR_MOBILE : PADDING_AFTER_TAB_BAR}px`,
      }}
    >
      <Stack
        sx={{
          height: '124px',
          width: isMobile ? 'calc(100vw - 60px)' : '100%',
          boxShadow: '0px 25px 35.8px 0px #00000040',
          borderRadius: '22px',
        }}
      >
        {position && <HomeMap position={position} />}
      </Stack>
      <Stack spacing={4} mt={2} direction={isMobile ? 'column' : 'row'}>
        {productData && <ProjectOverviewPanel productData={productData} />}
        {(installationProjectWithResourceDetails?.assignedResourcesDetails?.length ?? 0) > 0 && (
          <PeoplePanel people={installationProjectWithResourceDetails.assignedResourcesDetails} />
        )}
      </Stack>
    </Stack>
  );
}
