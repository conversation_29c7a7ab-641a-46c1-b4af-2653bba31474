import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { Box, Stack, Typography } from '@mui/material';
import { Heading } from '@ui/components/Heading/Heading';
import { PeopleIcon } from '@ui/components/Icons/PeopleIcon/PeopleIcon';
import { PersonCircleOutlinedIcon } from '@ui/components/StandardIcons/PersonCircleOutlinedIcon';
import Link from 'next/link';
import { FormattedMessage } from 'react-intl';

export default function PeoplePanel({ people }: { people: UserIdentity[] }) {
  return (
    <Stack
      spacing={2}
      sx={{
        width: '100%',
        padding: '24px',
        borderRadius: '22px',
        background: '#EDEFF0',
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="flex-end" gap={1}>
        <PeopleIcon />
        <Heading level={3} variant="headline4" color="textSecondary">
          <FormattedMessage id="installationHandover.projectOverview.people" defaultMessage="People" />
        </Heading>
      </Stack>
      <Stack
        spacing={1}
        gap={3}
        sx={{
          marginTop: '24px !important',
        }}
      >
        {people.map((person) => (
          <Stack
            key={person.userId?.value}
            justifyContent="flex-start"
            alignItems="flex-start"
            direction="row"
            gap={1}
            sx={{
              marginTop: '0px !important',
            }}
          >
            <Box>
              {person.userImage ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={`data:image/jpeg;base64,${btoa(
                    String.fromCharCode.apply(null, person.userImage.map((x) => x) as unknown as number[]),
                  )}`}
                  alt={`${person.firstName} ${person.lastName}`}
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '50%',
                  }}
                />
              ) : (
                <PersonCircleOutlinedIcon height={48} width={48} color="#53535A" />
              )}
            </Box>
            <Stack justifyContent="space-between" direction="column">
              <Typography variant="body1">{`${person.firstName} ${person.lastName}`}</Typography>
              <Typography variant="body1">{person.jobTitle}</Typography>
              <Typography variant="body1">{person.phoneNumber}</Typography>
              <Link href={`mailto:${person.email}`} passHref>
                <Typography variant="body1Link" sx={{ fontWeight: 400 }}>
                  {person.email}
                </Typography>
              </Link>
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Stack>
  );
}
