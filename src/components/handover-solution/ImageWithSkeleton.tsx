import { Skeleton, Stack } from '@mui/material';
import { ImgHTMLAttributes, useState } from 'react';

export function ImageWithSkeleton(props: {
  image: Omit<ImgHTMLAttributes<HTMLImageElement>, 'width' | 'height'> & {
    width: number;
    height: number;
  };
}) {
  const [loaded, setLoaded] = useState<boolean>(false);
  const { image } = props;
  const { width, height } = image;
  return (
    <Stack sx={{ borderRadius: '16px' }}>
      {!loaded && <Skeleton variant="rounded" width={width} height={height} style={{ marginBottom: -1 * height }} />}

      {/* eslint-disable-next-line jsx-a11y/alt-text, @next/next/no-img-element */}
      <img {...image} onLoad={() => setLoaded(true)} style={{ borderRadius: '16px', objectFit: 'cover' }} />
    </Stack>
  );
}
