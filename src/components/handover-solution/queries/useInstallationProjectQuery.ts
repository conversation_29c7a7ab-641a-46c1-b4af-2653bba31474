import { GetInstallationProjectResponse } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UseTRPCQueryResult } from '@trpc/react-query/shared';
import { api } from 'utils/api';

export const useInstallationProjectQuery = ({
  solutionId,
  select,
}: {
  solutionId?: string;
  select?: (data: any) => any;
}): UseTRPCQueryResult<GetInstallationProjectResponse, unknown> =>
  api.AiraBackend.getInstallationProject.useQuery({ energySolutionId: solutionId! }, { enabled: !!solutionId, select });
