import {
  Floor,
  Radiator,
  Room,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { ImageMap, RoomType } from 'components/heat-design/stores/types';
import { mapImageMapFromProtobuf } from 'components/heat-design/utils/loadFromProtobuf';
import { mapRoomTypeFromProtobuf } from 'components/heat-design/utils/protobufEnumMapping';
import { api } from 'utils/api';

export const useHeatDesignQuery = ({
  installationGroundworkId,
  solutionId,
  select,
}: {
  installationGroundworkId: string;
  solutionId?: string;
  select?: (data: any) => any;
}) =>
  api.HeatLossCalculator.loadHeatDesign.useQuery(
    { installationGroundworkId: installationGroundworkId!, energySolutionId: solutionId!, demoData: false },
    {
      enabled: !!solutionId && !!installationGroundworkId,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      select,
    },
  );

// select functions to get various parts of the heat design data
export const useFloors = ({
  installationGroundworkId,
  solutionId,
}: {
  installationGroundworkId: string;
  solutionId?: string;
}) =>
  useHeatDesignQuery({
    installationGroundworkId,
    solutionId,
    select: (data) =>
      data.heatDesign.dwelling?.floors.map((floor: Floor) => ({
        name: floor.name,
        id: floor.id!.value,
        svgImageData: floor.svgImageData,
        rooms: floor.rooms.map((room: Room) => ({
          name: room.name,
          roomType: mapRoomTypeFromProtobuf(room.roomType),
          underfloorHeatingWatt: room.underfloorHeatingWatt,
          id: room.id!.value,
          floorId: floor.id!.value,
          svgImageData: room.svgImageData,
          imageMap: mapImageMapFromProtobuf(room.imageMap),
          radiators: room.radiators.map((radiator: Radiator) => ({
            enabled: radiator.enabled,
            radiatorDetails: radiator.radiatorDetails,
            id: radiator.id!.value,
            roomId: room.id!.value,
            roomName: room.name,
            floorId: floor.id!.value,
            floorImageMap: mapImageMapFromProtobuf(radiator.floorImageMap),
            roomImageMap: mapImageMapFromProtobuf(radiator.imageMap),
            comment: radiator.comment,
            toBeInstalled: radiator.toBeInstalled,
            height: radiator.heightM,
            width: radiator.widthM,
          })),
        })),
      })),
  });

export type RadiatorProps = Omit<
  Radiator,
  'heatOutputAtDeltaT50InWatt' | 'imageMap' | 'floorImageMap' | 'dataSourceReferences' | 'id'
> & {
  id: string;
  roomId: string;
  roomName: string;
  floorId: string;
  floorImageMap: ImageMap;
  roomImageMap: ImageMap;
};

export type RoomPlanProps = Pick<Room, 'name' | 'underfloorHeatingWatt' | 'svgImageData'> & {
  roomType: RoomType;
  id: string;
  floorId: string;
  imageMap: ImageMap;
  radiators: RadiatorProps[];
};

export type FloorPlanProps = Omit<Floor, 'dataSourceReferences' | 'floorLevel' | 'floorUValueDefaults'> & {
  id: string;
  rooms: RoomPlanProps[];
  svgImageData: string;
};
