import { GetInstallationProjectResponse } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UseTRPCQueryResult } from '@trpc/react-query/shared';
import { api } from 'utils/api';

export const useSharePointUrlQuery = ({
  solutionId,
}: {
  solutionId?: string;
}): UseTRPCQueryResult<string | undefined, unknown> =>
  api.AiraBackend.getInstallationProject.useQuery(
    { energySolutionId: solutionId! },
    {
      enabled: !!solutionId,
      select: (data: GetInstallationProjectResponse | null) => data?.installationProject?.fileShareUrl,
    },
  );
