import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { useEnergySolution, useEnergySolutionId } from 'hooks/useEnergySolution';
import { FormattedMessage } from 'react-intl';
import { getCountryFromIso3166 } from 'utils/helpers';
import ExternalDocumentLink from './ExternalDocumentLink';

/*
 * Aerospace will automatically redirect to the market based on the user's
 * cookie preferences if they have stored set a language previously.
 * However, to guard for the possibility that the user has not stored their
 * preferences, we should default to the country code that is used for the
 * region of the solution, since otherwise the report will be presented in
 * languages. */
const localePrefixForCountry = (country: Country): string => {
  switch (country) {
    case Country.COUNTRY_DE:
      return '/de';
    case Country.COUNTRY_IT:
      return '/it';
    case Country.COUNTRY_GB:
    case Country.COUNTRY_PL:
    case Country.COUNTRY_UNSPECIFIED:
    case Country.COUNTRY_SE:
    case Country.UNRECOGNIZED:
      return ''; // Use the default locale
    default: {
      const exhaustiveCheck: never = country; // eslint-disable-line @typescript-eslint/no-unused-vars
      return '';
    }
  }
};

export default function InstallationReportLink() {
  const solutionId = useEnergySolutionId();
  const solution = useEnergySolution();
  const country = getCountryFromIso3166(solution.data?.solution?.region?.iso3166);
  if (!country) return null;

  const localePrefix = localePrefixForCountry(country);
  return (
    <ExternalDocumentLink
      icon={<PenOutlinedIcon color="#222226" />}
      href={`${localePrefix}/solution/${solutionId}/customer-acceptance-form`}
    >
      <FormattedMessage
        id="installationHandover.externalLinks.fillInInstallationReport"
        defaultMessage="Fill in installation report"
      />
    </ExternalDocumentLink>
  );
}
