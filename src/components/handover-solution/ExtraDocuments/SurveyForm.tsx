import { CircularProgress, Link, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { DownloadIcon } from '@ui/components/Icons/DownloadIcon/DownloadIcon';
import { OpenInIcon } from '@ui/components/Icons/OpenInIcon/OpenInIcon';
import { MagicplanIcon } from '@ui/components/Icons/Magicplan/MagicplanIcon';
import { useInstallationGroundworkIdContext } from '../Contexts/InstallationGroundworkIdContext';
import { useSurveyFormQuery } from '../queries/useSurveyFormQuery';
import { getLatestSurveyForm, getLatestSurveyFormSubmissionReport } from 'utils/helpers';

interface SurveyLinkRowProps {
  title: string;
  link: string;
  icon: React.ReactElement;
}

export function SurveyForm() {
  const intl = useIntl();
  const installationGroundworkId = useInstallationGroundworkIdContext();
  const { data: surveyFormsResponse, isLoading } = useSurveyFormQuery(installationGroundworkId);
  const latestSurveyForm = getLatestSurveyForm(surveyFormsResponse);
  const latestSurveyFormSubmissionReport = getLatestSurveyFormSubmissionReport(latestSurveyForm);

  if (isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: '300px',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    );
  }

  if (!latestSurveyForm) {
    return null;
  }

  const rows: SurveyLinkRowProps[] = [];
  if (latestSurveyFormSubmissionReport?.url) {
    rows.push({
      title: intl.formatMessage({
        id: 'installationHandover.extraDocuments.downloadPDF',
        defaultMessage: 'Download PDF',
      }),
      link: latestSurveyFormSubmissionReport.url,
      icon: <DownloadIcon />,
    });
  }
  if (latestSurveyForm.referenceCode) {
    if (latestSurveyForm.deeplinkToMobile) {
      rows.push({
        title: intl.formatMessage({
          id: 'installationHandover.extraDocuments.openMagicplanApp',
          defaultMessage: 'Open Magicplan app',
        }),
        link: latestSurveyForm.deeplinkToMobile,
        icon: <MagicplanIcon />,
      });
    }
    if (latestSurveyForm.webUrl) {
      rows.push({
        title: intl.formatMessage({
          id: 'installationHandover.extraDocuments.openMagicplanWebsite',
          defaultMessage: 'Open Magicplan website',
        }),
        link: latestSurveyForm.webUrl,
        icon: <OpenInIcon />,
      });
    }
  }
  if (rows.length === 0) {
    return null;
  }

  return (
    <>
      {rows.map((row) => (
        <Link href={row.link} target="_blank" rel="noreferrer" key={row.title}>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            spacing={1}
            sx={{
              background: '#22222608',
              padding: '16px',
              borderRadius: '12px',
              marginBottom: '10px',
              height: '56px',
            }}
          >
            <Typography variant="body1">{row.title}</Typography>
            {row.icon}
          </Stack>
        </Link>
      ))}
    </>
  );
}
