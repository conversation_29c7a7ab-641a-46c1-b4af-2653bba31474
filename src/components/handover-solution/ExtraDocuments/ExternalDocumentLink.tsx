import { Stack, Typography } from '@mui/material';
import Link, { LinkProps } from 'next/link';
import { ReactNode } from 'react';

export interface ExternalDocumentLinkProps extends LinkProps, React.AnchorHTMLAttributes<HTMLAnchorElement> {
  icon: ReactNode;
  children: ReactNode | ReactNode[];
  href: string;
}

export default function ExternalDocumentLink({ icon, children, href, ...props }: ExternalDocumentLinkProps) {
  return (
    <Link href={href} {...props}>
      <Stack
        component="span"
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        gap={1}
        borderRadius={1}
        mb={1}
        p={2}
        bgcolor="#22222608"
      >
        <Typography variant="body1">{children}</Typography>
        {icon}
      </Stack>
    </Link>
  );
}
