import { PhoneMobileOutlinedIcon } from '@ui/components/StandardIcons/PhoneMobileOutlinedIcon';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { FormattedMessage } from 'react-intl';
import ExternalDocumentLink from './ExternalDocumentLink';

export default function HeatPumpConfigLink() {
  const solutionId = useEnergySolutionId();
  const link = `aira://app.airahome.com/installer/add-job?esId=${solutionId}`;
  return (
    <ExternalDocumentLink
      icon={<PhoneMobileOutlinedIcon color="#222226" />}
      href={link ?? ''}
      target="_blank"
      rel="noreferrer"
    >
      <FormattedMessage
        id="installationHandover.externalLinks.heatPumpConfiguration"
        defaultMessage="Heat pump configuration"
      />
    </ExternalDocumentLink>
  );
}
