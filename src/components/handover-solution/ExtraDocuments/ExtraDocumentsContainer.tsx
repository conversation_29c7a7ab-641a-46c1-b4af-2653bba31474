import { Stack, useMediaQuery } from '@mui/material';
import { SurveyForm } from './SurveyForm';
import MiscellaneousFolder from './MiscellaneousFolder';
import { PADDING_AFTER_TAB_BAR, PADDING_AFTER_TAB_BAR_MOBILE } from '../constants';
import DeviationsTracker from './DeviationsTracker';
import HeatPumpConfigLink from './HeatPumpConfigLink';
import InstallationReportLink from './InstallationReportLink';

export default function ExtraDocumentsContainer() {
  const isMobile = useMediaQuery('(max-width: 700px)');
  return (
    <Stack
      spacing={2}
      sx={{
        width: '100%',
        borderRadius: '22px',
        height: '--webkit-fill-available',
        paddingTop: `${isMobile ? PADDING_AFTER_TAB_BAR_MOBILE : PADDING_AFTER_TAB_BAR}px`,
      }}
    >
      <Stack gap={1}>
        <SurveyForm />
        <MiscellaneousFolder />
        <DeviationsTracker />
        <HeatPumpConfigLink />
        <InstallationReportLink />
      </Stack>
    </Stack>
  );
}
