import { FormattedMessage } from 'react-intl';
import { FolderIcon } from '@ui/components/Icons/FolderIcon/FolderIcon';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useSharePointUrlQuery } from '../queries/useSharePointUrlQuery';
import ExternalDocumentLink from './ExternalDocumentLink';

export default function MiscellaneousFolder() {
  const solutionId = useEnergySolutionId();

  const { data: sharePointUrl } = useSharePointUrlQuery({ solutionId });
  return (
    sharePointUrl && (
      <ExternalDocumentLink icon={<FolderIcon />} href={sharePointUrl} target="_blank" rel="noreferrer">
        <FormattedMessage
          id="installationHandover.extraDocuments.sharepointFolder"
          defaultMessage="Sharepoint folder"
        />
      </ExternalDocumentLink>
    )
  );
}
