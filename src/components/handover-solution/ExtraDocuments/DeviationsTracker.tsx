import { ClockDotsOutlinedIcon } from '@ui/components/StandardIcons/ClockDotsOutlinedIcon';
import { FormattedMessage } from 'react-intl';
import ExternalDocumentLink from './ExternalDocumentLink';
import { useEnergySolution } from 'hooks/useEnergySolution';
import { getCountryFromIso3166 } from 'utils/helpers';
import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';

const deviationsTrackerLinks = [
  {
    country: Country.COUNTRY_IT,
    link: 'https://forms.office.com/Pages/ResponsePage.aspx?Host=Teams&id=c9gdDPu0rkiLbba6FQCl5BBTiduU9AtLsNvRuAYnXfNUQjU1TlYwVTVDRDY3TlBPQ1hJRlZIRlJHQi4u',
  },
  {
    country: Country.COUNTRY_DE,
    link: 'https://forms.office.com/Pages/ResponsePage.aspx?Host=Teams&id=c9gdDPu0rkiLbba6FQCl5BBTiduU9AtLsNvRuAYnXfNURDFKVUFVSlg4M09PUFpTN0g4Vk01SEtXWS4u',
  },
  {
    country: Country.COUNTRY_GB,
    link: 'https://forms.office.com/Pages/ResponsePage.aspx?Host=Teams&id=c9gdDPu0rkiLbba6FQCl5BBTiduU9AtLsNvRuAYnXfNUQkVPUTQ4VjBRVDlDUExTQk5MR0E4S0RDVy4u',
  },
];
export default function DeviationsTracker() {
  const solution = useEnergySolution();
  const country = getCountryFromIso3166(solution.data?.solution?.region?.iso3166);
  if (!country) return null;

  const link = deviationsTrackerLinks.find((l) => l.country === country)?.link;

  return (
    <ExternalDocumentLink
      icon={<ClockDotsOutlinedIcon color="#222226" />}
      href={link ?? ''}
      target="_blank"
      rel="noreferrer"
    >
      <FormattedMessage id="installationHandover.externalLinks.deviationsTracker" defaultMessage="Deviations tracker" />
    </ExternalDocumentLink>
  );
}
