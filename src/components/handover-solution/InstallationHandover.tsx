import { Box, Stack, useMediaQuery } from '@mui/material';
import { useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { useIntl } from 'react-intl';
import { beige } from '@ui/theme/colors';
import FloorPlanContainer from './FloorPlan/FloorPlanContainer';
import OverviewContainer from './Overview/OverviewContainer';
import ExtraDocumentsContainer from './ExtraDocuments/ExtraDocumentsContainer';
import RadiatorsContainer from './Radiators/RadiatorsContainer';

type TabNames = 'overview' | 'floor-plan' | 'radiators' | 'survey-form' | 'external-links';

export default function InstallationHandover() {
  const intl = useIntl();
  const isMobile = useMediaQuery('(max-width: 700px)');
  const [currentTab, setCurrentTab] = useState<TabNames>('overview');

  const tabs = [
    {
      value: 'overview',
      formattedMessage: intl.formatMessage({ id: 'handoverSolution.tabs.overview', defaultMessage: 'Overview' }),
    },
    {
      value: 'floor-plan',
      formattedMessage: intl.formatMessage({ id: 'handoverSolution.tabs.floorPlan', defaultMessage: 'Floor plan' }),
    },
    {
      value: 'radiators',
      formattedMessage: intl.formatMessage({ id: 'handoverSolution.tabs.radiators', defaultMessage: 'Radiators' }),
    },
    {
      value: 'external-links',
      formattedMessage: intl.formatMessage({
        id: 'handoverSolution.tabs.externalLinks',
        defaultMessage: 'External links',
      }),
    },
  ];

  const handleTabChange = (e: React.MouseEvent<HTMLButtonElement>) => {
    setCurrentTab(e.currentTarget.value as TabNames);
  };

  return (
    <Stack
      sx={{
        width: isMobile ? '100%' : '756px',
        height: '100%',
        padding: 0,
        margin: '0 auto',
        alignItems: 'center',
      }}
    >
      {!isMobile && (
        <Heading level={1} variant="headline1" mb={4} alignSelf="flex-start">
          {intl.formatMessage({ id: 'installationHandover.titles.mainTitle', defaultMessage: 'Installation Handover' })}
        </Heading>
      )}
      <Box
        sx={{
          display: 'flex',
          overflowX: 'auto',
          whiteSpace: 'nowrap',
          width: isMobile ? '100vw' : '100%',
          margin: isMobile ? '0px -24px 0 -24px' : '0px',
          padding: isMobile ? '10px 24px 10px 24px' : '10px 0',
          background: beige[150],
          zIndex: 500,
          flexWrap: isMobile ? 'nowrap' : 'wrap',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
          position: 'sticky',
          top: `${isMobile ? 74 : 84}px`, //
        }}
        gap="8px"
      >
        {tabs.map((tab) => (
          <Button
            variant={currentTab === tab.value ? 'contained' : 'outlined'}
            color="primary"
            onClick={handleTabChange}
            value={tab.value}
            key={tab.value}
            size="small"
            sx={{
              whiteSpace: 'nowrap',
              flexShrink: 0,
              borderRadius: '12px',
              padding: '9.5px 24px',
            }}
          >
            {tab.formattedMessage}
          </Button>
        ))}
      </Box>
      <Stack py={0} width="100%" maxWidth={isMobile ? '100%' : '756px'}>
        {currentTab === 'overview' && <OverviewContainer />}
        {currentTab === 'floor-plan' && <FloorPlanContainer />}
        {currentTab === 'radiators' && <RadiatorsContainer />}
        {currentTab === 'external-links' && <ExtraDocumentsContainer />}
      </Stack>
    </Stack>
  );
}
