import { create } from 'zustand';

export type ObjectStore = {
  selectedObject?: any;
  selectedObjectType?: string;
  showingDetails: boolean;

  actions: {
    setSelectedObject: (object?: any) => void;
    clearSelectedObject: () => void;
    setSelectedObjectType: (objectType: string) => void;
    setShowDetails: (show: boolean) => void;
  };
};

export const useObjectStore = create<ObjectStore>((set) => ({
  selectedObject: undefined,
  selectedObjectType: undefined,
  showingDetails: false,
  actions: {
    setSelectedObject: (object) => set({ selectedObject: object }),
    clearSelectedObject: () => set({ selectedObject: undefined, selectedObjectType: undefined }),
    setSelectedObjectType: (objectType) => set({ selectedObjectType: objectType }),
    setShowDetails: (show) => set({ showingDetails: show }),
  },
}));

export const useSelectedObject = () => useObjectStore((state) => state.selectedObject);
export const useSelectedObjectType = () => useObjectStore((state) => state.selectedObjectType);
export const useShowingDetails = () => useObjectStore((state) => state.showingDetails);
export const useObjectActions = () => useObjectStore((state) => state.actions);
