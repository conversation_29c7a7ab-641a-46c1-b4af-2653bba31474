import React from 'react';
import { Alert, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useInstallationGroundworkQuery } from '../queries/useInstallationGroundworkQuery';

const InstallationGroundworkIdContext = React.createContext<string | null>(null);

export const useInstallationGroundworkIdContext = () => {
  const installationGroundworkId = React.useContext(InstallationGroundworkIdContext);
  if (!installationGroundworkId) {
    throw new Error('useInstallationGroundworkIdContext must be used within a InstallationGroundworkIdContextProvider');
  }
  return installationGroundworkId;
};

export function InstallationGroundworkIdContextProvider({ children }: { children: React.ReactNode }) {
  const intl = useIntl();
  const solutionId = useEnergySolutionId();

  const installationGroundworkQuery = useInstallationGroundworkQuery(solutionId);

  if (installationGroundworkQuery.isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (installationGroundworkQuery.isError) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  if (!installationGroundworkQuery.data?.id?.value) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return (
    <InstallationGroundworkIdContext.Provider value={installationGroundworkQuery.data.id.value}>
      {children}
    </InstallationGroundworkIdContext.Provider>
  );
}
