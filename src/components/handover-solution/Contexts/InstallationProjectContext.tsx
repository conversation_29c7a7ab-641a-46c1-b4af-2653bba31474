import React from 'react';
import { Al<PERSON>, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useInstallationProjectQuery } from '../queries/useInstallationProjectQuery';

const InstallationContext = React.createContext<InstallationProject | null>(null);

export const useInstallationProjectContext = (): InstallationProject => {
  const installation = React.useContext(InstallationContext);
  if (!installation) {
    throw new Error('useInstallationContext must be used within a InstallationContextProvider');
  }
  return installation;
};

export function InstallationProjectContextProvider({ children }: { children: React.ReactNode }) {
  const intl = useIntl();
  const solutionId = useEnergySolutionId();

  const installationProjectQuery = useInstallationProjectQuery({ solutionId });

  if (installationProjectQuery.isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (installationProjectQuery.isError) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  if (
    !installationProjectQuery.data?.installationProject?.facilityId?.value ||
    !installationProjectQuery.data?.installationProject?.id?.value
  ) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return (
    <InstallationContext.Provider value={installationProjectQuery.data?.installationProject}>
      {children}
    </InstallationContext.Provider>
  );
}
