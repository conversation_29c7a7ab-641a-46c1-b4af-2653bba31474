import { SurveyFormReference } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.form.v1';
import { CircularProgress, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Heading } from '@ui/components/Heading/Heading';
import { CheckCircle, InfoOutlined as Info, Error as MuiError } from '@ui/components/Icons/material';
import { green, grey, red } from '@ui/theme/colors';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';

function CreateMagicplanStep({ onSuccess }: { onSuccess: (mobileUrl: string) => void }) {
  const router = useRouter();
  const { solution: _, job: jobId } = router.query as { solution?: string; job?: string };
  const [status, setStatus] = useState<'success' | 'failure' | 'running' | 'waiting' | 'no_account'>('waiting');

  const { mutateAsync: createSurveyForm } = api.AiraBackend.createSurveyForm.useMutation({
    onSuccess(response) {
      setStatus('success');
      onSuccess(response.deeplinkToMobile);
    },
  });

  useEffect(() => {
    setStatus('running');
    createSurveyForm({ jobId: jobId! }).catch((e) => {
      setStatus('failure');
      if (e.data?.code === 'NOT_FOUND') {
        // In this situation, we should invite the user to Magicplan. This
        // work is planned in https://aira.atlassian.net/browse/ESD-1051.
        // For now, we just inform the user that the technical surveyor
        // does not have an account and tell them how to create one.
        setStatus('no_account');

        // TODO: Handle when the endpoint throws a 404 because the job does not exist
        // TODO: Help the user a bit more when they don't have an account, e.g. a button to Breeze
      }
    });
  }, [createSurveyForm, jobId]);

  return (
    status !== 'waiting' && (
      <Stack direction="row" mb={2} role="status">
        <Stack mt="0.15em" width="1.5em">
          {status === 'running' && <CircularProgress size="1em" sx={{ boxSizing: 'border-box' }} />}
          {status === 'success' && <CheckCircle sx={{ color: green[500] }} />}
          {status === 'failure' && <MuiError sx={{ color: red[500] }} />}
          {status === 'no_account' && <Info sx={{ color: grey[500] }} />}
        </Stack>

        <Stack ml={1}>
          <Heading level={2} variant="headline2">
            <FormattedMessage id="form-redirect.status.magicplan.create.header" />
          </Heading>

          {status === 'success' && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.create.success" />
            </Typography>
          )}
          {status === 'failure' && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.create.failure" />
            </Typography>
          )}
          {status === 'no_account' && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.create.notice" />
            </Typography>
          )}
        </Stack>
      </Stack>
    )
  );
}

export default function GetOrCreateMagicplanStep({
  installationGroundworkId,
  onCreateSuccess,
  onGetSuccess,
}: {
  installationGroundworkId: string;
  onCreateSuccess: (mobileUrl: string) => void;
  onGetSuccess: (surveyFormReference?: SurveyFormReference) => void;
}) {
  const [shouldCreateMagicplan, setShouldCreateMagicplan] = useState(false);
  const { data, status } = api.AiraBackend.getSurveyFormReferences.useQuery({
    installationGroundworkId: installationGroundworkId,
  });

  const isValid = data?.surveyFormReferences.length !== 0;
  const isSuccess = status === 'success' && isValid;
  const isMissing = status === 'success' && !isValid;
  const isFailure = status === 'error';
  const isRunning = status === 'pending';

  useEffect(() => {
    if (isSuccess) {
      onGetSuccess(data?.surveyFormReferences.at(-1));
    }

    if (isMissing) {
      setShouldCreateMagicplan(true);
    }
  }, [isMissing, isSuccess, onGetSuccess, data?.surveyFormReferences]);

  return (
    <>
      <Stack direction="row" mb={2} role="status">
        <Stack mt="0.15em" width="1.5em">
          {isRunning && <CircularProgress size="1em" sx={{ boxSizing: 'border-box' }} />}
          {isSuccess && <CheckCircle sx={{ color: green[500] }} />}
          {(isFailure || isMissing) && <MuiError sx={{ color: red[500] }} />}
        </Stack>
        <Stack ml={1}>
          <Heading level={2} variant="headline2">
            <FormattedMessage id="form-redirect.status.magicplan.header" />
          </Heading>
          {isSuccess && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.success" />
            </Typography>
          )}
          {isFailure && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.failure" />
            </Typography>
          )}
          {isMissing && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.not-found" />
            </Typography>
          )}
        </Stack>
      </Stack>
      {shouldCreateMagicplan && <CreateMagicplanStep onSuccess={onCreateSuccess} />}
    </>
  );
}
