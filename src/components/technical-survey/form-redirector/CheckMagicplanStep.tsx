import { SurveyFormReference } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.form.v1';
import { CircularProgress, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { CheckCircle, Error as MuiError, PersonOutlined } from '@ui/components/Icons/material';
import { beige, green, grey, red } from '@ui/theme/colors';
import { MagicplanLinks } from 'components/heat-design/modals/MagicplanLinks';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';

function UserMismatchNotice({
  magicplanUserEmail,
  aerospaceUserEmail,
  installationGroundworkId,
  surveyFormReference,
  onReassignSuccess,
}: {
  magicplanUserEmail?: string;
  aerospaceUserEmail?: string;
  installationGroundworkId?: string;
  surveyFormReference: SurveyFormReference;
  onReassignSuccess: () => void;
}) {
  const router = useRouter();
  const { solution: energySolutionId } = router.query as { solution?: string };
  const { mutate: reassignProject, status } = api.HeatLossCalculator.reassignMagicplanProject.useMutation({
    onSuccess: onReassignSuccess,
  });

  return (
    <Stack
      gap={1}
      sx={{
        padding: 4,
        marginTop: 2,
        borderRadius: 2,
        overflowWrap: 'break-word',
        backgroundColor: beige[100],
      }}
    >
      <Typography color={grey[700]}>
        <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.magicplan-user" />{' '}
        <Typography variant="body1Emphasis">{magicplanUserEmail}</Typography>
      </Typography>
      <Typography color={grey[700]}>
        <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.aerospace-user" />{' '}
        <Typography variant="body1Emphasis">{aerospaceUserEmail}</Typography>
      </Typography>
      <Typography variant="body2" mt={2}>
        <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.advice" />
      </Typography>
      <Stack gap={1}>
        <Button
          variant="contained"
          disabled={status === 'pending'}
          startIcon={<PersonOutlined />}
          size="small"
          fullWidth
          onClick={() => {
            reassignProject({
              energySolutionId: energySolutionId ?? '',
              installationGroundworkId: installationGroundworkId ?? '',
            });
          }}
        >
          <FormattedMessage
            id={
              status === 'pending'
                ? 'heatDesign.magicplan.reassign.buttonLabel.loading'
                : 'heatDesign.magicplan.reassign.buttonLabel'
            }
          />
        </Button>
        <Typography variant="body2" mt={2} color={grey[600]}>
          <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.alternative" />
        </Typography>
        <MagicplanLinks surveyLinks={surveyFormReference} showTitle={false} />
        {status === 'success' && (
          <Stack direction="row" alignSelf="start" mt={2} role="status">
            <Stack width="1.5em">
              <CheckCircle sx={{ color: green[500] }} />
            </Stack>
            <Stack ml={1}>
              <Typography>
                <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.reassign.success" />
              </Typography>
            </Stack>
          </Stack>
        )}
        {status === 'error' && (
          <Stack direction="row" alignSelf="start" mt={2} role="status">
            <Stack width="1.5em">
              <MuiError sx={{ color: red[500] }} />
            </Stack>
            <Stack ml={1}>
              <Typography>
                <FormattedMessage id="form-redirect.status.magicplan.user-mismatch.reassign.failure" />
              </Typography>
            </Stack>
          </Stack>
        )}
      </Stack>
    </Stack>
  );
}

export default function CheckMagicplanStep({
  surveyFormReference,
  installationGroundworkId,
  onSuccess,
}: {
  surveyFormReference: SurveyFormReference;
  installationGroundworkId?: string;
  onSuccess: (shouldShowManualRedirect: boolean) => void;
}) {
  const { data: aerospaceUser, status: aerospaceUserStatus } = api.AiraBackend.whoAmI.useQuery();

  const isEnabled = () => surveyFormReference.formInstanceId?.value !== undefined;
  const { data, status } = api.HeatLossCalculator.getAssignedMagicplanUser.useQuery(
    { formInstanceId: surveyFormReference.formInstanceId?.value ?? '' },
    { enabled: isEnabled() },
  );

  const isValid = data?.userEmail !== undefined && data?.userEmail === aerospaceUser?.email;
  const isSuccess = status === 'success' && isValid;
  const isInvalid = status === 'success' && !isValid;
  const isFailure = status === 'error' || aerospaceUserStatus === 'error';
  const isRunning = status === 'pending';

  useEffect(() => {
    if (isSuccess) {
      onSuccess(true);
    }
  }, [onSuccess, isSuccess]);

  return (
    isEnabled() && (
      <Stack direction="row" mb={2} role="status">
        <Stack mt="0.15em" width="1.5em">
          {isRunning && <CircularProgress size="1em" sx={{ boxSizing: 'border-box' }} />}
          {isSuccess && <CheckCircle sx={{ color: green[500] }} />}
          {(isFailure || isInvalid) && <MuiError sx={{ color: red[500] }} />}
        </Stack>

        <Stack ml={1}>
          <Heading level={2} variant="headline2">
            <FormattedMessage id="form-redirect.status.magicplan.check.header" />
          </Heading>

          {isSuccess && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.check.success" />
            </Typography>
          )}
          {isFailure && (
            <Typography>
              <FormattedMessage id="form-redirect.status.magicplan.check.failure" />
            </Typography>
          )}
          {isInvalid && (
            <>
              <Typography>
                <FormattedMessage id="form-redirect.status.magicplan.check.incorrect" />
              </Typography>
              <UserMismatchNotice
                magicplanUserEmail={data?.userEmail}
                aerospaceUserEmail={aerospaceUser?.email}
                installationGroundworkId={installationGroundworkId}
                surveyFormReference={surveyFormReference}
                onReassignSuccess={() => onSuccess(false)}
              />
            </>
          )}
        </Stack>
      </Stack>
    )
  );
}
