import { CircularProgress, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Heading } from '@ui/components/Heading/Heading';
import { CheckCircle, Error as MuiError } from '@ui/components/Icons/material';
import { green, red } from '@ui/theme/colors';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';

export default function GetAerospaceDataStep({ onSuccess }: { onSuccess: (id: string) => void }) {
  const router = useRouter();
  const { solution: solutionId } = router.query as { solution?: string };

  const isEnabled = () => solutionId !== undefined;
  const { data, status } = api.AiraBackend.getGroundworkForSolution.useQuery(
    { solutionId: solutionId! },
    { enabled: isEnabled() },
  );

  const isValid = data?.id?.value !== undefined;
  const isSuccess = status === 'success' && isValid;
  const isFailure = status === 'error' || (status === 'success' && !isValid);
  const isRunning = status === 'pending';

  useEffect(() => {
    if (isSuccess) {
      // Silence ESLint warning because we check for null in the conditional
      //eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
      onSuccess(data?.id?.value!);
    }
  }, [onSuccess, isSuccess, data?.id?.value]);

  return (
    <Stack direction="row" mb={2} role="status">
      <Stack mt="0.15em" width="1.5em">
        {isRunning && <CircularProgress size="1em" sx={{ boxSizing: 'border-box' }} />}
        {isSuccess && <CheckCircle sx={{ color: green[500] }} />}
        {isFailure && <MuiError sx={{ color: red[500] }} />}
      </Stack>

      <Stack ml={1}>
        <Heading level={2} variant="headline2">
          <FormattedMessage id="form-redirect.status.aerospace.header" />
        </Heading>

        {isSuccess && (
          <Typography>
            <FormattedMessage id="form-redirect.status.aerospace.success" />
          </Typography>
        )}
        {isFailure && (
          <Typography>
            <FormattedMessage id="form-redirect.status.aerospace.failure" />
          </Typography>
        )}
      </Stack>
    </Stack>
  );
}
