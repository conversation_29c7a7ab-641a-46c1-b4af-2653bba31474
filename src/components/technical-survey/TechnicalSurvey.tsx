import { useGroundwork } from 'context/groundwork-context';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { api } from 'utils/api';
import { useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Card,
  CardMedia,
  Checkbox,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  InputAdornment,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { ExpandMore } from '@ui/components/Icons/material';
import { CameraVideoOutlinedIcon } from '@ui/components/StandardIcons/CameraVideoOutlinedIcon';
import { DataSourceReference } from '@aira/installation-groundwork-grpc-api/build/ts_out/com/aira/acquisition/contract/form/v3/model';
import { FormAnswer, FormSection, FormTag } from 'types/api/technicalSurvey';

function buildKey(references: DataSourceReference[]) {
  return references.map((ref) => `${ref.type}:${ref.reference}`).join(',');
}

function Pills({
  references = [],
  tags = [],
  language,
}: {
  references?: DataSourceReference[];
  tags?: FormTag[];
  language?: string | undefined;
}) {
  return (
    <Box>
      {references.map((reference) => (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            padding: '8px',
            borderRadius: '8px',
          }}
          key={buildKey([reference])}
        >
          <Typography variant="body1">
            {reference.type}: {reference.reference}
            {language ? ' - ' + language.toUpperCase() : ''}
          </Typography>
        </Box>
      ))}
      <Stack direction="row" alignItems="center" sx={{ marginTop: 1 }}>
        {tags.map((tag) => (
          <Chip
            key={tag.id}
            label={
              <Typography variant="body1" sx={{ padding: '8px' }}>
                {tag.id}
              </Typography>
            }
          />
        ))}
      </Stack>
    </Box>
  );
}

export default function TechnicalSurvey() {
  const solutionId = useEnergySolutionId();
  const { groundwork } = useGroundwork();

  const installationGroundworkId = groundwork.id!.value;
  const { data: surveyResponse, error } = api.HeatLossCalculator.fetchTechnicalSurvey.useQuery({
    energySolutionId: solutionId!,
    installationGroundworkId,
  });

  if (!surveyResponse) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography color="error">Error loading survey: {error.message}</Typography>
      </Box>
    );
  }

  const formSubmissions = surveyResponse.formSubmissions;
  return (
    <Paper sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="headline1" component="h1" gutterBottom>
        Technical Survey
      </Typography>
      <Divider sx={{ my: 2 }} />

      {formSubmissions.map((submission, index) => (
        <div key={`formSubmission-${index}`}>
          <Typography variant="headline2" component="h2">
            {submission.title || '[untitled form]'}
          </Typography>
          <Box key={buildKey(submission.dataSourceReferences)}>
            <Pills references={submission.dataSourceReferences} language={submission.language} />
            {submission.sections.map((section) => {
              const sectionIdentifier = section.dataSourceReferences
                .map((ref) => `${ref.type}:${ref.reference}`)
                .join(',');
              return (
                <Section
                  section={section}
                  installationGroundworkId={installationGroundworkId}
                  key={sectionIdentifier}
                />
              );
            })}
          </Box>
          <Divider sx={{ my: 3 }} />
        </div>
      ))}
    </Paper>
  );
}

function Section({ section, installationGroundworkId }: { section: FormSection; installationGroundworkId: string }) {
  const [isExpanded, setIsExpanded] = useState(false);

  const sectionIdentifier = section.dataSourceReferences.map((ref) => `${ref.type}:${ref.reference}`).join(',');
  return (
    <>
      <Accordion expanded={isExpanded} onChange={() => setIsExpanded((expanded) => !expanded)} sx={{ mb: 1 }}>
        <AccordionSummary
          sx={{ padding: '8px' }}
          expandIcon={<ExpandMore />}
          id={`section-${sectionIdentifier}-header`}
        >
          <Typography variant="headline3" sx={{ color: section.title ? 'black' : '#454545' }}>
            {section.title || 'No title available'}
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Pills references={section.dataSourceReferences} tags={section.tags} />
          {section.items.map((item, itemIndex) => (
            <Box key={itemIndex} sx={{ mb: 3, pl: 1 }}>
              <Typography variant="body1" fontWeight="medium" gutterBottom>
                {item.question}
              </Typography>

              {item.answer && renderAnswer(item.answer, installationGroundworkId)}

              {item.tags && item.tags.length > 0 && (
                <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {item.tags.map((tag, tagIndex) => (
                    <Typography
                      key={tagIndex}
                      variant="caption"
                      sx={{
                        bgcolor: 'primary.light',
                        color: 'primary.contrastText',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                      }}
                    >
                      {tag.id}
                    </Typography>
                  ))}
                </Box>
              )}

              {itemIndex < section.items.length - 1 && <Divider sx={{ mt: 2 }} />}
            </Box>
          ))}
        </AccordionDetails>
      </Accordion>
    </>
  );
}

function MediaRenderer(props: { url: string }) {
  const [isImage, setIsImage] = useState(true);

  const handleError = () => {
    setIsImage(false);
  };

  return (
    <>
      {isImage ? (
        <Card>
          <CardMedia
            onError={handleError}
            component="img"
            sx={{ height: 140, objectFit: 'cover' }}
            image={props.url}
            alt={`Photo ${props.url}`}
          />
        </Card>
      ) : (
        <Card>
          <CardMedia
            component="video"
            controls
            sx={{ maxHeight: 250, width: 250, objectFit: 'contain' }}
            image={props.url}
          />
        </Card>
      )}
    </>
  );
}

function renderAnswer(answer: FormAnswer, installationGroundworkId: string) {
  switch (answer.$case) {
    case 'textAnswer':
      return (
        <TextField
          fullWidth
          multiline
          variant="outlined"
          value={answer.textAnswer.text || ''}
          InputProps={{ readOnly: true }}
          sx={{ mt: 1 }}
        />
      );

    case 'numericAnswer':
      return (
        <TextField
          type="number"
          variant="outlined"
          value={answer.numericAnswer.value || ''}
          InputProps={{
            readOnly: true,
            endAdornment:
              answer.numericAnswer.unit === 'meter' ? <InputAdornment position="end">m</InputAdornment> : null,
          }}
          sx={{ mt: 1 }}
        />
      );

    case 'singleChoiceAnswer': {
      const choice = answer.singleChoiceAnswer.choice;
      return (
        <FormControl component="fieldset" sx={{ mt: 1 }}>
          <RadioGroup value={choice?.text || ''}>
            <FormControlLabel
              value={choice?.text || ''}
              control={<Radio checked={!!choice} />}
              label={choice?.text || 'No selection'}
              disabled
            />
            {choice?.tags && choice.tags.length > 0 && (
              <Box sx={{ ml: 4, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                {choice.tags.map((tag, i) => (
                  <Typography
                    key={i}
                    variant="caption"
                    sx={{
                      backgroundColor: 'secondary.light',
                      color: 'secondary.contrastText',
                      px: 0.5,
                      borderRadius: 0.5,
                    }}
                  >
                    {tag.id}
                  </Typography>
                ))}
              </Box>
            )}
          </RadioGroup>
        </FormControl>
      );
    }

    case 'multipleChoiceAnswer': {
      const { choices } = answer.multipleChoiceAnswer;
      return (
        <FormControl component="fieldset" sx={{ mt: 1 }}>
          <FormGroup>
            {choices.map((choice, i) => (
              <Box key={i}>
                <FormControlLabel control={<Checkbox checked disabled />} label={choice.text} />
                {choice.tags && choice.tags.length > 0 && (
                  <Box sx={{ ml: 4, display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 0.5 }}>
                    {choice.tags.map((tag, j) => (
                      <Typography
                        key={j}
                        variant="caption"
                        sx={{
                          bgcolor: 'secondary.light',
                          color: 'secondary.contrastText',
                          px: 0.5,
                          borderRadius: 0.5,
                        }}
                      >
                        {tag.id}
                      </Typography>
                    ))}
                  </Box>
                )}
              </Box>
            ))}
          </FormGroup>
        </FormControl>
      );
    }

    case 'photoAnswer': {
      const { photos } = answer.photoAnswer;
      return (
        <Grid container spacing={2} sx={{ mt: 1 }}>
          {photos.map((photo, i) => (
            <Grid sx={{ xs: 6, sm: 4, md: 3 }} key={`${i}`}>
              <MediaRenderer url={`/api/groundwork/${installationGroundworkId}/image/${photo.id!.value}`} />
            </Grid>
          ))}
          {photos.length === 0 && (
            <Grid>
              <Box
                sx={{
                  border: '1px dashed grey',
                  p: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  borderRadius: 1,
                }}
              >
                <CameraVideoOutlinedIcon />
                <Typography variant="body2" color="textSecondary">
                  No photos available
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      );
    }

    case 'none':
    default:
      return (
        <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
          No answer provided
        </Typography>
      );
  }
}
