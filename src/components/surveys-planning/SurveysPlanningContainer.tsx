import dynamic from 'next/dynamic';
import { useEffect, useMemo, useState } from 'react';
import { useRegionContext } from '../../context/RegionContext';
import getRegionAddress from 'components/installation-planning/helpers/getRegionAddress';
import SurveysSidebar from '../maps/surveysMap/SurveysSidebar';
import { MenuItem, Stack } from '@mui/material';
import ButtonSelect from '../../ui/components/ButtonSelect/ButtonSelect';
import { addWeeks, format, startOfWeek } from 'date-fns';
import { tz } from '@date-fns/tz';
import { add } from 'date-fns';
import { endOfDay } from 'date-fns';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { beige } from '@ui/theme/colors';
import { useSurveyType } from './contexts/SurveyTypeContext';
import { SurveyType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { DocumentOutlinedIcon } from '@ui/components/StandardIcons/DocumentOutlinedIcon';
import { FullSurveyJob } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import {
  useSelectedResource,
  useShowVisitsWithNoDate,
  useSurveysMapStoreActions,
} from 'components/maps/surveysMap/SurveysMapStore';
import { useSurveys } from './contexts/SurveysContext';
import { useRouter } from 'next/router';

const SurveysMap = dynamic(() => import('../maps/surveysMap/SurveysMap'), { ssr: false });
export interface RangeOption {
  id: number;
  from: Date;
  to: Date;
}

const rangeToLabel = (dateOption: { from: Date; to: Date }) => {
  const formattedFrom = format(dateOption.from, 'd MMM');
  const formattedTo = format(dateOption.to, 'd MMM');
  return `${formattedFrom} - ${formattedTo}`;
};

const dayOptions = [
  {
    id: 0,
    label: 'All days',
  },
  {
    id: 1,
    label: 'Monday',
  },
  {
    id: 2,
    label: 'Tuesday',
  },
  {
    id: 3,
    label: 'Wednesday',
  },
  {
    id: 4,
    label: 'Thursday',
  },
  {
    id: 5,
    label: 'Friday',
  },
];

export default function SurveysPlanningContainer({
  fullScreen = true,
  bookingType,
}: {
  fullScreen?: boolean;
  bookingType?: 'sales' | 'technicalSurvey' | undefined;
}) {
  const router = useRouter();
  const region = useRegionContext();
  const { surveyType, setSurveyType } = useSurveyType();
  const { surveysData: surveys } = useSurveys();
  const showVisitsWithNoDate = useShowVisitsWithNoDate();
  const selectedResource = useSelectedResource();
  const { setSelectedResource, setSelectedSurvey } = useSurveysMapStoreActions();

  const isBookingTool = router.pathname.includes('booking');
  const solutionId = isBookingTool ? (router.query.solution as string) : undefined;

  const getWeekOptions = (timeZone: string, nrOfWeeks: number = 3): RangeOption[] => {
    const currentStartOfWeek = startOfWeek(Date.now(), { weekStartsOn: 1, in: tz(timeZone) });

    const options = [];

    for (let i = 0; i < nrOfWeeks; i += 1) {
      const from = addWeeks(currentStartOfWeek, i);
      const to = endOfDay(add(from, { days: 4 }), { in: tz(timeZone) });
      options.push({ id: i + 1, from: from, to: to });
    }
    return options;
  };

  const weekOptions = getWeekOptions(region.timeZone, 4);

  const [isClient, setIsClient] = useState(false);

  const hubAddress = getRegionAddress(region.id!.value);
  const hubPosition = useMemo(
    () => (hubAddress?.geometry ? { lat: hubAddress.geometry.lat, lng: hubAddress.geometry.long } : null),
    [hubAddress],
  );
  const [selectedRange, setSelectedRange] = useState(weekOptions[0]!);
  const [selectedDay, setSelectedDay] = useState(dayOptions[0]!);
  const [filterByResource, setFilterByResource] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const filteredSurveys = useMemo(() => {
    let isSolutionId = false;
    if (isSolutionId) {
      const solution = surveys.find((s) => s.energySolution?.id?.value === solutionId);
      if (solution) {
        isSolutionId = true;
      }
    }
    const startDateFilter = (survey: FullSurveyJob) => {
      return (
        (showVisitsWithNoDate && survey.survey?.plannedStartAt === undefined) ||
        (survey.survey?.plannedStartAt &&
          survey.survey.plannedStartAt.getTime() >= selectedRange.from.getTime() &&
          survey.survey.plannedStartAt.getTime() <= selectedRange.to.getTime() &&
          (selectedDay.id === 0 || survey.survey.plannedStartAt.getDay() === selectedDay.id))
      );
    };

    const assigneeFilter = (survey: FullSurveyJob) => {
      if (filterByResource) {
        if (selectedResource) {
          return selectedResource.userId?.value === survey.survey?.assignees[0]?.userId?.value;
        } else {
          return true;
        }
      } else {
        return true;
      }
    };

    return surveys.filter((survey) => isSolutionId || (startDateFilter(survey) && assigneeFilter(survey)));
  }, [
    surveys,
    solutionId,
    showVisitsWithNoDate,
    selectedRange.from,
    selectedRange.to,
    selectedDay.id,
    filterByResource,
    selectedResource,
  ]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: fullScreen ? '100%' : 'calc(100vw - 800px)',
        height: '100%',
        position: 'relative',
      }}
    >
      <SurveysSidebar
        filterByResource={filterByResource}
        setFilterByResource={setFilterByResource}
        fullScreen={fullScreen}
      />
      <Stack
        sx={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-end',
          position: 'absolute',
          top: 30,
          right: 32,
          zIndex: 1000,
          gap: 1,
        }}
      >
        {!bookingType && (
          <ButtonSelect
            selectedValue={surveyType}
            onChange={(e) => {
              setSurveyType(e.target.value as SurveyType);
              setSelectedSurvey(null);
              setSelectedResource(null);
            }}
            name="selectedRange"
            options={[
              { label: 'Sales', value: SurveyType.SURVEY_TYPE_SALES },
              { label: 'Technical', value: SurveyType.SURVEY_TYPE_TECHNICAL },
            ].map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
            icon={DocumentOutlinedIcon}
            backgroundColor={beige[150]}
            backgroundColorHover={beige[150]}
            boxShadow
          />
        )}
        <ButtonSelect
          selectedValue={selectedRange.id}
          onChange={(e) => {
            const newDate = weekOptions.find((range) => range.id === e.target.value);
            if (newDate) {
              setSelectedRange(newDate);
              setSelectedSurvey(null);
            }
          }}
          name="selectedWeek"
          options={weekOptions.map((range) => (
            <MenuItem key={range.id} value={range.id}>
              {rangeToLabel(range)}
            </MenuItem>
          ))}
          icon={CalendarOutlinedIcon}
          backgroundColor={beige[150]}
          backgroundColorHover={beige[150]}
          boxShadow
        />

        <ButtonSelect
          selectedValue={selectedDay.id}
          onChange={(e) => {
            const newDay = dayOptions.find((day) => day.id === e.target.value);
            if (newDay) {
              setSelectedDay(newDay);
              setSelectedSurvey(null);
            }
          }}
          name="selectedDay"
          options={dayOptions.map((day) => (
            <MenuItem key={day.id} value={day.id}>
              {day.label}
            </MenuItem>
          ))}
          icon={CalendarOutlinedIcon}
          backgroundColor={beige[150]}
          backgroundColorHover={beige[150]}
          boxShadow
        />
      </Stack>
      {isClient && (
        <SurveysMap
          hubPosition={hubPosition}
          surveys={filteredSurveys}
          fullScreen={fullScreen}
          solutionId={solutionId}
          filterByResource={filterByResource}
        />
      )}
    </div>
  );
}
