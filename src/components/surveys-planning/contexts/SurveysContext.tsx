import React, { createContext, useContext, useMemo } from 'react';
import { api } from 'utils/api';
import { useRegionContext } from '../../../context/RegionContext';
import {
  FullSurveyJob,
  SurveyRole,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { useDateRange } from './DateRangeContext';
import { JobStatus } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import { useSurveyType } from './SurveyTypeContext';
import { SurveyType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';

interface SurveysContextType {
  surveysData: FullSurveyJob[];
  isLoadingSurveys: boolean;
  refetchSurveys: () => void;
}

const SurveysContext = createContext<SurveysContextType | undefined>(undefined);

export function SurveysProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();
  const { dateRange } = useDateRange();
  const { surveyType } = useSurveyType();
  const {
    data,
    refetch: refetchSurveys,
    isLoading: isLoadingSurveys,
  } = api.Survey.getSurveysForRegion.useQuery({
    regionId: regionId!.value,
    start: dateRange.startDate,
    end: dateRange.endDateInclusive,
  });

  const surveysData = useMemo(
    () =>
      data?.filter(
        (surveyJob) =>
          surveyJob.survey?.requiredRoles.includes(
            surveyType === SurveyType.SURVEY_TYPE_SALES
              ? SurveyRole.SURVEY_ROLE_SALES
              : SurveyRole.SURVEY_ROLE_TECHNICAL,
          ) && ![JobStatus.JOB_STATUS_FINISHED, JobStatus.JOB_STATUS_CANCELLED].includes(surveyJob.survey?.status),
      ),
    [data, surveyType],
  );

  const value = useMemo(
    () => ({
      surveysData: surveysData || [],
      isLoadingSurveys,
      refetchSurveys,
    }),
    [surveysData, isLoadingSurveys, refetchSurveys],
  );

  return <SurveysContext.Provider value={value}>{children}</SurveysContext.Provider>;
}

export function useSurveys() {
  const context = useContext(SurveysContext);
  if (context === undefined) {
    throw new Error('useSurveys must be used within a SurveysProvider');
  }
  return context;
}
