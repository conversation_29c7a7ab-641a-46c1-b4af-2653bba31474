import React, { createContext, useContext, useState, useMemo } from 'react';
import { addWeeks, nextFriday, startOfWeek, startOfToday, subWeeks } from 'date-fns';
import { tz } from '@date-fns/tz';
import { useRegionContext } from '../../../context/RegionContext';
import { DateRange } from 'components/installation-planning/topBar/DateRangePicker';

interface DateRangeContextType {
  dateRange: DateRange;
  setDateRange: (dateRange: DateRange) => void;
}

const DateRangeContext = createContext<DateRangeContextType | undefined>(undefined);

export function DateRangeProvider({ children }: { children: React.ReactNode }) {
  const { timeZone } = useRegionContext();

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange>(() => ({
    startDate: startOfWeek(startOfToday({ in: tz(timeZone) }), { in: tz(timeZone), weekStartsOn: 1 }),
    endDateInclusive: addWeeks(
      subWeeks(nextFriday(startOfToday({ in: tz(timeZone) }), { in: tz(timeZone) }), 1, {
        in: tz(timeZone),
      }),
      4,
    ),
  }));

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      dateRange,
      setDateRange,
    }),
    [dateRange, setDateRange],
  );

  return <DateRangeContext.Provider value={contextValue}>{children}</DateRangeContext.Provider>;
}

export function useDateRange() {
  const context = useContext(DateRangeContext);
  if (context === undefined) {
    throw new Error('useDateRange must be used within a DateRangeProvider');
  }
  return context;
}
