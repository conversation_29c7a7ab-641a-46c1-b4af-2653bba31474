import { api } from 'utils/api';
import { createContext, useContext, useMemo } from 'react';
import { UserIdentityView } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useRegionContext } from '../../../context/RegionContext';
import { SurveyResourceForRegion, SurveyResourceType } from '../types/surveyTypes';
import { useSurveyType } from './SurveyTypeContext';
import { SurveyType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { isAfter, startOfWeek } from 'date-fns';

const SurveyResourcesContext = createContext<{
  resourcesForRegion: Map<string, SurveyResourceForRegion>;
  isLoadingResources: boolean;
  isLoadingUserData: boolean;
}>({
  resourcesForRegion: new Map(),
  isLoadingResources: false,
  isLoadingUserData: false,
});

export function SurveyResourcesProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();
  const { surveyType } = useSurveyType();
  const { data: resourcesData, isLoading: isLoadingResources } = api.Resource.getResourcesForRegion.useQuery(
    { regionId: regionId!.value },
    {
      refetchInterval: 1000 * 60 * 5,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    },
  );

  const mondayThisWeek = startOfWeek(Date.now(), { weekStartsOn: 1 });

  const surveyorIds = useMemo(
    () =>
      resourcesData
        ?.filter((surveyor) => {
          if (!surveyor.endDate) return true;
          const endDate = new Date(surveyor.endDate.year, surveyor.endDate.month - 1, surveyor.endDate.day);
          return isAfter(endDate, mondayThisWeek);
        })
        ?.filter((surveyor) => {
          const roles = surveyor.roles
            .map((role) => {
              if (role.role && role.role.$case === 'technicalSurveyorRole')
                return SurveyResourceType.TECHNICAL_SURVEYOR;
              if (role.role && role.role.$case === 'salesSurveyorRole') return SurveyResourceType.SALES_SURVEYOR;
              return undefined;
            })
            .filter((r) => r !== undefined);
          return roles.length > 0;
        })
        .map((surveyor) => surveyor.userId!.value),
    [resourcesData, mondayThisWeek],
  );

  const { data: userData, isLoading: isLoadingUserData } = api.Resource.getResourceInfo.useQuery(
    {
      userIds: surveyorIds ?? [],
      view: UserIdentityView.USER_IDENTITY_VIEW_FULL,
    },
    {
      enabled: !!surveyorIds,
    },
  );

  const resourceDataWithRolesMap = useMemo(() => {
    if (!userData || !resourcesData) return new Map();
    const resourceDataWithRolesMap = new Map<string, SurveyResourceForRegion>();

    for (const r of userData) {
      const surveyor = resourcesData?.find((s) => s.userId!.value === r.userId!.value);
      if (!surveyor) continue;
      if (surveyor.roles === undefined || surveyor.roles.length === 0) continue;
      const roles = surveyor.roles
        .map((role) => {
          if (
            role.role &&
            role.role.$case === 'technicalSurveyorRole' &&
            surveyType === SurveyType.SURVEY_TYPE_TECHNICAL
          )
            return SurveyResourceType.TECHNICAL_SURVEYOR;
          if (role.role && role.role.$case === 'salesSurveyorRole' && surveyType === SurveyType.SURVEY_TYPE_SALES)
            return SurveyResourceType.SALES_SURVEYOR;
          return undefined;
        })
        .filter((r) => r !== undefined);
      if (roles.length === 0) continue;
      resourceDataWithRolesMap.set(r.userId!.value, { ...r, ...surveyor, roles });
    }

    return resourceDataWithRolesMap;
  }, [userData, resourcesData, surveyType]);

  const contextValue = useMemo(
    () => ({
      resourcesForRegion: resourceDataWithRolesMap || new Map(),
      isLoadingResources,
      isLoadingUserData,
    }),
    [resourceDataWithRolesMap, isLoadingResources, isLoadingUserData],
  );

  return <SurveyResourcesContext.Provider value={contextValue}>{children}</SurveyResourcesContext.Provider>;
}

export function useSurveyResources() {
  const context = useContext(SurveyResourcesContext);
  if (!context) {
    throw new Error('useSurveyResources must be used within a SurveyResourcesProvider');
  }
  return context;
}
