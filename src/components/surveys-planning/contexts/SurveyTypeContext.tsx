import React, { createContext, useContext, useState, useMemo } from 'react';
import { SurveyType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';

interface SurveyTypeContextType {
  surveyType: SurveyType;
  setSurveyType: (surveyType: SurveyType) => void;
}

const SurveyTypeContext = createContext<SurveyTypeContextType | undefined>(undefined);

export function SurveyTypeProvider({
  children,
  bookingType,
}: {
  children: React.ReactNode;
  bookingType?: 'sales' | 'technicalSurvey' | undefined;
}) {
  const [surveyType, setSurveyType] = useState<SurveyType>(() => {
    if (bookingType === 'sales') {
      return SurveyType.SURVEY_TYPE_SALES;
    } else if (bookingType === 'technicalSurvey') {
      return SurveyType.SURVEY_TYPE_TECHNICAL;
    }
    return SurveyType.SURVEY_TYPE_SALES;
  });

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      surveyType,
      setSurveyType,
    }),
    [surveyType, setSurveyType],
  );

  return <SurveyTypeContext.Provider value={contextValue}>{children}</SurveyTypeContext.Provider>;
}

export function useSurveyType() {
  const context = useContext(SurveyTypeContext);
  if (context === undefined) {
    throw new Error('useSurveyType must be used within a SurveyTypeProvider');
  }
  return context;
}
