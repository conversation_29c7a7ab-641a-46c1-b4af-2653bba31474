import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { Resource } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';

export enum SurveyResourceType {
  TECHNICAL_SURVEYOR = 'TECHNICAL_SURVEYOR',
  SALES_SURVEYOR = 'SALES_SURVEYOR',
}

export const surveyResourceMapping = {
  [SurveyResourceType.TECHNICAL_SURVEYOR]: 3,
  [SurveyResourceType.SALES_SURVEYOR]: 4,
};

export const reverseSurveyResourceMapping = {
  3: SurveyResourceType.TECHNICAL_SURVEYOR,
  4: SurveyResourceType.SALES_SURVEYOR,
};

export type SurveyResourceForRegion = UserIdentity & Omit<Resource, 'roles'> & { roles: SurveyResourceType[] };
