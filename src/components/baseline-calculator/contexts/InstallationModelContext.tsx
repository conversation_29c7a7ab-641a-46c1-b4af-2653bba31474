import { InstallationModel } from 'components/installation-model/types/types';
import React from 'react';

export const findLatestPublishModel = (m: InstallationModel[]) => {
  const sortedModels = m
    .filter(({ publishedAt }) => publishedAt)
    .sort((a, b) => -a.publishedAt!.localeCompare(b.publishedAt!));
  return sortedModels[0] ?? null;
};
export const InstallationModelContext = React.createContext<InstallationModel | null>(null);

export const useInstallationModelContext = (): InstallationModel => {
  const installationModel = React.useContext(InstallationModelContext);
  if (!installationModel) {
    throw new Error('useInstallationModelContext must be used within a InstallationModelContextProvider');
  }
  return installationModel;
};
