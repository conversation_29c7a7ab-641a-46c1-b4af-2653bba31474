import { ReactNode, createContext, useContext } from 'react';
import { Groundwork } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.groundwork.v1';
import { getCountryFromGroundwork } from 'utils/helpers';
import { getCountryCodeFromCountry } from 'utils/marketConfigurations';
import { api } from 'utils/api';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { Alert, Typography } from '@mui/material';
import { useIntl } from 'react-intl';

export const GroundworkContext = createContext<Groundwork | null>(null);

type ContextProviderProps = {
  solutionId?: string;
  children: ReactNode;
};

export function GroundworkContextProvider({ solutionId, children }: ContextProviderProps) {
  const intl = useIntl();
  const { data: groundwork, isLoading: isLoadingGroundwork } = api.AiraBackend.getGroundworkForSolution.useQuery(
    { solutionId: solutionId! },
    {
      enabled: !!solutionId,
    },
  );

  if (isLoadingGroundwork) {
    return (
      <HeatPumpLoader>
        <Typography variant="h6" px={3}>
          Importing groundwork...
        </Typography>
      </HeatPumpLoader>
    );
  }

  if (!groundwork) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return <GroundworkContext.Provider value={groundwork}>{children}</GroundworkContext.Provider>;
}

export function useGroundwork() {
  const groundwork = useContext(GroundworkContext);
  if (groundwork == null) {
    throw new Error('Groundwork context not initialised');
  }

  const country = getCountryFromGroundwork(groundwork);
  const countryCode = getCountryCodeFromCountry(country);

  return {
    groundwork,
    countryCode,
    country,
  };
}
