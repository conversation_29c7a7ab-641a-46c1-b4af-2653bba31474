import { useIntl } from 'react-intl';
import { Alert, Stack, Typography } from '@mui/material';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { InstallationModel } from 'components/installation-model/types/types';
import React from 'react';
import useGetInstallationModels from '../queries/useGetInstallationModels';
import { useGroundwork } from './GroundworkContext';

const CountryModelsContext = React.createContext<InstallationModel[] | null>(null);

export const useCountryModelsContext = (): InstallationModel[] => {
  const countryModels = React.useContext(CountryModelsContext);
  if (!countryModels) {
    throw new Error('useCountryModelsContext must be used within a CountryModelsContextProvider');
  }
  return countryModels;
};

export function CountryModelsContextProvider({ children }: { children: React.ReactNode }) {
  const intl = useIntl();

  const { countryCode } = useGroundwork();
  const installationModelQuery = useGetInstallationModels({ countryCode });

  if (installationModelQuery.isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (installationModelQuery.isError) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  const countryModels = installationModelQuery.data as InstallationModel[];

  if (!countryModels) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return <CountryModelsContext.Provider value={countryModels}>{children}</CountryModelsContext.Provider>;
}
