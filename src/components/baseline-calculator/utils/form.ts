import { Parameter, ParameterSpec } from '../../installation-model/types/types';

export type NullableParameter = Omit<Parameter, 'value'> & { value: Parameter['value'] | null };

export function isFormValid(form: NullableParameter[]): form is Parameter[] {
  return form.length > 0 && form.every((param) => param.value !== null && param.value !== undefined);
}

export function getDefaultValue(param: ParameterSpec) {
  if (param.type === 'boolean') return param.defaultBoolean;
  if (param.type === 'number') return param.defaultNumber;
  if (param.type === 'select') return param.defaultOption?.name;
  return null;
}

export function sanitizeValue(
  { minValue, maxValue, allowDecimals }: ParameterSpec,
  value: number | null,
): number | null {
  if (value === null) return null;
  if (allowDecimals === false) {
    return Number(value.toFixed(0));
  }
  if (typeof minValue === 'number' && value < minValue) {
    return minValue;
  }
  if (typeof maxValue === 'number' && value > maxValue) {
    return maxValue;
  }
  return value;
}
