import { ManHours } from '../../installation-model/types/types';

export const toMinutes = (manHours: ManHours) => {
  if (manHours.unit !== 'MINUTES') {
    throw new Error(`Unhandled unit for ManHours: ${manHours.unit}`);
  }
  return manHours.value;
};

export const toHours = (manHours: ManHours) => {
  const minutes = toMinutes(manHours);
  return (minutes / 60).toFixed(2);
};

export const toHoursFromSeconds = (seconds: number) => (seconds / 3600).toFixed(2);
