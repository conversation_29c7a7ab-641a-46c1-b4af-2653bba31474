import { Al<PERSON>, Box, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { InstallationBookingWrapper } from 'pages/solution/[solution]/installation';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { Modal } from '@ui/components/Modal/Modal';
import { api } from '../../utils/api';
import { useInstallationModelContext } from './contexts/InstallationModelContext';
import { useGroundwork } from './contexts/GroundworkContext';
import { useIsJobHoursModalOpen, useSetJobHoursModalOpen } from './stores/setJobHoursModalStore';
import useGetBaselineCalculationParams from './queries/useGetBaselineCalculationParams';
export default function SetJobHoursModal({ solutionId }: { solutionId: string }) {
  const intl = useIntl();
  const isModalOpen = useIsJobHoursModalOpen();
  const setIsModalOpen = useSetJobHoursModalOpen();
  const groundwork = useGroundwork();
  const selectedModel = useInstallationModelContext();
  const { countryCode, groundwork: installationGroundWork } = groundwork;
  const { id: installationGroundWorkIdUUID } = installationGroundWork;
  const installationGroundworkId = installationGroundWorkIdUUID?.value;

  const { data: initialParameters, isLoading: isLoadingInitialParams } = useGetBaselineCalculationParams({
    installationGroundworkId: installationGroundworkId ?? '',
    currentModel: selectedModel,
  });

  const {
    data: installationProject,
    isLoading: isLoadingInstallationProject,
    refetch: refetchInstallationProject,
  } = api.InstallationProject.getInstallationProject.useQuery({
    solutionId,
  });

  if (isLoadingInstallationProject || isLoadingInitialParams) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (initialParameters === undefined) {
    return (
      <Box
        sx={{
          margin: '40px',
        }}
      >
        <Alert severity="error">{intl.formatMessage({ id: 'baselineCalc.text.noModel' })}</Alert>
      </Box>
    );
  }

  if (solutionId && !installationProject?.id?.value) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return (
    <Modal isModalOpen={isModalOpen} handleClose={() => setIsModalOpen(false)} width="auto" height="auto">
      {isModalOpen && !!installationProject?.id?.value && initialParameters.length > 0 && (
        <InstallationBookingWrapper
          installationGroundworkId={installationGroundworkId!}
          installationProjectId={installationProject.id.value}
          onBookedInstallation={refetchInstallationProject}
          countryCode={countryCode!}
        />
      )}
    </Modal>
  );
}
