import { z } from 'zod';
import { parameterSpecSchema, resourceSchema, unitManHours } from '../installation-model/types/zodSchemas';

export const resourceManHoursSchema = z.object({
  resourceId: resourceSchema.shape.id,
  resourceName: z.string(),
  manHours: unitManHours,
});

export const resourceGroupManHoursSchema = z.object({
  resourceGroupId: z.string(),
  resourceGroupName: z.string(),
  manHours: unitManHours,
  resources: z.array(resourceManHoursSchema),
});

export const microActivityWithManHoursSchema = z.object({
  id: z.string(),
  name: z.string(),
  resourceManHours: z.array(resourceManHoursSchema),
  totalManHours: unitManHours,
});

export const macroActivityWithManHoursSchema = z.object({
  id: z.string(),
  name: z.string(),
  microActivities: z.array(microActivityWithManHoursSchema),
  totalManHours: unitManHours,
});

export const parameterSchema = z.object({
  id: parameterSpecSchema.shape.id,
  name: z.string(),
  value: z.union([z.number(), z.string(), z.boolean()]),
});

export const manHoursCalculationSchema = z.object({
  installationModelId: z.string(),
  installationModelName: z.string(),
  installationGroundworkId: z.string(),
  results: z.object({
    totalManHours: unitManHours,
    manHoursPerResourceGroup: z.array(resourceGroupManHoursSchema),
    macroActivities: z.array(macroActivityWithManHoursSchema),
  }),
  parameters: z.array(parameterSchema),
});

export const calculationRequestBody = z.object({
  installationModelId: z.string().uuid(),
  installationGroundworkId: z.optional(z.string().uuid()),
  parameters: z.array(
    parameterSchema.omit({
      name: true,
    }),
  ),
});

export type CalculationRequestBody = z.infer<typeof calculationRequestBody>;
