import { Card, Typography } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useIntl } from 'react-intl';
import { ResourceGroupTotalManHours } from '@aira/baseline-calculator-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.baseline.v1';
import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';
import { toHoursFromSeconds } from './utils/manHours';

interface Props {
  totalsPerResourceGroup: ResourceGroupTotalManHours[];
  totalManHours: Duration;
}

function GroupedActivities({ totalsPerResourceGroup, totalManHours }: Props) {
  const intl = useIntl();

  if (!totalsPerResourceGroup) return null;
  return (
    <Grid container p={1}>
      <Card sx={{ borderRadius: 2, width: '100%' }}>
        <Grid container display="flex" justifyContent="space-between" alignItems="flex-start" direction="column" p={2}>
          {totalsPerResourceGroup.map(
            ({ resourceGroupId, resourceGroupName, totalManHours: resourceGroupTotalManHours }) => (
              <Grid key={resourceGroupId?.value} container columnSpacing={12} rowSpacing={2} sx={{ pb: 2 }}>
                <Grid>
                  <Typography variant="body1" style={{ width: '160px' }}>
                    {resourceGroupName}
                  </Typography>
                </Grid>
                <Grid display="flex" justifyContent="flex-end" style={{ width: '160px' }}>
                  <Typography variant="body1Emphasis" textAlign="right">
                    {resourceGroupTotalManHours ? toHoursFromSeconds(resourceGroupTotalManHours.seconds) : ''}
                  </Typography>
                </Grid>
              </Grid>
            ),
          )}
          <Grid width="100%">
            <hr />
          </Grid>
          <Grid container columnSpacing={12} rowSpacing={4} sx={{ pb: 2 }}>
            <Grid>
              <Typography variant="body1" style={{ width: '160px' }}>
                {intl.formatMessage({ id: 'baselineCalc.text.totalManHours' })}
              </Typography>
            </Grid>
            <Grid display="flex" justifyContent="flex-end" style={{ width: '160px' }}>
              <Typography variant="body1Emphasis" textAlign="right">
                {totalManHours ? toHoursFromSeconds(totalManHours.seconds) : ''}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Card>
    </Grid>
  );
}

export default GroupedActivities;
