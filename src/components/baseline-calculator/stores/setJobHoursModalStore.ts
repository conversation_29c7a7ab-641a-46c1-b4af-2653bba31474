// zustand store for modal state

import { create } from 'zustand';

interface SetJobHoursModalStore {
  isModalOpen: boolean;
  actions: {
    setIsModalOpen: (isOpen: boolean) => void;
  };
}

export const useSetJobHoursModalStore = create<SetJobHoursModalStore>((set) => ({
  isModalOpen: false,
  actions: {
    setIsModalOpen: (isOpen) => set({ isModalOpen: isOpen }),
  },
}));

export const useIsJobHoursModalOpen = () => useSetJobHoursModalStore((state) => state.isModalOpen);
export const useSetJobHoursModalOpen = () => useSetJobHoursModalStore((state) => state.actions.setIsModalOpen);
