import fsPromises from 'fs/promises';
import path from 'path';

export async function getLocalData() {
  // Get the path of the json file
  const filePath = path.join(process.cwd(), 'src/components/baseline-calculator/exampleModel.json');
  // Read the json file
  const jsonData = (await fsPromises.readFile(filePath)) as unknown as string;
  // Parse data as json
  const objectData = JSON.parse(jsonData);

  return objectData;
}
