export const microActivities = [
  {
    id: '8043d4d2-382a-4c7a-99b9-326eb321527d',
    name: 'Loading material and equipment on the van',
    macroActivityId: '396169b7-1656-4146-bb36-4c509470b084',
    macroActivityName: 'Mobilization and preparation',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: '445644a4-5ea5-4fc5-b64a-a4df0cefd54d',
    name: 'Unload the van - Equipment positioning',
    macroActivityId: '396169b7-1656-4146-bb36-4c509470b084',
    macroActivityName: 'Mobilization and preparation',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: 'fb1cf93d-b472-4a30-b4ce-eeab407954b2',
    name: 'Ignition of the old system',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 10,
      },
    },
  },
  {
    id: 'ee860be0-4209-4576-a0e3-e658ca87ee56',
    name: 'Ignition and verification of boiler mass flow (back & forth)',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 40,
      },
    },
  },
  {
    id: 'd59a28ed-579c-4d56-8f7c-fa5957d1b0f7',
    name: 'Electrical disconnection',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: 'd0984314-ff3e-496c-97aa-3bf439388958',
    name: 'Emptying of existing boiler',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 40,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 40,
      },
    },
  },
  {
    id: '9cffcbaf-a3da-4eeb-8c51-0a3e92188d40',
    name: 'Release of pre-existing connections',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 50,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 50,
      },
    },
  },
  {
    id: '679dab0b-20c2-4edc-8d3e-35fca2c2114f',
    name: 'Removal',
    macroActivityId: '7efc9007-4e77-40c3-95c6-c41d1d2f52cd',
    macroActivityName: 'Decommissioning of existing unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 60,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 60,
      },
    },
  },
  {
    id: '9cf1c925-dc1c-4770-98f3-d795d7fdf96c',
    name: 'Pump connection',
    macroActivityId: 'c0e86b10-38d6-4cb1-a659-7f97d934e400',
    macroActivityName: 'Flushing existing system',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: 'de84a269-550d-4647-9a8f-39ba5fbcace6',
    name: 'Pump Execution',
    macroActivityId: 'c0e86b10-38d6-4cb1-a659-7f97d934e400',
    macroActivityName: 'Flushing existing system',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 5,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 5,
      },
    },
  },
  {
    id: '0420ad94-c017-4ada-a921-df86c5f5c7ad',
    name: 'Rinsing',
    macroActivityId: 'c0e86b10-38d6-4cb1-a659-7f97d934e400',
    macroActivityName: 'Flushing existing system',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 60,
      },
    },
  },
  {
    id: '60ecedf6-86a7-47c5-acf4-6fb3e5093ea9',
    name: 'Positioning',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 40,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 40,
      },
    },
  },
  {
    id: '71414e62-7e7d-477b-aef8-2403d793805e',
    name: 'Holes preparation',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 64,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 64,
      },
    },
  },
  {
    id: '8e9d65ac-c841-4f3a-aae5-657d705ab12f',
    name: 'Passage of new pipes from outdoor to indoor unit',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 500,
      },
    },
  },
  {
    id: '971d7494-9706-4895-b100-ecba8b724629',
    name: 'Connection to the Heat Pump',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 20,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 20,
      },
    },
  },
  {
    id: 'a8fc9833-7627-46d1-bfab-9a39ef2a5387',
    name: 'Installation of Condensate drainage system',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 60,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 60,
      },
    },
  },
  {
    id: '151725a1-3539-41ae-8b86-7bde1bd04094',
    name: 'Pipes Insulation',
    macroActivityId: 'a4e7849b-923f-4f90-a595-c48d3ac99f32',
    macroActivityName: 'Outdoor unit',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 250,
      },
    },
  },
  {
    id: '6c9e8fe5-a299-4e28-83c7-d6f0627416f9',
    name: 'Duct Installation',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 250,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 250,
      },
    },
  },
  {
    id: '8ad591a1-a2b0-402f-89b8-6ecdd78de683',
    name: 'Positioning',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 20,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 20,
      },
    },
  },
  {
    id: '691dead1-47d4-4c12-9b71-a3ae47b838d6',
    name: 'Pipes cutting',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: '7efd6de5-3012-47f7-8b5c-857d47728acb',
    name: 'Installation of Control Valve - 3way',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 90,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 90,
      },
    },
  },
  {
    id: '4ba92ca9-cd03-48ef-91b7-8c1a71f69030',
    name: 'Thermostatic Valve',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 60,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 60,
      },
    },
  },
  {
    id: '5daf2eda-7364-414e-a796-defdcebb82b7',
    name: 'Connection of indoor to house hydraulic system',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 120,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 120,
      },
    },
  },
  {
    id: 'd309c127-7101-4106-a973-17243868fbee',
    name: 'Installation of water taps',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: '21e25dd7-d43a-42c4-b954-8ae6ffc5e4e5',
    name: 'System loading with working fluid',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: 'd04cb9b9-7e71-4eab-a57d-1e9e1d7fbc0f',
    name: 'Leakage test',
    macroActivityId: 'ed378c06-c2fd-4b19-830b-a8f6746504c8',
    macroActivityName: 'Indoor unit',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: '0c8f10bf-11fe-43f2-8580-0f0ff424338c',
    name: 'Thermostat installation',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 120,
      },
    },
  },
  {
    id: '488d372d-2652-4bba-8254-a70f39b922f5',
    name: 'Open junction boxes',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 20,
      },
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 20,
      },
    },
  },
  {
    id: '50d7777e-83fa-4a31-8119-47a89f0e2765',
    name: 'Power Line Identification',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 10,
      },
    },
  },
  {
    id: '83af5ff8-3d79-4112-baef-65a101474e66',
    name: 'Electrical duct installation',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 180,
      },
    },
  },
  {
    id: '92d33fbc-c3c4-4109-8766-d6ef70055169',
    name: 'Replacement old power line',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 30,
      },
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: '3a56123c-f0f4-4874-8ad4-023197fe3ffc',
    name: 'Replacement protection switch',
    macroActivityId: '5e88ccc2-acab-4626-921a-02e35a89e50d',
    macroActivityName: 'Electrical works',
    manHours: {
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 10,
      },
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 10,
      },
    },
  },
  {
    id: 'd8c5ecf4-d875-44fb-a033-04676d00d8d3',
    name: 'Connection of Power Wires',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: 'ed277f3a-ea27-4f27-b6a5-ed2168a15cfe',
    name: 'Connection of communication wires',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '9f63bceb-8578-48b1-b300-a20b57fdc9aa',
    name: 'Protection switch connection',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '7322d005-efbe-4642-8cf2-9b37ccaf2365',
    name: 'Connection of Power Wires',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '749ee891-e2d7-461f-83ed-13fe17f8b1fa',
    name: 'Connection of communication wires',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: 'b32e2a92-a7af-494f-96ab-5dc13535a81e',
    name: 'Protection switch connection',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '1d3a3ad1-e21e-436b-951b-4ae9823a95ca',
    name: 'Electrical resistance connection',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '785c9f13-ff03-48d1-8061-fc6aed7bb697',
    name: 'Dedicated electrical resistance switch',
    macroActivityId: '61313bd8-921b-40b7-a07f-0018fd67cede',
    macroActivityName: 'Electrical connection',
    manHours: {
      ELECTRICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '56d3207b-5bb4-42b3-9b51-9987a30d3c76',
    name: 'Ignition',
    macroActivityId: '49357510-3b0a-4011-88cd-b1df1f0275c7',
    macroActivityName: 'Commissioning',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 0,
      },
    },
  },
  {
    id: '943198ea-2c85-4104-829e-fb7cc4b971d9',
    name: 'Set-up - loading pump parameters',
    macroActivityId: '49357510-3b0a-4011-88cd-b1df1f0275c7',
    macroActivityName: 'Commissioning',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: 'b42eb7f7-233c-4f17-b620-f1fc44c0580b',
    name: 'Functional tests',
    macroActivityId: '49357510-3b0a-4011-88cd-b1df1f0275c7',
    macroActivityName: 'Commissioning',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 180,
      },
    },
  },
  {
    id: '406e6941-147c-4bde-acfe-50c16099922b',
    name: 'Compilation of Heat Pump documents and certification',
    macroActivityId: '49357510-3b0a-4011-88cd-b1df1f0275c7',
    macroActivityName: 'Commissioning',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 30,
      },
    },
  },
  {
    id: 'ceadd895-1263-41e0-bca6-2800f6f5f529',
    name: 'Mounting and positioning',
    macroActivityId: 'dbc0cebd-f401-493b-926d-0890851ecfde',
    macroActivityName: 'Auxiliary boiler',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 15,
      },
      INSTALLATION_APPRENTICE: {
        unit: 'MINUTES',
        value: 15,
      },
    },
  },
  {
    id: '5f26a8cb-1f82-4931-9026-76b13c574c48',
    name: 'Connection of indoor to house hydraulic system',
    macroActivityId: 'dbc0cebd-f401-493b-926d-0890851ecfde',
    macroActivityName: 'Auxiliary boiler',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 50,
      },
    },
  },
  {
    id: '5f830d99-4c44-4e02-b9ba-dfc6b476bec3',
    name: 'Electrical connection',
    macroActivityId: 'dbc0cebd-f401-493b-926d-0890851ecfde',
    macroActivityName: 'Auxiliary boiler',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 5,
      },
    },
  },
  {
    id: '05651e54-87dd-4fbf-8a98-b640036af324',
    name: 'Removal',
    macroActivityId: 'dbc0cebd-f401-493b-926d-0890851ecfde',
    macroActivityName: 'Auxiliary boiler',
    manHours: {
      INSTALLATION_TECHNICIAN: {
        unit: 'MINUTES',
        value: 60,
      },
    },
  },
];
