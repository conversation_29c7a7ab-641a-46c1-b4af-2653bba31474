import { Button } from '@ui/components/Button/Button';
import { Box, Stack, Typography } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useIntl } from 'react-intl';
import { toast } from 'react-hot-toast';
import { useState } from 'react';
import { useRouter } from 'next/router';
import Form from './Form';
import MacroActivityTable from './macroActivitiesTable';
import GroupedActivities from './groupedActivities';
import { api } from '../../utils/api';
import type { InstallationModel, Parameter } from '../installation-model/types/types';
import useGetManHoursCalculationResult from './queries/useGetManHoursCalculationResult';
import { isFormValid, NullableParameter } from './utils/form';
import ModelSelector from './ModelSelector';
import { useInstallationModelContext } from './contexts/InstallationModelContext';
import { useSetJobHoursModalOpen } from './stores/setJobHoursModalStore';

export default function ManHoursCalculator({
  initialParameters,
  installationGroundworkId,
  setSelectedModel,
  lastSavedModelWithCalculation,
}: {
  initialParameters: NullableParameter[];
  installationGroundworkId?: string;
  setSelectedModel: (model: InstallationModel) => void;
  lastSavedModelWithCalculation?: string;
}) {
  const router = useRouter();
  const isSolutionPage = router.pathname.includes('solution');
  const currentModel = useInstallationModelContext();
  const intl = useIntl();
  const setIsModalOpen = useSetJobHoursModalOpen();
  const [parametersForm, setParametersForm] = useState<NullableParameter[]>(initialParameters);
  const formIsValid = parametersForm !== undefined && isFormValid(parametersForm);

  const manHoursCalculation = useGetManHoursCalculationResult({
    installationModelId: currentModel.id,
    parameters: parametersForm as Parameter[],
    formIsValid,
  });

  const { mutateAsync: saveManHoursCalculation } = api.ManHours.saveManHoursCalculation.useMutation({
    onMutate() {
      toast.loading(intl.formatMessage({ id: 'baselineCalc.notify.saving' }));
    },
    onSuccess() {
      toast.dismiss();
      toast.success(intl.formatMessage({ id: 'baselineCalc.notify.success' }));
    },
    onError() {
      toast.dismiss();
      toast.error(intl.formatMessage({ id: 'baselineCalc.notify.error' }));
    },
  });

  const handleSubmit = async () => {
    if (installationGroundworkId && formIsValid) {
      await saveManHoursCalculation({
        installationGroundworkId,
        installationModelId: currentModel.id,
        parameters: parametersForm.map((param) => ({
          id: param.id,
          value: param.value,
        })),
      });
    } else {
      toast.error(intl.formatMessage({ id: 'baselineCalc.notify.error' }));
    }
  };

  return (
    <Box
      sx={{
        width: '100%',
        padding: '20px 0 40px 0',
      }}
    >
      <Grid container spacing={2}>
        <Stack alignItems="flex-start" justifyContent="space-between" spacing={2}>
          <Typography variant="headline1" sx={{ paddingLeft: '8px' }}>
            {intl.formatMessage({ id: 'baselineCalc.title.calculateInstallationBaseline' })}
          </Typography>
          <ModelSelector
            setSelectedModel={setSelectedModel}
            lastSavedModelWithCalculation={lastSavedModelWithCalculation}
          />
        </Stack>
        <Grid container spacing={2} display="grid" pt={2} gridTemplateColumns="repeat(2, 1fr)">
          <Grid sx={{ marginBottom: '20px' }}>
            <Typography variant="headline2">{intl.formatMessage({ id: 'baselineCalc.title.inputs' })}</Typography>
            {!!currentModel && (
              <Form
                currentModel={currentModel}
                parametersForm={parametersForm}
                setParametersForm={setParametersForm}
                initialParameters={initialParameters}
              />
            )}
            {manHoursCalculation.data &&
              manHoursCalculation.data.result &&
              manHoursCalculation.data.result.totalManHours && (
                <>
                  <Typography variant="headline2" sx={{ marginTop: '20px' }}>
                    {intl.formatMessage({ id: 'baselineCalc.title.summary' })}
                  </Typography>
                  <GroupedActivities
                    totalsPerResourceGroup={manHoursCalculation.data.result.totalsPerResourceGroup}
                    totalManHours={manHoursCalculation.data.result.totalManHours}
                  />
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    gap={3}
                    sx={{
                      borderRadius: '67px',
                    }}
                  >
                    <Button sx={{ mt: 1 }} onClick={handleSubmit} fullWidth>
                      Save calculation
                    </Button>
                    {manHoursCalculation && isSolutionPage && (
                      <Button variant="outlined" sx={{ mt: 1 }} onClick={() => setIsModalOpen(true)} fullWidth>
                        Add job hours
                      </Button>
                    )}
                  </Stack>
                </>
              )}
          </Grid>
          {manHoursCalculation.data && manHoursCalculation.data.result && (
            <Grid>
              <Typography variant="headline2">{intl.formatMessage({ id: 'baselineCalc.title.output' })}</Typography>
              <MacroActivityTable macroActivities={manHoursCalculation.data.result.macroActivities} />
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
