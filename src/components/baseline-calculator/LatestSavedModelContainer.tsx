import { Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { api } from 'utils/api';
import { useGroundwork } from './contexts/GroundworkContext';
import ModelContainer from './ModelContainer';

export default function LatestSavedModelContainer({ solutionId }: { solutionId: string }) {
  const intl = useIntl();
  const groundwork = useGroundwork();

  const { data: latestSavedModelValues, isLoading: isLoadingSavedModel } =
    api.ManHours.getLatestInstallationModelParameters.useQuery({
      installationGroundworkId: groundwork.groundwork.id!.value,
    });

  if (isLoadingSavedModel) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  return <ModelContainer latestSavedModelValues={latestSavedModelValues} solutionId={solutionId} />;
}
