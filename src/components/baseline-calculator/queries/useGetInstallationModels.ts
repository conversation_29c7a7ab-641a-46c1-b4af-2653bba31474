import { api } from 'utils/api';

const useGetInstallationModels = ({ countryCode }: { countryCode: string }) =>
  api.ManHours.getAllInstallationModels.useQuery(
    {},
    {
      enabled: countryCode !== null,
      gcTime: 0,
      select: (installationModels) => {
        if (installationModels && installationModels.length > 0) {
          return installationModels.filter((model) => model.countryCode === countryCode);
        }
        return null;
      },
    },
  );

export default useGetInstallationModels;
