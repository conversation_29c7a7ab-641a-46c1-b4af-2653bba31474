import { api } from 'utils/api';
import { getDefaultValue } from '../utils/form';
import { InstallationModel } from 'components/installation-model/types/types';

const useGetBaselineCalculationParams = ({
  installationGroundworkId,
  currentModel,
}: {
  installationGroundworkId: string;
  currentModel?: InstallationModel;
}) => {
  const {
    data: parameters,
    isLoading: isLoadingSavedParams,
    isError: isErrorSavedParams,
    error,
  } = api.ManHours.getCalculationInstallationModelParameters.useQuery(
    {
      installationGroundworkId,
      installationModelId: currentModel?.id ?? '',
    },
    {
      enabled: !!installationGroundworkId && !!currentModel?.id,
      refetchOnWindowFocus: false,
      gcTime: 0,
      select: (savedParams) => {
        const savedParamsData = savedParams?.parameters?.parameters || [];
        const modelParameters = currentModel?.parameters || [];

        // Create a map of saved parameter values by ID
        const savedParamsMap = new Map();
        savedParamsData.forEach((param: any) => {
          const paramId = param.parameterId?.value;
          if (paramId) {
            savedParamsMap.set(
              paramId,
              param.value?.$case === 'numericValue'
                ? param.value.numericValue
                : param.value?.$case === 'stringValue'
                  ? param.value.stringValue
                  : param.value?.$case === 'booleanValue'
                    ? param.value.booleanValue
                    : null,
            );
          }
        });

        // First set default values for all model parameters, then override with saved values
        return modelParameters.map((param: any) => {
          const defaultValue = getDefaultValue(param) ?? null;
          const savedValue = savedParamsMap.get(param.id);

          return {
            id: param.id || '',
            name: param.name || '',
            value: savedValue !== undefined ? savedValue : defaultValue,
          };
        });
      },
    },
  );

  // When user changes the model we want to show empty inputs with model parameter names
  if (error?.data?.code === 'NOT_FOUND' && currentModel?.parameters) {
    const emptyParameters = currentModel.parameters.map((param: any) => ({
      id: param.id || '',
      name: param.name || '',
      value: null,
    }));

    return {
      data: emptyParameters,
      isLoading: false,
      error: null,
    };
  }

  return {
    data: parameters,
    isLoading: isLoadingSavedParams,
    error: isErrorSavedParams,
  };
};

export default useGetBaselineCalculationParams;
