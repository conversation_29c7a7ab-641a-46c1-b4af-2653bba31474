import { api } from 'utils/api';
import { Parameter } from 'components/installation-model/types/types';

const useGetManHoursCalculationResult = ({
  installationModelId,
  parameters,
  formIsValid,
}: {
  installationModelId: string;
  parameters: Parameter[];
  formIsValid: boolean;
}) =>
  api.ManHours.getManHoursCalculationResult.useQuery(
    {
      installationModelId,
      parameters: formIsValid ? parameters : [],
    },
    {
      enabled: !!installationModelId && formIsValid,
      gcTime: 0,
    },
  );

export default useGetManHoursCalculationResult;
