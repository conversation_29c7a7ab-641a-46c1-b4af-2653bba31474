import { Box, MenuItem, SelectChangeEvent, Select } from '@mui/material';
import { InstallationModel } from 'components/installation-model/types/types';
import { useCountryModelsContext } from './contexts/CountryModelsContext';
import { useInstallationModelContext } from './contexts/InstallationModelContext';

export default function ModelSelector({
  setSelectedModel,
  lastSavedModelWithCalculation,
}: {
  setSelectedModel: (model: InstallationModel) => void;
  lastSavedModelWithCalculation?: string;
}) {
  const countryModels = useCountryModelsContext();
  const selectedModel = useInstallationModelContext();

  const handleModelChange = (event: SelectChangeEvent<string>) => {
    const modelId = event.target.value;
    const model = countryModels.find((m) => m.id === modelId);
    if (model) {
      setSelectedModel(model);
    }
  };

  const lastSavedModelPublishedAt = countryModels.find((m) => m.id === lastSavedModelWithCalculation)?.publishedAt;
  const modelList = countryModels.slice().sort((a, b) => {
    const dateA = a.publishedAt ? new Date(a.publishedAt).getTime() : 0;
    const dateB = b.publishedAt ? new Date(b.publishedAt).getTime() : 0;
    return dateA - dateB;
  });

  let filteredModelList;
  if (lastSavedModelPublishedAt) {
    filteredModelList = modelList.filter((m) => m.publishedAt && m.publishedAt >= lastSavedModelPublishedAt);
  } else {
    // If no last saved model, show the only latest published model
    filteredModelList = modelList.slice(-1);
  }

  return (
    <Box pl={1}>
      <Select value={selectedModel?.id} onChange={handleModelChange} label="Model" name="selectModel">
        {filteredModelList.map((model) => (
          <MenuItem key={model.id} value={model.id}>
            {model.name}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
}
