import { useState } from 'react';
import { Alert, Box } from '@mui/material';
import { useIntl } from 'react-intl';
import { InstallationModel } from 'components/installation-model/types/types';
import { GetCalculationInstallationModelParametersResponse } from '@aira/baseline-calculator-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.baseline.v1';
import { useCountryModelsContext } from './contexts/CountryModelsContext';
import { findLatestPublishModel, InstallationModelContext } from './contexts/InstallationModelContext';
import ParametersContainer from './ParametersContainer';
import SetJobHoursModal from './SetJobHoursModal';

export default function ModelContainer({
  solutionId,
  latestSavedModelValues,
}: {
  solutionId: string;
  latestSavedModelValues?: GetCalculationInstallationModelParametersResponse;
}) {
  const intl = useIntl();
  const countryModels = useCountryModelsContext();
  const lastSavedModelWithCalculation = latestSavedModelValues?.parameters?.installationModelId?.value;
  const [selectedModel, setSelectedModel] = useState<InstallationModel | null>(
    countryModels.find((model) => model.id === lastSavedModelWithCalculation) ?? findLatestPublishModel(countryModels),
  );

  if (!selectedModel) {
    return (
      <Box
        sx={{
          margin: '40px',
        }}
      >
        <Alert severity="error">{intl.formatMessage({ id: 'baselineCalc.text.noModel' })}</Alert>
      </Box>
    );
  }
  return (
    <InstallationModelContext.Provider value={selectedModel}>
      <ParametersContainer
        setSelectedModel={setSelectedModel}
        lastSavedModelWithCalculation={lastSavedModelWithCalculation}
      />
      <SetJobHoursModal solutionId={solutionId} />
    </InstallationModelContext.Provider>
  );
}
