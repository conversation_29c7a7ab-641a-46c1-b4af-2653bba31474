import Grid from '@mui/material/Grid';
import { SyntheticEvent } from 'react';
import type { ParameterSpec, Parameter, InstallationModel } from '../installation-model/types/types';
import { BooleanInputRenderer, NumericInput<PERSON>enderer, SelectInputRenderer } from './inputRenderers';
import { NullableParameter, sanitizeValue } from './utils/form';

function Form({
  currentModel,
  initialParameters,
  parametersForm,
  setParametersForm,
}: {
  currentModel: InstallationModel;
  initialParameters: NullableParameter[];
  parametersForm: NullableParameter[];
  setParametersForm: (parameters: NullableParameter[]) => void;
}) {
  const updateForm = ({
    parameterId,
    value,
  }: {
    parameterId: Parameter['id'];
    value: number | string | boolean | null;
  }) => {
    const parameter = parametersForm.find((p) => p.id === parameterId);
    if (parameter) {
      setParametersForm(parametersForm.map((p) => (p.id === parameterId ? { ...p, value } : p)));
    } else {
      const modelParameterSpec = initialParameters.find((p) => p.id === parameterId);
      if (modelParameterSpec) {
        setParametersForm([...parametersForm, { id: parameterId, value, name: modelParameterSpec.name }]);
      }
    }
  };

  const updateFormNumbers = (value: number | null, parameter: ParameterSpec) => {
    updateForm({ parameterId: parameter.id, value: sanitizeValue(parameter, value) });
  };

  const renderParameter = (parameter: ParameterSpec) => {
    if (parameter.type === 'number') {
      const value = parametersForm.find((param) => param.id === parameter.id)?.value as number | null;
      const onChange = (v: number | null) => updateFormNumbers(v, parameter);
      return NumericInputRenderer({ input: parameter, onChange, value });
    }
    if (parameter.type === 'select') {
      const value = parametersForm.find((param) => param.id === parameter.id)?.value as string | null;
      const onChange = (v: { key: string; value: string }) => updateForm({ parameterId: parameter.id, value: v.value });
      return SelectInputRenderer({ input: parameter, onChange, value });
    }
    if (parameter.type === 'boolean') {
      const value = parametersForm.find((param) => param.id === parameter.id)?.value as boolean | null;
      const onChange = (_event: SyntheticEvent<Element, Event>, checked: boolean) =>
        updateForm({ parameterId: parameter.id, value: checked });
      return BooleanInputRenderer({ input: parameter, onChange, value });
    }
    return null;
  };

  return <Grid container>{parametersForm && currentModel.parameters?.map(renderParameter)}</Grid>;
}

export default Form;
