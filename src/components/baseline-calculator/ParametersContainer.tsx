import { Alert, Box, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { InstallationModel } from 'components/installation-model/types/types';
import ManHoursCalculator from './ManHoursCalculator';
import { useInstallationModelContext } from './contexts/InstallationModelContext';
import { useGroundwork } from './contexts/GroundworkContext';
import useGetBaselineCalculationParams from './queries/useGetBaselineCalculationParams';

export default function ParametersContainer({
  setSelectedModel,
  lastSavedModelWithCalculation,
}: {
  setSelectedModel: (model: InstallationModel) => void;
  lastSavedModelWithCalculation?: string;
}) {
  const intl = useIntl();
  const groundwork = useGroundwork();
  const selectedModel = useInstallationModelContext();
  const { groundwork: installationGroundWork } = groundwork;
  const { id: installationGroundWorkIdUUID } = installationGroundWork;
  const installationGroundworkId = installationGroundWorkIdUUID?.value;

  const { data: initialParameters, isLoading: isLoadingInitialParams } = useGetBaselineCalculationParams({
    installationGroundworkId: installationGroundworkId ?? '',
    currentModel: selectedModel,
  });

  if (isLoadingInitialParams) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.loading' })}</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (initialParameters === undefined) {
    return (
      <Box
        sx={{
          margin: '40px',
        }}
      >
        baseline
        <Alert severity="error">{intl.formatMessage({ id: 'baselineCalc.text.noModel' })}</Alert>
      </Box>
    );
  }

  return (
    <ManHoursCalculator
      initialParameters={initialParameters}
      installationGroundworkId={installationGroundworkId}
      setSelectedModel={setSelectedModel}
      lastSavedModelWithCalculation={lastSavedModelWithCalculation}
    />
  );
}
