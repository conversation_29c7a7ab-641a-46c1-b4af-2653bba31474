import {
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableCell,
  TableRow,
  Typography,
  Paper,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from '@mui/material';
import Grid from '@mui/material/Grid';
import { useIntl } from 'react-intl';
import { GridExpandMoreIcon } from '@mui/x-data-grid';
import { BaselineCalculationResult_MacroActivityManHours } from '@aira/baseline-calculator-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.baseline.v1';
import { toHoursFromSeconds } from './utils/manHours';

interface Props {
  macroActivities: BaselineCalculationResult_MacroActivityManHours[];
}

function MacroActivityTable({ macroActivities }: Props) {
  const intl = useIntl();

  if (!macroActivities) return null;

  return (
    <TableContainer component={Paper} sx={{ mt: 1 }}>
      <Table sx={{ minWidth: '280px', background: 'white' }} aria-label="output-table">
        <TableHead>
          <TableRow>
            <TableCell sx={{ paddingLeft: 4 }}>
              {intl.formatMessage({ id: 'baselineCalc.table.macroActivity' })}
            </TableCell>
            <TableCell sx={{ width: '30px' }}>
              {intl.formatMessage({ id: 'baselineCalc.table.totalManHours' })}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {macroActivities.map((macroActivity) => (
            <TableRow key={macroActivity.macroActivityName}>
              <TableCell>
                <Accordion sx={{ background: 'white' }}>
                  <AccordionSummary
                    aria-controls="panel1bh-content"
                    id="panel1bh-header"
                    expandIcon={<GridExpandMoreIcon />}
                  >
                    <Typography sx={{ flexShrink: 0 }}>{macroActivity.macroActivityName}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {macroActivity.microActivities.map((microActivity) => (
                      <Grid
                        container
                        rowSpacing={0}
                        sx={{ pb: 2 }}
                        key={macroActivity.macroActivityName + microActivity.microActivityName}
                      >
                        <Grid style={{ width: '220px', maxWidth: '220px', minWidth: '220px' }}>
                          <Typography variant="body2" sx={{ marginLeft: '14px' }}>
                            {microActivity.microActivityName}
                          </Typography>
                        </Grid>
                        <Grid display="flex" justifyContent="flex-end" style={{ minWidth: '40px', width: '40px' }}>
                          <Typography variant="body2Emphasis" textAlign="right">
                            {microActivity.totalManHours ? toHoursFromSeconds(microActivity.totalManHours.seconds) : ''}
                          </Typography>
                        </Grid>
                      </Grid>
                    ))}
                  </AccordionDetails>
                </Accordion>
              </TableCell>
              <TableCell style={{ verticalAlign: 'top' }}>
                <Typography variant="body1Emphasis" sx={{ width: '33%', flexShrink: 0 }}>
                  {macroActivity.totalManHours ? toHoursFromSeconds(macroActivity.totalManHours.seconds) : ''}
                </Typography>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

export default MacroActivityTable;
