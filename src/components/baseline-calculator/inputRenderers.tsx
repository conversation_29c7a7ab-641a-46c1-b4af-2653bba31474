import { SyntheticEvent } from 'react';
import { MenuItem, InputLabel, Typography, Radio, Stack } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useIntl } from 'react-intl';
import { NumericFormField } from '@ui/components/NumericFormField/NumericFormField';
import { Select } from '@ui/components/Select/Select';
import type { ParameterSpec } from '../installation-model/types/types';

export function NumericInputRenderer({
  input,
  onChange,
  value,
}: {
  input: ParameterSpec;
  onChange: (v: number | null) => void;
  value: number | null;
}) {
  const { id, name } = input;
  return (
    <Grid key={id} sx={{ width: '100%', marginTop: '10px' }}>
      <NumericFormField
        name={name}
        label={name}
        value={value}
        onChange={onChange}
        sx={{ width: '100%' }}
        size="small"
      />
    </Grid>
  );
}

export function SelectInputRenderer({
  input,
  onChange,
  value,
}: {
  input: ParameterSpec;
  onChange: (v: { key: string; value: string }) => void;
  value: string | null;
}) {
  const { name, options, id, defaultOption } = input;
  return (
    <Grid key={id} sx={{ width: '100%', marginTop: '10px' }}>
      <Select
        label={name}
        name={name}
        defaultValue={defaultOption?.value || ''}
        onChange={(e) =>
          onChange({
            key: name,
            value: e.target.value as string,
          })
        }
        value={value || defaultOption?.value || ''}
        size="small"
      >
        {options?.map((option) => (
          <MenuItem key={option.name} value={option.name}>
            {option.name}
          </MenuItem>
        ))}
      </Select>
    </Grid>
  );
}

export function BooleanInputRenderer({
  input,
  onChange,
  value,
}: {
  input: ParameterSpec;
  onChange: (e: SyntheticEvent<Element, Event>, checked: boolean) => void;
  value: boolean | null;
}) {
  const intl = useIntl();
  const { id, name } = input;
  const handleYes = (e: SyntheticEvent<Element, Event>) => onChange(e, true);
  const handleNo = (e: SyntheticEvent<Element, Event>) => onChange(e, false);
  return (
    <Grid key={id} sx={{ width: '100%', marginTop: '10px' }}>
      <InputLabel htmlFor={name}>
        <Typography variant="inputLabel">{name}</Typography>
      </InputLabel>
      <Stack direction="row" spacing={3}>
        <Stack direction="row" sx={{ alignItems: 'center' }}>
          <label htmlFor={`${name}Yes`}>
            <Typography variant="inputLabel">{intl.formatMessage({ id: 'baselineCalc.boolean.true' })}</Typography>
          </label>
          <Radio name={`${name}Yes`} checked={value === true} onChange={handleYes} />
        </Stack>
        <Stack direction="row" sx={{ alignItems: 'center' }}>
          <label htmlFor={`${name}No`}>
            <Typography variant="inputLabel">{intl.formatMessage({ id: 'baselineCalc.boolean.false' })}</Typography>
          </label>
          <Radio name={`${name}No`} checked={value === false} onChange={handleNo} />
        </Stack>
      </Stack>
    </Grid>
  );
}
