import { CircularProgress, <PERSON>, Stack, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { api } from 'utils/api';
import { useMemo, useState } from 'react';
import { Box } from '@mui/system';
import { InstallationProjectStage } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import {
  InstallationProjectJob_JobType,
  InstallationProjectResourceType,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { UserIdentityView } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { PeopleIcon } from '@ui/components/Icons/PeopleIcon/PeopleIcon';
import { FaceSmilingCircleOutlinedIcon } from '@ui/components/StandardIcons/FaceSmilingCircleOutlinedIcon';
import { CalendarClockOutlinedIcon } from '@ui/components/StandardIcons/CalendarClockOutlinedIcon';
import { CalendarCheckOutlinedIcon } from '@ui/components/StandardIcons/CalendarCheckOutlinedIcon';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { BluetoothOutlinedIcon } from '@ui/components/StandardIcons/BluetoothOutlinedIcon';
import { surface } from '@ui/theme/colors';
import { CheckOutlinedIcon } from '@ui/components/StandardIcons/CheckOutlinedIcon';
import { CheckIndeterminateOutlinedIcon } from '@ui/components/StandardIcons/CheckIndeterminateOutlinedIcon';
import { LinkPairOutlinedIcon } from '@ui/components/StandardIcons/LinkPairOutlinedIcon';
import {
  getTeamLeadName,
  getCommitionedDate,
  getInstallationComplete,
  getInstallationIncomplete,
  getPlannedStartDate,
  getActualStartDate,
  getStageText,
} from 'components/ongoing-installations/helpers';
import HoursForCompletionCell from 'components/ongoing-installations/HoursForCompletionCell';
import { useRegionContext } from 'context/RegionContext';
import { useCountryCodeContext } from 'context/CountryCodeContext';
import { subWeeks } from 'date-fns';
import DatePickers from './DatePickers';

type DateRange = {
  startDate: Date | undefined;
  endDate: Date | undefined;
};

const OngoingInstallationsTable = () => {
  const intl = useIntl();
  const twoWeeksFromNow = useMemo(() => new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), []);
  const region = useRegionContext();
  const countryCode = useCountryCodeContext();

  const regionId = region.id?.value;

  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: new Date(),
    endDate: twoWeeksFromNow,
  });
  const [activeCellId, setActiveCellId] = useState<string | null>(null);
  const { data: incompletedProjects, isLoading: isLoadingIncompletedProjects } =
    api.InstallationProject.getInstallationProjects.useQuery(
      {
        operationalUnitId: regionId ?? '',
        stages: [
          InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION,
          InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION,
          InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE,
        ],
      },
      {
        enabled: !!regionId,
        trpc: {
          context: {
            skipBatch: true,
          },
        },
      },
    );

  const twoWeeksAgo = useMemo(() => subWeeks(new Date(), 2), []);
  const { data: completedProjects, isLoading: isLoadingCompletedProjects } =
    api.InstallationProject.getInstallationProjects.useQuery(
      {
        operationalUnitId: regionId ?? '',
        stages: [InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED],
        updatedAfter: twoWeeksAgo,
      },
      {
        enabled: !!regionId,
        trpc: {
          context: {
            skipBatch: true,
          },
        },
      },
    );

  const projects = useMemo(() => {
    const incompleted = incompletedProjects || [];
    const completed = completedProjects || [];
    return [...incompleted, ...completed];
  }, [completedProjects, incompletedProjects]);

  const filteredProjects = useMemo(() => {
    return projects.filter((project) => {
      if (!dateRange?.startDate || !dateRange?.endDate) {
        return true;
      }
      const plannedStartDate = getPlannedStartDate(project);
      if (!plannedStartDate) {
        return true;
      }
      const plannedDateOnly = new Date(plannedStartDate).setHours(0, 0, 0, 0);
      const startDateOnly = new Date(dateRange.startDate).setHours(0, 0, 0, 0);
      const endDateOnly = new Date(dateRange.endDate).setHours(0, 0, 0, 0);

      const isInRange = plannedDateOnly >= startDateOnly && plannedDateOnly <= endDateOnly;
      return isInRange;
    });
  }, [projects, dateRange]);

  const teamLeadUserIds = useMemo(
    () =>
      Array.from(
        new Set(
          (projects ?? [])
            .flatMap((project) =>
              (project.installationProject?.assignedResources ?? [])
                .filter(
                  (res) => res.type === InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_TEAM_LEAD,
                )
                .map((res) => res.userId?.value),
            )
            .filter((id): id is string => Boolean(id)),
        ),
      ),
    [projects],
  );

  const { data: teamLeads } = api.Resource.getResourceInfo.useQuery(
    {
      userIds: teamLeadUserIds,
      view: UserIdentityView.USER_IDENTITY_VIEW_MINIMAL,
    },
    { enabled: teamLeadUserIds.length > 0 },
  );

  const teamLeadMap = useMemo(
    () =>
      new Map(
        (teamLeads ?? [])
          .filter((user) => user.userId?.value)
          .map((user) => [user.userId!.value!, `${user.firstName} ${user.lastName}`]),
      ),
    [teamLeads],
  );

  const rows = (filteredProjects ?? []).map((project, index) => {
    const projectId = project.installationProject?.id?.value;
    const stage = project.installationProject?.stage;
    const stageText = getStageText(stage || null);
    return {
      id: projectId ? `${projectId}-${index}` : `project-${index}`,
      project: project,
      customer: `${project.contact?.firstName ?? ''} ${project.contact?.lastName ?? ''}`,
      teamLead: getTeamLeadName(project, teamLeadMap ?? new Map()),
      stage: intl.formatMessage({ id: stageText } as MessageDescriptor),
      plannedStartDate: getPlannedStartDate(project),
      actualStartDate: getActualStartDate(project),
      commissioningDate: getCommitionedDate(project),
      installationIncomplete: getInstallationIncomplete(project),
      installationComplete: getInstallationComplete(project),
      hoursForCompletionInstallers: project,
      hoursForCompletionElectritians: project,
      hoursForCompletionLandscapers: project,
    };
  });

  const columns = [
    {
      field: 'customer',
      headerName: 'Customers',
      width: 300,
      renderHeader: () => <FormattedMessage id="ongoingInstallations.customers" defaultMessage="Customers" />,
    },
    {
      field: 'teamLead',
      headerName: 'Team lead',
      width: 300,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <PersonOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.teamLead" defaultMessage="Team lead" />
        </span>
      ),
    },
    {
      field: 'stage',
      headerName: 'Project Stage',
      width: 180,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <FaceSmilingCircleOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.projectStage" defaultMessage="Project Stage" />
        </span>
      ),
    },
    {
      field: 'plannedStartDate',
      headerName: 'Planned start date',
      width: 170,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <CalendarClockOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.plannedStartDate" defaultMessage="Planned start date" />
        </span>
      ),
    },
    {
      field: 'actualStartDate',
      headerName: 'Actual start date',
      width: 160,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <CalendarCheckOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.actualStartDate" defaultMessage="Actual start date" />
        </span>
      ),
    },
    {
      field: 'commissioningDate',
      headerName: 'Commissioning date',
      width: 180,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <BluetoothOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.commissioningDate" defaultMessage="Commissioning date" />
        </span>
      ),
    },
    {
      field: 'installationIncomplete',
      headerName: 'Installation incomplete',
      width: 230,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <CheckIndeterminateOutlinedIcon width={16} height={16} />
          <FormattedMessage
            id="ongoingInstallations.installationIncompleteDate"
            defaultMessage="Installation incomplete date"
          />
        </span>
      ),
    },
    {
      field: 'installationComplete',
      headerName: 'Installation complete',
      width: 210,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <CheckOutlinedIcon width={16} height={16} />
          <FormattedMessage
            id="ongoingInstallations.installationCompleteDate"
            defaultMessage="Installation complete date"
          />
        </span>
      ),
    },
    {
      field: 'hoursForCompletionInstallers',
      headerName: 'Hours for completion',
      width: 170,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <PeopleIcon width={16} height={16} />
          Add installer hours
        </span>
      ),
      renderCell: (params: any) => {
        return (
          <HoursForCompletionCell
            key={`${params.id}-hoursForCompletionInstallers`}
            project={params.row.project}
            jobType={InstallationProjectJob_JobType.JOB_TYPE_PLUMBING}
            isActive={activeCellId === `${params.id}-hoursForCompletionInstallers`}
            onActivate={() => setActiveCellId(`${params.id}-hoursForCompletionInstallers`)}
            onSuccess={() => setActiveCellId(null)}
          />
        );
      },
    },
    {
      field: 'hoursForCompletionElectritians',
      headerName: 'Hours for completion electricians',
      width: 180,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <PeopleIcon width={16} height={16} />
          Add electrician hours
        </span>
      ),
      renderCell: (params: any) => {
        return (
          <HoursForCompletionCell
            key={`${params.id}-hoursForCompletionElectritians`}
            project={params.row.project}
            jobType={InstallationProjectJob_JobType.JOB_TYPE_ELECTRICAL}
            isActive={activeCellId === `${params.id}-hoursForCompletionElectritians`}
            onActivate={() => setActiveCellId(`${params.id}-hoursForCompletionElectritians`)}
            onSuccess={() => setActiveCellId(null)}
          />
        );
      },
    },
    ...(countryCode === 'DE'
      ? [
          {
            field: 'hoursForCompletionLandscapers',
            headerName: 'Hours for completion landscapers',
            width: 190,
            renderHeader: () => (
              <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <PeopleIcon width={16} height={16} />
                Add landscaper hours
              </span>
            ),
            renderCell: (params: any) => {
              if (true) {
                return (
                  <HoursForCompletionCell
                    key={`${params.id}-hoursForCompletionLandscapers`}
                    project={params.row.project}
                    jobType={InstallationProjectJob_JobType.JOB_TYPE_LANDSCAPING}
                    isActive={activeCellId === `${params.id}-hoursForCompletionLandscapers`}
                    onActivate={() => setActiveCellId(`${params.id}-hoursForCompletionLandscapers`)}
                    onSuccess={() => setActiveCellId(null)}
                  />
                );
              } else {
                return null;
              }
            },
          },
        ]
      : []),
    {
      field: 'jobsOverview',
      headerName: 'Jobs overview',
      width: 150,
      renderHeader: () => (
        <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <LinkPairOutlinedIcon width={16} height={16} />
          <FormattedMessage id="ongoingInstallations.jobsOverview" defaultMessage="Jobs overview" />
        </span>
      ),
      renderCell: (params: any) => {
        return (
          <Link
            width="100%"
            height="100%"
            display="flex"
            alignItems="center"
            justifyContent="flex-start"
            target="_blank"
            href={`/solution/${params.row.project.energySolution?.id?.value}/installation`}
          >
            <Typography variant="body2Emphasis" sx={{ color: 'primary.main', textDecoration: 'underline' }}>
              <FormattedMessage id="ongoingInstallations.link" defaultMessage="Link" />
            </Typography>
          </Link>
        );
      },
    },
  ];
  return (
    <Stack>
      <Typography variant="headline1">
        <FormattedMessage id="ongoingInstallations.title" defaultMessage="Ongoing Installations" />
      </Typography>
      <Typography variant="headline2" sx={{ marginTop: 2 }}>
        {region?.name}
      </Typography>

      <DatePickers dateRange={dateRange} setDateRange={setDateRange} />

      {(isLoadingIncompletedProjects || isLoadingCompletedProjects) && (
        <Box
          sx={{
            height: '70vh',
            width: '90vw',
            margin: 'auto',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
          }}
        >
          <CircularProgress />
        </Box>
      )}
      {!isLoadingIncompletedProjects && !isLoadingCompletedProjects && (
        <Stack sx={{ display: 'flex', flexDirection: 'column', height: 'auto' }}>
          <DataGrid
            key={`filtered-${filteredProjects?.length}-${dateRange?.startDate?.getTime()}-${dateRange?.endDate?.getTime()}`}
            disableColumnSorting={true}
            rows={rows}
            columns={columns}
            disableColumnSelector={true}
            disableColumnMenu={true}
            onCellClick={(params) => {
              // Clear active cell if clicking on a cell that's not a HoursForCompletionCell
              if (
                params.field !== 'hoursForCompletionInstallers' &&
                params.field !== 'hoursForCompletionElectritians' &&
                params.field !== 'hoursForCompletionLandscapers'
              ) {
                setActiveCellId(null);
              }
            }}
            getRowClassName={(params) => {
              const rowIndex = rows.findIndex((row) => row.id === params.id);
              return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
            }}
            sx={{
              '& .even-row .MuiDataGrid-cell:not([data-field="customer"])': {
                backgroundColor: surface[100],
              },
              '& .MuiDataGrid-cell[data-field="customer"]': {
                borderRight: `1px solid ${surface[200]}`,
                borderLeft: 'none',
              },
              '& .MuiDataGrid-columnHeader[data-field="customer"]': {
                borderRight: `1px solid ${surface[200]}`,
                borderLeft: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderRight: `1px solid ${surface[100]}`,
                borderLeft: `1px solid ${surface[100]}`,
              },
              '& .MuiDataGrid-columnHeader': {
                borderRight: `1px solid ${surface[100]}`,
                borderLeft: `1px solid ${surface[100]}`,
              },
              '& .MuiDataGrid-cell:last-child': {
                borderRight: 'none',
              },
              '& .MuiDataGrid-columnHeader:last-child': {
                borderRight: 'none',
              },
            }}
          />
        </Stack>
      )}
    </Stack>
  );
};

export default OngoingInstallationsTable;
