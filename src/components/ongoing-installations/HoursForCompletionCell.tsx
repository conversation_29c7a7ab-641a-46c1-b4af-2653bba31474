import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Box, Button, TextField, Stack } from '@mui/material';
import { CheckCircleFilledIcon } from '@ui/components/StandardIcons/CheckCircleFilledIcon';
import { CrossSmallOutlinedIcon } from '@ui/components/StandardIcons/CrossSmallOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import {
  getExtendedSegments,
  getInstallationComplete,
  getIsIncompleteInstallationPlanned,
  getIsJobInProgress,
  getExtendedHours,
  getHoursForAllResources,
  getIsActualStartDatePerJob,
} from './helpers';
import {
  InstallationProjectJob,
  InstallationProjectJob_JobType,
  InstallationProjectJob_WorkSegmentPlannedType,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { FormattedMessage } from 'react-intl';
import { getTeamSizeForRole } from 'components/installation-booking/queries/useGetManHours';
import { reverseResourceMapping } from 'components/installation-planning/types/planningTypes';

const HoursForCompletionCell = React.memo(
  ({
    project,
    jobType,
    isActive = false,
    onActivate,
    onSuccess,
  }: {
    project: FullInstallationProjectEntity;
    jobType: InstallationProjectJob_JobType;
    isActive?: boolean;
    onActivate?: () => void;
    onSuccess?: () => void;
  }) => {
    const [hoursForCompletionInput, setHoursForCompletionInput] = useState<number | undefined>(undefined);
    const [isSuccess, setIsSuccess] = useState(false);

    const extendedSegments = getExtendedSegments(project, jobType);
    const isIncompleteInstallationPlanned = getIsIncompleteInstallationPlanned(project, jobType);
    const isJobInProgress = getIsJobInProgress(project, jobType);
    const job = project.installationProject?.jobs.find((job) => job.type === jobType);
    const isActualStartDatePerJob = getIsActualStartDatePerJob(project, jobType);
    const installationComplete = getInstallationComplete(project);
    const isOngoingInstallation = isJobInProgress && isActualStartDatePerJob && !installationComplete;
    const isSegmentsWithUnknownPlannedType = job?.workSegments?.some(
      (segment) =>
        segment.plannedType === InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_UNKNOWN,
    );

    const getIsHoursEditable = () => {
      if (!isOngoingInstallation) {
        return false;
      }
      if (isSegmentsWithUnknownPlannedType) {
        return false;
      }

      if (isIncompleteInstallationPlanned) {
        return false;
      }
      return true;
    };

    const isHoursEditable = getIsHoursEditable();

    const extendedHours = getExtendedHours(extendedSegments || []);
    const allExtendedHours = getHoursForAllResources(project, jobType, extendedHours || 0);

    useEffect(() => {
      setHoursForCompletionInput(allExtendedHours || undefined);
    }, [allExtendedHours]);

    const { mutateAsync: addJobHours, isPending: isAddingJobHours } =
      api.InstallationProject.extendInstallationProjectJob.useMutation({});

    const { mutateAsync: deleteJobHours, isPending: isDeletingJobHours } =
      api.InstallationProject.deleteInstallationProjectJobHours.useMutation({});

    const utils = api.useUtils();

    const addHoursRequest = async (job: InstallationProjectJob, hoursForCompletion: number, previousValue: number) => {
      if (!job.id?.value) {
        toast.error('Job not found');
        return;
      }
      try {
        await addJobHours({
          jobId: job.id.value,
          durationInHours:
            hoursForCompletion /
            getTeamSizeForRole(reverseResourceMapping[job.requiredRole as keyof typeof reverseResourceMapping]),
        });

        toast.success(
          <FormattedMessage
            id="installationPlanning.incompleteHours.success"
            defaultMessage="Hours have been added successfully."
          />,
        );

        setIsSuccess(true);

        utils.InstallationProject.getInstallationProjects.invalidate();
        onSuccess?.();
      } catch {
        toast.error('Failed to add hours');
        // Revert to previous value on error
        setHoursForCompletionInput(previousValue);
        setIsSuccess(false);
      }
    };
    const handleDelete = async ({
      job,
      isDeleteAllHours,
    }: {
      job: InstallationProjectJob;
      isDeleteAllHours: boolean;
    }) => {
      if (!job?.id?.value) {
        toast.error('Job not found');
        return;
      }
      // Calculate number of work segments with EXTENSION planned type
      const extendedWorkSegments =
        job.workSegments?.filter(
          (segment) =>
            segment.plannedType === InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
        ) || [];

      const numberOfWorkSegments = extendedWorkSegments.length;
      if (numberOfWorkSegments === 0) {
        toast.error('No extended work segments to delete');
        return;
      }

      try {
        await deleteJobHours({
          jobId: job.id.value,
          numberOfWorkSegments: numberOfWorkSegments,
        });

        if (isDeleteAllHours) {
          toast.success(
            <FormattedMessage
              id="installationPlanning.incompleteHours.success"
              defaultMessage="Extended hours removed successfully."
            />,
          );
        }

        if (isDeleteAllHours) {
          setHoursForCompletionInput(0);
        }
        if (!isDeleteAllHours) {
          setIsSuccess(false);
        }

        // Invalidate the installation projects cache to refresh the table data
        utils.InstallationProject.getInstallationProjects.invalidate();
        onSuccess?.();
      } catch {
        toast.error('Failed to remove extended hours');
      }
    };

    const handleSubmit = async () => {
      if (hoursForCompletionInput === undefined) {
        toast.error('Please enter hours to add');
        return;
      }

      if (!job?.id?.value) {
        toast.error('Job not found');
        return;
      }

      // Store previous value for rollback on error
      const previousValue = hoursForCompletionInput;
      const hoursToEdit = hoursForCompletionInput - allExtendedHours;
      if (hoursToEdit > 0) {
        addHoursRequest(job, hoursToEdit, previousValue);
      } else if (hoursToEdit < 0) {
        await handleDelete({ job, isDeleteAllHours: false });
        await addHoursRequest(job, hoursForCompletionInput, previousValue);
      }
    };

    return (
      <Box
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
        }}
        onClick={() => {
          onActivate?.();
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: 8, width: '100%', height: '100%' }}>
          {isActive && isHoursEditable && (
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{ width: '100%', height: '100%' }}
            >
              <Stack direction="row" justifyContent="left" alignItems="center" spacing={0.5}>
                <TextField
                  variant="standard"
                  sx={{
                    width: 'auto',
                    maxWidth: '30px',
                    background: 'inherit',
                    border: 0,
                    '& .MuiInput-underline:before': { borderBottom: 'none' },
                    '& .MuiInput-underline:after': { borderBottom: 'none' },
                    '& .MuiInput-underline:hover:not(.Mui-disabled):before': { borderBottom: 'none' },
                    '& input': { padding: 0, textAlign: 'left' },
                  }}
                  value={hoursForCompletionInput || 0}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === '' || /^\d+$/.test(value)) {
                      setHoursForCompletionInput(value === '' ? 0 : Number(value));
                      if (isSuccess) {
                        setIsSuccess(false);
                      }
                    }
                  }}
                />
                <Typography variant="body2" sx={{ marginLeft: '2px' }}>
                  hrs
                </Typography>
              </Stack>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ marginLeft: '8px', padding: 0 }}
              >
                <Button
                  size="small"
                  sx={{
                    paddingX: 1,
                    minWidth: '10px',
                    backgroundColor: 'transparent',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                    '&:disabled': {
                      backgroundColor: 'transparent',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: 'transparent',
                    },
                  }}
                  onClick={handleSubmit}
                  disabled={isAddingJobHours}
                >
                  <CheckCircleFilledIcon width={16} height={16} color={isSuccess ? grey[900] : grey[600]} />
                </Button>

                <Button
                  size="small"
                  sx={{
                    paddingX: 1,
                    minWidth: '10px',
                    backgroundColor: 'transparent',
                    '&:hover': {
                      backgroundColor: 'transparent',
                    },
                    '&:disabled': {
                      backgroundColor: 'transparent',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: 'transparent',
                    },
                  }}
                  onClick={() => handleDelete({ job: job as InstallationProjectJob, isDeleteAllHours: true })}
                  disabled={isDeletingJobHours}
                >
                  <CrossSmallOutlinedIcon width={16} height={16} />
                </Button>
              </Stack>
            </Stack>
          )}
          {(!isActive || !isHoursEditable) && <Typography variant="body2">{allExtendedHours ?? 0} hrs</Typography>}
        </div>
      </Box>
    );
  },
);

HoursForCompletionCell.displayName = 'HoursForCompletionCell';
export default HoursForCompletionCell;
