import {
  InstallationProjectJob_JobType,
  InstallationProjectResourceType,
  InstallationProjectJob_WorkSegmentPlannedType,
  InstallationProjectJob_WorkSegment,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProjectStage } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { compareTimestamps } from 'components/bill-of-materials/utils';
import { InstallationProjectJob_JobStatus } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

export const getTeamLeadName = (project: FullInstallationProjectEntity, teamLeadMap: Map<string, string>) => {
  const teamLeadResource = project.installationProject?.assignedResources?.find(
    (res) => res.type === InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_TEAM_LEAD,
  );
  const teamLeadUserId = teamLeadResource?.userId?.value;
  return teamLeadUserId ? teamLeadMap.get(teamLeadUserId) : '';
};

export const getCommitionedDate = (project: FullInstallationProjectEntity) => {
  const indicator = project.installationProject?.progressOverview?.progressMadeIndicators.find(
    (indicator) => indicator.indicator?.$case === 'heatPumpCommissioned',
  );
  if (indicator?.indicator?.$case === 'heatPumpCommissioned') {
    return indicator.indicator.heatPumpCommissioned?.commissionedAt?.toLocaleDateString(undefined, {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
    });
  }
  return undefined;
};

export const getInstallationComplete = (project: FullInstallationProjectEntity) => {
  const acceptanceUpdateIndicators = project.installationProject?.progressOverview?.progressMadeIndicators.filter(
    (indicator) => indicator.indicator?.$case === 'acceptanceUpdate',
  );

  const completedIndicator = acceptanceUpdateIndicators?.find((indicator) => {
    if (indicator.indicator?.$case === 'acceptanceUpdate') {
      return indicator.indicator.acceptanceUpdate?.outcome?.$case === 'completed';
    }
    return false;
  });

  if (completedIndicator?.indicator?.$case === 'acceptanceUpdate') {
    const completedDate = completedIndicator.indicator.acceptanceUpdate.submittedAt;
    return completedDate?.toLocaleDateString(undefined, {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
    });
  }
  return undefined;
};

const getInstallationIncompleteDate = (project: FullInstallationProjectEntity) => {
  const acceptanceUpdateIndicators = project.installationProject?.progressOverview?.progressMadeIndicators.filter(
    (indicator) => indicator.indicator?.$case === 'acceptanceUpdate',
  );

  // Filter for incomplete indicators and sort by submittedAt to get the most recent one
  const incompleteIndicators = acceptanceUpdateIndicators?.filter((indicator) => {
    if (indicator.indicator?.$case === 'acceptanceUpdate') {
      return indicator.indicator.acceptanceUpdate?.outcome?.$case === 'remainingWorkRequired';
    }
    return false;
  });

  // Sort by submittedAt descending and get the first (most recent) one
  const lastIncompleteIndicator = incompleteIndicators?.sort((a, b) => {
    const aDate = a.indicator?.$case === 'acceptanceUpdate' ? a.indicator.acceptanceUpdate?.submittedAt : undefined;
    const bDate = b.indicator?.$case === 'acceptanceUpdate' ? b.indicator.acceptanceUpdate?.submittedAt : undefined;
    return compareTimestamps(bDate, aDate);
  })?.[0];

  if (lastIncompleteIndicator?.indicator?.$case === 'acceptanceUpdate') {
    return lastIncompleteIndicator.indicator.acceptanceUpdate.submittedAt;
  }
  return undefined;
};

export const getInstallationIncomplete = (project: FullInstallationProjectEntity) => {
  const lastInstallation = getInstallationIncompleteDate(project);
  return lastInstallation?.toLocaleDateString(undefined, {
    month: '2-digit',
    day: '2-digit',
    year: '2-digit',
  });
};

export const getStageText = (stage: InstallationProjectStage | null) => {
  if (!stage) {
    return 'ongoingInstallations.status.unknown';
  }
  switch (stage) {
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION:
      return 'ongoingInstallations.status.preInstallation';
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION:
      return 'ongoingInstallations.status.installation';
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION:
      return 'ongoingInstallations.status.postInstallation';
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE:
      return 'ongoingInstallations.status.invoice';
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED:
      return 'ongoingInstallations.status.completed';
    case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW:
      return 'ongoingInstallations.status.new';
    default:
      return 'ongoingInstallations.status.unknown';
  }
};

export const getPlannedStartDate = (project: FullInstallationProjectEntity): string => {
  const plumberJob = project.installationProject?.jobs.find(
    (job) => job.type === InstallationProjectJob_JobType.JOB_TYPE_PLUMBING,
  );
  if (!plumberJob) {
    return '';
  }

  const firstDaySegment = plumberJob.workSegments.find((segment) => segment.sequenceNumber === 1);

  if (!firstDaySegment?.startTime) {
    return '';
  }

  return new Date(firstDaySegment.startTime).toLocaleDateString(undefined, {
    month: '2-digit',
    day: '2-digit',
    year: '2-digit',
  });
};

export const getIsActualStartDatePerJob = (
  project: FullInstallationProjectEntity,
  jobType: InstallationProjectJob_JobType,
) => {
  const job = project.installationProject?.jobs.find((job) => job.type === jobType);
  if (!job) {
    return false;
  }
  return job.workSegments.some((segment) => segment.timeCards.some((card) => card.startedAt));
};

export const getActualStartDate = (project: FullInstallationProjectEntity) => {
  const plumberJob = project.installationProject?.jobs.find(
    (job) => job.type === InstallationProjectJob_JobType.JOB_TYPE_PLUMBING,
  );

  if (!plumberJob) {
    return '';
  }

  const firstDaySegment = plumberJob.workSegments.find((segment) => segment.sequenceNumber === 1);

  if (!firstDaySegment?.timeCards || firstDaySegment.timeCards.length === 0) {
    return '';
  }

  const earliestStartTime = firstDaySegment.timeCards.reduce(
    (earliest: string | null, timeCard): string | null => {
      if (!timeCard.startedAt) return earliest;
      if (!earliest) return timeCard.startedAt.toISOString();
      return timeCard.startedAt.toISOString() < earliest ? timeCard.startedAt.toISOString() : earliest;
    },
    null as string | null,
  );

  if (earliestStartTime) {
    return new Date(earliestStartTime).toLocaleDateString(undefined, {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
    });
  }

  return '';
};

export const getExtendedSegments = (
  project: FullInstallationProjectEntity,
  jobType: InstallationProjectJob_JobType,
) => {
  const job = project.installationProject?.jobs.find((job) => job.type === jobType);

  const extendedSegments = job?.workSegments?.filter(
    (segment) =>
      segment.plannedType === InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
  );
  return extendedSegments;
};

export const getIsJobInProgress = (project: FullInstallationProjectEntity, jobType: InstallationProjectJob_JobType) => {
  const isJobStatusInProgress =
    project.installationProject?.jobs.find((job) => job.type === jobType)?.status ===
    InstallationProjectJob_JobStatus.JOB_STATUS_IN_PROGRESS;
  return isJobStatusInProgress;
};

export const getIsIncompleteInstallationPlanned = (
  project: FullInstallationProjectEntity,
  jobType: InstallationProjectJob_JobType,
) => {
  const job = project.installationProject?.jobs.find((job) => job.type === jobType);
  if (!job) {
    return false;
  }

  const isJobPlanned = getIsActualStartDatePerJob(project, jobType);
  if (!isJobPlanned) {
    return false;
  }

  const incompleteDate = getInstallationIncompleteDate(project);
  if (!incompleteDate) {
    return false;
  }

  const completeDate = getInstallationComplete(project);
  if (completeDate) {
    return false;
  }

  const extendedSegments = job?.workSegments?.filter(
    (segment) =>
      segment.plannedType === InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
  );

  const segmentsAfterLastIncomplete = extendedSegments?.filter((segment) => {
    if (segment.startTime && incompleteDate && segment.startTime < incompleteDate) {
      return false;
    }
    return true;
  });

  const hasPlannedSegment = segmentsAfterLastIncomplete?.some((segment) => segment.startTime);
  return hasPlannedSegment;
};

export const getExtendedHours = (extendedSegments: InstallationProjectJob_WorkSegment[]) => {
  if (extendedSegments && extendedSegments.length > 0) {
    const hours = extendedSegments.reduce((acc, segment) => {
      return acc + (segment.duration?.seconds || 0) / 3600;
    }, 0);
    return hours;
  }
  return null;
};

export const getHoursForAllResources = (
  project: FullInstallationProjectEntity,
  jobType: InstallationProjectJob_JobType,
  extendedHours: number,
) => {
  const requiredResourcesCount =
    project.installationProject?.jobs.find((job) => job.type === jobType)?.requiredResourcesCount || 0;
  const hours = extendedHours || 0;
  return hours * requiredResourcesCount;
};
