import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { grey } from '@ui/theme/colors';
import { marketConfiguration } from 'utils/marketConfigurations';
import { getDateFnsLocale } from 'utils/dates/localeMapping';
import { useCountryCodeContext } from 'context/CountryCodeContext';

type DateRange = {
  startDate: Date | undefined;
  endDate: Date | undefined;
};

const DatePickers = ({
  dateRange,
  setDateRange,
}: {
  dateRange: DateRange;
  setDateRange: (dateRange: DateRange) => void;
}) => {
  const countryCode = useCountryCodeContext();
  const localeKey = marketConfiguration[countryCode as keyof typeof marketConfiguration].locale;
  const localeForDatePicker = getDateFnsLocale(localeKey);
  return (
    <Stack direction="row" spacing={2} sx={{ py: 4 }}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={localeForDatePicker}>
        <Stack direction="row" spacing={2}>
          <Stack direction="column" alignItems="flex-start">
            <Typography sx={{ color: grey[700], fontWeight: 500, mb: 1 }} variant="body2">
              <FormattedMessage id="ongoingInstallations.filters.from" defaultMessage="From" />
            </Typography>
            <DatePicker
              defaultValue={dateRange.startDate}
              sx={{ width: '200px' }}
              onAccept={(newStartDate) => setDateRange({ ...dateRange, startDate: newStartDate ?? undefined })}
              slots={{ openPickerIcon: CalendarOutlinedIcon }}
              format="MM/dd/yyyy"
              maxDate={dateRange.endDate}
              views={['month', 'day']}
            />
          </Stack>

          <Stack direction="column" alignItems="flex-start" justifyContent="space-between">
            <Typography sx={{ color: grey[700], fontWeight: 500, mb: 1 }} variant="body2">
              <FormattedMessage id="ongoingInstallations.filters.to" defaultMessage="To" />
            </Typography>
            <DatePicker
              views={['month', 'day']}
              defaultValue={dateRange.endDate}
              sx={{ width: '200px' }}
              onAccept={(newEndDate) => setDateRange({ ...dateRange, endDate: newEndDate ?? undefined })}
              slots={{ openPickerIcon: CalendarOutlinedIcon }}
              format="MM/dd/yyyy"
              minDate={dateRange.startDate}
            />
          </Stack>
        </Stack>
      </LocalizationProvider>
    </Stack>
  );
};

export default DatePickers;
