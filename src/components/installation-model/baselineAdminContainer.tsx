import { Box, Stack, Typography } from '@mui/material';
import { api } from 'utils/api';
import { groupBy } from 'utils/groupBy';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { useState } from 'react';
import ModelSelector from './modelSelector';
import ModelModal from './modals/modelModal';
import { ModelInList } from './types/types';

export default function BaselineAdminContainer() {
  const [modelModalOpen, setModelModalOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<ModelInList | null>(null);
  const {
    data: installationModels,
    refetch: refetchAllInstallationModel,
    isLoading: isLoadingInstallationModels,
  } = api.ManHours.getAllInstallationModels.useQuery({});

  if (isLoadingInstallationModels) {
    return (
      <Stack>
        <Typography variant="headline2">Loading...</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (!installationModels) {
    return (
      <Stack>
        <Typography variant="headline2">No installation models found</Typography>
      </Stack>
    );
  }

  const modelsByCountry: Map<string, ModelInList[]> = groupBy(installationModels, (model) => model.countryCode);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        height: '100%',
        padding: '40px',
      }}
    >
      <ModelSelector
        refetchAllInstallationModels={refetchAllInstallationModel}
        modelsByCountry={modelsByCountry}
        setModelModalOpen={setModelModalOpen}
        selectedCountry={selectedCountry}
        setSelectedCountry={setSelectedCountry}
        selectedModel={selectedModel}
        setSelectedModel={setSelectedModel}
      />
      <ModelModal
        modelsByCountry={modelsByCountry}
        modelModalOpen={modelModalOpen}
        setModelModalOpen={setModelModalOpen}
        selectedModel={selectedModel}
      />
    </Box>
  );
}
