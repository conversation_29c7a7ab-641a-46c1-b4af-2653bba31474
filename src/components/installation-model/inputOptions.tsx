import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import GridAddIcon from '@ui/components/Icons/material/Add';
import { Button } from '@ui/components/Button/Button';
import { TextField } from '@ui/components/TextField/TextField';
import DeleteIcon from '@ui/components/Icons/material/Delete';
import { useEffect, useState } from 'react';
import { Option } from './types/types';
import { useModelStore } from './stores/modelStore';

type InputOptionsProps = {
  selectedInput: any;
  setSelectedInput: any;
  multipliersUsingSelectedInput: any;
  isPublished: boolean;
};

export default function InputOptions({
  selectedInput,
  setSelectedInput,
  multipliersUsingSelectedInput,
  isPublished,
}: InputOptionsProps) {
  const [selectedOption, setSelectedOption] = useState<Option>();
  const [selectedOptionName, setSelectedOptionName] = useState('');
  const { model, updateParameter: updateInput } = useModelStore();

  useEffect(() => {
    setSelectedOption(undefined);
  }, [selectedInput, setSelectedOption]);

  const handleSetSelectedOptionName = (event: any) => {
    setSelectedOptionName(event.target.value);
  };

  const handleSaveSelectedOptionName = () => {
    if (!model || !selectedInput || !selectedOption) return;
    const updatedOption = {
      ...selectedOption,
      name: selectedOptionName,
    };
    const updatedInput = {
      ...selectedInput,
      options: selectedInput.options?.map((option: Option) => {
        if (option.name === selectedOption.name) return updatedOption;
        return option;
      }),
    };
    setSelectedInput(updatedInput);
    updateInput(updatedInput);
    setSelectedOption(undefined);
  };

  const handleClickedAddNewOption = () => {
    if (!model || !selectedInput) return;
    const newOption = {
      name: 'placeholder name',
      value: 'placeholder value',
    };
    const updatedInput = {
      ...selectedInput,
      options: [...(selectedInput.options || []), newOption],
    };
    updateInput(updatedInput);
    setSelectedInput(updatedInput);
    setSelectedOption(newOption);
  };

  const handleDeleteOption = (option: Option) => {
    if (!model || !selectedInput) return;
    const updatedInput = {
      ...selectedInput,
      options: (selectedInput.options || []).filter((o: Option) => o.name !== option.name),
    };
    updateInput(updatedInput);
    setSelectedInput(updatedInput);
    setSelectedOption(undefined);
  };

  return (
    <>
      {selectedInput?.type === 'select' && (
        <Box
          sx={{
            maxWidth: 460,
            background: 'white',
            padding: '10px',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Typography variant="body1Emphasis">Options</Typography>
          <Box>
            <List dense>
              {(selectedInput.options || []).map((option: Option) => (
                <ListItem
                  key={option.name}
                  secondaryAction={
                    isPublished ? null : (
                      <IconButton edge="end" onClick={() => handleDeleteOption(option)}>
                        <DeleteIcon />
                      </IconButton>
                    )
                  }
                >
                  {isPublished ? (
                    <ListItemText primary={option.name} />
                  ) : (
                    <ListItemButton
                      selected={selectedOption?.name === option.name}
                      onClick={() => {
                        setSelectedOption(option);
                        setSelectedOptionName(option.name);
                      }}
                    >
                      <ListItemText primary={option.name} />
                    </ListItemButton>
                  )}
                </ListItem>
              ))}
              {!isPublished && (
                <>
                  <Divider />
                  <ListItemButton key="newOption" onClick={handleClickedAddNewOption}>
                    <ListItemIcon>
                      <GridAddIcon />
                    </ListItemIcon>
                    <ListItemText primary="Add new option" />
                  </ListItemButton>
                </>
              )}
            </List>
          </Box>
        </Box>
      )}
      {selectedOption && !isPublished && (
        <Box>
          {multipliersUsingSelectedInput.length > 0 && (
            <Alert severity="info" sx={{ marginBottom: '10px' }}>
              <Typography variant="body1">
                Be careful changing this as it may affect the multiplier in the microactivities list above
              </Typography>
            </Alert>
          )}
          <Box>
            <TextField
              label="Name"
              name="name"
              value={selectedOptionName}
              onChange={handleSetSelectedOptionName}
              size="small"
            />
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveSelectedOptionName}
              sx={{ marginLeft: '10px', marginBottom: '2px' }}
              size="small"
            >
              Save
            </Button>
          </Box>
        </Box>
      )}
    </>
  );
}
