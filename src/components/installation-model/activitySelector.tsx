import { Box, ListItemButton, ListItemText, Typography, Divider, ListItemIcon } from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import GridAddIcon from '@ui/components/Icons/material/Add';
import { useModelStore } from './stores/modelStore';
import { MacroActivity, MicroActivity } from './types/types';
import { useModalsStore } from './stores/modalsStore';
import DraggableActivitiesList from './draggableAcitivitiesList/draggableActivitiesList';

export default function ActivitySelector() {
  const {
    model,
    setModel,
    setSelectedMacroActivity,
    setSelectedMicroActivity,
    addMicroActivity,
    selectedMicroActivity,
    selectedMacroActivity,
    setCurrentMultiplier,
    updateMacroActivity,
  } = useModelStore();
  const { setMacroActivityModalOpen, setMicroActivityModalOpen } = useModalsStore();

  const handleMacroActivityItemClick = (clickedMacroActivityId: MacroActivity['id']) => {
    setSelectedMacroActivity(clickedMacroActivityId);
  };

  const handleMacroActivityEditClick = (clickedMacroActivityId: MacroActivity['id']) => {
    const clickedMacroActivity = model?.macroActivities.find(
      (macroActivity) => macroActivity.id === clickedMacroActivityId,
    );
    setMacroActivityModalOpen(true, clickedMacroActivity);
  };

  const handleMicroActivityEditClick = (clickedMicroActivityId: MicroActivity['id']) => {
    setSelectedMicroActivity(clickedMicroActivityId);
    const clickedMicroActivity = selectedMacroActivity?.microActivities.find(
      (microActivity) => microActivity.id === clickedMicroActivityId,
    );
    if (!clickedMicroActivity) return;
    setCurrentMultiplier(clickedMicroActivity.multiplier);
    setMicroActivityModalOpen(true, clickedMicroActivity);
  };

  const handleClickedNewMacroActivity = () => {
    setSelectedMacroActivity(null);
    setMacroActivityModalOpen(true);
  };

  const handleClickedNewMicroActivity = () => {
    addMicroActivity({
      id: uuidv4(),
      name: 'placeholder',
      resourcesRequired: [],
      manHoursInMinutes: 0,
      multiplier: { '*': [1] },
    });
    setMicroActivityModalOpen(true);
  };

  const handleReorderMacroActivity = ({
    destination,
    source,
  }: {
    destination: { index: number; id: string };
    source: { index: number; id: string };
  }) => {
    if (!destination || !model || model.macroActivities.length < source.index) return;
    const newMacroActivities: MacroActivity[] = [...model.macroActivities];
    const [removed] = newMacroActivities.splice(source.index, 1)!;
    newMacroActivities.splice(destination.index, 0, removed!);
    setModel({ ...model, macroActivities: newMacroActivities });
  };

  const handleReorderMicroActivity = ({
    destination,
    source,
  }: {
    destination: { index: number; id: string };
    source: { index: number; id: string };
  }) => {
    if (!destination || !selectedMacroActivity || !model?.macroActivities) return;
    const newMicroActivities: MicroActivity[] = [...selectedMacroActivity.microActivities];
    const [removed] = newMicroActivities.splice(source.index, 1);
    newMicroActivities.splice(destination.index, 0, removed!);
    setModel({
      ...model,
      macroActivities: model.macroActivities.map((macroActivity: MacroActivity) => {
        if (macroActivity.id === selectedMacroActivity.id) {
          return { ...macroActivity, microActivities: newMicroActivities };
        }
        return macroActivity;
      }),
    });
    updateMacroActivity({
      ...selectedMacroActivity,
      microActivities: newMicroActivities,
    });
  };

  const isPublished = !!(model?.publishedAt ?? false);

  return (
    <Box
      sx={{
        width: '100%',
        padding: '10px',
        marginTop: '40px',
      }}
    >
      <Typography variant="headline1">Select MicroActivity to update</Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          width: '100%',
          height: '100%',
          marginTop: '20px',
        }}
      >
        <Box
          sx={{
            minWidth: 360,
            marginTop: '10px',
            marginRight: '20px',
          }}
        >
          <Typography variant="headline2">Macro-activity</Typography>
          {model?.macroActivities && (
            <Box
              sx={{
                maxWidth: 360,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
                marginBottom: '10px',
              }}
            >
              <DraggableActivitiesList
                onDragEnd={handleReorderMacroActivity}
                activities={model?.macroActivities}
                selectedId={selectedMacroActivity?.id ?? null}
                handleClick={handleMacroActivityItemClick}
                handleEditClick={handleMacroActivityEditClick}
                isPublished={isPublished}
                type="macro"
              />
              {!isPublished && (
                <>
                  <Divider />
                  <ListItemButton key="newMacroActivity" onClick={() => handleClickedNewMacroActivity()}>
                    <ListItemIcon>
                      <GridAddIcon />
                    </ListItemIcon>
                    <ListItemText primary="Add new macro-activity" />
                  </ListItemButton>
                </>
              )}
            </Box>
          )}
        </Box>
        {selectedMacroActivity && (
          <Box
            sx={{
              minWidth: 360,
              marginTop: '10px',
              marginRight: '20px',
            }}
          >
            <Typography variant="headline2">Micro-activity</Typography>

            <Box
              sx={{
                maxWidth: 360,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
              }}
            >
              {selectedMacroActivity?.microActivities && (
                <>
                  <DraggableActivitiesList
                    onDragEnd={handleReorderMicroActivity}
                    activities={selectedMacroActivity?.microActivities}
                    selectedId={selectedMicroActivity?.id ?? null}
                    handleClick={isPublished ? handleMicroActivityEditClick : () => {}}
                    handleEditClick={handleMicroActivityEditClick}
                    isPublished={isPublished}
                    type="micro"
                  />

                  {!isPublished && (
                    <>
                      <Divider />
                      <ListItemButton key="newMicroActivity" onClick={() => handleClickedNewMicroActivity()}>
                        <ListItemIcon>
                          <GridAddIcon />
                        </ListItemIcon>
                        <ListItemText primary="Add new micro-activity" />
                      </ListItemButton>
                    </>
                  )}
                </>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
}
