import { useMemo } from 'react';
import type { Field, RuleType, Option as Opt } from 'react-querybuilder';
import { defaultOperators } from 'react-querybuilder';
import { Option } from '../types/types';
import { useModelStore } from '../stores/modelStore';

const useGetFields = () => {
  const { model } = useModelStore();
  return useMemo<Field[]>(() => {
    if (model?.parameters) {
      return model.parameters.map((parameter) => ({
        name: parameter.id,
        label: parameter.name,
        inputType: parameter.type === 'number' ? 'number' : undefined,
        validator: parameter.type === 'number' ? (r: RuleType) => !!r.value : undefined,
        valueEditorType: (() => {
          switch (parameter.type) {
            case 'boolean':
              return 'checkbox';
            case 'select':
              return 'select';
            case 'number':
              return 'text';
            default:
              return undefined;
          }
        })(),
        values:
          parameter.type === 'select' && parameter.options
            ? parameter.options.map(
                (option: Option) =>
                  ({
                    name: option.name,
                    label: option.name,
                  }) as Opt,
              )
            : undefined,
        operators:
          parameter.type === 'select' || parameter.type === 'boolean'
            ? defaultOperators.filter((op) => op.name === '=')
            : defaultOperators,
        defaultValue: (() => {
          if (parameter.type === 'boolean') {
            return false;
          }
          if (parameter.type === 'select' && parameter.options?.[0]?.name) {
            return parameter.options[0].name;
          }
          return undefined;
        })(),
      }));
    }
    return [];
  }, [model?.parameters]);
};

export default useGetFields;
