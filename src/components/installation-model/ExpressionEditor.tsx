import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { QueryBuilder, add } from 'react-querybuilder';
import { v4 as uuidv4 } from 'uuid';
import type { Field, Option, RuleGroupType, RuleType } from 'react-querybuilder';
import 'react-querybuilder/dist/query-builder.css';
import { Button } from '@ui/components/Button/Button';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { IconButton } from '@mui/material';
import { useState } from 'react';
import { useModelStore } from './stores/modelStore';
import { JsonLogicArithmetic, ParameterSpec } from './types/types';
import CustomFieldSelector from './customFieldSelector';
import CustomValueEditor from './customExpressionValueEditor';

export interface ExpressionEditorProps {
  expressionLogic: JsonLogicArithmetic;
  onUpdate: (q: JsonLogicArithmetic) => void;
}

export const combinators: Option[] = [
  { name: '+', label: '+' },
  { name: '-', label: '-' },
  { name: '*', label: '*' },
  { name: '/', label: '/' },
];

export const convertQueryToJsonLogic = (q: RuleGroupType): JsonLogicArithmetic => {
  const { rules } = q;
  const jsonLogic: JsonLogicArithmetic = {
    [q.combinator]: rules.map((rule: RuleType | RuleGroupType) => {
      if ('rules' in rule) {
        return convertQueryToJsonLogic(rule);
      }
      if (rule.field === 'value') {
        return rule.value;
      }
      return {
        var: rule.field,
      };
    }),
  } as JsonLogicArithmetic;
  return jsonLogic;
};

const convertJsonLogicToQuery = (expr: JsonLogicArithmetic): RuleGroupType => {
  if (typeof expr === 'number') {
    return {
      combinator: '*',
      rules: [
        {
          field: 'value',
          operator: '=',
          value: expr,
        },
      ],
    };
  }
  const combinator = (Object.keys(expr)[0] || '*') as keyof JsonLogicArithmetic;
  const rules = expr[combinator] as JsonLogicArithmetic[];
  const rqbQuery: RuleGroupType = {
    combinator,
    id: uuidv4(),
    rules: rules.map((rule: JsonLogicArithmetic | number | { var: string }) => {
      if (typeof rule === 'number' || (typeof rule === 'string' && !Number.isNaN(rule))) {
        return {
          id: uuidv4(),
          field: 'value',
          operator: '=',
          value: rule,
        };
      }
      if ('var' in rule) {
        return {
          id: uuidv4(),
          field: rule.var,
          operator: '',
          value: 1,
        };
      }
      if (typeof rule !== 'object' && typeof rule !== 'number') {
        throw new Error(`Unhandled multiplier type: ${typeof expr}`);
      }
      return convertJsonLogicToQuery(rule);
    }),
  };
  return rqbQuery;
};

export function ExpressionEditor({ expressionLogic, onUpdate }: ExpressionEditorProps): React.JSX.Element {
  const { model } = useModelStore();
  const [query, setQuery] = useState<RuleGroupType>(convertJsonLogicToQuery(expressionLogic));
  const numberParameters = model?.parameters.filter((parameter) => parameter.type === 'number') ?? [];
  const fields: Field[] =
    numberParameters.map((parameter: ParameterSpec) => ({
      id: parameter.id,
      name: parameter.id,
      label: parameter.name,
      inputType: parameter.type,
      operators: combinators,
      validator: (r: RuleType) => !!r.value,
    })) ?? [];
  const handleUpdate = (q: RuleGroupType) => {
    setQuery(q);
    onUpdate(convertQueryToJsonLogic(q));
  };

  const addRuleAction = (props: any) => {
    const handleAddConstantClick = () => {
      const newRule = {
        field: 'value',
        operator: '=',
        value: 1,
      };
      const newQuery = add(query, newRule, props.path);
      setQuery(newQuery);
    };
    return (
      <>
        <Button
          type="button"
          onClick={(e) => props.handleOnClick(e, 'value')}
          size="small"
          sx={{
            minWidth: '120px',
          }}
        >
          ADD INPUT
        </Button>
        <Button type="button" color="white" onClick={handleAddConstantClick} size="small">
          ADD CONSTANT
        </Button>
      </>
    );
  };

  const addGroupAction = (props: any) => (
    <Button
      type="button"
      color="secondary"
      onClick={(e) => props.handleOnClick(e, 'group')}
      size="small"
      sx={{
        minWidth: '120px',
      }}
    >
      ADD GROUP
    </Button>
  );

  const removeRuleAction = (props: any) => (
    <IconButton
      sx={{
        height: '30px',
        width: '30px',
      }}
      className={props.className}
      onClick={(e) => props.handleOnClick(e, 'group')}
    >
      <CloseIcon />
    </IconButton>
  );

  if (model?.publishedAt) {
    return (
      <div className="query-builder-then query-builder-disabled">
        <QueryBuilder fields={fields} query={query} combinators={combinators} showCombinatorsBetweenRules />
      </div>
    );
  }

  const removeGroupAction = (props: any) => (
    <IconButton
      sx={{
        height: '30px',
        width: '30px',
      }}
      className={props.className}
      onClick={(e) => props.handleOnClick(e, 'group')}
    >
      <CloseIcon />
    </IconButton>
  );

  return (
    <div className="query-builder-then">
      <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
        <QueryBuilder
          controlElements={{
            addRuleAction,
            addGroupAction,
            removeRuleAction,
            removeGroupAction,
            fieldSelector: CustomFieldSelector,
            valueEditor: CustomValueEditor,
          }}
          fields={fields}
          query={query}
          onQueryChange={handleUpdate}
          combinators={combinators}
          showCombinatorsBetweenRules
        />
      </QueryBuilderDnD>
    </div>
  );
}
