import { useEffect, useState } from 'react';
import { Box, Checkbox, InputLabel, MenuItem, Typography } from '@mui/material';
import jsonLogic from 'json-logic-js';
import { Card } from '@ui/components/Card/Card';
import { NumericFormField } from '@ui/components/NumericFormField/NumericFormField';
import { Select } from '@ui/components/Select/Select';
import { getUpdatedJSONLogic } from './utils/multiplierUtils';
import { doesJsonLogicValueContainInputId } from './utils/inputUtils';
import { useModelStore } from './stores/modelStore';
import { InstallationModel } from './types/types';

export default function MicroActivityMultiplierTest() {
  const { model, currentMultiplier, selectedMicroActivity } = useModelStore();

  const [inputsUsed, setInputsUsed] = useState([] as InstallationModel['parameters']);
  const [inputValues, setInputValues] = useState({} as Record<string, any>);

  const getAllInputsUsedInMultiplier = () => {
    const inputs = model?.parameters;
    if (!inputs) return inputs;
    if (!currentMultiplier) return [];
    const multiplierInJsonLogic = getUpdatedJSONLogic(currentMultiplier);
    const inputsUsedInMultiplier = [] as InstallationModel['parameters'];
    inputs.forEach((input) => {
      if (doesJsonLogicValueContainInputId(multiplierInJsonLogic, input.id)) inputsUsedInMultiplier.push(input);
    });
    return inputsUsedInMultiplier;
  };

  const evaluateMultiplier = () => {
    if (!currentMultiplier) return null;
    if (!getUpdatedJSONLogic(currentMultiplier)) return null;
    for (const key in inputValues) {
      if (inputValues[key] === null) return null;
    }
    try {
      const multiplierOutput = jsonLogic.apply(getUpdatedJSONLogic(currentMultiplier), inputValues);
      if (multiplierOutput === null) return null;
      if (typeof multiplierOutput === 'number')
        return multiplierOutput * (selectedMicroActivity?.manHoursInMinutes ?? 0);
      return null;
    } catch (_error) {
      return null;
    }
  };

  useEffect(() => {
    const inputs = model?.parameters;
    if (!inputs) return;
    const usedInputs = getAllInputsUsedInMultiplier();
    setInputsUsed(usedInputs ?? []);
    const newInputValues = {} as Record<string, any>;
    usedInputs?.forEach((input) => {
      switch (true) {
        case input.id in inputValues:
          newInputValues[input.id] = inputValues[input.id];
          break;
        case input.type === 'number':
          newInputValues[input.id] = 0;
          break;
        case input.type === 'boolean':
          newInputValues[input.id] = false;
          break;
        case input.type === 'select':
          newInputValues[input.id] = input.options?.[0]?.name;
          break;
        default:
          break;
      }
    });
    setInputValues(newInputValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [model, currentMultiplier]);

  if (inputsUsed.length > 0 && Object.keys(inputValues).length !== inputsUsed.length) return null;
  return (
    <Card
      sx={{
        minWidth: '300px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          flexDirection: 'column',
          gap: '20px',
        }}
      >
        <Typography variant="headline2">Test multiplier</Typography>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
            flexWrap: 'wrap',
            gap: '10px',
          }}
        >
          <Typography variant="body1Emphasis">Total minutes:</Typography>
          <Typography variant="body1">{evaluateMultiplier()}</Typography>
        </Box>

        <Typography variant="headline3">Inputs used:</Typography>
        {inputsUsed.map((input) => (
          <Box
            key={input.id}
            sx={{
              display: 'flex',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
              flexDirection: 'column',
              width: '100%',
              maxWidth: '260px',
            }}
          >
            {input.type === 'number' && (
              <NumericFormField
                key={input.id}
                name={input.name}
                label={input.name}
                value={inputValues[input.id]}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    [input.id]: value,
                  });
                }}
                sx={{ width: '100%' }}
              />
            )}
            {input.type === 'boolean' && (
              <>
                <InputLabel htmlFor={input.name}>
                  <Typography variant="inputLabel">{input.name}</Typography>
                </InputLabel>
                <Checkbox
                  checked={inputValues[input.id] ?? false}
                  onChange={(e) => {
                    setInputValues({
                      ...inputValues,
                      [input.id]: e.target.checked,
                    });
                  }}
                  name={input.name}
                />
              </>
            )}
            {input.type === 'select' && (
              <Select
                label={input.name}
                name={input.name}
                onChange={(e) => {
                  setInputValues({
                    ...inputValues,
                    [input.id]: e.target.value as string,
                  });
                }}
                value={inputValues[input.id]}
                size="small"
              >
                {input.options?.map((option) => (
                  <MenuItem key={option.name} value={option.name}>
                    {option.name}
                  </MenuItem>
                ))}
              </Select>
            )}
          </Box>
        ))}
      </Box>
    </Card>
  );
}
