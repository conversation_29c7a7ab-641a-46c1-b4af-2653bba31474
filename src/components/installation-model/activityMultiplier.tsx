import { Box, IconButton, Typography } from '@mui/material';
import type { RQBJsonLogic } from 'react-querybuilder';
import { Button } from '@ui/components/Button/Button';
import { Card } from '@ui/components/Card/Card';
import DeleteIcon from '@ui/components/Icons/material/Delete';
import { useModelStore } from './stores/modelStore';
import { MultiplierEditor } from './MultiplierEditor';
import { ConditionEditor } from './ConditionEditor';
import { IfElseExpression, JsonLogicArithmetic } from './types/types';
import {
  addAnotherCase,
  getUpdatedJSONLogic,
  removeCase,
  updateExpressionLogic,
  updateSpecificQuery,
} from './utils/multiplierUtils';

export default function ActivityMultiplier() {
  const {
    model,
    updateMicroActivity,
    copiedMultiplier,
    setCopiedMultiplier,
    selectedMicroActivity,
    currentMultiplier,
    updateCurrentMultiplier,
  } = useModelStore();

  const updateMicroActivityMultiplier = (updatedConditionals: IfElseExpression<JsonLogicArithmetic>) => {
    if (!selectedMicroActivity) return;
    const newMultiplier = getUpdatedJSONLogic(updatedConditionals);
    if (JSON.stringify(selectedMicroActivity.multiplier) === JSON.stringify(newMultiplier)) return;
    updateMicroActivity({
      ...selectedMicroActivity,
      multiplier: newMultiplier,
    });
  };
  const handleAddAnotherCase = () => {
    if (!currentMultiplier) return;
    const multiplier = { ...currentMultiplier };
    const updatedConditionals = addAnotherCase({ conditionals: multiplier });
    updateCurrentMultiplier(updatedConditionals);
    updateMicroActivityMultiplier(updatedConditionals);
  };

  const handleRemoveCase = (idx: number) => {
    if (!currentMultiplier) return;
    const multiplier = { ...currentMultiplier };
    const updatedConditionals = removeCase({ idx, conditionals: multiplier });
    updateCurrentMultiplier(updatedConditionals);
    updateMicroActivityMultiplier(updatedConditionals);
  };

  const handleUpdateQuery = (q: RQBJsonLogic, idx: number) => {
    if (!currentMultiplier) return;
    const multiplier = { ...currentMultiplier };
    const updatedConditionals = updateSpecificQuery({ conditionals: multiplier, condition: q, idx });
    updateCurrentMultiplier(updatedConditionals);
    updateMicroActivityMultiplier(updatedConditionals);
  };

  const handleUpdateExpressionLogic = (q: JsonLogicArithmetic, idx?: number) => {
    if (!currentMultiplier) return;
    const multiplier = { ...currentMultiplier };
    const updatedConditionals = updateExpressionLogic({ conditionals: multiplier, expr: q, idx });
    updateCurrentMultiplier(updatedConditionals);
    updateMicroActivityMultiplier(updatedConditionals);
  };

  const handleCopyMultiplierLogic = () => {
    if (!selectedMicroActivity) return;
    setCopiedMultiplier(selectedMicroActivity.multiplier);
  };

  const handlePasteMultiplierLogic = () => {
    if (!selectedMicroActivity || !copiedMultiplier) return;
    updateMicroActivity({
      ...selectedMicroActivity,
      multiplier: copiedMultiplier,
    });
  };

  if (!model || !selectedMicroActivity || !currentMultiplier) return null;

  const isPublished = !!(model?.publishedAt ?? false);

  return (
    <>
      <Card
        sx={{
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '20px',
          }}
        >
          <Typography variant="headline2">Multiplier</Typography>
          {!isPublished && (
            <>
              <Button variant="contained" color="primary" onClick={handleCopyMultiplierLogic}>
                Copy
              </Button>
              {!!copiedMultiplier && (
                <Button variant="contained" color="primary" onClick={handlePasteMultiplierLogic}>
                  Paste copied
                </Button>
              )}
            </>
          )}
        </Box>
        <Box
          sx={{
            width: '100%',
            background: 'white',
            marginTop: '10px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            flexWrap: 'wrap',
            gap: '14px',
            padding: '10px',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-around',
              alignItems: 'center',
              width: '100%',
              gap: '20px',
            }}
          >
            {currentMultiplier.conditionalCases.map((item, idx) => (
              <Card
                key={item.key}
                sx={{
                  '.MuiCardContent-root': {
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    gap: '30px',
                  },
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    width: '100%',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      width: '100%',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                    }}
                  >
                    <Typography variant="h6">{idx === 0 ? 'If' : 'Else If'}</Typography>
                    {!isPublished && (
                      <IconButton onClick={() => handleRemoveCase(idx)}>
                        <DeleteIcon />
                      </IconButton>
                    )}
                  </Box>
                  <ConditionEditor
                    condition={item.condition}
                    onUpdate={(q: RQBJsonLogic) => handleUpdateQuery(q, idx)}
                  />
                </Box>
                <MultiplierEditor
                  label="Then multiply by"
                  multiplier={item.case}
                  onUpdateExpressionLogic={(q: JsonLogicArithmetic) => handleUpdateExpressionLogic(q, idx)}
                />
              </Card>
            ))}
            <Card
              sx={{
                display: 'flex',
                flexDirection: currentMultiplier.conditionalCases?.length > 0 ? 'row' : 'column',
                alignItems: 'center',
                gap: '20px',
                justifyContent: 'center',
              }}
            >
              <MultiplierEditor
                label={(currentMultiplier.conditionalCases?.length ?? 0) > 0 ? 'Else multiply by' : 'Multiply by'}
                multiplier={currentMultiplier.defaultCase}
                onUpdateExpressionLogic={(q: JsonLogicArithmetic) => handleUpdateExpressionLogic(q)}
              />
              {!isPublished && (
                <Button
                  sx={{ marginTop: '20px' }}
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={handleAddAnotherCase}
                >
                  {currentMultiplier?.conditionalCases.length > 0 ? 'Add another case' : 'Add an if/else case'}
                </Button>
              )}
            </Card>
          </Box>
        </Box>
      </Card>
      {isPublished && (
        <style>
          {`
                .query-builder-disabled {
                  pointer-events: none;
                }
                .query-builder-disabled .ruleGroup .ruleGroup-header,
                .query-builder-disabled .ruleGroup .ruleGroup-body button,
                .query-builder-disabled .ruleGroup .ruleGroup-body svg
                {
                  display: none;
                }
                .query-builder-disabled .ruleGroup .ruleGroup-body .MuiInput-underline:before {
                  border-bottom: 0px;
                }
                `}
        </style>
      )}
      <style>
        {`
                .query-builder-then .rule-operators
                {
                  display: none;
                }
                .query-builder-then .ruleGroup {
                  background: #E4E0DA;
                }
                .query-builder-if .ruleGroup {
                  background: #ffffff;
                }
                `}
      </style>
    </>
  );
}
