import { ValueEditor } from 'react-querybuilder';
import type { ValueEditorProps } from 'react-querybuilder';

export const removeInitial0 = (input: string) => {
  if (input.substring(0, 0) === '0') {
    return input.substring(1);
  }
  return input;
};

function CustomValueEditor(props: ValueEditorProps) {
  const { inputType, handleOnChange, value } = props;

  if (inputType === 'number') {
    return (
      <ValueEditor
        {...props}
        inputType="number"
        value={Number(value).toString()}
        handleOnChange={(input) => {
          if (input === '') {
            handleOnChange(0);
          } else {
            handleOnChange(parseFloat(removeInitial0(input)));
          }
        }}
      />
    );
  }
  return <ValueEditor {...props} />;
}

CustomValueEditor.displayName = 'ValueEditor';

export default CustomValueEditor;
