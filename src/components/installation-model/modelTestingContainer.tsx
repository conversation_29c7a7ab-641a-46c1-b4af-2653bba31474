import { Button } from '@ui/components/Button/Button';
import { Box } from '@mui/material';
import ModelTestingModal from './modals/modelTestingModal';
import { useModalsStore } from './stores/modalsStore';

function ModelTestingContainer() {
  const { setCalculatorModalOpen } = useModalsStore();

  return (
    <Box
      sx={{
        height: '20px',
        position: 'fixed',
        top: 20,
        right: 20,
      }}
    >
      <Button variant="contained" color="primary" size="small" onClick={() => setCalculatorModalOpen(true)}>
        Test Model
      </Button>
      <ModelTestingModal />
    </Box>
  );
}

export default ModelTestingContainer;
