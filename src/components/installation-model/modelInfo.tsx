import { Box, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Card } from '@ui/components/Card/Card';
import { FormAlert } from '@ui/components/FormAlert/FormAlert';
import { TextField } from '@ui/components/TextField/TextField';
import ArrowBackIcon from '@ui/components/Icons/material/ArrowBack';
import SaveIcon from '@ui/components/Icons/material/Save';
import PublishIcon from '@ui/components/Icons/material/Publish';
import TimerIcon from '@ui/components/Icons/material/Timer';
import Link from 'next/link';
import { useModelStore } from './stores/modelStore';
import { api } from '../../utils/api';

export default function ModelInfo() {
  const { model, setModel } = useModelStore();
  const { mutateAsync: updateInstallationModel, isPending: isUpdatingInstallationModel } =
    api.ManHours.updateInstallationModel.useMutation();

  const handleSaveModel = async () => {
    if (!model) return;
    const updatedModel = await updateInstallationModel(model);
    if (updatedModel) setModel(updatedModel);
  };

  const handlePublishModel = async () => {
    if (!model) return;
    const updatedModel = await updateInstallationModel({ ...model, publishedAt: new Date().toISOString() });
    if (updatedModel) setModel(updatedModel);
  };

  const handleNameFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!model) return;
    const updatedModel = {
      ...model,
      name: event.target.value,
    };
    setModel(updatedModel);
  };

  const handleCommentsFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!model) return;
    const updatedModel = {
      ...model,
      comments: event.target.value,
    };
    setModel(updatedModel);
  };

  if (!model) return null;

  const isPublished = !!(model?.publishedAt ?? false);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        height: '100%',
      }}
    >
      <Typography variant="headline1">Installation Model &ndash; {model.name}</Typography>
      <Link href="/installation-model" passHref>
        <Button variant="text" color="primary" startIcon={<ArrowBackIcon />}>
          Return to model selector
        </Button>
      </Link>
      <Card
        sx={{
          '.MuiCardContent-root': {
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
          },
        }}
      >
        {model && (
          <>
            <Box
              sx={{
                minWidth: 200,
                padding: '10px',
                marginTop: '10px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
              }}
            >
              {isPublished ? (
                <>
                  <Typography variant="body1Emphasis">Country</Typography>
                  <Typography variant="body1">{model.countryCode}</Typography>
                </>
              ) : (
                <TextField
                  label="Country"
                  name="country"
                  value={model.countryCode}
                  onChange={() => {}}
                  sx={{
                    minWidth: 260,
                  }}
                  disabled
                />
              )}
            </Box>
            {!isPublished && (
              <Box
                sx={{
                  minWidth: 260,
                  padding: '10px',
                  marginTop: '10px',
                }}
              >
                <TextField
                  label="Model name"
                  name="modelName"
                  value={model.name}
                  onChange={handleNameFieldChange}
                  sx={{
                    minWidth: 260,
                  }}
                />
              </Box>
            )}
            <Box
              sx={{
                minWidth: 260,
                padding: '10px',
                marginTop: '10px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
              }}
            >
              {isPublished ? (
                <>
                  <Typography variant="body1Emphasis">Model Comments</Typography>
                  <Typography variant="body1" sx={{ maxWidth: '300px' }}>
                    {model.comments}
                  </Typography>
                </>
              ) : (
                <TextField
                  label="Model comments"
                  name="modelComments"
                  value={model.comments}
                  onChange={handleCommentsFieldChange}
                  sx={{
                    minWidth: 260,
                  }}
                />
              )}
            </Box>
            <Box
              sx={{
                minWidth: 260,
                padding: '10px',
                marginTop: '10px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
              }}
            >
              {isPublished ? (
                <>
                  <Typography variant="body1Emphasis">Model version</Typography>
                  <Typography variant="body1" sx={{ maxWidth: '300px' }}>
                    {model.version}
                  </Typography>
                </>
              ) : (
                <TextField
                  label="Model version"
                  name="modelVersion"
                  value={model.version}
                  onChange={() => {}}
                  sx={{
                    minWidth: 260,
                  }}
                  disabled
                />
              )}
            </Box>
          </>
        )}
      </Card>
      {isPublished ? (
        <FormAlert errorText="This model has been published and can no longer be edited. Go back to the model selector page and clone this model to make changes." />
      ) : (
        <Stack direction="row" spacing={2} sx={{ pt: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSaveModel}
            startIcon={<SaveIcon />}
            endIcon={isUpdatingInstallationModel ? <TimerIcon /> : undefined}
            disabled={isUpdatingInstallationModel}
          >
            Save model
          </Button>
          <Button variant="contained" color="success" onClick={handlePublishModel} startIcon={<PublishIcon />}>
            Publish model
          </Button>
        </Stack>
      )}
    </Box>
  );
}
