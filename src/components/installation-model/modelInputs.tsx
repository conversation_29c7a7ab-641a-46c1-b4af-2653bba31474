import {
  <PERSON>ert,
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemButton,
  Divider,
  ListItemText,
  ListItemIcon,
  MenuItem,
  SelectChangeEvent,
  IconButton,
} from '@mui/material';
import GridAddIcon from '@ui/components/Icons/material/Add';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import DeleteIcon from '@ui/components/Icons/material/Delete';
import { useModelStore } from './stores/modelStore';
import { ParameterSpec, MicroActivity } from './types/types';
import { doesJsonLogicValueContainInputId } from './utils/inputUtils';
import InputOptions from './inputOptions';

type InputType = 'number' | 'select' | 'boolean';
const inputTypes = ['number', 'select', 'boolean'];

const getMicroActivityNameIfMultiplierContainsInput = (
  microActivityName: MicroActivity['name'],
  multiplier: MicroActivity['multiplier'],
  inputId: ParameterSpec['id'],
) => {
  // go through a multiplier object which contains JSON logic and check if any of the inputs match the
  // inputId and if so, return microActivity.name, if not, return null
  // if the multiplier is a number, return null
  if (typeof multiplier === 'number') return null;
  // if the multiplier is a string and it matches the inputId, return microActivity.name, if not, return null
  if (typeof multiplier === 'string') {
    if (multiplier === inputId) return microActivityName;
    return null;
  }
  if (typeof multiplier === 'boolean') return null;
  // if the multiplier is an object containing a JSONLogic operator and its corresponding array,
  // pass in the array and inputId to doesJsonLogicValueContainInputId to recursively check if
  // the inputId is contained in the array or subobjects arrays
  if (typeof multiplier === 'object') {
    if (doesJsonLogicValueContainInputId(multiplier, inputId)) return microActivityName;
    return null;
  }
  return null;
};

export default function ModelInputs() {
  const { model, addParameter: addInput, updateParameter: updateInput, removeParameter: removeInput } = useModelStore();
  const [selectedInput, setSelectedInput] = useState<ParameterSpec | null>(null);
  const [multipliersUsingSelectedInput, setMultipliersUsingSelectedInput] = useState<string[]>([]);

  useEffect(() => {
    const microActivities = model?.macroActivities.flatMap((macroActivity) => macroActivity.microActivities) ?? [];
    if (selectedInput) {
      const newMultipliersUsingSelectedInput: string[] = [];
      microActivities.forEach((microActivity) => {
        const microActivityName = getMicroActivityNameIfMultiplierContainsInput(
          microActivity.name,
          microActivity.multiplier,
          selectedInput.id,
        );
        if (microActivityName) newMultipliersUsingSelectedInput.push(microActivityName);
      });
      setMultipliersUsingSelectedInput(newMultipliersUsingSelectedInput);
    } else {
      setMultipliersUsingSelectedInput([]);
    }
  }, [model?.macroActivities, selectedInput]);

  const handleClickedInput = (clickedInput: ParameterSpec) => {
    if (!model) return;
    setSelectedInput(clickedInput);
  };

  const handleClickedAddNewInput = () => {
    if (!model) return;
    const newInput = {
      id: uuidv4(),
      name: 'placeholder name',
      type: 'number' as InputType,
      allowDecimal: false,
      helperText: '',
    };
    addInput(newInput);
    setSelectedInput(newInput);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!model || !selectedInput) return;
    const updatedInput = {
      ...selectedInput,
      name: e.target.value,
    };
    updateInput(updatedInput);
    setSelectedInput(updatedInput);
  };

  const handleHelperTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!model || !selectedInput) return;
    const updatedInput = {
      ...selectedInput,
      helperText: e.target.value,
    };
    updateInput(updatedInput);
    setSelectedInput(updatedInput);
  };

  const handleTypeChange = (e: SelectChangeEvent<InputType>) => {
    if (!model || !selectedInput) return;
    const updatedInput = {
      id: selectedInput.id,
      name: selectedInput.name,
      helperText: selectedInput.helperText,
      type: e.target.value as InputType,
      ...(e.target.value === 'select' && { options: [] }),
    };
    updateInput(updatedInput);
    setSelectedInput(updatedInput);
  };

  const handleDeleteInput = (input: ParameterSpec) => {
    setSelectedInput(null);
    removeInput(input.id);
  };

  const isPublished = !!(model?.publishedAt ?? false);

  return (
    <Box
      sx={{
        minWidth: 260,
        marginTop: '10px',
        marginRight: '20px',
      }}
    >
      <Typography variant="headline2">Inputs</Typography>
      <Box
        sx={{
          background: 'white',
          padding: '10px',
          marginTop: '10px',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
        }}
      >
        <Box
          sx={{
            minWidth: 290,
            marginTop: '10px',
            marginRight: '20px',
          }}
        >
          <List dense>
            {model?.parameters.map((input: ParameterSpec) => (
              <ListItem key={input.id}>
                <ListItemButton selected={selectedInput?.id === input.id} onClick={() => handleClickedInput(input)}>
                  <ListItemText primary={input.name} />
                </ListItemButton>
              </ListItem>
            ))}
            {!isPublished && (
              <>
                <Divider />
                <ListItemButton key="newInput" onClick={handleClickedAddNewInput}>
                  <ListItemIcon>
                    <GridAddIcon />
                  </ListItemIcon>
                  <ListItemText primary="Add new input" />
                </ListItemButton>
              </>
            )}
          </List>
        </Box>
        {selectedInput && (
          <Box
            sx={{
              minWidth: 460,
              marginTop: '10px',
              marginRight: '20px',
            }}
          >
            <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
              <Typography variant="headline2">{selectedInput.name}</Typography>
              {!isPublished && multipliersUsingSelectedInput.length === 0 && (
                <IconButton edge="end" onClick={() => handleDeleteInput(selectedInput)}>
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
            {!isPublished && multipliersUsingSelectedInput.length > 0 && (
              <Alert severity="info" sx={{ marginBottom: '10px' }}>
                <Typography variant="body1">
                  This input is used in the following microactivities&apos; multipliers:
                </Typography>
                <Typography variant="body1Emphasis">{multipliersUsingSelectedInput.join(', ')}</Typography>
                <Typography variant="body1">
                  {`and therefore its type cannot be changed and it cannot be deleted.
                     If you wish to edit this input, please remove it from the microactivities' multipliers first.`}
                </Typography>
              </Alert>
            )}
            <Box
              sx={{
                maxWidth: 460,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
                gap: '10px',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Box>
                {isPublished ? (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0,
                      justifyContent: 'flex-start',
                      alignContent: 'center',
                    }}
                  >
                    <Typography variant="body1Emphasis">Name</Typography>
                    <Typography variant="body1">{selectedInput.name}</Typography>
                  </Box>
                ) : (
                  <TextField
                    label="Name"
                    name="name"
                    value={selectedInput.name}
                    onChange={handleNameChange}
                    sx={{ width: '100%' }}
                    size="small"
                  />
                )}
              </Box>
              <Box>
                {isPublished ? (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0,
                      justifyContent: 'flex-start',
                      alignContent: 'center',
                    }}
                  >
                    <Typography variant="body1Emphasis">Helper text</Typography>
                    <Typography variant="body1">{selectedInput.helperText}</Typography>
                  </Box>
                ) : (
                  <TextField
                    type="textarea"
                    label="Helper text"
                    name="helperText"
                    value={selectedInput.helperText}
                    onChange={handleHelperTextChange}
                    sx={{ width: '100%' }}
                    size="small"
                  />
                )}
              </Box>
              <Box>
                {isPublished || multipliersUsingSelectedInput.length > 0 ? (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 0,
                      justifyContent: 'flex-start',
                      alignContent: 'center',
                    }}
                  >
                    <Typography variant="body1Emphasis">Input type</Typography>
                    <Typography variant="body1">{selectedInput.type}</Typography>
                  </Box>
                ) : (
                  <Select
                    label="Input type"
                    name="Input type"
                    value={selectedInput.type}
                    onChange={(e) => handleTypeChange(e as SelectChangeEvent<InputType>)}
                    sx={{
                      ' .MuiInputBase-root': {
                        width: '250px',
                      },
                      '.MuiBox-root': {
                        marginTop: '-8px',
                      },
                      width: '100% !important',
                    }}
                    size="small"
                  >
                    {inputTypes.map((inputType) => (
                      <MenuItem key={inputType} value={inputType}>
                        {inputType}
                      </MenuItem>
                    ))}
                  </Select>
                )}
              </Box>
            </Box>
            {selectedInput?.type === 'select' && (
              <InputOptions
                selectedInput={selectedInput}
                isPublished={isPublished}
                multipliersUsingSelectedInput={multipliersUsingSelectedInput}
                setSelectedInput={setSelectedInput}
              />
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
}
