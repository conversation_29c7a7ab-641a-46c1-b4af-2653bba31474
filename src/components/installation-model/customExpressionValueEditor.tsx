import { useEffect } from 'react';
import type { ValueEditorProps } from 'react-querybuilder';
import { ValueEditor } from 'react-querybuilder';
import { removeInitial0 } from './customConditionValueEditor';

function CustomValueEditor(props: ValueEditorProps) {
  const { fieldData, inputType, handleOnChange, value } = props;
  useEffect(() => {
    if (inputType === 'number' && fieldData.name === 'value') {
      handleOnChange(null);
    }
  }, [fieldData.name, handleOnChange, inputType]);

  if (fieldData.name === 'value') {
    return (
      <ValueEditor
        {...props}
        inputType="number"
        value={Number(value).toString()}
        handleOnChange={(input) => {
          if (input === '') {
            handleOnChange(0);
          } else {
            handleOnChange(parseFloat(removeInitial0(input)));
          }
        }}
      />
    );
  }
  return null;
}

CustomValueEditor.displayName = 'ValueEditor';

export default CustomValueEditor;
