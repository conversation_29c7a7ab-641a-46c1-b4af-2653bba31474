import type { RQBJsonLogic, RuleGroupType } from 'react-querybuilder';
import { z } from 'zod';
import type { JsonLogicDifference, JsonLogicProduct, JsonLogicQuotient, JsonLogicSum } from 'json-logic-js';
import {
  installationModelSchema,
  parameterSpecSchema,
  resourceGroupSchema,
  resourceSchema,
  macroActivitySchema,
  unitManHours,
  microActivitySchema,
  optionSchema,
  modelInListSchema,
} from './zodSchemas';
import {
  macroActivityWithManHoursSchema,
  manHoursCalculationSchema,
  microActivityWithManHoursSchema,
  parameterSchema,
  resourceGroupManHoursSchema,
  resourceManHoursSchema,
} from '../../baseline-calculator/schemas';

export type InstallationModel = z.infer<typeof installationModelSchema>;

export type ModelInList = z.infer<typeof modelInListSchema>;

export type ModelsResponse = {
  models: ModelInList[];
};

export type ParameterSpec = z.infer<typeof parameterSpecSchema>;

export type Option = z.infer<typeof optionSchema>;

export type ResourceGroup = z.infer<typeof resourceGroupSchema>;

export type Resource = z.infer<typeof resourceSchema>;

export type MacroActivity = z.infer<typeof macroActivitySchema>;

export type ManHours = z.infer<typeof unitManHours>;

export type MicroActivity = z.infer<typeof microActivitySchema> & {
  multiplier: RQBJsonLogic;
};

export type MacroActivityWithManHours = z.infer<typeof macroActivityWithManHoursSchema>;

export type MicroActivityWithManHours = z.infer<typeof microActivityWithManHoursSchema>;

export type Parameter = z.infer<typeof parameterSchema>;

export type ResourceManHours = z.infer<typeof resourceManHoursSchema>;

export type ResourceGroupManHours = z.infer<typeof resourceGroupManHoursSchema>;

export type ManHoursCalculation = z.infer<typeof manHoursCalculationSchema>;

export interface Multiplier {
  rule: RuleGroupType;
}

export interface IfElseExpression<T> {
  conditionalCases: {
    condition: RQBJsonLogic;
    case: T;
    key: string;
  }[];
  defaultCase: T;
}

export type JsonLogicArithmetic = JsonLogicSum | JsonLogicProduct | JsonLogicQuotient | JsonLogicDifference;
