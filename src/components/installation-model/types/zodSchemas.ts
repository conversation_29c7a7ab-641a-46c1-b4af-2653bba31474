import { Country } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/location/v1/model';
import { PaymentType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.money.v1';
import { RQBJsonLogic } from 'react-querybuilder';
import { HouseType } from 'types/types';
import { z } from 'zod';

const rQBJsonLogic: z.ZodType<RQBJsonLogic> = z.any();
export const optionSchema = z.object({
  name: z.string(),
  value: z.string(),
});

export const unitManHours = z.object({
  unit: z.string(),
  value: z.number(),
});

export const resourceSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export const microActivitySchema = z.object({
  id: z.string(),
  name: z.string(),
  resourcesRequired: z.array(z.string()),
  multiplier: rQ<PERSON><PERSON><PERSON>Logic,
  manHoursInMinutes: z.number().nullable(), // TODO: remove nullable and make microActivity modal have it's own internal state that allows this to be null while editing
});

export const parameterSpecSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.union([z.literal('number'), z.literal('select'), z.literal('boolean')]),
  defaultNumber: z.number().nullish(),
  defaultOption: optionSchema.nullish(),
  defaultBoolean: z.boolean().nullish(),
  allowDecimals: z.boolean().nullish(),
  suffix: z.string().nullish(),
  step: z.number().nullish(),
  minValue: z.number().nullish(),
  maxValue: z.number().nullish(),
  helperText: z.string().nullish(),
  options: z.array(optionSchema).nullish(),
});

export const resourceGroupSchema = z.object({
  id: z.string(),
  name: z.string(),
  resources: z.array(resourceSchema),
});

export const macroActivitySchema = z.object({
  id: z.string(),
  name: z.string(),
  microActivities: z.array(microActivitySchema),
});

export const installationModelSchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.number(),
  createdAt: z.string().datetime().nullish(),
  updatedAt: z.string().datetime().nullish(),
  publishedAt: z.string().datetime().nullish(),
  countryCode: z.string(),
  comments: z.string(),
  parameters: z.array(parameterSpecSchema),
  macroActivities: z.array(macroActivitySchema),
  resourceGroups: z.array(resourceGroupSchema),
});

export const modelInListSchema = installationModelSchema.omit({
  parameters: true,
  macroActivities: true,
  resourceGroups: true,
});

export const hoursForRoleSchema = z.object({
  role: z.enum(['INSTALLER', 'LANDSCAPER', 'ELECTRICIAN']),
  hours: z.number(),
});

export const installationJobSchema = z.object({
  installationProjectId: z.string(),
  hoursForRole: z.array(hoursForRoleSchema),
});

const locationIdentifierSchema = z
  .object({
    $case: z.enum(['googlePlace']),
    googlePlace: z.object({
      placeId: z.string().min(1),
    }),
  })
  .or(
    z.object({
      $case: z.enum(['loqate']),
      loqate: z.object({
        id: z.string().min(1),
      }),
    }),
  );

const addressSchema = z.object({
  placeId: z.string({ required_error: 'addressIsRequired' }),
  street: z.string().optional(),
  formattedAddress: z.string(),
  postalCode: z.string(),
  city: z.string().optional(),
  iso3166: z.object({
    $case: z.enum(['country', 'countrySubdivision']),
    country: z.string().min(1).optional(),
    countrySubdivision: z
      .object({
        country: z.string().min(1),
        subdivision: z.string().min(1),
      })
      .optional(),
  }),
  geometry: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  identifier: locationIdentifierSchema.optional(),
});

export const surveyResourceSkillSchema = z.enum(['HEAT_PUMP', 'PHOTOVOLTAIC', 'BATTERY', 'SOLAR_THERMAL']);

export type SurveyResourceSkill = z.infer<typeof surveyResourceSkillSchema>;

export const saveRegionBody = z.object({
  region: z.string(),
});

export const updatePaymentBody = z.object({
  paymentType: z.nativeEnum(PaymentType),
});

export const supportedActions = [
  'ENERGY_SOLUTION_ACTION_TYPE_CANCEL',
  'ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_DOCUMENTATION_AND_APPLICATIONS',
  'ENERGY_SOLUTION_ACTION_TYPE_COMPLETE_TECHNICAL_DATA_COLLECTION',
  'ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRICE',
  'ENERGY_SOLUTION_ACTION_TYPE_LOCK_PRODUCTS',
  'ENERGY_SOLUTION_ACTION_TYPE_REOPEN',
  'ENERGY_SOLUTION_ACTION_TYPE_SEND_QUOTE',
  'ENERGY_SOLUTION_ACTION_TYPE_SET_FINANCING_SECURED',
  'ENERGY_SOLUTION_ACTION_TYPE_SET_ORDERED',
  'ENERGY_SOLUTION_ACTION_TYPE_UNDO_ORDERED',
  'ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_FULLY',
  'ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRICE',
  'ENERGY_SOLUTION_ACTION_TYPE_UNLOCK_PRODUCTS',
] as const;

export const actionTypes = z.enum(supportedActions);

export type ActionType = z.infer<typeof actionTypes>;

export const countryObject = z.nativeEnum(Country);

export type CreateFormPostBodyType = z.infer<typeof createFormPostBody>;

export const productSelectionsSelection = z.object({
  sku: z.string(),
  quantity: z.number(),
  priceOverrideMinorAmountExclTax: z.number().optional(),
  details: z.any().optional(),
  overrideLockedPrice: z.boolean().optional(),
});

export const solutionPatchBodyLineItems = z
  .array(z.union([productSelectionsSelection, z.array(productSelectionsSelection)]))
  .optional();

export const solutionPatchBody = z.object({
  lineItems: solutionPatchBodyLineItems,
  updatedLineItems: z.boolean().optional(),
  discounts: z
    .array(
      z.object({
        id: z.object({ value: z.string().nullish() }).optional(),
        name: z.string().refine((x) => x.length > 0, { message: 'discount name required' }),
        variant: z
          .object({
            $case: z.enum(['fixedAmount', 'percentual']),
            fixedAmount: z
              .object({
                amount: z
                  .object({
                    currencyCode: z.string(),
                    minorAmount: z.number().or(z.string()).pipe(z.coerce.number()),
                    taxDetails: z
                      .object({
                        minorAmountExcludingTax: z.number().or(z.string()).pipe(z.coerce.number()).optional(),
                        taxMinorAmount: z.number().or(z.string()).pipe(z.coerce.number()).optional(),
                        taxRate: z.number().refine((x) => x >= 0 && x <= 1, {
                          message: 'discount taxRate should be a number between 0 and 1',
                        }),
                      })
                      .optional(),
                  })
                  .optional(),
              })
              .optional(),
            percentual: z
              .object({
                value: z.number(),
                taxRate: z.number(),
              })
              .optional(),
            minorAmountIncludingTax: z.number().or(z.string()).pipe(z.coerce.number()).optional(),
          })
          .optional(),
      }),
    )
    .optional(),
  locked: z.boolean().optional(),
  regionId: z.string().optional(),
  subsidy: z
    .object({
      details: z
        .object({
          variant: z
            .object({
              $case: z.enum(['percentual', 'percentualTaxWriteoff', 'amount', 'disabled', 'none']),
              percentual: z
                .object({
                  // if string convert to number
                  value: z.number().or(z.string()).pipe(z.coerce.number()),
                  housingUnits: z.number().optional(),
                })
                .optional(),
              amount: z
                .object({
                  amount: z.object({
                    currencyCode: z.string(),
                    minorAmount: z.number().or(z.string()).pipe(z.coerce.number()),
                  }),
                })
                .optional(),
              percentualTaxWriteoff: z
                .object({
                  value: z.number().or(z.string()).pipe(z.coerce.number()),
                  durationYears: z.number().or(z.string()).pipe(z.coerce.number()),
                })
                .optional(),
              none: z.object({}).optional(),
            })
            .optional(),
        })
        .optional(),
      minorAmount: z.number().optional(),
      durationYears: z.number().optional(),
      type: z.enum(['PERCENTUAL', 'PERCENTUAL_TAX_WRITE_OFF', 'AMOUNT', 'DISABLED', 'NONE']).optional(),
    })
    .optional(),
  transition: z
    .object({
      note: z.string().optional(),
      action: z
        .object({
          type: actionTypes.optional(),
          paymentMethod: z
            .object({
              type: z.enum(['INSTALMENTS', 'INVOICE', 'SPLIT_INVOICE']).optional(),
            })
            .optional(),
        })
        .optional(),
    })
    .optional(),
  taxFacts: z
    .array(
      z.object({
        details: z.object({
          $case: z.enum(['defaultTaxOverride']),
          defaultTaxOverride: z.object({
            rate: z.number(),
          }),
        }),
      }),
    )
    .optional(),
  expiresAt: z.date().optional(),
});

export type SolutionPatchBodyType = z.infer<typeof solutionPatchBody>;

export const houseDataObject = z.object({
  groundworkId: z.string().uuid().nullish(),
  fuelType: z
    .union([z.literal(''), z.enum(['OIL', 'GAS', 'LIQUID_GAS'])])
    .optional()
    .nullish(),
  consumption: z.number().or(z.string()).pipe(z.coerce.number()).nullish(),
  sqmSize: z.number().optional().nullish(),
  measurement: z.enum(['houseSize', 'consumption', 'bedroomCount']).optional().nullish(),
  bedroomCount: z.number().optional().nullish(),
  houseType: z
    .nativeEnum(HouseType)
    .superRefine((value, ctx) => {
      if (!Object.values(HouseType).includes(value as any)) {
        // Cast to any due to TS enum/string mismatch
        ctx.addIssue({
          code: 'custom',
          message: 'You need to select a house type to be able to save',
        });
      }
    })
    .optional(),
  postalCode: z.string().optional().nullish(),
  region: z.string().optional(),
  housingUnits: z.number().optional().nullish(),
});

export type HouseData = z.infer<typeof houseDataObject>;

export const houseDataPatchBody = z
  .object({
    address: addressSchema,
    addressId: z.string().optional(),
    facilityId: z.string().optional(),
    newFacilityName: z.string().optional(),
    initialFacilityName: z.string().optional(),
  })
  .merge(houseDataObject);

export type HouseDataPatchBody = z.infer<typeof houseDataPatchBody>;

export const CountryCodeType = z.enum([
  'AD',
  'AL',
  'AM',
  'AT',
  'BA',
  'BE',
  'BG',
  'BY',
  'CH',
  'CY',
  'CZ',
  'DE',
  'DK',
  'EE',
  'ES',
  'FI',
  'FR',
  'GB',
  'GE',
  'GI',
  'GR',
  'HR',
  'HU',
  'IE',
  'IS',
  'IT',
  'LI',
  'LT',
  'LU',
  'LV',
  'MC',
  'MD',
  'ME',
  'MK',
  'MT',
  'NL',
  'NO',
  'PL',
  'PT',
  'RO',
  'RS',
  'RU',
  'SE',
  'SI',
  'SK',
  'SM',
  'TR',
  'UA',
  'VA',
  'XK',
]);

export const createFormPostBody = z
  .object({
    address: addressSchema.nullable(),
    firstName: z.string().min(1),
    lastName: z.string().min(1),
    email: z.string().min(1), // Using backends validation for email
    phoneNumber: z.string().min(1),
    countryCode: z.string().length(2),
    regionId: z.string().optional(),
    iso3166: z.object({
      $case: z.enum(['country', 'countrySubdivision']),
      country: z.string().min(1).optional(),
      countrySubdivision: z
        .object({
          country: z.string().min(1),
          subdivision: z.string().min(1),
        })
        .optional(),
    }),
    leadSource: z.number().optional(),
    allowEmailReuse: z.boolean(),
    addressId: z.string().optional(),
    facilityName: z.string().optional(),
    facilityId: z.string().optional(),
    newFacilityName: z.string().optional(),
    initialFacilityName: z.string().optional(),
  })
  .merge(houseDataObject);

export type CreateFormPostBody = z.infer<typeof createFormPostBody>;

export const productPatchBody = z.object({
  products: z.array(
    z.object({
      id: z.object({ value: z.string() }),
      sku: z.string().optional(),
      displayName: z.string().optional(),
      price: z.object({ currencyCode: z.string().optional(), minorAmount: z.number().optional() }).optional(),
    }),
  ),
});
