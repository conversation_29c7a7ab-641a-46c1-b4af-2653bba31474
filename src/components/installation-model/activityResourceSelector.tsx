import { useEffect } from 'react';
import { Box, List, ListItem, ListItemButton, ListItemText, Typography, Checkbox, ListSubheader } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { useModelStore } from './stores/modelStore';
import { Resource, ResourceGroup } from './types/types';

export default function ActivityResourceSelector() {
  const { model, updateMicroActivity, selectedMicroActivity } = useModelStore();

  useEffect(() => {
    const element = document.getElementById('activity-resource-selector');
    if (element) element.scrollIntoView();
  }, [selectedMicroActivity?.id]);

  const handleResourceItemClick = (clickedResource: Resource) => {
    if (!selectedMicroActivity) return;
    const currentResourcesRequired = selectedMicroActivity?.resourcesRequired;
    const updatedMicroActivity = {
      ...selectedMicroActivity,
      resourcesRequired: currentResourcesRequired.some((resourceRequired) => resourceRequired === clickedResource.id)
        ? currentResourcesRequired.filter((resourceRequired) => resourceRequired !== clickedResource.id)
        : [...currentResourcesRequired, clickedResource.id],
    };
    updateMicroActivity(updatedMicroActivity);
  };

  if (!model || !selectedMicroActivity) return null;

  return (
    <Card
      sx={{
        width: '400px',
        height: '100%',
      }}
      id="activity-resource-selector"
    >
      <Typography variant="headline2">Resources required</Typography>
      <Box
        sx={{
          marginTop: '4px',
        }}
      >
        {model.resourceGroups.length > 0 && (
          <List dense>
            {model.resourceGroups.map((resourceGroup: ResourceGroup) => (
              <ListItem key={resourceGroup.id} disableGutters>
                <List
                  dense
                  subheader={<ListSubheader component="div">{resourceGroup.name}</ListSubheader>}
                  sx={{
                    width: '100%',
                  }}
                >
                  {resourceGroup.resources.map((resource: any) => (
                    <ListItem
                      key={resource.id}
                      secondaryAction={
                        <Checkbox
                          edge="end"
                          onChange={() => handleResourceItemClick(resource)}
                          checked={selectedMicroActivity?.resourcesRequired.some(
                            (resourceRequired) => resourceRequired === resource.id,
                          )}
                          disabled={!!model.publishedAt}
                        />
                      }
                      disablePadding
                    >
                      <ListItemButton>
                        <ListItemText primary={resource.name} />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Card>
  );
}
