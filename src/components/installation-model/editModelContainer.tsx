import { Typography, Stack } from '@mui/material';
import { RQBJsonLogic } from 'react-querybuilder';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { api } from '../../utils/api';
import { InstallationModel, MacroActivity } from './types/types';
import ModelInitializer from './ModelInitializer';

export default function EditModelContainer({ installationModelId }: { installationModelId: string }) {
  const { data: installationModel, isLoading: isLoadingInstallationModel } = api.ManHours.getInstallationModel.useQuery(
    installationModelId,
    {
      refetchOnWindowFocus: false,
    },
  );

  if (isLoadingInstallationModel) {
    return (
      <Stack>
        <Typography variant="headline2">Loading...</Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (!installationModel) {
    return (
      <Stack>
        <Typography variant="headline2">No installation model found</Typography>
      </Stack>
    );
  }

  const model = { ...installationModel } as InstallationModel;
  // The following is to handle the fact that for presently stored models the multiplier field
  // in the microActivity has properties with keys. We can remove this once the models are updated
  const macroActivities: MacroActivity[] = model.macroActivities.map((macroActivity: MacroActivity) => ({
    ...macroActivity,
    microActivities: macroActivity.microActivities.map((microActivity) => {
      let newMultiplier =
        typeof microActivity.multiplier === 'number'
          ? { '*': [microActivity.multiplier] }
          : ({
              '*': [1],
            } as RQBJsonLogic);
      if (typeof microActivity.multiplier === 'object') {
        if ('*' in microActivity.multiplier) {
          newMultiplier = { '*': microActivity.multiplier['*'] } as RQBJsonLogic;
        }
        if ('if' in microActivity.multiplier) {
          newMultiplier = { if: microActivity.multiplier.if };
        }
      }
      return {
        ...microActivity,
        multiplier: newMultiplier,
      };
    }),
  }));

  model.macroActivities = macroActivities as MacroActivity[];

  return <ModelInitializer installationModel={model} />;
}
