import {
  Box,
  List,
  ListItemButton,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import Link from 'next/link';
import { ModelInList } from './types/types';
import { api } from '../../utils/api';

export default function ModelSelector({
  refetchAllInstallationModels,
  modelsByCountry,
  selectedCountry,
  setModelModalOpen,
  setSelectedCountry,
  selectedModel,
  setSelectedModel,
}: {
  refetchAllInstallationModels: () => void;
  modelsByCountry: Map<string, ModelInList[]>;
  setModelModalOpen: (open: boolean) => void;
  selectedCountry: string | null;
  setSelectedCountry: (countryCode: string) => void;
  selectedModel: ModelInList | null;
  setSelectedModel: (model: ModelInList | null) => void;
}) {
  const modelsForSelectedCountry = selectedCountry ? (modelsByCountry.get(selectedCountry) ?? []) : [];

  const { mutateAsync: deleteInstallationModel } = api.ManHours.deleteInstallationModel.useMutation();

  const handleCountryItemClick = (_event: React.MouseEvent<HTMLDivElement, MouseEvent>, countryCode: string) => {
    setSelectedCountry(countryCode);
  };

  const handleModelItemClick = (_event: React.MouseEvent<HTMLDivElement, MouseEvent>, clickedModel: ModelInList) => {
    setSelectedModel(clickedModel);
  };

  const handleCreateNewModel = () => {
    setModelModalOpen(true);
  };

  const handleDeleteModel = async () => {
    if (!selectedModel) return;
    await deleteInstallationModel(selectedModel.id);
    setSelectedModel(null);
    refetchAllInstallationModels();
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        height: '100%',
      }}
    >
      <Typography variant="headline1">Baseline Models</Typography>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          width: '100%',
          height: '100%',
          marginTop: '20px',
        }}
      >
        <Box
          sx={{
            minWidth: 260,
            marginTop: '10px',
            marginRight: '20px',
          }}
        >
          <Typography variant="headline2">Country</Typography>
          {modelsByCountry && (
            <Box
              sx={{
                minWidth: 260,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
                marginRight: '10px',
              }}
            >
              <List>
                {[...modelsByCountry.keys()].map((countryCode: string) => (
                  <ListItemButton
                    key={countryCode}
                    selected={selectedCountry === countryCode}
                    onClick={(event) => handleCountryItemClick(event, countryCode)}
                  >
                    <ListItemText primary={countryCode} />
                  </ListItemButton>
                ))}
              </List>
            </Box>
          )}
        </Box>
        {selectedCountry && (
          <Box
            sx={{
              minWidth: 260,
              marginTop: '10px',
              marginRight: '20px',
            }}
          >
            <Typography variant="headline2">Model</Typography>

            <Box
              sx={{
                minWidth: 260,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
              }}
            >
              {modelsForSelectedCountry && modelsForSelectedCountry.length > 0 ? (
                <List>
                  {modelsForSelectedCountry.map((m) => (
                    <ListItemButton
                      key={m.id}
                      selected={selectedModel?.id === m.id}
                      onClick={(event) => handleModelItemClick(event, m)}
                    >
                      <ListItemText primary={m.name} />
                    </ListItemButton>
                  ))}
                </List>
              ) : (
                <Typography variant="body1">No models for this country</Typography>
              )}
            </Box>
          </Box>
        )}
        {selectedCountry && selectedModel && (
          <Box
            sx={{
              minWidth: 260,
              marginTop: '10px',
            }}
          >
            <Typography variant="headline2">Metadata</Typography>
            <TableContainer
              sx={{
                minWidth: 260,
                background: 'white',
                padding: '10px',
                marginTop: '10px',
              }}
            >
              <Table sx={{ minWidth: 260 }} size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Comments</TableCell>
                    <TableCell>Version</TableCell>
                    <TableCell>Saved At</TableCell>
                    <TableCell>Published At</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>
                      <Typography variant="body1">{selectedModel.name}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{selectedModel.comments}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{selectedModel.version}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{selectedModel.updatedAt?.toString()}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{selectedModel.publishedAt?.toString()}</Typography>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-start',
                width: '100%',
                height: '100%',
                marginTop: '20px',
                gap: '1rem',
              }}
            >
              <Link passHref href={`/installation-model/${selectedModel.id}`}>
                <Button variant="contained" color="primary">
                  {selectedModel.publishedAt ? 'View' : 'Edit'}
                </Button>
              </Link>
              <Button variant="contained" color="primary" onClick={handleCreateNewModel}>
                Create updated version
              </Button>
              {!selectedModel.publishedAt && (
                <Button variant="contained" color="primary" onClick={handleDeleteModel}>
                  Delete
                </Button>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Box>
  );
}
