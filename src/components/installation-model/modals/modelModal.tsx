import { ChangeEvent, useEffect, useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { v4 as uuidv4 } from 'uuid';
import { MenuItem, SelectChangeEvent, Stack, Typography } from '@mui/material';
import { useRouter } from 'next/router';
import { CountryCode } from 'utils/marketConfigurations';
import { InstallationModel, ModelInList } from '../types/types';
import { api } from '../../../utils/api';

const initialModel: InstallationModel = {
  id: '',
  name: '',
  comments: '',
  version: 0,
  countryCode: CountryCode.IT,
  resourceGroups: [],
  parameters: [],
  macroActivities: [],
};

export default function ModelModal({
  modelsByCountry,
  modelModalOpen,
  setModelModalOpen,
  selectedModel,
}: {
  modelsByCountry: Map<string, ModelInList[]>;
  modelModalOpen: boolean;
  setModelModalOpen: (open: boolean) => void;
  selectedModel: ModelInList | null;
}) {
  const [newModel, setNewModel] = useState<InstallationModel>(initialModel);
  const { mutateAsync: createInstallationModel } = api.ManHours.createInstallationModel.useMutation();
  const router = useRouter();

  useEffect(() => {
    if (modelModalOpen) {
      if (selectedModel) {
        setNewModel({
          ...(selectedModel as InstallationModel),
          id: uuidv4(),
          version: selectedModel.version + 1,
          name: `${selectedModel?.name} - Cloned`,
          createdAt: undefined,
          updatedAt: undefined,
          publishedAt: undefined,
        });
      } else {
        setNewModel({
          ...initialModel,
          id: uuidv4(),
        });
      }
    }
  }, [modelModalOpen, selectedModel]);

  const handleClose = () => setModelModalOpen(false);

  const handleClickCreate = async () => {
    const created = await createInstallationModel(newModel);
    if (created?.installationModelId?.value) {
      handleClose();
      router.push(`/installation-model/${created.installationModelId.value}`);
    }
  };

  const handleNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    setNewModel((model) => ({
      ...model,
      name: e.target.value,
    }));
  };

  const handleCommentsChange = (e: ChangeEvent<HTMLInputElement>) => {
    setNewModel((model) => ({ ...model, comments: e.target.value }));
  };

  const handleChangeCountry = (e: SelectChangeEvent<string>) => {
    setNewModel((model) => ({ ...model, countryCode: e.target.value }));
  };

  return (
    <Modal isModalOpen={modelModalOpen} handleClose={handleClose} width="80vw" height="80vh">
      <Typography variant="headline1" component="h2" sx={{ alignSelf: 'center' }}>
        {selectedModel ? `Clone model - "${selectedModel.name}"` : 'Create model'}
      </Typography>
      <Stack spacing="1rem">
        <TextField label="Model name" name="modelName" value={newModel.name} onChange={handleNameChange} />
        <TextField label="Comments" name="comments" value={newModel.comments} onChange={handleCommentsChange} />
        <Select label="Country" name="country" value={newModel.countryCode} onChange={handleChangeCountry}>
          {[...modelsByCountry.keys()].map((countryCode) => (
            <MenuItem key={countryCode} value={countryCode}>
              {countryCode}
            </MenuItem>
          ))}
        </Select>
      </Stack>
      <Button sx={{ marginTop: '20px' }} onClick={handleClickCreate}>
        Create
      </Button>
    </Modal>
  );
}
