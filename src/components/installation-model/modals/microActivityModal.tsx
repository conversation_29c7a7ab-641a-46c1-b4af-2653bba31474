import { Box } from '@mui/material';
import { Modal } from '@ui/components/Modal/Modal';
import { useModalsStore } from '../stores/modalsStore';
import { useModelStore } from '../stores/modelStore';
import ActivityResourceSelector from '../activityResourceSelector';
import ActivityMulitplier from '../activityMultiplier';
import MicroActivityModalTitle from '../microActivityModalTitle';
import MicroActivitySettings from '../microActivitySettings';
import MicroActivityMultiplierTest from '../microActivityMultiplierTest';

export default function MicroActivityModal() {
  const { setSelectedMicroActivity, selectedMicroActivity } = useModelStore();
  const { microActivityModalOpen, setMicroActivityModalOpen } = useModalsStore();

  const handleClose = () => {
    if (selectedMicroActivity?.manHoursInMinutes === null) return;
    setSelectedMicroActivity(null);
    setMicroActivityModalOpen(false);
  };

  if (!selectedMicroActivity) return null;

  return (
    <Modal
      isModalOpen={microActivityModalOpen}
      handleClose={handleClose}
      width="95vw"
      height="95vh"
      sx={{
        justifyContent: 'flex-start',
      }}
    >
      <MicroActivityModalTitle handleClose={handleClose} />
      <MicroActivitySettings handleClose={handleClose} />
      <Box
        sx={{
          width: '100%',
          marginTop: '20px',
          overflow: 'visible',
          display: 'flex',
          flexDirection: 'row',
          gap: '4px',
        }}
      >
        <ActivityResourceSelector />
        <ActivityMulitplier />
        <MicroActivityMultiplierTest />
      </Box>
    </Modal>
  );
}
