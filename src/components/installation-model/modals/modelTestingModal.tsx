import { useState } from 'react';
import { Modal } from '@ui/components/Modal/Modal';
import { Box, Stack, Typography } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useIntl } from 'react-intl';
import { getDefaultValue, isFormValid, NullableParameter } from 'components/baseline-calculator/utils/form';
import GroupedActivities from 'components/baseline-calculator/groupedActivities';
import MacroActivityTable from 'components/baseline-calculator/macroActivitiesTable';
import useGetManHoursCalculationResult from 'components/baseline-calculator/queries/useGetManHoursCalculationResult';
import Form from 'components/baseline-calculator/Form';
import { useModalsStore } from '../stores/modalsStore';
import { useModelStore } from '../stores/modelStore';
import { Parameter } from '../types/types';

export default function ModelTestingModal() {
  const intl = useIntl();
  const { model } = useModelStore();
  const initialParameters = model?.parameters.map((parameter) => ({
    id: parameter.id,
    name: parameter.name,
    value: getDefaultValue(parameter) ?? null,
  })) as NullableParameter[];
  const { calculatorModalOpen, setCalculatorModalOpen } = useModalsStore();
  const [parametersForm, setParametersForm] = useState<NullableParameter[]>(initialParameters);
  const formIsValid = parametersForm !== undefined && isFormValid(parametersForm);

  const closeModal = () => setCalculatorModalOpen(false);

  const manHoursCalculation = useGetManHoursCalculationResult({
    installationModelId: model!.id,
    parameters: parametersForm as Parameter[],
    formIsValid,
  });

  return (
    <Modal isModalOpen={calculatorModalOpen} handleClose={closeModal} width="90vw" height="90vh">
      {model && initialParameters && (
        <Box
          sx={{
            width: '100%',
            padding: '20px 0 40px 0',
          }}
        >
          <Grid container spacing={2}>
            <Stack alignItems="flex-start" justifyContent="space-between" spacing={2}>
              <Typography variant="headline1" sx={{ paddingLeft: '8px' }}>
                {intl.formatMessage({ id: 'baselineCalc.title.calculateInstallationBaseline' })}
              </Typography>
            </Stack>
            <Grid container spacing={2} display="grid" pt={2} gridTemplateColumns="repeat(2, 1fr)">
              <Grid sx={{ marginBottom: '20px' }}>
                <Typography variant="headline2">{intl.formatMessage({ id: 'baselineCalc.title.inputs' })}</Typography>
                <Form
                  currentModel={model}
                  parametersForm={parametersForm}
                  setParametersForm={setParametersForm}
                  initialParameters={initialParameters}
                />

                {manHoursCalculation.data &&
                  manHoursCalculation.data.result &&
                  manHoursCalculation.data.result.totalManHours && (
                    <>
                      <Typography variant="headline2" sx={{ marginTop: '20px' }}>
                        {intl.formatMessage({ id: 'baselineCalc.title.summary' })}
                      </Typography>
                      <GroupedActivities
                        totalsPerResourceGroup={manHoursCalculation.data.result.totalsPerResourceGroup}
                        totalManHours={manHoursCalculation.data.result.totalManHours}
                      />
                    </>
                  )}
              </Grid>
              {manHoursCalculation.data && manHoursCalculation.data.result && (
                <Grid>
                  <Typography variant="headline2">{intl.formatMessage({ id: 'baselineCalc.title.output' })}</Typography>
                  <MacroActivityTable macroActivities={manHoursCalculation.data.result.macroActivities} />
                </Grid>
              )}
            </Grid>
          </Grid>
        </Box>
      )}
    </Modal>
  );
}
