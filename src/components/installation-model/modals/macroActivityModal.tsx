import { Typography } from '@mui/material';
import { ChangeEvent, useEffect, useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { TextField } from '@ui/components/TextField/TextField';
import { v4 as uuidv4 } from 'uuid';
import { MacroActivity } from '../types/types';
import { useModelStore } from '../stores/modelStore';
import { useModalsStore } from '../stores/modalsStore';

function initialMacroActivityState(): MacroActivity {
  return {
    id: uuidv4(),
    name: '',
    microActivities: [],
  };
}

export default function MacroActivityModal() {
  const { addMacroActivity, updateMacroActivity, removeMacroActivity } = useModelStore();
  const { macroActivityModalOpen, setMacroActivityModalOpen, updatingMacroActivity } = useModalsStore();
  const [newMacroActivity, setNewMacroActivity] = useState<MacroActivity>(initialMacroActivityState());

  useEffect(() => {
    setNewMacroActivity(updatingMacroActivity ?? initialMacroActivityState());
  }, [updatingMacroActivity, macroActivityModalOpen]);

  const handleNewMacroActivityNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    setNewMacroActivity({
      ...newMacroActivity,
      name: e.target.value,
    });
  };

  const closeModal = () => setMacroActivityModalOpen(false);

  const handleClose = () => closeModal();

  const handleSave = () => {
    if (updatingMacroActivity) {
      updateMacroActivity(newMacroActivity);
    } else {
      addMacroActivity(newMacroActivity);
    }
    closeModal();
  };

  const handleDelete = () => {
    if (!updatingMacroActivity) return;
    removeMacroActivity(updatingMacroActivity.id);
    closeModal();
  };

  return (
    <Modal isModalOpen={macroActivityModalOpen} handleClose={handleClose} width="500px" height="400px">
      <Typography id="modal-modal-title" variant="headline1" component="h2">
        {updatingMacroActivity ? 'Edit macro activity' : 'Add new macro activity'}
      </Typography>
      <div>
        <TextField
          label="Macro activity name"
          name="macroActivityName"
          value={newMacroActivity.name}
          onChange={handleNewMacroActivityNameChange}
          sx={{ width: '100%' }}
        />
      </div>
      <Button sx={{ marginTop: '20px' }} onClick={handleSave}>
        {updatingMacroActivity ? 'Update' : 'Add'}
      </Button>
      {updatingMacroActivity && (
        <Button onClick={handleDelete} variant="contained" color="warning">
          Delete
        </Button>
      )}
    </Modal>
  );
}
