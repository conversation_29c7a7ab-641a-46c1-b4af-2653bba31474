import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import type { RQBJsonLogic } from 'react-querybuilder';
import { CountryCode } from 'utils/marketConfigurations';
import {
  ParameterSpec,
  MacroActivity,
  MicroActivity,
  InstallationModel,
  IfElseExpression,
  JsonLogicArithmetic,
} from '../types/types';
import { createConditionals } from '../utils/multiplierUtils';

interface ModelState {
  model: InstallationModel | null;
  createNewModel: (basedOn?: InstallationModel) => void;
  setModel: (model: InstallationModel) => void;
  selectedMacroActivity: MacroActivity | null;
  setSelectedMacroActivity: (macroActivityId: MacroActivity['id'] | null) => void;
  addMacroActivity: (macroActivity: MacroActivity) => void;
  updateMacroActivity: (macroActivity: MacroActivity) => void;
  removeMacroActivity: (macroActivityId: string) => void;
  selectedMicroActivity: MicroActivity | null;
  setSelectedMicroActivity: (microActivityId: MicroActivity['id'] | null) => void;
  addMicroActivity: (microActivity: MicroActivity) => void;
  updateMicroActivity: (updatedMicroActivity: MicroActivity) => void;
  removeMicroActivity: (microActivityId: string) => void;
  addParameter: (input: ParameterSpec) => void;
  updateParameter: (input: ParameterSpec) => void;
  removeParameter: (inputId: string) => void;
  currentMultiplier: IfElseExpression<JsonLogicArithmetic> | null;
  setCurrentMultiplier: (multiplier: RQBJsonLogic | undefined) => void;
  updateCurrentMultiplier: (multiplier: IfElseExpression<JsonLogicArithmetic>) => void;
  copiedMultiplier: RQBJsonLogic | null;
  setCopiedMultiplier: (jsonLogic: RQBJsonLogic) => void;
}

const initialModel: InstallationModel = {
  id: '',
  name: '',
  comments: '',
  version: 0,
  countryCode: CountryCode.IT,
  parameters: [],
  macroActivities: [],
  resourceGroups: [],
};

export const selectMacroActivity = (state: ModelState, macroActivityId: MacroActivity['id']): MacroActivity | null =>
  state.model?.macroActivities.find((macroActivity) => macroActivity.id === macroActivityId) ?? null;

export const selectMicroActivity = (state: ModelState, microActivityId: MicroActivity['id']): MicroActivity | null => {
  if (state.selectedMacroActivity === null) return null;
  return (
    state.selectedMacroActivity.microActivities.find((microActivity) => microActivity.id === microActivityId) ?? null
  );
};

export const useModelStore = create<ModelState>()((set) => ({
  model: null,
  createNewModel: (basedOn = initialModel) => {
    const model: InstallationModel = {
      ...basedOn,
      id: uuidv4(),
      version: (basedOn?.version ?? 0) + 1,
      name: `${basedOn?.name} - cloned`,
      comments: '',
      createdAt: new Date().toISOString(),
      updatedAt: undefined,
      publishedAt: undefined,
    };
    set((state) => ({ ...state, model }));
  },
  setModel: (model: InstallationModel) => {
    set({ model });
  },
  selectedMacroActivity: null,
  setSelectedMacroActivity: (macroActivityId: MacroActivity['id'] | null) => {
    set((state) => {
      if (macroActivityId === state.selectedMacroActivity?.id) return state;
      return {
        selectedMacroActivity: macroActivityId === null ? null : selectMacroActivity(state, macroActivityId),
        selectedMicroActivity: null,
      };
    });
  },
  addMacroActivity: (macroActivity: MacroActivity) => {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          macroActivities: [...state.model.macroActivities, macroActivity],
        },
        selectedMacroActivity: macroActivity,
      };
    });
  },
  updateMacroActivity: (macroActivity: MacroActivity) => {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          macroActivities: state.model.macroActivities.map((m) => (m.id === macroActivity.id ? macroActivity : m)),
        },
        selectedMacroActivity: macroActivity,
      };
    });
  },
  removeMacroActivity(macroActivityId) {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          macroActivities: state.model.macroActivities.filter((m) => m.id !== macroActivityId),
        },
      };
    });
  },
  selectedMicroActivity: null,
  setSelectedMicroActivity: (microActivityId: string | null) => {
    set((state) => {
      if (microActivityId === state.selectedMicroActivity?.id) return state;
      return {
        selectedMicroActivity: microActivityId === null ? null : selectMicroActivity(state, microActivityId),
      };
    });
  },
  addMicroActivity: (microActivity: MicroActivity) => {
    set((state) => {
      if (!state.model || !state.selectedMacroActivity) return state;
      const updatedMacroActivity: MacroActivity = {
        ...state.selectedMacroActivity,
        microActivities: [...state.selectedMacroActivity.microActivities, microActivity],
      };
      const updatedModel: InstallationModel = {
        ...state.model,
        macroActivities: state.model.macroActivities.map((macroActivity) =>
          macroActivity.id === updatedMacroActivity.id ? updatedMacroActivity : macroActivity,
        ),
      };
      return {
        model: updatedModel,
        selectedMacroActivity: updatedMacroActivity,
        selectedMicroActivity: microActivity,
        currentMultiplier: createConditionals(microActivity.multiplier),
      };
    });
  },
  updateMicroActivity: (updatedMicroActivity: MicroActivity) => {
    set((state) => {
      if (!state.model || !state.selectedMacroActivity) return state;
      const updatedMacroActivity = {
        ...state.selectedMacroActivity,
        microActivities: state.selectedMacroActivity.microActivities.map((microActivity) =>
          microActivity.id === updatedMicroActivity.id ? updatedMicroActivity : microActivity,
        ),
      };
      const updatedModel = {
        ...state.model,
        macroActivities: state.model.macroActivities.map((macroActivity) =>
          macroActivity.id === state.selectedMacroActivity?.id ? updatedMacroActivity : macroActivity,
        ),
      };
      return {
        model: updatedModel,
        selectedMacroActivity: updatedMacroActivity,
        selectedMicroActivity: updatedMicroActivity,
      };
    });
  },
  removeMicroActivity(microActivityId) {
    set((state) => {
      if (!state.model || !state.selectedMacroActivity) return state;
      const updatedMacroActivity: MacroActivity = {
        ...state.selectedMacroActivity,
        microActivities: state.selectedMacroActivity.microActivities.filter(
          (microActivity) => microActivity.id !== microActivityId,
        ),
      };
      const updatedModel: InstallationModel = {
        ...state.model,
        macroActivities: state.model.macroActivities.map((macroActivity) =>
          macroActivity.id === state.selectedMacroActivity?.id ? updatedMacroActivity : macroActivity,
        ),
      };
      return {
        model: updatedModel,
        selectedMacroActivity: updatedMacroActivity,
        selectedMicroActivity: null,
        currentMultiplier: null,
      };
    });
  },
  addParameter(parameter) {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          parameters: [...state.model.parameters, parameter],
        },
      };
    });
  },
  updateParameter(updatedParameter) {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          parameters: state.model.parameters.map((parameter) =>
            parameter.id === updatedParameter.id ? updatedParameter : parameter,
          ),
        },
      };
    });
  },
  removeParameter(parameterId) {
    set((state) => {
      if (!state.model) return state;
      return {
        model: {
          ...state.model,
          parameters: state.model.parameters.filter((parameter) => parameter.id !== parameterId),
        },
      };
    });
  },
  currentMultiplier: null,
  setCurrentMultiplier: (multiplier: RQBJsonLogic | undefined) => {
    set({
      currentMultiplier: createConditionals(multiplier),
    });
  },
  updateCurrentMultiplier: (multiplier: IfElseExpression<JsonLogicArithmetic>) => {
    set({
      currentMultiplier: multiplier,
    });
  },
  copiedMultiplier: null,
  setCopiedMultiplier: (jsonLogic) => {
    set({ copiedMultiplier: jsonLogic });
  },
}));
