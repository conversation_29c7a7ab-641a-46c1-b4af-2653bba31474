import { create } from 'zustand';
import { MacroActivity, MicroActivity } from '../types/types';

interface ModalsState {
  macroActivityModalOpen: boolean;
  updatingMacroActivity: MacroActivity | null;
  setMacroActivityModalOpen: (isOpen: boolean, macroActivity?: MacroActivity) => void;
  microActivityModalOpen: boolean;
  setMicroActivityModalOpen: (isOpen: boolean, microActivity?: MicroActivity) => void;
  calculatorModalOpen: boolean;
  setCalculatorModalOpen: (isOpen: boolean) => void;
}

export const useModalsStore = create<ModalsState>((set) => ({
  macroActivityModalOpen: false,
  updatingMacroActivity: null,
  setMacroActivityModalOpen: (isOpen: boolean, macroActivity?: MacroActivity) => {
    set({
      macroActivityModalOpen: isOpen,
      updatingMacroActivity: macroActivity ?? null,
    });
  },
  microActivityModalOpen: false,
  setMicroActivityModalOpen: (isOpen) => {
    set({
      microActivityModalOpen: isOpen,
    });
  },
  calculatorModalOpen: false,
  setCalculatorModalOpen: (isOpen: boolean) => {
    set({ calculatorModalOpen: isOpen });
  },
}));
