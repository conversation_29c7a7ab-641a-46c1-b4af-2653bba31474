import { RQB<PERSON>sonLogic } from 'react-querybuilder';
import { v4 as uuidv4 } from 'uuid';
import { JsonLogicIf } from 'json-logic-js';
import { JsonLogicArithmetic, IfElseExpression } from '../types/types';

export const createConditionals = (multiplier: RQBJsonLogic | undefined): IfElseExpression<JsonLogicArithmetic> => {
  if (!multiplier) {
    return {
      conditionalCases: [],
      defaultCase: {
        '*': [1],
      },
    };
  }
  const parseIfExpression = (expr: RQBJsonLogic): IfElseExpression<JsonLogicArithmetic> => {
    if (typeof expr === 'object' && 'if' in expr) {
      if (!Array.isArray(expr.if)) throw new Error(`Expected if expression to be an array: ${JSON.stringify(expr)}`);
      if (expr.if.length < 3) throw new Error(`Insufficient branches in if expression: ${JSON.stringify(expr)}`);
      const defaultCase = expr.if[expr.if.length - 1];
      const conditionsPart = expr.if.slice(0, -1);
      const [conditionalCases] = conditionsPart.reduce(
        (
          [acc, condition]: [IfElseExpression<JsonLogicArithmetic>['conditionalCases'], RQBJsonLogic | null],
          conditionOrExpr,
        ): [IfElseExpression<JsonLogicArithmetic>['conditionalCases'], RQBJsonLogic | null] =>
          condition !== null
            ? [
                [
                  ...acc,
                  {
                    condition,
                    case: conditionOrExpr,
                    key: uuidv4(),
                  },
                ],
                null,
              ]
            : [acc, conditionOrExpr],
        [[], null],
      );
      return { defaultCase, conditionalCases };
    }
    if (
      (typeof expr === 'object' && Object.keys(expr).some((key) => ['*', '+', '/', '-'].includes(key))) ||
      typeof expr === 'number'
    ) {
      return {
        defaultCase: expr as JsonLogicArithmetic,
        conditionalCases: [],
      };
    }
    throw new Error(`Unhandled jsonlogic expression type in multiplier expression: ${JSON.stringify(expr)}`);
  };
  return parseIfExpression(multiplier);
};

export const addAnotherCase = ({ conditionals }: { conditionals: IfElseExpression<JsonLogicArithmetic> }) => {
  const updatedConditionals: IfElseExpression<JsonLogicArithmetic> = {
    ...conditionals,
    conditionalCases: [
      ...conditionals.conditionalCases,
      {
        condition: false,
        case: {
          '*': [1],
        },
        key: uuidv4(),
      },
    ],
  };

  return updatedConditionals;
};

export const updateSpecificQuery = ({
  conditionals,
  condition,
  idx,
}: {
  conditionals: IfElseExpression<JsonLogicArithmetic>;
  condition: RQBJsonLogic;
  idx: number;
}) => {
  const updatedConditionals = {
    ...conditionals,
    conditionalCases: conditionals.conditionalCases.map((c, i) => (i === idx ? { ...c, condition } : c)),
  };

  return updatedConditionals;
};

export const updateExpressionLogic = ({
  conditionals,
  expr,
  idx,
}: {
  conditionals: IfElseExpression<JsonLogicArithmetic>;
  expr: JsonLogicArithmetic;
  idx?: number;
}) => {
  const updatedConditionals =
    idx === undefined
      ? {
          ...conditionals,
          defaultCase: expr,
        }
      : {
          ...conditionals,
          conditionalCases: conditionals.conditionalCases.map((c, i) => (i === idx ? { ...c, case: expr } : c)),
        };
  return updatedConditionals;
};

export const removeCase = ({
  idx,
  conditionals,
}: {
  idx: number;
  conditionals: IfElseExpression<JsonLogicArithmetic>;
}) => {
  const updatedConditionals = {
    ...conditionals,
    conditionalCases: conditionals.conditionalCases.filter((_c, i) => i !== idx),
  };
  return updatedConditionals;
};

export const getUpdatedJSONLogic = (condition: IfElseExpression<JsonLogicArithmetic>): JsonLogicIf =>
  (condition.conditionalCases.length === 0
    ? condition.defaultCase
    : {
        if: [...condition.conditionalCases.flatMap((c) => [c.condition, c.case]), condition.defaultCase],
      }) as JsonLogicIf;
