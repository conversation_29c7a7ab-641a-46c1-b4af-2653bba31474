import { RQBJsonLogic } from 'react-querybuilder';
import { ParameterSpec } from '../types/types';

export const doesJsonLogicValueContainInputId = (
  value: RQBJsonLogic | number | string | boolean,
  inputId: ParameterSpec['id'],
): boolean => {
  // go through the array and check if any of the inputs match the inputId and if so, return true, if not, return false
  // if the array is a number, return false
  if (typeof value === 'boolean') return false;
  if (typeof value === 'number') return false;
  // if the array is a string and it matches the inputId, return true, if not, return false
  if (typeof value === 'string') {
    if (value === inputId) return true;
    return false;
  }
  if (Array.isArray(value)) {
    for (const element of value) {
      if (doesJsonLogicValueContainInputId(element, inputId)) return true;
    }
    return false;
  }
  if (typeof value === 'object') {
    const keys = Object.keys(value);
    for (const key of keys) {
      if (doesJsonLogicValueContainInputId(value[key as keyof RQBJsonLogic], inputId)) return true;
    }
    return false;
  }
  return false;
};
