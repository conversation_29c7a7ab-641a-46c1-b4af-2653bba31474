import { Box, Typography, Tooltip, IconButton } from '@mui/material';
import { InfoIcon } from '@ui/components/StandardIcons/InfoIcon';
import 'react-querybuilder/dist/query-builder.css';
import { ExpressionEditor } from './ExpressionEditor';
import type { JsonLogicArithmetic } from './types/types';

export interface MultiplierEditorProps {
  multiplier: JsonLogicArithmetic;
  onUpdateExpressionLogic: (v: JsonLogicArithmetic) => void;
  label?: string;
}

export function MultiplierEditor({ multiplier, label, onUpdateExpressionLogic }: MultiplierEditorProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
      }}
    >
      {label && (
        <Typography variant="h6">
          {label}
          <Tooltip title="Here you can choose to multiply by an input (the ones that are numbers) together with a single number or just the single number. If you want to disable the microactivity for this condition then just leave both fields blank.">
            <IconButton>
              <InfoIcon />
            </IconButton>
          </Tooltip>
        </Typography>
      )}
      <ExpressionEditor expressionLogic={multiplier} onUpdate={onUpdateExpressionLogic} />
    </Box>
  );
}
