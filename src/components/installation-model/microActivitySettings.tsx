import { Button } from '@ui/components/Button/Button';
import { NumericFormField } from '@ui/components/NumericFormField/NumericFormField';
import { TextField } from '@ui/components/TextField/TextField';
import { Box, Typography } from '@mui/material';
import DeleteIcon from '@ui/components/Icons/material/Delete';
import { ChangeEvent } from 'react';
import { useModelStore } from './stores/modelStore';

export default function MicroActivitySettings({ handleClose }: { handleClose: () => void }) {
  const { model, removeMicroActivity, selectedMicroActivity, updateMicroActivity } = useModelStore();
  const handleNameChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (!selectedMicroActivity) return;
    updateMicroActivity({ ...selectedMicroActivity, name: e.target.value ?? '' });
  };

  const handleMinutesChange = (value: number | null) => {
    if (!selectedMicroActivity) return;
    updateMicroActivity({ ...selectedMicroActivity, manHoursInMinutes: value });
  };

  const handleDelete = () => {
    if (!selectedMicroActivity) return;
    removeMicroActivity(selectedMicroActivity.id);
    handleClose();
  };

  if (!selectedMicroActivity) return null;
  const isPublished = !!(model?.publishedAt ?? false);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        marginTop: '20px',
        gap: '20px',
      }}
    >
      <Box>
        {isPublished ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0,
              justifyContent: 'flex-start',
              alignContent: 'center',
            }}
          >
            <Typography variant="body1Emphasis">Micro activity name</Typography>
            <Typography variant="body1">{selectedMicroActivity.name}</Typography>
          </Box>
        ) : (
          <TextField
            label="Micro activity name"
            name="microActivityName"
            value={selectedMicroActivity.name}
            onChange={handleNameChange}
            size="small"
            sx={{
              minWidth: '500px',
            }}
          />
        )}
      </Box>
      <Box>
        {isPublished ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 0,
              justifyContent: 'flex-start',
              alignContent: 'center',
            }}
          >
            <Typography variant="body1Emphasis">Number of minutes the activity takes</Typography>
            <Typography variant="body1">{selectedMicroActivity.manHoursInMinutes}</Typography>
          </Box>
        ) : (
          <NumericFormField
            label="Number of minutes the activity takes"
            name="microActivityMinutes"
            size="small"
            sx={{
              minWidth: '300px',
            }}
            canBeNegative={false}
            canBeDecimal={false}
            value={selectedMicroActivity.manHoursInMinutes}
            onChange={handleMinutesChange}
            helperText="Minutes must be a positive integer"
          />
        )}
      </Box>
      {!isPublished && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-end',
            gap: '10px',
            alignContent: 'center',
            marginTop: '20px',
            flex: '1 1 50%',
          }}
        >
          <Button color="error" onClick={handleDelete} startIcon={<DeleteIcon />}>
            Delete
          </Button>
        </Box>
      )}
    </Box>
  );
}
