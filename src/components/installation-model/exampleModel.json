{"countryCode": "IT", "name": "Example data", "version": 1, "id": "1486703c-edc9-4c13-91c5-97b6b396f721", "comments": "Example data hardcoded in frontend", "createdAt": "2017-01-01T00:00:00Z", "updatedAt": "2017-01-02T00:00:00Z", "publishedAt": null, "inputs": [{"id": "42bbd597-4a14-4ce6-a631-469abdbd4d25", "name": "Radiator count", "type": "number", "minValue": 0, "maxValue": 100, "defaultValue": 0, "helperText": "Number of radiators to install", "step": 1, "allowDecimals": false}, {"id": "dsf33597-4a44-4ce6-a631-3sdf3g655453", "name": "Complexity of indoor plumbing", "type": "select", "options": [{"name": "Easy", "value": "EASY"}, {"name": "Medium", "value": "MEDIUM"}, {"name": "Complex", "value": "COMPLEX"}], "helperText": "Easy- Heat Pump replacement - 1 zone - position of Indoor unit (e.g., if there is a dedicated area or if it is in the kitchen) \n Medium- Replacement of gas boiler - 1 zone - (e.g., if there is a dedicated area or if it is in the kitchen) \n Complex- Replacement of any unit - 2 zone - (e.g., if there is a dedicated area or if it is in the kitchen)", "defaultOption": {"name": "Easy", "value": "EASY"}}], "resourceGroups": [{"id": "e49e1e43-7907-4311-8ffd-bb7bd24c1f66", "name": "Installation", "resources": [{"id": "0c34b751-61aa-4a3e-b2da-c1b8d9b755e1", "name": "Installation Technician"}, {"id": "efae2335-0403-4b9e-8e80-df70a3d2853f", "name": "Installation Apprentice"}]}, {"id": "befed3dd-4f05-4353-9187-04a77511dd66", "name": "Electrician", "resources": [{"id": "e4a8d6bb-f75b-4ed3-ac7e-6c340b767062", "name": "Electrician"}]}], "macroActivities": [{"id": "4e1fbb48-2733-49d8-9bb4-5758d416c610", "name": "Install new radiator", "microActivities": [{"name": "Replacement of old brackets", "id": "7ee8010d-95db-4040-b323-fac90ca84d1f", "manHoursInMinutes": 10, "resourcesRequired": ["efae2335-0403-4b9e-8e80-df70a3d2853f"], "multiplier": {"if": [{"==": [{"var": "dsf33597-4a44-4ce6-a631-3sdf3g655453"}, "Easy"]}, {"*": [0.5, 1], "key": "95e80aad-cb08-4f31-9698-ed1acd7c72b0"}, {"==": [{"var": "dsf33597-4a44-4ce6-a631-3sdf3g655453"}, "Medium"]}, {"*": [1], "key": "2059a5ae-e929-4e1d-b533-acf42dfcdad1"}, {"==": [{"var": "dsf33597-4a44-4ce6-a631-3sdf3g655453"}, "Complex"]}, {"*": [2], "key": "3b716f22-0004-48ad-92fe-711772e3c3b5"}, {"*": [0], "key": "e2695b08-9d91-4126-8ba9-b7ce2690db94"}]}}]}]}