import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { formatQuery, parseJsonLogic, QueryBuilder } from 'react-querybuilder';
import type { RQBJsonLogic, RuleGroupType } from 'react-querybuilder';
import { Button } from '@ui/components/Button/Button';
import { CloseIcon } from '@ui/components/Icons/CloseIcon/CloseIcon';
import { IconButton } from '@mui/material';
import 'react-querybuilder/dist/query-builder.css';
import { useModelStore } from './stores/modelStore';
import useGetFields from './hooks/useGetFields';
import CustomValueEditor from './customConditionValueEditor';

export interface ConditionEditorProps {
  condition: RQBJsonLogic;
  onUpdate: (q: RQBJsonLogic) => void;
}

export function ConditionEditor({ condition, onUpdate }: ConditionEditorProps) {
  const { model } = useModelStore();
  const fields = useGetFields();
  const handleUpdate = (q: RuleGroupType) => {
    onUpdate(formatQuery(q, 'jsonlogic'));
  };

  const removeRuleAction = (props: any) => (
    <IconButton
      sx={{
        height: '30px',
        width: '30px',
      }}
      className={props.className}
      onClick={(e) => props.handleOnClick(e, 'group')}
    >
      <CloseIcon />
    </IconButton>
  );

  const removeGroupAction = (props: any) => (
    <IconButton
      sx={{
        height: '30px',
        width: '30px',
      }}
      className={props.className}
      onClick={(e) => props.handleOnClick(e, 'group')}
    >
      <CloseIcon />
    </IconButton>
  );

  const addRuleAction = (props: any) => (
    <Button
      type="button"
      className={props.className}
      title={props.title}
      onClick={(e) => props.handleOnClick(e, 'value')}
      size="small"
      sx={{
        minWidth: '120px',
      }}
    >
      ADD RULE
    </Button>
  );

  const addGroupAction = (props: any) => (
    <Button
      type="button"
      color="secondary"
      className={props.className}
      title={props.title}
      onClick={(e) => props.handleOnClick(e, 'group')}
      size="small"
      sx={{
        minWidth: '120px',
      }}
    >
      ADD GROUP
    </Button>
  );

  if (model?.publishedAt) {
    return (
      <div className="query-builder-if query-builder-disabled">
        <QueryBuilder fields={fields} defaultQuery={parseJsonLogic(condition)} showCombinatorsBetweenRules />
      </div>
    );
  }

  return (
    <div className="query-builder-if">
      <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
        <QueryBuilder
          controlElements={{
            removeRuleAction,
            removeGroupAction,
            addRuleAction,
            addGroupAction,
            valueEditor: CustomValueEditor,
          }}
          fields={fields}
          defaultQuery={parseJsonLogic(condition)}
          onQueryChange={handleUpdate}
          showCombinatorsBetweenRules
        />
      </QueryBuilderDnD>
    </div>
  );
}
