import * as React from 'react';
import { IconButton, ListItemButton, ListItemText, ListItem, Box } from '@mui/material';
import EditIcon from '@ui/components/Icons/material/Edit';
import { draggable, dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { MacroActivity, MicroActivity } from '../types/types';
import { Dot6OutlinedIcon } from '@ui/components/StandardIcons/Dot6OutlinedIcon';
import invariant from 'tiny-invariant';
import { useEffect, useState } from 'react';
import { brandYellow } from '@ui/theme/colors';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { createPortal } from 'react-dom';

export type DraggableActivityProps = {
  activity: MacroActivity | MicroActivity;
  index: number;
  selectedId: string | null;
  handleClick: (clickedMacroActivity: MacroActivity['id']) => void;
  type: 'macro' | 'micro';
  handleEditClick: (clickedActivityToEdit: MacroActivity['id'] | MicroActivity['id']) => void;
  isPublished: boolean;
  isDragDisabled: boolean;
  onDragEnd: ({
    source,
    destination,
  }: {
    source: { index: number; id: string };
    destination: { index: number; id: string };
  }) => void;
};
type State = { type: 'idle' } | { type: 'preview'; container: HTMLElement };

const DraggableActivity = ({
  activity,
  index,
  selectedId,
  handleClick,
  type,
  handleEditClick,
  isPublished,
  isDragDisabled,
  onDragEnd,
}: DraggableActivityProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [showDropIndicator, setShowDropIndicator] = useState(false);
  const ref = React.useRef<HTMLLIElement>(null);
  const [state, setState] = useState<State>({ type: 'idle' });
  useEffect(() => {
    const el = ref.current;
    invariant(el);
    return draggable({
      element: el,
      canDrag: () => !isDragDisabled,
      getInitialData: () => ({ type, id: activity.id, index }),
      onDragStart: () => setIsDragging(true),
      onDrop: () => setIsDragging(false),
      onGenerateDragPreview: ({ nativeSetDragImage }) => {
        setCustomNativeDragPreview({
          getOffset: () => ({ x: 10, y: 16 }),
          render: ({ container }) => {
            setState({ type: 'preview', container });
            return () => setState({ type: 'idle' });
          },
          nativeSetDragImage,
        });
      },
    });
  }, [activity.id, index, isDragDisabled, type, ref]);

  useEffect(() => {
    const el = ref.current;
    invariant(el);
    return dropTargetForElements({
      element: el,
      getData: () => ({ index, id: activity.id, type }),
      canDrop: () => !isDragDisabled,
      onDragEnter: ({ source }) => {
        if (source.data.type === type) {
          setShowDropIndicator(true);
        }
      },
      onDragLeave: () => {
        setShowDropIndicator(false);
      },
      onDrop: ({ source, location }) => {
        if (source.data.type !== type) {
          return;
        }
        const lastDropTarget = location.current.dropTargets[location.current.dropTargets.length - 1];
        if (!lastDropTarget || !('index' in lastDropTarget.data) || !('id' in lastDropTarget.data)) {
          return; // Exit if drop target data is invalid
        }

        const destination = {
          index: lastDropTarget.data.index as number,
          id: lastDropTarget.data.id as string,
        };
        const sourceData = {
          index: source.data.index as number,
          id: source.data.id as string,
        };
        setShowDropIndicator(false);
        onDragEnd({ source: sourceData, destination });
      },
    });
  }, [activity.id, index, isDragDisabled, ref, onDragEnd, type]);

  return (
    <>
      <ListItem
        ref={ref}
        style={{
          cursor: isDragDisabled ? 'default' : 'grab',
          backgroundColor: isDragging ? 'rgb(235,235,235)' : undefined,
          position: 'relative',
          opacity: isDragging ? 0.5 : 1,
        }}
        secondaryAction={
          isPublished ? null : (
            <IconButton
              edge="end"
              onClick={() => handleEditClick(activity.id)}
              sx={{
                cursor: isDragDisabled ? 'default' : 'grab',
              }}
            >
              <EditIcon />
            </IconButton>
          )
        }
      >
        {!isDragDisabled && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mr: 2, cursor: 'grab' }}>
            <Dot6OutlinedIcon />
          </Box>
        )}
        {type === 'macro' || isDragDisabled ? (
          <ListItemButton selected={selectedId === activity.id} onClick={() => handleClick(activity.id)}>
            <ListItemText primary={activity.name} />
          </ListItemButton>
        ) : (
          <div>
            <ListItemText primary={activity.name} />
          </div>
        )}
        {showDropIndicator && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              cursor: 'grab',
              height: '2px',
              width: '100%',
              backgroundColor: brandYellow[400],
            }}
          />
        )}
      </ListItem>
      {state.type === 'preview'
        ? createPortal(
            <ListItem
              style={{
                backgroundColor: 'rgb(235,235,235)',
                position: 'relative',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mr: 2, cursor: 'grab' }}>
                <Dot6OutlinedIcon />
              </Box>

              <div>
                <ListItemText primary={activity.name} />
              </div>
            </ListItem>,
            state.container,
          )
        : null}
    </>
  );
};

DraggableActivity.displayName = 'DraggableActivity';

export default React.memo(DraggableActivity);
