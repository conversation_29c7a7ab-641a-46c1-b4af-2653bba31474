import * as React from 'react';
import { List } from '@mui/material';
import { MacroActivity, MicroActivity } from '../types/types';
import DraggableActivity from './draggableActivity';

export type DraggableActivitiesListProps = {
  activities: MacroActivity[] | MicroActivity[];
  onDragEnd: ({
    source,
    destination,
  }: {
    source: { index: number; id: string };
    destination: { index: number; id: string };
  }) => void;
  selectedId: string | null;
  handleClick: (clickedMacroActivity: MacroActivity['id']) => void;
  handleEditClick: (clickedActivityToEdit: MacroActivity['id'] | MicroActivity['id']) => void;
  isPublished: boolean;
  type: 'macro' | 'micro';
};

function DraggableActivityList({
  activities,
  onDragEnd,
  selectedId,
  handleClick,
  handleEditClick,
  isPublished,
  type,
}: DraggableActivitiesListProps) {
  const listRef = React.useRef<HTMLUListElement>(null);

  return (
    <List dense ref={listRef} sx={{ width: '100%' }}>
      {activities.map((activity, index) => (
        <DraggableActivity
          key={activity.id}
          activity={activity}
          index={index}
          selectedId={selectedId}
          handleClick={handleClick}
          handleEditClick={handleEditClick}
          isPublished={isPublished}
          type={type}
          isDragDisabled={isPublished}
          onDragEnd={onDragEnd}
        />
      ))}
    </List>
  );
}

export default React.memo(DraggableActivityList);
