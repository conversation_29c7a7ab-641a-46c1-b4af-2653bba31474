import { Box, Typography, Stack } from '@mui/material';
import { useEffect } from 'react';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import ModelTestingContainer from './modelTestingContainer';
import ActivitySelector from './activitySelector';
import { InstallationModel } from './types/types';
import { useModelStore } from './stores/modelStore';
import ModelInfo from './modelInfo';
import ModelInputs from './modelInputs';
import MicroActivityModal from './modals/microActivityModal';
import MacroActivityModal from './modals/macroActivityModal';

export default function ModelInitializer({ installationModel }: { installationModel: InstallationModel }) {
  const { setModel, model } = useModelStore();
  useEffect(() => {
    setModel(installationModel);
  }, [installationModel, setModel]);

  if (!model) {
    return (
      <Stack>
        <Typography variant="headline2">Loading...</Typography>
        <HeatPump />
      </Stack>
    );
  }
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: '100%',
        height: '100%',
        padding: '40px',
      }}
    >
      <ModelInfo />
      <ModelInputs />
      <ActivitySelector />
      <ModelTestingContainer />
      <MicroActivityModal />
      <MacroActivityModal />
    </Box>
  );
}
