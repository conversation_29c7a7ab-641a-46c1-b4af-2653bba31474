import { Box, IconButton, Typography } from '@mui/material';
import CloseIcon from '@ui/components/Icons/material/Close';

export default function MicroActivityModalTitle({ handleClose }: { handleClose: () => void }) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
      }}
    >
      <Typography id="modal-modal-title" variant="headline1" component="h2">
        Edit micro activity
      </Typography>
      <IconButton aria-label="close" onClick={handleClose}>
        <CloseIcon />
      </IconButton>
    </Box>
  );
}
