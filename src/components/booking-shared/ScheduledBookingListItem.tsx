import { <PERSON>, Chip, Link, Stack, Typography } from '@mui/material';
import Public from '@ui/components/Icons/material/Public';
import { FormattedMessage, useIntl } from 'react-intl';
import { Button } from '@ui/components/Button/Button';
import { Card } from '@ui/components/Card/Card';
import { Popup } from '@ui/components/Popup/Popup';
import { useState } from 'react';
import { MessageKey } from 'messageType';
import { api } from '../../utils/api';
import Notes from 'components/booking-shared/Notes';
import TimeBooker from './TimeBooker';
import { JobStatus, Skill } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import { SurveyBooking } from './ScheduledBookings';
import ScheduledTimeText from 'components/booking-shared/ScheduledTimeText';

function getJobLabel(bookingType: 'sales' | 'technicalSurvey' | 'videoSales') {
  switch (bookingType) {
    case 'sales':
      return <FormattedMessage id="bookingTool.label.salesSurvey" defaultMessage="Sales Survey" />;
    case 'technicalSurvey':
      return <FormattedMessage id="bookingTool.label.technicalSurvey" defaultMessage="Technical Survey" />;
    case 'videoSales':
      return <FormattedMessage id="bookingTool.label.videoSalesMeeting" defaultMessage="Video Sales Meeting" />;
    default:
      return '';
  }
}
function getSkillLabel(skill: Skill) {
  switch (skill) {
    case Skill.SKILL_HEAT_PUMP:
      return 'bookingTool.skillsChip.HEAT_PUMP';
    case Skill.SKILL_BATTERY:
      return 'bookingTool.skillsChip.BATTERY';
    case Skill.SKILL_PHOTOVOLTAIC:
      return 'bookingTool.skillsChip.PHOTOVOLTAIC';
    case Skill.SKILL_SOLAR_THERMAL:
      return 'bookingTool.skillsChip.SOLAR_THERMAL';
  }
}

function getJobStatusLabel(status: JobStatus) {
  switch (status) {
    case JobStatus.JOB_STATUS_FINISHED:
      return 'bookingTool.surveyStatus.FINISHED';
    case JobStatus.JOB_STATUS_CANCELLED:
      return 'bookingTool.surveyStatus.CANCELLED';
    case JobStatus.JOB_STATUS_IN_PROGRESS:
      return 'bookingTool.surveyStatus.IN_PROGRESS';
    case JobStatus.JOB_STATUS_READY:
      return 'bookingTool.surveyStatus.READY';
    case JobStatus.JOB_STATUS_SCHEDULED:
      return 'bookingTool.surveyStatus.SCHEDULED';
    case JobStatus.JOB_STATUS_NOT_SCHEDULED:
      return 'bookingTool.surveyStatus.NOT_SCHEDULED';
    default:
      return 'bookingTool.surveyStatus.UNKNOWN';
  }
}

function statusColor(status: JobStatus) {
  switch (status) {
    case JobStatus.JOB_STATUS_FINISHED:
      return '#358267';
    case JobStatus.JOB_STATUS_CANCELLED:
      return '#8F2D2D';
    case JobStatus.JOB_STATUS_IN_PROGRESS:
    case JobStatus.JOB_STATUS_READY:
      return '#FFAF51';
    default:
      return '#D9D9D9';
  }
}

interface ScheduledBookingsListItemProps {
  surveyBooking: SurveyBooking;
  installationGroundworkId: string;
  setManuallyShowScheduler: (show: boolean) => void;
  bookingType: 'sales' | 'technicalSurvey' | 'videoSales';
}

export default function ScheduledBookingsListItem({
  surveyBooking: {
    surveyId,
    start,
    end,
    timeConstraint,
    status,
    notes,
    scheduled,
    roles: requiredRoles,
    assignedResources,
    requiredSkills,
    webLinkToJob,
    durationMinutes,
  },
  installationGroundworkId,
  setManuallyShowScheduler,
  bookingType,
}: ScheduledBookingsListItemProps) {
  const { formatMessage } = useIntl();

  const [showTimeBooker, setShowTimeBooker] = useState(false);
  const [isPopupOpen, setOpendPopup] = useState(false);
  const { refetch: refetchSurveys } = api.Survey.listSurveyBookings.useQuery({ installationGroundworkId });

  const { mutateAsync: unScheduleSurvey } = api.Survey.unScheduleSurvey.useMutation({
    onSuccess() {
      setShowTimeBooker(false);
      setManuallyShowScheduler(false);
      refetchSurveys();
    },
  });

  const { mutate: updateSurvey } = api.Survey.updateSurvey.useMutation();

  const handleScheduleSurvey = () => {
    setShowTimeBooker(true);
  };
  const handleHideTimeBooker = () => {
    setShowTimeBooker(false);
  };

  return (
    <Card
      sx={{
        overflow: 'visible',
        background: '#fff',
        borderRadius: '16px',
      }}
    >
      <Stack direction="row" sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
        <Stack direction="row" sx={{ alignItems: 'center', gap: '4px' }}>
          <div
            style={{
              height: '12px',
              width: '12px',
              borderRadius: '50%',
              backgroundColor: statusColor(status),
            }}
          />
          <Typography variant="body2">{formatMessage({ id: getJobStatusLabel(status) })}</Typography>
        </Stack>
        {webLinkToJob && (
          <Link href={webLinkToJob} target="_blank">
            <Chip
              label="Skedulo"
              icon={<Public sx={{ height: '18px' }} />}
              clickable
              sx={{
                borderRadius: '8px',
                fontSize: '12px',
                height: '26px',
                fontFamily: 'AiraText',
                '.MuiChip-label': {
                  padding: '4px 8px 4px 4px',
                  lineHeight: '18px',
                },
              }}
            />
          </Link>
        )}
      </Stack>
      <Typography variant="headline4" sx={{ fontSize: '24px' }}>
        {getJobLabel(bookingType)}
      </Typography>
      <ScheduledTimeText
        scheduled={scheduled}
        start={start!}
        end={end!}
        status={status}
        timeConstraint={timeConstraint}
      />
      {scheduled && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: '8px',
          }}
        >
          <Typography variant="body1">
            {formatMessage({ id: 'bookingTool.body.surveyorName' })}
            {(assignedResources ?? [])
              .map(
                ({ name }: { name?: string }) =>
                  name ?? formatMessage({ id: 'bookingTool.deactivatedUser', defaultMessage: 'Deactivated user' }),
              )
              .join(', ')}
          </Typography>
        </Box>
      )}
      {scheduled && [JobStatus.JOB_STATUS_SCHEDULED, JobStatus.JOB_STATUS_READY].includes(status) && (
        <Button onClick={() => setOpendPopup(true)} sx={{ width: '149px', marginY: '16px' }} size="small">
          {formatMessage({ id: 'bookingTool.button.unschedule' as MessageKey })}
        </Button>
      )}
      {requiredSkills && requiredSkills.length > 0 && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            marginTop: '8px',
            marginBottom: '8px',
          }}
        >
          {requiredSkills.map((skill) => (
            <Chip
              key={skill}
              label={formatMessage({ id: getSkillLabel(skill) })}
              sx={{
                marginTop: '8px',
                marginRight: '5px',
                borderRadius: '8px',
                fontSize: '12px',
                height: '26px',
                fontFamily: 'AiraText',
                '.MuiChip-label': {
                  padding: '4px 8px',
                  lineHeight: '18px',
                },
              }}
            />
          ))}
        </Box>
      )}
      <Notes
        notes={notes}
        updateSurveyNotes={(notes) => {
          updateSurvey({ surveyId, notes, skills: requiredSkills, roles: requiredRoles, durationMinutes });
        }}
      />
      {!scheduled && !showTimeBooker && (
        <Button
          variant="outlined"
          sx={{
            marginTop: '16px',
          }}
          onClick={handleScheduleSurvey}
        >
          {formatMessage({ id: 'bookingTool.button.scheduleSurvey' })}
        </Button>
      )}

      {!scheduled && showTimeBooker && (
        <>
          <Button
            variant="outlined"
            sx={{
              marginTop: '16px',
            }}
            onClick={handleHideTimeBooker}
          >
            {formatMessage({ id: 'bookingTool.button.hideScheduler' })}
          </Button>
          <Box
            sx={{
              marginTop: '16px',
            }}
          >
            <TimeBooker
              notes={notes}
              installationGroundworkId={installationGroundworkId}
              bookingType={bookingType}
              setManuallyShowScheduler={setManuallyShowScheduler}
              surveyId={surveyId}
            />
          </Box>
        </>
      )}
      <Popup
        description={formatMessage({ id: 'bookingTool.popup.unscheduleDescription' as MessageKey })}
        title={formatMessage({ id: 'bookingTool.popup.unscheduleHeader' as MessageKey })}
        handleClose={() => setOpendPopup(false)}
        open={isPopupOpen}
        agreeButtonText={formatMessage({ id: 'common.label.confirm' })}
        disagreeButtonText={formatMessage({ id: 'common.label.cancel' })}
        handleAgree={() => {
          setOpendPopup(false);
          unScheduleSurvey(surveyId);
        }}
      />
    </Card>
  );
}
