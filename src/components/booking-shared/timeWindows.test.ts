import { calculateTimeWindow, isInWindow } from './timeWindows';

describe('timeWindows', () => {
  describe('calculateTimeWindow', () => {
    it.each([
      ['Europe/London', new Date('2024-01-01T09:00:00.000Z'), new Date('2024-01-01T13:00:00.000Z')],
      ['Europe/Rome', new Date('2024-01-01T08:00:00.000Z'), new Date('2024-01-01T12:00:00.000Z')],
      ['Europe/Helsinki', new Date('2024-01-01T07:00:00.000Z'), new Date('2024-01-01T11:00:00.000Z')],
    ])('should calculate winter morning window in %p time zone', (timeZone, expectedStart, expectedEnd) => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      const morning = { start: 9, end: 13 };
      const result = calculateTimeWindow(date, morning, timeZone);
      expect(result.start).toEqual(expectedStart);
      expect(result.end).toEqual(expectedEnd);
    });

    it('should align to the start of an hour', () => {
      const relativeToDate = new Date('2024-01-01T01:23:45.678Z');
      const afternoon = { start: 13, end: 17 };
      const result = calculateTimeWindow(relativeToDate, afternoon, 'Europe/London');
      expect(result.start).toEqual(new Date('2024-01-01T13:00:00.000Z'));
      expect(result.end).toEqual(new Date('2024-01-01T17:00:00.000Z'));
    });

    it.each([
      ['Europe/London', new Date('2024-07-01T08:00:00.000Z'), new Date('2024-07-01T12:00:00.000Z')],
      ['Europe/Rome', new Date('2024-07-01T07:00:00.000Z'), new Date('2024-07-01T11:00:00.000Z')],
      ['Europe/Helsinki', new Date('2024-07-01T06:00:00.000Z'), new Date('2024-07-01T10:00:00.000Z')],
    ])('should calculate summer morning window in %p time zone', (timeZone, expectedStart, expectedEnd) => {
      const date = new Date('2024-07-01T00:00:00.000Z');
      const morning = { start: 9, end: 13 };
      const result = calculateTimeWindow(date, morning, timeZone);
      expect(result.start).toEqual(expectedStart);
      expect(result.end).toEqual(expectedEnd);
    });
  });

  describe('isInWindow', () => {
    it.each([
      // at the start of the window
      [new Date('2024-01-01T09:00:00.000Z'), new Date('2024-01-01T11:00:00.000Z')],
      // at the end of the window
      [new Date('2024-01-01T12:00:00.000Z'), new Date('2024-01-01T13:00:00.000Z')],
      // inside the window
      [new Date('2024-01-01T11:00:00.000Z'), new Date('2024-01-01T12:00:00.000Z')],
      // matching the window exactly
      [new Date('2024-01-01T09:00:00.000Z'), new Date('2024-01-01T13:00:00.000Z')],
      // at the start of the window (with daylight saving during summer)
      [new Date('2024-07-01T08:00:00.000Z'), new Date('2024-07-01T10:00:00.000Z')],
    ])('should check if time slot [%p, %p] is in the morning in London', (start, end) => {
      const morning = { start: 9, end: 13 };
      const result = isInWindow({ start, end }, morning, 'Europe/London');
      expect(result).toBe(true);
    });

    it('should support another time zone', () => {
      const morning = { start: 9, end: 13 };
      const result = isInWindow(
        {
          start: new Date('2024-07-01T07:00:00.000Z'),
          end: new Date('2024-07-01T09:00:00.000Z'),
        },
        morning,
        'Europe/Rome',
      );
      expect(result).toBe(true);
    });

    it.each([
      // overlapping the start
      [new Date('2024-01-01T08:00:00.000Z'), new Date('2024-01-01T10:00:00.000Z')],
      // overlapping the end
      [new Date('2024-01-01T10:00:00.000Z'), new Date('2024-01-01T14:00:00.000Z')],
      // before the start
      [new Date('2024-01-01T08:00:00.000Z'), new Date('2024-01-01T09:00:00.000Z')],
      // after the end
      [new Date('2024-01-01T13:00:00.000Z'), new Date('2024-01-01T14:00:00.000Z')],
    ])('should check if time slot [%p, %p] is not in the morning in London', (start, end) => {
      const morning = { start: 9, end: 13 };
      const result = isInWindow({ start, end }, morning, 'Europe/London');
      expect(result).toBe(false);
    });
  });
});
