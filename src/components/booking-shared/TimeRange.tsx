import { useRegionContext } from './RegionContext';
import { formatDate, formatTime, formatLongDate } from './timeUtils';

type TimeRangeVariant =
  | 'normal' // eg. "2024-01-01, 12:00 - 13:00" (default)
  | 'long'; // eg. "Monday January 1st, 12:00 - 13:00"

export default function TimeRange({ start, end, variant }: { start: Date; end: Date; variant?: TimeRangeVariant }) {
  const { timeZone } = useRegionContext();

  if (variant === 'long') {
    return (
      <>
        {formatLongDate(start, timeZone)}
        {', '}
        {formatTime(start, timeZone)}
        {' - '}
        {formatTime(end, timeZone)}
      </>
    );
  }

  // variant is 'normal'
  return (
    <>
      {formatDate(start, timeZone)}
      {', '}
      {formatTime(start, timeZone)}
      {' - '}
      {formatTime(end, timeZone)}
    </>
  );
}
