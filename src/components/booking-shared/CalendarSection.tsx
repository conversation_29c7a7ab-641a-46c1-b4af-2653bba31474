import { PickersDay, PickersDayProps } from '@mui/x-date-pickers/PickersDay';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';
import { Box, SxProps, Theme } from '@mui/material';
import { brandYellow, green, grey, red } from '@ui/theme/colors';
import { formatDate } from './timeUtils';
import type { AvailabilitiesForDate, AvailabilitiesGroupedByDate } from './types';

const sharedAvailabilityIndicatorStyle = {
  zIndex: 1,
  content: '""',
  left: '2px',
  bottom: '2px',
  position: 'absolute',
  width: '100%',
  height: '100%',
  pointerEvents: 'none',
};

export function getAvailabilityColor(
  availabilities: AvailabilitiesForDate['availabilities'],
  outsideCurrentMonth: boolean,
  trafficLightEnabled: boolean = false,
) {
  if (outsideCurrentMonth || !availabilities.length) {
    return null;
  }

  if (!trafficLightEnabled) {
    return green[400];
  }

  const hasTravelTime = (minutes: number) =>
    availabilities.some((availability) => (availability.bestTravelTimeBounds?.seconds ?? 0) / 60 === minutes);

  switch (true) {
    case hasTravelTime(40):
      return green[400];
    case hasTravelTime(70):
      return brandYellow[400];
    case hasTravelTime(90):
      return red[500];
    default:
      return null;
  }
}

function SlotAvailabilityIndicator({ children, color }: { children: React.ReactNode; color: string | null }) {
  if (!color) {
    return children;
  }
  return (
    <Box
      sx={{
        position: 'relative',
        '::before': {
          ...sharedAvailabilityIndicatorStyle,
          display: color ? 'block' : 'none',
          background: color,
          // horizontal pill shape
          clipPath:
            'path("M 18 25.5 H 29 c 1.1046 0 2 0.8954 2 2 c 0 1.1046 -0.8954 2 -2 2 H 7 c -1.1046 0 -2 -0.8954 -2 -2 c 0 -1.1046 0.8954 -2 2 -2 Z")',
        },
      }}
    >
      {children}
    </Box>
  );
}

function WindowAvailabilityIndicator({
  children,
  morningBadge,
  afternoonBadge,
}: {
  children: React.ReactNode;
  morningBadge: boolean;
  afternoonBadge: boolean;
}) {
  if (!morningBadge && !afternoonBadge) {
    return children;
  }
  return (
    <Box
      sx={{
        position: 'relative',
        '::before': {
          ...sharedAvailabilityIndicatorStyle,
          background: morningBadge ? green[500] : brandYellow[500],
          // horizontal pill shape (left side only)
          clipPath: 'path("M5 27.6C5 26.4954 5.89543 25.6 7 25.6H17V29.6H7C5.89543 29.6 5 28.7045 5 27.6Z")',
        },
        '::after': {
          ...sharedAvailabilityIndicatorStyle,
          background: afternoonBadge ? green[500] : brandYellow[500],
          // horizontal pill shape (right side only)
          clipPath: 'path("M19 25.6H29C30.1046 25.6 31 26.4954 31 27.6C31 28.7045 30.1046 29.6 29 29.6H19V25.6Z")',
        },
      }}
    >
      {children}
    </Box>
  );
}

export function CalendarDay(
  props: PickersDayProps<Date> & {
    availabilitiesGroupedByDate?: AvailabilitiesGroupedByDate;
    timeWindowsEnabled?: boolean;
    trafficLightEnabled?: boolean;
  },
) {
  const {
    availabilitiesGroupedByDate,
    day,
    outsideCurrentMonth,
    selected,
    timeWindowsEnabled,
    trafficLightEnabled = false,
    ...other
  } = props;

  // The "day" prop is a Date object representing the first instant of the day in the browser's default time zone
  // so we should use the same time zone when converting it to a string.
  const dayString = formatDate(day, Intl.DateTimeFormat().resolvedOptions().timeZone);

  const availabilities = availabilitiesGroupedByDate?.get(dayString);
  const availabilitiesOnDay = availabilities?.availabilities ?? [];
  const availabilitiesOnMorning = availabilities?.morningAvailabilities ?? [];
  const availabilitiesOnAfternoon = availabilities?.afternoonAvailabilities ?? [];
  const morningBadge = !outsideCurrentMonth && availabilitiesOnMorning.length > 0;
  const afternoonBadge = !outsideCurrentMonth && availabilitiesOnAfternoon.length > 0;

  const pickersDay = (
    <PickersDay
      {...other}
      outsideCurrentMonth={outsideCurrentMonth}
      day={day}
      disableRipple
      selected={selected}
      sx={
        {
          // Disable special hover / focus effects since the colors conflict with the
          // availability indicators.
          '&:hover': selected
            ? {
                backgroundColor: `${grey[900]} !important`,
              }
            : undefined,
          '&:focus': selected
            ? {
                backgroundColor: `${grey[900]} !important`,
                color: '#ffffff !important',
              }
            : {
                backgroundColor: `${grey[300]} !important`,
                color: `${grey[900]} !important`,
              },
        } as SxProps<Theme>
      }
    />
  );

  return timeWindowsEnabled ? (
    <WindowAvailabilityIndicator {...{ morningBadge, afternoonBadge }}>{pickersDay}</WindowAvailabilityIndicator>
  ) : (
    <SlotAvailabilityIndicator
      {...{ color: getAvailabilityColor(availabilitiesOnDay, outsideCurrentMonth, trafficLightEnabled) }}
    >
      {pickersDay}
    </SlotAvailabilityIndicator>
  );
}

interface CalendarSectionProps {
  isLoading: boolean;
  minDate: Date;
  maxDate: Date;
  availabilitiesGroupedByDate: AvailabilitiesGroupedByDate | null;
  selectedDate: Date | null;
  handleDateChange: (newValue: Date | null) => void;
  handleMonthChange: (monthStart: Date) => void;
  timeWindowsEnabled: boolean;
  trafficLightEnabled?: boolean;
}

export default function CalendarSection({
  isLoading,
  minDate,
  maxDate,
  availabilitiesGroupedByDate,
  selectedDate,
  timeWindowsEnabled,
  trafficLightEnabled = false,
  handleDateChange,
  handleMonthChange,
}: CalendarSectionProps) {
  return (
    <DateCalendar
      loading={isLoading}
      minDate={minDate}
      maxDate={maxDate}
      slots={{ day: CalendarDay }}
      slotProps={
        {
          day: { availabilitiesGroupedByDate, timeWindowsEnabled, trafficLightEnabled },
        } as any
      }
      value={selectedDate}
      onChange={handleDateChange}
      onMonthChange={handleMonthChange}
      views={['day']}
    />
  );
}
