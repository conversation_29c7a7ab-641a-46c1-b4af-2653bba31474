import {
  <PERSON>ert,
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Radio,
  Stack,
  Typography,
  TextField,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import React, { ChangeEvent, useCallback, useEffect, useState } from 'react';
import { startOfTomorrow, addMonths, endOfMonth, startOfMonth, isWithinInterval, max, min, endOfDay } from 'date-fns';
import { FormattedMessage, useIntl } from 'react-intl';
import nextHalfHourBoundary from 'utils/dates/nextHalfHourBoundary';
import { Autocomplete } from '@ui/components/Autocomplete/Autocomplete';
import type { OnChangeEvent } from '@ui/components/Autocomplete/Autocomplete';
import { NumericFormField } from '@ui/components/NumericFormField/NumericFormField';
import { groupBy } from 'utils/groupBy';
import { mapValues } from 'utils/mapValues';
import { MessageKey } from 'messageType';
import { GridExpandMoreIcon } from '@mui/x-data-grid';
import useDebounceEffect from '@ui/components/AddressInputLoqate/useDebounceEffect';
import { getCountryFromIso3166 } from 'utils/helpers';
import { formatDate } from './timeUtils';
import { SummarySection } from './SummarySection';
import { SelectSpecificTimeSection } from '../booking-tool/SelectSpecificTimeSection';
import CalendarSection from './CalendarSection';
import { api } from '../../utils/api';
import { isInWindow, timeWindows } from './timeWindows';
import { SelectTimeWindowSection } from '../booking-tool/SelectTimeWindowSection';
import { useRegionContext } from './RegionContext';
import { CountryCode, getCountryCodeFromCountry } from '../../utils/marketConfigurations';
import { Skill } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import {
  CheckAvailabilityResponse,
  JobType,
  JobType_MeetingType,
  SurveyRole,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { AvailabilitiesForDate } from 'components/booking-shared/types';
import Resources from 'components/booking-shared/Resources';

const skillSet: { key: Skill; translationKey: MessageKey }[] = [
  { key: Skill.SKILL_PHOTOVOLTAIC, translationKey: 'bookingTool.checkbox.photovoltaic' },
  { key: Skill.SKILL_BATTERY, translationKey: 'bookingTool.checkbox.battery' },
  { key: Skill.SKILL_SOLAR_THERMAL, translationKey: 'bookingTool.checkbox.solarThermal' },
];

enum SelectedTab {
  SPECIFIC_TIME = 0,
  TIME_WINDOW = 1,
  SET_TIME_LATER = 2,
}

const SURVEY_DURATIONS = [
  {
    key: 1,
    value: 60,
    enabled: (tab: SelectedTab, bookingType: 'sales' | 'technicalSurvey' | 'videoSales') =>
      tab !== SelectedTab.TIME_WINDOW && bookingType !== 'videoSales',
  },
  { key: 2, value: 90, enabled: () => true },
  { key: 3, value: 120, enabled: (tab: SelectedTab) => tab !== SelectedTab.TIME_WINDOW },
  { key: 4, value: 180, enabled: (tab: SelectedTab) => tab !== SelectedTab.TIME_WINDOW },
] as const;

type DurationMinutes = (typeof SURVEY_DURATIONS)[number]['value'];

const getDefaultDurationMinutes = (tab: SelectedTab): DurationMinutes => (tab === SelectedTab.TIME_WINDOW ? 90 : 120);

const surveyorTypes: { key: string; translationKey: MessageKey; value: Roles }[] = [
  { key: 'sales surveyor', translationKey: 'bookingTool.radio.salesSurveyor', value: [SurveyRole.SURVEY_ROLE_SALES] },
  {
    key: 'technical surveyor',
    translationKey: 'bookingTool.radio.technicalSurveyor',
    value: [SurveyRole.SURVEY_ROLE_TECHNICAL],
  },
];

type Roles = (SurveyRole.SURVEY_ROLE_SALES | SurveyRole.SURVEY_ROLE_TECHNICAL)[];

function DurationControl({
  selectedTab,
  durationMinutes,
  setDurationMinutes,
  bookingType,
}: {
  selectedTab: SelectedTab;
  durationMinutes: DurationMinutes;
  setDurationMinutes: (durationMinutes: DurationMinutes) => void;
  bookingType: 'sales' | 'technicalSurvey' | 'videoSales';
}) {
  const intl = useIntl();

  return (
    <FormGroup>
      <Typography variant="body1">{intl.formatMessage({ id: 'bookingTool.label.surveyDuration' })}</Typography>

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        {SURVEY_DURATIONS.filter(({ enabled }) => enabled(selectedTab, bookingType)).map((surveyDuration) => (
          <FormControlLabel
            key={surveyDuration.key}
            label={
              <FormattedMessage
                id="bookingTool.radio.duration"
                values={{ hours: Math.floor(surveyDuration.value / 60), minutes: surveyDuration.value % 60 }}
              />
            }
            checked={surveyDuration?.value === durationMinutes}
            control={<Radio />}
            onChange={() => setDurationMinutes(surveyDuration.value)}
          />
        ))}
      </Box>
    </FormGroup>
  );
}

export default function TimeBooker({
  scheduleOnly,
  installationGroundworkId,
  notes: initialNotes,
  bookingType,
  setManuallyShowScheduler,
  surveyId,
}: {
  scheduleOnly?: boolean;
  installationGroundworkId: string;
  notes?: string;
  bookingType: 'sales' | 'technicalSurvey' | 'videoSales';
  setManuallyShowScheduler: (show: boolean) => void;
  surveyId?: string;
}): React.JSX.Element {
  const [selectedTab, setSelectedTab] = useState<SelectedTab>(SelectedTab.SPECIFIC_TIME);
  const minDate = nextHalfHourBoundary(new Date());
  const maxDate = addMonths(minDate, 3);
  const [from, setFrom] = useState(minDate);
  const [to, setTo] = useState(endOfMonth(minDate));
  const [selectedDate, setSelectedDate] = useState<Date>(minDate);
  const [selectedTime, setSelectedTime] = useState<{ start: Date; end: Date } | null>(null);
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(null);
  const [selectedResourceEmail, setSelectedResourceEmail] = useState<string | null>(null);
  const [selectedTimeWindow, setSelectedTimeWindow] = useState<'morning' | 'afternoon' | null>(null);
  const [durationMinutes, setDurationMinutes] = useState<DurationMinutes>(getDefaultDurationMinutes(selectedTab));
  const selectedRoles =
    bookingType === 'sales' || bookingType === 'videoSales' ? surveyorTypes[0]!.value : surveyorTypes[1]!.value;
  const [selectedSkills, setSelectedSkills] = useState<Skill[]>([]);
  const [notes, setNotes] = useState<string>(initialNotes ?? '');
  const [assignSpecificResource, setAssignSpecificResource] = useState(false);
  const [specificResource, setSpecificResource] = useState<string | null>(null);
  const intl = useIntl();
  const { data: selfData } = api.AiraBackend.whoAmI.useQuery();
  const selfEmail = selfData?.email?.toLowerCase();
  const schedulingEnabled = selectedTab === SelectedTab.SPECIFIC_TIME || selectedTab === SelectedTab.TIME_WINDOW;
  const emailsToFilterFor = specificResource && schedulingEnabled ? [specificResource] : [];

  const { id: regionId, iso3166, timeZone } = useRegionContext();
  const country = getCountryFromIso3166(iso3166);
  const countryCode = getCountryCodeFromCountry(country);
  const getDefaultJobsPerDay = (code: CountryCode) => {
    switch (code) {
      case CountryCode.DE:
        return null;
      case CountryCode.GB:
        return 3;
      case CountryCode.IT:
        return null;
      default:
        return null;
    }
  };
  const defaultJobsPerDay = getDefaultJobsPerDay(countryCode);

  const [jobsPerDay, setJobsPerDay] = useState<number | null>(defaultJobsPerDay);
  const [debouncedJobsPerDay, setDebouncedJobsPerDay] = useState<number | null>(defaultJobsPerDay);

  const mapSurveyAvailabilityData = useCallback(
    (surveyAvailabilityData: CheckAvailabilityResponse) => {
      const timeSlots = surveyAvailabilityData.availableTimeSlots.map(
        ({ startsAt, endsAt, bestTravelTimeBounds, surveyorAvailabilities }) => ({
          start: startsAt!,
          end: endsAt!,
          bestTravelTimeBounds:
            bestTravelTimeBounds?.bound?.$case === 'limit' ? bestTravelTimeBounds.bound.limit.limit! : undefined,
          surveyorAvailabilities,
        }),
      );
      const groupedByDate = groupBy(timeSlots, ({ start }) => formatDate(start, timeZone));
      return mapValues(
        groupedByDate,
        (availabilitiesOnDay) =>
          availabilitiesOnDay && {
            availabilities: availabilitiesOnDay,
            morningAvailabilities: availabilitiesOnDay.filter((slot) =>
              isInWindow(slot, timeWindows.morning, timeZone),
            ),
            afternoonAvailabilities: availabilitiesOnDay.filter((slot) =>
              isInWindow(slot, timeWindows.afternoon, timeZone),
            ),
            resources: surveyAvailabilityData.availableTimeSlots.map(
              ({ surveyorAvailabilities }) => surveyorAvailabilities,
            ),
          },
      );
    },
    [timeZone],
  );

  const jobType = {
    type: {
      $case:
        bookingType === 'sales' || bookingType === 'videoSales'
          ? 'salesMeeting'
          : ('technicalSurveyMeeting' as 'salesMeeting' | 'technicalSurveyMeeting'),
      salesMeeting:
        bookingType === 'sales' || bookingType === 'videoSales'
          ? {
              meetingType:
                bookingType === 'sales'
                  ? JobType_MeetingType.MEETING_TYPE_PHYSICAL_MEETING
                  : JobType_MeetingType.MEETING_TYPE_REMOTE_MEETING,
            }
          : undefined,
      technicalSurveyMeeting: bookingType === 'technicalSurvey' ? {} : undefined,
    },
  } as JobType;

  const { data: availabilitiesGroupedByDate, isLoading } = api.Survey.getSurveyAvailability.useQuery(
    {
      installationGroundworkId,
      from,
      to,
      skills: selectedSkills,
      roles: selectedRoles,
      duration: durationMinutes,
      resourceEmails: emailsToFilterFor,
      jobsPerDay: debouncedJobsPerDay,
      jobType,
    },
    {
      select: mapSurveyAvailabilityData,
    },
  );

  useDebounceEffect(
    () => {
      setDebouncedJobsPerDay(jobsPerDay);
    },
    400,
    [jobsPerDay],
  );

  const { data: resourcesResponse } = api.Survey.getSurveyResources.useQuery({
    regionId: regionId!.value,
  });

  const filteredResources = (resourcesResponse ?? [])
    .filter((resource) => selectedSkills.every((skill) => resource.skills.includes(skill)))
    .filter((resource) => selectedRoles.every((role) => resource.roles.includes(role)));

  const optionsForSelf = filteredResources
    .filter(({ email }) => email.toLowerCase() === selfEmail)
    .map(({ name, email }) => ({
      label: `Myself (${name})`,
      value: email,
    }));
  const optionsForOtherResources = filteredResources
    .map(({ name, email }) => ({ name, email: email.toLowerCase() }))
    .filter(({ email }) => email !== selfEmail)
    .map(({ name, email }) => ({ label: name, value: email }));
  const optionsForSpecificResource = optionsForSelf.concat(optionsForOtherResources);

  const [dayAvailability, setDayAvailability] = useState<AvailabilitiesForDate | null>(null);
  useEffect(() => {
    if (
      availabilitiesGroupedByDate &&
      selectedDate &&
      isWithinInterval(selectedDate, { start: startOfMonth(from), end: to })
    ) {
      setDayAvailability(availabilitiesGroupedByDate.get(formatDate(selectedDate, timeZone)) ?? null);
    }
  }, [availabilitiesGroupedByDate, selectedDate, timeZone, from, to]);

  const handleMonthChange = (monthStart: Date) => {
    setFrom(max([monthStart, startOfTomorrow()]));
    setTo(min([endOfMonth(monthStart), endOfDay(maxDate)]));
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const handleDateChange = (newValue: Date | null) => {
    if (newValue) setSelectedDate(newValue);
    setSelectedTime(null);
    setSelectedTimeWindow(null);
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const isSelectedTime = ({ start, end }: { start: Date; end: Date }) =>
    start === selectedTime?.start && end === selectedTime?.end;

  const [timeGridError, setTimeGridError] = useState<string | null>(null);

  useEffect(() => setTimeGridError(null), [selectedDate, selectedTime]);

  const handleSkillChange = (skill: Skill, checked: boolean) => {
    if (checked && !selectedSkills.includes(skill)) {
      setSelectedSkills([...selectedSkills, skill]);
    } else {
      setSelectedSkills(selectedSkills.filter((s) => s !== skill));
    }
    setSelectedTime(null);
    setSelectedTimeWindow(null);
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const handleChangeTab = (_event: React.SyntheticEvent, newValue: SelectedTab) => {
    setSelectedTab(newValue);
    setSelectedTime(null);
    setSelectedTimeWindow(null);
    setAssignSpecificResource(false);
    setSpecificResource(null);
    setDurationMinutes(getDefaultDurationMinutes(newValue));
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const handleAssignSpecificResourceCheckbox = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAssignSpecificResource(event.target.checked);
    setSpecificResource(null);
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const changeTimeWindow = (timeWindow: 'morning' | 'afternoon' | null) => {
    setSelectedTimeWindow(timeWindow);
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const changeSpecificTime = (time: { start: Date; end: Date } | null) => {
    setSelectedTime(time);
    setSelectedResourceId(null);
    setSelectedResourceEmail(null);
  };

  const queryParams = new URLSearchParams(window.location.search);
  const timeWindowsEnabled = countryCode === 'GB' || queryParams.has('time-windows');

  const resources =
    dayAvailability?.availabilities.find((availability) => availability.start === selectedTime?.start)
      ?.surveyorAvailabilities ?? [];

  return (
    <Stack spacing={4}>
      {!scheduleOnly && bookingType !== 'videoSales' && (
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs variant="fullWidth" value={selectedTab} onChange={handleChangeTab}>
            <Tab label={intl.formatMessage({ id: 'bookingTool.tab.specificTime' })} value={SelectedTab.SPECIFIC_TIME} />
            {timeWindowsEnabled && (
              <Tab label={intl.formatMessage({ id: 'bookingTool.tab.timeWindow' })} value={SelectedTab.TIME_WINDOW} />
            )}
            <Tab
              label={intl.formatMessage({ id: 'bookingTool.tab.setTimeLater' })}
              value={SelectedTab.SET_TIME_LATER}
            />
          </Tabs>
        </Box>
      )}
      <FormGroup>
        <Typography variant="body1">{intl.formatMessage({ id: 'bookingTool.label.skills' })}</Typography>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          {skillSet.map((skill) => (
            <FormControlLabel
              key={skill.key}
              label={intl.formatMessage({ id: skill.translationKey })}
              checked={selectedSkills.includes(skill.key)}
              control={<Checkbox />}
              onChange={(_event, checked) => handleSkillChange(skill.key, checked)}
            />
          ))}
        </Box>
      </FormGroup>

      <DurationControl
        selectedTab={selectedTab}
        durationMinutes={durationMinutes}
        setDurationMinutes={setDurationMinutes}
        bookingType={bookingType}
      />
      {schedulingEnabled && (
        <>
          <FormGroup>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-start',
              }}
            >
              <Typography variant="body1">
                {intl.formatMessage({ id: 'bookingTool.label.assignSpecificPerson' })}
              </Typography>
              <Checkbox checked={assignSpecificResource} onChange={handleAssignSpecificResourceCheckbox} />
            </Box>
            {assignSpecificResource && (
              <Autocomplete
                disableTyping={false}
                label=""
                placeholder={intl.formatMessage({ id: 'bookingTool.placeholder.selectPerson' })}
                name="cleanEnergyExpert"
                options={optionsForSpecificResource}
                error={
                  !!specificResource && !optionsForSpecificResource.some((option) => option.value === specificResource)
                }
                errorText={intl.formatMessage({ id: 'bookingTool.error.resourceNotAvailable' })}
                onChange={(e: OnChangeEvent<string>) => {
                  setSpecificResource(e?.value ?? null);
                }}
                fullWidth
              />
            )}
          </FormGroup>
          <Accordion sx={{ background: '#FAF9F9', border: '1px solid black' }}>
            <AccordionSummary expandIcon={<GridExpandMoreIcon />}>
              <Typography sx={{ flexShrink: 0, margin: 1 }}>
                {intl.formatMessage({ id: 'installationBooking.moreOptions' })}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FormGroup>
                <NumericFormField
                  name="jobsPerDay"
                  label={intl.formatMessage({ id: 'installationBooking.fields.jobsPerDay' })}
                  value={jobsPerDay}
                  onChange={(e: number | null) => setJobsPerDay(e)}
                  fullWidth
                  size="small"
                />
                <Typography variant="body2" mt={1}>
                  {intl.formatMessage({ id: 'installationBooking.fields.jobsPerDayCaption' })}
                </Typography>
              </FormGroup>
            </AccordionDetails>
          </Accordion>
          <CalendarSection
            isLoading={isLoading}
            minDate={minDate}
            maxDate={maxDate}
            availabilitiesGroupedByDate={availabilitiesGroupedByDate ?? null}
            selectedDate={selectedDate}
            handleDateChange={handleDateChange}
            handleMonthChange={handleMonthChange}
            timeWindowsEnabled={selectedTab === SelectedTab.TIME_WINDOW}
            trafficLightEnabled
          />
          {selectedTab === SelectedTab.SPECIFIC_TIME ? (
            <SelectSpecificTimeSection
              selectedDate={selectedDate}
              availableTimes={dayAvailability?.availabilities ?? []}
              setSelectedTime={changeSpecificTime}
              isSelectedTime={isSelectedTime}
              trafficLightEnabled
            />
          ) : (
            <SelectTimeWindowSection
              selectedDate={selectedDate}
              dayAvailability={dayAvailability}
              setSelectedTimeWindow={changeTimeWindow}
              selectedTimeWindow={selectedTimeWindow}
            />
          )}

          {selectedTab === SelectedTab.SPECIFIC_TIME && resources && selectedTime && (
            <Resources
              resourcesData={resources}
              selectedResourceId={selectedResourceId}
              setSelectedResourceId={setSelectedResourceId}
              setSelectedResourceEmail={setSelectedResourceEmail}
              trafficLightEnabled={bookingType !== 'videoSales'}
            />
          )}
          {timeGridError && <Alert severity="error">{timeGridError}</Alert>}
        </>
      )}
      <FormGroup>
        <Typography variant="headline3" sx={{ mb: 1 }}>
          {intl.formatMessage({ id: 'bookingTool.label.notes' })}
        </Typography>
        <TextField
          name="notes"
          type="textarea"
          multiline
          value={notes}
          onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNotes(e.target.value)}
        />
      </FormGroup>

      <SummarySection
        withDateTime={selectedTab === SelectedTab.SPECIFIC_TIME || selectedTab === SelectedTab.TIME_WINDOW}
        selectedTime={selectedTime}
        selectedDate={selectedDate}
        selectedTimeWindow={selectedTimeWindow}
        selectedResourceEmail={selectedResourceEmail}
        installationGroundworkId={installationGroundworkId}
        setTimeGridError={setTimeGridError}
        setManuallyShowScheduler={setManuallyShowScheduler}
        jobType={jobType}
        skills={selectedSkills}
        roles={selectedRoles}
        durationMinutes={durationMinutes}
        notes={notes}
        surveyId={surveyId}
      />
    </Stack>
  );
}
