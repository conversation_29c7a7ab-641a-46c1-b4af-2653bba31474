import { useRegionContext } from './RegionContext';
import { formatDate, formatTime, formatLongDate } from './timeUtils';

type TimestampVariant =
  | 'time-short' // eg. "12:00"
  | 'date' // eg. "2024-01-01"
  | 'long-date'; // eg. "Monday January 1st"

export default function Timestamp({ t, variant }: { t: Date; variant: TimestampVariant }) {
  const { timeZone } = useRegionContext();

  if (variant === 'time-short') {
    return formatTime(t, timeZone);
  }

  if (variant === 'long-date') {
    return formatLongDate(t, timeZone);
  }

  if (variant === 'date') {
    return formatDate(t, timeZone);
  }

  throw new Error(`Unknown variant: ${variant}`);
}
