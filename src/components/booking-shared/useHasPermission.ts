import { Group } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { api } from 'utils/api';

export const useHasPermission = () => {
  const { data: user } = api.AiraBackend.whoAmI.useQuery();

  if (!user) {
    return false; // No user data, no permission
  }
  const isExternalUser =
    user.groupMemberships?.some((group: any) =>
      [
        Group.GROUP_EXTERNAL_ALL_EXPERTS,
        Group.GROUP_EXTERNAL_ALL_INSTALLER,
        Group.GROUP_ASSIGNED_EXTERNAL_ALL_EXPERTS,
        Group.GROUP_ASSIGNED_EXTERNAL_ALL_INSTALLER,
      ].includes(group.group),
    ) ?? false;

  return !isExternalUser;
};
