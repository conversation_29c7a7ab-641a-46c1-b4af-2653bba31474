import { createContext, useContext } from 'react';
import { Region } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';

export const RegionContext = createContext<Region | null>(null);

export function useRegionContext(): Region {
  const region = useContext(RegionContext);
  if (!region) {
    throw new Error('Region context not initialised');
  }
  return region;
}
