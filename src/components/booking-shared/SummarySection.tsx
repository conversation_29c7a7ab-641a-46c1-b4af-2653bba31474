import { Button } from '@ui/components/Button/Button';
import { <PERSON><PERSON>, Stack, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useGroundwork } from 'context/groundwork-context';
import { formatDate } from './timeUtils';
import { calculateTimeWindow, formatTimeWindowEnd, formatTimeWindowStart, timeWindows } from './timeWindows';
import { useRegionContext } from './RegionContext';
import TimeRange from './TimeRange';
import { api } from 'utils/api';
import {
  JobType,
  SurveyRole,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { Skill } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';

export function SummarySection({
  withDateTime,
  selectedTime,
  selectedDate,
  selectedTimeWindow,
  setTimeGridError,
  selectedResourceEmail,
  installationGroundworkId,
  jobType,
  skills,
  roles,
  setManuallyShowScheduler,
  durationMinutes,
  notes,
  surveyId,
}: {
  withDateTime: boolean;
  selectedTime: { start: Date; end: Date } | null;
  selectedDate: Date | null;
  selectedTimeWindow: 'morning' | 'afternoon' | null;
  setTimeGridError: (message: string) => void;
  selectedResourceEmail: string | null;
  installationGroundworkId: string;
  jobType: JobType;
  skills: Skill[];
  roles: SurveyRole[];
  setManuallyShowScheduler: (show: boolean) => void;
  durationMinutes: number;
  notes: string;
  surveyId?: string;
}) {
  const intl = useIntl();
  const {
    groundwork: { location },
  } = useGroundwork();
  const formattedAddress = location?.$case === 'exactAddress' ? location.exactAddress.formattedAddress : undefined;
  const [isBookingSurvey, setIsBookingSurvey] = useState(false);
  const [hasErrors, setHasErrors] = useState(false);
  const { timeZone } = useRegionContext();

  const { refetch: refetchSurveys } = api.Survey.listSurveyBookings.useQuery({ installationGroundworkId });

  const { mutateAsync: scheduleSurvey } = api.Survey.scheduleSurvey.useMutation({
    onSuccess: async () => {
      await refetchSurveys();
      setManuallyShowScheduler(false);
    },
  });

  const handleScheduleHomeSurveyClicked = async () => {
    if (withDateTime && !(selectedTime || (selectedTimeWindow && selectedDate))) {
      setTimeGridError(intl.formatMessage({ id: 'bookingTool.error.noTimeSlotSelected' }));
      return;
    }
    setIsBookingSurvey(true);
    try {
      if (withDateTime) {
        if (selectedTime) {
          if (!selectedResourceEmail) throw new Error('No resource email selected');
          await scheduleSurvey({
            installationGroundworkId,
            specificTime: selectedTime,
            resourceEmails: [selectedResourceEmail],
            jobType,
            skills,
            roles,
            durationMinutes,
            notes,
            surveyId,
          });
        } else {
          if (!selectedTimeWindow) throw new Error('No time window selected');
          if (!selectedDate) throw new Error('No date selected');
          await scheduleSurvey({
            installationGroundworkId,
            timeWindow: calculateTimeWindow(selectedDate, timeWindows[selectedTimeWindow], timeZone),
            jobType,
            skills,
            roles,
            durationMinutes,
            notes,
            surveyId,
          });
        }
      } else {
        await scheduleSurvey({
          installationGroundworkId,
          jobType,
          skills,
          roles,
          durationMinutes,
          notes,
          surveyId,
        });
      }
    } catch (_error) {
      setHasErrors(true);
    } finally {
      setIsBookingSurvey(false);
    }
  };

  // Reset any error on selected time change
  useEffect(() => setHasErrors(false), [selectedTime]);

  const isBookingDisabled =
    isBookingSurvey ||
    (withDateTime && ((!selectedTimeWindow && !selectedTime) || (!!selectedTime && selectedResourceEmail === null)));

  return (
    <Stack spacing={4}>
      <Stack spacing={4}>
        <Typography variant="headline2">{intl.formatMessage({ id: 'bookingTool.title.summary' })}</Typography>
        {withDateTime && (
          <Stack>
            <Typography variant="subtitle1">{intl.formatMessage({ id: 'bookingTool.label.dateAndTime' })}</Typography>
            {selectedTimeWindow && selectedDate && timeWindows[selectedTimeWindow] && (
              <Typography variant="body1Emphasis">
                {formatDate(selectedDate, timeZone)}
                {', '}
                {intl.formatMessage({ id: `bookingTool.timeWindow.${selectedTimeWindow}` })}{' '}
                {formatTimeWindowStart(timeWindows[selectedTimeWindow])}
                {' - '}
                {formatTimeWindowEnd(timeWindows[selectedTimeWindow])}
              </Typography>
            )}
            {!selectedTimeWindow && selectedTime && (
              <Typography variant="body1Emphasis">
                <TimeRange {...selectedTime} variant="normal" />
              </Typography>
            )}
            {!selectedTimeWindow && !selectedTime && <Typography variant="body1Emphasis">-</Typography>}
          </Stack>
        )}
        <Stack>
          <Typography variant="subtitle1">{intl.formatMessage({ id: 'common.label.address' })}</Typography>
          <Typography variant="body1">{formattedAddress}</Typography>
        </Stack>
      </Stack>

      <Button
        variant="contained"
        sx={{ width: '100%', ':disabled': { color: '#888' } }}
        disabled={isBookingDisabled}
        onClick={handleScheduleHomeSurveyClicked}
        loading={isBookingSurvey}
      >
        {intl.formatMessage({ id: 'bookingTool.button.bookJob' })}
      </Button>
      {selectedResourceEmail === null && (
        <Alert severity="info" sx={{ width: '100%' }}>
          <FormattedMessage id="bookingTool.error.noResourceSelected" defaultMessage="Please select a resource" />
        </Alert>
      )}
      {hasErrors && (
        <Alert severity="error">{intl.formatMessage({ id: 'bookingTool.error.jobSchedulingFailure' })}</Alert>
      )}
    </Stack>
  );
}
