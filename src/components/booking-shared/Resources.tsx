import { CheckAvailabilityResponse_SurveyorAvailability } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { UserIdentityView } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { brandYellow, red } from '@ui/theme/colors';
import { green } from '@ui/theme/colors';
import { api } from 'utils/api';

const availabilityIndicatorStyle = {
  zIndex: 1,
  content: '""',
  left: '14px',
  bottom: '10px',
  position: 'absolute',
  width: '212px',
  height: '6px',
  pointerEvents: 'none',
  borderRadius: '50px',
};

export function getAvailabilityColor(
  availabilities: CheckAvailabilityResponse_SurveyorAvailability[],
  trafficLightEnabled: boolean = false,
) {
  if (!availabilities.length) {
    return null;
  }

  if (!trafficLightEnabled) {
    return green[400];
  }

  const hasTravelTime = (minutes: number) =>
    availabilities.some((availability) => {
      if (availability.bestTravelTimeBounds?.bound?.$case === 'limit') {
        return (availability.bestTravelTimeBounds.bound.limit.limit?.seconds ?? 0) / 60 === minutes;
      }
      return false;
    });

  switch (true) {
    case hasTravelTime(40):
      return green[400];
    case hasTravelTime(70):
      return brandYellow[400];
    case hasTravelTime(90):
      return red[500];
    default:
      return null;
  }
}

export default function Resources({
  resourcesData,
  setSelectedResourceId,
  selectedResourceId,
  setSelectedResourceEmail,
  trafficLightEnabled,
}: {
  resourcesData: CheckAvailabilityResponse_SurveyorAvailability[];
  selectedResourceId: string | null;
  setSelectedResourceId: (resourceId: string) => void;
  setSelectedResourceEmail: (resourceEmail: string) => void;
  trafficLightEnabled?: boolean;
}) {
  const { data: resourcesInfoData } = api.Resource.getResourceInfo.useQuery(
    {
      userIds: resourcesData.map((resource) => resource.userId?.value).filter((userId) => userId !== undefined),
      view: UserIdentityView.USER_IDENTITY_VIEW_FULL,
    },
    {
      enabled: resourcesData.length > 0,
    },
  );

  return (
    <Stack>
      <Typography variant="headline4" sx={{ my: '36px' }}>
        Available resources ({resourcesInfoData?.length})
      </Typography>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))', gap: 16 }}>
        {resourcesInfoData?.map((resource) => {
          const userId = resource.userId?.value;
          const email = resource.email;
          const availability = resourcesData.find((r) => r.userId?.value === userId);
          if (!userId || !email) {
            return null;
          }
          return (
            <Button
              key={userId}
              onClick={() => {
                setSelectedResourceId(userId);
                setSelectedResourceEmail(email);
              }}
              variant={selectedResourceId === userId ? 'contained' : 'outlined'}
              sx={{
                flexDirection: 'row',
                cursor: 'pointer',
                width: '240px',
                alignItems: 'center',
                justifyContent: 'flex-start',
                ...(trafficLightEnabled && availability
                  ? {
                      '::before': {
                        ...availabilityIndicatorStyle,
                        display: 'block',
                        background: getAvailabilityColor([availability], trafficLightEnabled),
                      },
                    }
                  : undefined),
              }}
            >
              {`${resource.firstName} ${resource.lastName}`}
            </Button>
          );
        })}
      </div>
    </Stack>
  );
}
