import { JobStatus, Skill } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import {
  JobType,
  SurveyRole,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import ScheduledBookingsListItem from './ScheduledBookingListItem';

export interface SurveyBooking {
  surveyId: string;
  start?: Date;
  end?: Date;
  timeConstraint?: { startFromInclusive: Date; endByInclusive: Date };
  status: JobStatus;
  scheduled: boolean;
  notes?: string;
  roles: SurveyRole[];
  assignedResources: { name: string; email: string }[];
  requiredSkills: Skill[];
  webLinkToJob?: string;
  durationMinutes: number;
  jobType?: JobType;
}

interface ScheduledBookingsProps {
  surveyBookings: SurveyBooking[];
  installationGroundworkId: string;
  setManuallyShowScheduler: (show: boolean) => void;
  bookingType: 'sales' | 'technicalSurvey' | 'videoSales';
}

export function ScheduledBookings({
  surveyBookings,
  installationGroundworkId,
  setManuallyShowScheduler,
  bookingType,
}: ScheduledBookingsProps) {
  return surveyBookings.map((surveyBooking) => (
    <ScheduledBookingsListItem
      bookingType={bookingType}
      key={surveyBooking.surveyId}
      surveyBooking={surveyBooking}
      installationGroundworkId={installationGroundworkId}
      setManuallyShowScheduler={setManuallyShowScheduler}
    />
  ));
}
