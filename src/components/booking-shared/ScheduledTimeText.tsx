import { Box, Typography } from '@mui/material';
import { JobStatus } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/scheduling/v1/model';
import { MessageKey } from 'messageType';
import { SurveyBooking } from '../booking-shared/ScheduledBookings';
import { IntlShape, useIntl } from 'react-intl';
import TimeRange from './TimeRange';

function getBookingMessage(
  formatMessage: IntlShape['formatMessage'],
  status: SurveyBooking['status'],
  timeConstraint: SurveyBooking['timeConstraint'],
) {
  if (status === JobStatus.JOB_STATUS_FINISHED) {
    return formatMessage({ id: 'bookingTool.body.completedOn' });
  }
  if (timeConstraint) {
    return formatMessage({ id: 'bookingTool.body.scheduledForTimeWindow' });
  }
  return formatMessage({ id: 'bookingTool.body.scheduledForDateAndTime' });
}

function timeVariant(start: Date) {
  const now = new Date();

  const sixMonthsInMilliseconds = 6 * 30 * 24 * 60 * 60 * 1000;
  const timeDifference = now.getTime() - start.getTime();

  return timeDifference > sixMonthsInMilliseconds ? 'normal' : 'long';
}

export default function ScheduledTimeText({
  scheduled,
  start,
  end,
  status,
  timeConstraint,
}: {
  scheduled: boolean;
  start: Date;
  end: Date;
  status: JobStatus;
  timeConstraint?: { startFromInclusive: Date; endByInclusive: Date };
}) {
  const { formatMessage } = useIntl();
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginTop: '8px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'flex-start',
          gap: '4px',
        }}
      >
        {scheduled ? (
          <>
            <Typography variant="body1">{getBookingMessage(formatMessage, status, timeConstraint)}</Typography>
            <Typography variant="body1Emphasis">
              <TimeRange
                start={timeConstraint?.startFromInclusive ?? start!}
                end={timeConstraint?.endByInclusive ?? end!}
                variant={timeVariant(timeConstraint?.startFromInclusive ?? start!)}
              />
            </Typography>
          </>
        ) : (
          <Typography>{formatMessage({ id: 'bookingTool.body.surveyUnscheduled' as MessageKey })}</Typography>
        )}
      </Box>
    </Box>
  );
}
