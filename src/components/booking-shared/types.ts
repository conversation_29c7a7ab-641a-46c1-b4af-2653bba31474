import { CheckAvailabilityResponse_SurveyorAvailability } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/api/gateway/survey/v1/admin';
import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';

export interface TimeSlot {
  start: Date;
  end: Date;
}

export interface AvailabilitiesForDate {
  availabilities: {
    start: Date;
    end: Date;
    bestTravelTimeBounds?: Duration;
    surveyorAvailabilities?: CheckAvailabilityResponse_SurveyorAvailability[];
  }[];
  morningAvailabilities?: {
    start: Date;
    end: Date;
    bestTravelTimeBounds?: Duration;
    surveyorAvailabilities?: CheckAvailabilityResponse_SurveyorAvailability[];
  }[];
  afternoonAvailabilities?: {
    start: Date;
    end: Date;
    bestTravelTimeBounds?: Duration;
    surveyorAvailabilities?: CheckAvailabilityResponse_SurveyorAvailability[];
  }[];
}

export type AvailabilitiesGroupedByDate = Map<string, AvailabilitiesForDate | null>;
