import { useMemo, useState } from 'react';
import { api } from 'utils/api';
import { useIntl } from 'react-intl';
import { useMediaQuery } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { JobType_MeetingType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.survey.v1';
import { Stack, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import SurveyLinks from 'components/booking-shared/SurveyLinks';
import { ScheduledBookings } from './ScheduledBookings';
import TimeBooker from './TimeBooker';
import { Button } from '@ui/components/Button/Button';

const Title = ({ bookingType }: { bookingType: 'sales' | 'technicalSurvey' | 'videoSales' }) => {
  switch (bookingType) {
    case 'sales':
      return <FormattedMessage id="bookingTool.title.salesSurvey" defaultMessage="Sales survey" />;
    case 'technicalSurvey':
      return <FormattedMessage id="bookingTool.title.technicalSurvey" defaultMessage="Technical survey" />;
    case 'videoSales':
      return <FormattedMessage id="bookingTool.title.videoSalesBooking" defaultMessage="Video sales booking" />;
  }
};

export default function BookingToolContent({
  installationGroundworkId,
  bookingType,
}: {
  installationGroundworkId: string;
  bookingType: 'sales' | 'technicalSurvey' | 'videoSales';
}) {
  const [manuallyShowScheduler, setManuallyShowScheduler] = useState(false);
  const { data, isLoading } = api.Survey.listSurveyBookings.useQuery({ installationGroundworkId });
  const intl = useIntl();

  const isMobile = useMediaQuery('(max-width: 700px)');

  const handleAddNewSurvey = () => {
    setManuallyShowScheduler(true);
  };

  const surveyBookings = useMemo(
    () =>
      data?.filter((survey) => {
        switch (bookingType) {
          case 'sales':
            return (
              survey.jobType?.type?.$case === 'salesMeeting' &&
              survey.jobType?.type?.salesMeeting?.meetingType === JobType_MeetingType.MEETING_TYPE_PHYSICAL_MEETING
            );
          case 'technicalSurvey':
            return survey.jobType?.type?.$case === 'technicalSurveyMeeting';
          case 'videoSales':
            return (
              survey.jobType?.type?.$case === 'salesMeeting' &&
              survey.jobType?.type?.salesMeeting?.meetingType === JobType_MeetingType.MEETING_TYPE_REMOTE_MEETING
            );
          default:
            return false;
        }
      }),
    [data, bookingType],
  );

  const showScheduler = manuallyShowScheduler || (surveyBookings !== undefined && surveyBookings.length === 0);

  if (isLoading) {
    return <HeatPumpLoader />;
  }

  return (
    <Stack
      spacing={2}
      sx={{
        maxWidth: isMobile ? '100%' : '560px',
        width: '100%',
      }}
    >
      <Typography variant="headline1" textAlign="left">
        <Title bookingType={bookingType} />
      </Typography>
      {surveyBookings && surveyBookings?.length > 0 && (
        <>
          <SurveyLinks installationGroundworkId={installationGroundworkId} />
          <ScheduledBookings
            installationGroundworkId={installationGroundworkId}
            surveyBookings={surveyBookings}
            setManuallyShowScheduler={setManuallyShowScheduler}
            bookingType={bookingType}
          />
        </>
      )}
      {showScheduler ? (
        <Card
          sx={{
            background: '#fff',
          }}
        >
          <TimeBooker
            bookingType={bookingType}
            installationGroundworkId={installationGroundworkId}
            setManuallyShowScheduler={setManuallyShowScheduler}
          />
        </Card>
      ) : (
        <Stack
          sx={{
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
            width: '100%',
            height: '100%',
            paddingTop: '40px',
          }}
        >
          {surveyBookings && surveyBookings.length === 0 && (
            <Typography variant="body1" mb={2}>
              {intl.formatMessage({ id: 'booking.no.survey.scheduled' })}
            </Typography>
          )}
          <Button color="primary" variant="contained" onClick={handleAddNewSurvey}>
            {intl.formatMessage({ id: 'booking.button.schedule.survey' })}
          </Button>
        </Stack>
      )}
    </Stack>
  );
}
