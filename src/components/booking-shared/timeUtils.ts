import { tz } from '@date-fns/tz';
import { format } from 'date-fns';

// eg. "2024-01-01"
export const formatDate = (date: Date, timeZone: string): string =>
  format(date, 'yyyy-MM-dd', {
    in: tz(timeZone ?? Intl.DateTimeFormat().resolvedOptions().timeZone),
  });

// eg. "Monday January 1st"
export const formatLongDate = (date: Date, timeZone: string): string =>
  format(date, 'eeee MMMM do', {
    in: tz(timeZone),
  });

// eg. "12:00"
export const formatTime = (date: Date, timeZone: string): string =>
  format(date, 'HH:mm', {
    in: tz(timeZone),
  });
