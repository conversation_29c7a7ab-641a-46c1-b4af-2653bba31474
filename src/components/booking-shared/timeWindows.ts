import { tz } from '@date-fns/tz';
import { isWithinInterval, set } from 'date-fns';

export interface TimeWindow {
  start: number;
  end: number;
}

export const timeWindows = {
  morning: {
    start: 9,
    end: 13,
  },
  afternoon: {
    start: 13,
    end: 17,
  },
};

export function calculateTimeWindow(date: Date, timeWindow: TimeWindow, timeZone: string): { start: Date; end: Date } {
  return {
    start: set(
      date,
      {
        hours: timeWindow.start,
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
      },
      { in: tz(timeZone) },
    ),
    end: set(
      date,
      {
        hours: timeWindow.end,
        minutes: 0,
        seconds: 0,
        milliseconds: 0,
      },
      { in: tz(timeZone) },
    ),
  };
}

export function isInWindow(timeSlot: { start: Date; end: Date }, timeWindow: TimeWindow, timeZone: string): boolean {
  const tw = calculateTimeWindow(timeSlot.start, timeWindow, timeZone);
  return (
    isWithinInterval(timeSlot.start, tw, { in: tz(timeZone) }) &&
    isWithinInterval(timeSlot.end, tw, { in: tz(timeZone) })
  );
}

export function formatTimeWindowStart(timeWindow: TimeWindow) {
  const hourWithPadding = `${timeWindow.start}`.padStart(2, '0');
  return `${hourWithPadding}:00`;
}

export function formatTimeWindowEnd(timeWindow: TimeWindow) {
  const hourWithPadding = `${timeWindow.end}`.padStart(2, '0');
  return `${hourWithPadding}:00`;
}
