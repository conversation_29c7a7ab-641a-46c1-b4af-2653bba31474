import Public from '@ui/components/Icons/material/Public';
import Download from '@ui/components/Icons/material/Download';
import { Box, Chip, Link, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { api } from 'utils/api';
import { useIntl } from 'react-intl';
import { getLatestSurveyForm, getLatestSurveyFormSubmissionReport } from 'utils/helpers';

interface SurveyLink {
  label: string;
  icon: React.ReactElement;
  href: string;
}

interface SurveyLinkRowProps {
  message: string;
  links: SurveyLink[];
}

function SurveyLinkRow({ message, links }: SurveyLinkRowProps) {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        height: '100%',
      }}
    >
      <Typography variant="body1" textAlign="left">
        {message}
      </Typography>
      <Box>
        {links.map(({ label, icon, href }) => (
          <Link key={label + href} href={href} target="_blank">
            <Chip
              label={label}
              icon={icon}
              clickable
              sx={{
                borderRadius: '8px',
                fontSize: '12px',
                height: '26px',
                fontFamily: 'AiraText',
                '.MuiChip-label': {
                  padding: '4px 8px 4px 4px',
                  lineHeight: '18px',
                },
                marginLeft: '8px',
              }}
            />
          </Link>
        ))}
      </Box>
    </Box>
  );
}

export default function SurveyLinks({ installationGroundworkId }: { installationGroundworkId: string }) {
  const { data: surveyFormsResponse } = api.AiraBackend.getSurveyForms.useQuery(
    { installationGroundworkId },
    { enabled: !!installationGroundworkId },
  );
  const intl = useIntl();
  const latestSurveyForm = getLatestSurveyForm(surveyFormsResponse);
  const latestSurveyFormSubmissionReport = getLatestSurveyFormSubmissionReport(latestSurveyForm);

  if (!latestSurveyForm) {
    return null;
  }

  const rows: SurveyLinkRowProps[] = [];
  if (latestSurveyFormSubmissionReport?.url) {
    rows.push({
      message: intl.formatMessage({ id: 'bookingTool.label.surveyPDF' }),
      links: [
        {
          href: latestSurveyFormSubmissionReport.url,
          label: 'Download',
          icon: <Download sx={{ height: '18px' }} />,
        },
      ],
    });
  }
  if (latestSurveyForm.referenceCode) {
    const links: SurveyLink[] = [];
    if (latestSurveyForm.deeplinkToMobile) {
      links.push({
        href: latestSurveyForm.deeplinkToMobile,
        label: intl.formatMessage({ id: 'bookingTool.label.mobileLink' }),
        icon: <Public sx={{ height: '18px' }} />,
      });
    }
    if (latestSurveyForm.webUrl) {
      links.push({
        href: latestSurveyForm.webUrl,
        label: intl.formatMessage({ id: 'bookingTool.label.webLink' }),
        icon: <Public sx={{ height: '18px' }} />,
      });
    }
    if (links.length > 0) {
      rows.push({
        message: `${intl.formatMessage({ id: 'bookingTool.label.magicPlan' })} (${latestSurveyForm.referenceCode})`,
        links,
      });
    }
  }
  if (rows.length === 0) {
    return null;
  }

  return (
    <Card
      sx={{
        borderRadius: '8px',
        width: '100%',
        overflow: 'visible',
        background: '#fff',
        '.MuiCardContent-root': {
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: '8px',
          paddingTop: '16px !important',
          paddingBottom: '16px !important',
        },
      }}
    >
      {rows.map((link) => (
        <SurveyLinkRow key={link.message} {...link} />
      ))}
    </Card>
  );
}
