import { formatDate, formatLongDate, formatTime } from '../booking-shared/timeUtils';

describe('formatTime', () => {
  it.each([
    ['Europe/London', '12:00'],
    ['Europe/Rome', '13:00'],
    ['Europe/Helsinki', '14:00'],
  ])('formats time in %p time zone', (timeZone, expected) => {
    const date = new Date('2024-01-01T12:00:00Z');
    expect(formatTime(date, timeZone)).toEqual(expected);
  });
});

describe('formatLongDate', () => {
  it.each([
    ['Europe/London', 'Monday January 1st'],
    ['America/Los_Angeles', 'Sunday December 31st'],
  ])('formats date in %p time zone', (timeZone, expected) => {
    const date = new Date('2024-01-01T04:00:00Z');
    expect(formatLongDate(date, timeZone)).toEqual(expected);
  });
});

describe('formatDate', () => {
  it.each([
    ['Europe/London', '2024-01-01'],
    ['America/Los_Angeles', '2023-12-31'],
  ])('formats date in %p time zone', (timeZone, expected) => {
    const date = new Date('2024-01-01T04:00:00Z');
    expect(formatDate(date, timeZone)).toEqual(expected);
  });
});
