import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';
import { MenuItem, Stack, Typography } from '@mui/material';
import { Autocomplete } from '@ui/components/Autocomplete/Autocomplete';
import { Button } from '@ui/components/Button/Button';
import { Select } from '@ui/components/Select/Select';
import { COUNTRIES } from 'components/installation-planning/types/planningTypes';
import usePageViewEvent from 'hooks/usePageViewEvent';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { api } from 'utils/api';
import { formatRegionsToDropdownOptions } from 'utils/helpers';

type CountryWithName = { name: string; grpcCode: Country; code: string };

export function RegionSelector({ pageTitle, pagePath }: { pageTitle: string; pagePath: string }) {
  const router = useRouter();
  usePageViewEvent({ pageTitle });
  const [country, setCountry] = useState<CountryWithName>(COUNTRIES[0]);
  const [regionId, setRegionId] = useState<string | null>(null);

  const countryCode = country.grpcCode;
  const { data: regionsResponse } = api.AiraBackend.listRegionsGrpc.useQuery(
    { country: countryCode!, excludeNonSchedulable: false },
    {
      enabled: !!countryCode,
    },
  );

  const handleGoToRegion = () => {
    router.push(`/${pagePath}/${regionId}`);
  };

  const formattedRegions = useMemo(() => {
    const regions = regionsResponse?.regions ?? [];
    return formatRegionsToDropdownOptions(regions);
  }, [regionsResponse]);

  return (
    <Stack
      direction="column"
      spacing={4}
      sx={{
        p: 4,
        width: '400px',
      }}
    >
      <Typography variant="headline1">{pageTitle}</Typography>
      <Stack direction="row" alignItems="flex-end">
        <Select
          name="country"
          label="Country"
          value={country.name}
          onChange={(e) => {
            const countryName = e.target.value;
            setCountry(COUNTRIES.find((c) => c.name === countryName)!);
          }}
        >
          {COUNTRIES.map((c) => (
            <MenuItem key={c.name} value={c.name}>
              {c.name}
            </MenuItem>
          ))}
        </Select>
      </Stack>
      <Stack direction="row" alignItems="flex-end">
        <Autocomplete
          label="Region"
          disableClearable
          fullWidth
          error={false}
          defaultValue={formattedRegions.find((rg) => rg.value === regionId)}
          options={formattedRegions}
          onChange={(e) => {
            setRegionId(e?.value ?? null);
          }}
          name="region"
        />
        <Button sx={{ ml: 3 }} onClick={handleGoToRegion}>
          Go
        </Button>
      </Stack>
    </Stack>
  );
}
