import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { differenceInHours, differenceInMinutes, isFriday, subDays } from 'date-fns';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { tz } from '@date-fns/tz';
import { ContinuousWorkSegment, RoleBasedSegments } from './types/planningTypes';
import { getCacheKey, WorkingHoursOnDate } from './helpers/workingHours';
import { isSameDayFast } from './helpers/dateHelpers';
import { DateRange } from './topBar/DateRangePicker';

export default function createContinuousWorkSegmentsPerResourceTypeAndTeam({
  requiredRoles,
  projects,
  timeZone,
  teams,
  workingHoursForDatesMap,
  dateRange,
}: {
  requiredRoles: number[];
  projects: FullInstallationProjectEntity[];
  timeZone: string;
  teams: Team[];
  workingHoursForDatesMap: Map<string, WorkingHoursOnDate>;
  dateRange: DateRange;
}): RoleBasedSegments {
  // Initialize result structure with Map
  const result: RoleBasedSegments = new Map(
    requiredRoles.map((role) => [
      role,
      {
        continuousWorkSegmentsWithTeam: new Map(),
        continuousWorkSegmentsWithoutTeam: [],
      },
    ]),
  );

  // Pre-compute teams lookup map for faster checks
  const teamIdMap = new Set(teams.map((team) => team.id?.value).filter(Boolean));

  // Single pass through projects and jobs
  projects.forEach((project) => {
    project.installationProject?.jobs.forEach((job) => {
      if (!job.workSegments?.length || !job.id || !requiredRoles.includes(job.requiredRole)) return;

      const roleSegments = result.get(job.requiredRole);
      if (!roleSegments) return; // Type guard for safety

      const pushSegment = (segment: ContinuousWorkSegment) => {
        const hasTeam = segment.segmentDetails.teamId && teamIdMap.has(segment.segmentDetails.teamId);
        if (hasTeam) {
          const currentSegmentsForTeam =
            roleSegments.continuousWorkSegmentsWithTeam.get(segment.segmentDetails.teamId!) ?? [];
          const segmentStartTime = segment.segmentDetails.startTime.getTime();
          // find the index of the segment that has a start time that is closest and before the current segment start time in an efficient way
          const currentIndexClosest = currentSegmentsForTeam.findIndex(
            (s) => s.segmentDetails.startTime.getTime() > segmentStartTime,
          );
          if (currentIndexClosest === -1) {
            roleSegments.continuousWorkSegmentsWithTeam.set(segment.segmentDetails.teamId!, [
              ...currentSegmentsForTeam,
              segment,
            ]);
          } else {
            currentSegmentsForTeam.splice(currentIndexClosest, 0, segment);
            roleSegments.continuousWorkSegmentsWithTeam.set(segment.segmentDetails.teamId!, currentSegmentsForTeam);
          }
        } else {
          roleSegments.continuousWorkSegmentsWithoutTeam.push(segment);
        }
      };

      // Process segments for this job
      let lastSegmentEnd: Date | null = null;
      let currentSegmentGroup: InstallationProjectJob_WorkSegment[] = [];
      let currentSegmentGroupDuration = 0;
      let currentSegmentGroupTeamId: string | undefined = undefined;

      for (const ws of job.workSegments) {
        if (!ws.startTime || !ws.endTime) {
          continue;
        }
        if (
          ws.startTime.getTime() < dateRange.startDate.getTime() ||
          ws.endTime.getTime() > dateRange.endDateInclusive.getTime()
        ) {
          continue;
        }

        if (!lastSegmentEnd) {
          currentSegmentGroup = [ws];
          currentSegmentGroupDuration = differenceInHours(ws.endTime, ws.startTime);
          currentSegmentGroupTeamId = ws.teamId?.value;
          lastSegmentEnd = ws.endTime;
        } else {
          const workingHoursPrevious = workingHoursForDatesMap.get(getCacheKey(lastSegmentEnd, timeZone))!;
          const workingHoursCurrent = workingHoursForDatesMap.get(getCacheKey(ws.startTime, timeZone))!;
          if (!workingHoursPrevious && !workingHoursCurrent) {
            continue;
          }

          const workSegmentTeamId = ws.teamId?.value;
          // using isSameDayFast to check if the segments are on the same day
          // this should be ok as we are using the same timezone for all the dates
          const onSameDay = isSameDayFast(lastSegmentEnd, ws.startTime);
          const spansWeekend =
            isFriday(lastSegmentEnd, { in: tz(timeZone) }) && isSameDayFast(lastSegmentEnd, subDays(ws.startTime, 3));
          const spansOvernight = isSameDayFast(lastSegmentEnd, subDays(ws.startTime, 1));

          const adjacentGridDays =
            workingHoursPrevious &&
            workingHoursCurrent &&
            (spansWeekend || spansOvernight) &&
            differenceInMinutes(workingHoursPrevious.workEndsAt, lastSegmentEnd) < 70 &&
            differenceInMinutes(ws.startTime, workingHoursCurrent.workStartsAt) < 70;

          if ((onSameDay || adjacentGridDays) && workSegmentTeamId === currentSegmentGroupTeamId) {
            currentSegmentGroup.push(ws);
            currentSegmentGroupDuration += differenceInHours(ws.endTime, ws.startTime);
            lastSegmentEnd = ws.endTime;
          } else {
            pushSegment({
              ...project,
              segmentDetails: {
                jobId: job.id.value,
                requiredRole: job.requiredRole,
                startTime: currentSegmentGroup[0]!.startTime!,
                endTime: currentSegmentGroup[currentSegmentGroup.length - 1]!.endTime!,
                duration: currentSegmentGroupDuration,
                teamId: currentSegmentGroupTeamId,
                workSegments: currentSegmentGroup,
                status: job.status,
                assignedResources: job.assignedResources
                  .map((ar) => (ar.userId?.value ? { userId: ar.userId.value } : undefined))
                  .filter((ar): ar is { userId: string } => ar !== undefined),
              },
              hasAssignedResources: job.assignedResources.length > 0,
            });

            currentSegmentGroup = [ws];
            currentSegmentGroupDuration = differenceInHours(ws.endTime, ws.startTime);
            lastSegmentEnd = ws.endTime;
          }
          currentSegmentGroupTeamId = workSegmentTeamId;
        }
      }

      // If we ended with an open group, push it
      if (currentSegmentGroup.length > 0) {
        pushSegment({
          ...project,
          segmentDetails: {
            jobId: job.id.value,
            requiredRole: job.requiredRole,
            startTime: currentSegmentGroup[0]!.startTime!,
            endTime: currentSegmentGroup[currentSegmentGroup.length - 1]!.endTime!,
            duration: currentSegmentGroupDuration,
            teamId: currentSegmentGroup[0]?.teamId?.value,
            workSegments: currentSegmentGroup,
            status: job.status,
            assignedResources: job.assignedResources
              .map((ar) => (ar.userId?.value ? { userId: ar.userId?.value } : undefined))
              .filter((ar): ar is { userId: string } => ar !== undefined),
          },
          hasAssignedResources: job.assignedResources.length > 0,
        });
      }
    });
  });

  return result;
}
