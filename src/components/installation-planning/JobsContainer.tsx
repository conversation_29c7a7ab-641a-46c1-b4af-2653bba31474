import React, { useRef } from 'react';
import { Stack } from '@mui/material';
import { resourceMapping, useResourceTypesForCountry } from './types/planningTypes';
import UnscheduledJobsSidebar from './unscheduledJobs/UnscheduledJobsSidebar';
import TableGridContainer from './table/TableGridContainer';
import TopBar from './topBar/TopBar';
import { useTableContents } from './stores/ProjectStore';
import OverlappingSegmentsModal from './modals/OverlappingSegmentsModal';
import { useCountryCodeContext } from '../../context/CountryCodeContext';

export default function JobsContainer() {
  const countryCode = useCountryCodeContext();
  const resourceTypesForCountry = useResourceTypesForCountry(countryCode);
  const tableContents = useTableContents();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <Stack
      direction="column"
      spacing={0}
      sx={{
        maxWidth: '100dvw',
        height: '100dvh',
        maxHeight: '100dvh',
        overflow: 'hidden',
      }}
    >
      <TopBar />
      <Stack direction="row">
        {tableContents === 'Teams' && (
          <UnscheduledJobsSidebar
            requiredRoles={resourceTypesForCountry.map(
              (resourceType) => resourceMapping[resourceType as keyof typeof resourceMapping],
            )}
          />
        )}
        <Stack
          ref={scrollContainerRef}
          spacing={0}
          maxHeight="100dvh"
          sx={{
            width: '100%',
            overflow: 'auto',
            position: 'relative',
          }}
        >
          <TableGridContainer />
        </Stack>
      </Stack>
      <OverlappingSegmentsModal />
    </Stack>
  );
}
