import {
  InstallationProjectJob_WorkSegment,
  InstallationProjectJob_WorkSegmentStatus,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { tz } from '@date-fns/tz';
import { addHours, differenceInSeconds, isSameSecond } from 'date-fns';

type SegmentDetails = {
  jobId: string;
  workSegments: InstallationProjectJob_WorkSegment[];
  duration: number;
};

type Position = {
  x: number;
  y: number;
  splitTime: Date | undefined;
  segmentIndex: number | undefined;
};

type MouseCoordinates = {
  x: number;
  y: number;
  width: number;
};

export const getMouseCoordinates = (event: React.MouseEvent, element: HTMLElement): MouseCoordinates => {
  const rect = element.getBoundingClientRect();
  return {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
    width: rect.width,
  };
};

export const calculateStartedHours = (workSegments: InstallationProjectJob_WorkSegment[]): number => {
  const firstNonStartedIndex = workSegments.findIndex(
    (segment) => segment.status === InstallationProjectJob_WorkSegmentStatus.WORK_SEGMENT_STATUS_NOT_STARTED,
  );

  return workSegments.slice(0, firstNonStartedIndex).reduce((acc, segment) => {
    if (!segment.endTime || !segment.startTime) return acc;
    return acc + differenceInSeconds(segment.endTime, segment.startTime) / 3600;
  }, 0);
};

export const findSplitTimeAndSegment = (
  workSegments: InstallationProjectJob_WorkSegment[],
  hoursBeforeSplit: number,
  timeZoneName: string,
): Pick<Position, 'splitTime' | 'segmentIndex'> => {
  let remainingHours = hoursBeforeSplit;

  for (let i = 0; i < workSegments.length; i++) {
    const segment = workSegments[i];
    if (!segment) continue;

    const segmentDuration = (segment.duration?.seconds ?? 0) / 3600;

    if (remainingHours >= segmentDuration) {
      remainingHours -= segmentDuration;
      continue;
    }

    if (!segment.startTime) break;

    return {
      splitTime: addHours(segment.startTime, remainingHours, { in: tz(timeZoneName) }),
      segmentIndex: i,
    };
  }

  return { splitTime: undefined, segmentIndex: undefined };
};

export const handleMouseMove = (
  event: React.MouseEvent,
  segmentDetails: SegmentDetails,
  boxRef: React.RefObject<HTMLDivElement>,
  timeZone: string,
): Position | undefined => {
  if (!boxRef.current || !segmentDetails.duration) return undefined;

  const { x, y, width } = getMouseCoordinates(event, boxRef.current);
  const numberOfStartedHours = calculateStartedHours(segmentDetails.workSegments);

  // Calculate hours at mouse position
  const widthPerHour = width / segmentDetails.duration;
  const nearestHour = Math.round(x / widthPerHour);
  const hoursBeforeSplit = Math.max(nearestHour, numberOfStartedHours);

  // Calculate final x position
  const minX = Math.max(x, numberOfStartedHours * widthPerHour - 2);

  // Find split time and segment
  const { splitTime, segmentIndex } = findSplitTimeAndSegment(segmentDetails.workSegments, hoursBeforeSplit, timeZone);

  return { x: minX, y, splitTime, segmentIndex };
};

export const handleSplitSegmentClick = (
  position: Position,
  segmentDetails: SegmentDetails,
):
  | {
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    }
  | undefined => {
  const { splitTime, segmentIndex } = position;
  if (segmentIndex === undefined || !splitTime) return undefined;
  const segmentAtSplit = segmentDetails.workSegments[segmentIndex];
  if (!segmentAtSplit || !segmentAtSplit.endTime || !segmentAtSplit.startTime) return undefined;
  const segmentsBeforeSplit = segmentDetails.workSegments.slice(0, segmentIndex);
  const segmentsAfterSplit = segmentDetails.workSegments.slice(segmentIndex + 1);
  if (isSameSecond(splitTime, segmentAtSplit.endTime)) {
    segmentsBeforeSplit.push({
      ...segmentAtSplit,
    });
  } else if (isSameSecond(splitTime, segmentAtSplit.startTime)) {
    segmentsAfterSplit.push({
      ...segmentAtSplit,
    });
  } else {
    segmentsBeforeSplit.push({
      ...segmentAtSplit,
      endTime: splitTime,
      duration: {
        seconds: differenceInSeconds(splitTime, segmentAtSplit.startTime),
        nanos: 0,
      },
    });
    segmentsAfterSplit.unshift({
      ...segmentAtSplit,
      startTime: splitTime,
      duration: {
        seconds: differenceInSeconds(segmentAtSplit.endTime, splitTime),
        nanos: 0,
      },
    });
  }

  return {
    segmentsBeforeSplit,
    segmentsAfterSplit,
  };
};
