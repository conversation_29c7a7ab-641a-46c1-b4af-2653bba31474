export default function getDistanceBetweenTwoLatLong(
  coordinate1: { lat: number; long: number },
  coordinate2: { lat: number; long: number },
) {
  const r = 6371; // km
  const p = Math.PI / 180;

  const lat1 = coordinate1.lat;
  const lon1 = coordinate1.long;
  const lat2 = coordinate2.lat;
  const lon2 = coordinate2.long;
  const a =
    0.5 -
    Math.cos((lat2 - lat1) * p) / 2 +
    (Math.cos(lat1 * p) * Math.cos(lat2 * p) * (1 - Math.cos((lon2 - lon1) * p))) / 2;

  return 2 * r * Math.asin(Math.sqrt(a));
}
