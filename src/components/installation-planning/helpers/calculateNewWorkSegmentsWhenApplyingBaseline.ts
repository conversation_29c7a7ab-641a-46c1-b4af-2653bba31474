import { InstallationProjectJob } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { getWorkingHoursOnDate } from './workingHours';
import { differenceInHours, addDays, isWeekend } from 'date-fns';
import { tz } from '@date-fns/tz';
import { isPublicHoliday } from './dateHelpers';

function calculateNewWorkSegmentsWhenApplyingBaseline({
  newManHours,
  job,
  timeZone,
  holidays,
}: {
  newManHours: number;
  job: InstallationProjectJob;
  timeZone: string;
  holidays: Date[];
}) {
  if (newManHours <= 0) throw new Error('newManHours must be greater than 0'); // Guard clause for invalid jobDuration

  const newWorkSegments: any[] = [];

  const currentWorkSegments = job.workSegments;

  const startDate = currentWorkSegments[0]?.startTime;
  if (!startDate) throw new Error('The first work segment has no start date');

  let remainingManHours = newManHours;

  // Add the current work segments to the new work segments while the remaining man hours > 0
  currentWorkSegments.forEach((currentWorkSegment, i) => {
    if (remainingManHours <= 0) return;
    let currentDuration = currentWorkSegment.duration?.seconds ?? 0;
    // We have to check on the last one as it may e.g. only have 2 hours currently but we have extra time to add to the rest of the working day
    if (i === currentWorkSegments.length - 1) {
      if (!currentWorkSegment.startTime || !currentWorkSegment.endTime)
        throw new Error('The last work segment has no start or end date');
      const workingHours = getWorkingHoursOnDate(currentWorkSegment.startTime, timeZone);
      const remainingHoursInDay = differenceInHours(workingHours.workEndsAt, currentWorkSegment.endTime);
      const hoursToAdd = Math.min(
        remainingHoursInDay,
        remainingManHours /
          (currentWorkSegment.assignedResources.length > 0
            ? currentWorkSegment.assignedResources.length
            : job.requiredResourcesCount),
      );

      currentDuration = currentDuration + hoursToAdd * 3600;
    }

    const assignedResources =
      currentWorkSegment.assignedResources.length > 0
        ? currentWorkSegment.assignedResources.length
        : job.requiredResourcesCount;
    const manHours = (currentDuration / 3600) * assignedResources;
    const newManHoursForSegment = Math.min(manHours, remainingManHours);
    remainingManHours = remainingManHours - newManHoursForSegment;

    newWorkSegments.push({
      startTime: currentWorkSegment.startTime,
      teamId: currentWorkSegment.teamId?.value,
      duration: {
        seconds: (newManHoursForSegment / assignedResources) * 3600,
        nanos: 0,
      },
      assignedResources: currentWorkSegment.assignedResources,
    });
  });

  // Add the remaining new work segments
  const lastWorkSegment = newWorkSegments[newWorkSegments.length - 1]!;
  let currentDay = addDays(lastWorkSegment.startTime!, 1);
  const lastAssignedResources =
    currentWorkSegments[currentWorkSegments.length - 1]!.assignedResources.length &&
    currentWorkSegments[currentWorkSegments.length - 1]!.assignedResources.length > 0
      ? currentWorkSegments[currentWorkSegments.length - 1]!.assignedResources.length
      : job.requiredResourcesCount;
  while (remainingManHours > 0) {
    while (isWeekend(currentDay, { in: tz(timeZone) }) || isPublicHoliday(currentDay, holidays, timeZone)) {
      currentDay = addDays(currentDay, 1);
    }
    const workingHours = getWorkingHoursOnDate(currentDay, timeZone);
    const newManHoursForSegment = Math.min(workingHours.durationInHours * lastAssignedResources, remainingManHours);
    remainingManHours = remainingManHours - newManHoursForSegment;
    newWorkSegments.push({
      startTime: currentDay,
      duration: { seconds: (newManHoursForSegment / lastAssignedResources) * 3600, nanos: 0 },
      teamId: currentWorkSegments[currentWorkSegments.length - 1]?.teamId?.value,
      assignedResources: currentWorkSegments[currentWorkSegments.length - 1]?.assignedResources,
    });
    currentDay = addDays(currentDay, 1);
  }

  return newWorkSegments;
}

export default calculateNewWorkSegmentsWhenApplyingBaseline;
