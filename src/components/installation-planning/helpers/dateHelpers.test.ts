import { getWeekdayMetrics } from './dateHelpers';

describe('getWeekdayMetrics', () => {
  it('should return correct metrics for a standard work week', () => {
    const startDate = new Date('2024-10-07T00:00:00+01:00'); // Monday
    const endDate = new Date('2024-10-13T23:59:59+01:00'); // Sunday
    const timeZone = 'Europe/London';
    const holidays: Date[] = [];

    const metrics = getWeekdayMetrics(startDate, endDate, timeZone, holidays);

    // Test weekdaysInRange
    expect(metrics.weekdaysInRange).toHaveLength(5);
    expect(metrics.weekdaysInRange[0]!.date).toEqual(new Date('2024-10-07T00:00:00+01:00'));
    expect(metrics.weekdaysInRange[4]!.date).toEqual(new Date('2024-10-11T00:00:00+01:00'));

    // Test halfDaysInRange (2 entries per weekday)
    expect(metrics.halfDaysInRange).toHaveLength(10);
    expect(metrics.halfDaysInRange[0]).toEqual({
      date: new Date('2024-10-07T00:00:00+01:00'),
      startTime: '08',
      stringDate: '2024-10-07',
      isFriday: false,
    });
    expect(metrics.halfDaysInRange[1]).toEqual({
      date: new Date('2024-10-07T00:00:00+01:00'),
      startTime: '12',
      stringDate: '2024-10-07',
      isFriday: false,
    });

    // Test month metrics
    expect(metrics.nrOfWeekdaysPerMonth).toEqual([
      {
        month: 'October',
        year: 2024,
        numberOfWeekdays: 5,
      },
    ]);

    // Test week metrics
    expect(metrics.nrWeekdaysPerWeekNumber).toEqual([
      {
        week: '41',
        year: 2024,
        nrDays: 5,
      },
    ]);

    // Test working hours map
    expect(metrics.workingHoursForDatesMap.size).toBe(5);
  });

  it('should handle multi-month periods correctly', () => {
    const startDate = new Date('2024-09-30T00:00:00+01:00');
    const endDate = new Date('2024-11-01T23:59:59+01:00');
    const timeZone = 'Europe/London';
    const holidays: Date[] = [];

    const metrics = getWeekdayMetrics(startDate, endDate, timeZone, holidays);

    expect(metrics.nrOfWeekdaysPerMonth).toEqual([
      {
        month: 'September',
        year: 2024,
        numberOfWeekdays: 1,
      },
      {
        month: 'October',
        year: 2024,
        numberOfWeekdays: 23,
      },
      {
        month: 'November',
        year: 2024,
        numberOfWeekdays: 1,
      },
    ]);
  });

  it('should handle weekend-only periods', () => {
    const startDate = new Date('2024-12-28T00:00:00Z'); // Saturday
    const endDate = new Date('2024-12-29T23:59:59Z'); // Sunday
    const timeZone = 'Europe/London';
    const holidays: Date[] = [];

    const metrics = getWeekdayMetrics(startDate, endDate, timeZone, holidays);

    expect(metrics.weekdaysInRange).toHaveLength(0);
    expect(metrics.halfDaysInRange).toHaveLength(0);
    expect(metrics.nrOfWeekdaysPerMonth).toHaveLength(0);
    expect(metrics.nrWeekdaysPerWeekNumber).toHaveLength(0);
    expect(metrics.workingHoursForDatesMap.size).toBe(0);
  });

  it('should throw error for invalid date range', () => {
    const startDate = new Date('2024-10-10T00:00:00+02:00');
    const endDate = new Date('2024-10-01T23:59:59+02:00');
    const timeZone = 'Europe/Stockholm';
    const holidays: Date[] = [];

    expect(() => getWeekdayMetrics(startDate, endDate, timeZone, holidays)).toThrow(
      'Start date must be before end date',
    );
  });

  it('should return correct metrics for a week with a public holiday', () => {
    const startDate = new Date('2024-10-07T00:00:00+01:00'); // Monday
    const endDate = new Date('2024-10-13T23:59:59+01:00'); // Sunday
    const timeZone = 'Europe/London';
    const holidays: Date[] = [new Date('2024-10-11')];

    const metrics = getWeekdayMetrics(startDate, endDate, timeZone, holidays);

    expect(metrics.weekdaysInRange[4]!.isPublicHoliday).toBe(true);
  });
});
