import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { addDays, addHours, differenceInHours, isSameDay, isSameHour, isWeekend } from 'date-fns';
import { findIndex } from 'lodash';
import { tz } from '@date-fns/tz';
import { ContinuousWorkSegment } from '../types/planningTypes';
import { getWorkingHoursOnDate } from './workingHours';
import { isPublicHoliday } from './dateHelpers';

export default function calculateNewWorkSegments({
  startDate,
  jobDuration,
  jobBeingEdited,
  timeZone,
  holidays,
}: {
  startDate: Date;
  jobDuration: number;
  jobBeingEdited: ContinuousWorkSegment;
  timeZone: string;
  holidays: Date[];
}): InstallationProjectJob_WorkSegment[] {
  if (jobDuration <= 0) return []; // Guard clause for invalid jobDuration

  const newWorkSegments: InstallationProjectJob_WorkSegment[] = [];
  const workSegmentDuration = jobDuration;

  const jobWithAllSegments = jobBeingEdited.installationProject?.jobs.find(
    (job) => job.id?.value === jobBeingEdited.segmentDetails.jobId,
  );

  if (!jobWithAllSegments) return [];

  const { workEndsAt } = getWorkingHoursOnDate(startDate, timeZone);
  const maxLengthOfFirstDay = differenceInHours(workEndsAt, startDate);
  const lengthOfFirstDay = Math.min(maxLengthOfFirstDay, workSegmentDuration);

  let startIdxWorkSegmentGroup = 0;
  let remainingDuration = 0;

  if (
    isSameHour(jobBeingEdited.segmentDetails.startTime, startDate, { in: tz(timeZone) }) &&
    jobDuration === jobBeingEdited.segmentDetails.duration
  ) {
    return [];
  }

  // Find the start and end index of the work segment group being moved
  startIdxWorkSegmentGroup = findIndex(jobWithAllSegments.workSegments, (workSegment) =>
    isSameDay(workSegment.startTime!, jobBeingEdited.segmentDetails.startTime, { in: tz(timeZone) }),
  );
  const endIdxWorkSegmentGroup = findIndex(jobWithAllSegments.workSegments, (workSegment) =>
    isSameDay(workSegment.endTime!, jobBeingEdited.segmentDetails.endTime, { in: tz(timeZone) }),
  );

  if (startIdxWorkSegmentGroup === -1 || endIdxWorkSegmentGroup === -1) {
    return []; // Handle missing segment case appropriately
  }

  // For all work segments outside of the group being moved, just add them to the newWorkSegments array
  jobWithAllSegments.workSegments.forEach((workSegment, index) => {
    if (index < startIdxWorkSegmentGroup || index > endIdxWorkSegmentGroup) {
      newWorkSegments.push({ ...workSegment } as InstallationProjectJob_WorkSegment); // Create a shallow copy to avoid reference issues
    }
  });

  // Add the first day of the work segment group being moved
  newWorkSegments.splice(startIdxWorkSegmentGroup, 0, {
    ...(jobWithAllSegments.workSegments[startIdxWorkSegmentGroup] as InstallationProjectJob_WorkSegment),
    startTime: startDate,
    endTime: addHours(startDate, lengthOfFirstDay),
  });

  // Add the remaining work segments
  let currentIndex = startIdxWorkSegmentGroup + 1;
  let currentDay = addDays(startDate, 1);
  remainingDuration = workSegmentDuration - lengthOfFirstDay;

  while (remainingDuration > 0) {
    while (isWeekend(currentDay, { in: tz(timeZone) }) || isPublicHoliday(currentDay, holidays, timeZone)) {
      currentDay = addDays(currentDay, 1);
    }

    const workingHoursOnCurrentDay = getWorkingHoursOnDate(currentDay, timeZone);
    const lengthOfDay = Math.min(workingHoursOnCurrentDay.durationInHours, remainingDuration);
    newWorkSegments.splice(currentIndex, 0, {
      ...(jobWithAllSegments.workSegments[startIdxWorkSegmentGroup] as InstallationProjectJob_WorkSegment),
      startTime: workingHoursOnCurrentDay.workStartsAt,
      endTime: addHours(workingHoursOnCurrentDay.workStartsAt, lengthOfDay),
    });
    remainingDuration -= lengthOfDay;
    currentDay = addDays(currentDay, 1);
    currentIndex += 1;
  }

  return newWorkSegments;
}
