import { InstallationProjectJob_WorkSegment } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import calculateNewWorkSegmentsWhenApplyingBaseline from '../calculateNewWorkSegmentsWhenApplyingBaseline';

const workSegmentsNoAssignedResources = [
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    endTime: new Date('2025-05-05T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 1,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    assignedResources: [],
    plannedType: 1,
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    endTime: new Date('2025-05-06T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 2,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    assignedResources: [],
    plannedType: 1,
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    endTime: new Date('2025-05-07T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 3,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    assignedResources: [],
    plannedType: 1,
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    endTime: new Date('2025-05-08T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 4,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    assignedResources: [],
    plannedType: 1,
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    endTime: new Date('2025-05-09T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 5,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    assignedResources: [],
    plannedType: 1,
  },
];

const workSegmentsAssignedResources = [
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    endTime: new Date('2025-05-05T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 1,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    plannedType: 1,
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    endTime: new Date('2025-05-06T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 2,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    plannedType: 1,
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    endTime: new Date('2025-05-07T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 3,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    plannedType: 1,
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    endTime: new Date('2025-05-08T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 4,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    plannedType: 1,
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    status: 1,
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    endTime: new Date('2025-05-09T14:00:00.000Z'),
    timeCards: [],
    sequenceNumber: 5,
    teamId: {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
    plannedType: 1,
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
];

const job = {
  id: {
    value: '33sdfsd14-b333-4719-a00c-793cdfg070e0',
  },
  createdAt: new Date('2025-03-05T13:56:06.201Z'),
  type: 1,
  duration: {
    seconds: 144000,
    nanos: 0,
  },
  requiredRole: 1,
  requiredSkills: [1],
  status: 2,
  requiredResourcesCount: 2,
  assignedResources: [],
  assignedTeams: [
    {
      value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    },
  ],
  title: 'Frank the Tank',
  webLink: 'https://aira.my.skedulo.com/13',
};

const newWorkSegments96ManHours = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-12T06:00:00.000Z'),
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    assignedResources: [],
  },
];

const newWorkSegments64ManHours = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
];

const newWorkSegments96ManHoursWithAssignedResources = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-12T06:00:00.000Z'),
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
];

const newWorkSegments64ManHoursWithAssignedResources = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
];

const newWorkSegments64ManHoursWithAssignedResourcesWithSplit = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },

  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
  {
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    assignedResources: [
      {
        userId: {
          value: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
      {
        userId: {
          value: 'sdffdsdd-a08b-4d85-8960-1f79202f75e9',
        },
        roles: [],
        skills: [],
        skillLevel: 1,
      },
    ],
  },
];

const newWorkSegmentsHoliday = [
  {
    startTime: new Date('2025-05-05T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-06T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-07T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-08T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-09T06:00:00.000Z'),
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    assignedResources: [],
  },
  {
    startTime: new Date('2025-05-13T06:00:00.000Z'),
    duration: {
      seconds: 28800,
      nanos: 0,
    },
    teamId: '58f1c3cb-a08b-4d85-8960-1f79202f75e9',
    assignedResources: [],
  },
];

describe('calculateNewWorkSegmentsWhenApplyingBaseline', () => {
  const timeZone = 'Europe/Berlin';
  const noHolidays: Date[] = [];
  it('should return the correct segments for a job with 96 man hours and no assigned resources', () => {
    const newManHours = 96;
    const currentWorkSegments = workSegmentsNoAssignedResources;
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays: noHolidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegments96ManHours);
  });

  it('should return the correct segments for a job with 64 man hours and no assigned resources', () => {
    const newManHours = 64;
    const currentWorkSegments = workSegmentsNoAssignedResources;
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays: noHolidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegments64ManHours);
  });

  it('should return the correct segments for a job with 96 man hours with assigned resources', () => {
    const newManHours = 96;
    const currentWorkSegments = workSegmentsAssignedResources;
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays: noHolidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegments96ManHoursWithAssignedResources);
  });
  it('should return the correct segments for a job with 64 man hours with assigned resources', () => {
    const newManHours = 64;
    const currentWorkSegments = workSegmentsAssignedResources;
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays: noHolidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegments64ManHoursWithAssignedResources);
  });
  it('should return the correct segments for a job with 64 man hours with assigned resources but there is a day gap in the current work segments which should remain ', () => {
    const newManHours = 64;
    const currentWorkSegments = [
      workSegmentsAssignedResources[0],
      workSegmentsAssignedResources[1],
      workSegmentsAssignedResources[3],
      workSegmentsAssignedResources[4],
    ] as InstallationProjectJob_WorkSegment[];
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays: noHolidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegments64ManHoursWithAssignedResourcesWithSplit);
  });

  it('should return the correct segments for a job that extends over a public holiday', () => {
    const newManHours = 96;
    const currentWorkSegments = workSegmentsNoAssignedResources;
    const currentJob = { ...job, workSegments: currentWorkSegments };
    const holidays = [new Date('2025-05-12T00:00:00.000Z')];
    const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
      job: currentJob,
      timeZone,
      newManHours,
      holidays,
    });

    expect(newWorkSegments).toEqual(newWorkSegmentsHoliday);
  });
});
