import {
  addDays,
  addHours,
  differenceInHours,
  eachDayOfInterval,
  format,
  isAfter,
  isBefore,
  isFriday,
  isSameDay,
  isWeekend,
} from 'date-fns';
import { tz } from '@date-fns/tz';
import { NrOfWeekdaysPerMonth, NrWeekdaysPerWeekNumber } from '../types/planningTypes';

import { getCacheKey, getWorkingHoursOnDate, WorkingHoursOnDate } from './workingHours';

export interface WeekdayMetrics {
  weekdaysInRange: { date: Date; isFriday: boolean; isPublicHoliday: boolean }[];
  halfDaysInRange: {
    date: Date;
    startTime: '08' | '12';
    stringDate: string;
    isFriday: boolean;
  }[];
  halfDaysDateToIndexMap: Map<string, Map<'08' | '12', number>>;
  nrOfWeekdaysPerMonth: NrOfWeekdaysPerMonth[];
  nrWeekdaysPerWeekNumber: NrWeekdaysPerWeekNumber[];
  workingHoursForDatesMap: Map<string, WorkingHoursOnDate>;
}

/**
 * Analyzes a date range and returns weekday metrics including:
 * - List of weekdays
 * - Count of weekdays per month
 * - Count of weekdays per week
 */
export const getWeekdayMetrics = (
  startDate: Date,
  endDate: Date,
  timeZone: string,
  holidays: Date[],
): WeekdayMetrics => {
  if (isAfter(startDate, endDate)) {
    throw new Error('Start date must be before end date');
  }

  const weekdaysInRange: { date: Date; isFriday: boolean; isPublicHoliday: boolean }[] = [];
  const halfDaysInRange: {
    date: Date;
    startTime: '08' | '12';
    stringDate: string;
    isFriday: boolean;
  }[] = [];
  const nrOfWeekdaysPerMonth: NrOfWeekdaysPerMonth[] = [];
  const nrWeekdaysPerWeekNumber: NrWeekdaysPerWeekNumber[] = [];
  const halfDaysDateToIndexMap = new Map<string, Map<'08' | '12', number>>();

  // Track current periods
  let currentMonth = -1;
  let currentMonthName = '';
  let currentYear = 0;
  let monthWeekdays = 0;

  let currentWeek = '';
  let weekWeekdays = 0;

  // Get all days in range and filter weekdays
  const daysInRange = eachDayOfInterval({ start: startDate, end: endDate }, { in: tz(timeZone) });
  daysInRange.forEach((day) => {
    if (isWeekend(day, { in: tz(timeZone) })) return;

    const stringDate = format(day, 'yyyy-MM-dd', { in: tz(timeZone) });

    weekdaysInRange.push({
      date: day,
      isFriday: isFriday(day, { in: tz(timeZone) }),
      isPublicHoliday: isPublicHoliday(day, holidays, timeZone),
    });

    halfDaysInRange.push({
      date: day,
      startTime: '08',
      stringDate,
      isFriday: isFriday(day, { in: tz(timeZone) }),
    });
    halfDaysInRange.push({
      date: day,
      startTime: '12',
      stringDate,
      isFriday: isFriday(day, { in: tz(timeZone) }),
    });
    halfDaysDateToIndexMap.set(
      stringDate,
      new Map<'08' | '12', number>([
        ['08', halfDaysInRange.length - 2],
        ['12', halfDaysInRange.length - 1],
      ]),
    );

    // Month tracking
    const month = day.getMonth();
    if (month !== currentMonth) {
      if (currentMonth !== -1) {
        nrOfWeekdaysPerMonth.push({
          month: currentMonthName,
          year: currentYear,
          numberOfWeekdays: monthWeekdays,
        });
      }
      currentMonth = month;
      currentMonthName = day.toLocaleString('default', { month: 'long' });
      currentYear = day.getFullYear();
      monthWeekdays = 1;
    } else {
      monthWeekdays++;
    }

    // Week tracking
    const week = format(day, 'w');
    if (week !== currentWeek) {
      if (currentWeek !== '') {
        nrWeekdaysPerWeekNumber.push({
          week: currentWeek,
          year: currentYear,
          nrDays: weekWeekdays,
        });
      }
      currentWeek = week;
      weekWeekdays = 1;
    } else {
      weekWeekdays++;
    }
  });

  // Add final periods
  if (monthWeekdays > 0) {
    nrOfWeekdaysPerMonth.push({
      month: currentMonthName,
      year: currentYear,
      numberOfWeekdays: monthWeekdays,
    });
  }

  if (weekWeekdays > 0) {
    nrWeekdaysPerWeekNumber.push({
      week: currentWeek,
      year: currentYear,
      nrDays: weekWeekdays,
    });
  }

  const workingHoursForDatesMap = new Map<string, WorkingHoursOnDate>();
  weekdaysInRange.forEach(({ date }) => {
    const cacheKey = getCacheKey(date, timeZone);
    workingHoursForDatesMap.set(cacheKey, getWorkingHoursOnDate(date, timeZone));
  });

  return {
    weekdaysInRange,
    halfDaysInRange,
    nrOfWeekdaysPerMonth,
    nrWeekdaysPerWeekNumber,
    workingHoursForDatesMap,
    halfDaysDateToIndexMap,
  };
};

export function isPublicHoliday(date: Date, holidays: Date[], timeZone: string): boolean {
  return holidays.some((holiday) => isSameDay(holiday, date, { in: tz(timeZone) }));
}

export function formatDate(date: Date | undefined, timeZone: string): string {
  if (!date) return '';
  return format(date, 'EEE do MMM, HH:mm', {
    in: tz(timeZone),
  });
}

// Helper to move the time to the next workday if outside work hours
export function adjustToWorkHours({ time, timeZone }: { time: Date; timeZone: string }): Date {
  const workingHoursOnDate = getWorkingHoursOnDate(time, timeZone);
  if (isBefore(time, workingHoursOnDate.workStartsAt)) {
    return workingHoursOnDate.workStartsAt;
  }
  if (isAfter(time, workingHoursOnDate.workEndsAt)) {
    return getWorkingHoursOnDate(addDays(time, 1), timeZone).workStartsAt;
  }
  return time;
}

// Main function to calculate new end date
export function calculateNewEndDate({
  startTime,
  durationInHours,
  timeZone,
  holidays,
}: {
  startTime: Date;
  durationInHours: number;
  timeZone: string;
  holidays: Date[];
}): Date {
  let remainingHours = durationInHours;
  let currentTime = adjustToWorkHours({ time: startTime, timeZone });

  while (remainingHours > 0) {
    const workingHoursOnDate = getWorkingHoursOnDate(currentTime, timeZone);

    // Calculate how many work hours are left in the current workday
    const hoursUntilEndOfDay = differenceInHours(workingHoursOnDate.workEndsAt, currentTime);

    if (remainingHours <= hoursUntilEndOfDay) {
      // If the job finishes within the current workday, calculate the exact time
      currentTime = addHours(currentTime, remainingHours);
      remainingHours = 0;
    } else {
      // Otherwise, move to the next workday and subtract the workday hours from remainingHours
      remainingHours = remainingHours - hoursUntilEndOfDay;
      currentTime = addDays(currentTime, 1);

      // Skip weekends and public holidays
      while (isWeekend(currentTime) || isPublicHoliday(currentTime, holidays, timeZone)) {
        currentTime = addDays(currentTime, 1);
      }

      // Start at the beginning of the workday
      const workingHoursOnDate = getWorkingHoursOnDate(currentTime, timeZone);
      currentTime = workingHoursOnDate.workStartsAt;
    }
  }

  return currentTime;
}

export function isSameDayFast(d1: Date, d2: Date): boolean {
  return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
}

export function getWorkableHoursForRange(startDate: Date, endDate: Date, timeZone: string): number {
  const daysInRange = eachDayOfInterval({ start: startDate, end: endDate }, { in: tz(timeZone) });

  let workableHours = 0;

  daysInRange.forEach((day) => {
    if (isWeekend(day, { in: tz(timeZone) })) return;
    workableHours += 8;
  });

  return workableHours;
}
