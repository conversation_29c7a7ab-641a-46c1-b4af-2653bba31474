import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';

export const countryCodes = [
  {
    code: 'DE',
    grpcCode: Country.COUNTRY_DE,
  },
  {
    code: 'IT',
    grpcCode: Country.COUNTRY_IT,
  },
  {
    code: 'GB',
    grpcCode: Country.COUNTRY_GB,
  },
];

export function getCountryCode(countryGRPCCode: Country): string | undefined {
  return countryCodes.find((country) => country.grpcCode === countryGRPCCode)?.code;
}
