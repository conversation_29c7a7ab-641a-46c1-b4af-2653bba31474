import { tz } from '@date-fns/tz';
import { set } from 'date-fns';

export interface ShiftHours {
  startHour: number;
  endHour: number;
}

export type ShiftType = 'morning' | 'afternoon';

export interface ShiftTimesOnDate {
  shiftType: ShiftType;
  start: Date;
  end: Date;
  shiftHours: ShiftHours;
}

export interface WorkingHoursOnDate {
  workStartsAt: Date;
  workEndsAt: Date;
  shifts: ShiftTimesOnDate[];
  durationInHours: number;
}

// Define shifts as a const to ensure they're only calculated once
const WORKING_HOURS = [
  {
    type: 'morning' as const,
    shift: {
      startHour: 8,
      endHour: 12,
    },
  },
  {
    type: 'afternoon' as const,
    shift: {
      startHour: 12,
      endHour: 16,
    },
  },
] as const;

export const MORNING_SHIFT = WORKING_HOURS[0].shift;

// Pre-calculate day length since it's static
const DAY_LENGTH_HOURS = WORKING_HOURS.reduce((acc, { shift }) => acc + shift.endHour - shift.startHour, 0);

// Cache for shift calculations
const shiftTimeCache = new Map<string, WorkingHoursOnDate>();

// Helper to generate cache key
export const getCacheKey = (date: Date, timeZone: string): string => `${date.toISOString().split('T')[0]}-${timeZone}`;

// Optimized to reduce date-fns operations
const calculateAbsoluteShiftTimes = (
  relativeToDate: Date,
  timeSlot: ShiftHours,
  timeZone: string,
): { start: Date; end: Date } => ({
  start: set(
    relativeToDate,
    {
      hours: timeSlot.startHour,
      minutes: 0,
      seconds: 0,
      milliseconds: 0,
    },
    { in: tz(timeZone) },
  ),
  end: set(
    relativeToDate,
    {
      hours: timeSlot.endHour,
      minutes: 0,
      seconds: 0,
      milliseconds: 0,
    },
    { in: tz(timeZone) },
  ),
});

export const getWorkingHoursOnDate = (date: Date, timeZone: string): WorkingHoursOnDate => {
  const cacheKey = getCacheKey(date, timeZone);
  const cached = shiftTimeCache.get(cacheKey);

  if (cached) {
    return cached;
  }

  const shifts = WORKING_HOURS.map(({ type, shift }) => {
    const { start, end } = calculateAbsoluteShiftTimes(date, shift, timeZone);
    return {
      shiftType: type,
      start,
      end,
      shiftHours: shift,
    };
  });

  const result = {
    shifts,
    workStartsAt: shifts[0]!.start,
    workEndsAt: shifts[shifts.length - 1]!.end,
    durationInHours: DAY_LENGTH_HOURS,
  };

  // Cache the result
  shiftTimeCache.set(cacheKey, result);

  return result;
};

// Clear cache when needed (e.g., when component unmounts)
export const clearWorkingHoursCache = (): void => {
  shiftTimeCache.clear();
};
