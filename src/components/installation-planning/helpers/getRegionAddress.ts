type RegionAddress = {
  id: string;
  name: string;
  address: string;
  geometry: {
    lat: number;
    long: number;
  } | null;
};

const RegionAddresses: RegionAddress[] = [
  {
    id: '6e866be7-5c0e-47e1-bd11-28a5372094fa',
    name: 'Berlin',
    address: 'Rudolfstrasse 17, 10245 Berlin, DE',
    geometry: {
      lat: 52.50462,
      long: 13.45234,
    },
  },
  {
    id: '0c75d794-f5c9-4ffb-9a94-4ccba0bc703d',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    address: 'Dieselstrasse 8, 08371 Glauchau, DE',
    geometry: {
      lat: 50.84059,
      long: 12.53299,
    },
  },
  {
    id: '9ab5ee08-944c-457b-b9b3-2c623009f5fb',
    name: 'Munich',
    address: 'Innstrasse 9, 85640 Putzbrunn, DE',
    geometry: {
      lat: 48.0797,
      long: 11.69214,
    },
  },
  {
    id: '03496ad8-1e67-4586-a548-6b19add191b9',
    name: 'Nuremberg',
    address: 'Einsteinstrasse 6, 91074 <PERSON>, DE',
    geometry: {
      lat: 49.57722,
      long: 10.88462,
    },
  },
  {
    id: '8f0da4ec-a41d-4e40-8daf-fe1afa9703c8',
    name: 'Hamburg',
    address: 'Hermann-Buck Weg 19, 22309 Hamburg, DE',
    geometry: {
      lat: 53.606174,
      long: 10.051942,
    },
  },
  {
    id: '852d6e19-7119-4f5c-a37f-1d77f5c90805',
    name: 'Frankfurt',
    address: 'Dornhofstraße 71, 63263 Neu-Isenburg, DE',
    geometry: {
      lat: 50.0456616,
      long: 8.6814961,
    },
  },

  {
    id: 'b8c802b6-bf94-4df2-b465-bed311978b05',
    name: 'Northrine-Westfalia',
    address: 'Wankelstraße 35, 50996 Köln, DE',
    geometry: {
      lat: 50.870502,
      long: 6.994628,
    },
  },
  {
    id: 'ec9b559b-5e29-4e35-907d-7810fb0804e5',
    name: 'South East',
    address: 'Unit P, Acorn Industrial Park, Crayford, DA1 4AL',
    geometry: {
      lat: 51.45189,
      long: 0.1816,
    },
  },
  {
    id: '5ea00260-47ce-4d12-98e3-8d945ab40695',
    name: 'Yorkshire',
    address: '1A Meadowbrook Park, Halfway, Sheffield S20 3PJ ',
    geometry: {
      lat: 53.3291113,
      long: -1.3333089,
    },
  },
  {
    id: 'e7971ab1-9584-47de-8592-3e53f79bc085',
    name: 'North West',
    address: 'Unit 14, Mercury Park, Trafford Park, M417LJ',
    geometry: {
      lat: 53.4707236,
      long: -2.3230574,
    },
  },
  {
    id: 'e081c1ff-351d-457a-af9d-f5d2fdeec547',
    name: 'South West',
    address: 'Bee House, 140 Eastern Ave, Park Dr, Milton, Abingdon OX14 4SB',
    geometry: {
      lat: 51.6226087,
      long: -1.2850623,
    },
  },
  {
    id: '44e62c89-5af9-4880-9f53-261752cc8d1d',
    name: 'Midlands',
    address: '',
    geometry: null,
  },
  {
    id: '6d4318c6-878f-4870-9e91-f70551df3cd9',
    name: 'Scotland',
    address: 'Unit 1, 16 Munro Road, Stirling, FK7 7UU',
    geometry: {
      lat: 56.1152382,
      long: -3.9115245,
    },
  },
  {
    id: 'c85c214d-6088-4e88-a49c-cd928cffb9b1',
    name: 'Marche',
    address: 'Via Della Repubblica, 5, 60020 Sirolo, AN',
    geometry: {
      lat: 43.5121447,
      long: 13.6035152,
    },
  },
  {
    id: '2e3fd6c2-f779-4332-b336-cac3083017c1',
    name: 'Lazio',
    address: 'Via Eugenio Barsanti, 27 00012 Albuccione, RM',
    geometry: {
      lat: 41.9427341,
      long: 12.6767205,
    },
  },
  {
    id: '40d80114-515c-4368-aaed-0e994a2c5983',
    name: 'Lombardia',
    address: 'Lainate, via Ramazzotti 12',
    geometry: {
      lat: 45.5767124,
      long: 9.0408554,
    },
  },
  {
    id: 'bb95217d-54f8-4ccd-8807-3661e3d59a08',
    name: 'Emilia',
    address: '',
    geometry: null,
  },
  {
    id: '8cada866-7252-4d5f-b390-24b5e7670672',
    name: 'Veneto',
    address: '',
    geometry: null,
  },
];

const getRegionAddress = (regionId: string) => RegionAddresses.find((region) => region.id === regionId);
export default getRegionAddress;
