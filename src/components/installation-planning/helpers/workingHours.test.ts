import { getWorkingHoursOnDate, WorkingHoursOnDate } from './workingHours';

describe('getWorkingHoursOnDate', () => {
  it('should return correct working hours for the given date in GMT', () => {
    const timeZone = 'Europe/London';
    const date = new Date('2024-11-10T00:00:00Z');
    const workingHours: WorkingHoursOnDate = getWorkingHoursOnDate(date, timeZone);

    expect(workingHours.shifts).toHaveLength(2);

    const morningShift = workingHours.shifts[0]!;
    expect(morningShift.shiftType).toBe('morning');
    expect(morningShift.start).toEqual(new Date('2024-11-10T08:00:00Z'));
    expect(morningShift.end).toEqual(new Date('2024-11-10T12:00:00Z'));

    const afternoonShift = workingHours.shifts[1]!;
    expect(afternoonShift.shiftType).toBe('afternoon');
    expect(afternoonShift.start).toEqual(new Date('2024-11-10T12:00:00Z'));
    expect(afternoonShift.end).toEqual(new Date('2024-11-10T16:00:00Z'));

    expect(workingHours.workStartsAt).toEqual(morningShift.start);
    expect(workingHours.workEndsAt).toEqual(afternoonShift.end);
    expect(workingHours.durationInHours).toBe(8);
  });

  it('should return correct working hours for the given date in summer time', () => {
    const timeZone = 'Europe/London';
    const date = new Date('2024-07-10T23:00:00+01:00');
    const workingHours: WorkingHoursOnDate = getWorkingHoursOnDate(date, timeZone);

    expect(workingHours.shifts).toHaveLength(2);

    const morningShift = workingHours.shifts[0]!;
    expect(morningShift.shiftType).toBe('morning');
    expect(morningShift.start).toEqual(new Date('2024-07-10T08:00:00+01:00'));
    expect(morningShift.end).toEqual(new Date('2024-07-10T12:00:00+01:00'));

    const afternoonShift = workingHours.shifts[1]!;
    expect(afternoonShift.shiftType).toBe('afternoon');
    expect(afternoonShift.start).toEqual(new Date('2024-07-10T12:00:00+01:00'));
    expect(afternoonShift.end).toEqual(new Date('2024-07-10T16:00:00+01:00'));

    expect(workingHours.workStartsAt).toEqual(morningShift.start);
    expect(workingHours.workEndsAt).toEqual(afternoonShift.end);
    expect(workingHours.durationInHours).toBe(8);
  });

  it('should return correct working hours for the given date in a negative timezone', () => {
    const timeZone = 'America/New_York';
    const date = new Date('2023-10-10T00:00:00-04:00');
    const workingHours: WorkingHoursOnDate = getWorkingHoursOnDate(date, timeZone);

    expect(workingHours.shifts).toHaveLength(2);

    const morningShift = workingHours.shifts[0]!;
    expect(morningShift.shiftType).toBe('morning');
    expect(morningShift.start).toEqual(new Date('2023-10-10T08:00:00-04:00'));
    expect(morningShift.end).toEqual(new Date('2023-10-10T12:00:00-04:00'));

    const afternoonShift = workingHours.shifts[1]!;
    expect(afternoonShift.shiftType).toBe('afternoon');
    expect(afternoonShift.start).toEqual(new Date('2023-10-10T12:00:00-04:00'));
    expect(afternoonShift.end).toEqual(new Date('2023-10-10T16:00:00-04:00'));

    expect(workingHours.workStartsAt).toEqual(morningShift.start);
    expect(workingHours.workEndsAt).toEqual(afternoonShift.end);
    expect(workingHours.durationInHours).toBe(8);
  });
});
