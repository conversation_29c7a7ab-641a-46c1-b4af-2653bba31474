import { Origin, Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';

/**
 * Parses a team name string to extract prefix, number, and suffix
 * Used for natural sorting of team names like "Team 1 Alpha", "Team 2 Beta"
 */
function extractTeamNameParts(str: string) {
  // A regex to capture optional leading text, a number, and trailing text
  const match = str.match(/^(.*?)\b(\d+)\b(.*)$/);
  if (match) {
    return {
      prefix: match[1],
      number: parseInt(match[2]!, 10),
      suffix: match[3],
    };
  }
  return { prefix: str, number: null, suffix: '' };
}

export function getTeamsForRequiredRole(teams: Team[], requiredRole: number) {
  return teams
    .filter((team) => team.role === requiredRole)
    .toSorted((a, b) => {
      const aParts = extractTeamNameParts(a.name);
      const bParts = extractTeamNameParts(b.name);

      const aIsAiraTeam = a.origin === Origin.ORIGIN_AIRA;
      const bIsAiraTeam = b.origin === Origin.ORIGIN_AIRA;

      // First sort by origin
      if (aIsAiraTeam && !bIsAiraTeam) return -1;
      if (!aIsAiraTeam && bIsAiraTeam) return 1;

      // If both have a number and the same prefix & suffix
      if (
        aParts.number !== null &&
        bParts.number !== null &&
        aParts.prefix === bParts.prefix &&
        aParts.suffix === bParts.suffix
      ) {
        // Compare by numeric value
        return aParts.number - bParts.number;
      }

      // Otherwise, compare alphabetically
      const cmp = a.name.localeCompare(b.name);
      if (cmp !== 0) return cmp;

      // If alphabetically identical, compare by length to ensure shorter first
      return a.name.length - b.name.length;
    });
}
