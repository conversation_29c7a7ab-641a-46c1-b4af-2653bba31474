import { getHours, format } from 'date-fns';
import { tz } from '@date-fns/tz';
import { ContinuousWorkSegment } from './types/planningTypes';
import { MORNING_SHIFT } from './helpers/workingHours';
import { useDateRange } from './contexts/DateRangeContext';
import { useRegionContext } from '../../context/RegionContext';
import { useMemo } from 'react';

export default function useCreateGridForResource({
  continuousWorkSegmentsWithoutTeam,
}: {
  continuousWorkSegmentsWithoutTeam: ContinuousWorkSegment[];
}): ({ continuousWorkSegment: ContinuousWorkSegment; span: number } | null | 'taken')[][] {
  const { weekdayMetrics, dateIndexMap } = useDateRange();
  const { weekdaysInRange } = weekdayMetrics;
  const daysInInterval = weekdaysInRange.length;
  const { timeZone } = useRegionContext();

  return useMemo(() => {
    // 2. Sort all segments by startTime once
    const continuousWorkSegments = [...continuousWorkSegmentsWithoutTeam].sort(
      (a, b) => a.segmentDetails.startTime.getTime() - b.segmentDetails.startTime.getTime(),
    );

    // Create the initial grid with 1 row
    const grid = [new Array(daysInInterval * 2).fill(null)];

    // 3. Iterate through segments in sorted order and place them
    for (const segment of continuousWorkSegments) {
      const { startTime, endTime } = segment.segmentDetails;

      // --- Find the half-day index for start ---
      const startDayKey = format(startTime, 'yyyy-MM-dd', { in: tz(timeZone) });
      const startDayIndex = dateIndexMap.get(startDayKey);
      // If it's not in range, skip or handle as needed
      if (startDayIndex === undefined) {
        continue;
      }
      // Determine if it starts in the morning half or afternoon
      const startsInFirstHalf = getHours(startTime, { in: tz(timeZone) }) < MORNING_SHIFT.endHour;
      const startIdx = startDayIndex * 2 + (startsInFirstHalf ? 0 : 1);

      // --- Find the half-day index for end ---
      const endDayKey = format(endTime, 'yyyy-MM-dd', { in: tz(timeZone) });
      const endDayIndex = dateIndexMap.get(endDayKey);
      // If it's not in range, skip or handle as needed
      if (endDayIndex === undefined) {
        continue;
      }
      // Determine if it ends in the morning half or afternoon
      const endsInSecondHalf = getHours(endTime, { in: tz(timeZone) }) > MORNING_SHIFT.endHour;
      const endIdx = endDayIndex * 2 + (endsInSecondHalf ? 1 : 0);

      // The total number of half-day cells to occupy (inclusive range)
      const span = endIdx - startIdx + 1;

      // 4. Try to place the segment in an existing row or a new row
      let placed = false;
      let row = 0;
      while (!placed) {
        // If we've reached the bottom row (no more rows), append a new empty row
        if (row === grid.length) {
          grid.push(new Array(daysInInterval * 2).fill(null));
        }
        // Check if all cells in [startIdx, startIdx+span) are free
        let canPlace = true;
        for (let col = startIdx; col < startIdx + span; col++) {
          if (grid[row]![col] !== null) {
            canPlace = false;
            break;
          }
        }
        if (canPlace) {
          // Place the segment
          grid[row]![startIdx] = { continuousWorkSegment: segment, span };
          // Mark the spanned columns as taken
          for (let i = 1; i < span; i++) {
            grid[row]![startIdx + i] = 'taken';
          }
          placed = true;
        } else {
          row++;
        }
      }
    }

    // Push one more empty row, if desired for layout
    grid.push(new Array(daysInInterval * 2).fill(null));

    return grid;
  }, [continuousWorkSegmentsWithoutTeam, dateIndexMap, daysInInterval, timeZone]);
}
