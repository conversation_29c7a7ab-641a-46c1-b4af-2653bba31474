import React, { createContext, useContext, useState, useMemo } from 'react';
import { addWeeks, format, nextFriday, startOfWeek, startOfToday, subWeeks } from 'date-fns';
import { tz } from '@date-fns/tz';
import { useRegionContext } from '../../../context/RegionContext';
import { DateRange } from '../topBar/DateRangePicker';
import { getWeekdayMetrics, WeekdayMetrics } from '../helpers/dateHelpers';
import { usePublicHolidays } from './PublicHolidaysContext';
interface DateRangeContextType {
  dateRange: DateRange;
  setDateRange: (dateRange: DateRange) => void;
  weekdayMetrics: WeekdayMetrics;
  dateIndexMap: Map<string, number>;
}

const DateRangeContext = createContext<DateRangeContextType | undefined>(undefined);

export function DateRangeProvider({ children }: { children: React.ReactNode }) {
  const { timeZone } = useRegionContext();
  const { holidays } = usePublicHolidays();

  // Date range state
  const [dateRange, setDateRange] = useState<DateRange>(() => ({
    startDate: subWeeks(startOfWeek(startOfToday({ in: tz(timeZone) }), { in: tz(timeZone), weekStartsOn: 1 }), 1, {
      in: tz(timeZone),
    }),
    endDateInclusive: addWeeks(
      subWeeks(nextFriday(startOfToday({ in: tz(timeZone) }), { in: tz(timeZone) }), 1, {
        in: tz(timeZone),
      }),
      26,
    ),
  }));

  // Weekday metrics calculation
  const weekdayMetrics = useMemo(
    () => getWeekdayMetrics(dateRange.startDate, dateRange.endDateInclusive, timeZone, holidays),
    [dateRange.startDate, dateRange.endDateInclusive, timeZone, holidays],
  );

  // Build a map from YYY-MM-DD in timeZone -> index in weekdaysInRange
  const dateIndexMap = React.useMemo(() => {
    const map = new Map<string, number>();
    weekdayMetrics.weekdaysInRange.forEach(({ date }, idx) => {
      const tzDate = format(date, 'yyyy-MM-dd', { in: tz(timeZone) });
      map.set(tzDate, idx);
    });
    return map;
  }, [timeZone, weekdayMetrics.weekdaysInRange]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      dateRange,
      setDateRange,
      weekdayMetrics,
      dateIndexMap,
    }),
    [dateRange, setDateRange, weekdayMetrics, dateIndexMap],
  );

  return <DateRangeContext.Provider value={contextValue}>{children}</DateRangeContext.Provider>;
}

export function useDateRange() {
  const context = useContext(DateRangeContext);
  if (context === undefined) {
    throw new Error('useDateRange must be used within a DateRangeProvider');
  }
  return context;
}
