import { Stack, Typography } from '@mui/material';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { createContext, useContext, useMemo } from 'react';
import { QueryObserverResult } from '@tanstack/react-query';
import { inferProcedureOutput } from '@trpc/server';
import { AppRouter } from 'server/api/root';
import { TRPCClientErrorLike } from '@trpc/client/src/TRPCClientError';
import { useRegionContext } from '../../../context/RegionContext';

type ProjectsUpdatedSinceOutput = inferProcedureOutput<AppRouter['InstallationProject']['getInstallationProjects']>;

interface ProjectsUpdatedSinceContextType {
  projectsUpdatedSince: ProjectsUpdatedSinceOutput;
  isLoadingProjectsUpdatedSince: boolean;
  refetchProjectsUpdatedSince: () => Promise<
    QueryObserverResult<ProjectsUpdatedSinceOutput, TRPCClientErrorLike<AppRouter>>
  >;
  initialLoadTime: Date;
}

const ProjectsUpdatedSinceContext = createContext<ProjectsUpdatedSinceContextType | undefined>(undefined);

export function ProjectsUpdatedSinceProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();

  // Create an immutable constant that captures the initial load time
  const initialLoadTime = useMemo(() => new Date(), []);

  const {
    data: projectsUpdatedSince,
    isLoading: isLoadingProjectsUpdatedSince,
    refetch: refetchProjectsUpdatedSince,
  } = api.InstallationProject.getInstallationProjects.useQuery(
    {
      operationalUnitId: regionId!.value,
      updatedAfter: initialLoadTime,
    },
    {
      refetchOnWindowFocus: false,
      refetchInterval: 1000 * 60 * 2, // 5 minutes
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    },
  );

  const value = useMemo(
    () => ({
      projectsUpdatedSince: projectsUpdatedSince || [],
      isLoadingProjectsUpdatedSince,
      refetchProjectsUpdatedSince,
      initialLoadTime,
    }),
    [projectsUpdatedSince, isLoadingProjectsUpdatedSince, refetchProjectsUpdatedSince, initialLoadTime],
  );

  if (!isLoadingProjectsUpdatedSince && !projectsUpdatedSince) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">
          <FormattedMessage id="common.notify.error" />
        </Typography>
        <HeatPump />
      </Stack>
    );
  }

  return <ProjectsUpdatedSinceContext.Provider value={value}>{children}</ProjectsUpdatedSinceContext.Provider>;
}

export function useProjectsUpdatedSince() {
  const context = useContext(ProjectsUpdatedSinceContext);
  if (context === undefined) {
    throw new Error('useProjectsUpdatedSince must be used within a ProjectsUpdatedSinceProvider');
  }
  return context;
}
