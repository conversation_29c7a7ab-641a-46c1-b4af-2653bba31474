import { api } from 'utils/api';
import { createContext, useContext, useMemo } from 'react';
import { UserIdentityView } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useRegionContext } from '../../../context/RegionContext';
import { ResourceForRegion, resourceMapping } from '../types/planningTypes';

// Types
type ResourcesMap = Map<string, ResourceForRegion>;

// Loading State Context
type LoadingState = {
  isLoadingResources: boolean;
  isLoadingUserData: boolean;
};

const LoadingContext = createContext<LoadingState>({
  isLoadingResources: false,
  isLoadingUserData: false,
});

// Resources Data Context
const ResourcesDataContext = createContext<ResourcesMap>(new Map());

// Custom hooks for each context
export function useResourcesLoading() {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useResourcesLoading must be used within a ResourcesProvider');
  }
  return context;
}

export function useResources() {
  const context = useContext(ResourcesDataContext);
  if (!context) {
    throw new Error('useResourcesData must be used within a ResourcesProvider');
  }
  return context;
}

export function ResourcesProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();

  // Memoize query options
  const queryOptions = useMemo(
    () => ({
      refetchInterval: 1000 * 60 * 5,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    }),
    [],
  );

  // Resources query
  const { data: resourcesData, isLoading: isLoadingResources } = api.Resource.getResourcesForRegion.useQuery(
    { regionId: regionId!.value },
    queryOptions,
  );

  // Memoize resource IDs
  const resourcesIds = useMemo(
    () =>
      resourcesData
        ?.filter((resource) => {
          return (
            resource.roles.some((role) => role.role?.$case === 'plumberRole') ||
            resource.roles.some((role) => role.role?.$case === 'electricianRole') ||
            resource.roles.some((role) => role.role?.$case === 'landscaperRole')
          );
        })
        .map((resource) => resource.userId!.value) ?? [],
    [resourcesData],
  );

  // Resource info query
  const { data: resourcesInfoData, isLoading: isLoadingResourceData } = api.Resource.getResourceInfo.useQuery(
    { userIds: resourcesIds, view: UserIdentityView.USER_IDENTITY_VIEW_FULL },
    {
      enabled: resourcesIds.length > 0,
    },
  );

  // Memoize the resources map
  const resourcesForRegion = useMemo(() => {
    if (!resourcesInfoData?.length || !resourcesData?.length) {
      return new Map();
    }

    return new Map(
      resourcesInfoData.map((resource) => {
        const resourceData = resourcesData.find((r) => r.userId!.value === resource.userId!.value);
        const resourceRoles = resourceData?.roles
          .map((role) => {
            switch (true) {
              case role.role?.$case === 'plumberRole':
                return resourceMapping.INSTALLER;
              case role.role?.$case === 'electricianRole':
                return resourceMapping.ELECTRICIAN;
              case role.role?.$case === 'landscaperRole':
                return resourceMapping.LANDSCAPER;
              default:
                return null;
            }
          })
          .filter((role) => role !== null);
        return [
          resource.userId!.value,
          resourceData ? { ...resource, ...resourceData, roles: resourceRoles } : resource,
        ];
      }),
    );
  }, [resourcesInfoData, resourcesData]);

  // Memoize loading state
  const loadingState = useMemo(
    () => ({
      isLoadingResources,
      isLoadingUserData: isLoadingResourceData,
    }),
    [isLoadingResources, isLoadingResourceData],
  );

  return (
    <LoadingContext.Provider value={loadingState}>
      <ResourcesDataContext.Provider value={resourcesForRegion}>{children}</ResourcesDataContext.Provider>
    </LoadingContext.Provider>
  );
}
