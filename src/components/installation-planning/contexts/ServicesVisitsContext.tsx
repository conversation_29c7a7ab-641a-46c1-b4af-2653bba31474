import { createContext, useContext, useMemo } from 'react';
import { ServiceVisit } from '@aira/service-visit-grpc-api/build/ts_out/com/aira/acquisition/contract/service/visit/v1/model';
import { api } from 'utils/api';
import { useRegionContext } from '../../../context/RegionContext';
import { format, getMinutes, getHours } from 'date-fns';
import { tz } from '@date-fns/tz';
import { useDateRange } from './DateRangeContext';

export type ServiceVisitWithTime = ServiceVisit & {
  startDateIndex: number;
  startDate: Date;
  startHour: number;
  startMinutes: number;
};

type ServicesVisitsContextType = {
  serviceVisitsForResourcesMap: Map<string, ServiceVisitWithTime[]>;
  isLoadingServiceVisits: boolean;
};

const ServicesVisitsContext = createContext<ServicesVisitsContextType | undefined>(undefined);

export function ServicesVisitsProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId, timeZone } = useRegionContext();

  const { dateIndexMap } = useDateRange();
  const { data: serviceVisits, isLoading: isLoadingServiceVisits } = api.ServiceVisits.getServiceVisits.useQuery(
    {
      operationalUnitId: regionId!.value,
    },
    {
      refetchInterval: 1000 * 60 * 5, // 5 minutes
    },
  );

  const serviceVisitsForResourcesMap = useMemo(() => {
    if (isLoadingServiceVisits && !serviceVisits) {
      return new Map<string, ServiceVisitWithTime[]>();
    }
    if (!serviceVisits) {
      return new Map<string, ServiceVisitWithTime[]>();
    }
    const map = new Map<string, ServiceVisitWithTime[]>();

    serviceVisits?.serviceVisits.forEach((visit) => {
      visit.jobState?.assignees.forEach((resource) => {
        if (resource.user?.$case === 'knownUser') {
          const userId = resource.user.knownUser.userId?.value;
          if (userId) {
            const startDate = visit.jobState?.startTime;
            if (startDate) {
              const dateKey = format(startDate, 'yyyy-MM-dd');
              const startDateIndex = dateIndexMap.get(dateKey);
              const startHour = getHours(startDate, { in: tz(timeZone) });
              const startMinutes = getMinutes(startDate, { in: tz(timeZone) });
              if (startDateIndex !== undefined) {
                const existingVisits = map.get(userId) || [];
                const newVisit: ServiceVisitWithTime = {
                  ...visit,
                  startDateIndex,
                  startDate,
                  startHour,
                  startMinutes,
                };
                map.set(userId, [...existingVisits, newVisit]);
              }
            }
          }
        }
      });
    });
    return map;
  }, [isLoadingServiceVisits, serviceVisits, timeZone, dateIndexMap]);

  const value = useMemo(
    () => ({ serviceVisitsForResourcesMap, isLoadingServiceVisits }),
    [serviceVisitsForResourcesMap, isLoadingServiceVisits],
  );

  return <ServicesVisitsContext.Provider value={value}>{children}</ServicesVisitsContext.Provider>;
}

export function useServicesVisits() {
  const context = useContext(ServicesVisitsContext);
  if (!context) {
    throw new Error('useServicesVisits must be used within a ServicesVisitsProvider');
  }
  return context;
}
