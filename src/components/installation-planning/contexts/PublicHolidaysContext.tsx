import { createContext, useContext, useMemo } from 'react';
import { api } from 'utils/api';
import { addYears, endOfYear, startOfYear } from 'date-fns';
import { QueryObserverResult } from '@tanstack/react-query';
import { inferProcedureOutput } from '@trpc/server';
import { AppRouter } from 'server/api/root';
import { TRPCClientErrorLike } from '@trpc/client';
import { useRegionContext } from 'context/RegionContext';
import { NationalHoliday, RegionalHoliday } from '../types/planningTypes';
import { TZDate } from '@date-fns/tz';

type FindHolidaysOutput = inferProcedureOutput<AppRouter['Resource']['findHolidays']>;

interface PublicHolidaysContextType {
  holidays: Date[];
  regionalHolidays: RegionalHoliday[];
  nationalHolidays: NationalHoliday[];
  refetchFindHolidays: () => Promise<QueryObserverResult<FindHolidaysOutput, TRPCClientErrorLike<AppRouter>>>;
}

export const PublicHolidaysContext = createContext<PublicHolidaysContextType | undefined>(undefined);

export function PublicHolidaysProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId, timeZone } = useRegionContext();

  const today = new Date();

  const startDate = startOfYear(today);
  const endDate = addYears(endOfYear(today), 1);

  const { data, refetch } = api.Resource.findHolidays.useQuery(
    {
      regionId: regionId!.value,
      from: startDate,
      to: endDate,
    },
    { enabled: !!regionId?.value },
  );

  const nationalHolidays = useMemo(
    () =>
      data
        ?.filter((holiday) => holiday.type?.$case === 'nationalHoliday')
        .map((holiday) => ({
          date: new TZDate(holiday.day!.year, holiday.day!.month - 1, holiday.day!.day, timeZone),
        })) ?? [],
    [data, timeZone],
  );

  const regionalHolidays = useMemo(
    () =>
      data
        ?.filter((holiday) => holiday.type?.$case === 'regionalHoliday')
        .map((holiday) => ({
          id: holiday.identifier?.$case === 'id' ? holiday.identifier!.id.value : '',
          date: new TZDate(holiday.day!.year, holiday.day!.month - 1, holiday.day!.day, timeZone),
        })) ?? [],
    [data, timeZone],
  );

  const holidays = useMemo(
    () =>
      (regionalHolidays.map((holiday) => holiday.date) as Date[]).concat(
        nationalHolidays.map((holiday) => holiday.date) as Date[],
      ),
    [nationalHolidays, regionalHolidays],
  );

  const contextValue = useMemo(
    () => ({ holidays, nationalHolidays, regionalHolidays, refetchFindHolidays: refetch }),
    [holidays, nationalHolidays, regionalHolidays, refetch],
  );

  return <PublicHolidaysContext.Provider value={contextValue}>{children}</PublicHolidaysContext.Provider>;
}

export const usePublicHolidays = () => {
  const context = useContext(PublicHolidaysContext);
  if (!context) {
    throw new Error('usePublicHolidays must be used within a PublicHolidaysProvider');
  }
  return context;
};
