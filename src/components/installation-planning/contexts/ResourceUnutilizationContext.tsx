import React, { createContext, useContext, useMemo } from 'react';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { useRegionContext } from '../../../context/RegionContext';
import { useDateRange } from './DateRangeContext';
import { api } from 'utils/api';
import { useResources } from './ResourcesContext';
import { getHours, format } from 'date-fns';
import { QueryObserverResult } from '@tanstack/react-query';
import { AppRouter } from 'server/api/root';
import { inferProcedureOutput } from '@trpc/server';
import { TRPCClientErrorLike } from '@trpc/client';
import { ConnectingSlot, InactivityMap } from '../table/resourcesView/Inactivities/types';
import { calculateConnectingInactivities } from '../table/resourcesView/Inactivities/calculateConnectingInactivies';
import { tz } from '@date-fns/tz';

type ResourceUnutilizationOutput = inferProcedureOutput<AppRouter['Resource']['getResourceUnutilizationByRegionId']>;

interface ResourceUnutilizationContextType {
  resourceUnutilizationMap: Map<string, InactivityMap>;
  connectInactivitiesPerResource: Map<string, ConnectingSlot[]>;
  refetchResourceUnutilization: () => Promise<
    QueryObserverResult<ResourceUnutilizationOutput, TRPCClientErrorLike<AppRouter>>
  >;
  isLoadingResourceUnutilization: boolean;
}

const ResourceUnutilizationContext = createContext<ResourceUnutilizationContextType | undefined>(undefined);

export function ResourceUnutilizationProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId, timeZone } = useRegionContext();
  const { dateRange } = useDateRange();
  const resourcesForRegion = useResources();
  const { weekdayMetrics } = useDateRange();
  const { halfDaysInRange } = weekdayMetrics;

  const { data, refetch, isLoading } = api.Resource.getResourceUnutilizationByRegionId.useQuery({
    regionId: regionId!.value,
    from: dateRange.startDate,
    to: dateRange.endDateInclusive,
  });

  const resourceUnutilizationMap = React.useMemo(() => {
    const map = new Map<string, InactivityMap>();
    resourcesForRegion.forEach((resource) => {
      map.set(resource.userId!.value, new Map());
    });
    if (data) {
      data.utilizationDeviations.forEach((utilizationDeviation) => {
        const userId = utilizationDeviation.userId!.value;
        const resource = map.get(userId);
        if (resource) {
          utilizationDeviation.unutilizedTimeSlots.forEach((unutilizedTimeSlot) => {
            const slot = {
              regionId: unutilizedTimeSlot.operationalUnitId?.value,
              reason: unutilizedTimeSlot.unutilizedType!.type!.$case!,
              startTime: getHours(unutilizedTimeSlot.from!, { in: tz(timeZone) }),
              endTime: getHours(unutilizedTimeSlot.to!, { in: tz(timeZone) }),
              id: unutilizedTimeSlot.id!.value!,
            };
            resource.set(
              `${format(unutilizedTimeSlot.from!, 'yyyy-MM-dd')}-${(
                '0' +
                getHours(unutilizedTimeSlot.from!, {
                  in: tz(timeZone),
                })
              ).slice(-2)}`,
              slot,
            );
          });
          utilizationDeviation.unavailableTimeSlots.forEach((unavailability) => {
            const slot = {
              reason: unavailability.unavailabilityType!.type!.$case!,
              description:
                unavailability.unavailabilityType!.type!.$case === 'other'
                  ? unavailability.unavailabilityType!.type?.other!.description
                  : undefined,
              startTime: getHours(unavailability.from!, { in: tz(timeZone) }),
              endTime: getHours(unavailability.to!, { in: tz(timeZone) }),
              id: unavailability.id!.value,
            };
            resource.set(
              `${format(unavailability.from!, 'yyyy-MM-dd')}-${(
                '0' +
                getHours(unavailability.from!, {
                  in: tz(timeZone),
                })
              ).slice(-2)}`,
              slot,
            );
          });
          utilizationDeviation.holidays.forEach((holiday) => {
            const dateString = format(
              new Date(holiday.day!.year, holiday.day!.month - 1, holiday.day!.day),
              'yyyy-MM-dd',
            );
            const slot = {
              reason: 'holiday' as const,
              startTime: 8,
              endTime: 16,
              id: holiday.identifier?.$case === 'id' ? holiday.identifier!.id.value : `${userId}-holiday-${dateString}`,
            };
            resource.set(`${dateString}-08`, slot);
          });
        }
      });
    }
    return map;
  }, [data, resourcesForRegion, timeZone]);

  const connectInactivitiesPerResource = useMemo(() => {
    const map = new Map<string, ConnectingSlot[]>();
    resourceUnutilizationMap.forEach((inactivities, userId) => {
      const connectingSlots = calculateConnectingInactivities({ inactivities, halfDaysInRange });
      map.set(userId, connectingSlots);
    });
    return map;
  }, [resourceUnutilizationMap, halfDaysInRange]);

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(
    () => ({
      resourceUnutilizationMap,
      connectInactivitiesPerResource,
      refetchResourceUnutilization: refetch,
      isLoadingResourceUnutilization: isLoading,
    }),
    [resourceUnutilizationMap, connectInactivitiesPerResource, refetch, isLoading],
  );

  // Loading state
  const hasError = !isLoading && !data;

  if (isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">
          <FormattedMessage id="common.notify.loading" />
        </Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (hasError) {
    return (
      <div
        style={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">Error loading resource unutilization data</Typography>
      </div>
    );
  }

  return <ResourceUnutilizationContext.Provider value={value}>{children}</ResourceUnutilizationContext.Provider>;
}

export function useResourceUnutilization() {
  const context = useContext(ResourceUnutilizationContext);
  if (context === undefined) {
    throw new Error('useResourceUnutilization must be used within a ResourceUnutilizationProvider');
  }
  return context;
}
