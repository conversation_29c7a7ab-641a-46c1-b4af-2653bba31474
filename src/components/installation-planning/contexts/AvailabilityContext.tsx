import React, { createContext, useContext, useMemo } from 'react';
import { Role, Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { Typography, Stack } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { format } from 'date-fns';
import { tz } from '@date-fns/tz';
import { useRegionContext } from '../../../context/RegionContext';
import { useDateRange } from './DateRangeContext';
import { useResourceUnutilization } from './ResourceUnutilizationContext';
import { api } from 'utils/api';
import { useServicesVisits } from './ServicesVisitsContext';

export type HalfDayAvailabilityType = 'available' | 'partiallyAvailable' | 'unavailable' | 'resources unavailable';
interface AvailabilityContextType {
  teams: Team[];
  teamAvailabilityMap: Map<string, Record<string, Map<string, string>>>;
  availabilityMap: Map<string, Record<string, Record<string, Record<string, HalfDayAvailabilityType>>>>;
  refetchTeamAvailability: () => void;
  isLoadingTeamAvailability: boolean;
}

const AvailabilityContext = createContext<AvailabilityContextType | undefined>(undefined);

export function AvailabilityProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId, timeZone } = useRegionContext();
  const { dateRange, weekdayMetrics, dateIndexMap } = useDateRange();
  const { weekdaysInRange } = weekdayMetrics;
  const { resourceUnutilizationMap } = useResourceUnutilization();
  const { serviceVisitsForResourcesMap } = useServicesVisits();

  // Team availability data
  const {
    data,
    refetch: refetchTeamAvailability,
    isLoading: isLoadingTeamAvailability,
  } = api.Resource.getTeamAvailability.useQuery({
    regionId: regionId!.value,
    startDate: dateRange.startDate,
    endDate: dateRange.endDateInclusive,
    timeZone,
  });

  const teamAvailabilityData = useMemo(() => {
    const { availability, teams } = data ?? { teams: [], availability: [] };
    const teamAvailabilityMap = new Map<string, Record<string, Map<string, string>>>();
    (availability ?? []).forEach(({ date, availableTeamIds }) => {
      teamAvailabilityMap.set(format(date, 'yyyy-MM-dd', { in: tz(timeZone) }), {
        INSTALLER: new Map(
          teams
            .filter(({ role }) => role === Role.ROLE_PLUMBER)
            .map(({ id }) => [
              id!.value,
              availableTeamIds.some((teamId) => id!.value === teamId?.value) ? 'available' : 'unavailable',
            ]),
        ),
        ELECTRICIAN: new Map(
          teams
            .filter(({ role }) => role === Role.ROLE_ELECTRICIAN)
            .map(({ id }) => [
              id!.value,
              availableTeamIds.some((teamId) => id!.value === teamId?.value) ? 'available' : 'unavailable',
            ]),
        ),
        LANDSCAPER: new Map(
          teams
            .filter(({ role }) => role === Role.ROLE_LANDSCAPER)
            .map(({ id }) => [
              id!.value,
              availableTeamIds.some((teamId) => id!.value === teamId?.value) ? 'available' : 'unavailable',
            ]),
        ),
      });
    });
    return { teamAvailabilityMap, teams };
  }, [data, timeZone]);

  const { teams, teamAvailabilityMap } = teamAvailabilityData || { teams: [], teamAvailabilityMap: new Map() };

  // Build map from dateString => roleKey => set of teamId values
  // TeamAvailability[] -> map[dateString][roleKey] -> set of teamId values
  const availabilityMap = React.useMemo(() => {
    const map = new Map<string, Record<string, Record<string, Record<string, HalfDayAvailabilityType>>>>();
    // For each day, build a map from roleKey to set of teamId values

    const roleKeys = ['INSTALLER', 'ELECTRICIAN', 'LANDSCAPER'];
    const startTimes = ['08', '12'];
    const installerTeams = teams.filter((team) => team.role === Role.ROLE_PLUMBER);
    const electricianTeams = teams.filter((team) => team.role === Role.ROLE_ELECTRICIAN);
    const landscaperTeams = teams.filter((team) => team.role === Role.ROLE_LANDSCAPER);
    const teamsByRole = {
      ['INSTALLER']: installerTeams,
      ['ELECTRICIAN']: electricianTeams,
      ['LANDSCAPER']: landscaperTeams,
    };

    weekdaysInRange.forEach((weekday) => {
      const dateKey = format(weekday.date, 'yyyy-MM-dd', { in: tz(timeZone) });
      if (!map.has(dateKey)) {
        map.set(dateKey, {});
      }
      for (const startTime of startTimes) {
        if (!map.get(dateKey)![startTime]) {
          map.get(dateKey)![startTime] = {};
        }
        for (const roleKey of roleKeys) {
          if (!map.get(dateKey)![startTime]![roleKey]) {
            map.get(dateKey)![startTime]![roleKey] = {};
          }
          for (const team of teamsByRole[roleKey as keyof typeof teamsByRole]) {
            let availability = teamAvailabilityMap.get(dateKey)?.[roleKey]?.get(team.id!.value) ?? 'unavailable';
            const defaultTeamMembers = teams.find((t) => t.id?.value === team.id!.value)?.defaultResources;
            const numberOfUnavailableMembers = defaultTeamMembers?.filter((memberId) => {
              const unutilizedTimeSlot = resourceUnutilizationMap.get(memberId.value)?.get(dateKey + '-' + startTime);
              if (unutilizedTimeSlot) {
                return true;
              }
              // If the start time is 12, check if the resource is unavailable for the full day
              if (startTime === '12') {
                const unutilizedTimeSlotFullDay = resourceUnutilizationMap
                  .get(memberId.value)
                  ?.get(dateKey + '-' + '08');
                if (Boolean(unutilizedTimeSlotFullDay)) {
                  if (unutilizedTimeSlotFullDay?.endTime === 16) {
                    return true;
                  }
                }
              }
              return false;
            });
            if (availability === 'available' && defaultTeamMembers && defaultTeamMembers?.length > 0) {
              switch (true) {
                case defaultTeamMembers?.length > 1 &&
                  numberOfUnavailableMembers &&
                  numberOfUnavailableMembers?.length === 1:
                  availability = 'partiallyAvailable';
                  break;
                case defaultTeamMembers?.length > 1 &&
                  numberOfUnavailableMembers &&
                  numberOfUnavailableMembers?.length > 1:
                  availability = 'resources unavailable';
                  break;
                case defaultTeamMembers?.length === 1 &&
                  numberOfUnavailableMembers &&
                  numberOfUnavailableMembers?.length === 1:
                  availability = 'resources unavailable';
                  break;
              }
              if (availability === 'available') {
                defaultTeamMembers.forEach((memberId) => {
                  const serviceVisits = serviceVisitsForResourcesMap.get(memberId.value);
                  if (serviceVisits) {
                    for (const visit of serviceVisits) {
                      if (visit.startDateIndex === dateIndexMap.get(dateKey)) {
                        if (
                          (startTime === '08' && visit.startHour < 12) ||
                          (startTime === '12' &&
                            visit.startHour + (visit.jobConstraints?.duration?.seconds ?? 0) / 3600 >= 12)
                        ) {
                          if (availability === 'available') {
                            availability = 'partiallyAvailable';
                            break;
                          } else {
                            availability = 'unavailable';
                            break;
                          }
                        }
                      }
                    }
                  }
                });
              }
            }
            if (!map.get(dateKey)![startTime]![roleKey]![team.id!.value]) {
              map.get(dateKey)![startTime]![roleKey]![team.id!.value] = availability as HalfDayAvailabilityType;
            }
          }
        }
      }
    });

    return map;
  }, [
    resourceUnutilizationMap,
    teamAvailabilityMap,
    teams,
    timeZone,
    weekdaysInRange,
    dateIndexMap,
    serviceVisitsForResourcesMap,
  ]);

  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(
    () => ({
      teams,
      teamAvailabilityMap,
      availabilityMap,
      refetchTeamAvailability,
      isLoadingTeamAvailability,
    }),
    [teams, teamAvailabilityMap, availabilityMap, refetchTeamAvailability, isLoadingTeamAvailability],
  );

  // Loading state
  const isLoading = isLoadingTeamAvailability;
  const hasError = !isLoadingTeamAvailability && !teamAvailabilityData;

  if (isLoading) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">
          <FormattedMessage id="common.notify.loading" />
        </Typography>
        <HeatPump />
      </Stack>
    );
  }

  if (hasError) {
    return (
      <div
        style={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">Error loading team availability data</Typography>
      </div>
    );
  }

  return <AvailabilityContext.Provider value={value}>{children}</AvailabilityContext.Provider>;
}

export function useAvailability() {
  const context = useContext(AvailabilityContext);
  if (context === undefined) {
    throw new Error('useAvailability must be used within an AvailabilityProvider');
  }
  return context;
}
