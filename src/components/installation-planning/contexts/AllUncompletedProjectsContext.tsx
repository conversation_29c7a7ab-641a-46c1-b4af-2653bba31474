import { Typography, Stack } from '@mui/material';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { createContext, useContext, useMemo } from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useRegionContext } from '../../../context/RegionContext';

interface AllUncompletedProjectsContextType {
  allUncompletedProjectsAtTimeOfLoad: FullInstallationProjectEntity[];
  isLoadingAllUncompletedProjectsAtTimeOfLoad: boolean;
}

const AllUncompletedProjectsContext = createContext<AllUncompletedProjectsContextType | undefined>(undefined);

export function AllUncompletedProjectsProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();

  const { data: AllUncompletedProjectsAtTimeOfLoad, isLoading: isLoadingAllUncompletedProjectsAtTimeOfLoad } =
    api.InstallationProject.getInstallationProjects.useQuery(
      {
        operationalUnitId: regionId!.value,
      },
      {
        refetchOnWindowFocus: false,
        trpc: {
          context: {
            skipBatch: true,
          },
        },
      },
    );

  const value = useMemo(
    () => ({
      allUncompletedProjectsAtTimeOfLoad: AllUncompletedProjectsAtTimeOfLoad || [],
      isLoadingAllUncompletedProjectsAtTimeOfLoad,
    }),
    [AllUncompletedProjectsAtTimeOfLoad, isLoadingAllUncompletedProjectsAtTimeOfLoad],
  );

  if (!isLoadingAllUncompletedProjectsAtTimeOfLoad && !AllUncompletedProjectsAtTimeOfLoad) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">
          <FormattedMessage id="common.notify.error" />
        </Typography>
        <HeatPump />
      </Stack>
    );
  }

  return <AllUncompletedProjectsContext.Provider value={value}>{children}</AllUncompletedProjectsContext.Provider>;
}

export function useAllUncompletedProjects() {
  const context = useContext(AllUncompletedProjectsContext);
  if (context === undefined) {
    throw new Error('useAllUncompletedProjects must be used within a AllUncompletedProjectsProvider');
  }
  return context;
}
