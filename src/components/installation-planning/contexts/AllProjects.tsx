import { createContext, useContext, useMemo, useState } from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useCompletedProjects } from './CompletedProjectsContext';
import { useProjectsUpdatedSince } from './ProjectsUpdatedSinceContext';
import { useAllUncompletedProjects } from './AllUncompletedProjectsContext';
import {
  useStageFilter,
  useUnsavedChangesWithoutReason,
  useAssignedResourceFilter,
  useTicketFilter,
} from '../stores/ProjectStore';
import {
  InstallationProjectJobJobTypeToResourceType,
  UnsavedChange,
  useResourceTypesForCountry,
} from '../types/planningTypes';
import { useCountryCodeContext } from '../../../context/CountryCodeContext';
import { getTicketName } from '../topBar/filtersTab/TicketFilter';
import {
  InstallationProjectJob_JobStatus,
  InstallationProjectJob_WorkSegmentStatus,
  InstallationProjectStage,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

type AllProjectsContextType = {
  allProjectsBeforeFiltersAndUnsavedChangesApplied: FullInstallationProjectEntity[];
  projects: FullInstallationProjectEntity[];
  showOnHoldProjects: boolean;
  showInSalesRecoveryProjects: boolean;
  setShowOnHoldProjects: (show: boolean) => void;
  setShowInSalesRecoveryProjects: (show: boolean) => void;
  projectsWithUnappliedBaseline: FullInstallationProjectEntity[];
  projectsWithIncompleteInstallation: FullInstallationProjectEntity[];
};

const AllProjectsContext = createContext<AllProjectsContextType | undefined>(undefined);

export function AllProjectsProvider({ children }: { children: React.ReactNode }) {
  const [showOnHoldProjects, setShowOnHoldProjects] = useState(true);
  const [showInSalesRecoveryProjects, setShowInSalesRecoveryProjects] = useState(true);
  const { completedProjectsData: completedProjects } = useCompletedProjects();
  const { allUncompletedProjectsAtTimeOfLoad: uncompletedProjects } = useAllUncompletedProjects();
  const { projectsUpdatedSince } = useProjectsUpdatedSince();
  const stageFilter = useStageFilter();
  const assignedResouceFilter = useAssignedResourceFilter();
  const ticketFilter = useTicketFilter();
  const unsavedChangesWithoutReason = useUnsavedChangesWithoutReason();
  const countryCode = useCountryCodeContext();
  const resourceTypesForCountry = useResourceTypesForCountry(countryCode);

  // Create merged projects array by updating original projects with any changed ones
  const projectsData = useMemo(() => {
    if (!projectsUpdatedSince && !uncompletedProjects && !completedProjects) return [];

    // Create map of original projects for O(1) lookup
    const projectMap = new Map(
      (uncompletedProjects ?? [])
        .filter((project) => {
          if (
            !showOnHoldProjects &&
            project.installationProject?.progressOverview?.currentBlockers?.some(
              (blocker) => blocker.blocker?.$case === 'onHold',
            )
          ) {
            return false;
          }
          if (
            !showInSalesRecoveryProjects &&
            project.installationProject?.progressOverview?.currentBlockers?.some(
              (blocker) => blocker.blocker?.$case === 'inSalesRecovery',
            )
          ) {
            return false;
          }
          return true;
        })
        .map((project) => [project.installationProject?.id?.value, project]),
    );

    // Add completed projects to map
    completedProjects.forEach((changedProject) => {
      projectMap.set(changedProject.installationProject?.id?.value, changedProject);
    });

    // Update map with any changed projects
    projectsUpdatedSince.forEach((changedProject) => {
      projectMap.set(changedProject.installationProject?.id?.value, changedProject);
    });

    // Convert map back to array
    return Array.from(projectMap.values());
  }, [completedProjects, uncompletedProjects, projectsUpdatedSince, showOnHoldProjects, showInSalesRecoveryProjects]);

  const projectsWithFiltersApplied = useMemo(() => {
    const projects = projectsData ?? [];
    return projects.filter(
      (project) =>
        (stageFilter.length === 0 || stageFilter.some((stage) => stage.value === project.installationProject?.stage)) &&
        (assignedResouceFilter.length === 0 ||
          assignedResouceFilter.some((resourceId) =>
            project.installationProject?.assignedResources?.some((resource) => resource.userId?.value === resourceId),
          )) &&
        (ticketFilter.length === 0 ||
          ticketFilter.some((ticket) =>
            project.installationProject?.progressOverview?.currentBlockers?.some(
              (blocker) =>
                blocker.blocker?.$case === 'openTicket' &&
                getTicketName(blocker.blocker.openTicket.name) === ticket.value,
            ),
          )),
    );
  }, [assignedResouceFilter, projectsData, stageFilter, ticketFilter]);

  const projectsWithUnsavedChangesApplied: FullInstallationProjectEntity[] = useMemo(() => {
    // 1. Short-circuit if no changes
    if (!unsavedChangesWithoutReason.length) {
      return projectsWithFiltersApplied ?? [];
    }

    // 2. Build a fast lookup
    const changesLookup = new Map<string, Map<string, UnsavedChange>>();
    for (const change of unsavedChangesWithoutReason) {
      const { projectId, jobId } = change;
      if (!changesLookup.has(projectId)) {
        changesLookup.set(projectId, new Map());
      }
      changesLookup.get(projectId)!.set(jobId, change);
    }

    // 3. Map over projects, skipping changes if none
    return (projectsWithFiltersApplied ?? []).map((project) => {
      const projectId = project.installationProject?.id?.value;
      if (!projectId) return project;
      const changesForProject = changesLookup.get(projectId);
      if (!changesForProject) return project;

      let jobsChanged = false;
      const updatedJobs = project.installationProject?.jobs.map((job) => {
        const jobId = job.id?.value;
        const change = jobId && changesForProject.get(jobId);
        if (change) {
          jobsChanged = true;
          return { ...job, workSegments: change.workSegments };
        }
        return job;
      });

      // If no job was updated, return original object
      if (!jobsChanged) {
        return project;
      }

      return {
        ...project,
        installationProject: {
          ...project.installationProject,
          jobs: updatedJobs,
        },
      } as FullInstallationProjectEntity;
    });
  }, [projectsWithFiltersApplied, unsavedChangesWithoutReason]);

  const projectsWithUnappliedBaseline = useMemo(() => {
    const projects = new Map<string, FullInstallationProjectEntity>();
    for (const project of projectsWithUnsavedChangesApplied) {
      const baselines = project.installationProject?.jobDurationEstimates ?? [];
      const projectStage = project.installationProject?.stage;
      if (
        projectStage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED ||
        projectStage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE ||
        projectStage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION
      ) {
        continue;
      }
      const jobStatuses = project.installationProject?.jobs.map((job) => job.status);
      const allJobsCompleted = jobStatuses?.every(
        (status) => status === InstallationProjectJob_JobStatus.JOB_STATUS_FINISHED,
      );
      if (allJobsCompleted) {
        continue;
      }
      for (const baseline of baselines) {
        const resourceType =
          InstallationProjectJobJobTypeToResourceType[
            baseline.jobType as keyof typeof InstallationProjectJobJobTypeToResourceType
          ];
        if (resourceTypesForCountry.includes(resourceType)) {
          if (baseline.state?.state?.$case === 'unhandled') {
            const job = project.installationProject?.jobs.find((job) => job.type === baseline.jobType);
            if (job && job.status !== InstallationProjectJob_JobStatus.JOB_STATUS_FINISHED) {
              const jobAssignedResources = job.assignedResources;
              if (jobAssignedResources && jobAssignedResources.length > 0) {
                const scheduledManhours = job.workSegments.reduce((acc, segment) => {
                  const assignedResources = segment.assignedResources;
                  if (assignedResources && assignedResources.length > 0) {
                    return acc + ((segment.duration?.seconds ?? 0) / 3600) * assignedResources.length;
                  }
                  return acc + ((segment.duration?.seconds ?? 0) / 3600) * job.requiredResourcesCount;
                }, 0);
                if (Math.abs(scheduledManhours - (baseline.duration?.seconds ?? 0) / 3600) > 1) {
                  if (project.installationProject?.id?.value) {
                    projects.set(project.installationProject.id.value, project);
                  }
                  break;
                }
              } else {
                const scheduledManhours = ((job.duration?.seconds ?? 0) / 3600) * job.requiredResourcesCount;

                if (Math.abs(scheduledManhours - (baseline.duration?.seconds ?? 0) / 3600) > 1) {
                  if (project.installationProject?.id?.value) {
                    projects.set(project.installationProject.id.value, project);
                  }
                  break;
                }
              }
            }
          }
        }
      }
    }
    return Array.from(projects.values());
  }, [projectsWithUnsavedChangesApplied, resourceTypesForCountry]);

  const projectsWithIncompleteInstallation = useMemo(() => {
    return projectsWithUnsavedChangesApplied.filter((project) => {
      const isIncompleteTicket = project.installationProject?.progressOverview?.currentBlockers.some((blocker) => {
        if (
          blocker.blocker?.$case === 'openTicket' &&
          blocker.blocker.openTicket.name.includes('Installation Incomplete')
        ) {
          return true;
        }
      });
      const remainingWorkRequiredOrCompleted =
        project.installationProject?.progressOverview?.progressMadeIndicators.filter(
          (indicator) => indicator.indicator?.$case === 'acceptanceUpdate',
        );
      const hasCompletedAcceptance = remainingWorkRequiredOrCompleted?.find(
        (indicator) =>
          indicator.indicator?.$case === 'acceptanceUpdate' &&
          indicator.indicator.acceptanceUpdate.outcome?.$case === 'completed',
      );
      const isIncompleteReportOutcome = (remainingWorkRequiredOrCompleted?.length ?? 0) > 1 && !hasCompletedAcceptance;
      const hasUnscheduledJobs = project.installationProject?.jobs.some(
        (job) =>
          job.workSegments.some(
            (segment) =>
              segment.startTime &&
              segment.status === InstallationProjectJob_WorkSegmentStatus.WORK_SEGMENT_STATUS_FINISHED,
          ) && job.workSegments.some((segment) => !segment.startTime),
      );
      if (isIncompleteTicket || hasUnscheduledJobs || isIncompleteReportOutcome) {
        return true;
      }
      return false;
    });
  }, [projectsWithUnsavedChangesApplied]);

  const value = useMemo(
    () => ({
      allProjectsBeforeFiltersAndUnsavedChangesApplied: projectsData ?? [],
      projects: projectsWithUnsavedChangesApplied ?? [],
      showOnHoldProjects,
      showInSalesRecoveryProjects,
      setShowOnHoldProjects,
      setShowInSalesRecoveryProjects,
      projectsWithUnappliedBaseline,
      projectsWithIncompleteInstallation,
    }),
    [
      projectsData,
      projectsWithUnsavedChangesApplied,
      showOnHoldProjects,
      showInSalesRecoveryProjects,
      setShowOnHoldProjects,
      setShowInSalesRecoveryProjects,
      projectsWithUnappliedBaseline,
      projectsWithIncompleteInstallation,
    ],
  );

  return <AllProjectsContext.Provider value={value}>{children}</AllProjectsContext.Provider>;
}

export function useAllProjects() {
  const context = useContext(AllProjectsContext);
  if (context === undefined) {
    throw new Error('useAllProjects must be used within a AllProjectsProvider');
  }
  return context;
}
