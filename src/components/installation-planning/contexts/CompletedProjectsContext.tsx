import React, { createContext, useContext, useMemo } from 'react';
import { api } from 'utils/api';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useRegionContext } from '../../../context/RegionContext';
import { useDateRange } from './DateRangeContext';

interface CompletedProjectsContextType {
  completedProjectsData: FullInstallationProjectEntity[];
  isLoadingCompletedProjects: boolean;
  refetchCompletedProjects: () => void;
}

const CompletedProjectsContext = createContext<CompletedProjectsContextType | undefined>(undefined);

export function CompletedProjectsProvider({ children }: { children: React.ReactNode }) {
  const { dateRange } = useDateRange();
  const { id: regionId } = useRegionContext();

  const {
    data: completedProjectsData,
    refetch: refetchCompletedProjects,
    isLoading: isLoadingCompletedProjects,
  } = api.InstallationProject.getInstallationProjects.useQuery(
    {
      operationalUnitId: regionId!.value,
      updatedAfter: dateRange.startDate,
      showCompletedProjects: true,
    },
    {
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    },
  );

  const value = useMemo(
    () => ({
      completedProjectsData: completedProjectsData || [],
      isLoadingCompletedProjects,
      refetchCompletedProjects,
    }),
    [completedProjectsData, isLoadingCompletedProjects, refetchCompletedProjects],
  );

  return <CompletedProjectsContext.Provider value={value}>{children}</CompletedProjectsContext.Provider>;
}

export function useCompletedProjects() {
  const context = useContext(CompletedProjectsContext);
  if (context === undefined) {
    throw new Error('useCompletedProjects must be used within a CompletedProjectsProvider');
  }
  return context;
}
