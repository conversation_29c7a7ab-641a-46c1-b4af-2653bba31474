import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import {
  InstallationProjectJob_JobStatus,
  InstallationProjectJob_JobType,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Country } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.location.v1';
import {
  Group,
  UserIdentity,
} from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import {
  InstallationProjectActivity_JobRescheduled_ReasonCategory,
  InstallationProjectJob_WorkSegment,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { Resource, Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';

export interface SegmentGroups {
  continuousWorkSegmentsWithTeam: Map<string, ContinuousWorkSegment[]>;
  continuousWorkSegmentsWithoutTeam: ContinuousWorkSegment[];
}

export type ResourceForRegion = UserIdentity & Omit<Resource, 'roles'> & { roles: number[] };

export type RoleBasedSegments = Map<number, SegmentGroups>;

export type ResourceBasedSegments = Map<string, { continuousWorkSegments: ContinuousWorkSegment[] }>;
export type ContinuousWorkSegment = FullInstallationProjectEntity & {
  segmentDetails: {
    jobId: string;
    requiredRole: number;
    startTime: Date;
    endTime: Date;
    duration: number;
    workSegments: InstallationProjectJob_WorkSegment[];
    teamId?: string;
    assignedResources: {
      userId: string;
    }[];
    status: InstallationProjectJob_JobStatus;
    incompleteHours?: boolean;
  };
  hasAssignedResources: boolean;
};

export type Row = ({
  availability: 'available' | 'taken' | 'overlapping';
  numberOfJobs: number;
  jobs?: {
    continuousWorkSegment: ContinuousWorkSegment;
    span: number;
    innerRowNumber: number;
  }[];
} | null)[];

export type ContinuousWorkSegmentForJobItem = ContinuousWorkSegment & {
  segmentDetails: {
    startTime?: Date;
    endTime?: Date;
    jobId: string;
    requiredRole: number;
    duration?: number;
    workSegments: InstallationProjectJob_WorkSegment[];
    teamId?: string;
    assignedResources: {
      userId: string;
    }[];
    status: InstallationProjectJob_JobStatus;
  };
};

export type SegmentWithNoDuration = Omit<ContinuousWorkSegment, 'segmentDetails'> & {
  segmentDetails: {
    jobId: string;
    requiredRole: number;
  };
};

export type StageForFilter = {
  value: number;
  label: string;
};

export enum ResourceType {
  INSTALLER = 'INSTALLER',
  ELECTRICIAN = 'ELECTRICIAN',
  LANDSCAPER = 'LANDSCAPER',
}

export const InstallationProjectJobJobTypeToResourceType = {
  [InstallationProjectJob_JobType.JOB_TYPE_PLUMBING]: ResourceType.INSTALLER,
  [InstallationProjectJob_JobType.JOB_TYPE_ELECTRICAL]: ResourceType.ELECTRICIAN,
  [InstallationProjectJob_JobType.JOB_TYPE_LANDSCAPING]: ResourceType.LANDSCAPER,
};

export type NrOfWeekdaysPerMonth = {
  month: string;
  year: number;
  numberOfWeekdays: number;
};

export type NrWeekdaysPerWeekNumber = {
  week: string;
  nrDays: number;
  year: number;
};

export type UnsavedChange = {
  projectId: string;
  jobId: string;
  customerName: string;
  requiredRole: number;
  newStartTime: Date;
  previousStartTime?: Date;
  workSegments: InstallationProjectJob_WorkSegment[];
  userWhoMadeChange: string;
  previousTeamId?: string;
  newTeamId?: string;
  isNewSeparateHours?: boolean;
  reason?: {
    category?: InstallationProjectActivity_JobRescheduled_ReasonCategory;
    description?: string;
  };
  resourceId?: string;
  typeOfChange?:
    | 'split-segment'
    | 'move-segment'
    | 'edit-segment'
    | 'change-team'
    | 'change-team-and-move-segment'
    | 'set-initial-start-time'
    | 'separate-hours-added';
};

export const resourceMapping = {
  INSTALLER: 1,
  ELECTRICIAN: 2,
  LANDSCAPER: 3,
};

export const reverseResourceMapping = {
  1: ResourceType.INSTALLER,
  2: ResourceType.ELECTRICIAN,
  3: ResourceType.LANDSCAPER,
};

export const resourceTypes = [ResourceType.INSTALLER, ResourceType.ELECTRICIAN, ResourceType.LANDSCAPER] as const;

export type TeamAvailability = {
  date: Date;
  roles: {
    [key in ResourceType]: Team[];
  };
};

export type DayTeamPickerData = {
  role: keyof typeof reverseResourceMapping;
  date: Date;
  position: DOMRect;
};

export const COUNTRIES = [
  {
    name: 'Germany',
    grpcCode: Country.COUNTRY_DE,
    code: 'DE',
  },
  {
    name: 'Italy',
    grpcCode: Country.COUNTRY_IT,
    code: 'IT',
  },
  {
    name: 'United Kingdom',
    grpcCode: Country.COUNTRY_GB,
    code: 'GB',
  },
] as const;

export interface NationalHoliday {
  date: Date;
}

export interface RegionalHoliday {
  id: string;
  date: Date;
}

export const useResourceTypesForCountry = (countryCode: string) =>
  resourceTypes.filter((resourceType) => !(countryCode === 'GB' && resourceType === ResourceType.LANDSCAPER));

export const FIELD_RESOURCE_GROUPS = [
  Group.GROUP_EXTERNAL_ALL_INSTALLER,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_APPRENTICE,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_TECHNICIAN,
  Group.GROUP_AIRA_ALL_SENIOR_CLEAN_ENERGY_TECHNICIAN,
  Group.GROUP_AIRA_ALL_TEAM_LEAD_INSTALLATION,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_ELECTRICIAN,
  Group.GROUP_AIRA_ALL_SENIOR_CLEAN_ENERGY_ELECTRICIAN,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_LANDSCAPER,
];

export const INSTALLER_GROUPS = [
  Group.GROUP_EXTERNAL_ALL_INSTALLER,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_APPRENTICE,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_TECHNICIAN,
  Group.GROUP_AIRA_ALL_SENIOR_CLEAN_ENERGY_TECHNICIAN,
  Group.GROUP_AIRA_ALL_TEAM_LEAD_INSTALLATION,
];

export const ELECTRICIAN_GROUPS = [
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_ELECTRICIAN,
  Group.GROUP_AIRA_ALL_SENIOR_CLEAN_ENERGY_ELECTRICIAN,
];

export const LANDSCAPER_GROUPS = [Group.GROUP_AIRA_ALL_CLEAN_ENERGY_LANDSCAPER];

export const getGroupsForResourceType = (resourceType: ResourceType) => {
  switch (resourceType) {
    case ResourceType.INSTALLER:
      return INSTALLER_GROUPS;
    case ResourceType.ELECTRICIAN:
      return ELECTRICIAN_GROUPS;
    case ResourceType.LANDSCAPER:
      return LANDSCAPER_GROUPS;
    default:
      return [];
  }
};
