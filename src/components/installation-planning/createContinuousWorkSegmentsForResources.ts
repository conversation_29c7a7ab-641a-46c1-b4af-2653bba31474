import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { differenceInHours, differenceInMinutes, isFriday, subDays } from 'date-fns';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { tz } from '@date-fns/tz';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { ContinuousWorkSegment, ResourceBasedSegments } from './types/planningTypes';
import { getCacheKey, WorkingHoursOnDate } from './helpers/workingHours';
import { isSameDayFast } from './helpers/dateHelpers';

export function createContinuousWorkSegmentsForResources({
  projects,
  weekdaysInRange,
  timeZone,
  fieldResourcesWithDataMap,
  workingHoursForDatesMap,
}: {
  projects: FullInstallationProjectEntity[];
  weekdaysInRange: { date: Date; isFriday: boolean }[];
  timeZone: string;
  fieldResourcesWithDataMap: Map<string, UserIdentity>;
  workingHoursForDatesMap: Map<string, WorkingHoursOnDate>;
}): ResourceBasedSegments {
  const rangeStart = weekdaysInRange[0]!.date.getTime();
  const rangeEnd = weekdaysInRange[weekdaysInRange.length - 1]!.date.getTime();

  // Initialize result structure
  const result: ResourceBasedSegments = new Map();
  const resourceIds = Array.from(fieldResourcesWithDataMap.keys());

  // Pre-initialize result map
  resourceIds.forEach((id) => {
    result.set(id, { continuousWorkSegments: [] });
  });

  // Process each project
  for (const project of projects) {
    const jobs = project.installationProject?.jobs;
    if (!jobs?.length) continue;

    for (const job of jobs) {
      if (!job.workSegments?.length || !job.id || !job.assignedResources.length) continue;

      // Quick range check for job segments
      let hasSegmentsInRange = false;
      for (const ws of job.workSegments) {
        if (!ws.startTime || !ws.endTime) continue;
        const startMs = ws.startTime.getTime();
        const endMs = ws.endTime.getTime();
        if (startMs <= rangeEnd && endMs >= rangeStart) {
          hasSegmentsInRange = true;
          break;
        }
      }
      if (!hasSegmentsInRange) continue;

      // Create resource assignments map
      const resourceAssignments = new Map<
        string,
        {
          segments: InstallationProjectJob_WorkSegment[];
          duration: number;
          lastSegmentEnd: Date | null;
        }
      >();

      // Process segments in chronological order
      const sortedSegments = job.workSegments
        .filter((ws) => ws.startTime && ws.endTime && ws.assignedResources?.length)
        .sort((a, b) => a.startTime!.getTime() - b.startTime!.getTime());

      for (const ws of sortedSegments) {
        const assignedResources =
          ws.assignedResources
            ?.filter((ar) => ar.userId?.value && result.has(ar.userId.value))
            .map((ar) => ar.userId!.value) ?? [];

        for (const resourceId of assignedResources) {
          let assignment = resourceAssignments.get(resourceId);

          if (!assignment) {
            assignment = {
              segments: [],
              duration: 0,
              lastSegmentEnd: null,
            };
            resourceAssignments.set(resourceId, assignment);
          }

          const duration = differenceInHours(ws.endTime!, ws.startTime!);

          if (!assignment.lastSegmentEnd) {
            assignment.segments = [ws];
            assignment.duration = duration;
            assignment.lastSegmentEnd = ws.endTime ?? null;
            continue;
          }

          const workingHoursPrevious = workingHoursForDatesMap.get(getCacheKey(assignment.lastSegmentEnd!, timeZone));
          const workingHoursCurrent = workingHoursForDatesMap.get(getCacheKey(ws.startTime!, timeZone));

          if (!workingHoursPrevious && !workingHoursCurrent) {
            continue;
          }

          const onSameDay = isSameDayFast(assignment.lastSegmentEnd, ws.startTime!);
          const spansWeekend =
            isFriday(assignment.lastSegmentEnd, { in: tz(timeZone) }) &&
            isSameDayFast(assignment.lastSegmentEnd, subDays(ws.startTime!, 3));
          const spansOvernight = isSameDayFast(assignment.lastSegmentEnd, subDays(ws.startTime!, 1));

          const adjacentGridDays =
            workingHoursPrevious &&
            workingHoursCurrent &&
            (spansWeekend || spansOvernight) &&
            differenceInMinutes(workingHoursPrevious.workEndsAt, assignment.lastSegmentEnd) < 70 &&
            differenceInMinutes(ws.startTime!, workingHoursCurrent.workStartsAt) < 70;

          if (onSameDay || adjacentGridDays) {
            assignment.segments.push(ws);
            assignment.duration += duration;
            assignment.lastSegmentEnd = ws.endTime ?? null;
          } else {
            // Create continuous segment and push to result
            const segment: ContinuousWorkSegment = {
              ...project,
              segmentDetails: {
                jobId: job.id.value,
                requiredRole: job.requiredRole,
                startTime: assignment.segments[0]!.startTime!,
                endTime: assignment.segments[assignment.segments.length - 1]!.endTime!,
                duration: assignment.duration,
                teamId: assignment.segments[0]?.teamId?.value,
                workSegments: assignment.segments,
                assignedResources: [{ userId: resourceId }],
                status: job.status,
              },
              hasAssignedResources: true,
            };
            result.get(resourceId)!.continuousWorkSegments.push(segment);

            // Start new segment group
            assignment.segments = [ws];
            assignment.duration = duration;
            assignment.lastSegmentEnd = ws.endTime ?? null;
          }
        }
      }

      // Push remaining segments
      for (const [resourceId, assignment] of resourceAssignments) {
        if (assignment.segments.length === 0) continue;

        const segment: ContinuousWorkSegment = {
          ...project,
          segmentDetails: {
            jobId: job.id.value,
            requiredRole: job.requiredRole,
            startTime: assignment.segments[0]!.startTime!,
            endTime: assignment.segments[assignment.segments.length - 1]!.endTime!,
            duration: assignment.duration,
            teamId: assignment.segments[0]?.teamId?.value,
            workSegments: assignment.segments,
            assignedResources: [{ userId: resourceId }],
            status: job.status,
          },
          hasAssignedResources: true,
        };
        result.get(resourceId)!.continuousWorkSegments.push(segment);
      }
    }
  }

  return result;
}
