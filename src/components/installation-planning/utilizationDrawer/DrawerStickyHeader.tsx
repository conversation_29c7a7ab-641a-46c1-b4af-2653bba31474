import React from 'react';
import { Box } from '@mui/material';
import { Link, createTheme } from '@mui/material';
import MuiDrawer from '@mui/material/Drawer';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { foregroundThemes } from '@ui/theme/componentsThemes';
import type { BackgroundColor } from '@ui/theme/componentsThemes';
import { theme } from '@ui/theme/theme';
import { AiraCloseIcon } from '../../../ui/components/Icons/AiraCloseIcon/AiraCloseIcon';

interface DrawerProps {
  isOpen: boolean;
  setIsOpen: (show: boolean) => void;
  backgroundColor?: BackgroundColor;
  children?: React.ReactNode | React.ReactNode[];
  headerChildren?: React.ReactNode | React.ReactNode[];
}

export default function DrawerStickyHeader({
  isOpen,
  setIsOpen,
  children,
  headerChildren: titleChildren,
}: DrawerProps) {
  const componentTheme = createTheme(theme, foregroundThemes['dark']);

  return (
    <MuiDrawer
      anchor="right"
      open={isOpen}
      onClose={() => setIsOpen(false)}
      sx={{
        zIndex: 9999999,
      }}
      PaperProps={{
        sx: {
          backgroundColor: '#F3F2F0',
          height: '100vh',
          position: 'absolute',
          borderRadius: '44px 0px 0px 44px',
          top: 0,
          paddingBottom: 2,

          [theme.breakpoints.only('mobile')]: {
            width: '100vw',
            paddingX: 2,
            borderRadius: 0,
          },
          [theme.breakpoints.only('tablet')]: {
            width: '500px',
            paddingX: 4,
          },
          [theme.breakpoints.up('bigTablet')]: {
            width: '500px',
            paddingX: 4,
          },
        },
      }}
    >
      <Box
        sx={{
          display: 'flex',
          paddingTop: 3,
          pb: 3,
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#F3F2F0',

          position: 'sticky',
          top: '0px',
          zIndex: 6000,
        }}
      >
        {titleChildren}
        <Link
          component="button"
          aria-label="Close drawer"
          onClick={() => setIsOpen(false)}
          style={{ textDecoration: 'none', width: '32px', height: '32px' }}
        >
          <AiraCloseIcon width={16} height={16} color={theme.palette.aira.black} />
        </Link>
      </Box>

      <AiraThemeProvider theme={componentTheme}>{children}</AiraThemeProvider>
    </MuiDrawer>
  );
}
