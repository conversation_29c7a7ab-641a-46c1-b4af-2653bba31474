import React from 'react';
import { grey } from '@ui/theme/colors';

interface ChartProps {
  color: string;
  percentage: number;
  text: string;
}

export default function GaugeChart({ color, percentage, text }: ChartProps) {
  return (
    <svg viewBox="0 0 200 200">
      <path
        d="M100 10 a 90 90 0 0 1 0 180 a 90 90 0 0 1 0 -180"
        fill="none"
        strokeWidth="8"
        strokeLinecap="round"
        stroke={grey[200]}
        pathLength="100"
      />
      {percentage > 0 && (
        <path
          className="fill-path"
          d="M100 10 a 90 90 0 0 1 0 180 a 90 90 0 0 1 0 -180"
          fill="none"
          strokeWidth="8"
          strokeLinecap="round"
          strokeDasharray={`${percentage} ${100}`}
          stroke={color}
          pathLength="100"
        />
      )}
      <text
        x="50%"
        y="50%"
        dominantBaseline="middle"
        textAnchor="middle"
        dy="-4px"
        style={{ fontSize: '32px', fontWeight: 500, fontFamily: 'AiraDisplay' }}
      >
        <tspan>{percentage}</tspan>
        <tspan dy="-6px" style={{ fontSize: '16px' }}>
          %
        </tspan>
      </text>
      <text
        x="50%"
        y="50%"
        dy="24px"
        dominantBaseline="middle"
        textAnchor="middle"
        style={{ fill: grey[700], fontSize: '12px' }}
      >
        {text}
      </text>
    </svg>
  );
}
