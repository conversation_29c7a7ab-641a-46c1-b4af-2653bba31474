import { useProjectActions, useShowStatisticsDrawer } from 'components/installation-planning/stores/ProjectStore';
import React from 'react';
import { Box, MenuItem } from '@mui/material';
import { add, endOfDay, format, startOfWeek, subWeeks } from 'date-fns';
import { useRegionContext } from 'context/RegionContext';
import { Role } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import DrawerStickyHeader from './DrawerStickyHeader';
import ButtonSelect from './ButtonSelect';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { tz } from '@date-fns/tz';
import UtilizationStatistics from './UtilizationStatistics';

export interface RangeOption {
  id: number;
  from: Date;
  to: Date;
}

const rangeToLabel = (dateOption: { from: Date; to: Date }) => {
  const formattedFrom = format(dateOption.from, 'd MMM');
  const formattedTo = format(dateOption.to, 'd MMM');
  return `${formattedFrom} - ${formattedTo}`;
};

const roleOptions = [
  { value: Role.ROLE_PLUMBER, label: 'Installer' },
  { value: Role.ROLE_ELECTRICIAN, label: 'Electrician' },
  { value: Role.ROLE_LANDSCAPER, label: 'Landscaper' },
];

const getRangeOptions = (timeZone: string, nrOfWeeks: number = 4): RangeOption[] => {
  const currentStartOfWeek = startOfWeek(Date.now(), { weekStartsOn: 1, in: tz(timeZone) });

  const rangeOptions = [];

  for (let i = 0; i < nrOfWeeks; i += 1) {
    const from = subWeeks(currentStartOfWeek, i);
    const to = endOfDay(add(from, { days: 4 }), { in: tz(timeZone) });
    rangeOptions.push({ id: i + 1, from: from, to: to });
  }
  return rangeOptions;
};

export default function StatisticsDrawer() {
  const { setShowStatisticsDrawer } = useProjectActions();
  const showStatisticsDrawer = useShowStatisticsDrawer();

  const { timeZone } = useRegionContext();
  const rangeOptions = getRangeOptions(timeZone);

  const [selectedRange, setSelectedRange] = React.useState(rangeOptions[0]!);
  const [selectedRole, setSelectedRole] = React.useState(Role.ROLE_PLUMBER);

  return (
    <DrawerStickyHeader
      isOpen={showStatisticsDrawer}
      setIsOpen={setShowStatisticsDrawer}
      headerChildren={
        <Box display="flex" gap={2}>
          <ButtonSelect
            selectedValue={selectedRange?.id}
            onChange={(e) => {
              const newDate = rangeOptions.find((range) => range.id === e.target.value);
              if (newDate) {
                setSelectedRange(newDate);
              }
            }}
            name="selectedRange"
            options={rangeOptions.map((range) => (
              <MenuItem key={range.id} value={range.id}>
                {rangeToLabel(range)}
              </MenuItem>
            ))}
            icon={CalendarOutlinedIcon}
          />
          <ButtonSelect
            selectedValue={selectedRole}
            onChange={(e) => {
              setSelectedRole(e.target.value as Role);
            }}
            name="selectRole"
            options={roleOptions.map((role) => (
              <MenuItem key={role.value} value={role.value}>
                {role.label}
              </MenuItem>
            ))}
            icon={PersonOutlinedIcon}
          />
        </Box>
      }
    >
      <UtilizationStatistics selectedRange={selectedRange} selectedRole={selectedRole} />
    </DrawerStickyHeader>
  );
}
