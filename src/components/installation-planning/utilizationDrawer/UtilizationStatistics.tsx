import React from 'react';
import { Box, Typography } from '@mui/material';
import { brandYellow, magenta, red } from '@ui/theme/colors';
import { api } from 'utils/api';
import { useRegionContext } from 'context/RegionContext';
import {
  Role,
  UtilizationGroup,
  UtilizationStatistics as UtilizationStatisticsType,
  UtilizationType,
} from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import GaugeChart from './GaugeChart';
import { getWorkableHoursForRange } from '../helpers/dateHelpers';
import { RangeOption } from './StatisticsDrawer';

const utilizationTypeLabel = (type: UtilizationType) => {
  switch (type) {
    case UtilizationType.UTILIZATION_TYPE_CUSTOMER_ABSENCE:
      return 'Customer Absence';
    case UtilizationType.UTILIZATION_TYPE_INSTALLATION:
      return 'Installations';
    case UtilizationType.UTILIZATION_TYPE_SERVICE:
      return 'Service jobs';
    case UtilizationType.UTILIZATION_TYPE_NO_ORDER:
      return 'No Order';
    case UtilizationType.UTILIZATION_TYPE_SICKNESS:
      return 'Sickness';
    case UtilizationType.UTILIZATION_TYPE_TRAINING:
      return 'Training';
    case UtilizationType.UTILIZATION_TYPE_VACATION:
      return 'Vacation';
    case UtilizationType.UTILIZATION_TYPE_LENT_OUT:
      return 'Lent Out';
    case UtilizationType.UTILIZATION_TYPE_TIME_COMPENSATION:
      return 'Time Compensation';
    case UtilizationType.UTILIZATION_TYPE_HOLIDAY:
      return 'Public Holiday';
    default:
      return 'Unknown';
  }
};

const utilizationGroupLabel = (group: UtilizationGroup) => {
  switch (group) {
    case UtilizationGroup.UTILIZATION_GROUP_UTILISED:
      return 'Utilised';
    case UtilizationGroup.UTILIZATION_GROUP_UNUTILISED:
      return 'Un-utilised';
    case UtilizationGroup.UTILIZATION_GROUP_UNAVAILABLE:
      return 'Unavailable';
    default:
      return 'Unknown';
  }
};

const getColor = (group: UtilizationGroup) => {
  switch (group) {
    case UtilizationGroup.UTILIZATION_GROUP_UTILISED:
      return brandYellow[400];
    case UtilizationGroup.UTILIZATION_GROUP_UNUTILISED:
      return magenta[400];
    case UtilizationGroup.UTILIZATION_GROUP_UNAVAILABLE:
      return red[400];
    default:
      return brandYellow[400];
  }
};

const groups = [
  {
    group: UtilizationGroup.UTILIZATION_GROUP_UTILISED,
    types: [UtilizationType.UTILIZATION_TYPE_INSTALLATION, UtilizationType.UTILIZATION_TYPE_SERVICE],
  },
  {
    group: UtilizationGroup.UTILIZATION_GROUP_UNUTILISED,
    types: [UtilizationType.UTILIZATION_TYPE_NO_ORDER, UtilizationType.UTILIZATION_TYPE_CUSTOMER_ABSENCE],
  },
  {
    group: UtilizationGroup.UTILIZATION_GROUP_UNAVAILABLE,
    types: [
      UtilizationType.UTILIZATION_TYPE_HOLIDAY,
      UtilizationType.UTILIZATION_TYPE_VACATION,
      UtilizationType.UTILIZATION_TYPE_SICKNESS,
      UtilizationType.UTILIZATION_TYPE_TRAINING,
      UtilizationType.UTILIZATION_TYPE_TIME_COMPENSATION,
      // UtilizationType.UTILIZATION_TYPE_LENT_OUT,
    ],
  },
];

function calculatePercentage(
  utilizationStatistics: UtilizationStatisticsType,
  group: UtilizationGroup,
  type: UtilizationType,
  workableHours: number,
) {
  const typeStats = utilizationStatistics.groups
    .find((g) => g.group === group)
    ?.stats.find((stat) => stat.type === type);

  if (!typeStats || !typeStats.duration) {
    return 0;
  }

  const durationInMinutes = typeStats.duration.seconds / 60;

  return Math.round((durationInMinutes / getUtilizableMinutes(utilizationStatistics, group, workableHours)) * 100);
}

function getUtilizableMinutes(
  selectedStats: UtilizationStatisticsType,
  group: UtilizationGroup,
  workableHours: number,
) {
  const workableMinutes = workableHours * 60 * selectedStats.numberOfResources;

  if (group === UtilizationGroup.UTILIZATION_GROUP_UNAVAILABLE) {
    return workableMinutes;
  }
  const unavailableSeconds =
    selectedStats?.groups.find((group) => group.group === UtilizationGroup.UTILIZATION_GROUP_UNAVAILABLE)?.duration
      ?.seconds ?? 0;
  return workableMinutes - unavailableSeconds / 60;
}

export default function UtilizationStatistics({
  selectedRange,
  selectedRole,
}: {
  selectedRange: RangeOption;
  selectedRole: Role;
}) {
  const { id: regionId, timeZone } = useRegionContext();

  const workableHours = getWorkableHoursForRange(selectedRange.from, selectedRange.to, timeZone);

  const { data: utilizationStatistics } = api.Resource.findUtilizationStatistics.useQuery(
    {
      regionId: regionId!.value,
      roles: [selectedRole],
      from: selectedRange.from,
      to: selectedRange.to,
    },
    { enabled: !!regionId },
  );

  return (
    <Box gap={2}>
      <Box display="flex" flexDirection="column" gap={2}>
        {groups.map((group) => {
          return (
            <Box
              key={group.group}
              display="flex"
              flexDirection="column"
              alignItems="center"
              sx={{
                borderRadius: '22px',
                backgroundColor: '#22222608',
              }}
              p={1.5}
            >
              <Typography variant="body2Emphasis">{utilizationGroupLabel(group.group)}</Typography>
              <Box display="flex" flexWrap="wrap" width="100%" justifyContent="space-between">
                {group.types.map((type) => {
                  const percentage = utilizationStatistics
                    ? calculatePercentage(utilizationStatistics, group.group, type, workableHours)
                    : 0;
                  return (
                    <Box
                      width="49%"
                      key={type}
                      sx={{
                        '.fill-path': {
                          animation: 'progress 1s ease-out forwards',
                          '@keyframes progress': {
                            '0%': {
                              strokeDasharray: '0 100',
                            },
                          },
                        },
                      }}
                    >
                      <GaugeChart
                        percentage={percentage}
                        text={utilizationTypeLabel(type).toLowerCase()}
                        color={getColor(group.group)}
                      />
                    </Box>
                  );
                })}
              </Box>
            </Box>
          );
        })}
      </Box>
    </Box>
  );
}
