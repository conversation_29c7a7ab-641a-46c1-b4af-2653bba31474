import React, { SVGProps } from 'react';
import { FormControl, InputAdornment } from '@mui/material';
import { Select } from '@ui/components/Select/Select';
import { selectClasses, type SelectProps as MuiSelectProps } from '@mui/material/Select';
import { grey } from '@ui/theme/colors';

interface SelectProps<T extends string | number | string[]> extends Omit<MuiSelectProps<T>, 'onChange'> {
  onChange: NonNullable<MuiSelectProps<T>['onChange']>;
  selectedValue: T;
  name: string;
  options: React.ReactNode | React.ReactNode[];
  icon: (props: SVGProps<SVGSVGElement>) => React.JSX.Element;
}

export default function ButtonSelect<T extends string | number | string[]>({
  onChange,
  selectedValue,
  name,
  options,
  icon,
}: SelectProps<T>) {
  const Icon = icon;
  return (
    <FormControl
      sx={{
        [`& .MuiInputBase-root`]: {
          background: `#22222608`,
          fontSize: '14px',
          fontWeight: 500,

          borderRadius: '50px',
          px: '4px',
          py: '6px',
          '&:hover': {
            background: '#2222260F',
          },
        },
      }}
    >
      <Select
        variant="standard"
        disableUnderline
        value={selectedValue}
        onChange={onChange}
        label=""
        name={name}
        startAdornment={
          <InputAdornment position="start">
            <Icon width={16} color={grey[800]} />
          </InputAdornment>
        }
        inputProps={{
          IconComponent: () => null,
          sx: { pr: '12px !important' },
        }}
        MenuProps={{
          PaperProps: {
            style: {
              zIndex: 3500, // higher than the parent component's z-index
            },
          },
          disablePortal: true, // Ensures the menu renders inside the DOM hierarchy
          sx: {
            marginBlock: '0.5rem',
          },
        }}
        sx={{
          [`& .${selectClasses.select}`]: {
            '&:focus': {
              background: 'transparent',
            },
          },
        }}
      >
        {options}
      </Select>
    </FormControl>
  );
}
