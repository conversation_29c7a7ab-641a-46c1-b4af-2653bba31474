import React from 'react';
import { Stack } from '@mui/material';
import UnsavedChangesToast from './unsavedChanges/UnsavedChangesToast';
import JobsContainer from './JobsContainer';
import JobEditModal from './modals/JobEditModal';
import RightSidebar from './projectSidebar/RightSidebar';
import FiltersTab from './topBar/filtersTab/FiltersTab';
import UnsavedChangesSidebar from './unsavedChanges/UnsavedChangesSidebar';
import TeamsEditorModal from './modals/TeamsEditorModal';
import NoBaselineHoursModal from './modals/NoBaselineHoursModal';
import RemoveAssignedResourcesModal from './modals/RemoveAssignedResourcesModal';
import { useAllProjects } from './contexts/AllProjects';
import { useAvailability } from './contexts/AvailabilityContext';
import { useDateRange } from './contexts/DateRangeContext';
import NewBaselinesModal from './modals/NewBaselinesModal';
import DispatchjobsBar from './dispatchJobs/DispatchjobsBar';
import AssignTeamLeadModal from './modals/AssignTeamLeadModal';
import SetResourceInactivityModal from './modals/SetResourceInactivityModal';
import RemoveResourceInactivityModal from './modals/RemoveResourceInactivityModal';
import StatisticsDrawer from './utilizationDrawer/StatisticsDrawer';
import InstallationsMapModal from './modals/InstallationsMapModal';
import IncompleteHoursModal from './modals/IncompleteHoursModal';

export default function InstallationPlanningContainer() {
  const { teams, teamAvailabilityMap } = useAvailability();
  const { weekdayMetrics } = useDateRange();
  const { allProjectsBeforeFiltersAndUnsavedChangesApplied } = useAllProjects();

  if (
    weekdayMetrics.weekdaysInRange.length === 0 ||
    weekdayMetrics.nrOfWeekdaysPerMonth.length === 0 ||
    weekdayMetrics.nrWeekdaysPerWeekNumber.length === 0 ||
    teamAvailabilityMap === undefined ||
    allProjectsBeforeFiltersAndUnsavedChangesApplied === undefined ||
    teams === undefined ||
    weekdayMetrics.workingHoursForDatesMap.size === 0
  ) {
    return null;
  }

  return (
    <Stack sx={{ p: 0, position: 'relative', maxHeight: '100dvh', maxWidth: '100dvw' }} direction="row">
      <Stack sx={{ width: '100%' }}>
        <JobsContainer />
        <TeamsEditorModal />
        <JobEditModal />
        <AssignTeamLeadModal />
        <RemoveAssignedResourcesModal />
        <NoBaselineHoursModal />
        <IncompleteHoursModal />
        <SetResourceInactivityModal />
        <RemoveResourceInactivityModal />
        <InstallationsMapModal />
      </Stack>
      <DispatchjobsBar />
      <NewBaselinesModal />
      <FiltersTab />
      <RightSidebar />
      <StatisticsDrawer />
      <UnsavedChangesSidebar />
      <UnsavedChangesToast />
    </Stack>
  );
}
