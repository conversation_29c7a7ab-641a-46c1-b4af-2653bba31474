import { Stack, Typography, IconButton, Box, Divider } from '@mui/material';
import { brandYellow, green, grey, magenta, red, teal } from '@ui/theme/colors';
import { InstallationProjectStage } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { ArrowRightOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightOutlinedIcon';
import { CalendarIcon } from '@ui/components/Icons/Calendar/CalendarIcon';
import { ClockOutlinedIcon } from '@ui/components/StandardIcons/ClockOutlinedIcon';
import { HousePersonOutsideOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutsideOutlinedIcon';
import { EllipsisHorizontalOutlinedIcon } from '@ui/components/StandardIcons/EllipsisHorizontalOutlinedIcon';
import { EyeOutlinedIcon } from '@ui/components/StandardIcons/EyeOutlinedIcon';
import { CrossSmallOutlinedIcon } from '@ui/components/StandardIcons/CrossSmallOutlinedIcon';
import { sendGTMEvent } from '@next/third-parties/google';
import { useRegionContext } from '../../../context/RegionContext';
import { formatDate } from '../helpers/dateHelpers';
import { ContinuousWorkSegmentForJobItem, ResourceBasedSegments } from '../types/planningTypes';
import JobResources from './JobResources';
import AssignedResourcesForJob from './AssignedResourcesForJob';
import { useProjectActions, useRightClickMenu } from '../stores/ProjectStore';
import { useCanEdit } from '../hooks/useCanEdit';
import { useRef } from 'react';

export default function JobTooltip({
  continuousWorkSegment,
  hasAssignedResources,
  continuousSegmentsByResource,
  resourceId,
  toolTipPosition,
}: {
  continuousWorkSegment: ContinuousWorkSegmentForJobItem;
  hasAssignedResources: boolean;
  continuousSegmentsByResource: ResourceBasedSegments;
  resourceId?: string;
  toolTipPosition: {
    top: number;
    right: number;
  };
}) {
  const region = useRegionContext();
  const { setSelectedProject, setShowRightSidebar, setToolTipProject, setRightClickMenu } = useProjectActions();
  const rightClickMenu = useRightClickMenu();
  const { timeZone } = region;
  const { installationProject } = continuousWorkSegment;
  const canEdit = useCanEdit();
  const tooltipRef = useRef<HTMLDivElement>(null);
  const customerName = `${continuousWorkSegment.contact?.firstName} ${continuousWorkSegment.contact?.lastName}`;

  const customerAddress = continuousWorkSegment.address?.specificDetails?.formattedAddress.split(',');

  const firstPartAddress = customerAddress?.slice(0, -2).join(',');
  const secondPartAddress = customerAddress?.slice(-2).join(',');

  const stage = () => {
    if (!installationProject) return null;
    let text;
    let color;
    switch (true) {
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE:
        text = 'Invoice';
        color = magenta[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED:
        text = 'Completed';
        color = magenta[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION:
        text = 'Post-installation';
        color = magenta[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW:
        text = 'New';
        color = grey[100];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_SURVEY:
        text = 'Technical survey scheduled';
        color = grey[200];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_DESIGN:
        text = 'Awaiting technical design';
        color = teal[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION:
        text = 'Pre-installation';
        color = green[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION:
        text = 'Installation';
        color = brandYellow[300];
        break;
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_CLOSED_LOST:
        text = 'Deal close lost';
        color = red[600];
        break;
      default:
        text = 'Unknown';
        color = grey[200];
    }
    return (
      <Stack direction="row" alignItems="center" gap={3} sx={{ pl: '10px' }}>
        <Box sx={{ width: '12px', height: '12px', backgroundColor: color, borderRadius: '50%' }} />
        <Typography variant="body1Emphasis">{text}</Typography>
      </Stack>
    );
  };

  return (
    <Stack
      width="fit-content"
      py={1}
      sx={{ position: 'relative', background: 'white', height: 'fit-content' }}
      ref={tooltipRef}
    >
      <Stack direction="row" justifyContent="space-between" px={2} pb={2} pt={1}>
        {stage()}
        <Stack direction="row" gap={2}>
          <IconButton
            onClick={() => {
              sendGTMEvent({
                event: 'button_click',
                click_text: 'job_tooltip_eye',
                app_name: 'installation_planning',
              });
              setSelectedProject(continuousWorkSegment);
              setShowRightSidebar(true);
              setRightClickMenu(null);
            }}
            sx={{ background: grey[100], height: '24px', width: '24px', p: 0 }}
          >
            <EyeOutlinedIcon color={grey[900]} height={20} width={20} />
          </IconButton>
          {!resourceId && canEdit && (
            <IconButton
              onClick={() => {
                if (rightClickMenu) {
                  setRightClickMenu(null);
                } else {
                  if (tooltipRef.current) {
                    setRightClickMenu({
                      position: {
                        x: tooltipRef.current.getBoundingClientRect().right - 100,
                        y: tooltipRef.current.getBoundingClientRect().top + 46,
                        up: false,
                      },
                      project: continuousWorkSegment,
                      hasAssignedResources,
                      jobId: continuousWorkSegment.segmentDetails.jobId,
                    });
                    sendGTMEvent({
                      event: 'button_click',
                      click_text: 'job_tooltip_ellipsis',
                      app_name: 'installation_planning',
                    });
                  }
                }
              }}
              sx={{ background: grey[100], height: '24px', width: '24px', p: 0 }}
            >
              <EllipsisHorizontalOutlinedIcon height={20} width={20} color={grey[900]} />
            </IconButton>
          )}
          <IconButton
            onClick={() => setToolTipProject()}
            sx={{ background: grey[100], height: '24px', width: '24px', p: 0 }}
          >
            <CrossSmallOutlinedIcon height={20} width={20} color={grey[900]} />
          </IconButton>
        </Stack>
      </Stack>
      <Stack direction="column" justifyContent="space-between">
        <Stack px={2} direction="column" gap={2}>
          <Stack direction="column">
            <Stack direction="row" gap={2} p={1}>
              <HousePersonOutsideOutlinedIcon color={grey[700]} height={20} width={20} />
              <Stack sx={{ maxWidth: '400px' }}>
                <Typography variant="body2">{customerName}</Typography>
                <Typography variant="body2">{firstPartAddress}</Typography>
                {secondPartAddress && <Typography variant="body2">{secondPartAddress}</Typography>}
              </Stack>
            </Stack>
          </Stack>
          <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
            <Stack direction="row" gap={2} px={1} mt={1} alignItems="flex-start">
              <CalendarIcon color={grey[700]} height={20} width={20} />
              <Stack direction="row" gap={2} alignItems="center" alignSelf="flex-start">
                <Stack>
                  <Typography variant="body2">
                    {formatDate(continuousWorkSegment.segmentDetails.startTime, timeZone).split(',')[0]}
                  </Typography>

                  <Typography variant="body2">
                    {formatDate(continuousWorkSegment.segmentDetails.startTime, timeZone).split(',')[1]}
                  </Typography>
                </Stack>

                <ArrowRightOutlinedIcon color={grey[400]} height={20} width={20} />

                <Stack>
                  <Typography variant="body2">
                    {formatDate(continuousWorkSegment.segmentDetails.endTime, timeZone).split(',')[0]}
                  </Typography>
                  <Typography variant="body2">
                    {formatDate(continuousWorkSegment.segmentDetails.endTime, timeZone).split(',')[1]}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>

            <Stack direction="row" gap={2} p={1}>
              <ClockOutlinedIcon color={grey[700]} height={20} width={20} />
              <Typography variant="body2">{continuousWorkSegment.segmentDetails.duration} hours</Typography>
            </Stack>
          </Stack>
          <JobResources assignedResources={continuousWorkSegment.installationProject?.assignedResources || []} />
        </Stack>
      </Stack>

      {!resourceId && (
        <>
          <Divider sx={{ my: 3, mx: 3 }} />
          <AssignedResourcesForJob
            toolTipPosition={toolTipPosition}
            continuousWorkSegment={continuousWorkSegment}
            continuousSegmentsByResource={continuousSegmentsByResource}
          />
        </>
      )}
    </Stack>
  );
}
