/* eslint-disable @next/next/no-img-element */
import { Box, Skeleton, Stack, Typography } from '@mui/material';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { beige, grey } from '@ui/theme/colors';
import { InstallationProjectResourceType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { api } from 'utils/api';
import { InstallationProjectResource } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

const leadResourceTypes = [
  InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_DESIGN_ENGINEER,
  InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_PLANNER,
  InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_TEAM_LEAD,
];

function Resource({ person }: { person: UserIdentity }) {
  return (
    <Stack direction="row" gap={2} alignItems="flex-start" px={0} py={1}>
      {person.userImage ? (
        <Box
          sx={{
            borderRadius: '50%',
            width: '20px',
            height: '20px',
            backgroundColor: grey[100],
          }}
        >
          <img
            src={`data:image/jpeg;base64,${Buffer.from(person.userImage).toString('base64')}`}
            alt={`${person.firstName} ${person.lastName}`}
            style={{
              width: '20px',
              height: '20px',
              borderRadius: '50%',
            }}
          />
        </Box>
      ) : (
        <Box
          sx={{
            width: '20px',
            height: '20px',
            backgroundColor: beige[100],
            borderRadius: '50%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Typography variant="body2" fontSize={8} sx={{ textTransform: 'uppercase' }}>
            {person.firstName[0]} {person.lastName[0]}
          </Typography>
        </Box>
      )}
      <Stack>
        <Typography variant="body2" key={person.userId!.value}>
          {person.firstName} {person.lastName}
        </Typography>
        <Typography variant="body2Emphasis" pb={1}>
          {person.jobTitle}
        </Typography>
      </Stack>
    </Stack>
  );
}

export default function JobResources({
  assignedResources,
  asColumn,
}: {
  assignedResources: InstallationProjectResource[];
  asColumn?: boolean;
}) {
  const { data: leadRolesResources, isLoading: isLoadingResources } =
    api.InstallationProject.getAssignedResourceInfo.useQuery({
      resourceIds:
        assignedResources.filter((res) => leadResourceTypes.includes(res.type)).map((res) => res.userId!.value) || [],
      fullView: true,
    });

  return (
    <Stack gap={1} pl="6px">
      <Stack gap={asColumn ? 0 : 2} direction={asColumn ? 'column' : 'row'} alignItems="flex-start">
        {isLoadingResources || !leadRolesResources
          ? leadResourceTypes.map((type) => {
              return <Skeleton key={type} variant="text" width={100} height="16px" />;
            })
          : leadRolesResources.map((res, i) => {
              return <Resource key={i} person={res} />;
            })}
      </Stack>
    </Stack>
  );
}
