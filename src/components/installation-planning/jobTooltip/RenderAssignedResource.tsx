import { Skeleton, Stack, Typography } from '@mui/material';
import useTableCellDims from 'components/installation-planning/hooks/useTableCellDims';
import { useResources, useResourcesLoading } from 'components/installation-planning/contexts/ResourcesContext';
import ResourceImage from '../table/ResourceImage';

export default function RenderAssignedResource({ resourceId }: { resourceId: string }) {
  const { CELL_HEIGHT } = useTableCellDims();
  const { isLoadingResources } = useResourcesLoading();
  const resourcesForRegion = useResources();
  const resource = resourcesForRegion.get(resourceId);

  if (isLoadingResources && !resourcesForRegion) {
    return <Skeleton variant="text" width={100} height="16px" />;
  }

  if (!resource) {
    return null;
  }

  return (
    <Stack
      direction="row"
      spacing={1}
      sx={{
        height: `${CELL_HEIGHT}px`,
        minHeight: `${CELL_HEIGHT}px`,
        width: 'auto',
        maxWidth: '100%',
        justifyContent: 'flex-start',
        alignItems: 'center',
        pl: 1,
        pr: 0,
        pt: 0,
        background: '#fff',
      }}
    >
      <ResourceImage resource={resource} size="small" />

      <Stack direction="row" alignItems="center" spacing={1}>
        <Typography variant="body2" fontSize={12}>
          {resource.firstName} {resource.lastName}
        </Typography>
      </Stack>
    </Stack>
  );
}
