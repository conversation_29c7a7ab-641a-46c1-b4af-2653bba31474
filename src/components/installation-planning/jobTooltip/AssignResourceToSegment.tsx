import { Box, Tooltip, Typography, Stack, IconButton, CircularProgress } from '@mui/material';
import { beige, red, brandYellow } from '@ui/theme/colors';
import { api } from 'utils/api';
import { isPast, isSameDay, format } from 'date-fns';
import { FormattedMessage } from 'react-intl';
import { ServiceVisit } from '@aira/service-visit-grpc-api/build/ts_out/com/aira/acquisition/contract/service/visit/v1/model';
import toast from 'react-hot-toast';
import { useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import { ContinuousWorkSegment } from '../types/planningTypes';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { useResources } from '../contexts/ResourcesContext';
import { useCanEdit } from '../hooks/useCanEdit';
import { WorkSegmentForUpdate } from '../jobTooltip/AddResourcesToJob';
import { useResourceUnutilization } from '../contexts/ResourceUnutilizationContext';

export default function AssignResourceToSegment({
  isSaving,
  setIsSaving,
  jobHasUnsavedChanges,
  continuousWorkSegment,
  containsCurrentJob,
  wsIndex,
  sequenceNumber,
  startTime,
  resourceId,
  continuousWorkSegmentsForResource,
  serviceVisitsForResource,
}: {
  isSaving: boolean;
  setIsSaving: (isSaving: boolean) => void;
  jobHasUnsavedChanges: boolean;
  continuousWorkSegment: ContinuousWorkSegment;
  containsCurrentJob: boolean;
  wsIndex: number;
  sequenceNumber: number;
  startTime: Date;
  resourceId: string;
  continuousWorkSegmentsForResource: ContinuousWorkSegment[];
  serviceVisitsForResource: ServiceVisit[];
}) {
  const [isSavingSegment, setIsSavingSegment] = useState(false);
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const resourcesForRegion = useResources();
  const resourceName = `${resourcesForRegion.get(resourceId)?.firstName} ${
    resourcesForRegion.get(resourceId)?.lastName
  }`;
  const { installationProject } = continuousWorkSegment;
  const { mutateAsync: updateSegmentsAssignees } = api.InstallationProject.updateWorkSegmentsAssignees.useMutation();
  const canEdit = useCanEdit();

  const { resourceUnutilizationMap } = useResourceUnutilization();
  const checkUserAvailability = (userId: string, startTime: Date, duration?: number) => {
    const dateKey = format(startTime, 'yyyy-MM-dd');
    const hour = startTime.getHours();
    const timeKey = hour < 12 ? '08' : '12';

    const userUnutilizedTimeSlot = resourceUnutilizationMap.get(userId)?.get(dateKey + '-' + timeKey);
    if (userUnutilizedTimeSlot) {
      return { status: 'unavailable', reason: userUnutilizedTimeSlot.reason };
    }

    if (duration) {
      const startHour = startTime.getHours();
      const endHour = startHour + Math.floor(duration / 3600);

      if (startHour < 12 && endHour >= 12) {
        const afternoonUnutilizedTimeSlot = resourceUnutilizationMap.get(userId)?.get(dateKey + '-12');
        if (afternoonUnutilizedTimeSlot) {
          return { status: 'unavailable', reason: afternoonUnutilizedTimeSlot.reason };
        }
      }
    }

    if (timeKey === '12') {
      const userUnutilizedTimeSlotFullDay = resourceUnutilizationMap.get(userId)?.get(dateKey + '-08');
      if (userUnutilizedTimeSlotFullDay && userUnutilizedTimeSlotFullDay.endTime === 16) {
        return { status: 'unavailable', reason: userUnutilizedTimeSlotFullDay.reason };
      }
    }

    return { status: 'available', reason: null };
  };

  const { jobId } = continuousWorkSegment.segmentDetails;
  const job = continuousWorkSegment.installationProject?.jobs?.find((j) => j.id?.value === jobId);
  const currentWorkSegment = job?.workSegments?.find((ws) => ws.sequenceNumber === sequenceNumber);
  const segmentDuration = currentWorkSegment?.duration?.seconds;

  const resourceAvailability = checkUserAvailability(resourceId, startTime, segmentDuration);
  const conflictingSegments = continuousWorkSegmentsForResource.filter((cws) => {
    const { segmentDetails: cwsSegmentDetails, installationProject: cwsInstallationProject } = cws;
    const { workSegments: cwsWorkSegments } = cwsSegmentDetails;
    const id = cwsInstallationProject?.id?.value;
    if (!id) return false;
    if (id === continuousWorkSegment.installationProject?.id?.value) {
      return false;
    }
    if (cwsWorkSegments.some((ws) => ws.startTime && isSameDay(ws.startTime, startTime))) {
      return cwsWorkSegments.some((ws) => ws.assignedResources.some((ar) => ar.userId?.value === resourceId));
    }
    return false;
  });

  const conflictingServiceVisits = serviceVisitsForResource.filter((sv) => {
    const serviceVisitStartTime = sv.jobState?.startTime;
    if (!serviceVisitStartTime) return false;
    return isSameDay(serviceVisitStartTime, startTime);
  });

  const removeResourceFromSegment = async () => {
    const id = installationProject?.id?.value;
    const { jobId } = continuousWorkSegment.segmentDetails;
    const job = continuousWorkSegment.installationProject?.jobs?.find((j) => j.id?.value === jobId);
    if (!id || !job) return;
    setIsSaving(true);
    setIsSavingSegment(true);

    const jobWorkSegments = job.workSegments;
    const newWorkSegments: WorkSegmentForUpdate[] = jobWorkSegments.map((ws) => {
      if (ws.sequenceNumber === sequenceNumber) {
        return {
          startTime: ws.startTime!,
          teamId: ws.teamId,
          duration: ws.duration!,
          assigneeUserIds: ws.assignedResources
            .filter((ar) => ar.userId?.value !== resourceId)
            .map((ar) => ({ value: ar.userId?.value })) as { value: string }[],
        };
      }
      return {
        startTime: ws.startTime!,
        teamId: ws.teamId,
        duration: ws.duration!,
        assigneeUserIds: ws.assignedResources.map((ar) => ({ value: ar.userId?.value })) as { value: string }[],
      };
    });

    try {
      for (const ws of newWorkSegments) {
        if (!ws.duration) {
          throw new Error('Duration is required');
        }
      }
      const res = await updateSegmentsAssignees({
        jobId,
        workSegments: newWorkSegments,
      });
      if (res.job) {
        const result = await refetchProjectsUpdatedSince();
        if (result.status === 'success') {
          toast.success('Resource removed from segment');
        }
      } else {
        toast.error('Failed to remove resource from segment');
      }
    } catch (_error) {
      toast.error('Duration is required');
    } finally {
      setIsSaving(false);
      setIsSavingSegment(false);
    }
  };

  const addResourceToSegment = async () => {
    const id = installationProject?.id?.value;
    const { jobId } = continuousWorkSegment.segmentDetails;
    const job = continuousWorkSegment.installationProject?.jobs?.find((j) => j.id?.value === jobId);
    if (!id || !job) return;
    setIsSaving(true);
    setIsSavingSegment(true);
    const newWorkSegments = job.workSegments.map((ws) => {
      if (ws.sequenceNumber === sequenceNumber) {
        return {
          startTime: ws.startTime,
          teamId: ws.teamId,
          duration: ws.duration!,
          assigneeUserIds: [
            ...ws.assignedResources.map((ar) => ({ value: ar.userId?.value })),
            { value: resourceId },
          ].filter((ar) => ar.value !== null) as { value: string }[],
        };
      }
      return {
        startTime: ws.startTime,
        teamId: ws.teamId,
        duration: ws.duration!,
        assigneeUserIds: ws.assignedResources.map((ar) => ({ value: ar.userId?.value })) as { value: string }[],
      };
    });
    try {
      for (const ws of newWorkSegments) {
        if (!ws.duration) {
          throw new Error('Duration is required');
        }
      }
      const res = await updateSegmentsAssignees({
        jobId,
        workSegments: newWorkSegments,
      });
      if (res.job) {
        const result = await refetchProjectsUpdatedSince();
        if (result.status === 'success') {
          toast.success('Resource added to segment');
        }
      } else {
        toast.error('Failed to add resource to segment');
      }
    } catch (_error) {
      toast.error('Duration is required');
    } finally {
      setIsSavingSegment(false);
      setIsSaving(false);
    }
  };

  const isDisabled = jobHasUnsavedChanges || (isPast(startTime) && !isSameDay(startTime, new Date()));

  const isResourceAvailable = resourceAvailability.status === 'available';

  const getBorderColor = () => {
    if (conflictingSegments.length > 0 || !isResourceAvailable) {
      return red[300];
    }
    return containsCurrentJob ? brandYellow[300] : beige[100];
  };

  const getBackgroundColor = () => {
    if ((conflictingSegments.length > 0 && containsCurrentJob) || !isResourceAvailable) {
      return red[200];
    }
    return containsCurrentJob ? brandYellow[300] : beige[100];
  };

  return (
    <Stack
      justifyContent="center"
      alignItems="center"
      key={wsIndex}
      sx={{
        position: 'relative',
        height: 40,
        width: 40,
        padding: '1px 3px',
      }}
    >
      <Tooltip
        arrow
        disableHoverListener={
          conflictingSegments.length === 0 && conflictingServiceVisits.length === 0 && isResourceAvailable
        }
        placement="top"
        disableInteractive
        slotProps={{
          popper: {
            sx: {
              zIndex: 9999,
            },
          },
          tooltip: {
            sx: {
              backgroundColor: '#fff',
              borderRadius: '16px',
              padding: 3,
              boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
            },
          },
          arrow: {
            sx: {
              color: '#fff',
            },
          },
        }}
        title={
          conflictingServiceVisits.length > 0 || conflictingSegments.length > 0 ? (
            <Stack spacing={1}>
              {conflictingServiceVisits.length > 0 && (
                <Stack spacing={1}>
                  <Typography variant="body1" fontSize={12}>
                    <FormattedMessage
                      id="installationPlanning.assignResourceToSegment.conflictingServiceVisits"
                      defaultMessage={`${resourceName} is assigned to the following service visits on this day:`}
                      values={{ resourceName }}
                    />
                  </Typography>
                  {conflictingServiceVisits.map((sv) => (
                    <Typography variant="body1Emphasis" fontSize={12} key={sv.id?.value}>
                      {sv.summary.split(' | ')[1]}
                    </Typography>
                  ))}
                </Stack>
              )}
              {conflictingSegments.length > 0 && (
                <Stack spacing={1}>
                  <Typography variant="body1" fontSize={12}>
                    <FormattedMessage
                      id="installationPlanning.assignResourceToSegment.conflictingSegments"
                      defaultMessage={`${resourceName} is already assigned to the following customers on this day:`}
                      values={{ resourceName }}
                    />
                  </Typography>
                  {conflictingSegments.map((cws) => (
                    <Typography
                      variant="body1Emphasis"
                      fontSize={12}
                      key={`${cws.contact?.firstName}-${cws.contact?.lastName}`}
                    >
                      {cws.contact?.firstName} {cws.contact?.lastName}
                    </Typography>
                  ))}
                </Stack>
              )}
            </Stack>
          ) : (
            <Stack spacing={1}>
              <Typography variant="body1" fontSize={12}>
                <FormattedMessage
                  id="installationPlanning.assignResourceToSegment.unavailable"
                  defaultMessage={`${resourceName} is unavailable for this time`}
                />
              </Typography>
              {resourceAvailability.reason && (
                <Typography variant="body1" fontSize={12}>
                  Reason: {resourceAvailability.reason === 'vacation' && 'On vacation'}
                  {resourceAvailability.reason === 'sickness' && 'Sick leave'}
                  {resourceAvailability.reason === 'training' && 'In training'}
                  {resourceAvailability.reason === 'timeCompensation' && 'Time compensation'}
                  {resourceAvailability.reason === 'holiday' && 'Public holiday'}
                  {resourceAvailability.reason === 'other' && 'Other reason'}
                  {resourceAvailability.reason === 'noOrder' && 'No order'}
                  {resourceAvailability.reason === 'customerAbsence' && 'Customer absence'}
                  {resourceAvailability.reason === 'lentOut' && 'Lent out'}
                </Typography>
              )}
            </Stack>
          )
        }
      >
        <Box
          sx={{
            width: 28,
            height: 28,
            background: getBackgroundColor(),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '8px',
            border: `1px solid ${getBorderColor()}`,
          }}
        >
          <IconButton
            onClick={() => {
              sendGTMEvent({
                event: 'button_click',
                click_text: 'assign_resource_to_segment',
                app_name: 'installation_planning',
              });
              if (containsCurrentJob) {
                removeResourceFromSegment();
              } else {
                addResourceToSegment();
              }
            }}
            disabled={isDisabled || isSaving || !canEdit}
          >
            {isSavingSegment ? (
              <CircularProgress size={20} />
            ) : (
              <Typography variant="body2Emphasis" fontSize={12}>
                {startTime?.toLocaleString('en-US', {
                  day: '2-digit',
                })}
              </Typography>
            )}
          </IconButton>
        </Box>
      </Tooltip>
    </Stack>
  );
}
