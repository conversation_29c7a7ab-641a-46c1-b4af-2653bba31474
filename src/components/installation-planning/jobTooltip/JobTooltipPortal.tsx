import { Stack } from '@mui/material';

import { Portal } from '@mui/material';
import {
  useShowRemoveAssignedResourcesModal,
  useShowAssignTeamLeadModal,
  useJobBeingSplit,
  useToolTipProject,
  useJobBeingEdited,
} from '../stores/ProjectStore';
import { ARROW_SIZE } from '../jobBar/jobItemHelpers';
import JobTooltip from './JobTooltip';
import { ResourceBasedSegments, RoleBasedSegments } from '../types/planningTypes';

export default function JobTooltipPortal({
  continuousSegmentsByResource,
  continuousSegmentsByRole,
}: {
  continuousSegmentsByResource: ResourceBasedSegments;
  continuousSegmentsByRole: RoleBasedSegments;
}) {
  const showAssignTeamLeadModal = useShowAssignTeamLeadModal();
  const showRemoveAssignedResourcesModal = useShowRemoveAssignedResourcesModal();
  const jobBeingSplit = useJobBeingSplit();
  const toolTipProject = useToolTipProject();
  const jobBeingEdited = useJobBeingEdited();

  if (
    !toolTipProject ||
    jobBeingEdited ||
    showRemoveAssignedResourcesModal ||
    showAssignTeamLeadModal ||
    jobBeingSplit
  ) {
    return null;
  }

  const { segmentDetails, resourceId, position } = toolTipProject;
  const { teamId, requiredRole, jobId, workSegments } = segmentDetails;

  const segmentsForRole = continuousSegmentsByRole.get(requiredRole);
  let continuousWorkSegment;
  if (teamId) {
    continuousWorkSegment = segmentsForRole?.continuousWorkSegmentsWithTeam
      .get(teamId)
      ?.find(
        (segment) =>
          segment.segmentDetails.jobId === jobId &&
          segment.segmentDetails.workSegments[0]?.sequenceNumber === workSegments[0]?.sequenceNumber,
      );
  } else {
    continuousWorkSegment = segmentsForRole?.continuousWorkSegmentsWithoutTeam.find(
      (segment) =>
        segment.segmentDetails.jobId === jobId &&
        segment.segmentDetails.workSegments[0]?.sequenceNumber === workSegments[0]?.sequenceNumber,
    );
  }

  if (!continuousWorkSegment) {
    return null;
  }

  const { hasAssignedResources } = continuousWorkSegment;

  return (
    <Portal>
      <Stack
        direction="row"
        sx={{
          position: 'absolute',
          backgroundColor: '#fff',
          borderRadius: '8px',
          zIndex: 5201,
          flex: 1,
          width: 'fit-content',
          color: '#fff',
          padding: '8px',
          boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
          height: 'fit-content',
          ...position,
          '&::after': {
            content: '""',
            position: 'absolute',
            right: `-${ARROW_SIZE}px`,
            top: position?.arrowTop ?? 0,
            width: 0,
            height: 0,
            borderTop: `${ARROW_SIZE}px solid transparent`,
            borderBottom: `${ARROW_SIZE}px solid transparent`,
            borderLeft: `${ARROW_SIZE}px solid #fff`,
            filter: 'drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.15))',
          },
        }}
      >
        <JobTooltip
          continuousWorkSegment={continuousWorkSegment}
          hasAssignedResources={hasAssignedResources}
          continuousSegmentsByResource={continuousSegmentsByResource}
          resourceId={resourceId}
          toolTipPosition={position}
        />
      </Stack>
    </Portal>
  );
}
