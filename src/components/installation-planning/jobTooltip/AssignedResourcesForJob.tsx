import { Box, CircularProgress, Stack, Typography } from '@mui/material';
import { useMemo, useState } from 'react';
import { grey } from '@ui/theme/colors';
import { isEqual } from 'date-fns';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { InfoIcon } from '@ui/components/StandardIcons/InfoIcon';
import { FormattedMessage } from 'react-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { useProjectActions, useUnsavedChanges } from '../stores/ProjectStore';
import { ContinuousWorkSegment, ResourceBasedSegments, ResourceForRegion } from '../types/planningTypes';
import AssignResourceToSegment from './AssignResourceToSegment';
import AddResourcesToJob, { WorkSegmentForUpdate } from '../jobTooltip/AddResourcesToJob';
import { useServicesVisits } from '../contexts/ServicesVisitsContext';
import { useAvailability } from '../contexts/AvailabilityContext';
import { useCanEdit } from '../hooks/useCanEdit';
import { useResources } from '../contexts/ResourcesContext';
import RenderAssignedResource from './RenderAssignedResource';
import { Button } from '@ui/components/Button/Button';
import toast from 'react-hot-toast';
import { api } from 'utils/api';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';

export default function AssignedResourcesForJob({
  continuousWorkSegment,
  continuousSegmentsByResource,
  toolTipPosition,
}: {
  continuousWorkSegment: ContinuousWorkSegment;
  continuousSegmentsByResource: ResourceBasedSegments;
  toolTipPosition: {
    top: number;
    right: number;
  };
}) {
  const { serviceVisitsForResourcesMap } = useServicesVisits();
  const [isSaving, setIsSaving] = useState(false);
  const resourcesForRegion = useResources();
  const { segmentDetails, installationProject } = continuousWorkSegment;
  const { teams } = useAvailability();
  const { jobId, workSegments, teamId } = segmentDetails;
  const unsavedChanges = useUnsavedChanges();
  const { setShowUnsavedChanges } = useProjectActions();
  const canEdit = useCanEdit();
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const [showAddResourcesToTeam, setShowAddResourcesToTeam] = useState(false);
  const [isAddingDefaultResources, setIsAddingDefaultResources] = useState(false);
  const { mutateAsync: updateSegmentsAssignees } = api.InstallationProject.updateWorkSegmentsAssignees.useMutation();

  const defaultResourcesForTeam = useMemo<string[]>(() => {
    const team = teams.find((t) => t.id?.value === teamId);
    return team?.defaultResources.map((id) => id.value) ?? [];
  }, [teams, teamId]);
  const jobHasUnsavedChanges = unsavedChanges.some((change) => change.jobId === jobId);
  const resourcesToShow = useMemo(() => {
    const resources = Array.from(
      new Set(
        segmentDetails.workSegments.flatMap((ws) =>
          ws.assignedResources.map((ar) => (ar.userId ? ar.userId?.value : null)).filter((ar) => ar !== null),
        ),
      ),
    )
      .map((userId) => ({ userId }))
      .sort((a, b) => {
        const aResource = resourcesForRegion.get(a.userId);
        const bResource = resourcesForRegion.get(b.userId);
        const aIsTeamResource = defaultResourcesForTeam.includes(a.userId);
        const bIsTeamResource = defaultResourcesForTeam.includes(b.userId);

        if (aIsTeamResource && !bIsTeamResource) return 1;
        if (!aIsTeamResource && bIsTeamResource) return -1;

        const getFullName = (resource: ResourceForRegion | undefined) => {
          if (!resource) return '';
          return [resource.firstName, resource.lastName].filter(Boolean).join(' ') || resource.email || '';
        };

        return getFullName(aResource).localeCompare(getFullName(bResource));
      });

    return resources;
  }, [resourcesForRegion, defaultResourcesForTeam, segmentDetails.workSegments]);

  const assignDefaultResources = async () => {
    if (defaultResourcesForTeam.length === 0) {
      toast.error('Team has no default resources');
      return;
    }
    const { jobId } = segmentDetails;
    const id = installationProject?.id?.value;
    const job = installationProject?.jobs.find((j) => j.id?.value === jobId);
    if (!id || !job) return;
    setIsAddingDefaultResources(true);

    const sequenceNumsForContinuousWorkSegment = continuousWorkSegment.segmentDetails.workSegments.map(
      (ws) => ws.sequenceNumber,
    );
    const jobWorkSegments = job.workSegments;
    const newWorkSegments: WorkSegmentForUpdate[] = jobWorkSegments.map((ws) => {
      // If the work segment is part of the continuous work segment, add the default resources, otherwise use the existing resources
      // This is to handle the case where the job has been split into multiple continuous work segments
      if (sequenceNumsForContinuousWorkSegment.includes(ws.sequenceNumber)) {
        return {
          startTime: ws.startTime!,
          teamId: ws.teamId,
          duration: ws.duration!,
          assigneeUserIds: [...defaultResourcesForTeam.map((resource) => ({ value: resource }))],
        };
      } else {
        return {
          duration: ws.duration!,
          assigneeUserIds: ws.assignedResources.map((ar) => ({ value: ar.userId!.value })),
          teamId: ws.teamId,
          startTime: ws.startTime!,
        };
      }
    });

    try {
      for (const ws of newWorkSegments) {
        if (!ws.duration) {
          throw new Error('Duration is required');
        }
      }
      const res = await updateSegmentsAssignees({
        jobId,
        workSegments: newWorkSegments,
      });
      if (res.job) {
        const result = await refetchProjectsUpdatedSince();

        if (result.status === 'success') {
          toast.success('Default resources added to job');
        }
      } else {
        toast.error('Failed to add default resources to job');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to add default resources to job');
    } finally {
      setIsAddingDefaultResources(false);
    }
  };
  return (
    <>
      <Stack
        sx={{
          height: 'fit-content',
          width: '100%',
          background: '#fff',
          borderRadius: '0 0 8px 8px',
          padding: '6px 18px 14px 18px',
          opacity: jobHasUnsavedChanges ? 0.5 : 1,
          marginBottom: '18px',
        }}
      >
        <Stack direction="row" alignItems="flex-start" justifyContent="flex-start" width="100%" sx={{ zIndex: 2201 }}>
          <Stack alignItems="flex-start" justifyContent="flex-start" width="100%">
            <Stack
              direction="row"
              alignItems="center"
              justifyContent="flex-start"
              alignSelf="flex-start"
              sx={{
                position: 'relative',
                padding: '0px 4px 0 4px',
                background: '#fff',
                width: '190px',
                height: '28px',
                mb: '10px',
              }}
            >
              <Typography variant="body2">Resources</Typography>
            </Stack>

            {resourcesToShow.length > 0 && (
              <Stack
                sx={{
                  background: '#fff',
                  width: '198px',
                  height: '28px',
                  gap: '16px',
                }}
              >
                {resourcesToShow.map((resource) => {
                  const resourceId = resource.userId;
                  return (
                    <Stack
                      key={resource.userId}
                      direction="row"
                      alignItems="center"
                      sx={{
                        width: '100%',
                        height: '28px',
                      }}
                    >
                      <Box sx={{ height: '28px' }}>
                        <RenderAssignedResource resourceId={resourceId} />
                      </Box>
                    </Stack>
                  );
                })}
              </Stack>
            )}
          </Stack>
          <Stack>
            <Box sx={{ height: '28px', width: '100%', mb: '18px' }}>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                gap={4}
                sx={{
                  width: '100%',
                  height: 28,
                  padding: '0 10px',
                }}
              >
                {segmentDetails.workSegments.map((ws) => (
                  <Typography variant="body2Emphasis" fontSize={12} key={ws.startTime?.toLocaleString()}>
                    {ws.startTime?.toLocaleString('en-US', {
                      weekday: 'short',
                    })}
                  </Typography>
                ))}
              </Stack>
            </Box>

            {resourcesToShow.length > 0 && (
              <Stack
                sx={{
                  width: '100%',
                  gap: '16px',
                }}
              >
                {resourcesToShow.map((resource) => {
                  const resourceId = resource.userId;
                  const continuousWorkSegmentsForResource = continuousSegmentsByResource.get(resourceId);
                  const continuousWorkSegmentsForCurrentJob =
                    continuousWorkSegmentsForResource?.continuousWorkSegments.filter(
                      (ws) => ws.segmentDetails.jobId === jobId,
                    );
                  const serviceVisitsForResource = serviceVisitsForResourcesMap.get(resourceId) ?? [];

                  return (
                    <Stack
                      key={resource.userId}
                      direction="row"
                      alignItems="center"
                      gap={4}
                      sx={{
                        width: '100%',
                        height: '28px',
                      }}
                    >
                      {workSegments?.map((segment, wsIndex) => {
                        const { startTime, endTime, sequenceNumber } = segment;
                        if (!startTime || !endTime || !continuousWorkSegmentsForResource) return null;

                        const containsCurrentJob = continuousWorkSegmentsForCurrentJob?.find((cws) =>
                          cws.segmentDetails.workSegments.some(
                            (ws) =>
                              cws.segmentDetails.jobId === jobId &&
                              ws.startTime &&
                              ws.endTime &&
                              isEqual(ws.startTime, startTime) &&
                              isEqual(ws.endTime, endTime),
                          ),
                        );
                        return (
                          <AssignResourceToSegment
                            key={`${resourceId}-${sequenceNumber}`}
                            isSaving={isSaving}
                            setIsSaving={setIsSaving}
                            continuousWorkSegmentsForResource={continuousWorkSegmentsForResource.continuousWorkSegments}
                            jobHasUnsavedChanges={jobHasUnsavedChanges}
                            containsCurrentJob={Boolean(containsCurrentJob)}
                            wsIndex={wsIndex}
                            continuousWorkSegment={continuousWorkSegment}
                            sequenceNumber={segment.sequenceNumber}
                            startTime={startTime}
                            resourceId={resourceId}
                            serviceVisitsForResource={serviceVisitsForResource}
                          />
                        );
                      })}
                    </Stack>
                  );
                })}
              </Stack>
            )}
          </Stack>
        </Stack>
        {!jobHasUnsavedChanges && showAddResourcesToTeam && (
          <AddResourcesToJob
            toolTipPosition={toolTipPosition}
            continuousWorkSegment={continuousWorkSegment}
            setShowAddResourcesToTeam={setShowAddResourcesToTeam}
            teamId={teamId}
            isSaving={isSaving}
            setIsSaving={setIsSaving}
          />
        )}
      </Stack>
      {canEdit && !jobHasUnsavedChanges && (
        <Stack
          direction="column"
          alignItems="center"
          justifyContent="center"
          width="100%"
          gap="8px"
          sx={{ padding: '0 20px 16px 20px' }}
        >
          {resourcesToShow.length === 0 && (
            <Button
              variant="grey"
              fullWidth
              sx={{ borderRadius: '67px', height: '48px' }}
              onClick={() => {
                sendGTMEvent({
                  event: 'button_click',
                  click_text: 'assign_default_resources',
                  app_name: 'installation_planning',
                });
                assignDefaultResources();
              }}
              disabled={isAddingDefaultResources}
            >
              {isAddingDefaultResources ? (
                <CircularProgress size={20} />
              ) : (
                <>
                  <AddOutlinedIcon height={20} width={20} style={{ marginRight: '8px' }} />
                  Assign default
                </>
              )}
            </Button>
          )}
          <Button
            variant="grey"
            fullWidth
            sx={{ borderRadius: '67px', height: '48px' }}
            onClick={() => {
              sendGTMEvent({
                event: 'button_click',
                click_text: 'add_resources_to_job',
                app_name: 'installation_planning',
              });
              setShowAddResourcesToTeam(true);
            }}
            disabled={isAddingDefaultResources}
          >
            <AddOutlinedIcon height={20} width={20} style={{ marginRight: '8px' }} />
            Add resource
          </Button>
        </Stack>
      )}

      {jobHasUnsavedChanges && (
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="center"
          width="100%"
          onClick={() => setShowUnsavedChanges(true)}
          sx={{
            padding: '0 16px 16px 16px',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#22222608',
              borderRadius: '67px',
              border: 0,
              width: '100%',
              padding: '16px',
              gap: '8px',
              '&:hover': {
                cursor: 'pointer',
              },
            }}
            onClick={() => setShowUnsavedChanges(true)}
          >
            <InfoIcon height={16} width={16} color={grey[700]} />
            <Typography variant="body1" color={grey[700]}>
              <FormattedMessage
                id="installationPlanning.saveJobChangesToChangeAssignees"
                defaultMessage="Save job changes to change assignees"
              />
            </Typography>
          </Box>
        </Stack>
      )}
    </>
  );
}
