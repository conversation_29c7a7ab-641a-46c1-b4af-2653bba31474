/* eslint-disable @next/next/no-img-element */
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { Box, CircularProgress, Divider, IconButton, Portal, Stack, Typography } from '@mui/material';
import { useEffect, useMemo, useRef, useState } from 'react';
import { beige, grey } from '@ui/theme/colors';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { CheckIndeterminateOutlinedIcon } from '@ui/components/StandardIcons/CheckIndeterminateOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { sendGTMEvent } from '@next/third-parties/google';
import { ContinuousWorkSegment, ResourceForRegion } from '../types/planningTypes';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { useAvailability } from '../contexts/AvailabilityContext';
import { useResources } from '../contexts/ResourcesContext';

export type WorkSegmentForUpdate = {
  startTime: Date;
  teamId?: { value: string };
  duration: { seconds: number; nanos: number };
  assigneeUserIds: { value: string }[];
};

export default function AddResourcesToJob({
  isSaving,
  setIsSaving,
  setShowAddResourcesToTeam,
  continuousWorkSegment,
  toolTipPosition,
  teamId,
}: {
  isSaving: boolean;
  setIsSaving: (isSaving: boolean) => void;
  setShowAddResourcesToTeam: (show: boolean) => void;
  continuousWorkSegment: ContinuousWorkSegment;
  toolTipPosition: {
    top: number;
    right: number;
  };
  teamId?: string;
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isSavingResource, setIsSavingResource] = useState<string | null>(null);
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const resourcesForRegion = useResources();
  const { teams } = useAvailability();
  const { segmentDetails, installationProject } = continuousWorkSegment;
  const { requiredRole } = segmentDetails;

  const defaultResourcesForTeam = useMemo<string[]>(() => {
    const team = teams.find((t) => t.id?.value === teamId);
    return team?.defaultResources.map((id) => id.value) ?? [];
  }, [teams, teamId]);

  const { mutateAsync: updateSegmentsAssignees } = api.InstallationProject.updateWorkSegmentsAssignees.useMutation();

  const resourcesInJob = useMemo<{ userId: string }[]>(() => {
    const resources = Array.from(
      new Set(
        continuousWorkSegment.segmentDetails.workSegments.flatMap((ws) =>
          ws.assignedResources.map((ar) => (ar.userId ? ar.userId?.value : null)).filter((ar) => ar !== null),
        ),
      ),
    )
      .map((userId) => ({ userId }))
      .sort((a, b) => {
        const aResource = resourcesForRegion.get(a.userId);
        const bResource = resourcesForRegion.get(b.userId);
        const aIsTeamResource = defaultResourcesForTeam.includes(a.userId);
        const bIsTeamResource = defaultResourcesForTeam.includes(b.userId);

        if (aIsTeamResource && !bIsTeamResource) return -1;
        if (!aIsTeamResource && bIsTeamResource) return 1;

        const getFullName = (resource: ResourceForRegion | undefined) => {
          if (!resource) return '';
          return [resource.firstName, resource.lastName].filter(Boolean).join(' ') || resource.email || '';
        };

        return getFullName(aResource).localeCompare(getFullName(bResource));
      });

    return resources;
  }, [resourcesForRegion, defaultResourcesForTeam, continuousWorkSegment.segmentDetails.workSegments]);

  const handleAddResource = async (resource: string) => {
    const { jobId } = segmentDetails;
    const id = installationProject?.id?.value;
    const job = installationProject?.jobs.find((j) => j.id?.value === jobId);
    if (!id || !job) return;
    setIsSavingResource(resource);
    setIsSaving(true);

    const sequenceNumsForContinuousWorkSegment = continuousWorkSegment.segmentDetails.workSegments.map(
      (ws) => ws.sequenceNumber,
    );
    const jobWorkSegments = job.workSegments;
    const newWorkSegments: WorkSegmentForUpdate[] = jobWorkSegments.map((ws) => {
      if (sequenceNumsForContinuousWorkSegment.includes(ws.sequenceNumber)) {
        return {
          startTime: ws.startTime!,
          teamId: ws.teamId,
          duration: ws.duration!,
          assigneeUserIds: [
            ...ws.assignedResources.map((ar) => ({ value: ar.userId?.value })),
            { value: resource },
          ].filter((ar) => ar.value !== null) as { value: string }[],
        };
      } else {
        return {
          duration: ws.duration!,
          assigneeUserIds: ws.assignedResources.map((ar) => ({ value: ar.userId!.value })),
          teamId: ws.teamId,
          startTime: ws.startTime!,
        };
      }
    });

    try {
      for (const ws of newWorkSegments) {
        if (!ws.duration) {
          throw new Error('Duration is required');
        }
      }
      const res = await updateSegmentsAssignees({
        jobId,
        workSegments: newWorkSegments,
      });
      if (res.job) {
        const result = await refetchProjectsUpdatedSince();

        if (result.status === 'success') {
          toast.success('Resource added to job');
        }
      } else {
        toast.error('Failed to add resource to job');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to add resource to job');
    } finally {
      setIsSavingResource(null);
      setIsSaving(false);
    }
  };

  const handleRemoveResource = async (resource: string) => {
    const { jobId } = segmentDetails;
    const id = installationProject?.id?.value;
    const job = installationProject?.jobs.find((j) => j.id?.value === jobId);
    if (!id || !job) return;
    setIsSavingResource(resource);
    setIsSaving(true);

    const sequenceNumsForContinuousWorkSegment = continuousWorkSegment.segmentDetails.workSegments.map(
      (ws) => ws.sequenceNumber,
    );
    const jobWorkSegments = job.workSegments;
    const newWorkSegments: WorkSegmentForUpdate[] = jobWorkSegments.map((ws) => {
      if (sequenceNumsForContinuousWorkSegment.includes(ws.sequenceNumber)) {
        return {
          startTime: ws.startTime!,
          teamId: ws.teamId,
          duration: ws.duration!,
          assigneeUserIds: ws.assignedResources
            .map((ar) => ({ value: ar.userId?.value }))
            .filter((ar) => ar.value !== resource) as { value: string }[],
        };
      } else {
        return {
          duration: ws.duration!,
          assigneeUserIds: ws.assignedResources.map((ar) => ({ value: ar.userId!.value })),
          teamId: ws.teamId,
          startTime: ws.startTime!,
        };
      }
    });
    try {
      for (const ws of newWorkSegments) {
        if (!ws.duration) {
          throw new Error('Duration is required');
        }
      }
      const res = await updateSegmentsAssignees({
        jobId,
        workSegments: newWorkSegments,
      });
      if (res.job) {
        const result = await refetchProjectsUpdatedSince();
        if (result.status === 'success') {
          toast.success('Resource removed from job');
        }
      } else {
        toast.error('Failed to remove resource from job');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to remove resource from job');
    } finally {
      setIsSavingResource(null);
      setIsSaving(false);
    }
  };

  const allResourcesInTeam: UserIdentity[] = (resourcesInJob ?? [])
    .map((resource) => {
      const resourceData = resourcesForRegion.get(resource.userId);
      return resourceData ? ({ ...resourceData, roles: resourceData.roles ?? [] } as UserIdentity) : null;
    })
    .filter((resource): resource is UserIdentity => Boolean(resource));

  const allResourcesInTeamIds = allResourcesInTeam.map((resource) => resource.userId!.value);

  const allResourcesNotInTeam = Array.from(resourcesForRegion.values())
    .filter(
      (resource: ResourceForRegion) =>
        !allResourcesInTeamIds.includes(resource.userId!.value) &&
        resource.roles.some((role) => {
          return requiredRole === role;
        }),
    )
    .toSorted((a, b) => {
      if (defaultResourcesForTeam.includes(a.userId!.value) && !defaultResourcesForTeam.includes(b.userId!.value)) {
        return -1;
      }
      if (!defaultResourcesForTeam.includes(a.userId!.value) && defaultResourcesForTeam.includes(b.userId!.value)) {
        return 1;
      }
      return a.firstName.localeCompare(b.firstName);
    });

  const handleClickOutside = (e: MouseEvent) => {
    if (document.getElementsByClassName('MuiModal-root').length > 0) return;
    if (containerRef.current !== null && !containerRef.current.contains(e.target as Node)) {
      setShowAddResourcesToTeam(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  });

  return (
    <Portal>
      <Stack
        id="add-resources-to-team-modal"
        ref={containerRef}
        position="absolute"
        width="360px"
        height="auto"
        maxHeight="70vh"
        sx={{
          background: 'white',
          boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
          zIndex: 5202,
          borderRadius: '22px',
          top: toolTipPosition.top,
          right: toolTipPosition.right - 360,
        }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center" px={4} pt={4} pb={2}>
          <Typography variant="headline4">
            <FormattedMessage id="installationPlanning.addResourcesToJob.title" defaultMessage="People" />
          </Typography>
          <IconButton onClick={() => setShowAddResourcesToTeam(false)}>
            <CrossOutlinedIcon height={18} width={18} />
          </IconButton>
        </Stack>
        <Stack>
          <Typography variant="body2Emphasis" mb={1} px={4}>
            <FormattedMessage
              id="installationPlanning.addResourcesToJob.currentResources"
              defaultMessage="Current Resources"
            />
          </Typography>
          {allResourcesInTeam.length === 0 ? (
            <Stack py={2} px={4} justifyContent="center" width="100%" height="160px" minHeight="160px">
              <Typography variant="body2" color={grey[600]} mb={2} textAlign="center">
                <FormattedMessage
                  id="installationPlanning.addResourcesToJob.noResources"
                  defaultMessage="No resources assigned to this job"
                />
              </Typography>
            </Stack>
          ) : (
            <Stack
              sx={{
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                minHeight: '165px',
                maxHeight: '165px',
                msOverflowStyle: 'none', // IE and Edge
                scrollbarWidth: 'none', // Firefox
              }}
              spacing={1}
              py={2}
              px={4}
              pb={2}
              mb={2}
            >
              {allResourcesInTeam?.map((resource) => (
                <Stack
                  direction="row"
                  key={resource.userId!.value}
                  justifyContent="space-between"
                  alignItems="center"
                  minHeight="36px"
                >
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      {resource.userImage ? (
                        <Box
                          sx={{
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            backgroundColor: grey[100],
                          }}
                        >
                          <img
                            src={`data:image/jpeg;base64,${Buffer.from(resource.userImage).toString('base64')}`}
                            alt={`${resource.firstName} ${resource.lastName}`}
                            style={{
                              width: '20px',
                              height: '20px',
                              borderRadius: '50%',
                            }}
                          />
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: '20px',
                            height: '20px',
                            backgroundColor: beige[100],
                            borderRadius: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <Typography variant="body2" fontSize={8} sx={{ textTransform: 'uppercase' }}>
                            {resource.firstName[0]} {resource.lastName[0]}
                          </Typography>
                        </Box>
                      )}
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Typography variant="body2" fontSize={12}>
                          {resource.firstName} {resource.lastName}
                          {defaultResourcesForTeam.includes(resource.userId!.value) && ' [Default]'}
                        </Typography>
                      </Stack>
                    </Stack>
                  </Stack>
                  <IconButton
                    onClick={() => {
                      sendGTMEvent({
                        event: 'button_click',
                        click_text: 'remove_resource_from_job',
                        app_name: 'installation_planning',
                      });
                      handleRemoveResource(resource.userId!.value);
                    }}
                    disabled={Boolean(isSavingResource) || isSaving}
                  >
                    {isSavingResource === resource.userId!.value ? (
                      <CircularProgress size={20} />
                    ) : (
                      <CheckIndeterminateOutlinedIcon height={20} width={20} />
                    )}
                  </IconButton>
                </Stack>
              ))}
              <Box
                sx={{
                  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FFF 68.52%)',
                  height: '54px',
                  width: '100%',
                  zIndex: 2000,
                  position: 'absolute',
                  borderRadius: '0 0 22px 22px',
                  top: 240,
                  left: 0,
                  pointerEvents: 'none',
                }}
              />
            </Stack>
          )}

          <Divider sx={{ my: 2, mx: 4 }} />
          <Typography variant="body2Emphasis" mt={3} mb={2} px={4}>
            <FormattedMessage
              id="installationPlanning.addResourcesToJob.availableResources"
              defaultMessage="Available Resources"
            />
          </Typography>
          {allResourcesNotInTeam.length === 0 ? (
            <Stack py={2} px={4} justifyContent="center" width="100%" height="160px" minHeight="160px">
              <Typography variant="body2" color={grey[600]} mb={2} textAlign="center">
                <FormattedMessage
                  id="installationPlanning.addResourcesToJob.noAvailableResources"
                  defaultMessage="No available resources to add"
                />
              </Typography>
            </Stack>
          ) : (
            <Stack
              sx={{
                overflowY: 'auto',
                minHeight: '160px',
                maxHeight: '160px',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                msOverflowStyle: 'none', // IE and Edge
                scrollbarWidth: 'none', // Firefox
              }}
              px={4}
              pt={1}
              pb={2}
              mb={2}
            >
              {allResourcesNotInTeam.map((resource) => (
                <Stack
                  direction="row"
                  key={resource.userId!.value}
                  justifyContent="space-between"
                  alignItems="center"
                  minHeight="36px"
                >
                  <Stack direction="row" alignItems="center" spacing={2}>
                    {resource.userImage ? (
                      <Box
                        sx={{
                          borderRadius: '50%',
                          width: '20px',
                          height: '20px',
                          backgroundColor: grey[100],
                        }}
                      >
                        <img
                          src={`data:image/jpeg;base64,${Buffer.from(resource.userImage).toString('base64')}`}
                          alt={`${resource.firstName} ${resource.lastName}`}
                          style={{
                            width: '20px',
                            height: '20px',
                            borderRadius: '50%',
                          }}
                        />
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: beige[100],
                          borderRadius: '50%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Typography variant="body2" fontSize={8} sx={{ textTransform: 'uppercase' }}>
                          {resource.firstName[0]} {resource.lastName[0]}
                        </Typography>
                      </Box>
                    )}
                    <Typography variant="body2" fontSize={12}>
                      {resource.firstName} {resource.lastName}
                      {defaultResourcesForTeam.includes(resource.userId!.value) && ' [Default]'}
                    </Typography>
                  </Stack>
                  <IconButton
                    onClick={() => {
                      sendGTMEvent({
                        event: 'button_click',
                        click_text: 'add_resource_to_job',
                        app_name: 'installation_planning',
                      });
                      handleAddResource(resource.userId!.value);
                    }}
                    disabled={Boolean(isSavingResource) || isSaving}
                  >
                    {isSavingResource === resource.userId!.value ? (
                      <CircularProgress size={20} />
                    ) : (
                      <AddOutlinedIcon height={20} width={20} />
                    )}
                  </IconButton>
                </Stack>
              ))}
              <Box
                sx={{
                  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FFF 68.52%)',
                  height: '54px',
                  width: '100%',
                  zIndex: 2000,
                  position: 'absolute',
                  borderRadius: '0 0 22px 22px',
                  bottom: 0,
                  left: 0,
                  pointerEvents: 'none',
                }}
              />
            </Stack>
          )}
        </Stack>
      </Stack>
    </Portal>
  );
}
