import { format, getHours } from 'date-fns';
import { tz } from '@date-fns/tz';
import { Team } from '@aira/resource-grpc-api/build/ts_out/com/aira/acquisition/contract/resource/v1/model';
import { ContinuousWorkSegment, Row } from './types/planningTypes';
import { MORNING_SHIFT } from './helpers/workingHours';

export function calculateRow({
  continuousWorkSegments,
  team,
  rowSize,
  dateIndexMap,
  timeZone,
}: {
  continuousWorkSegments: ContinuousWorkSegment[];
  team?: Team;
  rowSize: number;
  dateIndexMap: Map<string, number>;
  timeZone: string;
}) {
  const row = Array.from({ length: rowSize }, () => ({
    availability: 'available' as const,
    numberOfJobs: 0,
  })) as Row;
  let numberOfRows = 1;
  // For each segment, compute its startIndex + endIndex, fill the row
  continuousWorkSegments.forEach((segment) => {
    const { startTime, endTime } = segment.segmentDetails;
    let startDayIndex = dateIndexMap.get(format(startTime, 'yyyy-MM-dd', { in: tz(timeZone) }));
    let endDayIndex = dateIndexMap.get(format(endTime, 'yyyy-MM-dd', { in: tz(timeZone) }));

    if (typeof startDayIndex === 'undefined') {
      for (const workSegment of segment.segmentDetails.workSegments) {
        if (workSegment.startTime) {
          startDayIndex = dateIndexMap.get(format(workSegment.startTime, 'yyyy-MM-dd', { in: tz(timeZone) }));
          if (typeof startDayIndex !== 'undefined') {
            break; // exit for..of early
          }
        }
      }
      if (typeof startDayIndex === 'undefined') {
        return;
      }
    }

    if (typeof endDayIndex === 'undefined') {
      for (const workSegment of segment.segmentDetails.workSegments) {
        if (
          workSegment.endTime &&
          dateIndexMap.get(format(workSegment.endTime, 'yyyy-MM-dd', { in: tz(timeZone) })) !== undefined
        ) {
          endDayIndex = dateIndexMap.get(format(workSegment.endTime, 'yyyy-MM-dd', { in: tz(timeZone) }));
          return; // exit workSegments forEach early
        }
      }
      if (typeof endDayIndex === 'undefined') {
        return;
      }
    }

    // Determine if it starts in the morning block or afternoon block
    let startIdx = startDayIndex * 2;
    if (getHours(startTime, { in: tz(timeZone) }) >= MORNING_SHIFT.endHour) {
      startIdx += 1;
    }

    // Determine if it ends in the morning block or afternoon block
    let endIdx = endDayIndex * 2;
    if (getHours(endTime, { in: tz(timeZone) }) > MORNING_SHIFT.endHour) {
      endIdx += 1;
    }

    // Compute the span of the segment and fill in the row
    const span = endIdx - startIdx + 1;

    // Get current number of jobs at this time slot, default to 0 if undefined
    const currentNumberOfJobs = row[startIdx]?.numberOfJobs ?? 0;

    // If adding this job would exceed current row capacity, increment total rows needed
    if (currentNumberOfJobs + 1 > numberOfRows) {
      numberOfRows += 1;
    }

    // Get current availability status for this time slot
    let availability = row[startIdx]?.availability ?? 'available';

    // Update availability based on number of jobs:
    // - First job marks slot as 'taken'
    // - Second job marks slot as 'overlapping'
    // - Additional jobs keep it as 'overlapping'
    if (currentNumberOfJobs === 0) {
      availability = 'taken';
    } else if (currentNumberOfJobs === 1) {
      availability = 'overlapping';
    }

    // Update the time slot with new job information
    row[startIdx] = {
      numberOfJobs: currentNumberOfJobs + 1,
      jobs: [
        ...(row[startIdx]?.jobs ?? []), // Preserve existing jobs
        {
          continuousWorkSegment: segment,
          span, // How many time slots this job spans
          innerRowNumber: currentNumberOfJobs + 1, // Used for vertical positioning
        },
      ],
      availability,
    };

    // Mark all subsequent time slots in the job's span as 'taken'
    // Note: Only marks 'available' slots as 'taken', preserves 'overlapping' status
    for (let i = 1; i < span; i++) {
      if (row[startIdx + i]!.availability === 'overlapping') {
        row[startIdx + i]!.numberOfJobs = (row[startIdx + i]!.numberOfJobs ?? 0) + 1;
      }
      if (row[startIdx + i]!.availability === 'taken') {
        row[startIdx + i]!.availability = 'overlapping';
        row[startIdx + i]!.numberOfJobs = (row[startIdx + i]!.numberOfJobs ?? 0) + 1;
      }
      if (row[startIdx + i]!.availability === 'available') {
        row[startIdx + i]!.availability = 'taken';
        row[startIdx + i]!.numberOfJobs = (row[startIdx + i]!.numberOfJobs ?? 0) + 1;
      }
    }
  });

  return { row, numberOfRows, ...(team && { team }) };
}
