import { Box, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { useProjectActions, useShowUnsavedChanges, useUnsavedChanges } from '../stores/ProjectStore';

export default function UnsavedChangesToast() {
  const showUnsavedChanges = useShowUnsavedChanges();
  const { setShowUnsavedChanges, setShowFiltersTab } = useProjectActions();
  const unsavedChanges = useUnsavedChanges();

  if (unsavedChanges.length === 0 || showUnsavedChanges) {
    return null;
  }

  const handleShowUnsavedChanges = () => {
    setShowUnsavedChanges(true);
    setShowFiltersTab(false);
  };

  return (
    <Stack
      direction="row"
      sx={{
        position: 'fixed',
        bottom: '20px',
        left: 'calc(50% - 200px)',
        display: 'flex',
        justifyContent: 'space-between',
        background: '#FAF9F9',
        borderRadius: '8px',
        padding: '16px',
        alignItems: 'center',
        marginBottom: '16px',
        boxShadow: '0px 36px 25px 0px rgba(0, 0, 0, 0.25)',
        zIndex: 1200,
      }}
    >
      <Box
        sx={{
          background: '#000',
          height: '8px',
          width: '8px',
          borderRadius: '50%',
          marginRight: '8px',
        }}
      />
      <Typography variant="body1" mr={4}>
        There are {unsavedChanges.length} unsaved changes.
      </Typography>

      <Button onClick={handleShowUnsavedChanges} variant="outlined" color="primary" size="small">
        See changes
      </Button>
    </Stack>
  );
}
