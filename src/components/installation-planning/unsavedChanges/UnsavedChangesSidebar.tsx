import {
  <PERSON>,
  <PERSON>ir<PERSON><PERSON><PERSON>ress,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>header,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
} from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { ClockDotsOutlinedIcon } from '@ui/components/StandardIcons/ClockDotsOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { InformationCircleOutlinedIcon } from '@ui/components/StandardIcons/InformationCircleOutlinedIcon';
import { Select } from '@ui/components/Select/Select';
import { useState } from 'react';
import { addWeeks, differenceInSeconds, isBefore, isEqual } from 'date-fns';
import { api } from 'utils/api';
import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';
import { FormattedMessage, useIntl } from 'react-intl';
import { MessageKey } from 'messageType';
import toast from 'react-hot-toast';
import { InstallationProjectActivity_JobRescheduled_ReasonCategory } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { grey } from '@ui/theme/colors';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { TZDate } from '@date-fns/tz';
import { formatDate } from '../helpers/dateHelpers';
import { COUNTRIES, reverseResourceMapping, UnsavedChange } from '../types/planningTypes';
import { useRegionContext } from '../../../context/RegionContext';
import { useProjectActions, useShowUnsavedChanges, useUnsavedChanges } from '../stores/ProjectStore';
import { useAvailability } from '../contexts/AvailabilityContext';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';

const REASON_CATEGORIES = new Map<string, string[]>([
  [
    'Third Party',
    [
      'REASON_CATEGORY_THIRD_PARTY_DISTRIBUTION_NETWORK_OPERATOR',
      'REASON_CATEGORY_THIRD_PARTY_CUSTOMER_TRADES',
      'REASON_CATEGORY_THIRD_PARTY_SUBCONTRACTOR',
    ],
  ],
  [
    'Customer',
    [
      'REASON_CATEGORY_CUSTOMER_FINANCE_NOT_COMPLETED',
      'REASON_CATEGORY_CUSTOMER_ILLNESS',
      'REASON_CATEGORY_CUSTOMER_SCOPE_CHANGED',
      'REASON_CATEGORY_CUSTOMER_RESCHEDULED',
    ],
  ],
  [
    'Permissions',
    [
      'REASON_CATEGORY_PERMISSIONS_PLANNING_APPLICATION',
      'REASON_CATEGORY_PERMISSIONS_DISTRIBUTION_NETWORK_OPERATOR',
      'REASON_CATEGORY_PERMISSIONS_SUBSIDY',
    ],
  ],
  [
    'Product Supply',
    ['REASON_CATEGORY_PRODUCT_SUPPLY_MATERIAL_UNAVAILABLE', 'REASON_CATEGORY_PRODUCT_SUPPLY_RUSH_ORDER'],
  ],
  ['Heat Design', ['REASON_CATEGORY_HEAT_DESIGN_NOT_COMPLETED']],
  ['Technical Survey', ['REASON_CATEGORY_TECHNICAL_SURVEY_NOT_COMPLETED']],
  ['Installment Capacity', ['REASON_CATEGORY_INSTALMENT_CAPACITY']],
  ['Uncategorized', ['REASON_CATEGORY_UNCATEGORIZED']],
]);

export default function UnsavedChangesSidebar() {
  const { formatMessage } = useIntl();
  const { teams } = useAvailability();
  const { refetchProjectsUpdatedSince, initialLoadTime } = useProjectsUpdatedSince();
  const unsavedChanges = useUnsavedChanges();
  const showUnsavedChanges = useShowUnsavedChanges();
  const [changeBeingSaved, setChangeBeingSaved] = useState<UnsavedChange | null>(null);
  const { setHoveredUnsavedChangeJobId, setUnsavedChanges, setShowUnsavedChanges } = useProjectActions();
  const region = useRegionContext();
  const { timeZone, id: regionId } = region;
  const { mutate: updateWorkSegments } = api.InstallationProject.updateInstallationProjectJobWorkSegments.useMutation();
  const utils = api.useUtils();
  const twoWeeksFromNow = addWeeks(new TZDate(new Date(), timeZone), 2);

  const handleSaveChange = (unsavedChangeToSave: UnsavedChange) => {
    setChangeBeingSaved(unsavedChangeToSave);
    updateWorkSegments(
      {
        jobId: unsavedChangeToSave.jobId,
        workSegments: unsavedChangeToSave.workSegments
          .filter(({ startTime, endTime }) => startTime && endTime)
          .map(({ startTime, endTime, teamId }) => ({
            startTime: startTime!,
            duration: {
              seconds: differenceInSeconds(endTime!, startTime!),
              nanos: 0,
            } satisfies Duration,
            teamId: teamId?.value,
          })),
        reason: unsavedChangeToSave.reason?.category
          ? {
              category: unsavedChangeToSave.reason.category,
              description:
                unsavedChangeToSave.reason.description && unsavedChangeToSave.reason.description.length > 0
                  ? unsavedChangeToSave.reason.description
                  : undefined,
            }
          : undefined,
      },
      {
        onSuccess: async () => {
          // Optimistically update the query cache for installation projects
          utils.InstallationProject.getInstallationProjects.setData(
            { operationalUnitId: regionId!.value, updatedAfter: initialLoadTime },
            (oldData) => {
              if (!oldData) return [];
              return oldData.map((project) => {
                if (unsavedChangeToSave?.projectId !== project.installationProject?.id?.value)
                  return project as FullInstallationProjectEntity;
                return {
                  ...(project as FullInstallationProjectEntity),
                  installationProject: {
                    ...(project.installationProject as InstallationProject),
                    jobs:
                      (project.installationProject as InstallationProject).jobs?.map((job) => {
                        if (job.id?.value !== unsavedChangeToSave.jobId) return job;
                        return {
                          ...job,
                          workSegments: unsavedChangeToSave.workSegments,
                        };
                      }) ?? [],
                  },
                };
              });
            },
          );

          refetchProjectsUpdatedSince();

          setTimeout(() => {
            const newUnsavedChanges = unsavedChanges.filter((change) => change.jobId !== unsavedChangeToSave.jobId);
            setUnsavedChanges(newUnsavedChanges);
            setChangeBeingSaved(null);
            if (newUnsavedChanges.length === 0) {
              setShowUnsavedChanges(false);
            }
          }, 1000);

          toast(
            <FormattedMessage
              id="installationPlanning.notify.updateWorkSegmentsSuccess"
              defaultMessage="Successfully updated the job times."
            />,
          );
        },
        onError: () => {
          toast.error(
            <FormattedMessage
              id="installationPlanning.notify.updateWorkSegmentsError"
              defaultMessage="Failed to update the job times."
            />,
            { position: 'bottom-center' },
          );
          setChangeBeingSaved(null);
        },
      },
    );
    // console.log('Saving changes');

    // socket.emit('sendMessage', JSON.stringify(unsavedChanges), regionId);
    // refetch jobs after update if no errors
  };

  if (!showUnsavedChanges) {
    return null;
  }

  const reasonCategoriesFilteredForCountry = Array.from(REASON_CATEGORIES).filter(([category]) => {
    // Hide permissions reason categories for Italy
    if (
      region.iso3166?.$case === 'country' &&
      region.iso3166.country === COUNTRIES.find((c) => c.code === 'IT')?.grpcCode &&
      category === 'Permissions'
    ) {
      return false;
    }
    return true;
  });

  return (
    <Stack
      direction="column"
      spacing={4}
      sx={{
        position: 'absolute',
        top: 80,
        right: 20,
        width: '400px',
        px: 3,
        py: 3,
        pb: 5,
        background: grey[100],
        borderRadius: '22px',
        boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        zIndex: 3001,
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="center" px={2}>
        <Typography variant="body2Emphasis" fontSize="19px">{`Unsaved Changes (${unsavedChanges.length})`}</Typography>
        <IconButton
          onClick={() => {
            setShowUnsavedChanges(false);
          }}
        >
          <CrossOutlinedIcon height={24} width={24} />
        </IconButton>
      </Stack>
      <Stack
        gap={1}
        spacing={2}
        sx={{
          maxHeight: 'calc(100dvh - 84px)',
          position: 'relative',
          px: 2,
          width: '100%',
          overflowY: 'auto',
        }}
      >
        {unsavedChanges.length === 0 && (
          <Box sx={{ alignSelf: 'center' }} mt={6}>
            <Stack
              direction="row"
              alignItems="center"
              spacing={1}
              gap={1}
              sx={{
                border: '1px dashed #D3D8D9',
                pr: 2,
                borderRadius: '50px',
              }}
            >
              <InformationCircleOutlinedIcon width={20} height={20} />
              <Typography variant="body2" sx={{ textAlign: 'center' }}>
                No unsaved changes
              </Typography>
            </Stack>
          </Box>
        )}
        {unsavedChanges?.map((unsavedChange) => (
          <Stack
            key={unsavedChange.jobId}
            direction="column"
            alignItems="stretch"
            justifyContent="space-around"
            gap={3}
            sx={{
              padding: '32px 16px',
              borderRadius: '16px',
              background: '#22222608',
              '&:hover': {
                backgroundColor: '#2222260F',
              },
            }}
          >
            <Stack
              onMouseEnter={() => setHoveredUnsavedChangeJobId(unsavedChange.jobId)}
              onMouseLeave={() => setHoveredUnsavedChangeJobId(null)}
            >
              <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                <Typography variant="body2Emphasis">Customer:</Typography>
                <Typography variant="body2" textAlign="end">
                  {unsavedChange.customerName}
                </Typography>
              </Stack>
              <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                <Typography variant="body2Emphasis">Changes made by:</Typography>
                <Typography variant="body2">{unsavedChange.userWhoMadeChange}</Typography>
              </Stack>
              <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                <Typography variant="body2Emphasis">Resource type:</Typography>
                <Typography
                  variant="body2"
                  sx={{
                    textTransform: 'capitalize',
                  }}
                >
                  {reverseResourceMapping[
                    unsavedChange.requiredRole as keyof typeof reverseResourceMapping
                  ].toLowerCase()}
                </Typography>
              </Stack>
              {unsavedChange.newTeamId && (
                <>
                  <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                    <Typography variant="body2Emphasis">Previous team:</Typography>
                    <Typography variant="body2">
                      {teams.find((team) => unsavedChange.previousTeamId === team.id!.value)?.name ?? 'Unassigned'}
                    </Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                    <Typography variant="body2Emphasis">New team:</Typography>
                    <Typography variant="body2">
                      {teams.find((team) => unsavedChange.newTeamId === team.id!.value)?.name}
                    </Typography>
                  </Stack>
                </>
              )}
              {unsavedChange.previousStartTime &&
                !isEqual(unsavedChange.previousStartTime, unsavedChange.newStartTime) && (
                  <>
                    <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                      <Typography variant="body2Emphasis">Previous start time:</Typography>
                      <Typography variant="body2">{formatDate(unsavedChange.previousStartTime, timeZone)}</Typography>
                    </Stack>
                    <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                      <Typography variant="body2Emphasis">New start time:</Typography>
                      <Typography variant="body2">{formatDate(unsavedChange.newStartTime, timeZone)}</Typography>
                    </Stack>
                  </>
                )}

              {!unsavedChange.previousStartTime && unsavedChange.newStartTime && (
                <>
                  <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                    <Typography variant="body2Emphasis">Previous start time:</Typography>
                    <Typography variant="body2">Unscheduled</Typography>
                  </Stack>
                  <Stack direction="row" spacing={1} width="100%" justifyContent="flex-start">
                    <Typography variant="body2Emphasis">New start time:</Typography>
                    <Typography variant="body2">{formatDate(unsavedChange.newStartTime, timeZone)}</Typography>
                  </Stack>
                </>
              )}

              {unsavedChange.isNewSeparateHours && (
                <Stack direction="row" spacing={1} pt={3} width="100%" justifyContent="flex-start" alignItems="center">
                  <ClockDotsOutlinedIcon height={16} width={16} />
                  <Typography variant="body2">A new separate block of hours has been added</Typography>
                </Stack>
              )}
            </Stack>

            {!unsavedChange.isNewSeparateHours &&
              unsavedChange.previousStartTime &&
              isBefore(unsavedChange.previousStartTime, twoWeeksFromNow) &&
              !isEqual(unsavedChange.previousStartTime, unsavedChange.newStartTime) && (
                <Stack spacing={1}>
                  <Select
                    label=""
                    displayEmpty
                    name="reason"
                    size="small"
                    value={unsavedChange.reason?.category?.toString() ?? ''}
                    shouldLiveOnTop
                    renderValue={(selected) => {
                      if (selected.length === 0) {
                        return <Typography sx={{ color: grey[400] }}>Select a Reason</Typography>;
                      }
                      if (selected !== undefined) {
                        return (
                          <Typography sx={{ textTransform: 'capitalize' }}>
                            {formatMessage({
                              id: `installationPlanning.jobRescheduledReason.${InstallationProjectActivity_JobRescheduled_ReasonCategory[selected as unknown as InstallationProjectActivity_JobRescheduled_ReasonCategory]}` as MessageKey,
                            })}
                          </Typography>
                        );
                      }
                      return '';
                    }}
                    onChange={(e) => {
                      setUnsavedChanges(
                        unsavedChanges.map((change) =>
                          change.jobId === unsavedChange.jobId
                            ? {
                                ...change,
                                reason: {
                                  category: e.target
                                    .value as unknown as InstallationProjectActivity_JobRescheduled_ReasonCategory,
                                  description: '',
                                },
                              }
                            : change,
                        ),
                      );
                    }}
                  >
                    <MenuItem value="" sx={{ borderBottom: '1px solid #D3D8D9' }}>
                      <Typography>Select a Reason</Typography>
                    </MenuItem>

                    {reasonCategoriesFilteredForCountry
                      .map(([groupName, categories]) => [
                        <ListSubheader key={`header-${groupName}`} sx={{ textTransform: 'capitalize' }}>
                          {formatMessage({
                            id: `installationPlanning.jobRescheduledReasonGroup.${groupName}` as MessageKey,
                          })}
                        </ListSubheader>,
                        ...categories.map((reasonName) => {
                          const reason =
                            InstallationProjectActivity_JobRescheduled_ReasonCategory[
                              reasonName as keyof typeof InstallationProjectActivity_JobRescheduled_ReasonCategory
                            ];
                          return (
                            <MenuItem key={reason} value={reason} sx={{ textTransform: 'capitalize' }}>
                              {formatMessage({
                                id: `installationPlanning.jobRescheduledReason.${reasonName}` as MessageKey,
                              })}
                            </MenuItem>
                          );
                        }),
                      ])
                      .flat()}
                  </Select>
                  {unsavedChange?.reason?.category ===
                    InstallationProjectActivity_JobRescheduled_ReasonCategory.REASON_CATEGORY_UNCATEGORIZED && (
                    <TextField
                      label=""
                      fullWidth
                      multiline
                      rows={2}
                      value={unsavedChange.reason?.description}
                      placeholder="Enter a description"
                      onChange={(e) => {
                        setUnsavedChanges(
                          unsavedChanges.map((change) =>
                            change.jobId === unsavedChange.jobId
                              ? {
                                  ...change,
                                  reason: {
                                    ...change.reason,
                                    description: e.target.value,
                                  },
                                }
                              : change,
                          ),
                        );
                      }}
                    />
                  )}
                </Stack>
              )}

            <Stack direction="row" spacing={1} width="100%">
              <Button
                variant="outlined"
                size="small"
                fullWidth
                onClick={() => {
                  const newUnsavedChanges = unsavedChanges.filter((change) => change.jobId !== unsavedChange.jobId);
                  setUnsavedChanges(newUnsavedChanges);
                  if (newUnsavedChanges.length === 0) {
                    setShowUnsavedChanges(false);
                  }
                }}
              >
                Undo
              </Button>
              <Button
                variant="contained"
                size="small"
                fullWidth
                onClick={() => {
                  handleSaveChange(unsavedChange);
                }}
                disabled={
                  changeBeingSaved !== null ||
                  (unsavedChange.previousStartTime &&
                    isBefore(unsavedChange.previousStartTime, twoWeeksFromNow) &&
                    !isEqual(unsavedChange.previousStartTime, unsavedChange.newStartTime) &&
                    (!unsavedChange.reason?.category ||
                      (unsavedChange.reason.category ===
                        InstallationProjectActivity_JobRescheduled_ReasonCategory.REASON_CATEGORY_UNCATEGORIZED &&
                        !unsavedChange.reason?.description)))
                }
              >
                {changeBeingSaved?.jobId === unsavedChange.jobId ? (
                  <>
                    <CircularProgress size="20px" color="secondary" sx={{ mr: 2 }} /> <span>Saving</span>
                  </>
                ) : (
                  'Save'
                )}
              </Button>
            </Stack>
          </Stack>
        ))}
      </Stack>
    </Stack>
  );
}
