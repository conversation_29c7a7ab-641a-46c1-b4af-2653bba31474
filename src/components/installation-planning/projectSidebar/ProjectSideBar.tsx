import {
  Box,
  Fade,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { ClockDotsOutlinedIcon } from '@ui/components/StandardIcons/ClockDotsOutlinedIcon';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { InformationCircleOutlinedIcon } from '@ui/components/StandardIcons/InformationCircleOutlinedIcon';
import { beige, grey } from '@ui/theme/colors';
import { FormattedMessage, useIntl } from 'react-intl';
import Link from 'next/link';
import { installationPackageSizeToJSON } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.energy.solution.v3';
import { InstallationProjectTicket } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useProjectActions, useSelectedProject, useUnsavedChanges } from '../stores/ProjectStore';
import { resourceMapping, UnsavedChange, useResourceTypesForCountry } from '../types/planningTypes';
import { useCountryCodeContext } from '../../../context/CountryCodeContext';
import HubspotButton from './HubspotButton';
import JobResources from '../jobTooltip/JobResources';

export default function ProjectSidebar() {
  const intl = useIntl();
  const countryCode = useCountryCodeContext();
  const unsavedChanges = useUnsavedChanges();
  const selectedProject = useSelectedProject();
  const { setUnsavedChanges, setProjectForNoBaselineHoursModal } = useProjectActions();
  const selectedJobUnsavedChanges = unsavedChanges.filter(
    (change: any) => change.projectId === selectedProject?.installationProject?.id?.value,
  );
  const rolesForCountry = useResourceTypesForCountry(countryCode).map((resourceType) => resourceMapping[resourceType]);

  if (!selectedProject) {
    return (
      <Stack
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'center',
          background: beige[100],
          height: '100%',
          flex: 1,
          width: '100%',
          padding: '24px',
          borderTop: `1px solid ${grey[300]}`,
        }}
      >
        <Box sx={{ alignSelf: 'center' }}>
          <Stack
            direction="row"
            alignItems="center"
            spacing={1}
            gap={1}
            sx={{
              border: '1px dashed #D3D8D9',
              p: 2,
              borderRadius: '50px',
            }}
          >
            <InformationCircleOutlinedIcon width={20} height={20} />
            <Typography variant="body1" sx={{ textAlign: 'center' }}>
              Select a job for information
            </Typography>
          </Stack>
        </Box>
      </Stack>
    );
  }

  const { contact, address } = selectedProject;
  const customerName = `${contact?.firstName} ${contact?.lastName}`;
  const installationAddress = address?.specificDetails?.formattedAddress;
  const customerPhone = contact?.phoneNumber;
  const customerEmail = contact?.email;

  const radiators = selectedProject.energySolution?.products.filter(
    (product) =>
      product.productDetails?.details?.$case === 'addon' &&
      product.productDetails?.details?.addon?.category === 'RADIATOR' &&
      product.sku !== '6-radiator-valve-change' &&
      product.sku !== '7-radiator-conversion',
    // This is because these two skus were included in the RADIATOR category, but they are not new radiators. They will be soon moved to a different category by the expansion team and this line can be removed
  );
  const radiatorsQuantity = radiators?.reduce((acc, radiator) => acc + radiator.quantity, 0);

  const heatPumpOutdoorUnit = selectedProject.energySolution?.products.find(
    (product) => product.productDetails?.details?.$case === 'heatPumpOutdoorUnit',
  )?.displayName;

  const bufferTank = selectedProject.energySolution?.products.find(
    (product) =>
      product.productDetails?.details?.$case === 'installationOption' &&
      product.productDetails?.details?.installationOption?.category === 'BUFFER_TANK',
  )?.displayName;

  const heatPumpIndoorUnit = selectedProject.energySolution?.products.find(
    (product) => product.productDetails?.details?.$case === 'heatPumpIndoorUnit',
  )?.displayName;

  const installationPackageSize = selectedProject.energySolution?.products.find(
    (product) => product.productDetails?.details?.$case === 'installationPackage',
  );

  const otherProducts = selectedProject.energySolution?.products.filter(
    (product) =>
      product.productDetails?.details?.$case !== 'heatPumpOutdoorUnit' &&
      product.productDetails?.details?.$case !== 'heatPumpIndoorUnit' &&
      !(
        product.productDetails?.details?.$case === 'addon' &&
        product.productDetails?.details?.addon?.category === 'RADIATOR'
      ) &&
      !(
        product.productDetails?.details?.$case === 'installationOption' &&
        product.productDetails?.details?.installationOption?.category === 'BUFFER_TANK'
      ) &&
      product.productDetails?.details?.$case !== 'installationPackage',
  );
  const rolesWithsJobs = (selectedProject.installationProject?.jobs ?? []).map((job) => job?.requiredRole);
  const projectMissingJobsForRoles = rolesForCountry.some((role) => !rolesWithsJobs.includes(role));
  const handleUndoJobChange = (jobId?: string) => {
    if (!jobId) {
      return;
    }
    setUnsavedChanges(unsavedChanges.filter((unsavedChange) => unsavedChange.jobId !== jobId));
    // socket.emit('sendMessage', JSON.stringify(newUnsavedChanges), regionId);
  };

  const openTickets = selectedProject.installationProject?.progressOverview?.currentBlockers.reduce<
    InstallationProjectTicket[]
  >((acc, blocker) => {
    if (blocker.blocker?.$case === 'openTicket') {
      acc.push(blocker.blocker.openTicket);
    }
    return acc;
  }, []);

  return (
    <Box
      id="project-popup"
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        background: beige[100],
        width: '100%',
        padding: '0 0 24px 0',
      }}
    >
      <Tooltip
        TransitionComponent={Fade}
        slotProps={{
          popper: {
            sx: {
              zIndex: 9999,
            },
          },
        }}
        arrow={false}
        componentsProps={{
          tooltip: {
            sx: {
              bgcolor: grey[100],
              color: grey[800],
              boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
              padding: 3,
            },
          },
        }}
        title={
          <Stack spacing={2}>
            <Typography variant="body2">{customerName}</Typography>
            <Stack>
              <Typography variant="body2Emphasis">
                {intl.formatMessage({ id: 'common.label.address', defaultMessage: 'Address' })}
              </Typography>
              <Typography variant="body2">{installationAddress}</Typography>
            </Stack>
            <Stack>
              <Typography variant="body2Emphasis">
                {intl.formatMessage({ id: 'common.label.emailAddress', defaultMessage: 'Email address' })}
              </Typography>
              <Typography variant="body1">{customerEmail}</Typography>
            </Stack>
            <Stack>
              <Typography variant="body2Emphasis">
                {intl.formatMessage({ id: 'common.label.phoneNumber', defaultMessage: 'Phone number' })}
              </Typography>
              <Typography variant="body1">{customerPhone}</Typography>
            </Stack>
          </Stack>
        }
        placement="left-start"
        sx={{ textTransform: 'Capitalize', left: '-10px' }}
      >
        <Box sx={{ padding: 2, width: '100%' }}>
          <Stack spacing={1} sx={{ backgroundColor: '#22222608', padding: 2, borderRadius: '8px' }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <HousePersonOutsideIcon height={16} width={16} /> <Typography variant="body2">{customerName}</Typography>
            </Stack>
            <Typography variant="body2">{installationAddress}</Typography>
          </Stack>
        </Box>
      </Tooltip>

      <Stack spacing={4} width="100%" px={2} pt={2}>
        <Stack spacing={1} width="100%">
          <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={1}>
            <ClockDotsOutlinedIcon width={16} height={16} />
            <Typography variant="body1">
              <FormattedMessage
                id="installationPlanning.projectSidebar.openTicketsInHubspot"
                defaultMessage="Open tickets in hubspot"
              />
            </Typography>
          </Stack>

          {(openTickets ?? []).length > 0 &&
            openTickets?.map((ticket) => (
              <Tooltip
                key={ticket.name}
                TransitionComponent={Fade}
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: grey[100],
                      color: grey[800],
                      boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
                      padding: 3,
                    },
                  },
                }}
                slotProps={{
                  popper: {
                    sx: {
                      zIndex: 9999,
                    },
                  },
                }}
                title={
                  <Stack spacing={1}>
                    <Typography variant="body2Emphasis">{ticket.name}</Typography>
                    <Typography variant="body2Emphasis">Due at:</Typography>
                    <Typography variant="body1">{ticket.dueAt?.toDateString()}</Typography>
                    <Typography variant="body2Emphasis">Recent update:</Typography>
                    <Typography variant="body1">{ticket.recentUpdate}</Typography>
                    <Typography variant="body2Emphasis">Stage:</Typography>
                    <Typography variant="body1">{ticket.stage}</Typography>
                    <Typography variant="body2Emphasis">Description:</Typography>
                    <Typography variant="body1">{ticket.description}</Typography>
                  </Stack>
                }
                placement="left"
                sx={{ textTransform: 'Capitalize' }}
              >
                <Box sx={{ backgroundColor: '#22222608', padding: 2, borderRadius: '8px' }}>
                  <Typography variant="body1Emphasis" fontSize={14} component="div" lineHeight="21px">
                    {ticket.name.split('for [Installation]')[0]}
                  </Typography>
                </Box>
              </Tooltip>
            ))}
        </Stack>
        <Box sx={{ width: '100%' }}>
          <TableContainer component={Box}>
            <Table aria-label="project details table">
              <TableBody>
                <TableRow aria-label="Radiators row">
                  <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                    <Typography variant="body2Emphasis">
                      <FormattedMessage id="installationPlanning.projectSidebar.radiators" defaultMessage="Radiators" />
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                    <Typography variant="body1">
                      {(radiatorsQuantity ?? 0) > 0 ? `${radiatorsQuantity} radiators` : 'No radiators to be installed'}
                    </Typography>
                  </TableCell>
                </TableRow>
                {heatPumpOutdoorUnit && (
                  <TableRow aria-label="Heat Pump Outdoor Unit row">
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body2Emphasis">
                        <FormattedMessage
                          id="installationPlanning.projectSidebar.heatPumpOutdoorUnit"
                          defaultMessage="Outdoor unit"
                        />
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body1">{heatPumpOutdoorUnit}</Typography>
                    </TableCell>
                  </TableRow>
                )}
                {bufferTank && (
                  <TableRow aria-label="Buffer Tank row">
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body2Emphasis">
                        <FormattedMessage
                          id="installationPlanning.projectSidebar.bufferTank"
                          defaultMessage="Buffer tank"
                        />
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body1">{bufferTank}</Typography>
                    </TableCell>
                  </TableRow>
                )}
                {heatPumpIndoorUnit && (
                  <TableRow aria-label="Heat Pump Indoor Unit row">
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body2Emphasis">
                        <FormattedMessage
                          id="installationPlanning.projectSidebar.heatPumpIndoorUnit"
                          defaultMessage="Indoor unit"
                        />
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body1">{heatPumpIndoorUnit}</Typography>
                    </TableCell>
                  </TableRow>
                )}

                {installationPackageSize && (
                  <TableRow aria-label="Installation Package Size row">
                    <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                      <Typography variant="body2Emphasis">
                        <FormattedMessage
                          id="installationPlanning.projectSidebar.installationPackageSize"
                          defaultMessage="Installation package size"
                        />
                      </Typography>
                    </TableCell>
                    <TableCell
                      sx={{
                        padding: '8px',
                        border: `1px solid ${grey[300]}`,
                        borderWidth: '1px 0px',
                        textTransform: 'capitalize',
                      }}
                    >
                      <Typography variant="body1">
                        {installationPackageSize.productDetails?.details?.$case === 'installationPackage'
                          ? installationPackageSizeToJSON(
                              installationPackageSize.productDetails.details.installationPackage.size,
                            )
                              .split('_')[3]
                              ?.toLowerCase()
                          : ''}
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
                {otherProducts?.map((product) => {
                  const productCategoryCase = product.productDetails?.details?.$case;
                  const productCategoryObject =
                    productCategoryCase && product.productDetails?.details
                      ? (product.productDetails.details as any)[productCategoryCase]
                      : {};
                  const productCategoryKey = Object.keys(productCategoryObject)[0]!;

                  let productCategory = productCategoryObject[productCategoryKey];
                  if (!productCategory) {
                    return null;
                  }
                  if (typeof productCategory === 'string') {
                    productCategory = productCategory.split('_').join(' ').toLowerCase();
                  }
                  if (typeof productCategory === 'number') {
                    productCategory = productCategory.toString();
                  }

                  return (
                    <TableRow key={product.productId?.value} aria-label="Other products row">
                      <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                        <Typography variant="body2Emphasis" sx={{ textTransform: 'capitalize' }}>
                          {productCategory}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ padding: '8px', border: `1px solid ${grey[300]}`, borderWidth: '1px 0px' }}>
                        <Typography variant="body1">{product.displayName}</Typography>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>

        <JobResources assignedResources={selectedProject.installationProject?.assignedResources || []} asColumn />

        {projectMissingJobsForRoles && (
          <Button
            variant="contained"
            size="small"
            onClick={() => {
              setProjectForNoBaselineHoursModal({
                ...selectedProject,
              });
            }}
          >
            Add job hours to project
          </Button>
        )}

        {selectedJobUnsavedChanges.length > 0 && (
          <>
            <hr style={{ width: '100%', border: '1px solid #E0E0E0' }} />
            {selectedJobUnsavedChanges.map((unsavedChange: UnsavedChange) => {
              const jobWithChanges = selectedProject.installationProject?.jobs.find(
                (job) => job?.id?.value === unsavedChange.jobId,
              );
              let resourceType = null;
              for (const resource in resourceMapping) {
                if (resourceMapping[resource as keyof typeof resourceMapping] === jobWithChanges?.requiredRole) {
                  resourceType = resource;
                }
              }

              return (
                <Stack key={unsavedChange.jobId} gap={2}>
                  <Typography variant="body1">{`${resourceType} job has unsaved changes`}</Typography>

                  <Button
                    variant="contained"
                    size="small"
                    onClick={() => handleUndoJobChange(jobWithChanges?.id?.value)}
                  >
                    {`Reset project ${resourceType?.toLowerCase()} jobs`}
                  </Button>
                </Stack>
              );
            })}
          </>
        )}

        <Box sx={{ width: '100%', mt: 2 }}>
          <Link href={`/solution/${selectedProject.energySolution?.id?.value}`} target="_blank">
            <Button variant="outlined" size="small" fullWidth>
              <FormattedMessage
                id="installationPlanning.viewProjectInAerospace"
                defaultMessage="View project in Aerospace"
              />
            </Button>
          </Link>
        </Box>
        {selectedProject.installationProject?.id?.value && (
          <HubspotButton installationProjectId={selectedProject.installationProject?.id?.value} />
        )}
      </Stack>
    </Box>
  );
}
