import { I<PERSON><PERSON><PERSON><PERSON>, Stack } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { useProjectActions, useShowRightSidebar } from '../stores/ProjectStore';
import ProjectSidebar from './ProjectSideBar';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';

const PlanningMap = dynamic(() => import('./PlanningMap'), {
  ssr: false,
});
export default function RightSidebar() {
  const { setShowRightSidebar, setSelectedProject } = useProjectActions();
  const showRightSidebar = useShowRightSidebar();

  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    // Set to true when the component is mounted on the client
    setIsClient(true);
  }, []);

  if (!showRightSidebar) {
    return null;
  }

  return (
    <Stack
      alignItems="center"
      justifyContent="flex-start"
      sx={{
        position: 'absolute',
        top: 80,
        right: 20,
        px: 2,
        width: '400px',
        height: '90dvh',
        maxHeight: '90dvh',
        overflowY: 'auto',
        borderLeft: `1px solid ${grey[200]}`,
        background: beige[100],
        zIndex: 3000,
        borderRadius: '22px',
        boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
      }}
    >
      <Stack
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        py={3}
        width="100%"
        spacing={2}
        sx={{ position: 'sticky', top: 0, zIndex: 3001, background: beige[100] }}
      >
        <IconButton
          onClick={() => {
            setSelectedProject();
            setShowRightSidebar(false);
          }}
        >
          <CrossOutlinedIcon height={24} width={24} />
        </IconButton>
      </Stack>
      <>
        {isClient ? <PlanningMap /> : <div />}
        <ProjectSidebar />
      </>
    </Stack>
  );
}
