import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-defaulticon-compatibility';
import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css';
import { Icon } from 'leaflet';
import { beige, brandYellow, green, grey } from '@ui/theme/colors';
import { useMemo, useState } from 'react';
import { Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { CarOutlinedIcon } from '@ui/components/StandardIcons/CarOutlinedIcon';
import { Checkbox } from '@ui/components/Checkbox/Checkbox';
import { PinOutlinedIcon } from '@ui/components/StandardIcons/PinOutlinedIcon';
import { TextField } from '@ui/components/TextField/TextField';
import {
  useHighlightProjectsWithinDistance,
  useHoveredProject,
  useProjectActions,
  useSelectedProject,
} from '../stores/ProjectStore';
import getDistanceB<PERSON>weenTwoLatLong from '../helpers/getDistanceBetweenTwoLatLong';
import { useRegionContext } from '../../../context/RegionContext';
import getRegionAddress from '../helpers/getRegionAddress';
import { useAllProjects } from '../contexts/AllProjects';
import { FitBounds } from 'components/maps/mapHelpers';
import useDrivingRoute from 'components/maps/installationsMap/useDrivingRoute';

export type DrivingDistanceAndTime = {
  startCoords: [number, number];
  endCoords: [number, number];
  drivingTime: string;
  drivingDistance: string;
};

function PlanningMap() {
  const { projects } = useAllProjects();
  const selectedProject = useSelectedProject();
  const hoveredProject = useHoveredProject();
  const hightlightProjectsWithinDistance = useHighlightProjectsWithinDistance();
  const [distanceInput, setDistanceInput] = useState<number | null>(10);
  const { setSelectedProject, setHighlightProjectsWithinDistance } = useProjectActions();
  const region = useRegionContext();
  const { drivingDistance, drivingTime, route } = useDrivingRoute({
    selectedProject,
    hoveredProject,
    useHub: true,
  });

  const hubAddress = getRegionAddress(region.id!.value);
  const hubGeometry = hubAddress?.geometry;

  const sizeMarkerIcon = 30;

  const createSvg = (backgroundColor: string, outlineColor: string) => {
    const svg = `<?xml version="1.0" encoding="iso-8859-1"?>
    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="none" viewBox="0 0 24 24">
      <path
        fill="${backgroundColor}"
        fillRule="evenodd"
        d="M12 4.5A5.5 5.5 0 0 0 6.5 10c0 2.445 1.451 4.845 3.129 6.762A22.5 22.5 0 0 0 12 19.086l.118-.1a22.4 22.4 0 0 0 2.253-2.224C16.05 14.845 17.5 12.445 17.5 10A5.5 5.5 0 0 0 12 4.5M12 3a7 7 0 0 0-7 7c0 6 7 11 7 11s7-5 7-11a7 7 0 0 0-7-7"
        clipRule="evenodd"
      />
      <path
        fill="${outlineColor}"
        fillRule="evenodd"
        d="M12 11.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m0 1.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6"
        clipRule="evenodd"
      />
    </svg>
    `;
    return encodeURIComponent(svg);
  };

  const markerColor = {
    noJobs: {
      standard: {
        backgroundColor: '#22222630',
        outlineColor: '#22222630',
      },
      hovered: {
        backgroundColor: '#222226',
        outlineColor: '#222226',
      },
      selectedProject: {
        backgroundColor: '#22222680',
        outlineColor: '#222226',
      },
    },
    unassigned: {
      standard: {
        backgroundColor: grey[200],
        outlineColor: grey[300],
      },
      hovered: {
        backgroundColor: grey[500],
        outlineColor: grey[300],
      },
      selectedProject: {
        backgroundColor: grey[200],
        outlineColor: grey[300],
      },
    },
    assigned: {
      standard: {
        backgroundColor: '#ffae51ba',
        outlineColor: grey[500],
      },
      hovered: {
        backgroundColor: brandYellow[500],
        outlineColor: grey[500],
      },
      selectedProject: {
        backgroundColor: brandYellow[500],
        outlineColor: grey[500],
      },
    },
  };

  const iconMarker = (colors: { backgroundColor: string; outlineColor: string }) =>
    new Icon({
      iconUrl: `data:image/svg+xml;utf8,${createSvg(colors.backgroundColor, colors.outlineColor)}`,
      iconSize: [sizeMarkerIcon, sizeMarkerIcon],
      iconAnchor: [sizeMarkerIcon / 2, sizeMarkerIcon],
      popupAnchor: [0, -sizeMarkerIcon],
    });

  const createAiraSymbol = () => {
    const airaSymbol = `<?xml version="1.0" encoding="iso-8859-1"?>
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 166.3 166.3" width="50" height="50">
      <rect fill="#222226" width="100%" height="100%" rx="28px" ry="28px" />
      <path fill="#FFAF51" d="M36.9,121.9l33.3-77.8c0.3-0.8,0.8-1,1.6-1h22.6c0.8,0,1.2,0.2,1.6,1l33.5,77.8c0.3,0.8-0.1,1.2-0.9,1.2h-12.2 c-0.8,0-1.1-0.1-1.5-0.8l-7.8-17.5c-0.3-0.8-0.8-1-1.6-1H60.9c-0.8,0-1.2,0.2-1.6,1l-7.7,17.5c-0.3,0.7-0.7,0.8-1.5,0.8H37.8 C37,123.2,36.6,122.7,36.9,121.9L36.9,121.9z M66,91.2h34.1c1,0,1.5-0.6,1-1.6l-14-32.9c-0.3-0.7-0.8-1-1.6-1h-4.9 c-0.8,0-1.3,0.3-1.6,1L65,89.7C64.6,90.7,65,91.2,66,91.2z" />
      <g> 
      <path fill="#FFAF51" d="M138.2,166.3h-15V160h15c12,0,21.8-9.8,21.8-21.8v-15h6.3v15C166.3,153.7,153.7,166.3,138.2,166.3z" />
      <path fill="#FFAF51" d="M138.2,0h-15v6.3h15c12,0,21.8,9.8,21.8,21.8v15h6.3v-15C166.3,12.6,153.7,0,138.2,0z" />
      <path fill="#FFAF51" d="M28.2,166.3h15V160h-15c-12,0-21.8-9.8-21.8-21.8v-15H0l0,15C0,153.7,12.6,166.3,28.2,166.3z" />
      <path fill="#FFAF51" d="M28.2,0l15,0v6.3h-15c-12,0-21.8,9.8-21.8,21.8v15H0l0-15C0,12.6,12.6,0,28.2,0z" />
      </g>
    </svg>`;
    return encodeURIComponent(airaSymbol);
  };

  const iconHub = new Icon({
    iconUrl: `data:image/svg+xml;utf8,${createAiraSymbol()}`,
    iconSize: [30, 30],
    iconAnchor: [30 / 2, 30],
    popupAnchor: [0, -30],
  });

  // Adjust projectToShow to include hoveredProject and selectedProject
  const projectToShow = useMemo(() => {
    if (hoveredProject && selectedProject) {
      if (hoveredProject.installationProject?.id?.value === selectedProject.installationProject?.id?.value) {
        return [selectedProject];
      }
      return [selectedProject, hoveredProject];
    }

    if (hightlightProjectsWithinDistance && selectedProject) {
      return projects.filter((project) => {
        if (!project.address?.specificDetails?.geometry || !selectedProject.address?.specificDetails?.geometry) {
          return false;
        }
        const distance = getDistanceBetweenTwoLatLong(
          selectedProject.address?.specificDetails?.geometry,
          project.address?.specificDetails?.geometry,
        );
        return distance < hightlightProjectsWithinDistance;
      });
    }
    if (selectedProject) return [selectedProject];
    return projects;
  }, [hoveredProject, selectedProject, hightlightProjectsWithinDistance, projects]);

  // Use useMemo to ensure markerPositions updates when projectToShow changes
  const markerPositions = useMemo(
    () =>
      [
        ...projectToShow.map((project) => {
          const lat = project.address?.specificDetails?.geometry?.lat;
          const lng = project.address?.specificDetails?.geometry?.long;
          if (lat !== undefined && lng !== undefined) {
            return [lat, lng];
          }
          return null;
        }),
        hubGeometry ? [hubGeometry.lat, hubGeometry.long] : null,
      ].filter((pos) => pos !== null) as [number, number][],
    [projectToShow, hubGeometry],
  );

  return (
    <Stack
      mb={2}
      px="16px"
      sx={{
        width: '100%',
        background: beige[100],
      }}
    >
      <MapContainer
        style={{ height: '324px', width: '318px', borderRadius: '8px' }}
        attributionControl={false}
        zoom={8}
      >
        <TileLayer
          attribution=""
          url="https://www.google.com/maps/vt?lyrs=m@189&gl=gb&x={x}&y={y}&z={z}"
          className="greyscale-map"
        />

        {/* Render project markers */}

        {projectToShow.map((project) => {
          const isSelected = selectedProject?.installationProject?.id?.value === project.installationProject?.id?.value;
          const isHovered = hoveredProject?.installationProject?.id?.value === project.installationProject?.id?.value;
          const getIcon = () => {
            let markerType = 'standard';
            if (isSelected) {
              markerType = 'selectedProject';
            } else if (isHovered) {
              markerType = 'hovered';
            }

            if (!project.installationProject || !project.installationProject.id) {
              return iconMarker(markerColor.noJobs[markerType as keyof typeof markerColor.noJobs]);
            }
            if (project.installationProject.jobs.length === 0) {
              return iconMarker(markerColor.noJobs[markerType as keyof typeof markerColor.noJobs]);
            }
            if (project.installationProject.jobs.some((job) => job.assignedResources.length > 0)) {
              return iconMarker(markerColor.assigned[markerType as keyof typeof markerColor.assigned]);
            }
            return iconMarker(markerColor.unassigned[markerType as keyof typeof markerColor.unassigned]);
          };

          const icon = getIcon();
          const customerName = `${project.contact?.firstName} ${project.contact?.lastName}`;
          return (
            <Marker
              key={project.installationProject?.id?.value}
              icon={icon}
              position={{
                lat: project.address?.specificDetails?.geometry?.lat ?? 0,
                lng: project.address?.specificDetails?.geometry?.long ?? 0,
              }}
              riseOnHover
              autoPanOnFocus
              eventHandlers={{
                click: () => {
                  setSelectedProject(project);
                },
              }}
            >
              {isHovered && (
                <Tooltip permanent direction="right">
                  <Typography variant="body1">{customerName}</Typography>
                </Tooltip>
              )}
              {isSelected && (
                <Tooltip permanent direction="right">
                  <Typography variant="body1">{customerName}</Typography>
                </Tooltip>
              )}
              <Tooltip direction="right">
                <Typography variant="body1">{customerName}</Typography>
              </Tooltip>
            </Marker>
          );
        })}

        {/* Render hub marker */}
        {hubGeometry && (
          <Marker
            icon={iconHub}
            position={{
              lat: hubGeometry.lat,
              lng: hubGeometry.long,
            }}
          />
        )}

        {/* Render Polyline between selected and hovered projects */}
        {route && <Polyline positions={route} color={green[700]} />}

        {/* Fit map bounds to markers and route */}
        <FitBounds markers={markerPositions} />
      </MapContainer>

      <Stack
        gap={2}
        sx={{ background: '#22222608', py: 3, px: 2, borderRadius: 1, mt: 2, height: 'fit-content', width: '100%' }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
          <Stack direction="row" justifyContent="flex-start" alignItems="center">
            <Checkbox
              checked={!!hightlightProjectsWithinDistance}
              onChange={() => {
                if (distanceInput && !hightlightProjectsWithinDistance) {
                  setHighlightProjectsWithinDistance(distanceInput);
                } else {
                  setHighlightProjectsWithinDistance();
                }
              }}
              label={
                <FormattedMessage
                  id="installationPlanning.highlightProjectsWithin"
                  defaultMessage="Highlight projects within "
                />
              }
            />
          </Stack>
          <Stack direction="row" gap={1} alignItems="center" ml={1}>
            <TextField
              name="distance"
              type="number"
              size="small"
              value={distanceInput?.toString() ?? ''}
              onChange={(e) => {
                const { value } = e.target;
                setDistanceInput(value ? parseFloat(value) : null);
                if (value && hightlightProjectsWithinDistance) {
                  setHighlightProjectsWithinDistance(parseFloat(value));
                }
              }}
              sx={{
                width: '50px',
                height: '24px',
                fontSize: '14px',
                '& input': {
                  textAlign: 'center',
                  border: 'none',
                },
                '.MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                  width: '50px',
                },
              }}
            />
            <Typography variant="body2">km</Typography>
          </Stack>
        </Stack>
        <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center">
          <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center">
            <CarOutlinedIcon height={16} width={16} />
            <Typography variant="body2">Driving Time</Typography>
          </Stack>

          <Typography variant="body2">{drivingTime}</Typography>
        </Stack>

        <Stack direction="row" spacing={1} justifyContent="space-between" alignItems="center">
          <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center">
            <PinOutlinedIcon height={16} width={16} />
            <Typography variant="body2">Driving Distance</Typography>
          </Stack>
          <Typography variant="body2">{drivingDistance}</Typography>
        </Stack>
      </Stack>
    </Stack>
  );
}

export default PlanningMap;
