import { Box } from '@mui/material';
import { sendGTMEvent } from '@next/third-parties/google';
import Link from 'next/link';
import { api } from 'utils/api';
import { Button } from '@ui/components/Button/Button';
import { FormattedMessage } from 'react-intl';

export default function HubspotButton({ installationProjectId }: { installationProjectId: string }) {
  const { data: hubspotLink } = api.Hubspot.getHubspotLink.useQuery(
    {
      installationProjectId,
    },
    {
      enabled: !!installationProjectId,
    },
  );

  if (!hubspotLink) return null;
  return (
    <Box sx={{ width: '100%', mt: 2 }}>
      <Link href={hubspotLink} target="_blank">
        <Button
          variant="outlined"
          size="small"
          fullWidth
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'view_project_in_hubspot',
              app_name: 'installation_planning',
            });
          }}
        >
          <FormattedMessage id="installationPlanning.viewProjectInHubspot" defaultMessage="View project in Hubspot" />
        </Button>
      </Link>
    </Box>
  );
}
