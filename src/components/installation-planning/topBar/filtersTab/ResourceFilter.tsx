import { Autocomplete, Skeleton, TextField } from '@mui/material';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { useResources, useResourcesLoading } from 'components/installation-planning/contexts/ResourcesContext';
import { useAssignedResourceFilter, useProjectActions } from '../../stores/ProjectStore';

export default function ResourceFilter() {
  const { isLoadingResources } = useResourcesLoading();
  const resourcesForRegion = useResources();
  const { setAssignedResourceFilter } = useProjectActions();
  const assignedResourceFilter = useAssignedResourceFilter();

  if (isLoadingResources) {
    return <Skeleton variant="rounded" width={300} height={40} />;
  }

  if (!resourcesForRegion) {
    return null;
  }

  const options = Array.from(resourcesForRegion.values());

  const selectedOption = assignedResourceFilter
    .map((resource) => resourcesForRegion.get(resource))
    .filter((option) => option !== undefined);
  return (
    <Autocomplete
      multiple
      defaultValue={[]}
      value={selectedOption}
      groupBy={(option) => option.jobTitle}
      options={options}
      getOptionLabel={(option) => `${option.firstName} ${option.lastName}`}
      getOptionKey={(option) => option.userId?.value ?? 'unknown'}
      renderInput={(params) => <TextField {...params} placeholder="Filter projects by resource" />}
      onChange={(event, newValue) => {
        if (!event || newValue.length === 0) {
          setAssignedResourceFilter([]);
          return;
        }
        setAssignedResourceFilter(newValue.map((resource) => resource.userId?.value as string));
      }}
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        zIndex: 4000,
        width: '100%',
        '& .MuiAutocomplete-inputRoot': {
          overflowY: 'auto',
          flexGrow: 1,
        },
        '& .MuiAutocomplete-tag': {
          whiteSpace: 'nowrap',
        },
        '& .MuiChip-label': {
          textTransform: 'capitalize',
          fontFamily: 'AiraText',
        },
      }}
      slotProps={{
        popper: {
          sx: {
            zIndex: 9999,
          },
        },
      }}
      popupIcon={<ChevronDown height={20} width={20} />}
    />
  );
}
