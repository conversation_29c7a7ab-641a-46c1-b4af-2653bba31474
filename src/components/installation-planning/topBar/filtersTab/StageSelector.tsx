import React from 'react';
import { MenuItem, Typography, Autocomplete, TextField, Chip, Box } from '@mui/material';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { brandYellow, green, grey, magenta, teal } from '@ui/theme/colors';
import { useIntl } from 'react-intl';
import { StageForFilter } from '../../types/planningTypes';
import { useProjectActions, useStageFilter } from '../../stores/ProjectStore';

export default function StageSelector() {
  const { formatMessage } = useIntl();
  const stageFilter = useStageFilter();
  const { setStageFilter } = useProjectActions();

  const stages = [
    {
      value: 1,
      label: formatMessage({ id: 'installationPlanning.stage.new', defaultMessage: 'New' }),
    },
    {
      value: 2,
      label: formatMessage({
        id: 'installationPlanning.stage.technicalSurvey',
        defaultMessage: 'Awaiting Technical Survey',
      }),
    },
    {
      value: 3,
      label: formatMessage({
        id: 'installationPlanning.stage.technicalDesign',
        defaultMessage: 'Awaiting Technical Design',
      }),
    },
    {
      value: 4,
      label: formatMessage({ id: 'installationPlanning.stage.preInstallation', defaultMessage: 'Pre Installation' }),
    },
    {
      value: 5,
      label: formatMessage({ id: 'installationPlanning.stage.installation', defaultMessage: 'Installation' }),
    },
    {
      value: 6,
      label: formatMessage({ id: 'installationPlanning.stage.postInstallation', defaultMessage: 'Post Installation' }),
    },
  ];

  const stageColors = [
    { value: 1, color: grey[100] },
    { value: 2, color: grey[200] },
    { value: 3, color: teal[300] },
    { value: 4, color: green[300] },
    { value: 5, color: brandYellow[300] },
    { value: 6, color: magenta[300] },
  ];

  const stageFilterValues = stageFilter.map((stage) => stage.value);

  const selectedOptions = stageFilter
    .map((stage) => stages.find((option) => option.value === stage.value))
    .filter((option) => option !== undefined);
  return stageFilter ? (
    <Autocomplete
      multiple
      onChange={(_event, newValue) => {
        setStageFilter(newValue);
      }}
      value={selectedOptions}
      options={stages}
      getOptionLabel={(stage: StageForFilter) => stage.label}
      getOptionKey={(option) => option.value}
      isOptionEqualToValue={(option, value) => option.value === value.value}
      renderOption={(props, option) => (
        <MenuItem
          {...props}
          key={option.value}
          value={option.value}
          sx={{
            '&:hover > div': {
              border: `1px solid black`,
            },
          }}
        >
          <Box
            sx={{
              width: '100%',
              padding: '8px 12px',
              borderRadius: '8px',
              background: stageColors.find((color) => color.value === option.value)?.color,
              border: stageFilterValues.includes(option.value)
                ? `1px solid black`
                : `1px solid ${stageColors.find((color) => color.value === option.value)?.color}`,
            }}
          >
            <Typography variant="body3Emphasis">{option.label}</Typography>
          </Box>
        </MenuItem>
      )}
      renderTags={(selectedStages, getTagProps) =>
        selectedStages.map((option, index) => (
          <Chip
            {...getTagProps({ index })}
            key={option.value}
            label={option.label}
            sx={{
              background: stageColors.find((color) => color.value === option.value)?.color,
              '& .MuiChip-label': {
                fontFamily: 'AiraText',
                textTransform: 'capitalize',
              },
            }}
          />
        ))
      }
      sx={{
        width: '100%',
      }}
      disableCloseOnSelect
      slotProps={{
        popper: {
          sx: {
            zIndex: 9999,
            '& .MuiAutocomplete-listbox': {
              '& .MuiAutocomplete-option': {
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                background: 'none !important',
              },
            },
          },
        },
      }}
      popupIcon={<ChevronDown height={20} width={20} />}
      renderInput={(params) => <TextField {...params} placeholder="Filter projects by stage" />}
    />
  ) : null;
}
