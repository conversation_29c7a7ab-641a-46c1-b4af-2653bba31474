import { FormControlLabel, Switch } from '@mui/material';
import { sendGTMEvent } from '@next/third-parties/google';
import { grey } from '@ui/theme/colors';
import { useAllProjects } from 'components/installation-planning/contexts/AllProjects';
import { FormattedMessage } from 'react-intl';

export default function InSalesRecoverySwitch() {
  const { showInSalesRecoveryProjects, setShowInSalesRecoveryProjects } = useAllProjects();

  return (
    <FormControlLabel
      control={
        <Switch
          checked={showInSalesRecoveryProjects}
          onChange={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'show_in_sales_recovery_projects_switch',
              app_name: 'installation_planning',
            });
            setShowInSalesRecoveryProjects(!showInSalesRecoveryProjects);
          }}
          name="highlightFlexibleProjects"
          color="secondary"
          sx={{
            width: '40px',
            height: '24px',
            padding: 0,
            my: 'auto',
            '& .MuiSwitch-track': {
              height: '24px',
              width: '42px',
              backgroundColor: showInSalesRecoveryProjects ? '#FFAF51' : 'rgba(34, 34, 38, 0.08)',
              opacity: 1,
              borderRadius: '100px',
            },
            '& .MuiSwitch-thumb': {
              position: 'relative',
              width: '18px',
              height: '18px',
              left: showInSalesRecoveryProjects ? '0px' : '2px',
              boxShadow: 'none',
              borderRadius: 26 / 2,
              top: '3px',
              border: 'none',
              backgroundColor: showInSalesRecoveryProjects ? '#ffffff' : '#ffffff',
            },
            '& .MuiTouchRipple-root': {
              display: 'none !important',
            },
            '& .MuiSwitch-input': {
              height: '24px',
              width: '42px',
            },
            '& .MuiSwitch-switchBase': {
              padding: 0,
              '&:hover': {
                backgroundColor: 'transparent',
              },
            },
          }}
        />
      }
      sx={{
        backgroundColor: 'rgba(34, 34, 38, 0.03)',
        borderRadius: '100px',
        px: 2,
        py: '10px',
        width: '100%',
        justifyContent: 'space-between',

        '.MuiFormControlLabel-label': {
          fontSize: '12px',
          lineHeight: '150%',
          fontWeight: 500,
          color: grey[900],
          mr: 2,
        },
      }}
      label={
        <FormattedMessage
          id="installationPlanning.inSalesRecoveryProjects"
          defaultMessage="In sales recovery projects"
        />
      }
      labelPlacement="start"
    />
  );
}
