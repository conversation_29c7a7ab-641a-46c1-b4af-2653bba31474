import { Autocomplete, TextField } from '@mui/material';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { useProjectActions, useTicketFilter } from '../../stores/ProjectStore';
import { useAllProjects } from 'components/installation-planning/contexts/AllProjects';
import { useMemo } from 'react';

export function getTicketName(str: string) {
  const containsInstallation = str.includes('[Installation]');
  if (containsInstallation) {
    return str.split('[Installation]')[0]!.trim().split(' ').slice(0, -1).join(' ');
  }
  if (str.includes('[KfW]')) {
    return '[KfW]';
  }
  return str;
}

export default function TicketFilter() {
  const { allProjectsBeforeFiltersAndUnsavedChangesApplied } = useAllProjects();
  const ticketFilter = useTicketFilter();
  const { setTicketFilter } = useProjectActions();

  const ticketTypes = useMemo(() => {
    const tickets = new Map<string, string>();
    allProjectsBeforeFiltersAndUnsavedChangesApplied.forEach((project) => {
      project.installationProject?.progressOverview?.currentBlockers.forEach((blocker) => {
        if (blocker.blocker?.$case === 'openTicket') {
          const ticketName = getTicketName(blocker.blocker.openTicket.name);
          tickets.set(ticketName, ticketName);
        }
      });
    });
    return Array.from(tickets.values()).map((ticket) => ({
      label: ticket,
      value: ticket,
    }));
  }, [allProjectsBeforeFiltersAndUnsavedChangesApplied]);

  const options = ticketTypes;

  return (
    <Autocomplete
      multiple
      defaultValue={[]}
      value={ticketFilter}
      options={options}
      getOptionLabel={(option) => option.label}
      getOptionKey={(option) => option.value}
      renderInput={(params) => <TextField {...params} placeholder="Filter projects by open ticket" />}
      onChange={(event, newValue) => {
        if (!event || newValue.length === 0) {
          setTicketFilter([]);
          return;
        }
        setTicketFilter(newValue);
      }}
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        zIndex: 4000,
        width: '100%',
        '& .MuiAutocomplete-inputRoot': {
          overflowY: 'auto',
          flexGrow: 1,
        },
        '& .MuiAutocomplete-tag': {
          whiteSpace: 'nowrap',
        },
        '& .MuiChip-label': {
          textTransform: 'capitalize',
          fontFamily: 'AiraText',
        },
      }}
      slotProps={{
        popper: {
          sx: {
            zIndex: 9999,
          },
        },
      }}
      popupIcon={<ChevronDown height={20} width={20} />}
    />
  );
}
