import { FormControlLabel, Switch } from '@mui/material';
import { sendGTMEvent } from '@next/third-parties/google';
import { grey } from '@ui/theme/colors';
import { useAllProjects } from 'components/installation-planning/contexts/AllProjects';
import { FormattedMessage } from 'react-intl';

export default function OnHoldSwitch() {
  const { showOnHoldProjects, setShowOnHoldProjects } = useAllProjects();

  return (
    <FormControlLabel
      control={
        <Switch
          checked={showOnHoldProjects}
          onChange={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'show_on_hold_projects_switch',
              app_name: 'installation_planning',
            });
            setShowOnHoldProjects(!showOnHoldProjects);
          }}
          name="highlightFlexibleProjects"
          color="secondary"
          sx={{
            width: '40px',
            height: '24px',
            padding: 0,
            my: 'auto',
            '& .MuiSwitch-track': {
              height: '24px',
              width: '42px',
              backgroundColor: showOnHoldProjects ? '#FFAF51' : 'rgba(34, 34, 38, 0.08)',
              opacity: 1,
              borderRadius: '100px',
            },
            '& .MuiSwitch-thumb': {
              position: 'relative',
              width: '18px',
              height: '18px',
              left: showOnHoldProjects ? '0px' : '2px',
              boxShadow: 'none',
              borderRadius: 26 / 2,
              top: '3px',
              border: 'none',
              backgroundColor: showOnHoldProjects ? '#ffffff' : '#ffffff',
            },
            '& .MuiTouchRipple-root': {
              display: 'none !important',
            },
            '& .MuiSwitch-input': {
              height: '24px',
              width: '42px',
            },
            '& .MuiSwitch-switchBase': {
              padding: 0,
              '&:hover': {
                backgroundColor: 'transparent',
              },
            },
          }}
        />
      }
      sx={{
        backgroundColor: 'rgba(34, 34, 38, 0.03)',
        borderRadius: '100px',
        px: 2,
        py: '10px',
        width: '100%',
        justifyContent: 'space-between',

        '.MuiFormControlLabel-label': {
          fontSize: '12px',
          lineHeight: '150%',
          fontWeight: 500,
          color: grey[900],
          mr: 2,
        },
      }}
      label={<FormattedMessage id="installationPlanning.onHoldProjects" defaultMessage="On-hold projects" />}
      labelPlacement="start"
    />
  );
}
