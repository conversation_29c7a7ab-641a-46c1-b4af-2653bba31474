import { IconButton, Stack } from '@mui/material';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { grey } from '@ui/theme/colors';
import StageSelector from './StageSelector';
import ProjectSearch from './ProjectSearch';
import ResourceFilter from './ResourceFilter';
import { useProjectActions, useShowFiltersTab } from '../../stores/ProjectStore';
import FlexibleProjectsSwitch from './FlexibleProjectsSwitch';
import InSalesRecoverySwitch from './InSalesRecoverySwitch';
import OnHoldSwitch from './OnHoldSwitch';
import TicketFilter from './TicketFilter';

export default function FiltersTab() {
  const { setShowFiltersTab } = useProjectActions();
  const showFiltersTab = useShowFiltersTab();

  if (!showFiltersTab) {
    return null;
  }

  return (
    <Stack
      direction="column"
      spacing={4}
      sx={{
        position: 'absolute',
        top: 80,
        right: 20,
        width: '400px',
        px: 0,
        pt: '24px',
        pb: 0,
        boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '22px',
        zIndex: 3001,
        background: grey[100],
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="center" px={2}>
        <IconButton
          onClick={() => {
            setShowFiltersTab(false);
          }}
        >
          <CrossOutlinedIcon height={24} width={24} />
        </IconButton>
      </Stack>
      <Stack sx={{ p: 7, pt: 0 }} spacing={3} alignItems="flex-start">
        <ProjectSearch />
        <ResourceFilter />
        <StageSelector />
        <TicketFilter />
        <FlexibleProjectsSwitch />
        <OnHoldSwitch />
        <InSalesRecoverySwitch />
      </Stack>
    </Stack>
  );
}
