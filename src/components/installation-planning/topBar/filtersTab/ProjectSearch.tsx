import { Autocomplete, TextField } from '@mui/material';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { useAllProjects } from 'components/installation-planning/contexts/AllProjects';
import { useFocusedProject, useProjectActions } from '../../stores/ProjectStore';

export default function ProjectSearch() {
  const { setFocusedProject, setSelectedProject } = useProjectActions();
  const currentFocusedProject = useFocusedProject();
  const { projects } = useAllProjects();

  const options = projects
    .map((project) => ({
      key: project.installationProject?.id?.value ?? 'unknown',
      label: `${project?.contact?.firstName} ${project?.contact?.lastName}`,
      value: project.installationProject?.id?.value ?? 'unknown',
    }))
    .sort((a, b) => a.label.localeCompare(b.label));

  const selectedOption = currentFocusedProject
    ? options.find((option) => option.value === currentFocusedProject.installationProject?.id?.value)
    : undefined;
  return (
    <Autocomplete
      fullWidth
      options={options}
      value={selectedOption}
      renderInput={(params) => <TextField {...params} placeholder="Find project" />}
      onChange={(event, value) => {
        if (!event || !value) {
          setFocusedProject(undefined);
          setSelectedProject(undefined);
          return;
        }
        const selectedProject = projects.find(
          (project) => project.installationProject?.id?.value === (value.value as string),
        );
        if (!selectedProject) return;
        setFocusedProject(selectedProject);
        setSelectedProject(selectedProject);
      }}
      sx={{
        '& .MuiChip-label': {
          textTransform: 'capitalize',
          fontFamily: 'AiraText',
        },
      }}
      slotProps={{
        popper: {
          sx: {
            zIndex: 9999,
          },
        },
      }}
      popupIcon={<ChevronDown height={20} width={20} />}
    />
  );
}
