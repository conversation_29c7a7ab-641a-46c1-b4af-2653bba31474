import { useProjectActions, useTeamUnavailabilityMode } from '../stores/ProjectStore';
import { Switch } from '../components/Switch';

export default function TeamUnavailabilityModeSwitch() {
  const teamUnavailabilityMode = useTeamUnavailabilityMode();
  const { setTeamUnavailabilityMode } = useProjectActions();
  return (
    <Switch
      checked={teamUnavailabilityMode}
      onChange={() => {
        if (teamUnavailabilityMode) {
          setTeamUnavailabilityMode(false);
        }
        if (!teamUnavailabilityMode) {
          setTeamUnavailabilityMode(true);
        }
      }}
      name="teamUnavailabilityMode"
      label="Team unavailability mode"
    />
  );
}
