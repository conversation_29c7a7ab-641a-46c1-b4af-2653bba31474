import { Box, Typography } from '@mui/material';
import { FilterOutlinedIcon } from '@ui/components/StandardIcons/FilterOutlinedIcon';
import { FormattedMessage } from 'react-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { useShowFiltersTab, useProjectActions } from '../stores/ProjectStore';

export default function FiltersButton() {
  const { setShowFiltersTab, setShowUnsavedChanges } = useProjectActions();
  const showFiltersTab = useShowFiltersTab();
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: showFiltersTab ? '#2222260F' : '#22222608',
        borderRadius: '67px',
        border: 0,
        fontSize: '12px',
        height: '40px',
        lineHeight: '150%',
        padding: '8px 16px',
        marginRight: '16px',
        '&:hover': {
          cursor: 'pointer',
        },
      }}
      onClick={() => {
        sendGTMEvent({
          event: 'button_click',
          click_text: 'filters_toggle',
          app_name: 'installation_planning',
        });
        if (!showFiltersTab) {
          setShowUnsavedChanges(false);
        }
        setShowFiltersTab(!showFiltersTab);
      }}
    >
      <FilterOutlinedIcon height={20} width={20} />
      <Typography variant="body1Emphasis" fontSize="12px" lineHeight="150%" pl={1}>
        <FormattedMessage id="installationPlanning.showFilters" defaultMessage="Filters" />
      </Typography>
    </Box>
  );
}
