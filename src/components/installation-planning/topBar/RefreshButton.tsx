import IconButton from '@mui/material/IconButton';
import { ArrowSpinClockwiseOutlinedIcon } from '@ui/components/StandardIcons/ArrowSpinClockwiseOutlinedIcon';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import toast from 'react-hot-toast';
import { FormattedMessage } from 'react-intl';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

export default function RefreshButton() {
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();

  const handleRefresh = async () => {
    try {
      await refetchProjectsUpdatedSince();
      toast.success(
        <FormattedMessage
          id="installationPlanning.topBar.refreshButton.success"
          defaultMessage="The latest data has been fetched"
        />,
      );
    } catch (e) {
      console.error(e);
      toast.error(
        <FormattedMessage id="installationPlanning.topBar.refreshButton.error" defaultMessage="Error refreshing" />,
      );
    }
  };

  return (
    <Tooltip
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: '#fff',
            borderRadius: '8px',
            padding: '8px',
            width: '200px',
            textAlign: 'center',
            boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.25)',
          },
        },
      }}
      title={
        <Typography variant="body1">
          <FormattedMessage
            id="installationPlanning.topBar.refreshButton.tooltip"
            defaultMessage="Get the latest data from the server"
          />
        </Typography>
      }
    >
      <IconButton
        onClick={handleRefresh}
        sx={{
          background: '#22222608',
          border: 'none',
          padding: 0,
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          '&:hover': {
            background: '#22222610',
          },
        }}
      >
        <ArrowSpinClockwiseOutlinedIcon color="#222226" />
      </IconButton>
    </Tooltip>
  );
}
