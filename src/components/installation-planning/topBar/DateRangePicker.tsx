import React, { useEffect, useState } from 'react';
import { addDays, addWeeks, isFriday, isMonday, nextFriday, startOfDay, startOfWeek } from 'date-fns';
import 'react-datepicker/dist/react-datepicker.css';
import { Button } from '@ui/components/Button/Button';
import { CalendarIcon } from '@ui/components/Icons/Calendar/CalendarIcon';
import { Stack, Typography } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { tz } from '@date-fns/tz';
import { sendGTMEvent } from '@next/third-parties/google';
import { useRegionContext } from '../../../context/RegionContext';
import { DateCalendar, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { FormattedMessage } from 'react-intl';

export interface DateRange {
  startDate: Date;
  endDateInclusive: Date;
}

interface DateRangeWithOptionalDates {
  startDate?: Date;
  endDateInclusive?: Date;
}

export default function DateRangePicker({
  savedDateRange,
  setSavedDateRange,
}: {
  savedDateRange: DateRange;
  setSavedDateRange: (dateRange: DateRange) => void;
}) {
  const { timeZone } = useRegionContext();
  const [dateRange, setDateRange] = useState<DateRangeWithOptionalDates>(savedDateRange);
  const [open, setOpen] = useState(false);

  const handleOnSaveDate = () => {
    if (!dateRange || !dateRange.startDate || !dateRange.endDateInclusive) return;
    // make sure the start date is a monday
    const newStartDate = startOfWeek(startOfDay(dateRange.startDate, { in: tz(timeZone) }), {
      in: tz(timeZone),
      weekStartsOn: 1,
    });
    // make sure the end date is a friday
    const newEndDate = addDays(startOfWeek(dateRange.endDateInclusive, { in: tz(timeZone), weekStartsOn: 1 }), 4);
    setSavedDateRange({ startDate: newStartDate, endDateInclusive: newEndDate });
    setOpen(false);
  };

  useEffect(() => {
    function handleClickedOutside(ev: MouseEvent) {
      const target = ev.target as HTMLElement;
      if (
        !target.closest('.date-range-picker-container') &&
        !target.closest('#start-date-picker') &&
        !target.closest('#end-date-picker') &&
        !target.closest('.MuiPopper-root')
      ) {
        setDateRange(savedDateRange);
        setOpen(false);
      }
    }
    // handle clicks outside by closing the date picker
    document.addEventListener('click', handleClickedOutside);
    return () => {
      document.removeEventListener('click', handleClickedOutside);
    };
  }, [dateRange, savedDateRange, setDateRange, setOpen]);

  const handleNextWeeksClick = (weeks: number) => {
    const newEndDate = addWeeks(nextFriday(dateRange.startDate!, { in: tz(timeZone) }), weeks);
    setDateRange({ startDate: dateRange.startDate, endDateInclusive: newEndDate });
    setOpen(true);
  };

  const shouldDisableEndDate = (date: Date) => {
    const daysIsFriday = isFriday(date);
    if (daysIsFriday) {
      return false;
    }
    return true;
  };

  const shouldDisableStartDate = (date: Date) => {
    const daysIsMonday = isMonday(date);
    if (daysIsMonday) {
      return false;
    }
    return true;
  };

  return (
    <div className="date-range-picker-container">
      <Stack
        direction="row"
        justifyContent="center"
        alignItems="center"
        onClick={() => {
          sendGTMEvent({
            event: 'button_click',
            click_text: 'date_range_picker',
            app_name: 'installation_planning',
          });
          setOpen(!open);
        }}
        sx={{
          cursor: 'pointer',
          padding: 0,
          background: 'rgba(34, 34, 38, 0.03)',
          height: '40px',
          width: '40px',
          borderRadius: '50%',
        }}
      >
        <CalendarIcon />
      </Stack>
      {open && (
        <Stack
          sx={{
            position: 'fixed',
            top: '58px',
            left: 'calc(50% - 460px)',
            padding: '36px',
            background: 'white',
            borderRadius: '8px',
            boxShadow: '0px 36px 25px 0px rgba(0, 0, 0, 0.25)',
            zIndex: 3300,
          }}
          gap={5}
        >
          <Stack direction="row" gap={4}>
            <Stack>
              <Stack gap={4} direction="row">
                <Stack>
                  <Typography variant="body1Emphasis">
                    <FormattedMessage id="installationPlanning.dateRangePicker.startDate" defaultMessage="Start Date" />
                  </Typography>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateCalendar
                      reduceAnimations
                      value={dateRange.startDate}
                      onChange={(date) => {
                        if (date) {
                          if (date > dateRange.endDateInclusive!) {
                            const newEndDate = addWeeks(date, 1);
                            setDateRange({ ...dateRange, startDate: date, endDateInclusive: newEndDate });
                          } else {
                            setDateRange({ ...dateRange, startDate: date });
                          }
                        }
                      }}
                      sx={{ width: '100%', marginTop: 0.5, zIndex: 5300 }}
                      shouldDisableDate={shouldDisableStartDate}
                    />
                  </LocalizationProvider>
                </Stack>
                <Stack
                  justifyContent="space-around"
                  sx={{
                    padding: '12px',
                    borderRadius: '8px',
                    background: beige[150],
                  }}
                  gap={2}
                >
                  <Button variant="contained" size="small" onClick={() => handleNextWeeksClick(4)}>
                    Add 4 weeks
                  </Button>
                  <Button variant="contained" size="small" onClick={() => handleNextWeeksClick(8)}>
                    Add 8 weeks
                  </Button>
                  <Button variant="contained" size="small" onClick={() => handleNextWeeksClick(12)}>
                    Add 12 weeks
                  </Button>
                  <Button variant="contained" size="small" onClick={() => handleNextWeeksClick(24)}>
                    Add 24 weeks
                  </Button>
                </Stack>
                <Stack>
                  <Typography variant="body1Emphasis">
                    <FormattedMessage id="installationPlanning.dateRangePicker.endDate" defaultMessage="End Date" />
                  </Typography>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateCalendar
                      reduceAnimations
                      value={dateRange.endDateInclusive}
                      onChange={(date) => {
                        if (date) {
                          if (date < dateRange.startDate!) {
                            const newStartDate = addWeeks(date, -1);
                            setDateRange({ ...dateRange, startDate: newStartDate, endDateInclusive: date });
                          } else {
                            setDateRange({ ...dateRange, endDateInclusive: date });
                          }
                        }
                      }}
                      sx={{ width: '100%', marginTop: 0.5 }}
                      shouldDisableDate={shouldDisableEndDate}
                    />
                  </LocalizationProvider>
                </Stack>
              </Stack>

              <Button variant="contained" onClick={handleOnSaveDate} sx={{ marginTop: 2 }}>
                Save
              </Button>
            </Stack>
          </Stack>
        </Stack>
      )}
    </div>
  );
}
