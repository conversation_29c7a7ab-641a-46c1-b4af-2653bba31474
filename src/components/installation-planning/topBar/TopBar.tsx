import { IconButton, Skeleton, Stack, Typography } from '@mui/material';
import { AiraSymbol } from '@ui/components/Branding/AiraSymbol';
import { useRouter } from 'next/router';
import DateRangePicker from './DateRangePicker';
import FiltersButton from './FiltersButton';
import { useRegionContext } from 'context/RegionContext';
import { useDateRange } from '../contexts/DateRangeContext';
import NotificationButton from './notificationCenter/NotificationButton';
import { useProjectActions, useTableContents } from '../stores/ProjectStore';
import DispatchModeSwitch from './DispatchModeSwitch';
import TeamUnavailabilityModeSwitch from './TeamUnavailabilityModeSwitch';
import TodayScroller from './TodayScroller';
import StatisticsButton from './StatisticsButton';
import { theme } from '@ui/theme/theme';
import { TableContentsSwitch } from './TableContentsSwitch';
import { useResourcesLoading } from '../contexts/ResourcesContext';
import { PinOutlinedIcon } from '@ui/components/StandardIcons/PinOutlinedIcon';
import RefreshButton from './RefreshButton';
import ResourcePageButton from './ResourcePageButton';

export default function TopBar() {
  const { dateRange, setDateRange } = useDateRange();
  const { setShowInstallationsMapModal } = useProjectActions();
  const router = useRouter();
  const region = useRegionContext();
  const tableContents = useTableContents();
  const { isLoadingResources } = useResourcesLoading();

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{ px: 0, py: '12px', background: '#22222608' }}
      gap={1}
      width="100dvw"
      flexWrap="wrap"
    >
      <Stack
        direction="row"
        justifyContent="flex-start"
        alignItems="center"
        gap={5}
        pl={2}
        sx={{
          minWidth: '400px',
          [theme.breakpoints.up(1900)]: {
            flexBasis: 0,
            flexGrow: 1,
          },
        }}
      >
        <IconButton
          sx={{
            background: 'transparent',
            border: 'none',
            padding: 0,
          }}
          onClick={() => {
            router.push('/installation-planning');
          }}
        >
          <AiraSymbol height={24} width={24} />
        </IconButton>
        <Typography fontSize="28px" variant="headline1">
          {region.name}
        </Typography>
        <RefreshButton />
      </Stack>
      <Stack direction="row" gap={2} mr={3}>
        <TodayScroller />
        <DateRangePicker savedDateRange={dateRange} setSavedDateRange={setDateRange} />
      </Stack>
      <Stack direction="row" alignItems="center" gap={3} mr={1} flexBasis={0} flexGrow={1} justifyContent="flex-end">
        {isLoadingResources ? (
          <>
            <Skeleton
              variant="rounded"
              animation="wave"
              height={40}
              width={160}
              sx={{
                ml: 2,
                borderRadius: '50px',
              }}
            />
            <Skeleton
              variant="rounded"
              animation="wave"
              height={40}
              width={160}
              sx={{
                ml: 2,
                borderRadius: '50px',
              }}
            />
            <Skeleton
              variant="rounded"
              animation="wave"
              height={40}
              width={90}
              sx={{
                ml: 2,
                mr: 1,
                borderRadius: '50px',
              }}
            />
          </>
        ) : (
          <>
            {tableContents === 'Resources' && <ResourcePageButton />}
            <IconButton
              onClick={() => {
                setShowInstallationsMapModal(true);
              }}
              sx={{
                background: '#22222608',
                border: 'none',
                padding: 0,
                borderRadius: '50%',
                width: '40px',
                height: '40px',
                '&:hover': {
                  background: '#22222610',
                },
              }}
            >
              <PinOutlinedIcon color="#222226" />
            </IconButton>
            {tableContents === 'Teams' && <DispatchModeSwitch />}
            {tableContents === 'Teams' && <TeamUnavailabilityModeSwitch />}
            {tableContents === 'Resources' && <StatisticsButton />}
            <TableContentsSwitch />
            <NotificationButton />
            <FiltersButton />
          </>
        )}
      </Stack>
    </Stack>
  );
}
