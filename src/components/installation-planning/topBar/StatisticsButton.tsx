import { Box, IconButton } from '@mui/material';
import { ChartColumnLineUpOutlinedIcon } from '@ui/components/StandardIcons/ChartColumnLineUpOutlinedIcon';
import { useProjectActions, useShowStatisticsDrawer } from '../stores/ProjectStore';
import { grey } from '@ui/theme/colors';

export default function StatisticsButton() {
  const { setShowStatisticsDrawer } = useProjectActions();
  const showStatisticsDrawer = useShowStatisticsDrawer();

  return (
    <Box position="relative">
      <IconButton
        sx={{
          borderRadius: '50%',
          backgroundColor: '#22222608',
          width: 40,
          height: 40,
          '&:hover': {
            backgroundColor: '#2222260F',
          },
        }}
        onClick={() => {
          setShowStatisticsDrawer(!showStatisticsDrawer);
        }}
      >
        <ChartColumnLineUpOutlinedIcon height={20} width={20} color={grey[900]} />
      </IconButton>
    </Box>
  );
}
