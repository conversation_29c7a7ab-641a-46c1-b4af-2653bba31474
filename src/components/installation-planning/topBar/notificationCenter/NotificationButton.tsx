import { Box, IconButton, Typography } from '@mui/material';
import { BellOutlinedIcon } from '@ui/components/StandardIcons/BellOutlinedIcon';
import { useProjectActions, useShowNewBaselines } from '../../stores/ProjectStore';
import { grey } from '@mui/material/colors';
import { useAllProjects } from 'components/installation-planning/contexts/AllProjects';
import { red } from '@ui/theme/colors';

export default function NotificationButton() {
  const showNewBaselines = useShowNewBaselines();
  const { projectsWithUnappliedBaseline } = useAllProjects();
  const { setShowNewBaselines } = useProjectActions();

  const nrOfUnappliedBaselines = projectsWithUnappliedBaseline.length;

  return (
    <Box position="relative">
      <IconButton
        sx={{
          borderRadius: '50%',
          backgroundColor: showNewBaselines ? '#2222261F' : '#22222608',
          width: 40,
          height: 40,
        }}
        onClick={() => {
          setShowNewBaselines(!showNewBaselines);
        }}
      >
        <BellOutlinedIcon height={20} width={20} color={grey[900]} />
      </IconButton>
      {nrOfUnappliedBaselines > 0 && (
        <Box
          sx={{
            position: 'absolute',
            top: 6,
            left: '50%',
            minWidth: '15px',
            height: '15px',
            backgroundColor: red[500],
            borderRadius: '8px',
            px: 0.5,
          }}
          display="flex"
          justifyContent="center"
        >
          <Typography variant="body2Emphasis" color="secondary.contrastText" sx={{ fontSize: '10px' }}>
            {nrOfUnappliedBaselines}
          </Typography>
        </Box>
      )}
    </Box>
  );
}
