import React from 'react';
import { Box, Typography, Stack, IconButton } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@ui/components/Icons/Chevron/Chevron';
import { sendGTMEvent } from '@next/third-parties/google';
import useTableCellDims from '../hooks/useTableCellDims';

export default function TodayScroller() {
  const { dayWidth } = useTableCellDims();
  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        background: 'rgba(34, 34, 38, 0.03)',
        borderRadius: '67px',
        width: '150px',
        height: '40px',
        margin: 'auto',
      }}
    >
      <IconButton
        onClick={() => {
          sendGTMEvent({
            event: 'button_click',
            click_text: 'scroll_left',
            app_name: 'installation_planning',
          });
          const tableContainer = document.getElementById('tableContainer');
          if (tableContainer) {
            tableContainer.scrollBy({ left: -200, behavior: 'smooth' });
          }
        }}
      >
        <ChevronLeft width={16} height={16} />
      </IconButton>
      <Box
        onClick={() => {
          sendGTMEvent({
            event: 'button_click',
            click_text: 'scroll_to_today',
            app_name: 'installation_planning',
          });
          const tableContainer = document.getElementById('tableContainer');
          if (tableContainer) {
            const scrollPosition = dayWidth * 5;
            tableContainer.scrollTo({ left: scrollPosition, behavior: 'smooth' });
          }
        }}
        sx={{
          cursor: 'pointer',
          padding: '4px 8px',
        }}
      >
        <Typography variant="body1Emphasis" fontSize="12px" lineHeight="150%">
          Today
        </Typography>
      </Box>
      <IconButton
        sx={{ height: '40px' }}
        onClick={() => {
          sendGTMEvent({
            event: 'button_click',
            click_text: 'scroll_right',
            app_name: 'installation_planning',
          });
          const tableContainer = document.getElementById('tableContainer');
          if (tableContainer) {
            tableContainer.scrollBy({ left: 200, behavior: 'smooth' });
          }
        }}
      >
        <ChevronRight width={16} height={16} />
      </IconButton>
    </Stack>
  );
}
