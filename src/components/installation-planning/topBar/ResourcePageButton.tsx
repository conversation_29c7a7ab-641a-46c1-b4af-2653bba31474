import { IconButton } from '@mui/material';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { useRouter } from 'next/router';
import { memo } from 'react';
import { api } from 'utils/api';

const ResourcePageButton = memo(function ResourcePageButton() {
  const router = useRouter();
  const { 'installation-planning': regionId } = router.query as unknown as { 'installation-planning': string };
  const { data: region } = api.AiraBackend.getRegion.useQuery({ regionId }, { enabled: !!regionId });

  const country = region?.iso3166?.$case === 'country' ? region.iso3166.country : null;

  if (!country) {
    return null;
  }

  const url = `/country/${country}/region/${regionId}/resources/`;

  return (
    <IconButton
      onClick={() => {
        window.open(url, '_blank');
      }}
      sx={{
        background: '#22222608',
        border: 'none',
        padding: 0,
        borderRadius: '50%',
        width: '40px',
        height: '40px',
        '&:hover': {
          background: '#22222610',
        },
      }}
    >
      <PersonOutlinedIcon color="#222226" />
    </IconButton>
  );
});

export default ResourcePageButton;
