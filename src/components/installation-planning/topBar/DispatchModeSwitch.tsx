import { Switch } from '../components/Switch';
import { useProjectActions, useDispatchMode } from '../stores/ProjectStore';

export default function DispatchModeSwitch() {
  const dispatchMode = useDispatchMode();
  const { setDispatchMode } = useProjectActions();
  return (
    <Switch
      checked={dispatchMode}
      onChange={() => {
        if (dispatchMode) {
          setDispatchMode(false);
        }
        if (!dispatchMode) {
          setDispatchMode(true);
        }
      }}
      name="dispatchMode"
      label="Dispatch mode"
    />
  );
}
