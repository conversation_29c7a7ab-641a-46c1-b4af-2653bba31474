import { useProjectActions, useTableContents } from '../stores/ProjectStore';
import { Switch } from '../components/Switch';

export function TableContentsSwitch() {
  const { setTableContents, setDispatchMode } = useProjectActions();
  const tableContents = useTableContents();
  return (
    <Switch
      checked={tableContents === 'Resources'}
      onChange={() => {
        if (tableContents === 'Resources') {
          setTableContents('Teams');
        }
        if (tableContents === 'Teams') {
          setDispatchMode(false);
          setTableContents('Resources');
        }
      }}
      name="tableContents"
      label="Resource view"
    />
  );
}
