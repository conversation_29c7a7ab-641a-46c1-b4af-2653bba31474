import { useMemo } from 'react';
import { ContinuousWorkSegment, Row } from './types/planningTypes';
import { useDateRange } from './contexts/DateRangeContext';
import { useRegionContext } from '../../context/RegionContext';
import { calculateRow } from './calculateRow';

export function useCalculateRowsForIndividuals({
  continuousSegmentsByResource,
}: {
  continuousSegmentsByResource: Map<string, Record<string, ContinuousWorkSegment[]>>;
}) {
  const { weekdayMetrics } = useDateRange();
  const { timeZone } = useRegionContext();
  const daysInInterval = weekdayMetrics.weekdaysInRange.length;
  const { dateIndexMap } = useDateRange();
  const rowSize = daysInInterval * 2;

  return useMemo(() => {
    // Initialize container for resource scheduling rows
    const rows: Record<string, { row: Row; nrRows: number }> = {};

    // Calculate row positions for each resource's work segments
    Array.from(continuousSegmentsByResource.entries()).forEach(([resourceId, continuousWorkSegmentsForResource]) => {
      if (!continuousWorkSegmentsForResource.continuousWorkSegments) {
        return;
      }
      const row = calculateRow({
        continuousWorkSegments: continuousWorkSegmentsForResource.continuousWorkSegments,
        rowSize,
        dateIndexMap,
        timeZone,
      });

      if (!rows[resourceId]) {
        rows[resourceId] = {
          row: [],
          nrRows: 1,
        };
      }

      rows[resourceId]!.row.push(...row.row);
      rows[resourceId]!.nrRows = Math.max(rows[resourceId]!.nrRows, row.numberOfRows);
    });

    return rows;
  }, [continuousSegmentsByResource, rowSize, dateIndexMap, timeZone]);
}
