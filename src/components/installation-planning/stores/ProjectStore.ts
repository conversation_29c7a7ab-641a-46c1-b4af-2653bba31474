import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProjectJob_WorkSegment } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { isEqual } from 'lodash';
import { create } from 'zustand';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  DayTeamPickerData,
  StageForFilter,
  UnsavedChange,
} from '../types/planningTypes';

// Helper function to compare unsavedChanges without the reason field
function hasNonReasonChanges(oldChanges: Omit<UnsavedChange, 'reason'>[], newChanges: UnsavedChange[]): boolean {
  const strippedNewChanges = newChanges.map(({ reason: _reason, ...rest }) => rest);
  return !isEqual(oldChanges, strippedNewChanges);
}

export type ProjectStore = {
  actions: {
    setAssignedResourceFilter: (assignedResourceFilter: string[]) => void;
    setComparedProject: (project?: FullInstallationProjectEntity) => void;
    setDayTeamPickerData: (dayTeamPickerData?: DayTeamPickerData) => void;
    setDispatchMode: (dispatchMode: boolean) => void;
    setDroppableStartTime: (time: null | Date) => void;
    setFocusedProject: (project?: FullInstallationProjectEntity) => void;
    setHighlightFlexibleProjects: (highlight: boolean) => void;
    setHighlightProjectsWithinDistance: (distance?: number) => void;
    setHoveredProject: (project?: FullInstallationProjectEntity) => void;
    setHoveredUnsavedChangeJobId: (jobId: null | string) => void;
    setIncompleteProjectId: (projectId: null | string) => void;
    setJobBeingDragged: (job?: ContinuousWorkSegment) => void;
    setJobBeingEdited: (job?: ContinuousWorkSegment) => void;
    setJobBeingSplit: (job?: ContinuousWorkSegment) => void;
    setJobsToDispatch: (jobs: Record<string, FullInstallationProjectEntity>) => void;
    setOverlappingSegmentsForResources: (
      overlappingSegmentsForResources: Map<
        string,
        {
          overlappingSegments: {
            segments: {
              customerName: string;
              jobId: string;
              segment: InstallationProjectJob_WorkSegment;
            }[];
          };
        }[]
      >,
    ) => void;
    setProjectForNoBaselineHoursModal: (project: null | FullInstallationProjectEntity) => void;
    setProjectForIncompleteHoursModal: (project: null | FullInstallationProjectEntity) => void;
    setRightClickMenu: (
      rightClickMenu: null | {
        jobId: string;
        position: {
          x: number;
          y: number;
          up: boolean;
        };
        project: ContinuousWorkSegmentForJobItem;
        hasAssignedResources: boolean;
      },
    ) => void;
    setSelectedProject: (project?: FullInstallationProjectEntity) => void;
    setSelectedResourceHalfDay: (
      selectedResourceHalfDay: null | { date: Date; resourceId: string; startTime: '08' | '12' },
    ) => void;
    setSeparateHoursToBeAdded: (job?: ContinuousWorkSegment) => void;
    setShowAssignTeamLeadModal: (project: null | { projectId: string; teamLeadResourceId?: string }) => void;
    setShowCompactView: (showCompactView: boolean) => void;
    setShowEditTeamsModal: (showEditTeamsModal: boolean) => void;
    setShowFiltersTab: (showFiltersTab: boolean) => void;
    setShowNewBaselines: (showNewBaselines: boolean) => void;
    setShowStatisticsDrawer: (showStatisticsDrawer: boolean) => void;
    setShowRemoveAssignedResourcesModal: (
      job: null | {
        continuousWorkSegment: ContinuousWorkSegmentForJobItem;
        jobId: string;
      },
    ) => void;
    setShowRemoveResourceInactivityModal: (
      showRemoveResourceInactivityModal: null | {
        ids: string[];
        type: 'unavailable' | 'unutilized';
      },
    ) => void;
    setShowRightSidebar: (showRightSidebar: boolean) => void;
    setShowInstallationsMapModal: (showInstallationsMapModal: boolean) => void;
    setShowTopBarMenu: (showTopBarMenu: boolean) => void;
    setShowUnsavedChanges: (showUnsavedChanges: boolean) => void;
    setShowUnscheduledJobsSidebar: (showUnscheduledJobsSidebar: boolean) => void;
    setSplitSegmentsToMove: (
      segments: null | {
        segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
        segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
      },
    ) => void;
    setStageFilter: (stageFilter: StageForFilter[]) => void;
    setTableContents: (tableContents: 'Resources' | 'Teams') => void;
    setTeamUnavailabilityMode: (teamUnavailabilityMode: boolean) => void;
    setTicketFilter: (ticketFilter: { label: string; value: string }[]) => void;
    setToolTipProject: (project?: {
      segmentDetails: ContinuousWorkSegment['segmentDetails'];
      resourceId?: string;
      position: {
        top: number;
        right: number;
        arrowTop: number;
      };
    }) => void;
    setUnsavedChanges: (unsavedChanges: UnsavedChange[]) => void;
    updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;
  };
  assignedResourceFilter: string[];
  comparedProject?: FullInstallationProjectEntity;
  dayTeamPickerData?: DayTeamPickerData;
  dispatchMode: boolean;
  droppableStartTime: null | Date;
  focusedProject?: FullInstallationProjectEntity;
  highlightFlexibleProjects: boolean;
  highlightProjectsWithinDistance?: number;
  hoveredProject?: FullInstallationProjectEntity;
  hoveredUnsavedChangeJobId: null | string;
  incompleteProjectId: null | string;
  jobBeingDragged?: ContinuousWorkSegment;
  jobBeingEdited?: ContinuousWorkSegment;
  jobBeingSplit?: ContinuousWorkSegment;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  overlappingSegmentsForResources: Map<
    string,
    {
      overlappingSegments: {
        segments: {
          customerName: string;
          jobId: string;
          segment: InstallationProjectJob_WorkSegment;
        }[];
      };
    }[]
  >;
  projectForNoBaselineHoursModal: null | FullInstallationProjectEntity;
  projectForIncompleteHoursModal: null | FullInstallationProjectEntity;
  rightClickMenu: null | {
    jobId: string;
    position: {
      x: number;
      y: number;
      up: boolean;
    };
    project: ContinuousWorkSegmentForJobItem;
    hasAssignedResources: boolean;
  };
  selectedProject?: FullInstallationProjectEntity;
  selectedResourceHalfDay: null | {
    date: Date;
    resourceId: string;
    startTime: '08' | '12';
  };
  separateHoursToBeAdded?: ContinuousWorkSegment;
  showAssignTeamLeadModal: null | { projectId: string; teamLeadResourceId?: string };
  showCompactView: boolean;
  showEditTeamsModal: boolean;
  showFiltersTab: boolean;
  showNewBaselines: boolean;
  showRemoveAssignedResourcesModal: null | {
    continuousWorkSegment: ContinuousWorkSegmentForJobItem;
    jobId: string;
  };
  showRemoveResourceInactivityModal: null | {
    ids: string[];
    type: 'unavailable' | 'unutilized';
  };
  showRightSidebar: boolean;
  showInstallationsMapModal: boolean;
  showStatisticsDrawer: boolean;
  showTopBarMenu: boolean;
  showUnsavedChanges: boolean;
  showUnscheduledJobsSidebar: boolean;
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  stageFilter: StageForFilter[];
  tableContents: 'Resources' | 'Teams';
  teamUnavailabilityMode: boolean;
  ticketFilter: { label: string; value: string }[];
  toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    resourceId?: string;
    position: {
      top: number;
      right: number;
      arrowTop: number;
    };
  };
  unsavedChanges: UnsavedChange[];
  unsavedChangesWithoutReason: Omit<UnsavedChange, 'reason'>[];
};

export const useProjectStore = create<ProjectStore>((set) => ({
  actions: {
    setAssignedResourceFilter: (assignedResourceFilter) => set({ assignedResourceFilter }),
    setComparedProject: (project) => set({ comparedProject: project }),
    setDayTeamPickerData: (dayTeamPickerData) => set({ dayTeamPickerData }),
    setDroppableStartTime: (time) => set({ droppableStartTime: time }),
    setFocusedProject: (project) => set({ focusedProject: project }),
    setHighlightFlexibleProjects: (highlight) => set({ highlightFlexibleProjects: highlight }),
    setHighlightProjectsWithinDistance: (distance) => set({ highlightProjectsWithinDistance: distance }),
    setHoveredProject: (project) => set({ hoveredProject: project }),
    setHoveredUnsavedChangeJobId: (jobId) => set({ hoveredUnsavedChangeJobId: jobId }),
    setIncompleteProjectId: (projectId) => set({ incompleteProjectId: projectId }),
    setProjectForIncompleteHoursModal: (project) => set({ projectForIncompleteHoursModal: project }),
    setJobBeingDragged: (job) => set({ jobBeingDragged: job }),
    setJobBeingEdited: (job) => set({ jobBeingEdited: job }),
    setOverlappingSegmentsForResources: (overlappingSegmentsForResources) => set({ overlappingSegmentsForResources }),
    setSelectedProject: (project) => set({ selectedProject: project }),
    setShowRemoveAssignedResourcesModal: (job) => {
      if (!job) {
        set({ showRemoveAssignedResourcesModal: null });
        return;
      }
      set({ showRemoveAssignedResourcesModal: job });
    },
    setStageFilter: (stageFilter) => set({ stageFilter }),
    setToolTipProject: (project) =>
      set((state) => {
        if (!project || isEqual(state.toolTipProject, project)) {
          return {
            toolTipProject: undefined,
            selectedProject: undefined,
            showRightSidebar: false,
            jobBeingEdited: undefined,
          };
        }
        return { toolTipProject: project };
      }),
    setDispatchMode: (dispatchMode) => set({ dispatchMode }),
    setJobBeingSplit: (job) => set({ jobBeingSplit: job }),
    setJobsToDispatch: (jobs: Record<string, FullInstallationProjectEntity>) => set({ jobsToDispatch: jobs }),
    setProjectForNoBaselineHoursModal: (project) => set({ projectForNoBaselineHoursModal: project }),
    setRightClickMenu: (rightClickMenu) => set({ rightClickMenu }),
    setSelectedResourceHalfDay: (selectedResourceHalfDay) => set({ selectedResourceHalfDay }),
    setSeparateHoursToBeAdded: (job) => set({ separateHoursToBeAdded: job }),
    setShowAssignTeamLeadModal: (project) => {
      if (!project) {
        set({ showAssignTeamLeadModal: null });
        return;
      }
      set({ showAssignTeamLeadModal: project });
    },
    setShowCompactView: (showCompactView) => set({ showCompactView }),
    setShowEditTeamsModal: (showEditTeamsModal) => set({ showEditTeamsModal }),
    setShowFiltersTab: (showFiltersTab) => set({ showFiltersTab }),
    setShowNewBaselines: (showNewBaselines) => set({ showNewBaselines }),
    setShowRemoveResourceInactivityModal: (showRemoveResourceInactivityModal) =>
      set({ showRemoveResourceInactivityModal }),
    setShowRightSidebar: (showRightSidebar) => set({ showRightSidebar }),
    setShowInstallationsMapModal: (showInstallationsMapModal) => set({ showInstallationsMapModal }),
    setShowStatisticsDrawer: (showStatisticsDrawer) => set({ showStatisticsDrawer }),
    setShowTopBarMenu: (showTopBarMenu) => set({ showTopBarMenu }),
    setShowUnsavedChanges: (showUnsavedChanges) => set({ showUnsavedChanges }),
    setShowUnscheduledJobsSidebar: (showUnscheduledJobsSidebar) => set({ showUnscheduledJobsSidebar }),
    setSplitSegmentsToMove: (segments) => set({ splitSegmentsToMove: segments }),
    setTableContents: (tableContents) => set({ tableContents }),
    setTeamUnavailabilityMode: (teamUnavailabilityMode) => set({ teamUnavailabilityMode }),
    setTicketFilter: (ticketFilter) => set({ ticketFilter }),
    setUnsavedChanges: (changes) => {
      set((state) => {
        // Check if there are any changes outside of the reason field
        const shouldUpdateWithoutReason = hasNonReasonChanges(state.unsavedChangesWithoutReason, changes);

        return {
          unsavedChanges: changes,
          unsavedChangesWithoutReason: shouldUpdateWithoutReason
            ? changes.map(({ reason: _reason, ...rest }) => rest)
            : state.unsavedChangesWithoutReason,
        };
      });
    },
    updateUnsavedChanges: (change) =>
      set((state) => {
        let newUnsavedChanges = state.unsavedChanges.filter((unsavedChange) => unsavedChange.jobId !== change.jobId);
        newUnsavedChanges = [...newUnsavedChanges, change];
        const shouldUpdateWithoutReason = hasNonReasonChanges(state.unsavedChangesWithoutReason, newUnsavedChanges);

        return {
          unsavedChanges: newUnsavedChanges,
          unsavedChangesWithoutReason: shouldUpdateWithoutReason
            ? newUnsavedChanges.map(({ reason: _reason, ...rest }) => rest)
            : state.unsavedChangesWithoutReason,
        };
      }),
  },
  assignedResourceFilter: [],
  comparedProject: undefined,
  dayTeamPickerData: undefined,
  dispatchMode: false,
  droppableStartTime: null,
  focusedProject: undefined,
  highlightFlexibleProjects: false,
  highlightProjectsWithinDistance: undefined,
  hoveredProject: undefined,
  hoveredUnsavedChangeJobId: null,
  incompleteProjectId: null,

  jobBeingDragged: undefined,
  jobBeingEdited: undefined,
  jobBeingSplit: undefined,
  jobsToDispatch: {} as Record<string, FullInstallationProjectEntity>,
  overlappingSegmentsForResources: new Map(),
  projectForNoBaselineHoursModal: null,
  projectForIncompleteHoursModal: null,
  rightClickMenu: null,
  selectedProject: undefined,
  selectedResourceHalfDay: null,
  separateHoursToBeAdded: undefined,
  showAssignTeamLeadModal: null,
  showCompactView: false,
  showEditTeamsModal: false,
  showFiltersTab: false,
  showNewBaselines: false,
  showStatisticsDrawer: false,
  showRemoveAssignedResourcesModal: null,
  showRemoveResourceInactivityModal: null,
  showRightSidebar: false,
  showInstallationsMapModal: false,
  showTopBarMenu: false,
  showUnsavedChanges: false,
  showUnscheduledJobsSidebar: true,
  splitSegmentsToMove: null,
  stageFilter: [],
  tableContents: 'Teams',
  teamUnavailabilityMode: false,
  ticketFilter: [],
  toolTipProject: undefined,
  unsavedChanges: [],
  unsavedChangesWithoutReason: [],
}));

export const useHighlightProjectsWithinDistance = () =>
  useProjectStore((state) => state.highlightProjectsWithinDistance);
export const useHighlightFlexibleProjects = () => useProjectStore((state) => state.highlightFlexibleProjects);
export const useSelectedProject = () => useProjectStore((state) => state.selectedProject);
export const useToolTipProject = () => useProjectStore((state) => state.toolTipProject);
export const useHoveredProject = () => useProjectStore((state) => state.hoveredProject);
export const useFocusedProject = () => useProjectStore((state) => state.focusedProject);
export const useJobBeingEdited = () => useProjectStore((state) => state.jobBeingEdited);
export const useComparedProject = () => useProjectStore((state) => state.comparedProject);
export const useJobBeingDragged = () => useProjectStore((state) => state.jobBeingDragged);
export const useDroppableStartTime = () => useProjectStore((state) => state.droppableStartTime);
export const useStageFilter = () => useProjectStore((state) => state.stageFilter);
export const useOverlappingSegmentsForResources = () =>
  useProjectStore((state) => state.overlappingSegmentsForResources);
export const useDayTeamPickerData = () => useProjectStore((state) => state.dayTeamPickerData);
export const useHoveredUnsavedChangeJobId = () => useProjectStore((state) => state.hoveredUnsavedChangeJobId);
export const useProjectActions = () => useProjectStore((state) => state.actions);
export const useAssignedResourceFilter = () => useProjectStore((state) => state.assignedResourceFilter);
export const useUnsavedChanges = () => useProjectStore((state) => state.unsavedChanges);
export const useUnsavedChangesWithoutReason = () => useProjectStore((state) => state.unsavedChangesWithoutReason);
export const useShowCompactView = () => useProjectStore((state) => state.showCompactView);
export const useTableContents = () => useProjectStore((state) => state.tableContents);
export const useShowRightSidebar = () => useProjectStore((state) => state.showRightSidebar);
export const useShowUnsavedChanges = () => useProjectStore((state) => state.showUnsavedChanges);
export const useShowRemoveAssignedResourcesModal = () =>
  useProjectStore((state) => state.showRemoveAssignedResourcesModal);
export const useShowAssignTeamLeadModal = () => useProjectStore((state) => state.showAssignTeamLeadModal);
export const useShowInstallationsMapModal = () => useProjectStore((state) => state.showInstallationsMapModal);
export const useShowUnscheduledJobsSidebar = () => useProjectStore((state) => state.showUnscheduledJobsSidebar);
export const useSeparateHoursToBeAdded = () => useProjectStore((state) => state.separateHoursToBeAdded);
export const useRightClickMenu = () => useProjectStore((state) => state.rightClickMenu);
export const useShowFiltersTab = () => useProjectStore((state) => state.showFiltersTab);
export const useJobBeingSplit = () => useProjectStore((state) => state.jobBeingSplit);
export const useSplitSegmentsToMove = () => useProjectStore((state) => state.splitSegmentsToMove);
export const useShowEditTeamsModal = () => useProjectStore((state) => state.showEditTeamsModal);
export const useProjectForNoBaselineHoursModal = () => useProjectStore((state) => state.projectForNoBaselineHoursModal);
export const useShowStatisticsDrawer = () => useProjectStore((state) => state.showStatisticsDrawer);
export const useShowNewBaselines = () => useProjectStore((state) => state.showNewBaselines);
export const useShowTopBarMenu = () => useProjectStore((state) => state.showTopBarMenu);
export const useDispatchMode = () => useProjectStore((state) => state.dispatchMode);
export const useJobsToDispatch = () => useProjectStore((state) => state.jobsToDispatch);
export const useSelectedResourceHalfDay = () => useProjectStore((state) => state.selectedResourceHalfDay);
export const useShowRemoveResourceInactivityModal = () =>
  useProjectStore((state) => state.showRemoveResourceInactivityModal);
export const useTeamUnavailabilityMode = () => useProjectStore((state) => state.teamUnavailabilityMode);
export const useTicketFilter = () => useProjectStore((state) => state.ticketFilter);
export const useIncompleteProjectId = () => useProjectStore((state) => state.incompleteProjectId);
export const useProjectForIncompleteHoursModal = () => useProjectStore((state) => state.projectForIncompleteHoursModal);
