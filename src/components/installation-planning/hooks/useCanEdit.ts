import { Group } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useMemo } from 'react';
import { api } from 'utils/api';
import { useIsProd } from './useIsProd';

const GROUPS_WITH_EDIT_ACCESS = [
  Group.GROUP_AIRA_ALL_PLANNING_MANAGER,
  Group.GROUP_AIRA_ALL_CLEAN_ENERGY_PROJECT_PLANNER,
  Group.GROUP_AIRA_ALL_TEAM_LEAD_CLEAN_ENERGY_PROJECT_PLANNER,
  Group.GROUP_PLANNING_TOOL_EDIT,
];

export const useCanEdit = () => {
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const isProd = useIsProd();

  return useMemo(
    () => Boolean(!isProd || user?.groupMemberships?.some(({ group }) => GROUPS_WITH_EDIT_ACCESS.includes(group))),
    [user, isProd],
  );
};
