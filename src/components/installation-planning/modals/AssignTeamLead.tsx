import { useProjectActions } from '../stores/ProjectStore';
import { Stack, Typography } from '@mui/material';
import { api } from 'utils/api';
import { Autocomplete } from '@ui/components/Autocomplete/Autocomplete';
import { useResources } from '../contexts/ResourcesContext';
import React, { useMemo } from 'react';
import { formatUserIdentityToDropdownOptions } from 'utils/helpers';
import { Button } from '@ui/components/Button/Button';
import {
  Group,
  UserIdentity,
} from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import toast from 'react-hot-toast';

const TEAM_LEAD_GROUPS = [Group.GROUP_AIRA_ALL_TEAM_LEAD_INSTALLATION, Group.GROUP_ASSIGNED_TEAM_LEAD_INSTALLER];

const getDropdownOptions = (resourceInfo: UserIdentity[] | undefined) => {
  const teamLeadResources = resourceInfo?.filter((resource) =>
    resource?.groupMemberships.some((gm) => TEAM_LEAD_GROUPS.includes(gm.group)),
  );

  return formatUserIdentityToDropdownOptions(teamLeadResources);
};

export default function AssignTeamLead({
  projectId,
  teamLeadId,
}: {
  projectId: string;
  teamLeadId: string | undefined;
}) {
  const resourcesForRegion = useResources();
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const { setShowAssignTeamLeadModal: setShowAssignTeamLeadModal } = useProjectActions();
  const { mutateAsync: assignTeamLead } = api.InstallationProject.assignTeamLead.useMutation();
  const [selectedResourceId, setSelectedResourceId] = React.useState<string | null>(teamLeadId ?? null);

  const resourceInfo = useMemo(() => Array.from(resourcesForRegion.values()), [resourcesForRegion]);

  const handleAssignTeamLead = async () => {
    if (!selectedResourceId || selectedResourceId === teamLeadId) {
      setShowAssignTeamLeadModal(null);
      return;
    }
    const response = await assignTeamLead({
      projectId: projectId,
      userId: selectedResourceId,
    });
    setShowAssignTeamLeadModal(null);

    if (response.installationProject) {
      const result = await refetchProjectsUpdatedSince();
      if (result.status === 'success') {
        toast.success('Team lead assigned to job');
      }
    } else {
      toast.error('Failed to assign team lead to job');
    }
  };

  const dropdownOptions = getDropdownOptions(resourceInfo);

  return (
    <Stack spacing={2} p={2}>
      <Typography variant="headline4">Assign team lead</Typography>
      <Stack spacing={1}>
        <Autocomplete
          disableTyping={false}
          placeholder="Select person"
          name="teamLead"
          disableClearable
          defaultValue={dropdownOptions.find((option) => option.value === selectedResourceId) ?? null}
          options={dropdownOptions}
          error={false}
          onChange={(e) => {
            setSelectedResourceId(e?.value ?? null);
          }}
          fullWidth
        />
        <Button variant="contained" onClick={handleAssignTeamLead} size="small">
          Save
        </Button>
      </Stack>
    </Stack>
  );
}
