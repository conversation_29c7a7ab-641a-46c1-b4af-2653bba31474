import { Box, IconButton, MenuItem, SelectChangeEvent, Stack, Typography } from '@mui/material';
import DatePicker from 'react-datepicker';
import { useMemo, useState } from 'react';
import { Button } from '@ui/components/Button/Button';
import { ClockOutlinedIcon } from '@ui/components/StandardIcons/ClockOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { eachDayOfInterval, getHours, isBefore, isWeekend, setHours, startOfDay } from 'date-fns';
import { tz, TZDate } from '@date-fns/tz';
import { grey } from '@ui/theme/colors';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { useRegionContext } from '../../../context/RegionContext';
import { calculateNewEndDate, formatDate } from '../helpers/dateHelpers';
import { ContinuousWorkSegment } from '../types/planningTypes';
import { useProjectActions } from '../stores/ProjectStore';
import calculateNewWorkSegments from '../helpers/calculateNewWorkSegments';
import DatePickerCustomHeader from '../components/DatePickerHeader';
import { usePublicHolidays } from '../contexts/PublicHolidaysContext';

export default function JobEdit({ jobBeingEdited }: { jobBeingEdited: ContinuousWorkSegment }) {
  const region = useRegionContext();
  const { holidays } = usePublicHolidays();
  const { timeZone } = region;
  const now = useMemo(() => TZDate.tz(timeZone), [timeZone]); // Memoize 'now' to prevent re-renders

  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const { setJobBeingEdited, updateUnsavedChanges } = useProjectActions();
  const [startDate, setStartDate] = useState(jobBeingEdited?.segmentDetails.startTime ?? new Date());
  const [startTime, setStartTime] = useState(
    getHours(jobBeingEdited?.segmentDetails.startTime, { in: tz(timeZone) })
      .toString()
      .padStart(2, '0'),
  );
  const [endDate, setEndDate] = useState(jobBeingEdited?.segmentDetails.endTime ?? new Date());

  const [jobDuration, setJobDuration] = useState<number>(jobBeingEdited.segmentDetails.duration);

  const customerName = `${jobBeingEdited.contact?.firstName} ${jobBeingEdited.contact?.lastName}`;

  const timeOptions = [
    { value: '08', label: '08:00' },
    { value: '12', label: '12:00' },
    { value: '09', label: '09:00' },
    { value: '10', label: '10:00' },
    { value: '11', label: '11:00' },
    { value: '13', label: '13:00' },
    { value: '14', label: '14:00' },
    { value: '15', label: '15:00' },
  ];

  const handleSelect = (start: Date | null) => {
    if (start) {
      // Handle past-dated start times
      if (isBefore(start, startOfDay(now))) {
        toast.error('The job cannot start in the past', { position: 'top-center', duration: 6000 });
        return;
      }
      const newStartDate = setHours(start, parseInt(startTime, 10), { in: tz(timeZone) });
      const newEndDate = calculateNewEndDate({
        startTime: newStartDate,
        durationInHours: jobBeingEdited.segmentDetails.duration,
        timeZone,
        holidays,
      });
      setStartDate(newStartDate);
      setEndDate(newEndDate);
    }
  };

  const handleStartTimeChange = (e: SelectChangeEvent<string>) => {
    const newStartTime = e.target.value;
    setStartTime(newStartTime);
    const newStartDate = setHours(startDate, parseInt(newStartTime, 10), { in: tz(timeZone) });
    const newEndDate = calculateNewEndDate({
      startTime: newStartDate,
      durationInHours: jobDuration,
      timeZone,
      holidays,
    });
    setEndDate(newEndDate);
  };

  const selectedDates = () => {
    const dates = eachDayOfInterval({ start: startDate, end: endDate });
    // Filter out the weekends
    // Return an array of weekdays
    return dates.filter((date) => !isWeekend(date, { in: tz(timeZone) }));
  };

  const handleSetDuration = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.value) {
      setJobDuration(0);
      return;
    }
    const duration = parseInt(e.target.value, 10);
    setJobDuration(duration);
    const newEndDate = calculateNewEndDate({
      startTime: startDate,
      durationInHours: duration,
      timeZone,
      holidays,
    });
    setEndDate(newEndDate);
  };

  const handleConfirmClicked = () => {
    const newWorkSegments = calculateNewWorkSegments({
      startDate,
      jobDuration,
      jobBeingEdited,
      timeZone,
      holidays,
    });

    if (!newWorkSegments) return;

    updateUnsavedChanges({
      projectId: jobBeingEdited.installationProject!.id!.value,
      jobId: jobBeingEdited.segmentDetails.jobId,
      customerName: `${jobBeingEdited.contact?.firstName} ${jobBeingEdited.contact?.lastName}`,
      requiredRole: jobBeingEdited.segmentDetails.requiredRole,
      newStartTime: startDate,
      previousStartTime: jobBeingEdited.segmentDetails.startTime,
      workSegments: newWorkSegments,
      userWhoMadeChange: `${user?.firstName} ${user?.lastName}`,
    });
    setJobBeingEdited();
  };

  return (
    <Stack width="373px" sx={{ position: 'relative', p: '48px' }}>
      <IconButton
        onClick={() => setJobBeingEdited()}
        sx={{ padding: 0, position: 'absolute', right: '24px', top: '24px' }}
      >
        <CrossOutlinedIcon height={24} width={24} />
      </IconButton>

      <Stack direction="row" gap={1} py={1}>
        <Typography variant="body1Emphasis">{customerName}</Typography>
      </Stack>
      <Stack direction="row" gap={1} pb={4}>
        <Typography variant="headline3">Change start date & time</Typography>
      </Stack>
      <Stack py={1} gap={1}>
        <TextField
          name="duration"
          size="small"
          label="Duration (hours)"
          sx={{
            border: `1px solid ${grey[300]}`,
            mb: 1,
          }}
          value={jobDuration}
          onChange={handleSetDuration}
          icon={<ClockOutlinedIcon height={16} width={16} />}
        />
        <Select
          label="Start time"
          name="start time"
          value={startTime}
          size="small"
          renderValue={(value) => {
            const selectedOption = timeOptions.find((option) => option.value === value);
            return `${formatDate(startDate, timeZone).split(',')[0]} ${selectedOption?.label}`;
          }}
          onChange={handleStartTimeChange}
          MenuProps={{
            PaperProps: {
              style: {
                zIndex: 3500, // higher than the parent component's z-index
              },
            },
            disablePortal: true, // Ensures the menu renders inside the DOM hierarchy
          }}
          sx={{
            mb: 1,
          }}
        >
          {timeOptions.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </Select>

        <Box className="date-picker-container">
          <DatePicker
            selected={startDate}
            inline
            onChange={handleSelect}
            highlightDates={selectedDates()}
            renderCustomHeader={DatePickerCustomHeader}
          />
        </Box>
      </Stack>
      <Stack pb={3}>
        <TextField
          name="end time"
          size="small"
          label="End date/time"
          sx={{
            border: `1px solid ${grey[300]}`,
            mb: 1,
          }}
          value={formatDate(endDate, timeZone)}
          onChange={() => {}}
          disabled
        />
      </Stack>
      <Button
        variant="contained"
        onClick={handleConfirmClicked}
        title="This change will then show up in unsaved changes"
        disabled={jobDuration === 0}
      >
        Confirm
      </Button>
    </Stack>
  );
}
