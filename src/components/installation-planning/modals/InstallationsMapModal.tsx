import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useShowInstallationsMapModal } from '../stores/ProjectStore';
import InstallationsMapContainer from './InstallationsMapContainer';

export default function InstallationsMapModal() {
  const showInstallationsMapModal = useShowInstallationsMapModal();
  const { setShowInstallationsMapModal } = useProjectActions();

  if (!showInstallationsMapModal) {
    return null;
  }

  return (
    <Modal
      isModalOpen={showInstallationsMapModal}
      handleClose={() => {
        setShowInstallationsMapModal(false);
      }}
      height="95dvh"
      width="95dvw"
      sx={{ padding: '0px' }}
    >
      <InstallationsMapContainer />
    </Modal>
  );
}
