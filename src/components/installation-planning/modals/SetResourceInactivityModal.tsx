import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useSelectedResourceHalfDay } from '../stores/ProjectStore';
import SetResourceInactivity from './SetResourceInactivity';

export default function ResourceInactivityModal() {
  const { setSelectedResourceHalfDay } = useProjectActions();
  const selectedResourceHalfDay = useSelectedResourceHalfDay();

  if (!selectedResourceHalfDay) return null;
  return (
    <Modal
      isModalOpen={selectedResourceHalfDay !== null}
      handleClose={() => setSelectedResourceHalfDay(null)}
      width="463px"
      height="fit-content"
      sx={{
        backgroundColor: '#fff',
      }}
    >
      <SetResourceInactivity />
    </Modal>
  );
}
