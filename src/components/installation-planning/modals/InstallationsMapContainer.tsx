import dynamic from 'next/dynamic';
import { useEffect, useMemo, useState } from 'react';
import { useRegionContext } from 'context/RegionContext';
import getRegionAddress from '../helpers/getRegionAddress';
import InstallationsSidebar from '../../maps/installationsMap/InstallationsSidebar';
import { grey } from '@ui/theme/colors';
import { Box, IconButton, MenuItem, Stack } from '@mui/material';
import ButtonSelect from '@ui/components/ButtonSelect/ButtonSelect';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { add, endOfDay, format, addWeeks, startOfWeek, isWithinInterval, getWeek, isAfter } from 'date-fns';
import { tz } from '@date-fns/tz';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useInstallationsMapStoreActions } from 'components/maps/installationsMap/InstallationsMapStore';
import { useAllProjects } from '../contexts/AllProjects';
import {
  InstallationProjectJob_JobType,
  InstallationProjectStage,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { useProjectActions } from '../stores/ProjectStore';
import StageSelector from 'components/maps/installationsMap/StageSelector';
import { StageForFilter } from '../types/planningTypes';

const InstallationsMap = dynamic(() => import('../../maps/installationsMap/InstallationsMap'), { ssr: false });

export interface RangeOption {
  id: string;
  from: Date | null;
  to: Date | null;
}

const rangeToLabel = (dateOption: RangeOption) => {
  if (dateOption.from === null || dateOption.to === null) {
    return 'All';
  }
  const formattedFrom = format(dateOption.from, 'd MMM');
  const formattedTo = format(dateOption.to, 'd MMM');
  return `${formattedFrom} - ${formattedTo}`;
};

export default function InstallationsMapContainer() {
  const [isClient, setIsClient] = useState(false);
  const region = useRegionContext();
  const hubAddress = getRegionAddress(region.id!.value);
  const hubGeometry = hubAddress?.geometry;
  const { setShowInstallationsMapModal } = useProjectActions();
  const { setSelectedProject } = useInstallationsMapStoreActions();
  const { allProjectsBeforeFiltersAndUnsavedChangesApplied } = useAllProjects();
  const [showVisitsWithNoDate, setShowVisitsWithNoDate] = useState(false);
  const startOfThisWeek = useMemo(
    () => startOfWeek(Date.now(), { weekStartsOn: 0, in: tz(region.timeZone) }),
    [region.timeZone],
  );
  const getRangeOptions = (timeZone: string, nrOfWeeks: number = 3): RangeOption[] => {
    const currentStartOfWeek = startOfWeek(Date.now(), { weekStartsOn: 1, in: tz(timeZone) });

    const rangeOptions = [];
    rangeOptions.push({
      id: 'All',
      from: null,
      to: null,
    });
    for (let i = 0; i < nrOfWeeks + 1; i += 1) {
      const from = addWeeks(currentStartOfWeek, i);
      const to = endOfDay(add(from, { days: 4 }), { in: tz(timeZone) });
      const weekNumber = getWeek(from, { weekStartsOn: 1, in: tz(timeZone) });
      rangeOptions.push({
        id: `${weekNumber.toString()} | ${format(from, 'dd MMM')} - ${format(to, 'dd MMM')}`,
        from: from,
        to: to,
      });
    }

    return rangeOptions;
  };

  const rangeOptions = useMemo(() => getRangeOptions(region.timeZone, 5), [region.timeZone]);
  const [selectedRange, setSelectedRange] = useState<RangeOption>(rangeOptions[1]!);
  const [selectedStages, setSelectedStages] = useState<StageForFilter[]>([]);

  const hubPosition = useMemo(() => {
    return { lat: hubGeometry?.lat ?? 0, long: hubGeometry?.long ?? 0 };
  }, [hubGeometry]);

  const allProjects = useMemo(() => {
    if (selectedStages.length === 0) {
      return allProjectsBeforeFiltersAndUnsavedChangesApplied;
    }
    return allProjectsBeforeFiltersAndUnsavedChangesApplied.filter((project) => {
      return selectedStages.some((stage) => stage.value === project.installationProject?.stage);
    });
  }, [allProjectsBeforeFiltersAndUnsavedChangesApplied, selectedStages]);

  const projectsWithNoDate = useMemo(() => {
    return allProjects.filter((project) => {
      if (
        project.installationProject?.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED ||
        project.installationProject?.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE
      ) {
        return false;
      }
      const installationJob = project.installationProject?.jobs.find(
        (job) => job.type === InstallationProjectJob_JobType.JOB_TYPE_PLUMBING,
      );
      if (!installationJob) return true;
      const startDate = installationJob?.workSegments[0]?.startTime;
      if (!startDate) {
        return true;
      }
      return false;
    });
  }, [allProjects]);

  const projectsSortedByStartWeek = useMemo(() => {
    const map = new Map<string, FullInstallationProjectEntity[]>(
      rangeOptions.map((range) => {
        if (range.id === 'All' || range.from === null || range.to === null) {
          const projects = allProjects.filter((project) => {
            const startDate = project.installationProject?.jobs?.find(
              (job) => job.type === InstallationProjectJob_JobType.JOB_TYPE_PLUMBING,
            )?.workSegments[0]?.startTime;
            if (startDate && isAfter(startDate, startOfThisWeek)) {
              return true;
            }
            return false;
          });
          return [`All`, projects];
        }

        const projects = allProjects.filter((project) => {
          const installationJob = project.installationProject?.jobs.find(
            (job) => job.type === InstallationProjectJob_JobType.JOB_TYPE_PLUMBING,
          );
          if (!installationJob) return false;
          const startDate = installationJob.workSegments[0]?.startTime;
          if (!startDate) {
            return false;
          }

          return isWithinInterval(startDate, {
            start: range.from!,
            end: range.to!,
          });
        });
        const weekNumber = getWeek(range.from, { weekStartsOn: 1, in: tz(region.timeZone) });
        const startDate = range.from;
        return [`${weekNumber.toString()} | ${format(startDate, 'dd MMM')} - ${format(range.to, 'dd MMM')}`, projects];
      }),
    );
    return map;
  }, [allProjects, rangeOptions, region.timeZone, startOfThisWeek]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const projectsToShow = useMemo(() => {
    const projects = [];
    if (showVisitsWithNoDate) {
      projects.push(...projectsWithNoDate);
    }
    projects.push(...projectsSortedByStartWeek.get(selectedRange.id)!);
    return projects;
  }, [projectsSortedByStartWeek, selectedRange.id, showVisitsWithNoDate, projectsWithNoDate]);

  if (!projectsToShow) {
    return null;
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        position: 'relative',
        padding: '0px',
      }}
    >
      <Stack
        direction="row"
        sx={{
          position: 'absolute',
          top: 30,
          right: 30,
          zIndex: 1000,
          gap: 2,
          justifyContent: 'flex-end',
          alignItems: 'center',
        }}
      >
        <StageSelector stageFilter={selectedStages} setStageFilter={setSelectedStages} />

        <Box
          sx={{
            width: '100%',
          }}
        >
          <ButtonSelect
            selectedValue={selectedRange.id}
            onChange={(e) => {
              const newDate = rangeOptions.find((range) => range.id === e.target.value);
              if (newDate) {
                setSelectedRange(newDate);
                setSelectedProject(null);
              }
            }}
            name="selectedRange"
            options={rangeOptions.map((range) => (
              <MenuItem key={range.id} value={range.id}>
                {rangeToLabel(range)}
              </MenuItem>
            ))}
            icon={CalendarOutlinedIcon}
            backgroundColor={grey[100]}
            backgroundColorHover={grey[150]}
            boxShadow
          />
        </Box>
        <IconButton
          onClick={() => {
            setShowInstallationsMapModal(false);
          }}
          sx={{
            backgroundColor: grey[100],
            boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
            '&:hover': {
              backgroundColor: grey[150],
            },
          }}
        >
          <CrossOutlinedIcon />
        </IconButton>
      </Stack>
      <InstallationsSidebar
        showVisitsWithNoDate={showVisitsWithNoDate}
        setShowVisitsWithNoDate={setShowVisitsWithNoDate}
        projectsSortedByStartWeek={projectsSortedByStartWeek}
        selectedRange={selectedRange}
      />
      {isClient && <InstallationsMap position={hubPosition} projects={projectsToShow} />}
    </div>
  );
}
