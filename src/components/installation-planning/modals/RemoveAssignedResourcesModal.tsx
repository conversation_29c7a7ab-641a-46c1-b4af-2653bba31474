import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useShowRemoveAssignedResourcesModal } from '../stores/ProjectStore';
import RemoveAssignedResources from './RemoveAssignedResources';

export default function RemoveAssignedResourcesModal() {
  const showRemoveAssignedResourcesModal = useShowRemoveAssignedResourcesModal();
  const { setShowRemoveAssignedResourcesModal } = useProjectActions();

  if (!showRemoveAssignedResourcesModal) return null;

  const { jobId, continuousWorkSegment } = showRemoveAssignedResourcesModal;
  return (
    <Modal
      isModalOpen
      handleClose={() => setShowRemoveAssignedResourcesModal(null)}
      height="fit-content"
      width="fit-content"
      sx={{
        boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
      }}
    >
      <RemoveAssignedResources jobId={jobId} continuousWorkSegment={continuousWorkSegment} />
    </Modal>
  );
}
