import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useJobBeingEdited } from '../stores/ProjectStore';
import JobEdit from './JobEdit';

export default function JobEditModal() {
  const { setJobBeingEdited } = useProjectActions();
  const jobBeingEdited = useJobBeingEdited();

  return (
    <Modal
      isModalOpen={!!jobBeingEdited}
      handleClose={() => setJobBeingEdited()}
      width="auto"
      height="auto"
      sx={{
        background: 'white',
        p: 0,
      }}
    >
      {jobBeingEdited && <JobEdit jobBeingEdited={jobBeingEdited} />}
    </Modal>
  );
}
