import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useProjectForIncompleteHoursModal } from '../stores/ProjectStore';
import IncompleteHours from './IncompleteHours';

export default function IncompleteHoursModal() {
  const projectForIncompleteHoursModal = useProjectForIncompleteHoursModal();
  const { setProjectForIncompleteHoursModal } = useProjectActions();

  return (
    <Modal
      isModalOpen={!!projectForIncompleteHoursModal}
      handleClose={() => setProjectForIncompleteHoursModal(null)}
      width="auto"
      height="auto"
    >
      {!!projectForIncompleteHoursModal && <IncompleteHours />}
    </Modal>
  );
}
