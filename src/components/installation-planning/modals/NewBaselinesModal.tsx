import { Typo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from '@mui/material';
import { AiraCloseIcon } from '@ui/components/Icons/AiraCloseIcon/AiraCloseIcon';
import { HousePersonOutsideIcon } from '@ui/components/StandardIcons/HousePersonOutsideIcon';
import { InstallationProjectJob_JobType } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/job';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { useAllProjects } from '../contexts/AllProjects';
import { useShowNewBaselines, useProjectActions } from '../stores/ProjectStore';
import { reverseResourceMapping } from '../types/planningTypes';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import {
  InstallationProjectJob,
  InstallationProjectJob_JobStatus,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useRegionContext } from '../../../context/RegionContext';
import calculateNewWorkSegmentsWhenApplyingBaseline from '../helpers/calculateNewWorkSegmentsWhenApplyingBaseline';
import { usePublicHolidays } from '../contexts/PublicHolidaysContext';

export default function NewBaselinesModal() {
  const { setShowNewBaselines, setHoveredUnsavedChangeJobId } = useProjectActions();
  const showNewBaselines = useShowNewBaselines();
  const { timeZone } = useRegionContext();
  const { projectsWithUnappliedBaseline } = useAllProjects();
  const { holidays } = usePublicHolidays();
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const { mutateAsync: ignoreDurationEstimates } = api.InstallationProject.ignoreDurationEstimates.useMutation();
  const { mutateAsync: updateWorkSegments } =
    api.InstallationProject.updateInstallationProjectJobWorkSegmentsFromNewManHours.useMutation();

  const handleDiscard = async ({
    projectId,
    jobType,
  }: {
    projectId?: string;
    jobType?: InstallationProjectJob_JobType;
  }) => {
    try {
      if (!projectId || !jobType) {
        throw new Error('Project ID and job type are required');
      }
      await ignoreDurationEstimates({ projectId, jobType });
      toast.success('Duration estimate discarded');
      await refetchProjectsUpdatedSince();
    } catch (_error) {
      toast.error('Failed to discard duration estimate');
    }
  };

  const handleApply = async ({
    projectId,
    job,
    newManHours,
  }: {
    projectId?: string;
    job?: InstallationProjectJob;
    newManHours?: number;
  }) => {
    try {
      if (!projectId || !job || !newManHours) {
        throw new Error('Project ID and job and new man hours are required');
      }

      const newWorkSegments = calculateNewWorkSegmentsWhenApplyingBaseline({
        newManHours,
        job,
        timeZone,
        holidays,
      });

      if (newWorkSegments.length === 0) {
        throw new Error('No work segments to update');
      }
      await updateWorkSegments({
        jobId: job.id!.value,
        workSegments: newWorkSegments,
      });

      toast.success('Duration estimate applied');
      await refetchProjectsUpdatedSince();
    } catch (_error) {
      toast.error('Failed to apply duration estimate');
    }
  };

  if (!showNewBaselines) {
    return null;
  }
  return (
    <Stack
      gap="32px"
      sx={{
        height: '90vh',
        width: '370px',
        overflow: 'hidden',
        position: 'fixed',
        background: 'white',
        boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        top: 80,
        right: '20px',
        zIndex: 5000,
        borderRadius: '22px',
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        sx={{
          zIndex: 1000,
          padding: '44px 44px 0px 44px',
        }}
      >
        <Typography variant="headline4">
          <FormattedMessage id="installationPlanning.newBaselinesModal.title" defaultMessage="New Baselines" />
          {` (${projectsWithUnappliedBaseline.length})`}
        </Typography>
        <IconButton onClick={() => setShowNewBaselines(false)}>
          <AiraCloseIcon height={16} width={16} />
        </IconButton>
      </Stack>
      <Stack
        sx={{
          overflow: 'auto',
          gap: '32px',
          padding: '0px 44px 44px 44px',
        }}
      >
        {projectsWithUnappliedBaseline.map((project) => (
          <Stack key={project.installationProject?.id?.value} gap="16px">
            <Stack direction="row" justifyContent="flex-start" alignItems="center" gap="8px">
              <HousePersonOutsideIcon height={16} width={16} />
              <Typography variant="body1Emphasis" fontSize="16px">
                {project.contact?.firstName} {project.contact?.lastName}
              </Typography>
            </Stack>
            {project.installationProject?.jobs.map((job) => {
              const durationEstimate = project.installationProject?.jobDurationEstimates.find(
                (estimate) =>
                  estimate.jobType === (job.requiredRole as unknown as InstallationProjectJob_JobType) &&
                  estimate.state?.state?.$case === 'unhandled',
              );
              let currentDuration;

              const jobAssignedResources = job.assignedResources;
              if (jobAssignedResources && jobAssignedResources.length > 0) {
                currentDuration = job.workSegments.reduce((acc, segment) => {
                  const assignedResources = segment.assignedResources;
                  if (assignedResources && assignedResources.length > 0) {
                    return acc + ((segment.duration?.seconds ?? 0) / 3600) * assignedResources.length;
                  }
                  return acc + ((segment.duration?.seconds ?? 0) / 3600) * job.requiredResourcesCount;
                }, 0);
              } else {
                currentDuration = ((job.duration?.seconds ?? 0) / 3600) * job.requiredResourcesCount;
              }
              const estimateEqualsCurrent =
                Math.abs((durationEstimate?.duration?.seconds ?? 0) / 3600 - currentDuration) < 1;

              if (
                durationEstimate &&
                job.status !== InstallationProjectJob_JobStatus.JOB_STATUS_FINISHED &&
                !estimateEqualsCurrent
              ) {
                return (
                  <Stack
                    key={job.id?.value}
                    sx={{
                      backgroundColor: '#22222608',
                      padding: '24px',
                      borderRadius: '16px',
                      gap: '8px',
                    }}
                    onMouseEnter={() => {
                      setHoveredUnsavedChangeJobId(job.id?.value ?? null);
                    }}
                    onMouseLeave={() => {
                      setHoveredUnsavedChangeJobId(null);
                    }}
                  >
                    <Typography variant="body1Emphasis" sx={{ textTransform: 'capitalize' }}>
                      {reverseResourceMapping[job.requiredRole as keyof typeof reverseResourceMapping].toLowerCase()}
                    </Typography>
                    <Stack direction="row" gap="8px" justifyContent="space-between" alignItems="center">
                      <Stack>
                        <Typography variant="body2Emphasis">
                          <FormattedMessage
                            id="installationPlanning.newBaselinesModal.currentDuration"
                            defaultMessage="Current Duration:"
                          />
                        </Typography>
                        <Typography variant="body2">{`${currentDuration} hours`}</Typography>
                      </Stack>
                      <Stack>
                        <Typography variant="body2Emphasis">
                          <FormattedMessage
                            id="installationPlanning.newBaselinesModal.newDuration"
                            defaultMessage="New Duration:"
                          />
                        </Typography>
                        <Typography variant="body2">
                          {durationEstimate?.duration?.seconds
                            ? `${durationEstimate.duration.seconds / 3600} hours`
                            : '-'}
                        </Typography>
                      </Stack>
                    </Stack>
                    <Stack direction="row" gap="8px" justifyContent="space-between" mt="14px" sx={{ width: '100%' }}>
                      <Button
                        variant="outlined"
                        sx={{ width: '46%' }}
                        onClick={() =>
                          handleDiscard({
                            projectId: project.installationProject?.id?.value,
                            jobType: job.requiredRole as unknown as InstallationProjectJob_JobType,
                          })
                        }
                      >
                        <FormattedMessage
                          id="installationPlanning.newBaselinesModal.discard"
                          defaultMessage="Discard"
                        />
                      </Button>
                      <Button
                        variant="contained"
                        sx={{ width: '46%' }}
                        onClick={() =>
                          handleApply({
                            projectId: project.installationProject?.id?.value,
                            job,
                            newManHours: (durationEstimate?.duration?.seconds ?? 0) / 3600,
                          })
                        }
                      >
                        <FormattedMessage id="installationPlanning.newBaselinesModal.apply" defaultMessage="Apply" />
                      </Button>
                    </Stack>
                  </Stack>
                );
              }
              return null;
            })}
          </Stack>
        ))}
      </Stack>
    </Stack>
  );
}
