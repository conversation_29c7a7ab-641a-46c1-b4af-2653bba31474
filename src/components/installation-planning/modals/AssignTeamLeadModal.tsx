import { useProjectActions, useShowAssignTeamLeadModal } from '../stores/ProjectStore';
import React from 'react';
import { Modal } from '@ui/components/Modal/Modal';
import AssignTeamLead from './AssignTeamLead';

export default function AssignTeamLeadModal() {
  const project = useShowAssignTeamLeadModal();
  const { setShowAssignTeamLeadModal: setShowAssignTeamLeadModal } = useProjectActions();

  return (
    <Modal
      isModalOpen={!!project}
      handleClose={() => setShowAssignTeamLeadModal(null)}
      height="fit-content"
      width="400px"
      sx={{
        boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
        overflowY: 'visible',
      }}
    >
      {project && <AssignTeamLead projectId={project.projectId} teamLeadId={project?.teamLeadResourceId} />}
    </Modal>
  );
}
