import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { useProjectActions, useSelectedResourceHalfDay } from '../stores/ProjectStore';
import { useState } from 'react';
import { isAfter } from 'date-fns/isAfter';
import { isSameDay } from 'date-fns/isSameDay';
import { Button } from '@ui/components/Button/Button';
import { useRegionContext } from '../../../context/RegionContext';
import { FormattedMessage, useIntl } from 'react-intl';
import { MenuItem } from '@mui/material';
import { ThermometerOutlinedIcon } from '@ui/components/StandardIcons/ThermometerOutlinedIcon';
import { CalendarRemoveOutlinedIcon } from '@ui/components/StandardIcons/CalendarRemoveOutlinedIcon';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { ClockOutlinedIcon } from '@ui/components/StandardIcons/ClockOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { GlobeOutlinedIcon } from '@ui/components/StandardIcons/GlobeOutlinedIcon';
import { HousePersonOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutlinedIcon';
import { ShieldOutlinedIcon } from '@ui/components/StandardIcons/ShieldOutlinedIcon';
import { Sun1OutlinedIcon } from '@ui/components/StandardIcons/Sun1OutlinedIcon';
import { Select } from '@ui/components/Select/Select';
import { eachDayOfInterval, format, isBefore, isWeekend, setHours } from 'date-fns';
import { tz, TZDate } from '@date-fns/tz';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { useRegions } from '../../../context/RegionsContext';
import { useResourceUnutilization } from '../contexts/ResourceUnutilizationContext';
import { isPublicHoliday, isSameDayFast } from '../helpers/dateHelpers';
import { MessageKey } from 'messageType';
import { usePublicHolidays } from '../contexts/PublicHolidaysContext';
import { de, enGB, it } from 'date-fns/locale';
import { useRouter } from 'next/router';
import { TextField } from '@ui/components/TextField/TextField';
import { DocumentOutlinedIcon } from '@ui/components/StandardIcons/DocumentOutlinedIcon';

const LOCALES = { 'en-gb': enGB, de: de, it: it };

const START_DAY_DURATIONS = [
  { value: 'firstHalf', label: 'First half', defaultMessage: 'First half' },
  { value: 'secondHalf', label: 'Second half', defaultMessage: 'Second half' },
  { value: 'fullDay', label: 'Full day', defaultMessage: 'Full day' },
];

const END_DAY_DURATIONS = [
  { value: 'firstHalf', label: 'First half', defaultMessage: 'First half' },
  { value: 'fullDay', label: 'Full day', defaultMessage: 'Full day' },
];

const REASONS = [
  {
    value: 'noOrder',
    label: 'No order',
    type: 'unutilized',
    icon: CalendarRemoveOutlinedIcon,
    defaultMessage: 'No order',
  },
  {
    value: 'customerAbsence',
    label: 'Customer absence',
    type: 'unutilized',
    icon: HousePersonOutlinedIcon,
    defaultMessage: 'Customer absence',
  },
  { value: 'lentOut', label: 'Lent out', type: 'unutilized', icon: GlobeOutlinedIcon, defaultMessage: 'Lent out' },
  {
    value: 'timeCompensation',
    label: 'Time compensation',
    type: 'unavailable',
    icon: ClockOutlinedIcon,
    defaultMessage: 'Time compensation',
  },
  { value: 'vacation', label: 'Vacation', type: 'unavailable', icon: Sun1OutlinedIcon, defaultMessage: 'Vacation' },
  {
    value: 'sickness',
    label: 'Sickness',
    type: 'unavailable',
    icon: ThermometerOutlinedIcon,
    defaultMessage: 'Sickness',
  },
  { value: 'training', label: 'Training', type: 'unavailable', icon: ShieldOutlinedIcon, defaultMessage: 'Training' },
  { value: 'other', label: 'Other', type: 'unavailable', icon: DocumentOutlinedIcon, defaultMessage: 'Other' },
];

export default function SetResourceInactivity() {
  const router = useRouter();
  const intl = useIntl();
  const { locale } = router;
  const chosenLocale = locale || 'en-GB';
  const region = useRegionContext();
  const { timeZone, id: regionId } = region;
  const { holidays } = usePublicHolidays();
  const { setSelectedResourceHalfDay } = useProjectActions();
  const selectedResourceHalfDay = useSelectedResourceHalfDay();
  const { resourceUnutilizationMap, refetchResourceUnutilization } = useResourceUnutilization();
  const regions = useRegions();
  const resourceUnutilization = resourceUnutilizationMap.get(selectedResourceHalfDay!.resourceId);

  const [startDate, setStartDate] = useState(
    setHours(selectedResourceHalfDay!.date, Number(selectedResourceHalfDay!.startTime), {
      in: tz(timeZone),
    }),
  );
  const [endDate, setEndDate] = useState(startDate);
  const [reason, setReason] = useState<
    'noOrder' | 'customerAbsence' | 'lentOut' | 'timeCompensation' | 'vacation' | 'sickness' | 'training' | 'other' | ''
  >('');
  const [otherReason, setOtherReason] = useState('');
  const [startDayDuration, setStartDayDuration] = useState(
    selectedResourceHalfDay!.startTime === '12' ? 'secondHalf' : 'firstHalf',
  );
  const [endDayDuration, setEndDayDuration] = useState('fullDay');
  const sameStartAndEndDate = isSameDay(startDate, endDate);

  const [lentOutRegionId, setLentOutRegionId] = useState<string>('');

  const { mutateAsync: setResourceInactivity } = api.Resource.setResourceInactivity.useMutation();

  const handleSelectStart = (start: Date | null) => {
    if (!start) return;
    const newStartDate = new TZDate(start, timeZone);
    if (isSameDay(newStartDate, startDate, { in: tz(timeZone) })) return;
    const newStartDateBeforeEndDate = isBefore(newStartDate, endDate);
    const oldStartDateAndEndDateIsSameDay = isSameDay(startDate, endDate, { in: tz(timeZone) });

    if (newStartDateBeforeEndDate && oldStartDateAndEndDateIsSameDay) {
      if (startDayDuration === 'firstHalf') {
        setStartDayDuration(endDayDuration === 'firstHalf' ? 'secondHalf' : 'fullDay');
      }
    }

    setStartDate(new TZDate(start, timeZone));
  };
  const handleSelectEnd = (end: Date | null) => {
    if (!end) return;
    const newEndDate = new TZDate(end, timeZone);
    if (isSameDay(newEndDate, endDate, { in: tz(timeZone) })) return;
    const newEndDateAfterStartDate = isAfter(newEndDate, startDate);
    const oldEndDateAndStartDateIsSameDay = isSameDay(endDate, startDate, { in: tz(timeZone) });
    if (oldEndDateAndStartDateIsSameDay && newEndDateAfterStartDate) {
      if (startDayDuration === 'firstHalf') {
        setStartDayDuration(endDayDuration === 'firstHalf' ? 'secondHalf' : 'fullDay');
      }
    }
    setEndDate(new TZDate(end, timeZone));
  };
  const handleClose = () => {
    setSelectedResourceHalfDay(null);
  };
  const handleSaveClicked = async () => {
    const typeOfUnutilization = REASONS.find((r) => r.value === reason)?.type;

    if (!typeOfUnutilization || reason === '') return;
    const daysInInterval = eachDayOfInterval({ start: startDate, end: endDate });
    const weekdaysInInterval = daysInInterval.filter((d) => !isWeekend(d) && !isPublicHoliday(d, holidays, timeZone));
    try {
      const days = weekdaysInInterval.map((d, index) => {
        let startTime;
        let endTime;

        const morningDateString = `${format(d, 'yyyy-MM-dd')}-08`;
        const afternoonDateString = `${format(d, 'yyyy-MM-dd')}-12`;
        const resourceUnutilizationForMorning = resourceUnutilization?.get(morningDateString);
        const resourceUnutilizationForAfternoon = resourceUnutilization?.get(afternoonDateString);
        if (index === 0) {
          startTime = startDayDuration !== 'secondHalf' ? 8 : 12;
          endTime = weekdaysInInterval.length > 1 || startDayDuration !== 'firstHalf' ? 16 : 12;
          if (
            resourceUnutilizationForMorning &&
            (resourceUnutilizationForMorning.endTime === 16 || startDayDuration !== 'secondHalf')
          ) {
            throw new Error('Resource is already in use for morning', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
          if (resourceUnutilizationForAfternoon && startDayDuration !== 'firstHalf') {
            throw new Error('Resource is already in use for afternoon', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
        } else if (index === weekdaysInInterval.length - 1) {
          startTime = 8;
          endTime = endDayDuration === 'fullDay' ? 16 : 12;
          if (resourceUnutilizationForAfternoon && endDayDuration === 'fullDay') {
            throw new Error('Resource is already in use for afternoon', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
          if (resourceUnutilizationForMorning) {
            throw new Error('Resource is already in use for morning', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
        } else {
          if (resourceUnutilizationForAfternoon) {
            throw new Error('Resource is already in use for afternoon', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
          if (resourceUnutilizationForMorning) {
            throw new Error('Resource is already in use for morning', { cause: { date: format(d, 'yyyy-MM-dd') } });
          }
          startTime = 8;
          endTime = 16;
        }

        let unutilizedType;
        let unavailabilityType;

        if (typeOfUnutilization === 'unutilized') {
          unutilizedType = reason as 'noOrder' | 'customerAbsence' | 'lentOut';
        } else {
          unavailabilityType = reason as 'vacation' | 'sickness' | 'training' | 'timeCompensation' | 'other';
        }

        return {
          from: setHours(new TZDate(d, timeZone), startTime),
          to: setHours(new TZDate(d, timeZone), endTime),
          type: {
            unutilizedType,
            unavailabilityType,
          },
          description: reason === 'other' ? otherReason : undefined,
        };
      });

      await setResourceInactivity({
        resourceId: selectedResourceHalfDay!.resourceId,
        days,
        regionId: regionId!.value,
        lentOutRegionId: lentOutRegionId !== '' ? lentOutRegionId : undefined,
      });
      toast.success(
        <FormattedMessage
          id="installationPlanning.unutilization.addAbsenceSuccess"
          defaultMessage="Absence added successfully"
        />,
      );
      await refetchResourceUnutilization();
      handleClose();
    } catch (error: any) {
      if (error.cause) {
        toast.error(
          <FormattedMessage
            id="installationPlanning.unutilization.resourceAlreadyInActive"
            defaultMessage="Resource is already unavailabile/ununitilized for {date}"
            values={{ date: error.cause.date }}
          />,
        );
      } else {
        toast.error(
          <FormattedMessage
            id="installationPlanning.unutilization.addAbsenceError"
            defaultMessage="Failed to add absence"
          />,
        );
      }
      console.error(error);
    }
  };

  const shouldDisableDate = (date: Date) => {
    const daysIsWeekend = isWeekend(date);
    if (daysIsWeekend) {
      return true;
    }
    return false;
  };

  const startDaysDurations = START_DAY_DURATIONS.filter((option) => {
    if (isSameDayFast(startDate, endDate)) {
      return true;
    }
    return option.value !== 'firstHalf';
  });

  const localeForDatePicker = LOCALES[chosenLocale as keyof typeof LOCALES] || enGB;

  return (
    <Stack gap={5}>
      <Stack spacing={4}>
        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
          <Typography variant="headline4">
            <FormattedMessage id="installationPlanning.unutilization.addAbsence" defaultMessage="Add absence" />
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              borderRadius: '50%',
              padding: 0,
              height: 24,
              width: 24,
            }}
          >
            <CrossOutlinedIcon height={24} width={24} />
          </IconButton>
        </Stack>
        <Stack gap={1}>
          <Typography variant="body1">
            <FormattedMessage id="installationPlanning.unutilization.reason" defaultMessage="Reason" />
          </Typography>
          <Select
            label=""
            name="reason"
            value={reason}
            onChange={(e) => {
              setReason(
                e.target.value as
                  | 'noOrder'
                  | 'customerAbsence'
                  | 'lentOut'
                  | 'timeCompensation'
                  | 'vacation'
                  | 'sickness'
                  | 'training'
                  | 'other',
              );
              if (e.target.value !== 'lentOut') {
                setLentOutRegionId('');
              }
              if (e.target.value !== 'other') {
                setOtherReason('');
              }
            }}
          >
            {REASONS.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                <Stack direction="row" spacing={3} alignItems="center">
                  {option.icon && <option.icon height={20} width={20} />}
                  <Typography variant="body1">
                    <FormattedMessage
                      id={`installationPlanning.removeResourceUnavailability.${option.label}` as MessageKey}
                      defaultMessage={option.defaultMessage}
                    />
                  </Typography>
                </Stack>
              </MenuItem>
            ))}
          </Select>
        </Stack>
        {reason === 'lentOut' && (
          <Stack gap={1}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationPlanning.unutilization.lentOutRegion"
                defaultMessage="Region lent out to"
              />
            </Typography>
            <Select
              label=""
              name="lentOutRegionId"
              value={lentOutRegionId}
              onChange={(e) => setLentOutRegionId(e.target.value)}
            >
              {regions
                .filter((region) => region.id?.value !== regionId?.value)
                ?.map((region) => (
                  <MenuItem key={region.id?.value} value={region.id?.value}>
                    <Typography variant="body1">{region.name}</Typography>
                  </MenuItem>
                ))}
            </Select>
          </Stack>
        )}
        {reason === 'other' && (
          <Stack gap={1}>
            <TextField
              label="Other reason"
              name="otherReason"
              value={otherReason}
              onChange={(e) => setOtherReason(e.target.value)}
              helperText={
                otherReason.length === 0
                  ? intl.formatMessage({
                      id: 'installationPlanning.unutilization.otherReasonHelperText',
                      defaultMessage: 'Please provide the other reason',
                    })
                  : undefined
              }
            />
          </Stack>
        )}
        <Stack gap={1}>
          <Typography variant="body1">
            <FormattedMessage id="installationPlanning.unutilization.fromTo" defaultMessage="From - To" />
          </Typography>
          <Stack direction="row" spacing={2}>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={localeForDatePicker}>
              <DatePicker
                defaultValue={startDate}
                onAccept={handleSelectStart}
                sx={{ width: '100%', marginTop: 0.5 }}
                slots={{ openPickerIcon: CalendarOutlinedIcon }}
                shouldDisableDate={shouldDisableDate}
                maxDate={endDate}
              />
            </LocalizationProvider>
            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={localeForDatePicker}>
              <DatePicker
                defaultValue={endDate}
                onAccept={handleSelectEnd}
                sx={{ width: '100%', marginTop: 0.5 }}
                slots={{ openPickerIcon: CalendarOutlinedIcon }}
                shouldDisableDate={shouldDisableDate}
                minDate={startDate}
              />
            </LocalizationProvider>
          </Stack>
        </Stack>

        <Stack direction="row" spacing={2}>
          <Stack width="50%" gap={1}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationPlanning.unutilization.firstDayDuration"
                defaultMessage="First day duration"
              />
            </Typography>

            <Select
              label=""
              name="duration"
              value={startDayDuration}
              onChange={(e) => setStartDayDuration(e.target.value)}
            >
              {startDaysDurations.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Stack direction="row" spacing={1}>
                    <Typography variant="body1">{option.label}</Typography>
                  </Stack>
                </MenuItem>
              ))}
            </Select>
          </Stack>
          {!sameStartAndEndDate && (
            <Stack width="50%" gap={1}>
              <Typography variant="body1">
                <FormattedMessage
                  id="installationPlanning.unutilization.endDayDuration"
                  defaultMessage="End day duration"
                />
              </Typography>

              <Select
                label=""
                name="duration"
                value={endDayDuration}
                onChange={(e) => setEndDayDuration(e.target.value)}
              >
                {END_DAY_DURATIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    <Stack direction="row" spacing={1}>
                      <Typography variant="body1">{option.label}</Typography>
                    </Stack>
                  </MenuItem>
                ))}
              </Select>
            </Stack>
          )}
        </Stack>
      </Stack>

      <Button
        variant="contained"
        onClick={handleSaveClicked}
        disabled={reason.length === 0 || (reason === 'other' && otherReason.length === 0)}
      >
        <FormattedMessage id="common.label.save" />
      </Button>
    </Stack>
  );
}
