import { Skeleton, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { PersonCircleOutlinedIcon } from '@ui/components/StandardIcons/PersonCircleOutlinedIcon';
import toast from 'react-hot-toast';
import { api } from 'utils/api';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { ContinuousWorkSegmentForJobItem } from '../types/planningTypes';
import { useRegionContext } from '../../../context/RegionContext';
import { useProjectActions } from '../stores/ProjectStore';

export default function RemoveAssignedResources({
  jobId,
  continuousWorkSegment,
}: {
  jobId: string;
  continuousWorkSegment: ContinuousWorkSegmentForJobItem;
}) {
  const { setShowRemoveAssignedResourcesModal } = useProjectActions();
  const region = useRegionContext();
  const utils = api.useUtils();

  const projectId = continuousWorkSegment.installationProject?.id?.value;
  const assignedResources = continuousWorkSegment.installationProject?.jobs
    .find((job) => job.id?.value === jobId)
    ?.assignedResources.flatMap((res) => res.userId?.value)
    .filter((id): id is string => id !== undefined);

  const { data: resourceInfo, isLoading: isLoadingResourceInfo } =
    api.InstallationProject.getAssignedResourceInfo.useQuery({
      resourceIds: (assignedResources ?? []) as string[],
    });
  const { refetch: refetchInstallationProjects } = api.InstallationProject.getInstallationProjects.useQuery({
    operationalUnitId: region.id!.value,
  });
  const { mutateAsync: unassignJobResources } = api.InstallationProject.unassignJobResources.useMutation();
  const handleRemoveAssignedResources = () => {
    unassignJobResources(
      { jobId },
      {
        onSuccess: async () => {
          // Optimistically update the query cache for installation projects
          utils.InstallationProject.getInstallationProjects.setData(
            { operationalUnitId: region.id!.value },
            (oldData) => {
              if (!oldData) return [];
              return oldData.map((project) => {
                if (projectId !== project.installationProject?.id?.value)
                  return project as FullInstallationProjectEntity;
                return {
                  ...(project as FullInstallationProjectEntity),
                  installationProject: {
                    ...(project.installationProject as InstallationProject),
                    jobs:
                      (project.installationProject as InstallationProject).jobs?.map((job) => {
                        if (job.id?.value !== jobId) return job;
                        return {
                          ...job,
                          assignedResources: [],
                        };
                      }) ?? [],
                  },
                };
              });
            },
          );
          toast.success('Assigned resources removed successfully from the job');
          refetchInstallationProjects();
          setShowRemoveAssignedResourcesModal(null);
        },
        onError: () => {
          toast.error('Failed to remove assigned resources from the job');
        },
      },
    );
  };

  return (
    <Stack spacing={2} p={2}>
      <Typography variant="headline4">Unassign the following resources?</Typography>
      <Stack spacing={1}>
        {isLoadingResourceInfo
          ? (assignedResources ?? []).map((res) => (
              <Stack direction="row" key={res} spacing={2} alignItems="center">
                <PersonCircleOutlinedIcon height={32} width={32} color="#53535A" />
                <Skeleton key={res} variant="text" width={100} height="16px" />
              </Stack>
            ))
          : resourceInfo?.map((resource) => (
              <Stack direction="row" key={resource.userId?.value} spacing={2} alignItems="center">
                {resource.userImage ? (
                  // eslint-disable-next-line @next/next/no-img-element
                  <img
                    src={`data:image/jpeg;base64,${btoa(
                      String.fromCharCode.apply(null, resource.userImage.map((x) => x) as unknown as number[]),
                    )}`}
                    alt={`${resource.firstName} ${resource.lastName}`}
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                    }}
                  />
                ) : (
                  <PersonCircleOutlinedIcon height={32} width={32} color="#53535A" />
                )}
                <Typography key={resource.userId?.value} variant="body1">
                  {resource.firstName} {resource.lastName}
                </Typography>
              </Stack>
            ))}
      </Stack>
      <Stack direction="row" spacing={2} pt={4}>
        <Button variant="contained" onClick={handleRemoveAssignedResources} fullWidth>
          Yes
        </Button>
        <Button variant="outlined" onClick={() => setShowRemoveAssignedResourcesModal(null)} fullWidth>
          No
        </Button>
      </Stack>
    </Stack>
  );
}
