import { Alert, CircularProgress, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import InstallationBooking from 'components/installation-booking/InstallationBooking';
import { useGetManHours } from 'components/installation-booking/queries/useGetManHours';
import { resourceMapping, reverseResourceMapping, useResourceTypesForCountry } from '../types/planningTypes';
import { useProjectActions, useProjectForNoBaselineHoursModal } from '../stores/ProjectStore';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { useCountryCodeContext } from '../../../context/CountryCodeContext';

export default function NoBaselineHours() {
  const { setProjectForNoBaselineHoursModal } = useProjectActions();
  const projectForNoBaselineHoursModal = useProjectForNoBaselineHoursModal();
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const countryCode = useCountryCodeContext();
  const closeModal = () => setProjectForNoBaselineHoursModal(null);

  const intl = useIntl();
  const installationProjectId = projectForNoBaselineHoursModal?.installationProject?.id?.value ?? '';
  const rolesForCountry = useResourceTypesForCountry(countryCode).map((resourceType) => resourceMapping[resourceType]);
  const rolesWithsJobs = (projectForNoBaselineHoursModal?.installationProject?.jobs ?? []).map(
    (job) => job?.requiredRole,
  );
  const missingRoles = rolesForCountry
    .filter((role) => !rolesWithsJobs.includes(role))
    .map((role) => reverseResourceMapping[role as keyof typeof reverseResourceMapping]);

  const { data: manHoursComputation, isLoading: isLoadingManHours } = useGetManHours({
    installationGroundworkId:
      projectForNoBaselineHoursModal?.installationProject?.installationGroundworkId?.value ?? '',
    countryCode,
  });

  if (isLoadingManHours) {
    return <CircularProgress />;
  }

  const manHours = manHoursComputation?.manHoursForAllRoles;
  const isBasedOnBaseline = manHoursComputation?.isBasedOnBaseline;

  if (!manHours || isBasedOnBaseline === undefined) {
    return (
      <Alert severity="error" sx={{ margin: '20px auto' }}>
        <Typography variant="body1">{intl.formatMessage({ id: 'common.notify.error' })}</Typography>
      </Alert>
    );
  }

  return (
    <InstallationBooking
      installationProjectId={installationProjectId}
      onBookedInstallation={refetchProjectsUpdatedSince}
      initialManHours={manHours}
      isBasedOnBaseline={isBasedOnBaseline}
      missingRoles={missingRoles}
      closeModal={closeModal}
    />
  );
}
