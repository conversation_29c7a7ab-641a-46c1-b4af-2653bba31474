import { CircularProgress, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { FormattedMessage } from 'react-intl';
import { resourceMapping, reverseResourceMapping, useResourceTypesForCountry } from '../types/planningTypes';
import { useProjectActions, useProjectForIncompleteHoursModal } from '../stores/ProjectStore';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { useCountryCodeContext } from '../../../context/CountryCodeContext';
import { useState } from 'react';
import { TextField } from '@ui/components/TextField/TextField';
import { api } from 'utils/api';
import toast from 'react-hot-toast';

export default function IncompleteHours() {
  const countryCode = useCountryCodeContext();
  const rolesForCountry = useResourceTypesForCountry(countryCode).map((resourceType) => resourceMapping[resourceType]);

  const [hoursToAdd, setHoursToAdd] = useState<Record<number, number>>(
    rolesForCountry.reduce(
      (acc, role) => {
        acc[role] = 0;
        return acc;
      },
      {} as Record<number, number>,
    ),
  );
  const { setProjectForIncompleteHoursModal } = useProjectActions();
  const projectForIncompleteHoursModal = useProjectForIncompleteHoursModal();
  const { refetchProjectsUpdatedSince } = useProjectsUpdatedSince();

  const { mutateAsync: addJobHours, isPending: isAddingJobHours } =
    api.InstallationProject.extendInstallationProjectJob.useMutation({});

  const handleSubmit = async () => {
    if (projectForIncompleteHoursModal?.installationProject?.jobs === undefined) {
      return;
    }
    const jobsToUpdate = projectForIncompleteHoursModal?.installationProject?.jobs
      .map((job) => {
        if (job.requiredRole === undefined) {
          return null;
        }
        const hoursToAddForJob = hoursToAdd[job.requiredRole];
        if (hoursToAddForJob === 0 || hoursToAddForJob === undefined) {
          return null;
        }
        return {
          jobId: job.id?.value,
          requiredRole: job.requiredRole,
          durationInHours: hoursToAddForJob,
        };
      })
      .filter((job) => job !== null);

    if (jobsToUpdate.length === 0) {
      toast.error('No jobs to update');
      return;
    }

    const addJobHoursPromises = jobsToUpdate.map(
      (job) =>
        addJobHours({ jobId: job.jobId ?? '', durationInHours: job.durationInHours })
          .then(() => null) // No need for specific success tracking in this array
          .catch(() => reverseResourceMapping[job.requiredRole as keyof typeof reverseResourceMapping]), // Return job role if it fails
    );

    // Run all additions in parallel
    const results = await Promise.allSettled(addJobHoursPromises);

    // Track failures by role
    const failedRoles: string[] = [];
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        if (jobsToUpdate[index]) {
          failedRoles.push(
            reverseResourceMapping[jobsToUpdate[index]!.requiredRole as keyof typeof reverseResourceMapping],
          ); // Add the role of the failed job to the list
        }
      }
    });

    // Handle results
    if (failedRoles.length > 0) {
      // Show error toast with information about failed deletions
      toast.error(
        <>
          <FormattedMessage
            id="installationPlanning.incompleteHours.errors.failedToAddHours"
            defaultMessage="Failed to add hours to the following roles: "
          />
          {failedRoles.join(', ')}
        </>,
        { position: 'bottom-center' },
      );
    } else {
      refetchProjectsUpdatedSince();
      // All deletions succeeded
      setProjectForIncompleteHoursModal(null);
      toast(
        <FormattedMessage
          id="installationPlanning.incompleteHours.success"
          defaultMessage="All hours have been added successfully."
        />,
      );
    }
    setProjectForIncompleteHoursModal(null);
    refetchProjectsUpdatedSince();
  };

  const getNumberOfDaysAndHours = (hours: number) => {
    const days = Math.floor(hours / 8);
    const remainingHours = hours % 8;
    return { days, hours: remainingHours };
  };

  const capitalizeFirstLetter = (val: string) => {
    return String(val).charAt(0).toUpperCase() + String(val).slice(1);
  };
  return (
    <Stack>
      <Typography variant="h6" mb={2}>
        <FormattedMessage id="installationPlanning.incompleteHours.title" defaultMessage="Incomplete Hours" />
      </Typography>
      <Stack py={2} mb={2} pb={2} gap={6}>
        {rolesForCountry.map((role) => (
          <Stack key={role} gap={2}>
            <TextField
              label={capitalizeFirstLetter(
                reverseResourceMapping[role as keyof typeof reverseResourceMapping].toLowerCase(),
              )}
              type="number"
              value={hoursToAdd[role]}
              onChange={(e) => setHoursToAdd({ ...hoursToAdd, [role]: Number(e.target.value) })}
              name={`hoursToAdd-${role}`}
              suffix="h"
            />
            <Typography variant="body1">
              <FormattedMessage
                id="installationPlanning.incompleteHours.description"
                defaultMessage="This will add {days} days and {hours} hours to the job"
                values={{
                  days: getNumberOfDaysAndHours(hoursToAdd[role] ?? 0).days,
                  hours: getNumberOfDaysAndHours(hoursToAdd[role] ?? 0).hours,
                }}
              />
            </Typography>
          </Stack>
        ))}
      </Stack>
      <Button variant="contained" onClick={() => handleSubmit()} disabled={isAddingJobHours}>
        {isAddingJobHours ? (
          <CircularProgress size={20} />
        ) : (
          <FormattedMessage id="installationPlanning.incompleteHours.save" defaultMessage="Save" />
        )}
      </Button>
    </Stack>
  );
}
