import { Modal } from '@ui/components/Modal/Modal';
import { useProjectForNoBaselineHoursModal, useProjectActions } from '../stores/ProjectStore';
import NoBaselineHours from './NoBaselineHours';

export default function NoBaselineHoursModal() {
  const projectForNoBaselineHoursModal = useProjectForNoBaselineHoursModal();
  const { setProjectForNoBaselineHoursModal } = useProjectActions();

  return (
    <Modal
      isModalOpen={!!projectForNoBaselineHoursModal}
      handleClose={() => setProjectForNoBaselineHoursModal(null)}
      width="auto"
      height="auto"
    >
      {!!projectForNoBaselineHoursModal && <NoBaselineHours />}
    </Modal>
  );
}
