import { Box, IconButton, Stack, Typography } from '@mui/material';
import { FormattedMessage } from 'react-intl';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { Modal } from '@ui/components/Modal/Modal';
import { useOverlappingSegmentsForResources, useProjectActions } from '../stores/ProjectStore';
import { useResources } from '../contexts/ResourcesContext';

export default function OverlappingSegmentsModal() {
  const overlappingSegmentsForResources = useOverlappingSegmentsForResources();
  const { setOverlappingSegmentsForResources } = useProjectActions();
  const resourcesForRegion = useResources();
  if (overlappingSegmentsForResources.size === 0) return null;

  return (
    <Modal isModalOpen handleClose={() => setOverlappingSegmentsForResources(new Map())} width="766px">
      <Stack>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="headline4">
            <FormattedMessage
              id="installationPlanning.overlappingSegments.title"
              defaultMessage="Overlapping Segments"
            />
          </Typography>
          <IconButton onClick={() => setOverlappingSegmentsForResources(new Map())}>
            <CrossOutlinedIcon />
          </IconButton>
        </Stack>
        <Stack>
          <Box py={2}>
            <Typography variant="body1">
              <FormattedMessage
                id="installationPlanning.overlappingSegments.explanation"
                defaultMessage="With your recent change, the following resources now have overlapping segments. Please adjust them to ensure no overlaps. The affected segments are listed below."
              />
            </Typography>
          </Box>
          {Array.from(overlappingSegmentsForResources.entries()).map(([resourceId, overlappingSegments]) => (
            <Stack key={resourceId}>
              <Stack
                spacing={2}
                sx={{
                  border: '1px solid #E0E0E0',
                  borderRadius: '8px',
                  padding: '16px',
                  backgroundColor: '#22222206',
                }}
              >
                <Typography variant="headline4">
                  {resourcesForRegion.get(resourceId)?.firstName} {resourcesForRegion.get(resourceId)?.lastName}
                </Typography>
                <Stack direction="row" gap={2} flexWrap="wrap" justifyContent="flex-start" alignItems="flex-start">
                  {overlappingSegments.map((os) => (
                    <Stack
                      key={os.overlappingSegments.segments[0]!.jobId + os.overlappingSegments.segments[1]!.jobId}
                      sx={{
                        border: '1px solid #E0E0E0',
                        borderRadius: '8px',
                        padding: '16px',
                        backgroundColor: '#22222206',
                        width: '200px',
                        minWidth: '200px',
                      }}
                    >
                      <Typography variant="body1">
                        {os.overlappingSegments.segments[0]!.segment.startTime?.toLocaleDateString(undefined, {
                          weekday: 'short',
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric',
                        })}
                      </Typography>

                      <Stack direction="row" spacing={2} justifyContent="flex-start" alignItems="center">
                        <Stack>
                          {os.overlappingSegments.segments.map((segment) => (
                            <Stack key={segment.jobId}>
                              <Stack direction="row" spacing={2}>
                                <Typography variant="body1Emphasis">{segment.customerName}</Typography>
                              </Stack>
                            </Stack>
                          ))}
                        </Stack>
                      </Stack>
                    </Stack>
                  ))}
                </Stack>
              </Stack>
            </Stack>
          ))}
        </Stack>
      </Stack>
    </Modal>
  );
}
