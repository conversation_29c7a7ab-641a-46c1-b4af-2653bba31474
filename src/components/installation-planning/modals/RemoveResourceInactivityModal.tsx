import { Modal } from '@ui/components/Modal/Modal';
import { useProjectActions, useShowRemoveResourceInactivityModal } from '../stores/ProjectStore';
import RemoveResourceInactivity from './RemoveResourceInactivity';

export default function RemoveResourceInactivityModal() {
  const { setShowRemoveResourceInactivityModal } = useProjectActions();
  const showRemoveResourceInactivityModal = useShowRemoveResourceInactivityModal();

  return (
    <Modal
      isModalOpen={showRemoveResourceInactivityModal !== null}
      handleClose={() => setShowRemoveResourceInactivityModal(null)}
      height="fit-content"
      width="fit-content"
    >
      {showRemoveResourceInactivityModal !== null && <RemoveResourceInactivity />}
    </Modal>
  );
}
