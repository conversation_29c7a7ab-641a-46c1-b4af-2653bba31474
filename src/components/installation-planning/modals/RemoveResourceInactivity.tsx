import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useProjectActions, useShowRemoveResourceInactivityModal } from '../stores/ProjectStore';
import { FormattedMessage } from 'react-intl';
import { Button } from '@ui/components/Button/Button';
import { api } from 'utils/api';
import { useResourceUnutilization } from '../contexts/ResourceUnutilizationContext';
import toast from 'react-hot-toast';

export default function RemoveResourceInactivity() {
  const { setShowRemoveResourceInactivityModal } = useProjectActions();
  const showRemoveResourceInactivityModal = useShowRemoveResourceInactivityModal();
  const { refetchResourceUnutilization } = useResourceUnutilization();

  const { mutateAsync: removeResourceInactivity } = api.Resource.removeResourceInactivity.useMutation();

  const handleCancel = () => {
    setShowRemoveResourceInactivityModal(null);
  };

  const handleConfirm = async () => {
    if (!showRemoveResourceInactivityModal) return;
    const { type, ids } = showRemoveResourceInactivityModal;
    const deletionPromises = ids.map(
      (id) =>
        removeResourceInactivity({ type, id })
          .then(() => null) // No need for specific success tracking in this array
          .catch(() => id), // Return id if it fails
    );

    // Run all deletions in parallel
    const results = await Promise.allSettled(deletionPromises);

    // Track failures by id
    const failedIds: string[] = [];
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        if (ids[index]) {
          failedIds.push(ids[index]);
        }
      }
    });

    // Handle results
    if (failedIds.length > 0) {
      // Show error toast with information about failed deletions
      toast.error(
        <>
          <FormattedMessage
            id="installationPlanning.removeResourceUnavailability.errors"
            defaultMessage="Failed to delete some resource unavailability"
          />
          {failedIds.join(', ')}
        </>,
        { position: 'bottom-center' },
      );
    } else {
      await refetchResourceUnutilization();
      setShowRemoveResourceInactivityModal(null);
      toast(
        <FormattedMessage
          id="installationPlanning.removeResourceUnavailability.success"
          defaultMessage="Resource inactivity have been deleted successfully."
        />,
      );
    }
  };

  return (
    <Stack gap={3}>
      <Typography>
        <FormattedMessage
          id="installationPlanning.removeResourceUnavailability.title"
          defaultMessage="Remove Resource Unavailability?"
        />
      </Typography>
      <Stack direction="row" spacing={2} justifyContent="space-between">
        <Button variant="outlined" onClick={handleCancel} sx={{ width: '50%' }}>
          <FormattedMessage
            id="installationPlanning.removeResourceUnavailability.button.cancel"
            defaultMessage="Cancel"
          />
        </Button>
        <Button variant="contained" onClick={handleConfirm} sx={{ width: '50%' }}>
          <FormattedMessage
            id="installationPlanning.removeResourceUnavailability.button.confirm"
            defaultMessage="Confirm"
          />
        </Button>
      </Stack>
    </Stack>
  );
}
