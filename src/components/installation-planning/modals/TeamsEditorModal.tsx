import { Modal } from '@ui/components/Modal/Modal';
import TeamsEditor from './TeamsEditor';
import { useProjectActions, useShowEditTeamsModal } from '../stores/ProjectStore';
import { useAvailability } from '../contexts/AvailabilityContext';

export default function TeamsEditorModal() {
  const { teams } = useAvailability();
  const showEditTeamsModal = useShowEditTeamsModal();
  const { setShowEditTeamsModal } = useProjectActions();

  return (
    <Modal
      isModalOpen={showEditTeamsModal}
      handleClose={() => setShowEditTeamsModal(false)}
      height="80vh"
      width="auto"
      sx={{
        background: 'white',
      }}
    >
      {teams && <TeamsEditor />}
    </Modal>
  );
}
