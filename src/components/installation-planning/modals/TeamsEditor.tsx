import React, { useState } from 'react';
import {
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  MenuItem,
  IconButton,
} from '@mui/material';
import { BinOutlinedIcon } from '@ui/components/StandardIcons/BinOutlinedIcon';
import { Button } from '@ui/components/Button/Button';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { Select } from '@ui/components/Select/Select';
import { TextField } from '@ui/components/TextField/TextField';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { useCountryCodeContext } from 'context/CountryCodeContext';
import { resourceMapping, reverseResourceMapping } from '../types/planningTypes';
import { useAvailability } from '../contexts/AvailabilityContext';
import { useRegionContext } from '../../../context/RegionContext';

export default function TeamsEditor() {
  const { id: regionId } = useRegionContext();
  const countryCode = useCountryCodeContext();
  const { teams, refetchTeamAvailability } = useAvailability();
  const [teamToAdd, setTeamToAdd] = useState<Omit<Team, 'id'> | null>(null);
  const [teamToEdit, setTeamToEdit] = useState<Team | null>(null);
  const roles = Object.values(resourceMapping).filter(
    (resourceType) => !(countryCode === 'GB' && resourceType === 3),
  ) as (keyof typeof reverseResourceMapping)[];

  const teamsByRole = roles.map((role) => ({
    role,
    teamsForRole: teams.filter((team: Team) => !team.deletedAt).filter((team: Team) => team.role === role),
  }));

  const { mutate: addTeam } = api.Resource.addTeam.useMutation({
    onSuccess() {
      refetchTeamAvailability();
      toast.success('Team added successfully');
      setTeamToAdd(null);
    },
    onError(error: any) {
      toast.error(error.message);
    },
  });
  const handleAddTeam = () => {
    if (!teamToAdd?.name) {
      toast.error('Please enter a name for the team');
      return;
    }
    if (!regionId) {
      toast.error('Region ID is missing');
      return;
    }
    addTeam({ regionId: regionId.value, teamName: teamToAdd.name, role: teamToAdd.role, origin: teamToAdd.origin });
  };

  const { mutate: updateTeam } = api.Resource.updateTeam.useMutation({
    onSuccess() {
      refetchTeamAvailability();
      toast.success('Team updated successfully');
      setTeamToEdit(null);
    },
    onError(error: any) {
      toast.error(error.message);
    },
  });

  const handleUpdateTeam = () => {
    if (!teamToEdit?.name) {
      toast.error('Please enter a name for the team');
      return;
    }
    if (!teamToEdit.id?.value) {
      toast.error('Team ID is missing');
      return;
    }
    updateTeam({
      teamId: teamToEdit.id.value,
      teamName: teamToEdit.name,
    });
  };

  const { mutate: deleteTeam } = api.Resource.deleteTeam.useMutation({
    onSuccess() {
      refetchTeamAvailability();
      toast.success('Team deleted successfully');
    },
    onError(error: any) {
      toast.error(error.message);
    },
  });

  const handleDeleteTeam = (teamId: string) => {
    deleteTeam({ teamId });
  };

  return (
    <Stack>
      {teamsByRole.map(({ role, teamsForRole }) => (
        <TableContainer component={Paper} key={role} style={{ marginBottom: '20px', boxShadow: 'none' }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="headline3" style={{ padding: '16px', textTransform: 'capitalize' }}>
              {reverseResourceMapping[role].toLowerCase()} Teams
            </Typography>
            <Box p={2}>
              <Button
                variant="contained"
                onClick={() =>
                  setTeamToAdd({
                    name: '',
                    role: role as Team['role'],
                    origin: 1,
                    operationalUnitId: { value: regionId!.value },
                    defaultResources: [],
                  })
                }
              >
                Add Team
              </Button>
            </Box>
          </Stack>

          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Aira Or External</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {teamsForRole.map((team: Team) => (
                <TableRow key={team.id?.value ?? 'unknown'}>
                  <TableCell width="540px">
                    {teamToEdit && teamToEdit?.id?.value === team.id?.value ? (
                      <TextField
                        name="name"
                        size="small"
                        value={teamToEdit.name}
                        onChange={(e) => setTeamToEdit({ ...teamToEdit, name: e.target.value })}
                      />
                    ) : (
                      team.name
                    )}
                  </TableCell>
                  <TableCell>{team.origin === 1 ? 'Aira' : 'External'}</TableCell>
                  <TableCell>
                    {teamToEdit?.id === team.id ? (
                      <Button variant="contained" onClick={handleUpdateTeam} size="small">
                        Save
                      </Button>
                    ) : (
                      <Stack direction="row" spacing={1}>
                        <IconButton
                          onClick={() => {
                            setTeamToEdit({ ...team });
                          }}
                          sx={{
                            height: '40px',
                            width: '40px',
                            background: '#22222608',
                          }}
                        >
                          <PenOutlinedIcon color="#000" />
                        </IconButton>
                        <IconButton
                          onClick={() => team.id && handleDeleteTeam(team.id.value)}
                          sx={{
                            height: '40px',
                            width: '40px',
                            background: '#22222608',
                          }}
                        >
                          <BinOutlinedIcon color="#000" />
                        </IconButton>
                      </Stack>
                    )}
                  </TableCell>
                </TableRow>
              ))}
              {teamToAdd && teamToAdd.role === role && (
                <TableRow>
                  <TableCell>
                    <TextField
                      name="name"
                      size="small"
                      value={teamToAdd.name}
                      onChange={(e) => setTeamToAdd({ ...teamToAdd, name: e.target.value })}
                      sx={{
                        pb: '0 !important',
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Select
                      label=""
                      name="origin"
                      value={teamToAdd.origin.toString()}
                      size="small"
                      onChange={(e) => setTeamToAdd({ ...teamToAdd, origin: Number(e.target.value) as 1 | 2 })}
                      sx={{
                        width: '120px',
                        pb: '0 !important',
                      }}
                    >
                      <MenuItem value="1">Aira</MenuItem>
                      <MenuItem value="2">External</MenuItem>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Button variant="contained" onClick={handleAddTeam} size="small">
                      Save
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      ))}
    </Stack>
  );
}
