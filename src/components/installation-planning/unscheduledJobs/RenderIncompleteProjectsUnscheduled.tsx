import React, { useMemo } from 'react';
import { Box, Skeleton, Stack, Typography } from '@mui/material';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { api } from 'utils/api';
import { ContinuousWorkSegment, reverseResourceMapping } from '../types/planningTypes';
import DraggableJob from '../jobBar/DraggableJob';
import {
  useDispatchMode,
  useHoveredUnsavedChangeJobId,
  useHighlightProjectsWithinDistance,
  useHighlightFlexibleProjects,
  useProjectActions,
  useFocusedProject,
  useJobsToDispatch,
  useSelectedProject,
  useSplitSegmentsToMove,
  useJobBeingSplit,
  useUnsavedChanges,
  useUnsavedChangesWithoutReason,
} from '../stores/ProjectStore';
import useTableCellDims from '../hooks/useTableCellDims';
import { useRegionContext } from '../../../context/RegionContext';
import { useAllProjects } from '../contexts/AllProjects';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { InstallationProjectJob_WorkSegmentPlannedType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { STAGES_TO_SHOW_IN_UNSCHEDULED_JOBS } from './UnscheduledJobsSidebar';

export default function RenderIncompleteProjectsUnscheduled({ requiredRole }: { requiredRole: number }) {
  const { projectsWithIncompleteInstallation: projects } = useAllProjects();
  const { id: regionId, timeZone } = useRegionContext();
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const {
    setJobBeingDragged,
    setJobBeingSplit,
    setJobsToDispatch,
    setOverlappingSegmentsForResources,
    setRightClickMenu,
    setSelectedProject,
    setShowUnsavedChanges,
    setSplitSegmentsToMove,
    setToolTipProject,
    updateUnsavedChanges,
    setHoveredProject,
    setShowRightSidebar,
  } = useProjectActions();
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const focusedProjectInStore = useFocusedProject();
  const dispatchMode = useDispatchMode();
  const highlightFlexibleProjects = useHighlightFlexibleProjects();
  const highlightProjectsWithinDistance = useHighlightProjectsWithinDistance();
  const hoveredUnsavedChangeJobId = useHoveredUnsavedChangeJobId();
  const jobBeingSplit = useJobBeingSplit();
  const jobsToDispatch = useJobsToDispatch();
  const { initialLoadTime, refetchProjectsUpdatedSince } = useProjectsUpdatedSince();
  const selectedProject = useSelectedProject();
  const splitSegmentsToMove = useSplitSegmentsToMove();
  const unsavedChanges = useUnsavedChanges();
  const unsavedChangesWithoutReason = useUnsavedChangesWithoutReason();

  const resourceType = reverseResourceMapping[requiredRole as keyof typeof reverseResourceMapping];

  const { isLoading: isLoadingProjectsAtTimeOfLoad } = api.InstallationProject.getInstallationProjects.useQuery(
    {
      operationalUnitId: regionId!.value,
    },
    {
      refetchOnWindowFocus: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    },
  );

  const getUnscheduledResourceProjects = ({
    allProjects,
    role,
    focusedProject,
  }: {
    allProjects: FullInstallationProjectEntity[];
    role: number;
    focusedProject: FullInstallationProjectEntity | undefined;
  }) =>
    allProjects
      .filter((project) =>
        project.installationProject?.jobs.some(
          (job) =>
            job.workSegments.length === 0 ||
            job.workSegments.some(
              (segment) =>
                segment.plannedType ===
                InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
            ) ||
            (job.workSegments.some((segment) => segment.startTime) &&
              job.workSegments.some((segment) => !segment.startTime)),
        ),
      )
      .map((proj) => ({
        ...proj,
        job: proj.installationProject?.jobs.find((job) => job.requiredRole === role),
      }))
      .filter(
        (project) =>
          project.job &&
          (project.job.workSegments.some(
            (segment) =>
              segment.plannedType === InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
          ) ||
            (project.job.workSegments.some((segment) => segment.startTime) &&
              project.job.workSegments.some((segment) => !segment.startTime))) &&
          (focusedProject === undefined ||
            (focusedProject &&
              project.installationProject?.id?.value === focusedProject.installationProject?.id?.value)),
      )
      .map(
        (
          project,
        ): ContinuousWorkSegment & {
          segmentDetails:
            | {
                jobId: string;
                requiredRole: number;
                workSegments: InstallationProjectJob_WorkSegment[];
                startTime?: Date;
                endTime?: Date;
                duration?: number | undefined;
              }
            | undefined;
        } => {
          if (!project.job) return project as ContinuousWorkSegment & { segmentDetails: undefined };
          const { workSegments, id: jobId } = project.job;
          if (!jobId) return project as ContinuousWorkSegment & { segmentDetails: undefined };
          const workSegmentsWithoutStartTime = workSegments.filter((segment) => !segment.startTime);
          const duration =
            workSegmentsWithoutStartTime.reduce((acc, segment) => {
              if (segment.duration?.seconds) {
                return acc + segment.duration.seconds;
              }
              return acc;
            }, 0) / 3600;
          return {
            ...project,
            segmentDetails: {
              jobId: jobId.value,
              requiredRole: project.job.requiredRole,
              workSegments: workSegmentsWithoutStartTime,
              duration,
              incompleteHours: true,
            } as unknown as ContinuousWorkSegment['segmentDetails'],
            hasAssignedResources: project.job.assignedResources.length > 0,
          };
        },
      )
      .sort((a, b) => {
        if (!a.segmentDetails.duration) return -1;
        if (!b.segmentDetails.duration) return 1;
        return a.segmentDetails.duration - b.segmentDetails.duration;
      })
      .filter(
        (project) =>
          project.segmentDetails.duration &&
          project.installationProject &&
          STAGES_TO_SHOW_IN_UNSCHEDULED_JOBS.includes(project.installationProject.stage),
      );

  const unscheduledResourceProjects = useMemo(
    () =>
      getUnscheduledResourceProjects({
        allProjects: projects,
        role: requiredRole,
        focusedProject: focusedProjectInStore,
      }),
    [projects, requiredRole, focusedProjectInStore],
  );

  return (
    <Box
      style={{
        width: '233px',
        maxWidth: '233px',
        background: '#fff',
        padding: 0,
        whiteSpace: 'nowrap',
        paddingLeft: '10px',
        paddingBottom: '24px',
      }}
    >
      <Box
        sx={{
          background: '#fff',
          maxWidth: '233px',
          width: '233px',
          height: `${CELL_HEIGHT}px`,
          maxHeight: `${CELL_HEIGHT}px`,
        }}
      >
        <Stack direction="row" alignItems="center" gap={1} sx={{ padding: '0 0 0 20px' }}>
          <Typography variant="body1" fontSize="14px" lineHeight="16px">
            {resourceType.charAt(0).toUpperCase() + resourceType.slice(1).toLowerCase()} jobs
          </Typography>
        </Stack>
      </Box>
      <Stack
        gap={1}
        sx={{
          maxWidth: '223px',
          width: '223px',
          padding: '0 12px 24px 20px',
          m: 0,
        }}
      >
        {isLoadingProjectsAtTimeOfLoad &&
          [1, 2, 3, 4, 5].map((item) => (
            <Skeleton animation="wave" variant="rounded" key={item} height={CELL_HEIGHT - 10} width="260px" />
          ))}
        {unscheduledResourceProjects.length > 0 &&
          unscheduledResourceProjects.map((project) => (
            <DraggableJob
              key={`${project.segmentDetails.jobId ?? ''}${requiredRole}unscheduled`}
              continuousWorkSegment={project}
              continuousSegmentsByResource={new Map()}
              canEdit
              containerRef={null}
              dayWidth={dayWidth}
              dispatchMode={dispatchMode}
              focusedProject={focusedProjectInStore}
              highlightFlexibleProjects={highlightFlexibleProjects}
              highlightProjectsWithinDistance={highlightProjectsWithinDistance}
              hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
              jobBeingSplit={jobBeingSplit}
              jobsToDispatch={jobsToDispatch}
              initialLoadTime={initialLoadTime}
              refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
              regionId={regionId!.value}
              resourceId={undefined}
              selectedProject={selectedProject}
              setHoveredProject={setHoveredProject}
              setJobBeingDragged={setJobBeingDragged}
              setJobBeingSplit={setJobBeingSplit}
              setJobsToDispatch={setJobsToDispatch}
              setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
              setRightClickMenu={setRightClickMenu}
              setSelectedProject={setSelectedProject}
              setShowRightSidebar={setShowRightSidebar}
              setShowUnsavedChanges={setShowUnsavedChanges}
              setSplitSegmentsToMove={setSplitSegmentsToMove}
              setToolTipProject={setToolTipProject}
              splitSegmentsToMove={splitSegmentsToMove}
              timeZone={timeZone}
              unsavedChanges={unsavedChanges}
              unsavedChangesWithoutReason={unsavedChangesWithoutReason}
              updateUnsavedChanges={updateUnsavedChanges}
              user={user as UserIdentity}
              incomplete={true}
            />
          ))}
      </Stack>
    </Box>
  );
}
