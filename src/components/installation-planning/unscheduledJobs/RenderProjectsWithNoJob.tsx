import React, { useMemo } from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { Box, IconButton, Stack, Tooltip, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import {
  useFocusedProject,
  useHighlightFlexibleProjects,
  useHighlightProjectsWithinDistance,
  useProjectActions,
  useSelectedProject,
} from '../stores/ProjectStore';
import getDistanceBetweenTwoLatLong from '../helpers/getDistanceBetweenTwoLatLong';
import flexibleBookingDiscountNames from '../helpers/flexibleBookingDiscountNames';
import { useAllProjects } from '../contexts/AllProjects';
import { PauseOutlinedIcon } from '@ui/components/StandardIcons/PauseOutlinedIcon';
import { FaceFrowningCircleOutlinedIcon } from '@ui/components/StandardIcons/FaceFrowningCircleOutlinedIcon';
import { getBackgroundColor } from '../jobBar/jobItemHelpers';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { STAGES_TO_SHOW_IN_UNSCHEDULED_JOBS } from './UnscheduledJobsSidebar';

export function BlockerIcon({ blockerName, Icon }: { blockerName: string; Icon: React.ReactNode }) {
  return (
    <Tooltip
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: '#fff',
          },
        },
      }}
      placement="top"
      title={
        <Typography variant="body1Emphasis" fontSize={14} lineHeight="14px" color={grey[800]}>
          {blockerName}
        </Typography>
      }
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {Icon}
      </Box>
    </Tooltip>
  );
}

const RenderProjectsWithNoJob = React.memo(function RenderProjectsWithNoJob() {
  const { projects } = useAllProjects();
  const selectedProject = useSelectedProject();
  const focusedProjectInStore = useFocusedProject();
  const highlightFlexibleProjects = useHighlightFlexibleProjects();
  const highlightProjectsWithinDistance = useHighlightProjectsWithinDistance();
  const { setSelectedProject, setHoveredProject, setShowRightSidebar, setProjectForNoBaselineHoursModal } =
    useProjectActions();

  const getProjectsWithNoJobs = (
    allProjects: FullInstallationProjectEntity[],
    focusedProject?: FullInstallationProjectEntity,
  ) =>
    allProjects
      .filter(
        (project) =>
          (focusedProject === undefined ||
            focusedProject.installationProject?.id === project.installationProject?.id) &&
          project.installationProject &&
          STAGES_TO_SHOW_IN_UNSCHEDULED_JOBS.includes(project.installationProject.stage) &&
          project.installationProject.jobs.length < 1,
      )
      .map((project) => ({
        ...project,
      }))
      .sort((a, b) => {
        if (a.installationProject?.stage && b.installationProject?.stage) {
          // sort first by stage then by name
          if (a.installationProject.stage === b.installationProject.stage) {
            return a.contact?.firstName?.localeCompare(b.contact?.firstName ?? '') ?? 0;
          }
          return a.installationProject.stage - b.installationProject.stage;
        }
        return 0;
      });

  const projectsWithNoJobs = useMemo(
    () => getProjectsWithNoJobs(projects, focusedProjectInStore),
    [projects, focusedProjectInStore],
  );

  if (projectsWithNoJobs.length === 0) {
    return null;
  }

  return (
    <Stack
      spacing={1}
      alignItems="flex-start"
      maxWidth="320px"
      gap="2px"
      py={2}
      pl={3}
      sx={{
        overflow: 'auto',
        overflowX: 'hidden',
      }}
    >
      {projectsWithNoJobs.map((project) => {
        const { energySolution } = project;
        const coordinates = project.address?.specificDetails?.geometry;
        const isSelected = selectedProject?.installationProject?.id?.value === project.installationProject?.id?.value;
        const customerFlexibleBookingTime = () =>
          energySolution?.discounts.some((discount) => flexibleBookingDiscountNames.includes(discount.name));

        const flexibleBookTimeStyles = () => {
          if (highlightFlexibleProjects && customerFlexibleBookingTime()) {
            return {
              animation: 'shake 300ms infinite linear',
              boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.25);',
            };
          }
          return {};
        };

        const isOnHold = project?.installationProject?.progressOverview?.currentBlockers?.some(
          (blocker) => blocker.blocker?.$case === 'onHold',
        );

        const isInSalesRecovery = project?.installationProject?.progressOverview?.currentBlockers?.some(
          (blocker) => blocker.blocker?.$case === 'inSalesRecovery',
        );
        let distanceToSelectedProject;
        let isWithinDistanceOfSelected = false;
        const backgroundColor = getBackgroundColor({
          installationProject: project.installationProject,
          isSelected: true,
        });
        let outlineStyles = {};
        if (!isSelected && selectedProject && highlightProjectsWithinDistance) {
          if (coordinates && selectedProject?.address?.specificDetails?.geometry) {
            distanceToSelectedProject = getDistanceBetweenTwoLatLong(
              selectedProject?.address?.specificDetails?.geometry,
              coordinates,
            );
            isWithinDistanceOfSelected = distanceToSelectedProject < highlightProjectsWithinDistance;
            if (isWithinDistanceOfSelected) {
              outlineStyles = {
                outlineWidth: '1px',
                outlineOffset: '0',
                outlineColor: 'rgba(0, 130, 206, 0.75)',
                outlineStyle: 'solid',
                animation: 'animateOutline 2s ease infinite',
              };
            }
          }
        }

        return (
          <Stack
            onClick={() => {
              if (isSelected) {
                setShowRightSidebar(false);
                setSelectedProject(undefined);
              } else {
                setSelectedProject(project);
                setShowRightSidebar(true);
              }
            }}
            onMouseEnter={() => {
              setHoveredProject(project);
            }}
            onMouseLeave={() => {
              setHoveredProject(undefined);
            }}
            sx={{
              flexDirection: 'row',

              cursor: 'pointer',
              width: '260px',
              height: '42px',
              minHeight: '42px',
              padding: '8px',
              justifyContent: 'space-between',
              alignItems: 'center',
              borderBottom: '1px solid #D3D8D9',

              ...outlineStyles,
              ...flexibleBookTimeStyles(),
            }}
            key={`${project.installationProject?.id?.value ?? ''}unscheduled`}
          >
            <Stack direction="row" alignItems="center" gap="8px">
              <Box
                sx={{
                  height: '10px',
                  width: '10px',
                  borderRadius: '50%',
                  backgroundColor: backgroundColor?.background,
                }}
              />
              <Typography
                variant="body1Emphasis"
                fontSize={12}
                lineHeight="14px"
                sx={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  WebkitLineClamp: 2,
                  maxHeight: '28px',
                }}
              >
                {project.contact?.firstName} {project.contact?.lastName}
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="center" gap="4px">
              {isOnHold && (
                <BlockerIcon
                  blockerName="On Hold"
                  Icon={<PauseOutlinedIcon color={grey[800]} width={16} height={16} />}
                />
              )}
              {isInSalesRecovery && (
                <BlockerIcon
                  blockerName="In Sales Recovery"
                  Icon={<FaceFrowningCircleOutlinedIcon color={grey[800]} width={16} height={16} />}
                />
              )}

              <IconButton
                onClick={() => {
                  setProjectForNoBaselineHoursModal(project);
                }}
              >
                <AddOutlinedIcon color={grey[800]} width={16} height={16} />
              </IconButton>
            </Stack>
          </Stack>
        );
      })}
    </Stack>
  );
});

export default RenderProjectsWithNoJob;
