import { I<PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material';
import { useAllProjects } from '../contexts/AllProjects';
import { useProjectActions } from '../stores/ProjectStore';
import { grey } from '@ui/theme/colors';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { useMemo } from 'react';
import { InstallationProjectJob_WorkSegmentPlannedType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

export default function RenderIncompleteProjectsWithNoHours() {
  const { projectsWithIncompleteInstallation } = useAllProjects();
  const { setProjectForIncompleteHoursModal, setShowRightSidebar, setSelectedProject } = useProjectActions();

  const incompleteProjectsWithNoHours = useMemo(() => {
    return projectsWithIncompleteInstallation.filter((project) => {
      return !project.installationProject?.jobs.some(
        (job) =>
          (job.workSegments.length === 0 &&
            job.workSegments.some(
              (segment) =>
                segment.plannedType ===
                InstallationProjectJob_WorkSegmentPlannedType.WORK_SEGMENT_PLANNED_TYPE_EXTENSION,
            )) ||
          (job.workSegments.some((segment) => segment.startTime) &&
            job.workSegments.some((segment) => !segment.startTime)),
      );
    });
  }, [projectsWithIncompleteInstallation]);

  return (
    <Stack
      spacing={1}
      alignItems="flex-start"
      maxWidth="320px"
      gap="2px"
      py={2}
      pl={3}
      sx={{
        overflow: 'auto',
        overflowX: 'hidden',
      }}
    >
      {incompleteProjectsWithNoHours.map((project) => (
        <Stack
          key={project.installationProject?.id?.value}
          onClick={() => {
            setShowRightSidebar(true);
            setSelectedProject(project);
          }}
          sx={{
            flexDirection: 'row',
            cursor: 'pointer',
            minWidth: '252px',
            height: '42px',
            minHeight: '42px',
            padding: '8px',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: '1px solid #D3D8D9',
          }}
        >
          <Typography
            variant="body1Emphasis"
            fontSize={12}
            lineHeight="14px"
            sx={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              WebkitLineClamp: 2,
              maxHeight: '28px',
            }}
          >
            {project.contact?.firstName} {project.contact?.lastName}
          </Typography>
          <IconButton
            onClick={() => {
              setProjectForIncompleteHoursModal(project);
            }}
          >
            <AddOutlinedIcon color={grey[800]} width={16} height={16} />
          </IconButton>
        </Stack>
      ))}
    </Stack>
  );
}
