import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Icon<PERSON>utton, Tab } from '@mui/material';
import { ChevronRight } from '@ui/components/Icons/Chevron/Chevron';
import { beige, grey } from '@ui/theme/colors';
import RenderProjectsWithNoJob from './RenderProjectsWithNoJob';
import { resourceMapping, reverseResourceMapping } from '../types/planningTypes';
import { useFocusedProject, useShowUnscheduledJobsSidebar, useProjectActions } from '../stores/ProjectStore';
import RenderUnscheduledWithBaseline from './RenderUnscheduledWithBaseline';
import { InstallationProjectJob_JobResourceRole } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import RenderIncompleteProjects from './RenderIncompleteProjectsWithNoHours';
import RenderIncompleteProjectsUnscheduled from './RenderIncompleteProjectsUnscheduled';
import { InstallationProjectStage } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

export const STAGES_TO_SHOW_IN_UNSCHEDULED_JOBS = [
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INITIALIZED,
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW,
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_SURVEY,
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_DESIGN,
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION,
  InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION,
];

export default function UnscheduledJobsSidebar({ requiredRoles }: { requiredRoles: number[] }) {
  const [topTab, setTopTab] = useState<'upcoming' | 'incomplete'>('upcoming');
  const [bottomTab, setBottomTab] = useState<'noJob' | 'unscheduled'>('unscheduled');
  const focusedProject = useFocusedProject();
  const showUnscheduledJobsSidebar = useShowUnscheduledJobsSidebar();
  const { setShowUnscheduledJobsSidebar } = useProjectActions();

  useEffect(() => {
    if (focusedProject) {
      const focusedProjectInstallerJob = focusedProject.installationProject?.jobs.find(
        (job) => job.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER,
      );
      const focusedProjectHasBaseline =
        focusedProjectInstallerJob?.workSegments.length && focusedProjectInstallerJob.workSegments.length > 0;

      const focusedProjectHasUnscheduledJob = focusedProjectInstallerJob?.workSegments.some(
        (workSegment) => !workSegment.startTime,
      );

      const focusedProjectHasIncompleteInstallation =
        focusedProject.installationProject?.progressOverview?.currentBlockers.some(
          (blocker) =>
            blocker.blocker?.$case === 'openTicket' &&
            blocker.blocker.openTicket.name.includes('⚠️ Installation Incomplete ⚠️'),
        );

      if (focusedProjectHasIncompleteInstallation) {
        setTopTab('incomplete');
        if (focusedProjectHasUnscheduledJob) {
          setBottomTab('unscheduled');
        } else {
          setBottomTab('noJob');
        }
      } else if (!focusedProjectInstallerJob) {
        setTopTab('upcoming');
        setBottomTab('noJob');
      } else {
        if (focusedProjectHasUnscheduledJob && focusedProjectHasBaseline) {
          setTopTab('upcoming');
          setBottomTab('unscheduled');
        }
      }
    }
  }, [focusedProject]);

  if (!showUnscheduledJobsSidebar) {
    return (
      <Stack
        alignItems="center"
        sx={{
          background: beige[100],
          borderRight: '1px solid #D3D8D9',
          pt: 2,
          width: '66px',
          minWidth: '66px',
        }}
      >
        <IconButton
          onClick={() => setShowUnscheduledJobsSidebar(true)}
          sx={{
            borderRadius: '50%',
            padding: '8px',
            width: '40px',
            ':hover': {
              background: beige[200],
            },
          }}
        >
          <ChevronRight height={24} width={24} />
        </IconButton>
      </Stack>
    );
  }
  return (
    <Stack
      sx={{
        background: '#fff',
        margin: 0,
        padding: 0,
        height: 'calc(100dvh - 68px)',
        width: '320px',
        minWidth: '320px',
        borderRight: '1px solid #D3D8D9',
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Tab
          label="Upcoming"
          onClick={() => setTopTab('upcoming')}
          sx={{
            fontSize: '12px',
            fontWeight: '500',

            height: '36px',
            width: '50%',
            padding: '8px 12px',
            opacity: 1,
            color: '#000',
            background: topTab === 'upcoming' ? 'transparent' : `${beige[150]}`,
            ':hover': {
              background: 'transparent',
            },
          }}
        />
        <Tab
          label="Incomplete"
          onClick={() => setTopTab('incomplete')}
          sx={{
            fontSize: '12px',
            fontWeight: '500',
            height: '36px',
            width: '50%',
            padding: '8px 12px',
            color: '#000',
            opacity: 1,
            background: topTab === 'incomplete' ? 'transparent' : beige[150],
            ':hover': {
              background: 'transparent',
            },
          }}
        />
      </Stack>
      <Stack px={3} py={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" pb={3} gap="4px">
          <Tab
            onClick={() => setBottomTab('noJob')}
            label="No Hours"
            sx={{
              fontSize: '12px',
              fontWeight: '500',
              height: '36px',
              padding: '8px 12px',
              width: '50%',
              flexShrink: 0,
              opacity: 1,
              color: bottomTab === 'noJob' ? `${grey[900]}` : `${grey[700]}`,
              borderBottom: bottomTab === 'noJob' ? `2px solid ${grey[900]}` : '2px solid transparent',
              background: 'transparent',
              marginRight: '8px',
              ':hover': {
                background: 'transparent',
                color: `${grey[900]}`,
              },
            }}
          />
          <Tab
            label="With Hours"
            onClick={() => setBottomTab('unscheduled')}
            sx={{
              fontSize: '12px',
              fontWeight: '500',
              height: '36px',
              width: '50%',
              flexShrink: 0,
              padding: '8px 12px',
              opacity: 1,
              color: bottomTab === 'unscheduled' ? `${grey[900]}` : `${grey[700]}`,
              borderBottom: bottomTab === 'unscheduled' ? `2px solid ${grey[900]}` : '2px solid transparent',
              background: 'transparent',
              ':hover': {
                color: `${grey[900]}`,
              },
            }}
          />
        </Stack>
      </Stack>

      {bottomTab === 'noJob' && (topTab === 'upcoming' ? <RenderProjectsWithNoJob /> : <RenderIncompleteProjects />)}

      {bottomTab === 'unscheduled' && (
        <Stack
          spacing={0}
          sx={{
            overflow: 'auto',
            position: 'relative',
            width: '100%',
          }}
        >
          {requiredRoles.map((requiredRole) => {
            const resourceType = reverseResourceMapping[requiredRole as keyof typeof reverseResourceMapping];
            return topTab === 'upcoming' ? (
              <RenderUnscheduledWithBaseline
                key={`${resourceType}unscheduled`}
                requiredRole={resourceMapping[resourceType]}
              />
            ) : (
              <RenderIncompleteProjectsUnscheduled
                key={`${resourceType}unscheduled`}
                requiredRole={resourceMapping[resourceType]}
              />
            );
          })}
        </Stack>
      )}
    </Stack>
  );
}
