import { FormControlLabel, Switch as MuiSwitch, SwitchProps } from '@mui/material';
import { grey } from '@ui/theme/colors';

export function Switch({ checked, onChange, name, label }: { label: string } & SwitchProps) {
  return (
    <FormControlLabel
      control={
        <MuiSwitch
          checked={checked}
          onChange={onChange}
          name={name}
          color="secondary"
          sx={{
            width: '40px',
            height: '24px',
            padding: 0,
            my: 'auto',
            '& .MuiSwitch-track': {
              height: '24px',
              width: '42px',
              backgroundColor: checked ? '#FFAF51' : 'rgba(34, 34, 38, 0.08)',
              opacity: 1,
              borderRadius: '100px',
            },
            '& .MuiSwitch-thumb': {
              position: 'relative',
              width: '18px',
              height: '18px',
              left: checked ? '0px' : '2px',
              boxShadow: 'none',
              borderRadius: 26 / 2,
              top: '3px',
              border: 'none',
              backgroundColor: checked ? '#ffffff' : '#ffffff',
            },
            '& .MuiTouchRipple-root': {
              display: 'none !important',
            },
            '& .MuiSwitch-input': {
              height: '24px',
              width: '42px',
            },
            '& .MuiSwitch-switchBase': {
              padding: 0,
              '&:hover': {
                backgroundColor: 'transparent',
              },
            },
          }}
        />
      }
      sx={{
        backgroundColor: 'rgba(34, 34, 38, 0.03)',
        borderRadius: '100px',
        px: 2,
        py: '8px',
        mx: 0,
        '.MuiFormControlLabel-label': {
          fontSize: '12px',
          lineHeight: '150%',
          fontWeight: 500,
          color: grey[900],
          mr: 2,
        },
      }}
      label={label}
      labelPlacement="start"
    />
  );
}
