import { IconButton, Typography } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@ui/components/Icons/Chevron/Chevron';
import { ReactDatePickerCustomHeaderProps } from 'react-datepicker';

export default function DatePickerCustomHeader({
  date,
  decreaseMonth,
  increaseMonth,
}: ReactDatePickerCustomHeaderProps) {
  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 10px' }}>
      <IconButton onClick={decreaseMonth} aria-label="Previous Month" sx={{ padding: 0 }}>
        <ChevronLeft height={20} width={20} />
      </IconButton>
      <Typography variant="body2Emphasis">
        {date.toLocaleString('default', { month: 'long', year: 'numeric' })}
      </Typography>
      <IconButton onClick={increaseMonth} aria-label="Next Month" sx={{ padding: 0 }}>
        <ChevronRight height={20} width={20} />
      </IconButton>
    </div>
  );
}
