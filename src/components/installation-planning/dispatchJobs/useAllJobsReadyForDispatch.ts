import { InstallationProjectJob_JobStatus } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useAllProjects } from '../contexts/AllProjects';

export default function useAllJobsReadyForDispatch() {
  const { projects } = useAllProjects();
  const jobsReadyToBeDispatched: Record<string, FullInstallationProjectEntity> = {};
  projects.forEach((project) => {
    project.installationProject?.jobs?.forEach((job) => {
      if (job.status === InstallationProjectJob_JobStatus.JOB_STATUS_SCHEDULED && job.assignedResources.length > 0) {
        jobsReadyToBeDispatched[job.id!.value] = project;
      }
    });
  });

  return jobsReadyToBeDispatched;
}
