import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import { Button } from '@ui/components/Button/Button';
import { FormattedMessage } from 'react-intl';
import toast from 'react-hot-toast';
import { api } from 'utils/api';
import { useState } from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProjectJob_JobStatus } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { CrossSmallOutlinedIcon } from '@ui/components/StandardIcons/CrossSmallOutlinedIcon';
import { useDispatchMode, useJobsToDispatch, useProjectActions } from '../stores/ProjectStore';
import useAllJobsReadyForDispatch from './useAllJobsReadyForDispatch';
import { useProjectsUpdatedSince } from '../contexts/ProjectsUpdatedSinceContext';
import { useRegionContext } from '../../../context/RegionContext';

export default function DispatchjobsBar() {
  const dispatchMode = useDispatchMode();
  const [isDispatching, setIsDispatching] = useState(false);
  const jobsToDispatch = useJobsToDispatch();
  const { setJobsToDispatch, setDispatchMode } = useProjectActions();
  const allJobsReadyForDispatch = useAllJobsReadyForDispatch();
  const { refetchProjectsUpdatedSince, initialLoadTime } = useProjectsUpdatedSince();
  const { id: regionId } = useRegionContext();
  const utils = api.useUtils();
  const jobsIds = Object.keys(jobsToDispatch);

  const { mutateAsync: dispatchJobs } = api.InstallationProject.dispatchInstallationProjectJob.useMutation();

  if (!dispatchMode) return null;

  const optimisticUpdate = (jobsToUpdate: string[]) => {
    utils.InstallationProject.getInstallationProjects.setData(
      { operationalUnitId: regionId!.value, updatedAfter: initialLoadTime },
      (oldData) => {
        if (!oldData) return [];
        return oldData.map((project) => {
          const hasJobsJustDispatched = project.installationProject?.jobs?.some((job) =>
            jobsToUpdate.includes(job.id?.value ?? ''),
          );
          if (!hasJobsJustDispatched) return project as FullInstallationProjectEntity;
          return {
            ...(project as FullInstallationProjectEntity),
            installationProject: {
              ...project.installationProject,
              jobs:
                project.installationProject?.jobs?.map((job) => {
                  if (!jobsToUpdate.includes(job.id?.value ?? '')) return job;
                  return {
                    ...job,
                    status: InstallationProjectJob_JobStatus.JOB_STATUS_READY,
                  };
                }) ?? [],
            } as FullInstallationProjectEntity['installationProject'],
          };
        });
      },
    );
  };

  const handleDispatch = async () => {
    setIsDispatching(true);
    let jobs = jobsIds;
    if (jobsIds.length === 0) {
      jobs = Object.keys(allJobsReadyForDispatch);
    }

    const dispatchPromises = jobs.map(
      (jobId) =>
        dispatchJobs({ jobId })
          .then(() => null) // No need for specific success tracking in this array
          .catch(() => jobId), // Return jobId if it fails
    );
    const results = await Promise.allSettled(dispatchPromises);
    const failedJobs: string[] = [];
    const successfulJobs: string[] = [];
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        failedJobs.push(jobs[index]!); // Add the jobId of the failed job to the list
      } else {
        successfulJobs.push(jobs[index]!);
      }
    });
    if (failedJobs.length > 0) {
      const failedJobsNames: string[] = [];
      for (const jobId of failedJobs) {
        if (jobsIds.length === 0)
          failedJobsNames.push(
            `${allJobsReadyForDispatch?.[jobId]?.contact?.firstName} ${allJobsReadyForDispatch?.[jobId]?.contact?.lastName}`,
          );
        else
          failedJobsNames.push(
            `${jobsToDispatch?.[jobId]?.contact?.firstName} ${jobsToDispatch?.[jobId]?.contact?.lastName}`,
          );
      }
      optimisticUpdate(successfulJobs);
      toast.error(
        <>
          <FormattedMessage
            id="installationPlanning.dispatchJobs.failedToDispatchJobs"
            defaultMessage="Failed to dispatch the following jobs: "
          />
          {failedJobsNames.join(', ')}
        </>,
        { position: 'bottom-center' },
      );
      const newJobsToDispatch: Record<string, FullInstallationProjectEntity> = {};
      failedJobs.forEach((jobId) => {
        if (!jobsToDispatch?.[jobId]) return;
        newJobsToDispatch[jobId] = jobsToDispatch?.[jobId];
      });
      setJobsToDispatch(newJobsToDispatch);
    } else {
      setJobsToDispatch({});
      optimisticUpdate(successfulJobs);
      toast.success(
        <FormattedMessage
          id="installationPlanning.dispatchJobs.successfullyDispatchedJobs"
          defaultMessage="Jobs dispatched successfully"
        />,
      );
    }

    refetchProjectsUpdatedSince();
    setIsDispatching(false);
  };

  return (
    <Stack
      sx={{
        height: 'fit-content',
        width: '300px',
        position: 'absolute',
        bottom: 60,
        left: 'calc(50% - 150px)',
        gap: '16px',
        backgroundColor: '#ffffff',
        zIndex: 4000,
        borderRadius: '8px',
        boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
        padding: '24px',
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Typography variant="body2Emphasis">
          <FormattedMessage id="installationPlanning.dispatchJobs.title" defaultMessage="Dispatch mode" />
        </Typography>
        <IconButton
          sx={{
            marginRight: '-14px',
          }}
          onClick={() => {
            setJobsToDispatch({});
            setDispatchMode(false);
          }}
        >
          <CrossSmallOutlinedIcon />
        </IconButton>
      </Stack>
      <Typography variant="body2">
        <FormattedMessage
          id="installationPlanning.dispatchJobs.description"
          defaultMessage="Click a job with assignees to dispatch it or dispatch all below."
        />
      </Typography>

      <Button
        variant="contained"
        sx={{
          width: '100%',
          height: '40px',
          borderRadius: '8px',
        }}
        onClick={handleDispatch}
        disabled={isDispatching}
      >
        {jobsIds.length > 0 ? (
          <>
            <FormattedMessage
              id="installationPlanning.dispatchJobs.dispatchSelected"
              defaultMessage="Dispatch selected"
            />
            {` (${jobsIds.length})`}
          </>
        ) : (
          <FormattedMessage id="installationPlanning.dispatchJobs.dispatchAll" defaultMessage="Dispatch all" />
        )}
        {isDispatching && <CircularProgress size={24} />}
      </Button>
    </Stack>
  );
}
