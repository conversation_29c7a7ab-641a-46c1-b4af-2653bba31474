import { Box, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { ScissorIcon } from '@ui/components/Icons/ScissorIcon/ScissorIcon';
import { useRouter } from 'next/router';
import { handleSplitSegmentClick } from '../helpers/continuousSegmentSplitter';
import { useProjectActions } from '../stores/ProjectStore';
import { ContinuousWorkSegmentForJobItem } from '../types/planningTypes';

export default function JobSplitter({
  position,
  segmentDetails,
}: {
  position: {
    x: number;
    y: number;
    splitTime: Date | undefined;
    segmentIndex: number | undefined;
  };
  segmentDetails: ContinuousWorkSegmentForJobItem['segmentDetails'];
}) {
  const router = useRouter();
  const { locale } = router;
  const { setSplitSegmentsToMove } = useProjectActions();
  return (
    <Box
      sx={{
        position: 'absolute',
        height: '81px',
        borderLeft: `2px solid ${grey[900]}`,
        left: position.x,
        top: -40,
        zIndex: 4200,
        cursor: 'none', //
      }}
      onClick={(e) => {
        e.stopPropagation();
        const newSplitSegmentsToMove = handleSplitSegmentClick(position, segmentDetails);
        if (newSplitSegmentsToMove) {
          setSplitSegmentsToMove(newSplitSegmentsToMove);
        }
      }}
    >
      {/* Custom Cursor Icon */}
      <div
        style={{
          position: 'absolute',
          left: -1,
          top: 60,
          transform: 'translate(-50%, -50%)',
          width: '30px', // Size of custom cursor
          height: '30px',
          borderRadius: '50%', // Make it circular
          pointerEvents: 'none', // Prevent interference with mouse events
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white', // Icon color
          fontSize: '16px', // Icon font size
        }}
      >
        <ScissorIcon color={grey[900]} />
      </div>
      {position.splitTime && (
        <Typography
          variant="body1Emphasis"
          fontSize={12}
          lineHeight="14px"
          color={grey[900]}
          sx={{
            position: 'absolute',
            top: -30,
            left: -24,

            padding: '8px',
            borderRadius: '8px',
          }}
        >
          {`${position.splitTime?.toLocaleTimeString(locale, { hour: '2-digit', minute: '2-digit' })}`}
        </Typography>
      )}
    </Box>
  );
}
