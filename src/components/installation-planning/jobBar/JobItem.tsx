import { Box, Stack, Tooltip, Typography } from '@mui/material';
import { grey } from '@ui/theme/colors';
import { memo, useEffect, useRef, useState } from 'react';
import { FaceFrowningCircleOutlinedIcon } from '@ui/components/StandardIcons/FaceFrowningCircleOutlinedIcon';
import { FlameOutlinedIcon } from '@ui/components/StandardIcons/FlameOutlinedIcon';
import { PauseOutlinedIcon } from '@ui/components/StandardIcons/PauseOutlinedIcon';
import { PeopleIcon } from '@ui/components/Icons/PeopleIcon/PeopleIcon';
import {
  InstallationProjectJob_JobResourceRole,
  InstallationProjectJob_JobStatus,
  InstallationProjectJob_WorkSegment,
  InstallationProjectStage,
} from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { ContinuousWorkSegment, ContinuousWorkSegmentForJobItem, UnsavedChange } from '../types/planningTypes';
import getDistanceBetweenTwoLatLong from '../helpers/getDistanceBetweenTwoLatLong';
import flexibleBookingDiscountNames from '../helpers/flexibleBookingDiscountNames';
import { useRegionContext } from '../../../context/RegionContext';
import SplitSegments from './SplitSegments';
import { handleMouseMove } from '../helpers/continuousSegmentSplitter';
import JobSplitter from './JobSplitter';
import { getCountryCode } from '../helpers/getCountryCode';
import { getBackgroundColor, getToolTipPosition } from './jobItemHelpers';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';

export type Position = {
  x: number;
  y: number;
  splitTime: Date | undefined;
  segmentIndex: number | undefined;
};

const JobItem = memo(function JobItem({
  canEdit,
  continuousWorkSegment,
  incomplete,
  resourceId,
  setRightClickMenu,
  setHoveredProject,
  setSelectedProject,
  setJobBeingSplit,
  setShowRightSidebar,
  setSplitSegmentsToMove,
  setToolTipProject,
  setJobsToDispatch,
  dispatchMode,
  jobsToDispatch,
  dayWidth,
  unsavedChanges,
  splitSegmentsToMove,
  focusedProject,
  selectedProject,
  jobBeingSplit,
  highlightProjectsWithinDistance,
  highlightFlexibleProjects,
  hoveredUnsavedChangeJobId,
}: {
  canEdit: boolean;
  continuousWorkSegment: ContinuousWorkSegmentForJobItem;
  incomplete?: boolean;
  resourceId?: string;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    resourceId?: string;
    position: {
      top: number;
      right: number;
      arrowTop: number;
    };
  }) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      jobId: string;
      position: {
        x: number;
        y: number;
        up: boolean;
      };
      project: ContinuousWorkSegmentForJobItem;
      hasAssignedResources: boolean;
    },
  ) => void;
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setJobBeingSplit: (jobId?: ContinuousWorkSegmentForJobItem) => void;
  setShowRightSidebar: (show: boolean) => void;
  setSplitSegmentsToMove: (
    segments: {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    } | null,
  ) => void;
  dispatchMode: boolean;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  dayWidth: number;
  unsavedChanges: UnsavedChange[];
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  focusedProject?: FullInstallationProjectEntity;
  selectedProject?: FullInstallationProjectEntity;
  jobBeingSplit?: ContinuousWorkSegmentForJobItem;
  highlightProjectsWithinDistance?: number;
  highlightFlexibleProjects: boolean;
  hoveredUnsavedChangeJobId: string | null;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
}) {
  const { segmentDetails, contact, address, installationProject, energySolution, hasAssignedResources } =
    continuousWorkSegment;

  const boxRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const { timeZone, iso3166 } = useRegionContext();

  const [position, setPosition] = useState<Position>({ x: 0, y: 0, splitTime: undefined, segmentIndex: undefined });

  const isDE = iso3166?.$case === 'country' ? getCountryCode(iso3166.country) === 'DE' : false;

  useEffect(() => {
    // if the job is being split and the user presses escape, cancel the split
    const handleKeyDown = (event: KeyboardEvent) => {
      if (jobBeingSplit && jobBeingSplit.segmentDetails.jobId === segmentDetails.jobId) {
        if (event.key === 'Escape') {
          setJobBeingSplit();
          setSplitSegmentsToMove(null);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [jobBeingSplit, segmentDetails.jobId, setJobBeingSplit, setSplitSegmentsToMove]);

  useEffect(() => {
    if (hoveredUnsavedChangeJobId === segmentDetails.jobId) {
      boxRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
    }
  }, [hoveredUnsavedChangeJobId, segmentDetails.jobId]);

  useEffect(() => {
    if (segmentDetails.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER) {
      if (focusedProject?.installationProject?.jobs.some((job) => job.id?.value === segmentDetails.jobId)) {
        boxRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
      }
    }
  }, [focusedProject, segmentDetails.jobId, segmentDetails.requiredRole]);

  const handleRightClick = (event: React.MouseEvent) => {
    event.preventDefault();
    if (!segmentDetails.startTime || !boxRef.current) return;
    setSelectedProject(continuousWorkSegment);
    const x = event.clientX;
    const y = event.clientY > window.innerHeight / 2 ? window.innerHeight - event.clientY : event.clientY;
    setRightClickMenu({
      jobId: segmentDetails.jobId,
      position: { x, y, up: event.clientY > window.innerHeight / 2 },
      project: continuousWorkSegment,
      hasAssignedResources,
    });
  };

  if (!contact) return null;
  const { firstName, lastName } = contact;
  const customerName = `${firstName} ${lastName}`;

  const project = {
    contact,
    address,
    installationProject,
    energySolution,
  };

  const isDispatched =
    segmentDetails.status === InstallationProjectJob_JobStatus.JOB_STATUS_READY ||
    segmentDetails.status === InstallationProjectJob_JobStatus.JOB_STATUS_IN_PROGRESS;

  const isFinished =
    segmentDetails.status === InstallationProjectJob_JobStatus.JOB_STATUS_FINISHED &&
    (installationProject?.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION ||
      installationProject?.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE ||
      installationProject?.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED);

  let isDeEmphasized = false;

  const isSelected = selectedProject?.installationProject?.id?.value === installationProject?.id?.value;

  const hasBeenEdited = unsavedChanges.some(
    (change: UnsavedChange) =>
      change.projectId === continuousWorkSegment.installationProject?.id?.value &&
      change.jobId === segmentDetails.jobId,
  );

  if (hoveredUnsavedChangeJobId !== null && hoveredUnsavedChangeJobId !== segmentDetails.jobId) {
    isDeEmphasized = true;
  }

  if (focusedProject) {
    if (focusedProject.installationProject?.id?.value !== installationProject?.id?.value) {
      isDeEmphasized = true;
    }
  }

  if (!resourceId && dispatchMode) {
    if (
      segmentDetails.status !== InstallationProjectJob_JobStatus.JOB_STATUS_SCHEDULED ||
      segmentDetails.assignedResources.length === 0
    ) {
      isDeEmphasized = true;
    }
  }

  const coordinates = project.address?.specificDetails?.geometry;
  let distanceToSelectedProject;
  let isWithinDistanceOfSelected = false;

  if (!isSelected && selectedProject && highlightProjectsWithinDistance) {
    if (coordinates && selectedProject?.address?.specificDetails?.geometry) {
      distanceToSelectedProject = getDistanceBetweenTwoLatLong(
        selectedProject?.address?.specificDetails?.geometry,
        coordinates,
      );
      isWithinDistanceOfSelected = distanceToSelectedProject < highlightProjectsWithinDistance;
      if (!isWithinDistanceOfSelected) {
        isDeEmphasized = true;
      }
    }
  }

  const customerFlexibleBookingTime = () =>
    energySolution?.discounts.some((discount) => flexibleBookingDiscountNames.includes(discount.name));

  if (highlightFlexibleProjects && !customerFlexibleBookingTime()) {
    isDeEmphasized = true;
  }

  const backgroundColor = getBackgroundColor({
    installationProject,
    isSelected,
    resourceId,
  });

  const durationInDays = Math.min(segmentDetails.duration ? segmentDetails.duration / 8 : 1, 5);

  const getWidth = () => {
    if (segmentDetails.startTime) {
      return `100%`;
    }
    return '260px';
  };

  const opacity = () => {
    if (!isDeEmphasized) return 1;
    return isSelected ? 0.65 : 0.3;
  };

  const oilRemovalNeeded =
    isDE &&
    energySolution?.products.some(
      (product) =>
        product.productDetails?.details?.$case === 'installationAddon' &&
        product.productDetails?.details?.installationAddon?.category === 'OIL_TANK_REMOVAL' &&
        product.productMode === 1,
    );

  const isOnHold = installationProject?.progressOverview?.currentBlockers?.some(
    (blocker) => blocker.blocker?.$case === 'onHold',
  );

  const isInSalesRecovery = installationProject?.progressOverview?.currentBlockers?.some(
    (blocker) => blocker.blocker?.$case === 'inSalesRecovery',
  );

  return (
    <Box
      ref={boxRef}
      onContextMenu={!resourceId && canEdit ? handleRightClick : undefined}
      sx={{
        display: 'flex',
        flexDirection: 'row',
        borderRadius: '8px',
        ...backgroundColor,
        height: '42px',
        maxHeight: '42px',
        marginLeft: segmentDetails.startTime ? '1px' : '0px',
        flexGrow: 1,
        cursor:
          !canEdit || jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId || resourceId ? 'pointer' : 'move',
        width: getWidth(),
        padding: '0px 8px',
        border:
          dispatchMode && jobsToDispatch?.[segmentDetails.jobId]
            ? '1px solid #000000'
            : `1px solid ${backgroundColor?.background}`,
        alignItems: 'center',
        textOverflow: 'ellipsis',
        userSelect: 'none',
        justifyContent: 'space-between',
        opacity: opacity(),
        ':hover': {
          border:
            dispatchMode && jobsToDispatch?.[segmentDetails.jobId]
              ? '1px solid #000000'
              : `1px solid ${backgroundColor?.background}`,
        },
      }}
      onMouseMove={(e) => {
        if (jobBeingSplit?.segmentDetails.jobId !== segmentDetails.jobId) return;
        const newPositionForSplit = handleMouseMove(e, segmentDetails, boxRef, timeZone);
        if (newPositionForSplit) {
          setPosition(newPositionForSplit);
        }
      }}
      onClick={(e) => {
        if (dispatchMode) {
          if (isDeEmphasized) {
            return;
          }
          if (jobsToDispatch?.[segmentDetails.jobId]) {
            const newJobsToDispatch = { ...(jobsToDispatch ?? {}) };
            delete newJobsToDispatch[segmentDetails.jobId];
            setJobsToDispatch(newJobsToDispatch);
            return;
          }
          const newJobsToDispatch = { ...(jobsToDispatch ?? {}), [segmentDetails.jobId]: continuousWorkSegment };
          setJobsToDispatch(newJobsToDispatch);
          return;
        }
        if (document.getElementById('add-resources-to-team-modal')) {
          return;
        }

        if (
          jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId ||
          tooltipRef.current?.contains(e.target as Node) ||
          (e.target as HTMLElement).classList.contains('job-item-menu')
        ) {
          // Ignore clicks inside tooltip
          return;
        }

        if (!segmentDetails.startTime) {
          setToolTipProject();
          setSelectedProject(project);
          setShowRightSidebar(true);
          return;
        }

        const newToolTipPosition = getToolTipPosition({
          jobItemRef: boxRef.current,
          durationInDays: segmentDetails.duration,
          resourceId,
        });

        setSelectedProject(project);

        if (newToolTipPosition) {
          setToolTipProject({
            segmentDetails,
            resourceId,
            position: newToolTipPosition,
          });
        }
      }}
      onMouseEnter={() => {
        setHoveredProject(project);
      }}
      onMouseLeave={() => {
        setHoveredProject();
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between" width="100%">
        <Typography
          variant="body1Emphasis"
          fontSize={12}
          lineHeight="14px"
          sx={{
            pl: '4px',
            py: 0,
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            WebkitLineClamp: segmentDetails.startTime ? 2 : 1,
            maxHeight: '28px', // 2 lines * 14px line height
            maxWidth: segmentDetails.startTime ? `calc(${durationInDays * 2 * dayWidth}px - 6px)` : '100%',
            color: grey[800],
          }}
        >
          {customerName}
        </Typography>
      </Stack>

      {!resourceId && hasAssignedResources && !isFinished && !incomplete && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '50px',
            padding: '2px',
            border: isDispatched ? `1px solid ${grey[800]}` : 'none',
          }}
        >
          <PeopleIcon color={grey[800]} width={16} height={16} />
        </Box>
      )}

      {isOnHold && (
        <Tooltip
          componentsProps={{
            tooltip: {
              sx: {
                backgroundColor: '#fff',
              },
            },
          }}
          title={
            <Typography variant="body1Emphasis" fontSize={14} lineHeight="14px" color={grey[800]}>
              On Hold
            </Typography>
          }
          placement="top"
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: !segmentDetails.startTime && segmentDetails.duration ? '8px' : '0px',
            }}
          >
            <PauseOutlinedIcon color={grey[800]} width={16} height={16} />
          </Box>
        </Tooltip>
      )}

      {isInSalesRecovery && (
        <Tooltip
          componentsProps={{
            tooltip: {
              sx: {
                backgroundColor: '#fff',
              },
            },
          }}
          placement="top"
          title={
            <Typography variant="body1Emphasis" fontSize={14} lineHeight="14px" color={grey[800]}>
              In Sales Recovery
            </Typography>
          }
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: !segmentDetails.startTime && segmentDetails.duration ? '8px' : '0px',
            }}
          >
            <FaceFrowningCircleOutlinedIcon color={grey[800]} width={16} height={16} />
          </Box>
        </Tooltip>
      )}

      {oilRemovalNeeded && !incomplete && (
        <Tooltip
          componentsProps={{
            tooltip: {
              sx: {
                backgroundColor: '#fff',
              },
            },
          }}
          title={
            <Typography variant="body1Emphasis" fontSize={14} lineHeight="14px" color={grey[800]}>
              Oil Removal Needed
            </Typography>
          }
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: !segmentDetails.startTime && segmentDetails.duration ? '8px' : '0px',
            }}
          >
            <FlameOutlinedIcon color={grey[800]} width={16} height={16} />
          </Box>
        </Tooltip>
      )}
      {!segmentDetails.startTime && segmentDetails.duration && (
        <Box
          sx={{
            display: 'flex',
            width: '22px',
            height: '22px',
            minWidth: '22px',
            minHeight: '22px',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '50%',
            background: grey[150],
          }}
        >
          <Typography variant="body1Emphasis" fontSize={10} lineHeight="14px" color={grey[800]}>
            {Math.max(durationInDays, 1).toFixed(0)}
          </Typography>
        </Box>
      )}

      {hasBeenEdited && (
        <Box
          sx={{
            height: '14px',
            width: '14px',
            borderRadius: '50%',
            border: `2px solid #fff`,
            backgroundColor,
            position: 'absolute',
            boxShadow: '0px px 2px 2px rgba(0, 0, 0, 0.25)',
            top: '-4px',
            right: '-4px',
          }}
        />
      )}
      {jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId &&
        !splitSegmentsToMove &&
        jobBeingSplit?.segmentDetails.startTime === segmentDetails.startTime && (
          <JobSplitter position={position} segmentDetails={segmentDetails} />
        )}
      {jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId &&
        splitSegmentsToMove &&
        jobBeingSplit?.segmentDetails.startTime === segmentDetails.startTime && <SplitSegments />}
    </Box>
  );
});

export default JobItem;
