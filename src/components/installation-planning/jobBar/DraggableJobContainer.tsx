import React from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useRef } from 'react';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  ResourceBasedSegments,
  UnsavedChange,
} from '../types/planningTypes';
import DraggableJob from './DraggableJob';

const DraggableJobContainer = ({
  CELL_HEIGHT,
  canEdit,
  continuousSegmentsByResource,
  continuousWorkSegment,
  dayWidth,
  dispatchMode,
  focusedProject,
  highlightFlexibleProjects,
  highlightProjectsWithinDistance,
  hoveredUnsavedChangeJobId,
  innerRowNumber,
  jobBeingDragged,
  jobBeingSplit,
  jobsToDispatch,
  initialLoadTime,
  refetchProjectsUpdatedSince,
  regionId,
  resourceId,
  selectedProject,
  setHoveredProject,
  setJobBeingDragged,
  setJobBeingSplit,
  setJobsToDispatch,
  setOverlappingSegmentsForResources,
  setRightClickMenu,
  setSelectedProject,
  setShowRightSidebar,
  setShowUnsavedChanges,
  setSplitSegmentsToMove,
  setToolTipProject,
  span,
  splitSegmentsToMove,
  startPositon,
  timeZone,
  unsavedChanges,
  unsavedChangesWithoutReason,
  updateUnsavedChanges,
  user,
}: {
  CELL_HEIGHT: number;
  canEdit: boolean;
  continuousSegmentsByResource: ResourceBasedSegments;
  continuousWorkSegment: ContinuousWorkSegment;
  dayWidth: number;
  dispatchMode: boolean;
  focusedProject?: FullInstallationProjectEntity;
  highlightFlexibleProjects: boolean;
  highlightProjectsWithinDistance?: number;
  hoveredUnsavedChangeJobId: null | string;
  innerRowNumber: number;
  jobBeingDragged?: ContinuousWorkSegment;
  jobBeingEdited?: ContinuousWorkSegmentForJobItem;
  jobBeingSplit?: ContinuousWorkSegmentForJobItem;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  initialLoadTime: Date;
  regionId: string;
  resourceId?: string;
  selectedProject?: FullInstallationProjectEntity;
  span: number;
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  startPositon: number;
  timeZone: string;
  unsavedChanges: UnsavedChange[];
  unsavedChangesWithoutReason: UnsavedChange[];
  user: UserIdentity;
  refetchProjectsUpdatedSince: () => void;
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setJobBeingDragged: (jobBeingDragged?: ContinuousWorkSegment) => void;
  setJobBeingSplit: (jobBeingSplit?: ContinuousWorkSegment) => void;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
  setOverlappingSegmentsForResources: (
    overlappingSegmentsForResources: Map<
      string,
      {
        overlappingSegments: {
          segments: {
            customerName: string;
            jobId: string;
            segment: InstallationProjectJob_WorkSegment;
          }[];
        };
      }[]
    >,
  ) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      hasAssignedResources: boolean;
      jobId: string;
      position: {
        up: boolean;
        x: number;
        y: number;
      };
      project: ContinuousWorkSegmentForJobItem;
    },
  ) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setShowRightSidebar: (show: boolean) => void;
  setShowUnsavedChanges: (showUnsavedChanges: boolean) => void;
  setSplitSegmentsToMove: (
    segments: null | {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    },
  ) => void;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    position: {
      arrowTop: number;
      right: number;
      top: number;
    };
    resourceId?: string;
  }) => void;
  updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const left = 190 + (startPositon * dayWidth) / 2 + 2;

  const noPointerEvents =
    jobBeingDragged && jobBeingDragged.segmentDetails.jobId !== continuousWorkSegment.segmentDetails.jobId;

  return (
    <div
      ref={containerRef}
      style={{
        background: noPointerEvents ? 'white' : 'transparent',
        height: `${CELL_HEIGHT - 4}px`,
        left: `${left}px`,
        marginTop: `${CELL_HEIGHT * (innerRowNumber - 1) + 2}px`,
        maxHeight: `${CELL_HEIGHT - 4}px`,
        maxWidth: `${(dayWidth / 2) * span - 4}px`,
        padding: '0px',
        pointerEvents: noPointerEvents ? 'none' : 'auto',
        position: 'absolute',
        width: `${(dayWidth / 2) * span - 4}px`,
      }}
    >
      <DraggableJob
        canEdit={canEdit}
        containerRef={containerRef}
        continuousSegmentsByResource={continuousSegmentsByResource}
        continuousWorkSegment={continuousWorkSegment}
        dayWidth={dayWidth}
        dispatchMode={dispatchMode}
        focusedProject={focusedProject}
        highlightFlexibleProjects={highlightFlexibleProjects}
        highlightProjectsWithinDistance={highlightProjectsWithinDistance}
        hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
        jobBeingSplit={jobBeingSplit}
        jobsToDispatch={jobsToDispatch}
        initialLoadTime={initialLoadTime}
        refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
        regionId={regionId}
        resourceId={resourceId}
        selectedProject={selectedProject}
        setHoveredProject={setHoveredProject}
        setJobBeingDragged={setJobBeingDragged}
        setJobBeingSplit={setJobBeingSplit}
        setJobsToDispatch={setJobsToDispatch}
        setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
        setRightClickMenu={setRightClickMenu}
        setSelectedProject={setSelectedProject}
        setShowRightSidebar={setShowRightSidebar}
        setShowUnsavedChanges={setShowUnsavedChanges}
        setSplitSegmentsToMove={setSplitSegmentsToMove}
        setToolTipProject={setToolTipProject}
        splitSegmentsToMove={splitSegmentsToMove}
        timeZone={timeZone}
        unsavedChanges={unsavedChanges}
        unsavedChangesWithoutReason={unsavedChangesWithoutReason}
        updateUnsavedChanges={updateUnsavedChanges}
        user={user}
      />
    </div>
  );
};

export default React.memo(DraggableJobContainer);
