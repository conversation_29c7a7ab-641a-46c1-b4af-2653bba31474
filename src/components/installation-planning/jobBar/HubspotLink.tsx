import { ListItemButton, ListItemIcon, ListItemText } from '@mui/material';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import { sendGTMEvent } from '@next/third-parties/google';
import Link from 'next/link';
import { grey } from '@ui/theme/colors';
import { api } from 'utils/api';
import { FormattedMessage } from 'react-intl';

export default function HubspotLink({
  installationProjectId,
  setRightClickMenu,
}: {
  installationProjectId: string;
  setRightClickMenu: (rightClickMenu: any) => void;
}) {
  const { data: hubspotLink } = api.Hubspot.getHubspotLink.useQuery(
    {
      installationProjectId,
    },
    {
      enabled: !!installationProjectId,
    },
  );

  if (!hubspotLink) return null;
  return (
    <Link
      href={hubspotLink}
      target="_blank"
      onClick={() => {
        sendGTMEvent({
          event: 'button_click',
          click_text: 'view_project_in_hubspot',
          app_name: 'installation_planning',
        });
        setRightClickMenu(null);
      }}
    >
      <ListItemButton sx={{ padding: '5px 10px', cursor: 'pointer' }}>
        <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
          <ArrowRightTopSquareOutlinedIcon height={16} width={16} color={grey[900]} />
        </ListItemIcon>
        <ListItemText>
          <FormattedMessage id="installationPlanning.viewProjectInHubspot" defaultMessage="View project in Hubspot" />
        </ListItemText>
      </ListItemButton>
    </Link>
  );
}
