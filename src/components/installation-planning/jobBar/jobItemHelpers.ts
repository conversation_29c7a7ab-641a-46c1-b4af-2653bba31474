import {
  InstallationProject,
  InstallationProjectStage,
} from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/model';
import { brandYellow, green, grey, magenta, teal } from '@ui/theme/colors';

export const getBackgroundColor = ({
  installationProject,
  isSelected,
  resourceId,
  isMapIcon,
}: {
  installationProject?: InstallationProject;
  isSelected: boolean;
  resourceId?: string;
  isMapIcon?: boolean;
}): { background: string; color?: string; ':hover'?: { background: string }; border?: string } | null => {
  if (!installationProject) return null;
  switch (true) {
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE:
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED:
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION:
      if (isSelected) {
        return {
          background: magenta[400],
          ...(isMapIcon && {
            color: magenta[700],
          }),
        };
      }
      return {
        background: magenta[300],
        ...(isMapIcon && {
          color: magenta[700],
        }),
        ':hover': {
          background: magenta[400],
        },
      };

    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION:
      if (isSelected) {
        return {
          background: brandYellow[400],
          ...(isMapIcon && {
            color: brandYellow[700],
          }),
        };
      }
      return {
        background: brandYellow[300],
        ...(isMapIcon && {
          color: brandYellow[700],
        }),
        ':hover': {
          background: brandYellow[400],
        },
      };
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW:
      if (isSelected) {
        return {
          background: grey[150],
          ...(isMapIcon && {
            color: grey[700],
          }),
        };
      }
      return {
        background: grey[100],
        ...(isMapIcon && {
          color: grey[700],
        }),
        ':hover': {
          background: grey[150],
        },
      };
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_SURVEY:
      if (isSelected) {
        return {
          background: grey[300],
          ...(isMapIcon && {
            color: grey[700],
          }),
        };
      }
      return {
        background: grey[200],
        ...(isMapIcon && {
          color: grey[700],
        }),
        ':hover': {
          background: grey[300],
        },
      };
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_DESIGN:
      if (isSelected) {
        if (resourceId) {
          return {
            background: teal[400],
            border: `1px solid ${teal[400]}`,
          };
        }
        return {
          background: teal[400],
          ...(isMapIcon && {
            color: teal[700],
          }),
        };
      }
      if (resourceId) {
        return {
          background: teal[300],
          border: `1px solid ${teal[400]}`,
        };
      }
      return {
        background: teal[300],
        ...(isMapIcon && {
          color: teal[700],
        }),
        ':hover': {
          background: teal[400],
        },
      };
    case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION:
      if (isSelected) {
        if (resourceId) {
          return {
            background: green[400],
            border: `1px solid ${green[400]}`,
          };
        }
        return {
          background: green[400],
          ...(isMapIcon && {
            color: green[700],
          }),
        };
      }
      if (resourceId) {
        return {
          background: green[300],
          ':hover': {
            background: green[400],
          },
          border: `1px solid ${green[400]}`,
        };
      }
      return {
        background: green[300],
        ...(isMapIcon && {
          color: green[700],
        }),
        ':hover': {
          background: green[400],
        },
      };

    default:
      if (isSelected) {
        if (resourceId) {
          return {
            background: grey[100],
            border: `1px solid ${grey[300]}`,
          };
        }
        return {
          background: grey[300],
          ...(isMapIcon && {
            color: grey[700],
          }),
        };
      }
      return {
        background: grey[200],
        ...(isMapIcon && {
          color: grey[700],
        }),
        ':hover': {
          background: grey[300],
        },
      };
  }
};

export const ARROW_SIZE = 8; // Size of the arrow in pixels
export const getToolTipPosition = ({
  jobItemRef,
  durationInDays,
  resourceId,
}: {
  jobItemRef: HTMLDivElement | null;
  durationInDays: number;
  resourceId?: string;
}) => {
  if (!jobItemRef) return null;

  const boxRect = jobItemRef.getBoundingClientRect();
  let tooltipWidth;
  switch (true) {
    case durationInDays > 5:
      tooltipWidth = 600;
      break;
    case durationInDays > 4:
      tooltipWidth = 560;
      break;
    case durationInDays > 3:
      tooltipWidth = 490;
      break;
    case durationInDays > 2:
      tooltipWidth = 450;
      break;
    default:
      tooltipWidth = 400;
  }

  const MARGIN = 20;
  const TOOLTIP_HEIGHT = resourceId ? 250 : 500;

  // Calculate right position with minimum to prevent left cutoff
  const rightPosition = Math.min(window.innerWidth - boxRect.left + 10, window.innerWidth - tooltipWidth - MARGIN);

  // Initial centered position
  let tooltipTop = boxRect.top - (TOOLTIP_HEIGHT - boxRect.height) / 2;

  // Ensure tooltip stays within viewport bounds
  const maxTop = window.innerHeight - TOOLTIP_HEIGHT - MARGIN;
  tooltipTop = Math.max(MARGIN, Math.min(tooltipTop, maxTop));

  // Calculate arrow position relative to job item
  const arrowTop = Math.max(
    ARROW_SIZE,
    Math.min(boxRect.top - tooltipTop + boxRect.height / 2, TOOLTIP_HEIGHT - ARROW_SIZE * 2) - 6,
  );

  return {
    top: tooltipTop,
    right: rightPosition,
    arrowTop,
  };
};
