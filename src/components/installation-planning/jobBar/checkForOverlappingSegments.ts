import { isSameDay } from 'date-fns';
import { ResourceBasedSegments } from '../types/planningTypes';

import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { tz } from '@date-fns/tz';

const checkForOverlappingSegmentsForResources = ({
  newWorkSegments,
  activeJobId,
  activeJobCustomerName,
  continuousSegmentsByResource,
  timeZone,
}: {
  newWorkSegments: InstallationProjectJob_WorkSegment[];
  activeJobId: string;
  activeJobCustomerName: string;
  continuousSegmentsByResource: ResourceBasedSegments;
  timeZone: string;
}) => {
  const overlappingSegmentsForResources = new Map<
    string,
    {
      overlappingSegments: {
        segments: {
          segment: InstallationProjectJob_WorkSegment;
          jobId: string;
          customerName: string;
        }[];
      };
    }[]
  >();

  for (const nws of newWorkSegments) {
    const assignedResources = nws.assignedResources.map((ar) => ar.userId?.value);
    for (const resId of assignedResources) {
      if (resId) {
        const resouceSegments = continuousSegmentsByResource.get(resId);
        if (resouceSegments) {
          for (const segment of resouceSegments.continuousWorkSegments) {
            if (segment.segmentDetails.jobId !== activeJobId) {
              for (const ws of segment.segmentDetails.workSegments) {
                if (isSameDay(ws.startTime!, nws.startTime!, { in: tz(timeZone) })) {
                  if (!overlappingSegmentsForResources.has(resId)) {
                    overlappingSegmentsForResources.set(resId, []);
                  }
                  overlappingSegmentsForResources.get(resId)?.push({
                    overlappingSegments: {
                      segments: [
                        {
                          segment: ws,
                          jobId: segment.segmentDetails.jobId,
                          customerName: `${segment.contact?.firstName} ${segment.contact?.lastName}`,
                        },
                        {
                          segment: nws,
                          jobId: activeJobId,
                          customerName: activeJobCustomerName,
                        },
                      ],
                    },
                  });
                }
              }
            }
          }
        }
      }
    }
  }
  return overlappingSegmentsForResources;
};

export default checkForOverlappingSegmentsForResources;
