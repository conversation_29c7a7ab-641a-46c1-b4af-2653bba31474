import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { InstallationProject } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import {
  InstallationProjectJob,
  InstallationProjectJob_WorkSegment,
  InstallationProjectJob_WorkSegmentStatus,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { BaseEventPayload, ElementDragType } from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { scrollJustEnoughIntoView } from '@atlaskit/pragmatic-drag-and-drop/element/scroll-just-enough-into-view';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { TZDate, tz } from '@date-fns/tz';
import {
  addDays,
  addHours,
  differenceInHours,
  differenceInSeconds,
  isBefore,
  isEqual,
  isSameDay,
  isSameHour,
  isWeekend,
  startOfDay,
} from 'date-fns';
import { findIndex } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import toast from 'react-hot-toast';
import { FormattedMessage } from 'react-intl';
import invariant from 'tiny-invariant';
import { api } from 'utils/api';
import { getWorkingHoursOnDate } from '../helpers/workingHours';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  ResourceBasedSegments,
  UnsavedChange,
  reverseResourceMapping,
} from '../types/planningTypes';
import JobItem from './JobItem';
import JobItemMoving from './JobItemMoving';
import checkForOverlappingSegmentsForResources from './checkForOverlappingSegments';

type State = { container: HTMLElement; type: 'preview' } | { type: 'idle' };

export default function DraggableJob({
  canEdit,
  containerRef,
  continuousSegmentsByResource,
  continuousWorkSegment,
  dayWidth,
  dispatchMode,
  focusedProject,
  highlightFlexibleProjects,
  highlightProjectsWithinDistance,
  hoveredUnsavedChangeJobId,
  jobBeingSplit,
  jobsToDispatch,
  incomplete,
  initialLoadTime,
  refetchProjectsUpdatedSince,
  regionId,
  resourceId,
  selectedProject,
  setHoveredProject,
  setJobBeingDragged,
  setJobBeingSplit,
  setJobsToDispatch,
  setOverlappingSegmentsForResources,
  setRightClickMenu,
  setSelectedProject,
  setShowRightSidebar,
  setShowUnsavedChanges,
  setSplitSegmentsToMove,
  setToolTipProject,
  splitSegmentsToMove,
  timeZone,
  unsavedChanges,
  unsavedChangesWithoutReason,
  updateUnsavedChanges,
  user,
}: {
  containerRef?: null | React.RefObject<HTMLDivElement>;
  continuousSegmentsByResource: ResourceBasedSegments;
  continuousWorkSegment: ContinuousWorkSegment & {
    segmentDetails: {
      duration?: number;
      endTime?: Date;
      jobId: string;
      requiredRole: number;
      startTime?: Date;
      workSegments: InstallationProjectJob_WorkSegment[];
    };
  };
  resourceId?: string;
  setJobBeingDragged: (job?: ContinuousWorkSegment) => void;
  setOverlappingSegmentsForResources: (
    overlappingSegmentsForResources: Map<
      string,
      {
        overlappingSegments: {
          segments: {
            customerName: string;
            jobId: string;
            segment: InstallationProjectJob_WorkSegment;
          }[];
        };
      }[]
    >,
  ) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setShowUnsavedChanges: (show: boolean) => void;
  updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;

  canEdit: boolean;
  incomplete?: boolean;
  initialLoadTime: Date;
  regionId: string;
  timeZone: string;
  unsavedChangesWithoutReason: UnsavedChange[];
  user: UserIdentity;
  refetchProjectsUpdatedSince: () => void;

  dayWidth: number;
  dispatchMode: boolean;
  focusedProject?: FullInstallationProjectEntity;
  highlightFlexibleProjects: boolean;
  highlightProjectsWithinDistance?: number;
  hoveredUnsavedChangeJobId: null | string;
  jobBeingSplit?: ContinuousWorkSegmentForJobItem;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  selectedProject?: FullInstallationProjectEntity;
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  unsavedChanges: UnsavedChange[];
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setJobBeingSplit: (jobId?: ContinuousWorkSegmentForJobItem) => void;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      hasAssignedResources: boolean;
      jobId: string;
      position: {
        up: boolean;
        x: number;
        y: number;
      };
      project: ContinuousWorkSegmentForJobItem;
    },
  ) => void;
  setShowRightSidebar: (show: boolean) => void;
  setSplitSegmentsToMove: (
    segments: null | {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    },
  ) => void;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    position: {
      arrowTop: number;
      right: number;
      top: number;
    };
    resourceId?: string;
  }) => void;
}) {
  const [state, setState] = useState<State>({ type: 'idle' });
  const { hasAssignedResources, segmentDetails } = continuousWorkSegment;

  const { mutateAsync: updateWorkSegments } =
    api.InstallationProject.updateInstallationProjectJobWorkSegments.useMutation();
  const utils = api.useUtils();

  const segmentRequiredRole = segmentDetails.requiredRole;
  const segmentProjectId = continuousWorkSegment.installationProject?.id?.value;
  const segmentJobId = segmentDetails.jobId;

  const segmentCurrentStartDateTime = segmentDetails.startTime
    ? segmentDetails.startTime.toISOString()
    : segmentDetails.incompleteHours
      ? 'incomplete'
      : 'unscheduled';

  const handleDragEnd = useCallback(
    async ({ location }: BaseEventPayload<ElementDragType>) => {
      const over = location.current.dropTargets[0]?.data as { id: string };
      if (!over) return;

      const { id: overId } = over;

      if (!segmentDetails) {
        setJobBeingDragged();
        return;
      }

      const newStartDate = new Date(overId.split('|')[0]!);
      const overRequiredRole = Number(overId.split('|')[1]);
      const newTeamId = overId.split('|')[3] !== '' ? overId.split('|')[3] : undefined;

      //////////////////////////////////////////////////////////////
      // Check if the job has been split or separate hours have been added
      const unsavedChange = unsavedChanges.find(
        (unsavedChange) => unsavedChange.projectId === segmentProjectId && unsavedChange.jobId === segmentJobId,
      );
      if (unsavedChange && ['split-segment', 'separate-hours-added'].includes(unsavedChange.typeOfChange ?? '')) {
        toast.error(
          'This job has been split or separate hours have been added. Please save you current changes first, then move the job.',
          { duration: 6000, position: 'top-center' },
        );
        setJobBeingDragged();
        return;
      }
      //////////////////////////////////////////////////////////////

      //////////////////////////////////////////////////////////////
      // Check if the job has been moved to a slot for a different role
      if (overRequiredRole !== segmentRequiredRole) {
        const toastText = `You cannot move ${reverseResourceMapping[Number(segmentRequiredRole) as keyof typeof reverseResourceMapping].toLowerCase()} job to a slot for ${reverseResourceMapping[Number(overRequiredRole) as keyof typeof reverseResourceMapping].toLowerCase()} jobs`;
        toast.error(toastText, { duration: 6000, position: 'top-center' });
        setJobBeingDragged();
        return;
      }
      //////////////////////////////////////////////////////////////

      const currentJob = continuousWorkSegment?.installationProject?.jobs.find(
        (job) => job.requiredRole === Number(segmentRequiredRole) && job.id?.value === segmentJobId,
      ) as InstallationProjectJob;

      let previousTeamId;
      const now = TZDate.tz(timeZone);

      if (!(currentJob && continuousWorkSegment) || !segmentJobId || !segmentProjectId) {
        setJobBeingDragged();
        return;
      }

      const newWorkSegments: InstallationProjectJob_WorkSegment[] = [];

      const { workEndsAt } = getWorkingHoursOnDate(newStartDate, timeZone);
      const maxLengthOfFirstDay = differenceInHours(workEndsAt, newStartDate);
      const lengthOfFirstDay = Math.min(maxLengthOfFirstDay, segmentDetails.duration);

      let startIdxWorkSegmentGroup = 0;
      let remainingDuration = 0;

      if (segmentCurrentStartDateTime !== 'unscheduled' && segmentCurrentStartDateTime !== 'incomplete') {
        // Find the start and end index of the work segment group being moved
        startIdxWorkSegmentGroup = findIndex(currentJob.workSegments, (workSegment) =>
          isSameDay(workSegment.startTime!, segmentDetails.startTime, { in: tz(timeZone) }),
        );

        if (
          currentJob.workSegments[startIdxWorkSegmentGroup]?.status ===
            InstallationProjectJob_WorkSegmentStatus.WORK_SEGMENT_STATUS_IN_PROGRESS ||
          currentJob.workSegments[startIdxWorkSegmentGroup]?.status ===
            InstallationProjectJob_WorkSegmentStatus.WORK_SEGMENT_STATUS_FINISHED
        ) {
          toast.error('Cannot move in-progress or finished work segments', { duration: 6000, position: 'top-center' });
          setJobBeingDragged();
          return;
        }
        previousTeamId = currentJob.workSegments[startIdxWorkSegmentGroup]?.teamId?.value;

        if (
          isSameHour(segmentCurrentStartDateTime, newStartDate, { in: tz(timeZone) }) &&
          newTeamId === previousTeamId
        ) {
          setJobBeingDragged();
          return;
        }

        const endIdxWorkSegmentGroup = findIndex(currentJob.workSegments, (workSegment) =>
          isSameDay(workSegment.endTime!, segmentDetails.endTime, { in: tz(timeZone) }),
        );

        // For all work segments outside of the group being moved, just add them to the newWorkSegments array
        currentJob.workSegments.forEach((workSegment, index) => {
          if (index < startIdxWorkSegmentGroup || index > endIdxWorkSegmentGroup) {
            newWorkSegments.push(workSegment);
          }
        });
      }

      if (segmentCurrentStartDateTime === 'incomplete') {
        // Find the start and end index of the work segment group being moved
        if (segmentDetails.workSegments[0]?.sequenceNumber) {
          startIdxWorkSegmentGroup = segmentDetails.workSegments[0]?.sequenceNumber - 1;
          // For all work segments before the start of the incomplete segments, add them to the newWorkSegments array
          currentJob.workSegments.forEach((workSegment, index) => {
            if (index < startIdxWorkSegmentGroup) {
              newWorkSegments.push(workSegment);
            }
          });
        } else {
          toast.error('Incomplete job has no sequence number', { duration: 6000, position: 'top-center' });
          setJobBeingDragged();
          return;
        }
      }

      // Add the first day of the work segment group being moved
      newWorkSegments.splice(startIdxWorkSegmentGroup, 0, {
        ...(currentJob.workSegments[startIdxWorkSegmentGroup] as InstallationProjectJob_WorkSegment),
        endTime: addHours(newStartDate, lengthOfFirstDay),
        startTime: newStartDate,
        teamId: newTeamId ? { value: newTeamId } : undefined,
      });

      // Handle past-dated start times
      if (isBefore(newStartDate, startOfDay(now))) {
        toast.error('The job cannot start in the past', { duration: 6000, position: 'top-center' });
        setJobBeingDragged();
        return;
      }

      // Add the remaining work segments
      let currentIndex = startIdxWorkSegmentGroup + 1;
      let currentDay = addDays(newStartDate, 1);
      remainingDuration = segmentDetails.duration - lengthOfFirstDay;

      while (remainingDuration > 0) {
        while (isWeekend(currentDay, { in: tz(timeZone) })) {
          currentDay = addDays(currentDay, 1);
        }

        const workingHoursOnCurrentDay = getWorkingHoursOnDate(currentDay, timeZone);
        const lengthOfDay = Math.min(workingHoursOnCurrentDay.durationInHours, remainingDuration);
        newWorkSegments.splice(currentIndex, 0, {
          ...(currentJob.workSegments[startIdxWorkSegmentGroup] as InstallationProjectJob_WorkSegment),
          endTime: addHours(workingHoursOnCurrentDay.workStartsAt, lengthOfDay),
          startTime: workingHoursOnCurrentDay.workStartsAt,
          teamId: newTeamId ? { value: newTeamId } : undefined,
        });
        remainingDuration -= lengthOfDay;
        currentDay = addDays(currentDay, 1);
        currentIndex += 1;
      }

      let currentUnsavedChange;
      currentUnsavedChange = unsavedChanges.find(
        (unsavedChange) => unsavedChange.projectId === segmentProjectId && unsavedChange.jobId === segmentJobId,
      );

      if (!currentUnsavedChange) {
        currentUnsavedChange = unsavedChangesWithoutReason.find(
          (unsavedChange) => unsavedChange.projectId === segmentProjectId && unsavedChange.jobId === segmentJobId,
        );
      }

      // Keep the original start time and team if the segment has been moved before
      const previousStartTime = currentUnsavedChange
        ? currentUnsavedChange.previousStartTime
        : segmentDetails.startTime;

      const originalTeamId = currentUnsavedChange ? currentUnsavedChange.previousTeamId : previousTeamId;

      // if the segment has not been moved before, and only the team has changed, and the start time is the same, then we don't need to create a new unsaved change
      // just save directly to the backend
      if (!currentUnsavedChange && originalTeamId !== newTeamId && isEqual(segmentDetails.startTime, newStartDate)) {
        // Optimistically update the query cache for installation projects
        utils.InstallationProject.getInstallationProjects.setData(
          { operationalUnitId: regionId, updatedAfter: initialLoadTime },
          (oldData) => {
            if (!oldData) return [];
            return oldData.map((project) => {
              if (segmentProjectId !== project.installationProject?.id?.value)
                return project as FullInstallationProjectEntity;
              return {
                ...(project as FullInstallationProjectEntity),
                installationProject: {
                  ...(project.installationProject as InstallationProject),
                  jobs:
                    (project.installationProject as InstallationProject).jobs?.map((job) => {
                      if (job.id?.value !== segmentJobId) return job;
                      return {
                        ...job,
                        workSegments: newWorkSegments,
                      };
                    }) ?? [],
                },
              };
            });
          },
        );
        try {
          await updateWorkSegments({
            jobId: segmentJobId,
            workSegments: newWorkSegments
              .filter(({ endTime, startTime }) => startTime && endTime)
              .map(({ endTime, startTime, teamId }) => ({
                duration: {
                  nanos: 0,
                  seconds: differenceInSeconds(endTime!, startTime!),
                } satisfies Duration,
                startTime: startTime!,
                teamId: teamId?.value,
              })),
          });
          toast(
            <FormattedMessage
              defaultMessage="Successfully updated the team assigned to the job."
              id="installationPlanning.notify.updatedJobTeam"
            />,
          );
        } catch (_error) {
          toast.error(
            <FormattedMessage
              defaultMessage="Failed to update the team assigned to the job."
              id="installationPlanning.notify.updatedJobTeamError"
            />,
            { position: 'bottom-center' },
          );
        } finally {
          refetchProjectsUpdatedSince();
          setJobBeingDragged();
        }
        return;
      }

      let typeOfChange;
      if (newTeamId !== originalTeamId) {
        if (isEqual(segmentDetails.startTime, newStartDate)) {
          typeOfChange = 'change-team' as const;
        } else {
          typeOfChange = 'change-team-and-move-segment' as const;
        }
      } else {
        typeOfChange = 'move-segment' as const;
      }

      updateUnsavedChanges({
        ...currentUnsavedChange,
        customerName: `${continuousWorkSegment.contact?.firstName} ${continuousWorkSegment.contact?.lastName}`,
        jobId: segmentJobId,
        newStartTime: newStartDate,
        newTeamId,
        previousStartTime,
        previousTeamId: originalTeamId,
        projectId: segmentProjectId,
        requiredRole: Number(segmentRequiredRole),
        userWhoMadeChange: `${user?.firstName} ${user?.lastName}`,
        workSegments: newWorkSegments,
        typeOfChange: typeOfChange,
      });

      if (unsavedChanges.length === 0) {
        setShowUnsavedChanges(true);
      }

      const overlappingSegmentsForResources = checkForOverlappingSegmentsForResources({
        activeJobCustomerName: `${continuousWorkSegment.contact?.firstName} ${continuousWorkSegment.contact?.lastName}`,
        activeJobId: segmentDetails.jobId,
        continuousSegmentsByResource,
        newWorkSegments,
        timeZone,
      });

      setOverlappingSegmentsForResources(overlappingSegmentsForResources);

      setJobBeingDragged();
    },
    [
      segmentDetails,
      unsavedChanges,
      segmentRequiredRole,
      continuousWorkSegment,
      timeZone,
      segmentJobId,
      segmentProjectId,
      segmentCurrentStartDateTime,
      updateUnsavedChanges,
      user?.firstName,
      user?.lastName,
      continuousSegmentsByResource,
      setOverlappingSegmentsForResources,
      setJobBeingDragged,
      unsavedChangesWithoutReason,
      utils.InstallationProject.getInstallationProjects,
      regionId,
      initialLoadTime,
      updateWorkSegments,
      refetchProjectsUpdatedSince,
      setShowUnsavedChanges,
    ],
  );
  const jobRef = useRef(null); // Create a ref for the card

  useEffect(() => {
    const el = jobRef.current;
    invariant(el);

    return draggable({
      element: el,
      canDrag: () => !resourceId && canEdit && !dispatchMode,
      getInitialData: () => ({ type: 'job' }),
      onDragStart: () => {
        setSelectedProject(continuousWorkSegment);
        setJobBeingDragged(continuousWorkSegment);
        if (containerRef?.current && 'style' in containerRef.current) {
          (containerRef.current as HTMLElement).style.pointerEvents = 'none';
        }
      },
      onDrop: ({ location, source }: BaseEventPayload<ElementDragType>) => {
        if (location.current.dropTargets[0]?.data) {
          handleDragEnd({ location, source });
        }
        if (containerRef?.current) {
          (containerRef.current as HTMLElement).style.pointerEvents = 'auto';
        }
      },
      onGenerateDragPreview: ({ nativeSetDragImage, source }) => {
        scrollJustEnoughIntoView({ element: source.element });
        setCustomNativeDragPreview({
          nativeSetDragImage,
          getOffset: () => ({ x: 10, y: 16 }),
          render: ({ container }) => {
            setState({ container, type: 'preview' });
            return () => setState({ type: 'idle' });
          },
        });
      },
    });
  }, [
    containerRef,
    continuousWorkSegment,
    handleDragEnd,
    hasAssignedResources,
    resourceId,
    segmentDetails,
    setJobBeingDragged,
    setSelectedProject,
    canEdit,
    dispatchMode,
  ]);

  const containerStyle = useMemo(() => ({ width: '100%' }), []);

  return (
    <>
      <div ref={jobRef} style={containerStyle}>
        <JobItem
          canEdit={canEdit}
          continuousWorkSegment={continuousWorkSegment}
          dayWidth={dayWidth}
          dispatchMode={dispatchMode}
          focusedProject={focusedProject}
          highlightFlexibleProjects={highlightFlexibleProjects}
          highlightProjectsWithinDistance={highlightProjectsWithinDistance}
          hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
          jobBeingSplit={jobBeingSplit}
          jobsToDispatch={jobsToDispatch}
          resourceId={resourceId}
          selectedProject={selectedProject}
          setHoveredProject={setHoveredProject}
          setJobBeingSplit={setJobBeingSplit}
          setJobsToDispatch={setJobsToDispatch}
          setRightClickMenu={setRightClickMenu}
          setSelectedProject={setSelectedProject}
          setShowRightSidebar={setShowRightSidebar}
          setSplitSegmentsToMove={setSplitSegmentsToMove}
          setToolTipProject={setToolTipProject}
          splitSegmentsToMove={splitSegmentsToMove}
          unsavedChanges={unsavedChanges}
          incomplete={incomplete}
        />
      </div>
      {state.type === 'preview'
        ? createPortal(<JobItemMoving continuousWorkSegment={continuousWorkSegment} />, state.container)
        : null}
    </>
  );
}
