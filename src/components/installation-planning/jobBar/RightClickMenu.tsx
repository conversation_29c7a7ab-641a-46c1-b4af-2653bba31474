import { List, ListItemButton, ListItemIcon, ListItemText, Portal } from '@mui/material';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import { ClockDotsOutlinedIcon } from '@ui/components/StandardIcons/ClockDotsOutlinedIcon';
import { EyeOutlinedIcon } from '@ui/components/StandardIcons/EyeOutlinedIcon';
import { EyeStrikethroughOutlinedIcon } from '@ui/components/StandardIcons/EyeStrikethroughOutlinedIcon';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { PeopleIcon } from '@ui/components/Icons/PeopleIcon/PeopleIcon';
import { sendGTMEvent } from '@next/third-parties/google';
import { isEmpty } from 'lodash';
import Link from 'next/link';
import { grey } from '@ui/theme/colors';
import { ContentCutOutlined } from '@ui/components/Icons/material';
import {
  useFocusedProject,
  useJobBeingSplit,
  useProjectActions,
  useRightClickMenu,
  useToolTipProject,
} from '../stores/ProjectStore';
import { PersonOutlinedIcon } from '@ui/components/StandardIcons/PersonOutlinedIcon';
import { InstallationProjectResourceType } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { useEffect } from 'react';
import HubspotLink from './HubspotLink';
import { FormattedMessage } from 'react-intl';

export default function RightClickMenu({}) {
  const rightClickMenu = useRightClickMenu();
  const jobBeingSplit = useJobBeingSplit();
  const focusedProject = useFocusedProject();
  const toolTipProject = useToolTipProject();
  const {
    setShowRemoveAssignedResourcesModal,
    setFocusedProject,
    setSeparateHoursToBeAdded,
    setJobBeingSplit,
    setJobBeingEdited,
    setSplitSegmentsToMove,
    setToolTipProject,
    setShowAssignTeamLeadModal,
    setRightClickMenu,
  } = useProjectActions();

  useEffect(() => {
    const closeMenu = () => {
      if (rightClickMenu && !toolTipProject) {
        setRightClickMenu(null);
      }
    };
    // Close the menu when clicking outside or pressing Esc
    const handleGlobalClick = () => closeMenu();
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') closeMenu();
    };
    window.addEventListener('contextMenu', handleGlobalClick);
    window.addEventListener('click', handleGlobalClick);
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('contextMenu', handleGlobalClick);
      window.removeEventListener('click', handleGlobalClick);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [setRightClickMenu, rightClickMenu, toolTipProject]);

  if (!rightClickMenu) return null;
  const { position, project, hasAssignedResources } = rightClickMenu;
  const { segmentDetails } = project;

  const menuPosition = {
    top: position.up ? undefined : position.y,
    bottom: position.up ? position.y : undefined,
    left: position.x,
  };

  return (
    <Portal>
      <List
        id="job-item-menu"
        sx={{
          position: 'absolute',
          top: menuPosition.top,
          bottom: menuPosition.bottom,
          left: menuPosition.left,
          backgroundColor: '#fff',
          borderRadius: '8px',
          listStyle: 'none',
          padding: '10px',
          margin: 0,
          boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
          zIndex: 5400,
          width: '280px',
        }}
      >
        <ListItemButton
          sx={{ padding: '5px 10px', cursor: 'pointer' }}
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'edit_times',
              app_name: 'installation_planning',
            });
            setJobBeingEdited(project);
            setRightClickMenu(null);
          }}
        >
          <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
            <PenOutlinedIcon height={16} width={16} color={grey[900]} />
          </ListItemIcon>
          <ListItemText>Edit times</ListItemText>
        </ListItemButton>
        <ListItemButton
          sx={{ padding: '5px 10px', cursor: 'pointer' }}
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'split_job',
              app_name: 'installation_planning',
            });
            if (jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId) {
              setJobBeingSplit();
              setSplitSegmentsToMove(null);
            } else {
              setJobBeingSplit(project);
            }
            setRightClickMenu(null);
          }}
        >
          <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
            <ContentCutOutlined sx={{ fontSize: '16px' }} />
          </ListItemIcon>
          <ListItemText>
            {jobBeingSplit?.segmentDetails.jobId === segmentDetails.jobId ? 'Cancel split' : 'Split job'}
          </ListItemText>
        </ListItemButton>
        <ListItemButton
          sx={{ padding: '5px 10px', cursor: 'pointer' }}
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'add_separate_hours',
              app_name: 'installation_planning',
            });
            setSeparateHoursToBeAdded(project);
            setToolTipProject();
            setRightClickMenu(null);
          }}
        >
          <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
            <ClockDotsOutlinedIcon height={16} width={16} color={grey[900]} />
          </ListItemIcon>
          <ListItemText>Add separate hours</ListItemText>
        </ListItemButton>

        {hasAssignedResources && (
          <ListItemButton
            sx={{ padding: '5px 10px', cursor: 'pointer' }}
            onClick={() => {
              sendGTMEvent({
                event: 'button_click',
                click_text: 'remove_assigned_resources',
                app_name: 'installation_planning',
              });
              setShowRemoveAssignedResourcesModal({ jobId: segmentDetails.jobId, continuousWorkSegment: project });
              setRightClickMenu(null);
            }}
          >
            <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
              <PeopleIcon height={16} width={16} color={grey[900]} />
            </ListItemIcon>
            <ListItemText>Remove assigned resources</ListItemText>
          </ListItemButton>
        )}
        <ListItemButton
          sx={{ padding: '5px 10px', cursor: 'pointer' }}
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'assign_team_lead',
              app_name: 'installation_planning',
            });
            setShowAssignTeamLeadModal({
              projectId: project.installationProject!.id!.value,
              teamLeadResourceId: project.installationProject?.assignedResources.find(
                (assignedResource) =>
                  assignedResource.type ===
                  InstallationProjectResourceType.INSTALLATION_PROJECT_RESOURCE_TYPE_TEAM_LEAD,
              )?.userId?.value,
            });
            setRightClickMenu(null);
          }}
        >
          <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
            <PersonOutlinedIcon height={16} width={16} color={grey[900]} />
          </ListItemIcon>
          <ListItemText>Assign team lead</ListItemText>
        </ListItemButton>
        <ListItemButton
          sx={{ padding: '5px 10px', cursor: 'pointer' }}
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: isEmpty(focusedProject) ? 'hide_other_projects' : 'show_other_projects',
              app_name: 'installation_planning',
            });
            if (isEmpty(focusedProject)) {
              setFocusedProject(project);
              setRightClickMenu(null);
              return;
            }
            setFocusedProject();
            setToolTipProject();
            setRightClickMenu(null);
          }}
        >
          <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
            {isEmpty(focusedProject) ? (
              <EyeStrikethroughOutlinedIcon height={16} width={16} color={grey[900]} />
            ) : (
              <EyeOutlinedIcon height={16} width={16} color={grey[900]} />
            )}
          </ListItemIcon>
          <ListItemText> {isEmpty(focusedProject) ? 'Hide other projects' : 'Show other projects'}</ListItemText>
        </ListItemButton>
        <Link
          href={`/solution/${project.energySolution?.id?.value}/installation`}
          target="_blank"
          onClick={() => {
            sendGTMEvent({
              event: 'button_click',
              click_text: 'view_project_in_aerospace',
              app_name: 'installation_planning',
            });
            setRightClickMenu(null);
          }}
        >
          <ListItemButton sx={{ padding: '5px 10px', cursor: 'pointer' }}>
            <ListItemIcon sx={{ width: '38px', minWidth: '38px' }}>
              <ArrowRightTopSquareOutlinedIcon height={16} width={16} color={grey[900]} />
            </ListItemIcon>
            <ListItemText>
              <FormattedMessage
                id="installationPlanning.viewProjectInAerospace"
                defaultMessage="View project in Aerospace"
              />
            </ListItemText>
          </ListItemButton>
        </Link>
        <HubspotLink
          installationProjectId={rightClickMenu!.project.installationProject!.id!.value}
          setRightClickMenu={setRightClickMenu}
        />
      </List>
    </Portal>
  );
}
