import { Box, IconButton, Stack, Typography } from '@mui/material';
import { DurationForRoleInput } from 'components/installation-booking/DurationForRoleInput';
import { useCallback, useEffect, useRef, useState } from 'react';
import { grey } from '@ui/theme/colors';
import { CalendarClockOutlinedIcon } from '@ui/components/StandardIcons/CalendarClockOutlinedIcon';
import { CalendarOutlinedIcon } from '@ui/components/StandardIcons/CalendarOutlinedIcon';
import { CardBankOutlinedIcon } from '@ui/components/StandardIcons/CardBankOutlinedIcon';
import { CheckCircleOutlinedIcon } from '@ui/components/StandardIcons/CheckCircleOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { DeviceOutlinedIcon } from '@ui/components/StandardIcons/DeviceOutlinedIcon';
import { PenOutlinedIcon } from '@ui/components/StandardIcons/PenOutlinedIcon';
import { RulerOutlinedIcon } from '@ui/components/StandardIcons/RulerOutlinedIcon';
import { StarOutlinedIcon } from '@ui/components/StandardIcons/StarOutlinedIcon';
import { getTeamSizeForRole, InstallationBookingRole } from 'components/installation-booking/queries/useGetManHours';
import { WORK_HOURS_PER_DAY } from 'components/installation-booking/durationFormatting';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import invariant from 'tiny-invariant';
import { BaseEventPayload, ElementDragType } from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types';
import { scrollJustEnoughIntoView } from '@atlaskit/pragmatic-drag-and-drop/element/scroll-just-enough-into-view';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { createPortal } from 'react-dom';
import { tz } from '@date-fns/tz';
import { addDays, addHours, isAfter, isBefore, isSameDay, isWeekend } from 'date-fns';
import toast from 'react-hot-toast';
import {
  InstallationProjectJob,
  InstallationProjectJob_WorkSegment,
  InstallationProjectStage,
} from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { api } from 'utils/api';
import { findIndex } from 'lodash';
import { useProjectActions, useSeparateHoursToBeAdded } from '../stores/ProjectStore';
import { ContinuousWorkSegment, reverseResourceMapping } from '../types/planningTypes';
import useTableCellDims from '../hooks/useTableCellDims';
import { useRegionContext } from '../../../context/RegionContext';
import { getWorkingHoursOnDate } from '../helpers/workingHours';

function NewSegmentBox({
  workRole,
  contact,
  hours,
  stage,
  dragging,
}: {
  hours: number | null;
  workRole: InstallationBookingRole;
  contact: ContinuousWorkSegment['contact'];
  stage?: InstallationProjectStage;
  dragging?: boolean;
}) {
  const { dayWidth } = useTableCellDims();
  const customerName = `${contact?.firstName} ${contact?.lastName}`;
  const getDurationInDays = (role: InstallationBookingRole) => {
    if (!hours) {
      return 0;
    }
    const teamSize = getTeamSizeForRole(role);
    const totalHours = hours / teamSize;
    const fullDays = Math.floor(totalHours / WORK_HOURS_PER_DAY);
    const partialDayHours = totalHours % WORK_HOURS_PER_DAY;

    return fullDays + (partialDayHours > 4 ? 1 : partialDayHours > 0 ? 0.5 : 0);
  };

  const durationInDays = getDurationInDays(workRole);
  const width = () => `${durationInDays * dayWidth - 6}px`;
  const backgroundColor = grey[200];

  const stageIcon = () => {
    if (!stage) return null;
    switch (stage) {
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW:
        return <StarOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_SURVEY:
        return <RulerOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_DESIGN:
        return <PenOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION:
        return <CalendarClockOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION:
        return <DeviceOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION:
        return <CalendarOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INVOICE:
        return <CardBankOutlinedIcon height={16} width={16} />;
      case InstallationProjectStage.INSTALLATION_PROJECT_STAGE_COMPLETED:
        return <CheckCircleOutlinedIcon height={16} width={16} />;
      default:
        return null;
    }
  };

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{
        mt: 2,
        background: backgroundColor,
        width: width(),
        height: '42px',
        padding: '0px 8px',
        borderRadius: '8px',
        alignItems: 'center',
        cursor: 'move',
        boxShadow: dragging ? 'none' : '0px 4px 4px rgba(0, 0, 0, 0.25)',
      }}
    >
      <Typography
        variant="body1Emphasis"
        fontSize={12}
        lineHeight="14px"
        sx={{
          pl: '4px',
          py: 0,
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          WebkitLineClamp: 2,
          maxHeight: '28px',
          maxWidth: `calc(${durationInDays * 2 * dayWidth}px - 6px)`,
          color: grey[800],
        }}
      >
        {customerName}
      </Typography>
      <Box
        sx={{
          height: '16px',
          width: '16px',
        }}
      >
        {stageIcon()}
      </Box>
    </Stack>
  );
}

export default function SeparateHoursComponent() {
  const originalContinuousSegment = useSeparateHoursToBeAdded();
  const { updateUnsavedChanges, setSeparateHoursToBeAdded } = useProjectActions();
  const [hours, setHours] = useState<number | null>(16);
  const region = useRegionContext();
  const timeZone = region?.timeZone;
  const [state, setState] = useState<{ type: 'idle' } | { type: 'preview'; container: HTMLElement }>({ type: 'idle' });
  const jobRef = useRef<HTMLDivElement>(null);
  const id = 'separateHours';
  const { segmentDetails: originalContinuousSegmentDetails, contact } = originalContinuousSegment!;
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const workRole =
    reverseResourceMapping[originalContinuousSegmentDetails.requiredRole as keyof typeof reverseResourceMapping];
  const handleDragEnd = useCallback(
    ({ location, source }: BaseEventPayload<ElementDragType>) => {
      // extract the data from the dropTarget and the draggable
      // Information about the date, start hour, team etc is contained in the id as a '|' separate string
      // date|requiredRole|rowIndex|teamId
      if (!location.current.dropTargets[0]?.data) return;
      const { id: dropTargetData } = location.current.dropTargets[0]?.data as { id: string };
      const draggedSegmentData = source.data as {
        id: string;
        segmentDetails: ContinuousWorkSegment['segmentDetails'];
      };

      if (!dropTargetData && draggedSegmentData) return;
      if (!originalContinuousSegment || !originalContinuousSegmentDetails) return;

      const startDateAtDropTarget = new Date(dropTargetData.split('|')[0]!);
      const roleAtDropTarget = dropTargetData.split('|')[1];
      const draggedSegmentRole = originalContinuousSegmentDetails.requiredRole;
      const projectId = originalContinuousSegment.installationProject!.id!.value;
      const { jobId } = originalContinuousSegmentDetails;
      const project = { ...originalContinuousSegment };
      const selectedJob = project?.installationProject?.jobs.find(
        (job) => job.requiredRole === Number(draggedSegmentRole) && job.id?.value === jobId,
      ) as InstallationProjectJob;
      const newTeamId = dropTargetData.split('|')[3] !== '' ? dropTargetData.split('|')[3] : undefined;

      if (Number(roleAtDropTarget) !== draggedSegmentRole) {
        const toastText = `You cannot move ${reverseResourceMapping[Number(draggedSegmentRole) as keyof typeof reverseResourceMapping].toLowerCase()} job to a slot for ${reverseResourceMapping[Number(roleAtDropTarget) as keyof typeof reverseResourceMapping].toLowerCase()} jobs`;
        toast.error(toastText, { position: 'top-center', duration: 6000 });
        return;
      }

      if (!(selectedJob && project) || !hours) return;

      const newWorkSegments: InstallationProjectJob_WorkSegment[] = [];

      let remainingDuration = 0;

      const endIdxOriginalContinuousSegments = findIndex(selectedJob.workSegments, (workSegment) =>
        isSameDay(workSegment.endTime!, originalContinuousSegmentDetails.endTime, { in: tz(timeZone) }),
      );

      const startIdxOriginalContinuousSegments = findIndex(selectedJob.workSegments, (workSegment) =>
        isSameDay(workSegment.endTime!, originalContinuousSegmentDetails.startTime, { in: tz(timeZone) }),
      );

      // Add the first day of the new segment
      const teamSize = getTeamSizeForRole(workRole);
      const totalHours = hours / teamSize;

      const lengthOfFirstDay = Math.min(totalHours, WORK_HOURS_PER_DAY);
      newWorkSegments[0] = {
        ...(selectedJob.workSegments[startIdxOriginalContinuousSegments] as InstallationProjectJob_WorkSegment),
        startTime: startDateAtDropTarget,
        endTime: addHours(startDateAtDropTarget, lengthOfFirstDay),
        teamId: newTeamId ? { value: newTeamId } : undefined,
      };
      // Add the remaining days of the new segment
      let currentIndex = 1;
      let currentDay = addDays(startDateAtDropTarget, 1);
      remainingDuration = totalHours - lengthOfFirstDay;

      while (remainingDuration > 0) {
        while (isWeekend(currentDay, { in: tz(timeZone) })) {
          currentDay = addDays(currentDay, 1);
        }

        const workingHoursOnCurrentDay = getWorkingHoursOnDate(currentDay, timeZone);
        const lengthOfDay = Math.min(workingHoursOnCurrentDay.durationInHours, remainingDuration);
        newWorkSegments.splice(currentIndex, 0, {
          ...(selectedJob.workSegments[endIdxOriginalContinuousSegments] as InstallationProjectJob_WorkSegment),
          startTime: workingHoursOnCurrentDay.workStartsAt,
          endTime: addHours(workingHoursOnCurrentDay.workStartsAt, lengthOfDay),
          teamId: newTeamId ? { value: newTeamId } : undefined,
        });
        remainingDuration -= lengthOfDay;
        currentDay = addDays(currentDay, 1);
        currentIndex += 1;
      }

      let indexToStartNewSegmentsAt: number;

      const newSegmentEndsBeforeStartOfOriginalSegments = isBefore(
        newWorkSegments[newWorkSegments.length - 1]!.endTime!,
        originalContinuousSegmentDetails.startTime,
      );
      const newSegmentStartsAfterEndOfOriginalSegments = isAfter(
        newWorkSegments[0]!.startTime!,
        originalContinuousSegmentDetails.endTime,
      );

      if (newSegmentEndsBeforeStartOfOriginalSegments) {
        indexToStartNewSegmentsAt = startIdxOriginalContinuousSegments;
        // For all work segment up to the index where the new segments should go,  just add them to the newWorkSegments array
      } else if (newSegmentStartsAfterEndOfOriginalSegments) {
        indexToStartNewSegmentsAt = endIdxOriginalContinuousSegments + 1;
      } else {
        const toastText = `This new segment cannot overlap the current one`;
        toast.error(toastText, { position: 'top-center', duration: 6000 });
        return;
      }

      const originalWorkSegmentsBeforeNewSegment = selectedJob.workSegments.slice(0, indexToStartNewSegmentsAt);
      const originalWorkSegmentsAfterNewSegment = selectedJob.workSegments.slice(indexToStartNewSegmentsAt);

      const allSegments = [
        ...originalWorkSegmentsBeforeNewSegment,
        ...newWorkSegments,
        ...originalWorkSegmentsAfterNewSegment,
      ];

      updateUnsavedChanges({
        projectId: projectId!,
        jobId: jobId!,
        customerName: `${project.contact?.firstName} ${project.contact?.lastName}`,
        requiredRole: Number(draggedSegmentRole),
        newStartTime: startDateAtDropTarget,
        previousStartTime: undefined,
        newTeamId,
        workSegments: allSegments,
        userWhoMadeChange: `${user?.firstName} ${user?.lastName}`,
        isNewSeparateHours: true,
        typeOfChange: 'separate-hours-added' as const,
      });
      setSeparateHoursToBeAdded(undefined);
    },
    [
      originalContinuousSegment,
      hours,
      originalContinuousSegmentDetails,
      setSeparateHoursToBeAdded,
      timeZone,
      updateUnsavedChanges,
      user?.firstName,
      user?.lastName,
      workRole,
    ],
  );

  useEffect(() => {
    const el = jobRef.current;
    invariant(el);

    return draggable({
      element: el,
      getInitialData: () => ({ type: 'newSegment', id, originalContinuousSegmentDetails }),
      onGenerateDragPreview: ({ nativeSetDragImage, source }) => {
        scrollJustEnoughIntoView({ element: source.element });
        setCustomNativeDragPreview({
          getOffset: () => ({ x: 10, y: 36 }),
          render: ({ container }) => {
            setState({ type: 'preview', container });
            return () => setState({ type: 'idle' });
          },
          nativeSetDragImage,
        });
      },
      onDrop: handleDragEnd,
    });
  }, [handleDragEnd, originalContinuousSegmentDetails]);

  const stage = originalContinuousSegment?.installationProject?.stage;

  return (
    <Stack
      sx={{
        width: 'fit-content',
        position: 'fixed',
        zIndex: 4000,
        bottom: '40px',
        left: 'calc(50vw - 200px)',
        background: '#fff',
        maxWidth: '100%',
        boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)',
        padding: 5,
        borderRadius: 2,
      }}
      spacing={2}
    >
      <Box sx={{ maxWidth: '400px' }}>
        <IconButton
          sx={{ position: 'absolute', top: 8, right: 8 }}
          onClick={() => setSeparateHoursToBeAdded(undefined)}
        >
          <CrossOutlinedIcon height={24} width={24} />
        </IconButton>
        <DurationForRoleInput workRole={workRole} title="Add separate work segment" hours={hours} setHours={setHours} />
      </Box>
      <Stack justifyContent="space-between" alignItems="flex-start">
        <Typography variant="body2" sx={{ mt: 1 }}>
          Drag the box below to where you want to put the hours
        </Typography>
        <div ref={jobRef} style={{ width: '100%' }}>
          <NewSegmentBox hours={hours} workRole={workRole} contact={contact} stage={stage} />
        </div>
        {state.type === 'preview'
          ? createPortal(
              <NewSegmentBox hours={hours} workRole={workRole} contact={contact} stage={stage} dragging />,
              state.container,
            )
          : null}
      </Stack>
    </Stack>
  );
}
