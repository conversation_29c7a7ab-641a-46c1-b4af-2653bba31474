import { Box, Stack, Typography } from '@mui/material';
import { brandYellow, green, grey, magenta, teal } from '@ui/theme/colors';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { InstallationProjectStage } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/model';
import { memo } from 'react';
import { ContinuousWorkSegment } from '../types/planningTypes';
import useTableCellDims from '../hooks/useTableCellDims';

const JobItemMoving = memo(function JobItemMoving({
  continuousWorkSegment,
}: {
  continuousWorkSegment: ContinuousWorkSegment & {
    segmentDetails: {
      startTime?: Date;
      endTime?: Date;
      jobId: string;
      requiredRole: number;
      duration?: number;
      workSegments: InstallationProjectJob_WorkSegment[];
    };
  };
}) {
  const { segmentDetails, contact, installationProject } = continuousWorkSegment;
  const { dayWidth } = useTableCellDims();

  if (!contact) return null;
  const { firstName, lastName } = contact;
  const customerName = `${firstName} ${lastName}`;

  const job = continuousWorkSegment.installationProject?.jobs.find((j) => j.id!.value === segmentDetails.jobId);

  const durationInHalfDays = Math.min(segmentDetails.duration ? Math.max(segmentDetails.duration / 4, 1) : 1, 10);

  const getWidth = () => {
    if (segmentDetails.startTime) {
      return `${(durationInHalfDays / 2) * dayWidth - 6}px`;
    }
    return '260px';
  };

  const backgroundColor = () => {
    if (!installationProject) return null;
    switch (true) {
      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION:
        return {
          background: magenta[400],
        };
      case (job?.assignedResources ?? []).length > 0:
        return {
          background: brandYellow[400],
        };

      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_NEW ||
        installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_SURVEY:
        return {
          background: grey[300],
        };

      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_TECHNICAL_DESIGN:
        return {
          background: teal[400],
        };

      case installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_PRE_INSTALLATION ||
        installationProject.stage === InstallationProjectStage.INSTALLATION_PROJECT_STAGE_INSTALLATION:
        return {
          background: green[400],
        };

      default:
        return {
          background: grey[300],
        };
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        ...backgroundColor(),
        height: '42px',
        maxHeight: '42px',
        opacity: 0.3,
        flexGrow: 1,
        width: getWidth(),
        padding: '0px 8px',
        borderRadius: '8px',
        alignItems: 'center',
        textOverflow: 'ellipsis',
        position: 'relative',
        justifyContent: 'space-between',
      }}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between" width="100%">
        <Typography
          variant="body1Emphasis"
          fontSize={12}
          lineHeight="14px"
          sx={{
            pl: '4px',
            py: 0,
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            WebkitLineClamp: segmentDetails.startTime ? 2 : 1,
            maxHeight: '28px', // 2 lines * 14px line height
            maxWidth: segmentDetails.startTime ? `calc(${(durationInHalfDays / 2) * dayWidth}px - 46px)` : '100%',
            color: grey[800],
          }}
        >
          {customerName}
        </Typography>
      </Stack>
      {!segmentDetails.startTime && segmentDetails.duration && (
        <Box
          sx={{
            display: 'flex',
            width: '22px',
            height: '22px',

            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '50%',
            background: grey[150],
          }}
        >
          <Typography variant="body1Emphasis" fontSize={10} lineHeight="14px" color={grey[800]}>
            {Math.max(durationInHalfDays * 0.5, 1).toFixed(0)}
          </Typography>
        </Box>
      )}
    </Box>
  );
});

export default JobItemMoving;
