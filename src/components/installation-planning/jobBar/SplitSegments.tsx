import { Box } from '@mui/material';
import { useCallback, useEffect, useRef } from 'react';
import { draggable } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import invariant from 'tiny-invariant';
import { BaseEventPayload, ElementDragType } from '@atlaskit/pragmatic-drag-and-drop/dist/types/internal-types';
import { scrollJustEnoughIntoView } from '@atlaskit/pragmatic-drag-and-drop/element/scroll-just-enough-into-view';
import { setCustomNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { tz, TZDate } from '@date-fns/tz';
import { api } from 'utils/api';
import { InstallationProjectJob } from '@aira/installation-project-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/job';
import { addDays, addHours, getHours, isBefore, isSameMinute, isWeekend, startOfDay } from 'date-fns';
import { findIndex } from 'lodash';
import toast from 'react-hot-toast';
import { grey } from '@ui/theme/colors';
import { createRoot } from 'react-dom/client';
import { useRegionContext } from '../../../context/RegionContext';
import { reverseResourceMapping } from '../types/planningTypes';
import { getWorkingHoursOnDate } from '../helpers/workingHours';
import {
  useJobBeingSplit,
  useProjectActions,
  useSplitSegmentsToMove,
  useUnsavedChanges,
  useUnsavedChangesWithoutReason,
} from '../stores/ProjectStore';

export default function SplitSegments() {
  const splitSegmentsToMove = useSplitSegmentsToMove();
  const jobBeingSplit = useJobBeingSplit();
  const endSegmentRef = useRef(null);
  const { timeZone } = useRegionContext();
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const unsavedChnages = useUnsavedChanges();
  const unsavedChangesWithoutReason = useUnsavedChangesWithoutReason();
  const { setJobBeingDragged, updateUnsavedChanges, setJobBeingSplit, setSplitSegmentsToMove } = useProjectActions();

  const { segmentsBeforeSplit, segmentsAfterSplit } = splitSegmentsToMove!;

  const handleDragEnd = useCallback(
    ({ location }: BaseEventPayload<ElementDragType>) => {
      const over = location.current.dropTargets[0]?.data as { id: string };

      if (!over) return;
      const { id: overId } = over;
      const segmentGroupBeingSplit = jobBeingSplit?.segmentDetails;
      if (!segmentGroupBeingSplit) return;

      const newStartDate = new Date(overId.split('|')[0]!);

      if (isBefore(newStartDate, segmentsBeforeSplit[segmentsBeforeSplit.length - 1]!.endTime!)) {
        toast.error('The job cannot start before the previous job ends', { position: 'top-center', duration: 6000 });
        return;
      }

      const overRequiredRole = overId.split('|')[1];
      const activeRequiredRole = segmentGroupBeingSplit.requiredRole;
      const activeProjectId = jobBeingSplit.installationProject?.id?.value;
      const activeJobId = segmentGroupBeingSplit.jobId;
      const activeProject = { ...jobBeingSplit };
      const activeJob = activeProject?.installationProject?.jobs.find(
        (job) => job.requiredRole === Number(activeRequiredRole) && job.id?.value === activeJobId,
      ) as InstallationProjectJob;
      const newTeamId = overId.split('|')[3] !== '' ? overId.split('|')[3] : undefined;
      const now = TZDate.tz(timeZone);

      if (overRequiredRole !== activeRequiredRole.toString()) {
        const toastText = `You cannot move ${reverseResourceMapping[Number(activeRequiredRole) as keyof typeof reverseResourceMapping].toLowerCase()} job to a slot for ${reverseResourceMapping[Number(overRequiredRole) as keyof typeof reverseResourceMapping].toLowerCase()} jobs`;
        toast.error(toastText, { position: 'top-center', duration: 6000 });
        return;
      }

      if (!(activeJob && activeProject)) return;

      if (segmentsAfterSplit.length === 0) {
        toast.error('You cannot split a job with no segments', { position: 'top-center', duration: 6000 });
        return;
      }

      const newWorkSegments: InstallationProjectJob_WorkSegment[] = [];
      const workSegmentDuration =
        segmentsAfterSplit.reduce((acc, segment) => acc + (segment?.duration?.seconds ?? 0), 0) / 3600;

      let startIdxWorkSegmentGroup = 0;
      let remainingDuration = 0;

      // Find the start and end index of the work segment group being moved
      startIdxWorkSegmentGroup = findIndex(activeJob.workSegments, (workSegment) =>
        isSameMinute(workSegment.startTime!, segmentGroupBeingSplit.startTime),
      );
      const previousTeamId = segmentsBeforeSplit[segmentsBeforeSplit.length - 1]?.teamId?.value;
      if (isSameMinute(newStartDate, segmentsAfterSplit[0]!.startTime!) && newTeamId === previousTeamId) return;

      const endIdxWorkSegmentGroup = findIndex(activeJob.workSegments, (workSegment) =>
        isSameMinute(workSegment.endTime!, segmentGroupBeingSplit.endTime),
      );

      // For all work segments before the group being moved, just add them to the newWorkSegments array
      activeJob.workSegments.forEach((workSegment, index) => {
        if (index < startIdxWorkSegmentGroup) {
          newWorkSegments.push(workSegment);
        }
      });

      segmentsBeforeSplit.forEach((segment) => {
        newWorkSegments.push(segment);
      });

      // Handle past-dated start times
      if (isBefore(newStartDate, startOfDay(now))) {
        toast.error('The job cannot start in the past', { position: 'top-center', duration: 6000 });
        return;
      }

      let currentDay = newStartDate;
      remainingDuration = workSegmentDuration;
      let workingHoursOnCurrentDay = getWorkingHoursOnDate(currentDay, timeZone);

      let lengthOfDay = Math.min(
        getHours(workingHoursOnCurrentDay.workEndsAt, { in: tz(timeZone) }) -
          getHours(newStartDate, { in: tz(timeZone) }),
        remainingDuration,
      );
      let startTime = newStartDate;

      while (remainingDuration > 0) {
        while (isWeekend(currentDay, { in: tz(timeZone) })) {
          currentDay = addDays(currentDay, 1);
        }

        newWorkSegments.push({
          ...(activeJob.workSegments[startIdxWorkSegmentGroup] as InstallationProjectJob_WorkSegment),
          startTime,
          endTime: addHours(startTime, lengthOfDay),
          ...(newTeamId && { teamId: { value: newTeamId } }),
        });
        remainingDuration -= lengthOfDay;
        currentDay = addDays(currentDay, 1);
        workingHoursOnCurrentDay = getWorkingHoursOnDate(currentDay, timeZone);
        startTime = workingHoursOnCurrentDay.workStartsAt;
        lengthOfDay = Math.min(workingHoursOnCurrentDay.durationInHours, remainingDuration);
      }

      // For all work segments outside of the group being moved, just add them to the newWorkSegments array
      activeJob.workSegments.forEach((workSegment, index) => {
        if (index > endIdxWorkSegmentGroup) {
          newWorkSegments.push(workSegment);
        }
      });

      let currentUnsavedChange;
      currentUnsavedChange = unsavedChnages.find(
        (unsavedChange) => unsavedChange.projectId === activeProjectId && unsavedChange.jobId === activeJobId,
      );

      if (!currentUnsavedChange) {
        currentUnsavedChange = unsavedChangesWithoutReason.find(
          (unsavedChange) => unsavedChange.projectId === activeProjectId && unsavedChange.jobId === activeJobId,
        );
      }

      // Keep the original start time and team if the segment has been moved before
      const previousStartTime = currentUnsavedChange
        ? currentUnsavedChange.previousStartTime
        : segmentGroupBeingSplit.startTime;

      const originalTeamId = currentUnsavedChange ? currentUnsavedChange.previousTeamId : previousTeamId;

      updateUnsavedChanges({
        ...currentUnsavedChange,
        projectId: activeProjectId!,
        jobId: activeJobId!,
        customerName: `${activeProject.contact?.firstName} ${activeProject.contact?.lastName}`,
        requiredRole: Number(activeRequiredRole),
        newStartTime: newStartDate,
        previousStartTime,
        previousTeamId: originalTeamId,
        newTeamId,
        workSegments: newWorkSegments,
        userWhoMadeChange: `${user?.firstName} ${user?.lastName}`,
        typeOfChange: 'split-segment',
      });

      setJobBeingSplit();
      setSplitSegmentsToMove(null);

      // TODO: Convert these back into dates on the receiver side before storing in react state
      // const newWorkSegmentsWithStringDates = newWorkSegments.map((workSegment) => ({
      //   ...workSegment,
      //   startTime: workSegment.startTime!.toISOString(),
      //   endTime: workSegment.endTime!.toISOString(),
      // }));

      // socket.emit(
      //   'sendMessage',
      //   JSON.stringify([
      //     ...newUnsavedChanges,
      //     {
      //       projectId: activeProjectId!,
      //       jobId: activeJobId!,
      //       workSegments: newWorkSegments,
      //       userWhoMadeChange: `${user?.firstName} ${user?.lastName}`,
      //     },
      //   ]),
      //   regionId,
      // );

      setJobBeingDragged();
    },
    [
      jobBeingSplit,
      timeZone,
      segmentsAfterSplit,
      segmentsBeforeSplit,
      unsavedChnages,
      updateUnsavedChanges,
      user?.firstName,
      user?.lastName,
      setJobBeingSplit,
      setSplitSegmentsToMove,
      setJobBeingDragged,
      unsavedChangesWithoutReason,
    ],
  );

  useEffect(() => {
    const el = endSegmentRef.current;
    invariant(el);

    return draggable({
      element: el,
      canDrag: () => true,
      getInitialData: () => ({ type: 'split-segment' }),
      onGenerateDragPreview: ({ nativeSetDragImage, source }) => {
        scrollJustEnoughIntoView({ element: source.element });
        setCustomNativeDragPreview({
          getOffset: () => ({ x: 10, y: 16 }),
          nativeSetDragImage,
          render: ({ container }) => {
            // Create a new div for the React root
            const newEl = document.createElement('div');
            container.appendChild(newEl);

            // Create a root and render the preview
            const root = createRoot(newEl);
            const elRect = el as HTMLElement;
            root.render(
              <Box
                sx={{
                  width: `calc(${elRect.getBoundingClientRect().width}px - 3px)`,
                  height: '42px',
                  maxHeight: '42px',
                  backgroundColor: grey[200],
                  borderRadius: '4px',
                  boxShadow: `0px 25px 36px 0px rgba(0, 0, 0, 0.25)`,
                  backgroundImage: `linear-gradient(90deg, ${grey[100]} 50%, transparent 50%), linear-gradient(90deg, ${grey[100]} 50%, transparent 50%), linear-gradient(0deg, ${grey[100]} 50%, transparent 50%), linear-gradient(0deg, ${grey[100]} 50%, transparent 50%)`,
                  backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
                  backgroundSize: '15px 2px, 15px 2px, 2px 15px, 2px 15px',
                  backgroundPosition: 'left top, right bottom, left bottom, right top',
                  animation: 'border-dance 1s infinite linear',
                  '@keyframes border-dance': {
                    '0%': {
                      backgroundPosition: 'left top, right bottom, left bottom, right top',
                    },

                    '100%': {
                      backgroundPosition: 'left 15px top, right 15px bottom, left bottom 15px, right top 15px',
                    },
                  },
                }}
              />,
            );

            // Return a cleanup function
            return () => {
              root.unmount();
              container.removeChild(newEl);
            };
          },
        });
      },
      onDrop: ({ location, source }: BaseEventPayload<ElementDragType>) => {
        if (location.current.dropTargets[0]?.data) {
          handleDragEnd({ location, source });
        }
      },
    });
  }, [handleDragEnd, jobBeingSplit?.contact?.firstName, jobBeingSplit?.contact?.lastName]);

  const segmentsBeforeSplitWidth =
    segmentsBeforeSplit.reduce((acc, segment) => acc + (segment?.duration?.seconds ?? 0), 0) / 3600;
  const segmentsAfterSplitWidth =
    segmentsAfterSplit.reduce((acc, segment) => acc + (segment?.duration?.seconds ?? 0), 0) / 3600;

  const totalWidth = segmentsBeforeSplitWidth + segmentsAfterSplitWidth;
  const segmentsBeforeSplitWidthPercentage = (segmentsBeforeSplitWidth / totalWidth) * 100;
  const segmentsAfterSplitWidthPercentage = (segmentsAfterSplitWidth / totalWidth) * 100;

  return (
    <>
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: `${segmentsBeforeSplitWidthPercentage}%`,
          height: '100%',
        }}
      />
      <Box
        ref={endSegmentRef}
        sx={{
          position: 'absolute',
          top: 0,
          left: `calc(${segmentsBeforeSplitWidthPercentage}% + 3px)`,
          width: `calc(${segmentsAfterSplitWidthPercentage}% - 3px)`,
          height: '100%',
          borderRadius: '0 4px 4px 0',
          backgroundColor: grey[200],
          boxShadow: `0px 25px 36px 0px rgba(0, 0, 0, 0.25)`,
          backgroundImage: `linear-gradient(90deg, ${grey[100]} 50%, transparent 50%), linear-gradient(90deg, ${grey[100]} 50%, transparent 50%), linear-gradient(0deg, ${grey[100]} 50%, transparent 50%), linear-gradient(0deg, ${grey[100]} 50%, transparent 50%)`,
          backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
          backgroundSize: '15px 2px, 15px 2px, 2px 15px, 2px 15px',
          backgroundPosition: 'left top, right bottom, left bottom, right top',
          animation: 'border-dance 1s infinite linear',
          '@keyframes border-dance': {
            '0%': {
              backgroundPosition: 'left top, right bottom, left bottom, right top',
            },

            '100%': {
              backgroundPosition: 'left 15px top, right 15px bottom, left bottom 15px, right top 15px',
            },
          },
        }}
      />
    </>
  );
}
