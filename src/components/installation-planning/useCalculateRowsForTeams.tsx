import { useMemo } from 'react';
import { Team } from '@aira/resource-grpc-api/build/ts_out/com/aira/acquisition/contract/resource/v1/model';
import { ContinuousWorkSegment, Row } from './types/planningTypes';
import { useDateRange } from './contexts/DateRangeContext';
import { useRegionContext } from '../../context/RegionContext';
import { calculateRow } from './calculateRow';

export function useCalculateRowsForTeams({
  continuousWorkSegmentsWithTeam,
  teamsForResource,
}: {
  continuousWorkSegmentsWithTeam: Map<string, ContinuousWorkSegment[]>;
  teamsForResource: Team[];
}) {
  const { weekdayMetrics } = useDateRange();
  const { timeZone } = useRegionContext();
  const daysInInterval = weekdayMetrics.weekdaysInRange.length;
  const { dateIndexMap } = useDateRange();
  const rowSize = daysInInterval * 2;

  return useMemo(() => {
    const teamsRows: { row: Row; numberOfRows: number; team: Team }[] = [];
    teamsForResource.forEach((team) => {
      if (!team.id?.value) {
        return;
      }
      const continuousWorkSegments = continuousWorkSegmentsWithTeam.get(team.id.value) ?? [];

      const { row, numberOfRows } = calculateRow({
        continuousWorkSegments,
        team,
        rowSize,
        dateIndexMap,
        timeZone,
      });

      if (team.deletedAt && row.length === 0) {
        return;
      }
      teamsRows.push({ row, numberOfRows, ...(team && { team }) });
    });

    return teamsRows;
  }, [teamsForResource, continuousWorkSegmentsWithTeam, rowSize, dateIndexMap, timeZone]);
}
