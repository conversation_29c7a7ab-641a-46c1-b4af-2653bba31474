import React, { useEffect, useMemo, useRef } from 'react';
import { Box, Skeleton, Stack, Typography } from '@mui/material';
import invariant from 'tiny-invariant';
import { autoScrollForElements } from '@atlaskit/pragmatic-drag-and-drop-auto-scroll/element';
import { api } from 'utils/api';
import { useResourceTypesForCountry, resourceMapping } from '../types/planningTypes';
import DaysHeader from './tableHeaders/DaysHeader';
import WeekNumberHeader from './tableHeaders/WeekNumberHeader';
import MonthsHeader from './tableHeaders/MonthsHeader';
import useTableCellDims from '../hooks/useTableCellDims';
import { useSeparateHoursToBeAdded, useTableContents } from '../stores/ProjectStore';
import SeparateHoursComponent from '../jobBar/SeparateHoursComponent';
import { useRegionContext } from '../../../context/RegionContext';
import TeamsTable from './teamsView/TeamsTable';
import ResourcesTableContainer from './resourcesView/ResourcesTableContainer';
import { useCountryCodeContext } from '../../../context/CountryCodeContext';
import { useDateRange } from '../contexts/DateRangeContext';
import { useAllProjects } from '../contexts/AllProjects';
import { useResources } from '../contexts/ResourcesContext';
import JobTooltipPortal from '../jobTooltip/JobTooltipPortal';
import RightClickMenu from '../jobBar/RightClickMenu';
import { isBefore } from 'date-fns';
import { useAvailability } from '../contexts/AvailabilityContext';
import { createContinuousWorkSegmentsForResources } from '../createContinuousWorkSegmentsForResources';
import createContinuousWorkSegmentsPerResourceTypeAndTeam from '../createContinuousWorkSegmentsPerResourceTypeAndTeam';

export default function TableGridContainer() {
  const resourcesForRegion = useResources();
  const { projects } = useAllProjects();
  const { timeZone, id: regionId } = useRegionContext();
  const countryCode = useCountryCodeContext();
  const { dayWidth } = useTableCellDims();
  const { teams } = useAvailability();
  const resourceTypesForCountry = useResourceTypesForCountry(countryCode);
  const { weekdayMetrics, dateRange } = useDateRange();
  const { workingHoursForDatesMap, weekdaysInRange } = weekdayMetrics;
  const daysInInterval = weekdayMetrics.weekdaysInRange.length;
  const tableWidth = dayWidth * daysInInterval + 190;
  const separateHoursToBeAddedToJob = useSeparateHoursToBeAdded();
  const tableContents = useTableContents();
  const tableRef = useRef<HTMLDivElement>(null);

  const { isLoading: isLoadingProjectsAtTimeOfLoad } = api.InstallationProject.getInstallationProjects.useQuery(
    {
      operationalUnitId: regionId!.value,
    },
    {
      refetchOnWindowFocus: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
    },
  );

  useEffect(() => {
    const element = tableRef.current;
    invariant(element, 'Element should be defined');

    return autoScrollForElements({ element });
  }, [tableRef]);

  const resourceTypesToShow = resourceTypesForCountry;
  const requiredRoles = React.useMemo(
    () => resourceTypesToShow.map((resourceType) => resourceMapping[resourceType]),
    [resourceTypesToShow],
  );

  const continuousSegmentsByResource = useMemo(() => {
    return createContinuousWorkSegmentsForResources({
      projects,
      weekdaysInRange,
      timeZone,
      fieldResourcesWithDataMap: resourcesForRegion,
      workingHoursForDatesMap,
    });
  }, [projects, weekdaysInRange, timeZone, resourcesForRegion, workingHoursForDatesMap]);

  const continuousSegmentsByRole = useMemo(() => {
    return createContinuousWorkSegmentsPerResourceTypeAndTeam({
      requiredRoles,
      projects,
      timeZone,
      teams: teams.filter((team) => !team.deletedAt || isBefore(dateRange.startDate, team.deletedAt)),
      workingHoursForDatesMap,
      dateRange,
    });
  }, [requiredRoles, projects, timeZone, teams, workingHoursForDatesMap, dateRange]);

  return (
    <Stack
      spacing={0}
      ref={tableRef}
      id="tableContainer"
      maxHeight="calc(100vh - 90px)"
      sx={{
        maxWidth: '100dvw',
        overflow: 'auto',
        position: 'relative',
        paddingBottom: '100px',
      }}
    >
      <Stack
        direction="column"
        pt={2}
        spacing={0}
        sx={{
          width: `${tableWidth}px`,
          maxWidth: `${tableWidth}px`,
          position: 'relative',
          background: '#fff',
        }}
      >
        {separateHoursToBeAddedToJob && <SeparateHoursComponent />}
        <MonthsHeader tableWidth={tableWidth} />
        <WeekNumberHeader tableWidth={tableWidth} />
        <DaysHeader tableWidth={tableWidth} />
        {isLoadingProjectsAtTimeOfLoad && (
          <>
            <Skeleton
              variant="rectangular"
              sx={{
                width: '100%',
                height: '100%',
                justifyContent: 'center',
                alignItems: 'center',
                position: 'absolute',
                top: 194,
                left: 190,
                zIndex: 6000,
              }}
              height={1900}
            />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                left: `calc(50vw - 340px)`,
                position: 'absolute',
                top: `calc(40vh - 100px)`,
                borderRadius: '10px',
                padding: 6,
                zIndex: 6001,
                backgroundColor: 'rgba(34, 34, 38, 0.51)',
              }}
            >
              <Stack spacing={4} direction="row" alignItems="baseline">
                <Typography variant="headline1" color="#fff">
                  Loading job data
                </Typography>
                <span className="dot-flashing" />
              </Stack>
            </Box>
          </>
        )}
        {tableContents === 'Teams' && continuousSegmentsByResource && (
          <TeamsTable
            resourceTypesToShow={resourceTypesToShow}
            requiredRoles={requiredRoles}
            continuousSegmentsByResource={continuousSegmentsByResource}
            continuousSegmentsByRole={continuousSegmentsByRole}
            tableRef={tableRef}
          />
        )}

        {tableContents === 'Resources' && (
          <ResourcesTableContainer
            requiredRoles={requiredRoles}
            continuousSegmentsByResource={continuousSegmentsByResource}
            fieldResourcesWithDataMap={resourcesForRegion}
            resourceTypesToShow={resourceTypesToShow}
            tableRef={tableRef}
          />
        )}
      </Stack>
      {continuousSegmentsByResource && continuousSegmentsByRole && (
        <JobTooltipPortal
          continuousSegmentsByResource={continuousSegmentsByResource}
          continuousSegmentsByRole={continuousSegmentsByRole}
        />
      )}
      <RightClickMenu />
    </Stack>
  );
}
