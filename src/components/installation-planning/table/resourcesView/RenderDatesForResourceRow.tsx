import { useEffect, useState } from 'react';
import { useResourceUnutilization } from 'components/installation-planning/contexts/ResourceUnutilizationContext';
import HalfDaySquare from './HalfDaySquare';
import { VisibleDate } from '../useVirtualizedTimeline';

function RenderDatesForResourceRow({
  requiredRole,
  rowIndex,
  numberOfRows,
  resourceId,
  setSelectedResourceHalfDay,
  canEdit,
  visibleDates,
}: {
  requiredRole: number;
  rowIndex: number;
  numberOfRows?: number;
  resourceId: string;
  setSelectedResourceHalfDay: (resourceHalfDay: { resourceId: string; date: Date; startTime: '08' | '12' }) => void;
  canEdit: boolean;
  visibleDates: VisibleDate[];
}) {
  const { resourceUnutilizationMap } = useResourceUnutilization();
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => setHydrated(true));
    } else {
      setTimeout(() => setHydrated(true), 50); // fallback
    }
  }, []);

  return (
    <div style={{ position: 'relative', height: '100%' }}>
      {hydrated &&
        visibleDates.map(({ key, date, startTime, transform, isFriday, stringDate }) => {
          const resourceIsInactiveOnDay = resourceUnutilizationMap.get(resourceId)?.get(`${stringDate}-${startTime}`);
          const resourceIsInactiveOnHalfDay =
            startTime === '08' ? resourceIsInactiveOnDay?.startTime === 8 : resourceIsInactiveOnDay?.endTime === 16;
          return (
            <HalfDaySquare
              key={`${key}-${rowIndex}-${requiredRole}`}
              date={date}
              startTime={startTime}
              resourceIsInactive={resourceIsInactiveOnHalfDay}
              numberOfRows={numberOfRows ?? 1}
              resourceId={resourceId}
              transform={transform}
              setSelectedResourceHalfDay={setSelectedResourceHalfDay}
              canEdit={canEdit}
              isDateFriday={isFriday}
            />
          );
        })}
    </div>
  );
}

export default RenderDatesForResourceRow;
