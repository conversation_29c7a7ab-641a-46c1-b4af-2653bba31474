import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';
import { ServiceVisit } from '@aira/service-visit-grpc-api/build/ts_out/index.com.aira.acquisition.contract.service.visit.v1';
import { Typography, Tooltip } from '@mui/material';
import { red } from '@ui/theme/colors';
import { useResourcesLoading } from 'components/installation-planning/contexts/ResourcesContext';
import { ServiceVisitWithTime } from 'components/installation-planning/contexts/ServicesVisitsContext';
import useTableCellDims from 'components/installation-planning/hooks/useTableCellDims';
import { addHours, format } from 'date-fns';
import { memo, useMemo } from 'react';

// Extracted service visit item component
const ServiceVisitItem = memo(
  ({
    visit,
    startDateIndex,
    startHour,
    startMinutes,
    dayWidth,
    CELL_HEIGHT,
    duration,
    startDate,
  }: {
    visit: ServiceVisit;
    startDateIndex: number;
    startHour: number;
    startMinutes: number;
    dayWidth: number;
    CELL_HEIGHT: number;
    duration: Duration;
    startDate: Date;
  }) => {
    const visitSummary = useMemo(() => visit.summary.split(' | ')[1], [visit.summary]);
    const hourWidth = dayWidth / 10;
    const durationInHours = duration.seconds / 3600;
    const durationWidth = durationInHours * hourWidth;
    const width = durationWidth;
    const leftPos =
      startDateIndex * dayWidth + Math.max(0, startHour - 8) * hourWidth + startMinutes * (hourWidth / 60);
    return (
      <div
        key={visit.id?.value}
        style={{
          position: 'absolute',
          width: `${width}px`,
          height: `${CELL_HEIGHT}px`,
          top: 0,
          left: `${leftPos}px`,
          zIndex: 1202,
          padding: '10px 10px',
        }}
      >
        <Tooltip
          placement="top"
          arrow
          componentsProps={{
            tooltip: {
              sx: {
                background: '#fff',
                borderRadius: '8px',
                boxShadow: '0px 0px 10px 0px rgba(0, 0, 0, 0.1)',
              },
            },
            arrow: {
              sx: {
                color: '#fff',
              },
            },
          }}
          title={
            <div
              style={{
                padding: '10px',
              }}
            >
              <Typography variant="body2">Service visit: {visitSummary}</Typography>
              <Typography variant="body2">
                {format(new Date(startDate), 'HH:mm')} -{' '}
                {format(new Date(addHours(startDate, durationInHours)), 'HH:mm')}
              </Typography>
            </div>
          }
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              background: '#fff',
              border: `2px solid ${red[200]}`,
              width: `${width}px`,
              height: `${CELL_HEIGHT - 20}px`,
              borderRadius: '8px',
              padding: '4px',
            }}
          />
        </Tooltip>
      </div>
    );
  },
);

ServiceVisitItem.displayName = 'ServiceVisitItem';

const RenderServiceVisitsForResource = memo(({ serviceVisits }: { serviceVisits: ServiceVisitWithTime[] }) => {
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const { isLoadingResources } = useResourcesLoading();

  // Early returns for empty states
  if (serviceVisits.length === 0 || isLoadingResources) {
    return null;
  }

  return (
    <div
      style={{
        position: 'relative',
      }}
    >
      {serviceVisits.map((visit) =>
        visit.jobConstraints?.duration ? (
          <ServiceVisitItem
            key={visit.id?.value}
            visit={visit}
            startDateIndex={visit.startDateIndex}
            dayWidth={dayWidth}
            startDate={visit.startDate}
            startHour={visit.startHour}
            startMinutes={visit.startMinutes}
            duration={visit.jobConstraints.duration}
            CELL_HEIGHT={CELL_HEIGHT}
          />
        ) : null,
      )}
    </div>
  );
});

RenderServiceVisitsForResource.displayName = 'RenderServiceVisitsForResource';

export default RenderServiceVisitsForResource;
