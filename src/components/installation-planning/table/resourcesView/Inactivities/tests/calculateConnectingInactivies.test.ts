import { inactivitiesForTest } from './inactivitiesForTest';
import { halfDaysInRangeForTest } from './halfDaysInRangeForTest';
import { calculateConnectingInactivities } from '../calculateConnectingInactivies';
import { connectingSlotsForTest } from './connectingSlotsForTest';

describe('calculateConnectingInactivities', () => {
  it('should return connecting slots', () => {
    const result = calculateConnectingInactivities({
      inactivities: inactivitiesForTest,
      halfDaysInRange: halfDaysInRangeForTest,
    });
    expect(result).toEqual(connectingSlotsForTest);
  });
});
