import { InactivityMap } from '../types';

export const inactivitiesForTest: InactivityMap = new Map([
  [
    '2025-03-25-12',
    {
      regionId: '0478864c-f115-4730-8f71-6297b13c5910',
      reason: 'noOrder',
      startTime: 12,
      endTime: 16,
      id: 'a4146708-231c-4f7a-b8d8-0a5e9a0ac4b1',
    },
  ],
  [
    '2025-03-31-08',
    {
      regionId: '0478864c-f115-4730-8f71-6297b13c5910',
      reason: 'lentOut',
      startTime: 8,
      endTime: 16,
      id: 'c00a7469-ea70-4877-9ff2-dec1739df8d2',
    },
  ],
  [
    '2025-04-04-08',
    {
      reason: 'sickness',
      startTime: 8,
      endTime: 16,
      id: 'f3b4b2bf-2026-4a5c-ab1c-0d9666f9827a',
    },
  ],
  [
    '2025-04-07-08',
    {
      reason: 'sickness',
      startTime: 8,
      endTime: 16,
      id: '7f2b3cf7-9e6d-4293-ad98-236d05c7653a',
    },
  ],
  [
    '2025-04-08-08',
    {
      reason: 'sickness',
      startTime: 8,
      endTime: 16,
      id: 'e5b6264a-5638-42ee-ba42-f0d65ef8dc1b',
    },
  ],
  [
    '2025-04-09-08',
    {
      reason: 'sickness',
      startTime: 8,
      endTime: 16,
      id: 'b77eeef5-a6bc-427e-a2df-7dfc39c2aa7a',
    },
  ],
  [
    '2025-04-10-08',
    {
      reason: 'sickness',
      startTime: 8,
      endTime: 12,
      id: 'dc9ae638-711e-4be1-9205-31008e49307c',
    },
  ],
  [
    '2025-04-14-12',
    {
      reason: 'vacation',
      startTime: 12,
      endTime: 16,
      id: '04842119-589b-4e7f-abec-cd3b26acca8a',
    },
  ],
  [
    '2025-04-15-08',
    {
      reason: 'vacation',
      startTime: 8,
      endTime: 12,
      id: '4e3d8cb3-d977-4531-a280-9bb3836619df',
    },
  ],
  [
    '2025-04-16-12',
    {
      regionId: '0478864c-f115-4730-8f71-6297b13c5910',
      reason: 'noOrder',
      startTime: 12,
      endTime: 16,
      id: '08b0495c-224a-4c1e-927d-db5448200761',
    },
  ],
  [
    '2025-04-17-08',
    {
      reason: 'training',
      startTime: 8,
      endTime: 12,
      id: 'h345ergrg-3352-473f-b3c4-9175daad39f3',
    },
  ],
  [
    '2025-04-17-12',
    {
      reason: 'noOrder',
      startTime: 12,
      endTime: 16,
      id: '39e66779-3352-473f-b3c4-9175daad39f3',
    },
  ],
  [
    '2025-04-22-12',
    {
      reason: 'training',
      startTime: 12,
      endTime: 16,
      id: 'dfg342rd-3352-473f-b3c4-9175daad39f3',
    },
  ],
]);
