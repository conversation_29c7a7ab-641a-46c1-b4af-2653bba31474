export const halfDaysInRangeForTest: {
  date: Date;
  startTime: '08' | '12';
  stringDate: string;
}[] = [
  {
    date: new Date('2025-03-24T00:00:00.000+01:00'),
    startTime: '08',
    stringDate: '2025-03-24',
  },
  {
    date: new Date('2025-03-24T00:00:00.000+01:00'),
    startTime: '12',
    stringDate: '2025-03-24',
  },
  {
    date: new Date('2025-03-25T00:00:00.000+01:00'),
    startTime: '08',
    stringDate: '2025-03-25',
  },
  {
    date: new Date('2025-03-25T00:00:00.000+01:00'),
    startTime: '12',
    stringDate: '2025-03-25',
  },
  {
    date: new Date('2025-03-26T00:00:00.000+01:00'),
    startTime: '08',
    stringDate: '2025-03-26',
  },
  {
    date: new Date('2025-03-26T00:00:00.000+01:00'),
    startTime: '12',
    stringDate: '2025-03-26',
  },
  {
    date: new Date('2025-03-27T00:00:00.000+01:00'),
    startTime: '08',
    stringDate: '2025-03-27',
  },
  {
    date: new Date('2025-03-27T00:00:00.000+01:00'),
    startTime: '12',
    stringDate: '2025-03-27',
  },
  {
    date: new Date('2025-03-28T00:00:00.000+01:00'),
    startTime: '08',
    stringDate: '2025-03-28',
  },
  {
    date: new Date('2025-03-28T00:00:00.000+01:00'),
    startTime: '12',
    stringDate: '2025-03-28',
  },
  {
    date: new Date('2025-03-31T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-03-31',
  },
  {
    date: new Date('2025-03-31T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-03-31',
  },
  {
    date: new Date('2025-04-01T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-01',
  },
  {
    date: new Date('2025-04-01T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-01',
  },
  {
    date: new Date('2025-04-02T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-02',
  },
  {
    date: new Date('2025-04-02T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-02',
  },
  {
    date: new Date('2025-04-03T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-03',
  },
  {
    date: new Date('2025-04-03T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-03',
  },
  {
    date: new Date('2025-04-04T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-04',
  },
  {
    date: new Date('2025-04-04T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-04',
  },
  {
    date: new Date('2025-04-07T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-07',
  },
  {
    date: new Date('2025-04-07T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-07',
  },
  {
    date: new Date('2025-04-08T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-08',
  },
  {
    date: new Date('2025-04-08T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-08',
  },
  {
    date: new Date('2025-04-09T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-09',
  },
  {
    date: new Date('2025-04-09T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-09',
  },
  {
    date: new Date('2025-04-10T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-10',
  },
  {
    date: new Date('2025-04-10T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-10',
  },
  {
    date: new Date('2025-04-11T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-11',
  },
  {
    date: new Date('2025-04-11T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-11',
  },
  {
    date: new Date('2025-04-14T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-14',
  },
  {
    date: new Date('2025-04-14T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-14',
  },
  {
    date: new Date('2025-04-15T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-15',
  },
  {
    date: new Date('2025-04-15T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-15',
  },
  {
    date: new Date('2025-04-16T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-16',
  },
  {
    date: new Date('2025-04-16T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-16',
  },
  {
    date: new Date('2025-04-17T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-17',
  },
  {
    date: new Date('2025-04-17T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-17',
  },
  {
    date: new Date('2025-04-18T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-18',
  },
  {
    date: new Date('2025-04-18T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-18',
  },
  {
    date: new Date('2025-04-21T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-21',
  },
  {
    date: new Date('2025-04-21T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-21',
  },
  {
    date: new Date('2025-04-22T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-22',
  },
  {
    date: new Date('2025-04-22T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-22',
  },
  {
    date: new Date('2025-04-23T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-23',
  },
  {
    date: new Date('2025-04-23T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-23',
  },
  {
    date: new Date('2025-04-24T00:00:00.000+02:00'),
    startTime: '08',
    stringDate: '2025-04-24',
  },
  {
    date: new Date('2025-04-24T00:00:00.000+02:00'),
    startTime: '12',
    stringDate: '2025-04-24',
  },
];
