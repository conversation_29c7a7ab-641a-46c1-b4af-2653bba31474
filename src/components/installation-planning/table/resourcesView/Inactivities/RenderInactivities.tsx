import { useResourceUnutilization } from 'components/installation-planning/contexts/ResourceUnutilizationContext';
import ConnectingInactivity from './ConnectingInactivty';
import useTableCellDims from 'components/installation-planning/hooks/useTableCellDims';

export default function RenderInactivities({ resourceId, numberOfRows }: { resourceId: string; numberOfRows: number }) {
  const { connectInactivitiesPerResource } = useResourceUnutilization();
  const connectingSlots = connectInactivitiesPerResource.get(resourceId);
  const { dayWidth } = useTableCellDims();

  return (
    <div style={{ display: 'flex', flexDirection: 'row', gap: 0 }}>
      {connectingSlots?.map((connectingSlot) => {
        const transform = `translate3d(${(connectingSlot.halfDayStartDateIndex * dayWidth) / 2}px,0px,0)`;
        return (
          <ConnectingInactivity
            key={connectingSlot.ids.join('-')}
            connectingSlot={connectingSlot}
            transform={transform}
            numberOfRows={numberOfRows}
          />
        );
      })}
    </div>
  );
}
