import { useCanEdit } from 'components/installation-planning/hooks/useCanEdit';
import useTableCellDims from 'components/installation-planning/hooks/useTableCellDims';
import { CalendarRemoveOutlinedIcon } from '@ui/components/StandardIcons/CalendarRemoveOutlinedIcon';
import { ClockOutlinedIcon } from '@ui/components/StandardIcons/ClockOutlinedIcon';
import { GlobeOutlinedIcon } from '@ui/components/StandardIcons/GlobeOutlinedIcon';
import { HousePersonOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutlinedIcon';
import { ShieldOutlinedIcon } from '@ui/components/StandardIcons/ShieldOutlinedIcon';
import { Sun1OutlinedIcon } from '@ui/components/StandardIcons/Sun1OutlinedIcon';
import { ThermometerOutlinedIcon } from '@ui/components/StandardIcons/ThermometerOutlinedIcon';
import { beige, brandYellow, grey } from '@ui/theme/colors';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useProjectActions } from 'components/installation-planning/stores/ProjectStore';
import { useState } from 'react';
import { ConnectingSlot } from './types';
import { DocumentOutlinedIcon } from '@ui/components/StandardIcons/DocumentOutlinedIcon';

const REASONS = new Map([
  ['noOrder', { label: 'No order', type: 'unutilized', icon: CalendarRemoveOutlinedIcon }],
  ['customerAbsence', { label: 'Customer absence', type: 'unutilized', icon: HousePersonOutlinedIcon }],
  ['lentOut', { label: 'Lent out', type: 'unutilized', icon: GlobeOutlinedIcon }],
  ['timeCompensation', { label: 'Time compensation', type: 'unavailable', icon: ClockOutlinedIcon }],
  ['vacation', { label: 'Vacation', type: 'unavailable', icon: Sun1OutlinedIcon }],
  ['sickness', { label: 'Sickness', type: 'unavailable', icon: ThermometerOutlinedIcon }],
  ['training', { label: 'Training', type: 'unavailable', icon: ShieldOutlinedIcon }],
  ['holiday', { label: 'Public holiday', type: 'unavailable', icon: undefined }],
  ['other', { label: 'Other', type: 'unavailable', icon: DocumentOutlinedIcon }],
]);

export default function ConnectingInactivity({
  connectingSlot,
  transform,
  numberOfRows,
}: {
  connectingSlot: ConnectingSlot;
  transform: string;
  numberOfRows: number;
}) {
  const canEdit = useCanEdit();
  const { dayWidth, CELL_HEIGHT } = useTableCellDims();
  const { setShowRemoveResourceInactivityModal } = useProjectActions();
  const [isOver, setIsOver] = useState(false);
  const Reason = REASONS.get(connectingSlot.reason)!;

  const isHoliday = connectingSlot.reason === 'holiday';

  const handleClick = () => {
    if (!canEdit || isHoliday) {
      return;
    }
    setShowRemoveResourceInactivityModal({
      type: Reason?.type as 'unutilized' | 'unavailable',
      ids: connectingSlot.ids,
    });
  };

  const getWidth = () => {
    const startTimeEndTimeIsSameDay = connectingSlot.startDate === connectingSlot.endDate;
    if (startTimeEndTimeIsSameDay) {
      return connectingSlot.firstDayDuration === 'full' ? dayWidth : dayWidth / 2;
    }
    let newWidth = connectingSlot.firstDayDuration === 'full' ? dayWidth : dayWidth / 2;
    connectingSlot.dates.forEach((_, index) => {
      if (index === 0 || index === connectingSlot.dates.length - 1) {
        return;
      }
      newWidth += dayWidth;
    });
    newWidth += connectingSlot.lastDayDuration === 'full' ? dayWidth : dayWidth / 2;
    return newWidth;
  };

  const stripePattern = `repeating-linear-gradient(
  35deg,
  white,
  white 4px,
  ${isOver && !isHoliday ? brandYellow[400] : grey[150]} 4px,
  ${isOver && !isHoliday ? brandYellow[400] : grey[150]} 8px
)`;

  return (
    <Tooltip
      title={
        <Typography variant="body2">{Reason.label === 'Other' ? connectingSlot.description : Reason?.label}</Typography>
      }
      placement="top"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: '#fff',
            borderRadius: '4px',
            padding: '8px',
            boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
          },
        },
        arrow: {
          sx: {
            color: '#fff',
            position: 'absolute',
            transform: 'translateX(-100%)',
          },
        },
      }}
    >
      <div
        onClick={handleClick}
        onMouseEnter={() => setIsOver(true)}
        onMouseLeave={() => setIsOver(false)}
        style={{
          position: 'absolute',
          top: 0,
          transform,
          border: isOver && !isHoliday ? `2px solid ${brandYellow[400]}` : `2px solid ${beige[100]}`,
          height: `${CELL_HEIGHT * numberOfRows - 1}px`,
          maxHeight: `${CELL_HEIGHT * numberOfRows - 1}px`,
          minWidth: `${getWidth() - 2}px`,
          width: `${getWidth() - 2}px`,
          padding: '0',
          cursor: canEdit && !isHoliday ? 'pointer' : 'default',
          willChange: 'transform',
          background: stripePattern,
          backfaceVisibility: 'hidden',
        }}
      >
        {Reason.icon && (
          <div
            style={{
              position: 'absolute',
              top: 'calc(50% - 16px)',
              left: '4px',
              height: '32px',
              width: '32px',
              background: isOver && !isHoliday ? brandYellow[400] : '#fff',
              borderRadius: '50px',
            }}
          >
            <div style={{ position: 'absolute', top: 'calc(50% - 9px)', left: 'calc(50% - 9px)' }}>
              {Reason.label !== 'Other' && (
                <Reason.icon color={isOver && !isHoliday ? '#fff' : grey[600]} height={18} width={18} />
              )}
            </div>
          </div>
        )}
      </div>
    </Tooltip>
  );
}
