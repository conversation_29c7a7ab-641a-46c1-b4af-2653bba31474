import { ConnectingSlot, InactivityMap } from './types';

export const calculateConnectingInactivities = ({
  inactivities,
  halfDaysInRange,
}: {
  inactivities: InactivityMap;
  halfDaysInRange: {
    date: Date;
    startTime: '08' | '12';
    stringDate: string;
  }[];
}) => {
  // for all the half days in the date range, check if the resource is inactive
  // put together half days where the half days are consecutive and the reason is the same.
  // return an array of ConnectingSlot
  const connectingSlots: ConnectingSlot[] = [];

  let currentStartDate: string | null = null;
  let currentEndDate: string | null = null;
  let currentReason: string | null = null;
  let currentDescription: string | undefined = undefined;
  let currentStartTime: number | null = null;
  let currentEndTime: number | null = null;
  let currentHalfDayStartDateIndex: number | null = null;
  let currentIds: string[] = [];
  let currentDates: string[] = [];

  const idsUsed = new Set<string>();
  const datesUsed = new Set<string>();

  halfDaysInRange.forEach(({ stringDate, startTime }, index) => {
    const inactivity = inactivities?.get(`${stringDate}-${startTime}`);

    // if there is no inactivity for this half day, we need to check if there is an ongoing inactivity
    // from the previous half day and we need to finish it and reset the current values
    // if there is no ongoing inactivity, we just return
    if (!inactivity) {
      if (currentStartDate === null) {
        return;
      }
      if (currentStartDate !== null && currentEndDate !== stringDate) {
        connectingSlots.push({
          startDate: currentStartDate!,
          endDate: currentEndDate!,
          reason: currentReason!,
          description: currentDescription!,
          startTime: currentStartTime!,
          endTime: currentEndTime!,
          dates: currentDates,
          ids: currentIds,
          halfDayStartDateIndex: currentHalfDayStartDateIndex!,
          firstDayDuration:
            currentStartTime === 8 &&
            ((currentEndTime === 16 && currentStartDate === currentEndDate) || currentStartDate !== currentEndDate)
              ? 'full'
              : 'half',
          lastDayDuration: currentEndTime === 16 ? 'full' : 'half',
        });

        currentStartDate = null;
        currentEndDate = null;
        currentReason = null;
        currentDescription = undefined;
        currentStartTime = null;
        currentEndTime = null;
        currentIds = [];
        currentHalfDayStartDateIndex = null;
        currentDates = [];
        return;
      }
      if (currentStartDate !== null && currentEndDate === stringDate) {
        return;
      }
      return;
    }

    // if an inactivity is already set for this date and the end time for it is 16
    // then we don't need to do anything as there we cant add a new inactivity for this date
    // and we can't finish the slot as
    if (currentEndDate === stringDate && currentEndTime === 16) {
      return;
    }

    const isCorrectStartTime = startTime === '08' ? inactivity?.startTime === 8 : inactivity?.startTime === 12;

    // this covers the case where there is no inactivity set by the precending half day
    // and the current day contains an activity but it is not in the current half day
    // so we skip this half day
    if (!currentStartDate && !isCorrectStartTime) {
      return;
    }

    const isFirstInactivtySlot =
      inactivity && (currentStartDate === null || inactivity.reason !== currentReason) && isCorrectStartTime;
    const isFollowingInactivitySlot =
      currentStartDate !== null && currentReason === inactivity?.reason && currentEndTime === 16 && isCorrectStartTime;

    if (isFirstInactivtySlot) {
      if (currentReason !== null && currentReason !== inactivity.reason) {
        connectingSlots.push({
          startDate: currentStartDate!,
          endDate: currentEndDate!,
          reason: currentReason!,
          description: currentDescription,
          startTime: currentStartTime!,
          endTime: currentEndTime!,
          dates: currentDates,
          ids: currentIds,
          halfDayStartDateIndex: currentHalfDayStartDateIndex!,
          firstDayDuration:
            currentStartTime === 8 &&
            ((currentEndTime === 16 && currentStartDate === currentEndDate) || currentStartDate !== currentEndDate)
              ? 'full'
              : 'half',
          lastDayDuration: currentEndTime === 16 ? 'full' : 'half',
        });
      }
      currentStartDate = stringDate;
      currentReason = inactivity.reason;
      currentDescription = inactivity.description;
      currentStartTime = inactivity.startTime;
      currentEndTime = inactivity.endTime;
      currentEndDate = stringDate;
      currentIds = [inactivity.id];
      currentHalfDayStartDateIndex = index;
      currentDates = [stringDate];
      datesUsed.add(stringDate);
      idsUsed.add(inactivity.id);
    } else if (isFollowingInactivitySlot) {
      currentEndDate = stringDate;
      currentEndTime = inactivity.endTime;
      if (!idsUsed.has(inactivity.id) && !datesUsed.has(stringDate)) {
        currentIds.push(inactivity.id);
        idsUsed.add(inactivity.id);
        datesUsed.add(stringDate);
        currentDates.push(stringDate);
      }
    } else {
      if (currentStartDate !== null) {
        connectingSlots.push({
          startDate: currentStartDate!,
          endDate: currentEndDate!,
          reason: currentReason!,
          description: currentDescription,
          startTime: currentStartTime!,
          endTime: currentEndTime!,
          dates: currentDates,
          ids: currentIds,
          halfDayStartDateIndex: currentHalfDayStartDateIndex!,
          firstDayDuration:
            currentStartTime === 8 &&
            ((currentEndTime === 16 && currentStartDate === currentEndDate) || currentStartDate !== currentEndDate)
              ? 'full'
              : 'half',
          lastDayDuration: currentEndTime === 16 ? 'full' : 'half',
        });
      }

      // if there is no inactivity set for this date or the inactivity is already used
      // we reset the everything to null or []. The right side of the || is for the case where
      // the current date is the same as the previous date but the end time of the inactivity is 12
      // and we are currently in the second half of the day.
      if (idsUsed.has(inactivity.id) || !isCorrectStartTime) {
        currentStartDate = null;
        currentEndDate = null;
        currentReason = null;
        currentDescription = undefined;
        currentStartTime = null;
        currentEndTime = null;
        currentIds = [];
        currentHalfDayStartDateIndex = null;
        currentDates = [];
      } else {
        currentStartDate = stringDate;
        currentEndDate = stringDate;
        currentReason = inactivity?.reason;
        currentDescription = inactivity?.description;
        currentStartTime = inactivity?.startTime;
        currentEndTime = inactivity?.endTime;
        currentIds = [inactivity.id!];
        currentHalfDayStartDateIndex = index;
        currentDates = [stringDate];
      }
    }
  });

  return connectingSlots;
};
