export type ConnectingSlot = {
  startDate: string;
  endDate: string;
  reason: string;
  description?: string;
  startTime: number;
  endTime: number;
  ids: string[];
  firstDayDuration: 'full' | 'half';
  lastDayDuration: 'full' | 'half';
  halfDayStartDateIndex: number;
  dates: string[];
};

export type InactivityMap = Map<
  string,
  {
    reason:
      | 'noOrder'
      | 'customerAbsence'
      | 'lentOut'
      | 'timeCompensation'
      | 'vacation'
      | 'sickness'
      | 'training'
      | 'holiday'
      | 'other';
    description?: string;
    regionId?: string;
    startTime: number;
    endTime: number;
    id: string;
  }
>;
