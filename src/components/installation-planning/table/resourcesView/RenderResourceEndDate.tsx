import useTableCellDims from 'components/installation-planning/hooks/useTableCellDims';
import { beige, grey } from '@ui/theme/colors';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import { DateMessage } from '@aira/resource-grpc-api/build/ts_out/com/aira/contract/common/v1/date';
import { tz, TZDate } from '@date-fns/tz';
import { useRegionContext } from 'context/RegionContext';
import { addDays, format, isSunday, isSaturday } from 'date-fns';
import { FormattedMessage } from 'react-intl';

const stripePattern = `repeating-linear-gradient(
  35deg,
  white,
  white 4px,
  ${grey[150]} 4px,
  ${grey[150]} 8px
)`;

const NoLongerResourceIcon = ({ color, height, width }: { color: string; height: number; width: number }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M9.7541 56.9475C11.3534 58.0327 13.2837 58.6666 15.3618 58.6666H48.6479C54.1767 58.6666 58.6563 54.1802 58.6479 48.6514L58.6225 32.0078C58.6183 29.2541 57.4789 26.624 55.4727 24.7377L48.5129 18.1936L46.3904 20.3158L53.4177 26.9233C54.822 28.2437 55.6196 30.0848 55.6225 32.0123L55.6479 48.656C55.6538 52.5261 52.5181 55.6666 48.6479 55.6666H15.3618C14.1157 55.6666 12.9455 55.341 11.9318 54.7701L9.7541 56.9475ZM17.1648 49.5378L25.5345 41.1691C25.8153 41.1673 26.1131 41.1674 26.4304 41.1675L26.6619 41.1675L37.3311 41.167L37.5625 41.167C39.1971 41.1663 40.3125 41.1659 41.2691 41.422C43.8587 42.1151 45.8814 44.1378 46.5747 46.7273C46.8308 47.6839 46.8305 48.7993 46.8299 50.4339L46.8299 50.6653H43.8299C43.8299 48.7104 43.817 48.0269 43.6768 47.5032C43.2608 45.9494 42.0472 44.7359 40.4934 44.32C39.9696 44.1798 39.2862 44.1669 37.3312 44.167L26.662 44.1675C24.7065 44.1676 24.0228 44.1805 23.4989 44.3209C21.9456 44.7369 20.7324 45.9502 20.3164 47.5035C20.1761 48.0274 20.1632 48.7111 20.1632 50.6666H17.1632L17.1632 50.4352C17.1631 50.1173 17.163 49.819 17.1648 49.5378ZM29.7041 37L32.3861 34.3183C34.8367 34.1295 36.7924 32.174 36.9815 29.7235L39.9965 26.7089V29.3333C39.9965 33.7516 36.4148 37.3333 31.9965 37.3333C31.1998 37.3333 30.4303 37.2168 29.7041 37ZM34.8044 19.1733C33.9307 18.8457 32.9845 18.6666 31.9965 18.6666C27.5782 18.6666 23.9965 22.2484 23.9965 26.6666V29.3333C23.9965 29.5428 24.0046 29.7504 24.0204 29.9559L26.9965 26.9801V26.6666C26.9965 23.9052 29.2351 21.6666 31.9965 21.6666C32.0989 21.6666 32.2006 21.6697 32.3015 21.6758L34.8044 19.1733ZM39.8306 14.1477L36.788 11.2868C34.0953 8.7549 29.8981 8.75279 27.2029 11.282L10.5464 26.9121C9.13364 28.2378 8.33348 30.0898 8.33643 32.0273L8.35714 45.6172L5.3617 48.6123L5.33644 32.0318C5.33222 29.2641 6.4753 26.6184 8.49359 24.7244L25.15 9.09431C29.0004 5.4812 34.9963 5.48423 38.8431 9.10121L41.953 12.0255L39.8306 14.1477Z"
        fill={color}
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M0.939464 57.2759L54.2794 3.94254L56.4006 6.06399L3.06065 59.3973L0.939464 57.2759Z"
        fill={color}
      />
    </svg>
  );
};

export default function RenderResourceEndDate({
  endDate,
  numberOfRows,
}: {
  endDate: DateMessage;
  numberOfRows: number;
}) {
  const { timeZone } = useRegionContext();
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const { dateIndexMap } = useDateRange();
  let date = new TZDate(endDate.year, endDate.month - 1, endDate.day, timeZone);
  if (isSaturday(date)) {
    date = addDays(date, 2);
  } else if (isSunday(date)) {
    date = addDays(date, 1);
  }
  const tzDate = format(date, 'yyyy-MM-dd', { in: tz(timeZone) });
  const dateIndex = dateIndexMap.get(tzDate);
  if (!dateIndex) {
    return null;
  }
  const transform = `translate3d(${dateIndex * dayWidth}px,0px,0)`;

  return (
    <Tooltip
      title={
        <Typography variant="body2">
          <FormattedMessage
            id="installationPlanning.noLongerResource"
            defaultMessage="This resource is no longer available"
          />
        </Typography>
      }
      placement="left-start"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            backgroundColor: '#fff',
            borderRadius: '4px',
            padding: '8px',
            boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
          },
        },
        arrow: {
          sx: {
            color: '#fff',
            position: 'absolute',
            transform: 'translateX(-100%)',
          },
        },
      }}
    >
      <div
        style={{
          transform,
          border: `2px solid ${beige[100]}`,
          height: `${CELL_HEIGHT * numberOfRows - 1}px`,
          maxHeight: `${CELL_HEIGHT * numberOfRows - 1}px`,
          width: `calc(100% - ${dateIndex * dayWidth}px)`,
          padding: '0',
          cursor: 'default',
          willChange: 'transform',
          background: stripePattern,
          backfaceVisibility: 'hidden',
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: 'calc(50% - 16px)',
            left: '4px',
            height: '32px',
            width: '32px',
            background: '#fff',
            borderRadius: '50px',
          }}
        >
          <div style={{ position: 'absolute', top: 'calc(50% - 9px)', left: 'calc(50% - 9px)' }}>
            <NoLongerResourceIcon color={grey[600]} height={18} width={18} />
          </div>
        </div>
      </div>
    </Tooltip>
  );
}
