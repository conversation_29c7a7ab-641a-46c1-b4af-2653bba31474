import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import useTableCellDims from '../../hooks/useTableCellDims';
import { useResources, useResourcesLoading } from 'components/installation-planning/contexts/ResourcesContext';
import ResourceImage from '../ResourceImage';

export default function RenderResource({ resourceId, nrRows }: { resourceId: string; nrRows?: number }) {
  const { CELL_HEIGHT } = useTableCellDims();
  const { isLoadingResources } = useResourcesLoading();
  const resourcesForRegion = useResources();
  const resource = resourcesForRegion.get(resourceId);

  if (isLoadingResources && !resourcesForRegion) {
    return <Skeleton variant="text" width={100} height="16px" />;
  }

  if (!resource) {
    return null;
  }

  const tooltipInfo = () => (
    <Stack p={1} spacing={1}>
      <Stack direction="row" spacing={2} alignItems="center">
        <ResourceImage resource={resource} size="large" />
        <Stack direction="column" spacing={0}>
          <Typography variant="body1">
            {resource.firstName} {resource.lastName}
          </Typography>
          <Typography variant="body2Emphasis">{resource.jobTitle}</Typography>
        </Stack>
      </Stack>
    </Stack>
  );

  const nrRowsToRender = nrRows ?? 1;
  return (
    <Stack
      direction="row"
      spacing={1}
      sx={{
        height: `${CELL_HEIGHT * nrRowsToRender}px`,
        minHeight: `${CELL_HEIGHT * nrRowsToRender}px`,
        width: '190px',
        maxWidth: '190px',
        justifyContent: 'flex-start',
        alignItems: nrRowsToRender > 1 ? 'flex-start' : 'center',
        pl: 3,
        pr: 2,
        pt: nrRowsToRender > 1 ? 1 : 0,
        background: '#fff',
      }}
    >
      <ResourceImage resource={resource} size="small" />

      <Tooltip
        title={tooltipInfo()}
        placement="right"
        componentsProps={{
          tooltip: {
            sx: {
              backgroundColor: '#fff',
              borderRadius: '16px',
              boxShadow: '0px 25px 36px 0px rgba(0, 0, 0, 0.25)',
            },
          },
          arrow: {
            sx: {
              backgroundColor: '#fff',
            },
          },
        }}
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography variant="body2" fontSize={12}>
            {resource.firstName} {resource.lastName}
          </Typography>
        </Stack>
      </Tooltip>
    </Stack>
  );
}
