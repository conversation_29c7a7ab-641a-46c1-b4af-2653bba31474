import React from 'react';
import { beige } from '@ui/theme/colors';
import {
  useProjectActions,
  useShowCompactView,
  useUnsavedChangesWithoutReason,
  useUnsavedChanges,
  useDispatchMode,
  useJobsToDispatch,
  useFocusedProject,
  useSelectedProject,
  useJobBeingSplit,
  useHighlightProjectsWithinDistance,
  useHighlightFlexibleProjects,
  useHoveredUnsavedChangeJobId,
  useSplitSegmentsToMove,
  useJobBeingDragged,
} from 'components/installation-planning/stores/ProjectStore';
import { Typography } from '@mui/material';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import useTableCellDims from '../../hooks/useTableCellDims';
import RenderResource from './RenderResource';
import DraggableJobContainer from '../../jobBar/DraggableJobContainer';
import {
  ResourceBasedSegments,
  ResourceForRegion,
  resourceMapping,
  ResourceType,
  reverseResourceMapping,
} from '../../types/planningTypes';
import RenderServiceVisitsForResource from './RenderServiceVisitsForResource';
import { useDateRange } from '../../contexts/DateRangeContext';
import { useServicesVisits } from '../../contexts/ServicesVisitsContext';
import { useCalculateRowsForIndividuals } from '../../useCalculateRowsForIndividuals';
import RenderDatesForResourceRow from './RenderDatesForResourceRow';
import RenderInactivities from './Inactivities/RenderInactivities';
import { useCanEdit } from 'components/installation-planning/hooks/useCanEdit';
import { useRegionContext } from 'context/RegionContext';
import { api } from 'utils/api';
import { useProjectsUpdatedSince } from 'components/installation-planning/contexts/ProjectsUpdatedSinceContext';
import { useVirtualizedTimeline } from '../useVirtualizedTimeline';
import RenderResourceEndDate from './RenderResourceEndDate';
export default function ResourcesTableContainer({
  requiredRoles,
  continuousSegmentsByResource,
  fieldResourcesWithDataMap,
  resourceTypesToShow,
  tableRef,
}: {
  requiredRoles: number[];
  continuousSegmentsByResource?: ResourceBasedSegments;
  fieldResourcesWithDataMap: Map<string, ResourceForRegion>;
  resourceTypesToShow: ResourceType[];
  tableRef: React.RefObject<HTMLDivElement>;
}) {
  const { serviceVisitsForResourcesMap } = useServicesVisits();
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const isCompactView = useShowCompactView();
  const { weekdayMetrics } = useDateRange();
  const daysInInterval = weekdayMetrics.weekdaysInRange.length;
  const tableWidth = dayWidth * daysInInterval;
  const {
    setSelectedResourceHalfDay,
    setOverlappingSegmentsForResources,
    setJobBeingDragged,
    setSelectedProject,
    setShowUnsavedChanges,
    updateUnsavedChanges,
    setJobBeingSplit,
    setShowRightSidebar,
    setSplitSegmentsToMove,
    setToolTipProject,
    setJobsToDispatch,
    setRightClickMenu,
    setHoveredProject,
  } = useProjectActions();
  const highlightProjectsWithinDistance = useHighlightProjectsWithinDistance();
  const highlightFlexibleProjects = useHighlightFlexibleProjects();
  const hoveredUnsavedChangeJobId = useHoveredUnsavedChangeJobId();
  const jobBeingDragged = useJobBeingDragged();
  const splitSegmentsToMove = useSplitSegmentsToMove();
  const jobsToDispatch = useJobsToDispatch();
  const focusedProject = useFocusedProject();
  const selectedProject = useSelectedProject();
  const jobBeingSplit = useJobBeingSplit();
  const dispatchMode = useDispatchMode();
  const unsavedChanges = useUnsavedChanges();
  const unsavedChangesWithoutReason = useUnsavedChangesWithoutReason();
  const { timeZone, id: regionId } = useRegionContext();
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const canEdit = useCanEdit();
  const { refetchProjectsUpdatedSince, initialLoadTime } = useProjectsUpdatedSince();
  const resourceRows = useCalculateRowsForIndividuals({
    continuousSegmentsByResource: continuousSegmentsByResource || new Map(),
  });
  // Extract array of resource IDs for rendering the table rows after first sorting by name
  const fieldResourceIds = Array.from(fieldResourcesWithDataMap.keys()).sort((a, b) => {
    const resourceA = fieldResourcesWithDataMap.get(a);
    const resourceB = fieldResourcesWithDataMap.get(b);
    return resourceA?.firstName.localeCompare(resourceB?.firstName ?? '') ?? 0;
  });
  const halfDayWidth = dayWidth / 2;

  const { visibleDates } = useVirtualizedTimeline(tableRef, halfDayWidth, weekdayMetrics.halfDaysInRange);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        padding: 0,
        width: `${tableWidth + 190}px`,
        zIndex: 1200,
      }}
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          padding: 0,
          width: `${tableWidth}px`,
          zIndex: 1200,
        }}
      >
        {resourceTypesToShow.map((resourceType) => (
          <div
            key={resourceType}
            style={{
              display: 'flex',
              flexDirection: 'column',
              padding: 0,
              width: `${tableWidth}px`,
              zIndex: 4200,
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                height: `${CELL_HEIGHT + 12}px`,
                position: 'sticky',
                left: 0,
                top: 110,
                zIndex: 4202,
                background: '#fff',
                borderRight: '1px solid #D3D8D9',
                borderBottom: '2px solid #D3D8D9',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  height: `${CELL_HEIGHT + 12}px`,
                  width: '191px',
                  minWidth: '191px',
                  padding: 0,
                  paddingLeft: '16px',
                  position: 'sticky',
                  left: 0,
                  top: 117,
                  zIndex: 1202,
                  background: beige[100],
                  borderRight: '1px solid #D3D8D9',
                  borderTop: '1px solid #D3D8D9',
                  borderBottom: '1px solid #D3D8D9',
                }}
              >
                <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                  {resourceType.toLowerCase()} Resources
                </Typography>
              </div>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  height: `${CELL_HEIGHT + 12}px`,
                  maxHeight: `${CELL_HEIGHT + 12}px`,
                  padding: 0,
                  width: `${tableWidth}px`,
                  position: 'sticky',
                  left: 0,
                  top: 117,
                  zIndex: 1200,
                }}
              >
                {weekdayMetrics.weekdaysInRange.map(({ date }, index) => {
                  if (index % 5 === 0) {
                    return (
                      <div
                        key={date.toISOString() + resourceType}
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          width: `${dayWidth * 5}px`,
                          maxWidth: `${dayWidth * 5}px`,
                          padding: isCompactView ? '0px' : '10px',
                          background: '#fff',
                          height: `${CELL_HEIGHT + 12}px`,
                          maxHeight: `${CELL_HEIGHT + 12}px`,
                          borderRight: '2px solid #D3D8D9',
                          borderTop: '1px solid #D3D8D9',
                          borderBottom: '1px solid #D3D8D9',
                        }}
                      />
                    );
                  }
                  return null;
                })}
              </div>
            </div>

            {fieldResourceIds.map((resourceId, index) => {
              const resource = fieldResourcesWithDataMap.get(resourceId);
              const roles = resource?.roles;

              if (resource?.primaryOperationalUnit?.id?.value !== regionId?.value) {
                return null;
              }

              const nrRows = resourceRows[resourceId]?.nrRows ?? 1;
              const serviceVisitsForResource = serviceVisitsForResourcesMap.get(resourceId) ?? [];
              const endDate = resource?.endDate;
              if (resource?.enabled === false) {
                return null;
              }
              if (!roles?.some((role) => role !== undefined)) {
                return null;
              }

              if (
                roles?.some(
                  (role) =>
                    Boolean(role) &&
                    reverseResourceMapping[role as keyof typeof reverseResourceMapping] === resourceType,
                )
              ) {
                return (
                  <div
                    key={`$${resourceId}`}
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 0,
                      width: `100%`,
                      height: `${CELL_HEIGHT * nrRows}px`,
                      maxHeight: `${CELL_HEIGHT * nrRows}px`,
                      padding: 0,
                      zIndex: 1202,
                      position: 'relative',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        gap: 0,
                        position: 'relative',
                        width: '100%',
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          height: '100%',
                          position: 'sticky',
                          top: 0,
                          left: 0,
                          zIndex: 1203,
                          background: beige[100],
                          borderRight: '1px solid #D3D8D9',
                          width: '191px',
                          minWidth: '191px',
                          transform: 'translate3d(0,0,0)',
                        }}
                      >
                        <RenderResource nrRows={nrRows} key={resourceId} resourceId={resourceId} />
                      </div>
                      <RenderServiceVisitsForResource serviceVisits={serviceVisitsForResource} />
                      <RenderDatesForResourceRow
                        requiredRole={resourceMapping[resourceType as keyof typeof resourceMapping]}
                        rowIndex={index}
                        numberOfRows={nrRows}
                        resourceId={resourceId}
                        setSelectedResourceHalfDay={setSelectedResourceHalfDay}
                        canEdit={canEdit}
                        visibleDates={visibleDates}
                      />
                      <RenderInactivities resourceId={resourceId} numberOfRows={nrRows} />
                      {endDate && <RenderResourceEndDate numberOfRows={nrRows} endDate={endDate} />}
                    </div>

                    <div style={{ display: 'flex', flexDirection: 'row', gap: 0 }}>
                      {resourceRows[resourceId]?.row.map(
                        (row, startPositon) =>
                          row &&
                          row.jobs?.map((cell) => (
                            <DraggableJobContainer
                              CELL_HEIGHT={CELL_HEIGHT}
                              setToolTipProject={setToolTipProject}
                              setRightClickMenu={setRightClickMenu}
                              setHoveredProject={setHoveredProject}
                              continuousWorkSegment={cell.continuousWorkSegment}
                              span={cell.span}
                              innerRowNumber={cell.innerRowNumber}
                              startPositon={startPositon}
                              key={`${cell.continuousWorkSegment.segmentDetails.startTime}-${cell.continuousWorkSegment.segmentDetails.jobId}-${requiredRoles[0]}-${resourceId}`}
                              resourceId={resourceId}
                              continuousSegmentsByResource={continuousSegmentsByResource || new Map()}
                              setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
                              unsavedChanges={unsavedChanges}
                              unsavedChangesWithoutReason={unsavedChangesWithoutReason}
                              timeZone={timeZone}
                              regionId={regionId!.value}
                              user={user as UserIdentity}
                              canEdit={canEdit}
                              refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
                              initialLoadTime={initialLoadTime}
                              setJobBeingDragged={setJobBeingDragged}
                              setSelectedProject={setSelectedProject}
                              setShowUnsavedChanges={setShowUnsavedChanges}
                              updateUnsavedChanges={updateUnsavedChanges}
                              dispatchMode={dispatchMode}
                              setShowRightSidebar={setShowRightSidebar}
                              setSplitSegmentsToMove={setSplitSegmentsToMove}
                              setJobsToDispatch={setJobsToDispatch}
                              jobsToDispatch={jobsToDispatch}
                              dayWidth={dayWidth}
                              focusedProject={focusedProject}
                              selectedProject={selectedProject}
                              jobBeingSplit={jobBeingSplit}
                              setJobBeingSplit={setJobBeingSplit}
                              highlightProjectsWithinDistance={highlightProjectsWithinDistance}
                              highlightFlexibleProjects={highlightFlexibleProjects}
                              hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
                              splitSegmentsToMove={splitSegmentsToMove}
                              jobBeingDragged={jobBeingDragged}
                            />
                          )),
                      )}
                    </div>
                  </div>
                );
              }
              return null;
            })}
          </div>
        ))}
      </div>
    </div>
  );
}
