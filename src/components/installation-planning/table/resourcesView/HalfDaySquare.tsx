import { memo, useCallback, useMemo, useRef, useState } from 'react';
import { brandYellow } from '@ui/theme/colors';
import IconButton from '@mui/material/IconButton';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import useTableCellDims from '../../hooks/useTableCellDims';

// Constants
const BORDER_COLOR = '#D3D8D9';

// Memoized style calculations
const getBorderWidths = (startTime: '08' | '12', isDateFriday: boolean) => {
  if (startTime === '12') {
    return {
      rightBorderWidth: isDateFriday ? '2px' : '1px',
      leftBorderWidth: '0px',
    };
  }
  return {
    rightBorderWidth: isDateFriday ? '2px' : '1px',
    leftBorderWidth: '1px',
  };
};

interface HalfDaySquareProps {
  date: Date;
  startTime: '08' | '12';
  resourceIsInactive?: boolean;
  numberOfRows?: number;
  resourceId: string;
  transform: string;
  setSelectedResourceHalfDay: (resourceHalfDay: { resourceId: string; date: Date; startTime: '08' | '12' }) => void;
  canEdit: boolean;
  isDateFriday: boolean;
}

const HalfDaySquare = memo(function HalfDaySquare({
  date,
  startTime,
  resourceIsInactive,
  numberOfRows = 1,
  resourceId,
  transform,
  setSelectedResourceHalfDay,
  canEdit,
  isDateFriday,
}: HalfDaySquareProps) {
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const dateRef = useRef(null);
  const [isOver, setIsOver] = useState(false);

  const borderWidths = useMemo(() => getBorderWidths(startTime, isDateFriday), [startTime, isDateFriday]);

  const handleClickAdd = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      if (!canEdit) {
        return;
      }
      setSelectedResourceHalfDay({ resourceId, date, startTime });
    },
    [canEdit, setSelectedResourceHalfDay, resourceId, date, startTime],
  );

  return (
    <div
      ref={dateRef}
      onMouseEnter={() => setIsOver(true)}
      onMouseLeave={() => setIsOver(false)}
      style={{
        position: 'absolute',
        top: 0,
        transform,
        borderRight: `${borderWidths.rightBorderWidth} solid ${BORDER_COLOR}`,
        borderBottom: `1px solid ${BORDER_COLOR}`,
        borderLeft: `${borderWidths.leftBorderWidth} solid ${BORDER_COLOR}`,
        height: `${CELL_HEIGHT * numberOfRows}px`,
        maxHeight: `${CELL_HEIGHT * numberOfRows}px`,
        minWidth: `${dayWidth / 2}px`,
        width: `${dayWidth / 2}px`,
        padding: '0',
        backgroundColor: '#fff',
        cursor: canEdit ? 'pointer' : 'default',
        willChange: 'transform',
        backfaceVisibility: 'hidden',
      }}
    >
      {isOver && !resourceIsInactive && (
        <div
          style={{
            position: 'absolute',
            top: -1,
            left: -1,
            width: 'calc(100% + 2px)',
            height: 'calc(100% + 2px)',
            border: `2px solid ${brandYellow[400]}`,
            zIndex: 1000,
          }}
        >
          <IconButton
            sx={{
              position: 'absolute',
              top: 'calc(50% - 12px)',
              left: 'calc(50% - 12px)',
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              background: brandYellow[400],
              padding: 0,
            }}
            onClick={handleClickAdd}
            disableRipple
          >
            <AddOutlinedIcon height={24} width={24} color="#fff" />
          </IconButton>
        </div>
      )}
    </div>
  );
});

export default HalfDaySquare;
