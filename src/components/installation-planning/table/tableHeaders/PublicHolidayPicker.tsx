import { Box, IconButton, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import { Button } from '@ui/components/Button/Button';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { brandYellow, red } from '@ui/theme/colors';
import DatePickerCustomHeader from 'components/installation-planning/components/DatePickerHeader';
import { usePublicHolidays } from 'components/installation-planning/contexts/PublicHolidaysContext';
import { useResourceUnutilization } from 'components/installation-planning/contexts/ResourceUnutilizationContext';
import { useRegionContext } from 'context/RegionContext';
import { addYears, endOfYear, startOfYear } from 'date-fns';
import { useState } from 'react';
import DatePicker from 'react-datepicker';
import { api } from 'utils/api';

export default function PublicHolidayPicker({ handleClose }: { handleClose: () => void }) {
  const region = useRegionContext();
  const { refetchResourceUnutilization } = useResourceUnutilization();
  const { regionalHolidays, nationalHolidays, refetchFindHolidays } = usePublicHolidays();
  const { mutateAsync: addRegionalHolidays } = api.Resource.addRegionalHolidays.useMutation();
  const { mutateAsync: deleteHolidays } = api.Resource.deleteHolidays.useMutation();

  const today = new Date();
  const startDate = startOfYear(today);
  const endDate = addYears(endOfYear(today), 1);

  const initialDates = regionalHolidays.map((holiday) => holiday.date);
  const blockedDates = nationalHolidays.map((holiday) => holiday.date);

  const [selectedDates, setSelectedDates] = useState<Date[]>(initialDates);

  const handleSave = async () => {
    handleClose();

    await handleAddHolidays();
    await handleRemoveHolidays();

    refetchFindHolidays();
    refetchResourceUnutilization();
  };

  const handleAddHolidays = async () => {
    const datesToAdd = selectedDates.filter(
      (date) => initialDates.find((item) => item.getTime() === date.getTime()) === undefined,
    );

    if (datesToAdd.length > 0) {
      await addRegionalHolidays({ datesToAdd, operationalUnitId: region.id!.value, timeZone: region.timeZone });
    }
  };

  const handleRemoveHolidays = async () => {
    const datesToRemove = initialDates?.filter(
      (date) => selectedDates.find((item) => item.getTime() === date.getTime()) === undefined,
    );

    const holidaysToRemove = regionalHolidays
      .filter((holiday) => {
        return datesToRemove?.some((date) => date.getTime() === holiday.date.getTime());
      })
      .map((holiday) => holiday.id);

    if (holidaysToRemove.length > 0) {
      await deleteHolidays({ holidayIds: holidaysToRemove });
    }
  };

  const handleSelect = (value: Date[] | null) => {
    if (value) {
      setSelectedDates(value);
    }
  };

  return (
    <Stack
      p={3}
      gap={3}
      sx={{
        '.react-datepicker__day--today': {
          backgroundColor: ` ${brandYellow[400]} !important`,
        },
        '.react-datepicker__day--selected': {
          backgroundColor: `${red[500]} !important`,
        },
        '.react-datepicker__day:hover': {
          backgroundColor: `${red[500]} !important`,
        },
        '.react-datepicker__day--disabled': {
          ':hover': {
            backgroundColor: `white !important`,
            color: `#ccc`,
          },
        },
        '.react-datepicker__day--excluded': {
          color: `${red[500]}`,
          fontWeight: 500,
          ':hover': {
            backgroundColor: `white`,
            color: `${red[500]}`,
          },
        },
      }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="body1Emphasis">Edit Holidays</Typography>
        <IconButton
          onClick={handleClose}
          sx={{
            borderRadius: '50%',
            padding: 0,
            height: 24,
            width: 24,
          }}
        >
          <CrossOutlinedIcon height={20} width={20} />
        </IconButton>
      </Box>
      <DatePicker
        selectedDates={selectedDates}
        excludeDates={blockedDates}
        selectsMultiple
        inline
        includeDateIntervals={[{ start: startDate, end: endDate }]}
        shouldCloseOnSelect={false}
        onChange={handleSelect}
        disabledKeyboardNavigation
        renderCustomHeader={DatePickerCustomHeader}
      />
      <Box display="flex" gap={2}>
        {selectedDates.length > 0 && (
          <Box
            display="flex"
            justifyContent="center"
            alignItems="center"
            sx={{ backgroundColor: '#22222608', width: '56px', borderRadius: 1 }}
          >
            <Typography variant="body1Emphasis" color={red[500]}>
              {selectedDates.length}
            </Typography>
          </Box>
        )}
        <Button sx={{ flex: 1 }} onClick={handleSave}>
          Done
        </Button>
      </Box>
    </Stack>
  );
}
