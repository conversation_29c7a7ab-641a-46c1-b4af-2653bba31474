import { Box, Stack, Typography } from '@mui/material';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import useTableCellDims from '../../hooks/useTableCellDims';

export default function WeekNumberHeader({ tableWidth }: { tableWidth: number }) {
  const { weekdayMetrics } = useDateRange();
  const { nrWeekdaysPerWeekNumber } = weekdayMetrics;
  const { dayWidth } = useTableCellDims();
  return (
    <Stack
      direction="row"
      sx={{
        position: 'sticky',
        left: 0,
        top: 47,
        zIndex: 1201,
        width: `${tableWidth}px`,
      }}
    >
      <Box
        sx={{
          position: 'sticky',
          left: 0,
          top: 0,
          minWidth: '190px',
          height: '24px',
          background: '#fff',
          zIndex: 1201,
        }}
      />
      {nrWeekdaysPerWeekNumber.map((week) => (
        <Box
          sx={{
            position: 'sticky',
            left: 190,
            top: 0,
            border: 'none',
            background: '#fff',
            minWidth: `${dayWidth * week.nrDays}px`,
            paddingLeft: '26px',
          }}
          key={week.week + week.year}
        >
          <Typography variant="body1">Wk {week.week}</Typography>
        </Box>
      ))}
    </Stack>
  );
}
