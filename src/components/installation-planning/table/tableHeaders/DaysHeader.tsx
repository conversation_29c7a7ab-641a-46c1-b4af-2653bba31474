import React from 'react';
import { Typography, Stack, Box } from '@mui/material';
import { TZDate } from '@date-fns/tz';
import { red } from '@mui/material/colors';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import { useRegionContext } from '../../../../context/RegionContext';
import useTableCellDims from '../../hooks/useTableCellDims';
import { useShowCompactView } from '../../stores/ProjectStore';
import PublicHolidaySelector from './PublicHolidaySelector';

export default function DaysHeader({ tableWidth }: { tableWidth: number }) {
  const { weekdayMetrics } = useDateRange();
  const { weekdaysInRange } = weekdayMetrics;
  const isCompactView = useShowCompactView();
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const region = useRegionContext();
  const { timeZone } = region;
  const now = TZDate.tz(timeZone).toISOString().split('T')[0];

  return (
    <Stack
      direction="row"
      sx={{
        position: 'sticky',
        left: 0,
        top: 71,
        zIndex: 1202,
        borderTop: 'none',
        borderBottom: '1px solid #D3D8D9',
        width: `${tableWidth}px`,
      }}
    >
      <Box
        sx={{
          position: 'sticky',
          left: 0,
          top: 0,
          minWidth: '190px',
          height: '45px',
          background: '#fff',
          zIndex: 1201,
          pl: 2,
        }}
        display="flex"
        alignItems="center"
      >
        <PublicHolidaySelector />
      </Box>
      {weekdaysInRange.map(({ date, isPublicHoliday }, index) => {
        const dateString = date.toISOString().split('T')[0]!;
        return (
          <Stack
            key={dateString}
            direction="row"
            justifyContent="center"
            alignItems="center"
            sx={{
              height: `${CELL_HEIGHT}px`,
              width: `${dayWidth}px`,
              maxWidth: `${dayWidth}px`,
              padding: '0px',
              borderRight: index === 4 || (index + 1) % 5 === 0 ? '1px solid #D3D8D9' : 'none',
              borderLeft: index === 0 || index % 5 === 0 ? '1px solid #D3D8D9' : 'none',
              background: '#fff',
            }}
          >
            <Typography
              variant={isCompactView ? 'body2' : 'body1'}
              sx={{
                textAlign: 'center',
                backgroundColor: dateString === now ? '#FFAF51' : '#fff',
                padding: isCompactView ? '2px 6px' : '4px 8px',
                borderRadius: '12px',
                width: `${isCompactView ? dayWidth - 20 : dayWidth}px`,
                maxWidth: `${isCompactView ? dayWidth - 20 : dayWidth}px`,
                color: isPublicHoliday ? red[600] : '#000',
                lineHeight: isCompactView ? '16px' : '20px',
              }}
            >
              {date.toLocaleDateString(undefined, { weekday: 'short', day: 'numeric' })}
            </Typography>
          </Stack>
        );
      })}
    </Stack>
  );
}
