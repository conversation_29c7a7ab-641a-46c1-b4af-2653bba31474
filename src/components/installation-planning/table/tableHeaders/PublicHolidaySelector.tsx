import { Button } from '@ui/components/Button/Button';
import { StarOutlinedIcon } from '@ui/components/StandardIcons/StarOutlinedIcon';
import { useState } from 'react';
import PublicHolidayPicker from './PublicHolidayPicker';
import { Popover } from '@mui/material';
import { grey } from '@mui/material/colors';

export default function PublicHolidaySelector({}) {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <Button
        size="small"
        sx={{
          px: 1,
          fontSize: '10px',
          height: '25px',
          fontWeight: '400',
          background: !open ? '#22222608' : '#2222260F',
          borderRadius: '20px',
          color: grey[900],

          '&:hover': {
            background: '#2222260F',
          },
          '.MuiButton-startIcon': {
            mr: '2px',
          },
        }}
        onClick={(event) => {
          handleClick(event);
        }}
        startIcon={<StarOutlinedIcon width="11px" height="11px" />}
      >
        Edit Holidays
      </Button>
      <Popover
        open={open}
        onClose={handleClose}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        sx={{ ml: 2, borderRadius: '22px' }}
        slotProps={{
          paper: {
            sx: {
              borderRadius: '22px',
            },
          },
        }}
      >
        <PublicHolidayPicker handleClose={handleClose} />
      </Popover>
    </div>
  );
}
