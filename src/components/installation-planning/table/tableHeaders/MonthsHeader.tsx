import { Box, Stack, Typography } from '@mui/material';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import useTableCellDims from '../../hooks/useTableCellDims';

export default function MonthsHeader({ tableWidth }: { tableWidth: number }) {
  const { weekdayMetrics } = useDateRange();
  const { nrOfWeekdaysPerMonth } = weekdayMetrics;
  const { dayWidth } = useTableCellDims();
  return (
    <Stack
      direction="row"
      sx={{
        position: 'sticky',
        left: 0,
        top: 0,
        width: `${tableWidth}px`,
        zIndex: 1201,
        height: '47px',
        background: '#fff',
      }}
    >
      {nrOfWeekdaysPerMonth.map((month, index) => (
        <Box
          sx={{
            position: 'sticky',
            left: 0,
            top: 0,
            border: 'none',
            pl: 2,
            background: '#fff',
            width: `${dayWidth * month.numberOfWeekdays + (index === 0 ? 190 : 0)}px`,
            maxWidth: `${dayWidth * month.numberOfWeekdays + (index === 0 ? 190 : 0)}px`,
            height: '47px',
          }}
          key={month.month + month.year}
        >
          <Stack spacing={month.numberOfWeekdays < 2 ? 0 : 1} direction="row" alignItems="center">
            <Typography variant="headline3">
              {' '}
              {month.numberOfWeekdays < 2 ? month.month.substring(0, 3) : month.month}
            </Typography>
            <Typography variant="headline3" sx={{ fontWeight: 400 }}>
              {month.year}
            </Typography>
          </Stack>
        </Box>
      ))}
    </Stack>
  );
}
