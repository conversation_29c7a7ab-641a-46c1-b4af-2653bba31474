import React from 'react';
import ResourceGroupTableContainer from './ResourceGroupTableContainer';
import { ResourceBasedSegments, ResourceType, RoleBasedSegments } from '../../types/planningTypes';

export default function TeamsTable({
  resourceTypesToShow,
  continuousSegmentsByResource,
  continuousSegmentsByRole,
  tableRef,
}: {
  resourceTypesToShow: ResourceType[];
  requiredRoles: number[];
  continuousSegmentsByResource?: ResourceBasedSegments;
  continuousSegmentsByRole?: RoleBasedSegments;
  tableRef: React.RefObject<HTMLDivElement>;
}) {
  if (!continuousSegmentsByRole) return null;

  return resourceTypesToShow.map((resourceType) => (
    <ResourceGroupTableContainer
      key={resourceType}
      resourceType={resourceType}
      continuousSegmentsByRole={continuousSegmentsByRole}
      continuousSegmentsByResource={continuousSegmentsByResource}
      tableRef={tableRef}
    />
  ));
}
