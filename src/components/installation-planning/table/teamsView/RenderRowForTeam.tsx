import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/job';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { Stack } from '@mui/material';
import { beige } from '@ui/theme/colors';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import { isAfter } from 'date-fns';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  ResourceBasedSegments,
  Row,
  UnsavedChange,
} from '../../types/planningTypes';
import RenderDatesForRow from './RenderDatesForRow';
import RenderJobsForTeam from './RenderJobsForTeam';
import RenderTeamNames from './RenderTeamNames';
import { useSelectedProject } from 'components/installation-planning/stores/ProjectStore';
import { VisibleDate } from '../useVirtualizedTimeline';
import { WeekdayMetrics } from 'components/installation-planning/helpers/dateHelpers';

export default function RenderRowForTeam({
  CELL_HEIGHT,
  canEdit,
  continuousSegmentsByResource,
  dayWidth,
  dispatchMode,
  focusedProject,
  highlightFlexibleProjects,
  highlightProjectsWithinDistance,
  hoveredUnsavedChangeJobId,
  jobBeingDragged,
  jobBeingSplit,
  jobsToDispatch,
  initialLoadTime,
  refetchProjectsUpdatedSince,
  refetchTeamAvailability,
  regionId,
  requiredRole,
  roleKey,
  setDroppableStartTime,
  setHoveredProject,
  setJobBeingDragged,
  setJobBeingSplit,
  setJobsToDispatch,
  setOverlappingSegmentsForResources,
  setRightClickMenu,
  setSelectedProject,
  setShowRightSidebar,
  setShowUnsavedChanges,
  setSplitSegmentsToMove,
  setToolTipProject,
  splitSegmentsToMove,
  tableRef,
  teamRow,
  teamUnavailabilityMode,
  timeZone,
  unsavedChanges,
  unsavedChangesWithoutReason,
  updateUnsavedChanges,
  user,
  visibleDates,
  workingHoursForDatesMap,
}: {
  visibleDates: VisibleDate[];
  workingHoursForDatesMap: WeekdayMetrics['workingHoursForDatesMap'];
  CELL_HEIGHT: number;
  canEdit: boolean;
  continuousSegmentsByResource: ResourceBasedSegments;
  dayWidth: number;
  dispatchMode: boolean;
  focusedProject?: FullInstallationProjectEntity;
  hoveredUnsavedChangeJobId: null | string;
  jobBeingDragged?: ContinuousWorkSegment;
  jobBeingSplit?: ContinuousWorkSegment;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  initialLoadTime: Date;
  regionId: string;
  requiredRole: number;
  roleKey: string;
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  tableRef: React.RefObject<HTMLDivElement>;
  teamRow: {
    numberOfRows: number;
    row: Row;
    team: Team;
  };
  teamUnavailabilityMode: boolean;
  timeZone: string;
  unsavedChanges: UnsavedChange[];
  unsavedChangesWithoutReason: UnsavedChange[];
  user: UserIdentity;
  highlightFlexibleProjects: boolean;
  highlightProjectsWithinDistance?: number;
  refetchProjectsUpdatedSince: () => void;
  refetchTeamAvailability: () => void;
  setDroppableStartTime: (date: Date | null) => void;
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setJobBeingDragged: (job?: ContinuousWorkSegment) => void;
  setJobBeingSplit: (job?: ContinuousWorkSegment) => void;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
  setOverlappingSegmentsForResources: (
    overlappingSegmentsForResources: Map<
      string,
      {
        overlappingSegments: {
          segments: {
            customerName: string;
            jobId: string;
            segment: InstallationProjectJob_WorkSegment;
          }[];
        };
      }[]
    >,
  ) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      hasAssignedResources: boolean;
      jobId: string;
      position: {
        up: boolean;
        x: number;
        y: number;
      };
      project: ContinuousWorkSegmentForJobItem;
    },
  ) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setShowRightSidebar: (show: boolean) => void;
  setShowUnsavedChanges: (show: boolean) => void;
  setSplitSegmentsToMove: (
    segments: null | {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    },
  ) => void;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    position: {
      arrowTop: number;
      right: number;
      top: number;
    };
    resourceId?: string;
  }) => void;
  updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;
}) {
  const { dateRange } = useDateRange();
  const selectedProject = useSelectedProject();
  if (teamRow.team.deletedAt && isAfter(dateRange.startDate, teamRow.team.deletedAt)) {
    return null;
  }

  return (
    <Stack
      direction="row"
      gap={0}
      key={teamRow.team.id!.value}
      sx={{
        height: `${CELL_HEIGHT * teamRow.numberOfRows}px`,
        maxHeight: `${CELL_HEIGHT * teamRow.numberOfRows}px`,
        padding: 0,
        position: 'relative',
        width: `100%`,
      }}
    >
      <Stack
        sx={{
          background: beige[100],
          borderRight: '1px solid #D3D8D9',
          height: '100%',
          left: 0,
          position: 'sticky',
          top: 110,
          zIndex: 1202,
        }}
      >
        <RenderTeamNames requiredRole={requiredRole} row={teamRow} />
      </Stack>
      <RenderDatesForRow
        CELL_HEIGHT={CELL_HEIGHT}
        canEdit={canEdit}
        dayWidth={dayWidth}
        numberOfRows={teamRow.numberOfRows}
        refetchTeamAvailability={refetchTeamAvailability}
        regionId={regionId}
        requiredRole={requiredRole}
        roleKey={roleKey}
        rowIndex={0}
        setToolTipProject={setToolTipProject}
        tableRef={tableRef}
        team={teamRow.team}
        teamUnavailabilityMode={teamUnavailabilityMode}
        timeZone={timeZone}
        setDroppableStartTime={setDroppableStartTime}
        visibleDates={visibleDates}
        workingHoursForDatesMap={workingHoursForDatesMap}
      />
      <RenderJobsForTeam
        CELL_HEIGHT={CELL_HEIGHT}
        canEdit={canEdit}
        continuousSegmentsByResource={continuousSegmentsByResource}
        dayWidth={dayWidth}
        dispatchMode={dispatchMode}
        focusedProject={focusedProject}
        highlightFlexibleProjects={highlightFlexibleProjects}
        highlightProjectsWithinDistance={highlightProjectsWithinDistance}
        hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
        jobBeingDragged={jobBeingDragged}
        jobBeingSplit={jobBeingSplit}
        jobsToDispatch={jobsToDispatch}
        initialLoadTime={initialLoadTime}
        refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
        regionId={regionId}
        requiredRole={requiredRole}
        row={teamRow.row}
        selectedProject={selectedProject}
        setHoveredProject={setHoveredProject}
        setJobBeingDragged={setJobBeingDragged}
        setJobBeingSplit={setJobBeingSplit}
        setJobsToDispatch={setJobsToDispatch}
        setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
        setRightClickMenu={setRightClickMenu}
        setSelectedProject={setSelectedProject}
        setShowRightSidebar={setShowRightSidebar}
        setShowUnsavedChanges={setShowUnsavedChanges}
        setSplitSegmentsToMove={setSplitSegmentsToMove}
        setToolTipProject={setToolTipProject}
        splitSegmentsToMove={splitSegmentsToMove}
        team={teamRow.team}
        timeZone={timeZone}
        unsavedChanges={unsavedChanges}
        unsavedChangesWithoutReason={unsavedChangesWithoutReason}
        updateUnsavedChanges={updateUnsavedChanges}
        user={user}
      />
    </Stack>
  );
}
