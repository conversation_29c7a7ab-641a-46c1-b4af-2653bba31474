import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { useEffect, useState } from 'react';
import DroppableDate from './DroppableDate';
import { useAvailability } from '../../contexts/AvailabilityContext';
import { ContinuousWorkSegment } from 'components/installation-planning/types/planningTypes';
import AvailabilityHalfDay from './AvailabilityHalfDay';
import { WeekdayMetrics } from 'components/installation-planning/helpers/dateHelpers';
import { VisibleDate } from '../useVirtualizedTimeline';

function RenderDatesForRow({
  roleKey,
  requiredRole,
  rowIndex,
  team,
  numberOfRows,
  canEdit,
  timeZone,
  regionId,
  CELL_HEIGHT,
  dayWidth,
  setToolTipProject,
  refetchTeamAvailability,
  teamUnavailabilityMode,
  setDroppableStartTime,
  visibleDates,
  workingHoursForDatesMap,
}: {
  roleKey: string;
  requiredRole: number;
  rowIndex: number;
  team?: Team;
  numberOfRows?: number;
  tableRef: React.RefObject<HTMLDivElement>;
  canEdit: boolean;
  timeZone: string;
  regionId: string;
  CELL_HEIGHT: number;
  dayWidth: number;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    resourceId?: string;
    position: {
      top: number;
      right: number;
      arrowTop: number;
    };
  }) => void;
  refetchTeamAvailability: () => void;
  teamUnavailabilityMode: boolean;
  setDroppableStartTime: (date: Date | null) => void;
  visibleDates: VisibleDate[];
  workingHoursForDatesMap: WeekdayMetrics['workingHoursForDatesMap'];
}) {
  const { availabilityMap } = useAvailability();
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => setHydrated(true));
    } else {
      setTimeout(() => setHydrated(true), 50); // fallback
    }
  }, []);

  const teamId = team?.id?.value;
  return (
    <div style={{ position: 'relative', height: '100%' }}>
      {hydrated &&
        visibleDates.map(({ key, transform, isFriday, stringDate, date, startTime }) => {
          const teamIsAvailable = teamId
            ? (availabilityMap.get(stringDate)?.[startTime]?.[roleKey]?.[teamId] ?? 'unavailable')
            : 'available';
          return teamUnavailabilityMode ? (
            <AvailabilityHalfDay
              key={`${key}-${rowIndex}-${requiredRole}`}
              date={date}
              startTime={startTime}
              requiredRole={requiredRole}
              rowIndex={rowIndex}
              teamIsAvailable={teamIsAvailable}
              teamId={team?.id?.value}
              numberOfRows={numberOfRows ?? 1}
              workingHoursForDatesMap={workingHoursForDatesMap}
              transform={transform}
              disabled={!!team?.deletedAt}
              canEdit={canEdit}
              timeZone={timeZone}
              isDateFriday={isFriday}
              regionId={regionId}
              CELL_HEIGHT={CELL_HEIGHT}
              dayWidth={dayWidth}
              refetchTeamAvailability={refetchTeamAvailability}
              setToolTipProject={setToolTipProject}
            />
          ) : (
            <DroppableDate
              key={`${key}-${rowIndex}-${requiredRole}`}
              date={date}
              startTime={startTime}
              requiredRole={requiredRole}
              rowIndex={rowIndex}
              teamIsAvailable={teamIsAvailable}
              teamId={team?.id?.value}
              numberOfRows={numberOfRows ?? 1}
              workingHoursForDatesMap={workingHoursForDatesMap}
              transform={transform}
              disabled={!!team?.deletedAt}
              isDateFriday={isFriday}
              canEdit={canEdit}
              timeZone={timeZone}
              regionId={regionId}
              CELL_HEIGHT={CELL_HEIGHT}
              dayWidth={dayWidth}
              setDroppableStartTime={setDroppableStartTime}
            />
          );
        })}
    </div>
  );
}

export default RenderDatesForRow;
