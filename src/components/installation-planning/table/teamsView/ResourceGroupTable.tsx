import React from 'react';
import { Box, Typography, Stack, IconButton } from '@mui/material';
import { beige, grey } from '@ui/theme/colors';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { useDateRange } from 'components/installation-planning/contexts/DateRangeContext';
import {
  useDispatchMode,
  useFocusedProject,
  useHighlightFlexibleProjects,
  useHighlightProjectsWithinDistance,
  useHoveredUnsavedChangeJobId,
  useJobBeingDragged,
  useJobBeingSplit,
  useJobsToDispatch,
  useProjectActions,
  useShowCompactView,
  useSplitSegmentsToMove,
  useTeamUnavailabilityMode,
  useUnsavedChanges,
  useUnsavedChangesWithoutReason,
} from '../../stores/ProjectStore';
import {
  ResourceType,
  resourceMapping,
  reverseResourceMapping,
  ResourceBasedSegments,
  ContinuousWorkSegment,
  Row,
} from '../../types/planningTypes';
import useTableCellDims from '../../hooks/useTableCellDims';
import RenderJobsInExtraRow from './RenderJobsInExtraRow';
import RenderDatesForRow from './RenderDatesForRow';
import RenderRowForTeam from './RenderRowForTeam';
import { useCanEdit } from 'components/installation-planning/hooks/useCanEdit';
import { useRegionContext } from 'context/RegionContext';
import { useAvailability } from 'components/installation-planning/contexts/AvailabilityContext';
import { useProjectsUpdatedSince } from 'components/installation-planning/contexts/ProjectsUpdatedSinceContext';
import { api } from 'utils/api';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { useVirtualizedTimeline } from '../useVirtualizedTimeline';
export default function ResourceGroupTable({
  resourceType,
  resourceGrid,
  teamsRows,
  continuousSegmentsByResource,
  tableRef,
}: {
  resourceType: ResourceType;
  resourceGrid: ({ continuousWorkSegment: ContinuousWorkSegment; span: number } | null | 'taken')[][];
  teamsRows: {
    row: Row;
    numberOfRows: number;
    team: Team;
  }[];
  continuousSegmentsByResource?: ResourceBasedSegments;
  tableRef: React.RefObject<HTMLDivElement>;
}) {
  const canEdit = useCanEdit();
  const { timeZone, id: regionId } = useRegionContext();
  const { CELL_HEIGHT, dayWidth } = useTableCellDims();
  const { refetchTeamAvailability } = useAvailability();
  const isCompactView = useShowCompactView();
  const teamUnavailabilityMode = useTeamUnavailabilityMode();
  const { weekdayMetrics } = useDateRange();
  const unsavedChanges = useUnsavedChanges();
  const unsavedChangesWithoutReason = useUnsavedChangesWithoutReason();
  const {
    setShowEditTeamsModal,
    setToolTipProject,
    setOverlappingSegmentsForResources,
    setJobBeingDragged,
    setSelectedProject,
    setShowUnsavedChanges,
    updateUnsavedChanges,
    setJobBeingSplit,
    setRightClickMenu,
    setHoveredProject,
    setShowRightSidebar,
    setSplitSegmentsToMove,
    setJobsToDispatch,
    setDroppableStartTime,
  } = useProjectActions();
  const jobBeingDragged = useJobBeingDragged();
  const jobBeingSplit = useJobBeingSplit();
  const jobsToDispatch = useJobsToDispatch();
  const focusedProject = useFocusedProject();
  const splitSegmentsToMove = useSplitSegmentsToMove();
  const hoveredUnsavedChangeJobId = useHoveredUnsavedChangeJobId();
  const highlightProjectsWithinDistance = useHighlightProjectsWithinDistance();
  const highlightFlexibleProjects = useHighlightFlexibleProjects();
  const dispatchMode = useDispatchMode();
  const { data: user } = api.AiraBackend.whoAmI.useQuery();
  const { refetchProjectsUpdatedSince, initialLoadTime } = useProjectsUpdatedSince();
  const daysInInterval = weekdayMetrics.weekdaysInRange.length;
  const tableWidth = dayWidth * daysInInterval;
  const requiredRole: 3 | 1 | 2 = resourceMapping[resourceType] as 3 | 1 | 2;
  const halfDayWidth = dayWidth / 2;

  const rowNames = resourceGrid.map((_, i) => `row-${requiredRole}-${i}`);
  const roleKey = reverseResourceMapping[requiredRole as keyof typeof reverseResourceMapping];

  const { visibleDates } = useVirtualizedTimeline(tableRef, halfDayWidth, weekdayMetrics.halfDaysInRange);

  return (
    <Stack
      sx={{
        height: '100%',
        padding: 0,
        width: `${tableWidth}px`,
        zIndex: 1200,
      }}
    >
      <Stack
        direction="row"
        sx={{
          height: '100%',
          position: 'sticky',
          left: 0,
          top: 110,
          zIndex: 5202,
          background: beige[100],
          borderRight: '1px solid #D3D8D9',
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            height: `${CELL_HEIGHT + 12}px`,
            width: '191px',
            minWidth: '191px',
            padding: 0,
            pl: 2,
            position: 'sticky',
            left: 0,
            top: 117,
            zIndex: 5202,
            background: beige[100],
            borderBottom: '1px solid #D3D8D9',
            borderTop: '1px solid #D3D8D9',
            borderRight: '1px solid #D3D8D9',
          }}
        >
          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
            {resourceType.toLowerCase()} Teams
          </Typography>
          <IconButton
            onClick={() => {
              setShowEditTeamsModal(true);
            }}
          >
            <AddOutlinedIcon height={18} width={18} color={grey[800]} />
          </IconButton>
        </Stack>
        <Stack
          direction="row"
          sx={{
            height: `${CELL_HEIGHT + 12}px`,
            maxHeight: `${CELL_HEIGHT + 12}px`,
            padding: 0,
            width: `${tableWidth}px`,
            position: 'sticky',
            left: 0,
            top: 117,
            zIndex: 1200,
          }}
        >
          {weekdayMetrics.weekdaysInRange.map(({ date, isFriday }) => {
            return (
              <Stack
                key={date.toISOString() + resourceType}
                justifyContent="center"
                sx={{
                  width: `${dayWidth}px`,
                  maxWidth: `${dayWidth}px`,
                  padding: isCompactView ? '0px' : '10px',
                  background: '#fff',
                  height: `${CELL_HEIGHT + 12}px`,
                  maxHeight: `${CELL_HEIGHT + 12}px`,
                  borderRight: `${isFriday ? '2px' : '0px'} solid #D3D8D9`,
                  borderTop: '1px solid #D3D8D9',
                  borderBottom: '1px solid #D3D8D9',
                }}
              />
            );
          })}
        </Stack>
      </Stack>
      <Stack>
        {teamsRows.map((teamRow) => (
          <RenderRowForTeam
            key={teamRow.team.id!.value}
            teamRow={teamRow}
            roleKey={roleKey}
            requiredRole={requiredRole}
            continuousSegmentsByResource={continuousSegmentsByResource || new Map()}
            tableRef={tableRef}
            canEdit={canEdit}
            timeZone={timeZone}
            regionId={regionId!.value}
            CELL_HEIGHT={CELL_HEIGHT}
            dayWidth={dayWidth}
            teamUnavailabilityMode={teamUnavailabilityMode}
            setToolTipProject={setToolTipProject}
            refetchTeamAvailability={refetchTeamAvailability}
            setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
            unsavedChanges={unsavedChanges}
            unsavedChangesWithoutReason={unsavedChangesWithoutReason}
            setJobBeingDragged={setJobBeingDragged}
            setSelectedProject={setSelectedProject}
            setShowUnsavedChanges={setShowUnsavedChanges}
            updateUnsavedChanges={updateUnsavedChanges}
            dispatchMode={dispatchMode}
            user={user as UserIdentity}
            refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
            initialLoadTime={initialLoadTime}
            jobBeingDragged={jobBeingDragged}
            setJobBeingSplit={setJobBeingSplit}
            setRightClickMenu={setRightClickMenu}
            setHoveredProject={setHoveredProject}
            setShowRightSidebar={setShowRightSidebar}
            setSplitSegmentsToMove={setSplitSegmentsToMove}
            setJobsToDispatch={setJobsToDispatch}
            jobsToDispatch={jobsToDispatch}
            focusedProject={focusedProject}
            jobBeingSplit={jobBeingSplit}
            highlightProjectsWithinDistance={highlightProjectsWithinDistance}
            highlightFlexibleProjects={highlightFlexibleProjects}
            hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
            splitSegmentsToMove={splitSegmentsToMove}
            setDroppableStartTime={setDroppableStartTime}
            visibleDates={visibleDates}
            workingHoursForDatesMap={weekdayMetrics.workingHoursForDatesMap}
          />
        ))}

        {resourceGrid.map((row, rowIndex) => {
          if (!row) return null;
          return (
            <Stack
              direction="row"
              gap={0}
              key={rowNames[rowIndex]}
              sx={{
                width: `100%`,
                height: `${CELL_HEIGHT}px`,
                maxHeight: `${CELL_HEIGHT}px`,
                padding: 0,
                position: 'relative',
              }}
            >
              <Stack
                sx={{
                  height: '100%',
                  position: 'sticky',
                  left: 0,
                  top: 110,
                  zIndex: 1202,
                  background: beige[100],
                  borderRight: '1px solid #D3D8D9',
                  ...(rowIndex === resourceGrid.length - 1 && { borderBottom: '1px solid #D3D8D9' }),
                }}
              >
                <Box
                  sx={{ height: `${CELL_HEIGHT}px`, maxHeight: `${CELL_HEIGHT}px`, width: '190px', minWidth: '190px' }}
                />
              </Stack>
              <RenderDatesForRow
                roleKey={roleKey}
                requiredRole={requiredRole}
                rowIndex={teamsRows.length + rowIndex}
                tableRef={tableRef}
                canEdit={canEdit}
                timeZone={timeZone}
                regionId={regionId!.value}
                CELL_HEIGHT={CELL_HEIGHT}
                dayWidth={dayWidth}
                setToolTipProject={setToolTipProject}
                refetchTeamAvailability={refetchTeamAvailability}
                teamUnavailabilityMode={teamUnavailabilityMode}
                setDroppableStartTime={setDroppableStartTime}
                visibleDates={visibleDates}
                workingHoursForDatesMap={weekdayMetrics.workingHoursForDatesMap}
              />
              <RenderJobsInExtraRow
                requiredRole={requiredRole}
                continuousSegmentsByResource={continuousSegmentsByResource || new Map()}
                row={row}
                canEdit={canEdit}
                CELL_HEIGHT={CELL_HEIGHT}
                setToolTipProject={setToolTipProject}
                dayWidth={dayWidth}
                dispatchMode={dispatchMode}
                focusedProject={focusedProject}
                highlightFlexibleProjects={highlightFlexibleProjects}
                highlightProjectsWithinDistance={highlightProjectsWithinDistance}
                hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
                jobBeingDragged={jobBeingDragged}
                jobBeingSplit={jobBeingSplit}
                jobsToDispatch={jobsToDispatch}
                initialLoadTime={initialLoadTime}
                regionId={regionId!.value}
                setHoveredProject={setHoveredProject}
                setJobBeingDragged={setJobBeingDragged}
                setJobBeingSplit={setJobBeingSplit}
                setJobsToDispatch={setJobsToDispatch}
                setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
                setRightClickMenu={setRightClickMenu}
                setSelectedProject={setSelectedProject}
                setShowRightSidebar={setShowRightSidebar}
                setShowUnsavedChanges={setShowUnsavedChanges}
                setSplitSegmentsToMove={setSplitSegmentsToMove}
                splitSegmentsToMove={splitSegmentsToMove}
                timeZone={timeZone}
                refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
                unsavedChanges={unsavedChanges}
                unsavedChangesWithoutReason={unsavedChangesWithoutReason}
                updateUnsavedChanges={updateUnsavedChanges}
                user={user as UserIdentity}
              />
            </Stack>
          );
        })}
      </Stack>
    </Stack>
  );
}
