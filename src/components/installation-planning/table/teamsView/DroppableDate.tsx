import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { grey } from '@ui/theme/colors';
import { dropTargetForElements } from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { getCacheKey, WorkingHoursOnDate } from '../../helpers/workingHours';

// Constants
export const BORDER_COLOR = '#D3D8D9';
export const STRIPE_PATTERN = `repeating-linear-gradient(
  35deg,
  white,
  white 4px,
  ${grey[150]} 4px,
  ${grey[150]} 8px
)`;

// Memoized style calculations
const getBorderWidths = (startTime: '08' | '12', isDateFriday: boolean) => {
  if (startTime === '12') {
    return {
      rightBorderWidth: isDateFriday ? '2px' : '1px',
      leftBorderWidth: '0px',
    };
  }
  return {
    rightBorderWidth: isDateFriday ? '2px' : '1px',
    leftBorderWidth: '1px',
  };
};

interface DroppableDateProps {
  date: Date;
  startTime: '08' | '12';
  requiredRole: number;
  rowIndex: number;
  teamIsAvailable?: 'available' | 'partiallyAvailable' | 'unavailable' | 'resources unavailable';
  teamId?: string;
  numberOfRows?: number;
  workingHoursForDatesMap: Map<string, WorkingHoursOnDate>;
  transform: string;
  disabled: boolean;
  canEdit: boolean;
  timeZone: string;
  isDateFriday: boolean;
  regionId: string;
  CELL_HEIGHT: number;
  dayWidth: number;
  setDroppableStartTime: (date: Date | null) => void;
}

const DroppableDate = memo(function DroppableDate({
  date,
  startTime,
  requiredRole,
  rowIndex,
  teamIsAvailable,
  teamId,
  numberOfRows = 1,
  workingHoursForDatesMap,
  transform,
  disabled,
  canEdit,
  timeZone,
  isDateFriday,
  CELL_HEIGHT,
  dayWidth,
  setDroppableStartTime,
}: DroppableDateProps) {
  const dateRef = useRef(null);
  const [isOver, setIsOver] = useState(false);

  const isEditable = canEdit && !disabled;

  const borderWidths = useMemo(() => getBorderWidths(startTime, isDateFriday), [startTime, isDateFriday]);

  const workingHours = useMemo(() => {
    const { shifts } = workingHoursForDatesMap.get(getCacheKey(date, timeZone))!;
    return shifts.find(({ shiftHours }) => shiftHours.startHour === Number(startTime))!;
  }, [date, timeZone, startTime, workingHoursForDatesMap]);

  const id = useMemo(
    () => `${workingHours.start.toISOString()}|${requiredRole}|${rowIndex}|${teamId ?? ''}`,
    [workingHours.start, requiredRole, rowIndex, teamId],
  );

  useEffect(() => {
    if (!isEditable || !dateRef.current) return undefined;

    return dropTargetForElements({
      element: dateRef.current,
      getData: () => ({ id }),
      onDragEnter: () => {
        setIsOver(true);
        setDroppableStartTime(date);
      },
      onDragLeave: () => {
        setIsOver(false);
        setDroppableStartTime(null);
      },
      onDrop: () => setIsOver(false),
    });
  }, [date, id, setDroppableStartTime, isEditable]);

  const backgroundColor = useMemo(
    () => ({
      background: isOver && teamIsAvailable ? '#D3D8D9' : '#fff',
    }),
    [teamIsAvailable, isOver],
  );

  return (
    <div
      ref={dateRef}
      style={{
        position: 'absolute',
        top: 0,
        transform,
        borderRight: `${borderWidths.rightBorderWidth} solid ${BORDER_COLOR}`,
        borderBottom: `1px solid ${BORDER_COLOR}`,
        borderLeft: `${borderWidths.leftBorderWidth} solid ${BORDER_COLOR}`,
        height: `${CELL_HEIGHT * numberOfRows}px`,
        maxHeight: `${CELL_HEIGHT * numberOfRows}px`,
        minWidth: `${dayWidth / 2}px`,
        width: `${dayWidth / 2}px`,
        padding: '1px 3px',
        backgroundColor: backgroundColor.background,
        willChange: 'transform',
        backfaceVisibility: 'hidden',
      }}
    >
      {teamId && teamIsAvailable !== 'available' && (
        <div
          style={{
            position: 'absolute',
            top: '2px',
            left: '2px',
            height: 'calc(100% - 4px)',
            width: 'calc(100% - 4px)',
            background: teamIsAvailable === 'partiallyAvailable' ? STRIPE_PATTERN : grey[150],
          }}
        />
      )}
    </div>
  );
});

export default DroppableDate;
