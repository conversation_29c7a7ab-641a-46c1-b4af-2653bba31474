import React, { useMemo } from 'react';
import { useAvailability } from 'components/installation-planning/contexts/AvailabilityContext';
import { ResourceType, resourceMapping, RoleBasedSegments, ResourceBasedSegments } from '../../types/planningTypes';
import { useCalculateRowsForTeams } from '../../useCalculateRowsForTeams';
import ResourceGroupTable from './ResourceGroupTable';
import { getTeamsForRequiredRole } from '../../helpers/teamsHelpers';
import useCreateGridForResource from '../../useCreateGridForResource';
/**
 * Container component that manages the data processing and state for a resource group table.
 * Handles team organization, resource allocation, and continuous work segment calculations.
 */
export default function ResourceGroupTableContainer({
  resourceType,
  continuousSegmentsByRole,
  continuousSegmentsByResource,
  tableRef,
}: {
  resourceType: ResourceType;
  continuousSegmentsByRole: RoleBasedSegments;
  continuousSegmentsByResource?: ResourceBasedSegments;
  tableRef: React.RefObject<HTMLDivElement>;
}) {
  const { teams } = useAvailability();

  // Map role IDs to their corresponding resource types (e.g., 3 = installer, 1 = surveyor)
  const requiredRole: 3 | 1 | 2 = resourceMapping[resourceType] as 3 | 1 | 2;

  const { continuousWorkSegmentsWithTeam, continuousWorkSegmentsWithoutTeam } = continuousSegmentsByRole.get(
    requiredRole,
  ) ?? {
    continuousWorkSegmentsWithTeam: new Map(),
    continuousWorkSegmentsWithoutTeam: [],
  };

  // Create the rows for the jobs without a team
  const resourceGrid = useCreateGridForResource({
    continuousWorkSegmentsWithoutTeam,
  });

  // Sort teams naturally by name, handling numeric parts separately for proper ordering
  const teamsForResource = useMemo(() => getTeamsForRequiredRole(teams, requiredRole), [teams, requiredRole]);

  const teamsRows = useCalculateRowsForTeams({
    continuousWorkSegmentsWithTeam,
    teamsForResource,
  });

  return (
    <ResourceGroupTable
      resourceGrid={resourceGrid}
      teamsRows={teamsRows}
      resourceType={resourceType}
      continuousSegmentsByResource={continuousSegmentsByResource}
      tableRef={tableRef}
    />
  );
}
