/* eslint-disable @next/next/no-img-element */
import { Box, Divider, IconButton, Portal, Stack, Typography } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { beige, grey } from '@ui/theme/colors';
import { AddOutlinedIcon } from '@ui/components/StandardIcons/AddOutlinedIcon';
import { CheckIndeterminateOutlinedIcon } from '@ui/components/StandardIcons/CheckIndeterminateOutlinedIcon';
import { CrossOutlinedIcon } from '@ui/components/StandardIcons/CrossOutlinedIcon';
import { FormattedMessage } from 'react-intl';
import { ResourceForRegion } from 'components/installation-planning/types/planningTypes';
import { useAvailability } from 'components/installation-planning/contexts/AvailabilityContext';
import { api } from 'utils/api';
import { useResources } from 'components/installation-planning/contexts/ResourcesContext';

const ARROW_SIZE = 8; // Size of the arrow in pixels

export default function AddResourcesToTeam({
  setShowAddResourcesToTeam,
  teamId,
  teamName,
  position,
  requiredRole,
}: {
  setShowAddResourcesToTeam: (show: boolean) => void;
  teamId: string;
  teamName: string;
  position: {
    top: number;
    left: number;
    parentTop: number;
  };
  requiredRole: number;
}) {
  const resourcesForRegion = useResources();
  const containerRef = useRef<HTMLDivElement>(null);
  const [resourcesInTeam, setResourcesInTeam] = useState<string[]>([]);
  const { teams, refetchTeamAvailability } = useAvailability();
  const { mutateAsync: updateTeam } = api.Resource.updateTeam.useMutation();

  useEffect(() => {
    const defaultTeamResources =
      teams.find((t) => t.id?.value === teamId)?.defaultResources.map((id) => id.value) ?? [];
    if (defaultTeamResources) {
      setResourcesInTeam(defaultTeamResources);
    }
  }, [teamId, teams]);

  const handleAddResource = async (resourceId: string) => {
    const newResourcesInTeam = [...resourcesInTeam, resourceId];
    await updateTeam({
      teamId,
      defaultResources: newResourcesInTeam,
    });
    setResourcesInTeam(newResourcesInTeam);
    refetchTeamAvailability();
  };

  const handleRemoveResource = async (resourceId: string) => {
    const newResourcesInTeam = resourcesInTeam.filter((id) => id !== resourceId);
    await updateTeam({
      teamId,
      defaultResources: newResourcesInTeam,
    });
    setResourcesInTeam(newResourcesInTeam);
    refetchTeamAvailability();
  };

  const allResourcesInTeam: ResourceForRegion[] =
    (resourcesInTeam ?? [])
      .map((resource) => {
        if (!resource) return null;
        const resourceData = resourcesForRegion.get(resource);
        return resourceData ? { ...resourceData, roles: resourceData.roles ?? [] } : null;
      })
      .filter((resource) => resource !== null) || [];

  const allResourcesNotInTeam = Array.from(resourcesForRegion.values())
    .filter(
      (resource) =>
        !resourcesInTeam.includes(resource.userId!.value) &&
        resource.roles.some((role) => {
          return requiredRole === role;
        }),
    )
    .toSorted((a, b) => a.firstName.localeCompare(b.firstName));

  const handleClickOutside = (e: MouseEvent) => {
    if (document.getElementsByClassName('MuiModal-root').length > 0) return;
    if (containerRef.current !== null && !containerRef.current.contains(e.target as Node)) {
      setShowAddResourcesToTeam(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  });

  return (
    <Portal>
      <Stack
        id="add-resources-to-team-modal"
        ref={containerRef}
        position="absolute"
        width="360px"
        height="auto"
        maxHeight="70vh"
        sx={{
          background: 'white',
          boxShadow: '0px 24px 38px 0px rgba(0, 0, 0, 0.25)',
          zIndex: 5202,
          borderRadius: '22px',
          top: position.top,
          left: position.left,

          '&::after': {
            content: '""',
            position: 'absolute',
            left: `-${ARROW_SIZE}px`,
            top: `calc(${position.parentTop - position.top}px + 10px)`,
            width: 0,
            height: 0,
            borderTop: `${ARROW_SIZE}px solid transparent`,
            borderBottom: `${ARROW_SIZE}px solid transparent`,
            borderRight: `${ARROW_SIZE}px solid #fff`,
            filter: 'drop-shadow(-2px 2px 2px rgba(0, 0, 0, 0.15))',
          },
        }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center" px={4} pt={4} pb={2}>
          <Typography variant="headline4">{`${teamName}`}</Typography>
          <IconButton onClick={() => setShowAddResourcesToTeam(false)}>
            <CrossOutlinedIcon height={18} width={18} />
          </IconButton>
        </Stack>
        <Stack>
          <Typography variant="body2Emphasis" mb={1} px={4}>
            <FormattedMessage
              id="installationPlanning.addResourcesToTeam.defaultResources"
              defaultMessage="Default Resources"
            />
          </Typography>
          {allResourcesInTeam.length === 0 ? (
            <Stack py={2} px={4} justifyContent="center" width="100%" height="160px" minHeight="160px">
              <Typography variant="body2" color={grey[600]} mb={2} textAlign="center">
                <FormattedMessage
                  id="installationPlanning.addResourcesToTeam.noResources"
                  defaultMessage="No resources assigned to this team"
                />
              </Typography>
            </Stack>
          ) : (
            <Stack
              sx={{
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                minHeight: '160px',
                maxHeight: '160px',
                msOverflowStyle: 'none', // IE and Edge
                scrollbarWidth: 'none', // Firefox
              }}
              py={2}
              px={4}
              pb={2}
              mb={2}
            >
              {allResourcesInTeam?.map((resource) => (
                <Stack
                  direction="row"
                  key={resource.userId!.value}
                  justifyContent="space-between"
                  alignItems="center"
                  minHeight="36px"
                >
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      {resource.userImage ? (
                        <Box
                          sx={{
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            backgroundColor: grey[100],
                          }}
                        >
                          <img
                            src={`data:image/jpeg;base64,${Buffer.from(resource.userImage).toString('base64')}`}
                            alt={`${resource.firstName} ${resource.lastName}`}
                            style={{
                              width: '20px',
                              height: '20px',
                              borderRadius: '50%',
                            }}
                          />
                        </Box>
                      ) : (
                        <Box
                          sx={{
                            width: '20px',
                            height: '20px',
                            backgroundColor: beige[100],
                            borderRadius: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <Typography variant="body2" fontSize={8} sx={{ textTransform: 'uppercase' }}>
                            {resource.firstName[0]} {resource.lastName[0]}
                          </Typography>
                        </Box>
                      )}

                      <Typography variant="body2" fontSize={12}>
                        {resource.firstName} {resource.lastName}
                      </Typography>
                    </Stack>
                  </Stack>
                  <IconButton onClick={() => handleRemoveResource(resource.userId!.value)}>
                    <CheckIndeterminateOutlinedIcon height={20} width={20} />
                  </IconButton>
                </Stack>
              ))}
              <Box
                sx={{
                  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FFF 68.52%)',
                  height: '54px',
                  width: '100%',
                  zIndex: 2000,
                  position: 'absolute',
                  borderRadius: '0 0 22px 22px',
                  top: 240,
                  left: 0,
                  pointerEvents: 'none',
                }}
              />
            </Stack>
          )}

          <Divider sx={{ my: 2, mx: 4 }} />
          <Typography variant="body2Emphasis" mt={3} mb={2} px={4}>
            <FormattedMessage
              id="installationPlanning.addResourcesToTeam.availableResources"
              defaultMessage="Available Resources"
            />
          </Typography>
          {allResourcesNotInTeam.length === 0 ? (
            <Stack py={2} px={4} justifyContent="center" width="100%" height="160px" minHeight="160px">
              <Typography variant="body2" color={grey[600]} mb={2} textAlign="center">
                <FormattedMessage
                  id="installationPlanning.addResourcesToTeam.noAvailableResources"
                  defaultMessage="No available resources to add"
                />
              </Typography>
            </Stack>
          ) : (
            <Stack
              sx={{
                overflowY: 'auto',
                minHeight: '160px',
                maxHeight: '160px',
                '&::-webkit-scrollbar': {
                  display: 'none',
                },
                msOverflowStyle: 'none', // IE and Edge
                scrollbarWidth: 'none', // Firefox
              }}
              px={4}
              pt={1}
              pb={2}
              mb={2}
            >
              {allResourcesNotInTeam.map((resource) => (
                <Stack
                  direction="row"
                  key={resource.userId!.value}
                  justifyContent="space-between"
                  alignItems="center"
                  minHeight="36px"
                >
                  <Stack direction="row" alignItems="center" spacing={2}>
                    {resource.userImage ? (
                      <Box
                        sx={{
                          borderRadius: '50%',
                          width: '20px',
                          height: '20px',
                          backgroundColor: grey[100],
                        }}
                      >
                        <img
                          src={`data:image/jpeg;base64,${Buffer.from(resource.userImage).toString('base64')}`}
                          alt={`${resource.firstName} ${resource.lastName}`}
                          style={{
                            width: '20px',
                            height: '20px',
                            borderRadius: '50%',
                          }}
                        />
                      </Box>
                    ) : (
                      <Box
                        sx={{
                          width: '20px',
                          height: '20px',
                          backgroundColor: beige[100],
                          borderRadius: '50%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                      >
                        <Typography variant="body2" fontSize={8} sx={{ textTransform: 'uppercase' }}>
                          {resource.firstName[0]} {resource.lastName[0]}
                        </Typography>
                      </Box>
                    )}
                    <Typography variant="body2" fontSize={12}>
                      {resource.firstName} {resource.lastName}
                    </Typography>
                  </Stack>
                  <IconButton onClick={() => handleAddResource(resource.userId!.value)}>
                    <AddOutlinedIcon height={20} width={20} />
                  </IconButton>
                </Stack>
              ))}
              <Box
                sx={{
                  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.00) 0%, #FFF 68.52%)',
                  height: '54px',
                  width: '100%',
                  zIndex: 2000,
                  position: 'absolute',
                  borderRadius: '0 0 22px 22px',
                  bottom: 0,
                  left: 0,
                  pointerEvents: 'none',
                }}
              />
            </Stack>
          )}
        </Stack>
      </Stack>
    </Portal>
  );
}
