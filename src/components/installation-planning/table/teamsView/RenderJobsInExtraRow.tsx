import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/job';
import React from 'react';
import DraggableJobContainer from '../../jobBar/DraggableJobContainer';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  ResourceBasedSegments,
  UnsavedChange,
} from '../../types/planningTypes';
import { useSelectedProject } from 'components/installation-planning/stores/ProjectStore';
const RenderJobsInExtraRow = React.memo(function RenderJobsInExtraRow({
  CELL_HEIGHT,
  canEdit,
  continuousSegmentsByResource,
  dayWidth,
  dispatchMode,
  focusedProject,
  highlightFlexibleProjects,
  highlightProjectsWithinDistance,
  hoveredUnsavedChangeJobId,
  jobBeingDragged,
  jobBeingSplit,
  jobsToDispatch,
  initialLoadTime,
  regionId,
  requiredRole,
  row,
  setHoveredProject,
  setJobBeingDragged,
  setJobBeingSplit,
  setJobsToDispatch,
  setOverlappingSegmentsForResources,
  setRightClickMenu,
  setSelectedProject,
  setShowRightSidebar,
  setShowUnsavedChanges,
  setSplitSegmentsToMove,
  setToolTipProject,
  splitSegmentsToMove,
  timeZone,
  refetchProjectsUpdatedSince,
  unsavedChanges,
  unsavedChangesWithoutReason,
  updateUnsavedChanges,
  user,
}: {
  CELL_HEIGHT: number;
  canEdit: boolean;
  continuousSegmentsByResource: ResourceBasedSegments;
  dayWidth: number;
  dispatchMode: boolean;
  focusedProject?: FullInstallationProjectEntity;
  highlightFlexibleProjects: boolean;
  highlightProjectsWithinDistance?: number;
  hoveredUnsavedChangeJobId: null | string;
  jobBeingDragged?: ContinuousWorkSegment;
  jobBeingSplit?: ContinuousWorkSegment;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  initialLoadTime: Date;
  regionId: string;
  requiredRole: number;
  row: (null | 'taken' | { continuousWorkSegment: ContinuousWorkSegment; span: number })[];
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
  timeZone: string;
  unsavedChanges: UnsavedChange[];
  unsavedChangesWithoutReason: UnsavedChange[];
  user: UserIdentity;
  refetchProjectsUpdatedSince: () => void;
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setJobBeingDragged: (job?: ContinuousWorkSegment) => void;
  setJobBeingSplit: (job?: ContinuousWorkSegment) => void;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
  setOverlappingSegmentsForResources: (
    overlappingSegmentsForResources: Map<
      string,
      {
        overlappingSegments: {
          segments: {
            customerName: string;
            jobId: string;
            segment: InstallationProjectJob_WorkSegment;
          }[];
        };
      }[]
    >,
  ) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      hasAssignedResources: boolean;
      jobId: string;
      position: {
        up: boolean;
        x: number;
        y: number;
      };
      project: ContinuousWorkSegmentForJobItem;
    },
  ) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setShowRightSidebar: (show: boolean) => void;
  setShowUnsavedChanges: (show: boolean) => void;
  setSplitSegmentsToMove: (
    segments: null | {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    },
  ) => void;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    position: {
      arrowTop: number;
      right: number;
      top: number;
    };
    resourceId?: string;
  }) => void;
  updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;
}): React.JSX.Element[] {
  const selectedProject = useSelectedProject();
  const result: React.JSX.Element[] = [];
  row.forEach((columnData, i) => {
    if (columnData && columnData !== 'taken') {
      result.push(
        <DraggableJobContainer
          CELL_HEIGHT={CELL_HEIGHT}
          canEdit={canEdit}
          continuousSegmentsByResource={continuousSegmentsByResource}
          continuousWorkSegment={columnData.continuousWorkSegment}
          dayWidth={dayWidth}
          dispatchMode={dispatchMode}
          focusedProject={focusedProject}
          highlightFlexibleProjects={highlightFlexibleProjects}
          highlightProjectsWithinDistance={highlightProjectsWithinDistance}
          hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
          innerRowNumber={1}
          jobBeingDragged={jobBeingDragged}
          jobBeingSplit={jobBeingSplit}
          jobsToDispatch={jobsToDispatch}
          key={`${columnData.continuousWorkSegment.segmentDetails.startTime.toISOString()}-${columnData.continuousWorkSegment.segmentDetails.jobId}-${requiredRole}`}
          initialLoadTime={initialLoadTime}
          refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
          regionId={regionId}
          selectedProject={selectedProject}
          setHoveredProject={setHoveredProject}
          setJobBeingDragged={setJobBeingDragged}
          setJobBeingSplit={setJobBeingSplit}
          setJobsToDispatch={setJobsToDispatch}
          setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
          setRightClickMenu={setRightClickMenu}
          setSelectedProject={setSelectedProject}
          setShowRightSidebar={setShowRightSidebar}
          setShowUnsavedChanges={setShowUnsavedChanges}
          setSplitSegmentsToMove={setSplitSegmentsToMove}
          setToolTipProject={setToolTipProject}
          span={columnData.span}
          splitSegmentsToMove={splitSegmentsToMove}
          startPositon={i}
          timeZone={timeZone}
          unsavedChanges={unsavedChanges}
          unsavedChangesWithoutReason={unsavedChangesWithoutReason}
          updateUnsavedChanges={updateUnsavedChanges}
          user={user}
        />,
      );
    }
  });
  return result;
});

export default RenderJobsInExtraRow;
