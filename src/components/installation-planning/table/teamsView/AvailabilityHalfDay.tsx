import { memo, useCallback, useMemo } from 'react';
import { grey } from '@ui/theme/colors';
import { api } from 'utils/api';
import toast from 'react-hot-toast';
import { useTeamUnavailabilityMode } from '../../stores/ProjectStore';
import { WorkingHoursOnDate } from '../../helpers/workingHours';
import { DateRange } from '../../topBar/DateRangePicker';
import { useDateRange } from '../../contexts/DateRangeContext';
import { ContinuousWorkSegment } from 'components/installation-planning/types/planningTypes';
import { HalfDayAvailabilityType } from 'components/installation-planning/contexts/AvailabilityContext';
import { BORDER_COLOR, STRIPE_PATTERN } from './DroppableDate';

// Memoized style calculations
const getBorderWidths = (startTime: '08' | '12', isDateFriday: boolean) => {
  if (startTime === '12') {
    return {
      rightBorderWidth: isDateFriday ? '2px' : '1px',
      leftBorderWidth: '0px',
    };
  }
  return {
    rightBorderWidth: isDateFriday ? '2px' : '1px',
    leftBorderWidth: '1px',
  };
};

// Custom hook for team availability mutations
const useTeamAvailability = ({
  teamId,
  date,
  timeZone,
  dateRange,
  regionId,
}: {
  teamId: string | undefined;
  date: Date;
  timeZone: string;
  dateRange?: DateRange;
  regionId: string | undefined;
}) => {
  const utils = api.useUtils();
  const { mutate: deleteTeamUnavailability } = api.Resource.deleteTeamUnavailability.useMutation();
  const { mutate: addTeamUnavailability } = api.Resource.addTeamUnavailability.useMutation();

  const updateAvailabilityCache = useCallback(
    (isAdding: boolean) => {
      if (!dateRange || !regionId) return;

      utils.Resource.getTeamAvailability.setData(
        {
          regionId,
          startDate: dateRange.startDate,
          endDate: dateRange.endDateInclusive,
          timeZone,
        },
        (oldData) => {
          if (!oldData || !teamId) return { teams: [], availability: [] };

          const availability = oldData.availability.map(({ date: oldDate, availableTeamIds }) => {
            if (oldDate === date) {
              return {
                date: oldDate,
                availableTeamIds: isAdding
                  ? [...availableTeamIds, { value: teamId }]
                  : availableTeamIds.filter((id) => id.value !== teamId),
              };
            }
            return { date: oldDate, availableTeamIds };
          });
          return { teams: oldData.teams, availability };
        },
      );
    },
    [date, dateRange, regionId, teamId, timeZone, utils],
  );

  return {
    handleRemoveUnavailability: useCallback(
      (refetchTeamAvailability?: () => void) => {
        if (!teamId) return;

        deleteTeamUnavailability(
          { teamId, days: [date], timeZone },
          {
            onSuccess: () => {
              updateAvailabilityCache(true);
              refetchTeamAvailability?.();
              toast.success('Team availability added successfully');
            },
            onError: (error: any) => toast.error(error.message),
          },
        );
      },
      [teamId, deleteTeamUnavailability, date, timeZone, updateAvailabilityCache],
    ),

    handleAddUnavailability: useCallback(
      (refetchTeamAvailability?: () => void) => {
        if (!teamId) return;

        addTeamUnavailability(
          { teamId, days: [date], timeZone },
          {
            onSuccess: () => {
              updateAvailabilityCache(false);
              refetchTeamAvailability?.();
              toast.success('Team availability removed successfully');
            },
            onError: (error: any) => toast.error(error.message),
          },
        );
      },
      [teamId, addTeamUnavailability, date, timeZone, updateAvailabilityCache],
    ),
  };
};

interface AvailabilityHalfDayProps {
  date: Date;
  startTime: '08' | '12';
  requiredRole: number;
  rowIndex: number;
  teamIsAvailable: HalfDayAvailabilityType;
  teamId?: string;
  numberOfRows?: number;
  workingHoursForDatesMap: Map<string, WorkingHoursOnDate>;
  transform: string;
  disabled: boolean;
  canEdit: boolean;
  timeZone: string;
  isDateFriday: boolean;
  regionId: string;
  CELL_HEIGHT: number;
  dayWidth: number;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    resourceId?: string;
    position: {
      top: number;
      right: number;
      arrowTop: number;
    };
  }) => void;
  refetchTeamAvailability: () => void;
}

const AvailabilityHalfDay = memo(function AvailabilityHalfDay({
  date,
  startTime,
  teamIsAvailable,
  teamId,
  numberOfRows = 1,
  transform,
  disabled,
  canEdit,
  timeZone,
  isDateFriday,
  regionId,
  CELL_HEIGHT,
  dayWidth,
  refetchTeamAvailability,
}: AvailabilityHalfDayProps) {
  const teamUnavailabilityMode = useTeamUnavailabilityMode();

  const { dateRange } = useDateRange();

  const isEditable = canEdit && !disabled;

  const borderWidths = useMemo(() => getBorderWidths(startTime, isDateFriday), [startTime, isDateFriday]);

  const { handleAddUnavailability, handleRemoveUnavailability } = useTeamAvailability({
    teamId,
    date,
    timeZone,
    dateRange,
    regionId,
  });

  const handleClick = useCallback(() => {
    if (!isEditable) {
      return;
    }
    if (teamIsAvailable === 'partiallyAvailable') {
      toast.error(
        'A default team member is unavailable. Please go into the resource view if you need to change this. ',
      );
      return;
    }
    if (teamIsAvailable === 'resources unavailable') {
      toast.error('All team members are unavailable. Please go into the resource view if you need to change this. ');
      return;
    }
    if (teamIsAvailable === 'available' && teamId) {
      handleAddUnavailability(refetchTeamAvailability);
    } else if (teamIsAvailable === 'unavailable' && teamId) {
      handleRemoveUnavailability(refetchTeamAvailability);
    }
  }, [
    teamIsAvailable,
    teamId,
    handleAddUnavailability,
    handleRemoveUnavailability,
    refetchTeamAvailability,
    isEditable,
  ]);

  return (
    <div
      onClick={isEditable && teamId && teamUnavailabilityMode ? handleClick : undefined}
      style={{
        position: 'absolute',
        top: 0,
        transform,
        borderRight: `${borderWidths.rightBorderWidth} solid ${BORDER_COLOR}`,
        borderBottom: `1px solid ${BORDER_COLOR}`,
        borderLeft: `${borderWidths.leftBorderWidth} solid ${BORDER_COLOR}`,
        height: `${CELL_HEIGHT * numberOfRows}px`,
        maxHeight: `${CELL_HEIGHT * numberOfRows}px`,
        minWidth: `${dayWidth / 2}px`,
        width: `${dayWidth / 2}px`,
        padding: '1px 3px',
        backgroundColor: '#fff',
        cursor: canEdit && teamId ? 'pointer' : 'default',
        willChange: 'transform',
        backfaceVisibility: 'hidden',
      }}
    >
      {teamId && teamIsAvailable !== 'available' && (
        <div
          style={{
            position: 'absolute',
            top: '2px',
            left: '2px',
            height: 'calc(100% - 4px)',
            width: 'calc(100% - 4px)',
            background: teamIsAvailable === 'partiallyAvailable' ? STRIPE_PATTERN : grey[150],
          }}
        />
      )}
    </div>
  );
});

export default AvailabilityHalfDay;
