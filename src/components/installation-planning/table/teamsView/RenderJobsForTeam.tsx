import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import DraggableJobContainer from '../../jobBar/DraggableJobContainer';
import {
  ContinuousWorkSegment,
  ContinuousWorkSegmentForJobItem,
  ResourceBasedSegments,
  Row,
  UnsavedChange,
} from '../../types/planningTypes';
import { InstallationProjectJob_WorkSegment } from '@aira/installation-project-grpc-api/build/ts_out/com/aira/acquisition/contract/installation/project/v1/job';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/index.com.aira.acquisition.contract.identity.v2';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';

export default function RenderJobsForTeam({
  row,
  team,
  requiredRole,
  continuousSegmentsByResource,
  setOverlappingSegmentsForResources,
  unsavedChanges,
  unsavedChangesWithoutReason,
  timeZone,
  regionId,
  user,
  canEdit,
  refetchProjectsUpdatedSince,
  initialLoadTime,
  setJobBeingDragged,
  setSelectedProject,
  setShowUnsavedChanges,
  updateUnsavedChanges,
  dispatchMode,
  jobBeingDragged,
  setJobBeingSplit,
  CELL_HEIGHT,
  setToolTipProject,
  setRightClickMenu,
  setHoveredProject,
  setShowRightSidebar,
  setSplitSegmentsToMove,
  setJobsToDispatch,
  jobsToDispatch,
  dayWidth,
  focusedProject,
  selectedProject,
  jobBeingSplit,
  highlightProjectsWithinDistance,
  highlightFlexibleProjects,
  hoveredUnsavedChangeJobId,
  splitSegmentsToMove,
}: {
  row: Row;
  team: Team;
  requiredRole: number;
  continuousSegmentsByResource: ResourceBasedSegments;
  setOverlappingSegmentsForResources: (
    overlappingSegmentsForResources: Map<
      string,
      {
        overlappingSegments: {
          segments: {
            segment: InstallationProjectJob_WorkSegment;
            jobId: string;
            customerName: string;
          }[];
        };
      }[]
    >,
  ) => void;
  unsavedChanges: UnsavedChange[];
  unsavedChangesWithoutReason: UnsavedChange[];
  timeZone: string;
  regionId: string;
  user: UserIdentity;
  canEdit: boolean;
  refetchProjectsUpdatedSince: () => void;
  initialLoadTime: Date;
  setJobBeingDragged: (job?: ContinuousWorkSegment) => void;
  setSelectedProject: (project?: FullInstallationProjectEntity) => void;
  setShowUnsavedChanges: (show: boolean) => void;
  updateUnsavedChanges: (unsavedChange: UnsavedChange) => void;
  dispatchMode: boolean;
  jobBeingDragged?: ContinuousWorkSegment;
  setJobBeingSplit: (job?: ContinuousWorkSegment) => void;
  CELL_HEIGHT: number;
  setToolTipProject: (toolTipProject?: {
    segmentDetails: ContinuousWorkSegment['segmentDetails'];
    resourceId?: string;
    position: {
      top: number;
      right: number;
      arrowTop: number;
    };
  }) => void;
  setRightClickMenu: (
    rightClickMenu: null | {
      jobId: string;
      position: {
        x: number;
        y: number;
        up: boolean;
      };
      project: ContinuousWorkSegmentForJobItem;
      hasAssignedResources: boolean;
    },
  ) => void;
  setHoveredProject: (project?: FullInstallationProjectEntity) => void;
  setShowRightSidebar: (show: boolean) => void;
  setSplitSegmentsToMove: (
    segments: null | {
      segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
      segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
    },
  ) => void;
  setJobsToDispatch: (jobsToDispatch: Record<string, FullInstallationProjectEntity>) => void;
  jobsToDispatch: Record<string, FullInstallationProjectEntity>;
  dayWidth: number;
  focusedProject?: FullInstallationProjectEntity;
  selectedProject?: FullInstallationProjectEntity;
  jobBeingSplit?: ContinuousWorkSegment;
  highlightProjectsWithinDistance?: number;
  highlightFlexibleProjects: boolean;
  hoveredUnsavedChangeJobId: null | string;
  splitSegmentsToMove: null | {
    segmentsAfterSplit: InstallationProjectJob_WorkSegment[];
    segmentsBeforeSplit: InstallationProjectJob_WorkSegment[];
  };
}) {
  const result: React.JSX.Element[] = [];
  row.forEach((cell, i) => {
    if (cell && cell.availability !== 'available' && !!cell.jobs) {
      cell.jobs.forEach((job) => {
        result.push(
          <DraggableJobContainer
            continuousWorkSegment={job.continuousWorkSegment}
            span={job.span}
            startPositon={i}
            key={`${job.continuousWorkSegment.segmentDetails.startTime}-${job.continuousWorkSegment.segmentDetails.jobId}-${requiredRole}-${team.id?.value}`}
            innerRowNumber={job.innerRowNumber}
            continuousSegmentsByResource={continuousSegmentsByResource}
            setOverlappingSegmentsForResources={setOverlappingSegmentsForResources}
            unsavedChanges={unsavedChanges}
            unsavedChangesWithoutReason={unsavedChangesWithoutReason}
            timeZone={timeZone}
            regionId={regionId}
            user={user}
            canEdit={canEdit}
            refetchProjectsUpdatedSince={refetchProjectsUpdatedSince}
            initialLoadTime={initialLoadTime}
            setJobBeingDragged={setJobBeingDragged}
            setSelectedProject={setSelectedProject}
            setShowUnsavedChanges={setShowUnsavedChanges}
            updateUnsavedChanges={updateUnsavedChanges}
            dispatchMode={dispatchMode}
            jobBeingDragged={jobBeingDragged}
            setJobBeingSplit={setJobBeingSplit}
            CELL_HEIGHT={CELL_HEIGHT}
            setToolTipProject={setToolTipProject}
            setRightClickMenu={setRightClickMenu}
            setHoveredProject={setHoveredProject}
            setShowRightSidebar={setShowRightSidebar}
            setSplitSegmentsToMove={setSplitSegmentsToMove}
            setJobsToDispatch={setJobsToDispatch}
            jobsToDispatch={jobsToDispatch}
            dayWidth={dayWidth}
            focusedProject={focusedProject}
            selectedProject={selectedProject}
            jobBeingSplit={jobBeingSplit}
            highlightProjectsWithinDistance={highlightProjectsWithinDistance}
            highlightFlexibleProjects={highlightFlexibleProjects}
            hoveredUnsavedChangeJobId={hoveredUnsavedChangeJobId}
            splitSegmentsToMove={splitSegmentsToMove}
          />,
        );
      });
    }
  });
  return result;
}
