import { Stack, Typography, Tooltip } from '@mui/material';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { FormattedMessage } from 'react-intl';
import { Row } from 'components/installation-planning/types/planningTypes';
import { useRef, useState } from 'react';
import { useCanEdit } from 'components/installation-planning/hooks/useCanEdit';
import useTableCellDims from '../../hooks/useTableCellDims';
import AddResourcesToTeam from './AddResourcesToTeam';

const TEAM_NAME_WIDTH = 190;

export default function RenderTeamNames({
  row,
  requiredRole,
}: {
  row: {
    row: Row;
    numberOfRows: number;
    team: Team;
  };
  requiredRole: number;
}) {
  const { CELL_HEIGHT } = useTableCellDims();
  const [showAddResourcesToTeam, setShowAddResourcesToTeam] = useState(false);
  const teamNameRef = useRef<HTMLDivElement>(null);
  const canEdit = useCanEdit() && !row.team.deletedAt;

  const getPositionForAddResourcesToTeam = () => {
    if (!teamNameRef.current) return { top: 0, left: 200, parentTop: 0 };

    const position = teamNameRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const modalHeight = 681;
    const modalOffset = 10;

    // Calculate vertical position to maintain overlap with team name
    const verticalPosition = Math.min(
      Math.max(
        // Ensure modal stays within viewport top
        10,
        // Position modal to overlap with team name
        position.top - modalHeight / 2 + position.height / 2,
      ),
      // Don't go lower than viewport bottom - modal height - padding
      viewportHeight - modalHeight - 10,
    );

    // Position modal a fixed distance from team name
    const horizontalPosition = position.left + TEAM_NAME_WIDTH + modalOffset;

    return {
      top: verticalPosition,
      left: horizontalPosition,
      parentTop: position.top,
    };
  };

  return (
    <Stack
      direction="row"
      ref={teamNameRef}
      key={row.team.id!.value}
      sx={{
        justifyContent: 'space-between',
        alignItems: row.numberOfRows > 1 ? 'flex-start' : 'center',
        height: `${CELL_HEIGHT * row.numberOfRows}px`,
        minHeight: `${CELL_HEIGHT * row.numberOfRows}px`,
        width: '190px',
        background: '#fff',
        borderBottom: '1px solid #D3D8D9',
        position: 'relative',
      }}
    >
      <Stack direction="row" alignItems="center">
        <Tooltip
          title={
            canEdit ? (
              <Typography color="white" variant="body1" fontSize="14px">
                <FormattedMessage
                  id="installationPlanning.team.clickToManageResources"
                  defaultMessage="Click to manage team resources"
                />
              </Typography>
            ) : undefined
          }
        >
          <Typography
            variant="body2Emphasis"
            fontSize={12}
            sx={{
              cursor: canEdit ? 'pointer' : 'default',
              px: 2,
              py: 1,
              borderRadius: '8px',
              '&:hover': {
                textDecoration: canEdit ? 'underline' : 'none',
              },
              color: row.team.deletedAt ? 'rgba(34,34,38,0.45)' : undefined,
            }}
            onClick={() => {
              if (!canEdit) return;
              setShowAddResourcesToTeam(true);
            }}
          >
            {row.team.name}
          </Typography>
        </Tooltip>
      </Stack>
      {showAddResourcesToTeam && (
        <AddResourcesToTeam
          teamId={row.team.id!.value}
          teamName={row.team.name}
          position={getPositionForAddResourcesToTeam()}
          setShowAddResourcesToTeam={setShowAddResourcesToTeam}
          requiredRole={requiredRole}
        />
      )}
    </Stack>
  );
}
