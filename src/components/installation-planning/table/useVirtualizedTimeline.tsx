// useVirtualizedTimeline.ts
import { useCallback, useEffect, useRef, useState } from 'react';
import { WeekdayMetrics } from '../helpers/dateHelpers';

export interface VisibleRange {
  firstIndex: number;
  lastIndex: number;
}

export type VisibleDate = WeekdayMetrics['halfDaysInRange'][number] & {
  key: string;
  actualIndex: number;
  transform: string;
};

export const useVirtualizedTimeline = (
  tableRef: React.RefObject<HTMLDivElement>,
  halfDayWidth: number,
  halfDaysInRange: any[],
) => {
  const MIN_BUFFER_SIZE = 10;
  const BUFFER_RATIO = 0.5;

  const [visibleRange, setVisibleRange] = useState<VisibleRange>({ firstIndex: 0, lastIndex: 30 });

  const scrollingRef = useRef(false);
  const animationFrameRef = useRef<number | null>(null);
  const prevScrollLeftRef = useRef(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const calculateBufferSize = useCallback(
    (clientWidth: number) => {
      const visibleItems = Math.ceil(clientWidth / halfDayWidth);
      return Math.max(MIN_BUFFER_SIZE, Math.floor(visibleItems * BUFFER_RATIO));
    },
    [halfDayWidth],
  );

  const calculateVisibleRange = useCallback(
    (scrollLeft: number, clientWidth: number): VisibleRange => {
      const visibleItems = Math.ceil(clientWidth / halfDayWidth);
      const bufferSize = calculateBufferSize(clientWidth);

      const firstIndex = Math.max(0, Math.floor(scrollLeft / halfDayWidth) - bufferSize);
      const lastIndex = Math.min(
        halfDaysInRange.length - 1,
        Math.floor(scrollLeft / halfDayWidth) + visibleItems + bufferSize,
      );

      return { firstIndex, lastIndex };
    },
    [halfDayWidth, halfDaysInRange.length, calculateBufferSize],
  );

  const updateVisibleRangeIfNeeded = useCallback(() => {
    const node = tableRef.current;
    if (!node) return;

    const { scrollLeft, clientWidth } = node;
    const scrollDiff = Math.abs(scrollLeft - prevScrollLeftRef.current);
    const scrollThreshold = halfDayWidth / 4;

    if (scrollDiff > scrollThreshold) {
      prevScrollLeftRef.current = scrollLeft;
      setVisibleRange(calculateVisibleRange(scrollLeft, clientWidth));
    }

    if (scrollingRef.current) {
      animationFrameRef.current = requestAnimationFrame(updateVisibleRangeIfNeeded);
    }
  }, [calculateVisibleRange, halfDayWidth, tableRef]);

  useEffect(() => {
    const node = tableRef.current;
    if (!node) return;

    const handleScrollStart = () => {
      scrollingRef.current = true;
      if (animationFrameRef.current === null) {
        animationFrameRef.current = requestAnimationFrame(updateVisibleRangeIfNeeded);
      }
    };

    const handleScrollEnd = () => {
      scrollingRef.current = false;
      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };

    const debouncedScrollEnd = () => {
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
      scrollTimeoutRef.current = setTimeout(() => {
        handleScrollEnd();
      }, 100);
    };

    node.addEventListener('scroll', handleScrollStart, { passive: true });
    node.addEventListener('scroll', debouncedScrollEnd, { passive: true });

    return () => {
      node.removeEventListener('scroll', handleScrollStart);
      node.removeEventListener('scroll', debouncedScrollEnd);
      if (animationFrameRef.current !== null) cancelAnimationFrame(animationFrameRef.current);
    };
  }, [updateVisibleRangeIfNeeded, tableRef]);

  useEffect(() => {
    const node = tableRef.current;
    if (!node) return;
    setVisibleRange(calculateVisibleRange(node.scrollLeft, node.clientWidth));
  }, [calculateVisibleRange, halfDaysInRange.length, tableRef]);

  const visibleDates = halfDaysInRange
    .slice(visibleRange.firstIndex, visibleRange.lastIndex + 1)
    .map((halfDay, index) => {
      const actualIndex = visibleRange.firstIndex + index;

      return {
        ...(halfDay as WeekdayMetrics['halfDaysInRange'][number]),
        key: `${halfDay.stringDate}-${halfDay.startTime}`,
        actualIndex,
        transform: `translateX(${actualIndex * halfDayWidth}px)`,
      } satisfies VisibleDate;
    });

  return { visibleDates };
};
