/* eslint-disable @next/next/no-img-element */
import { grey, beige } from '@ui/theme/colors';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { ResourceForRegion } from '../types/planningTypes';
import { UserIdentity } from '@aira/identity-grpc-api/build/ts_out/com/aira/acquisition/contract/identity/v2/model';

export default function ResourceImage({
  resource,
  size = 'small',
}: {
  resource: ResourceForRegion | UserIdentity;
  size?: 'small' | 'medium' | 'large';
}) {
  const sizeMap = {
    small: '20px',
    medium: '48px',
    large: '80px',
  };
  if (resource.userImage) {
    return (
      <Box
        sx={{
          borderRadius: '50%',
          width: sizeMap[size],
          height: sizeMap[size],
          backgroundColor: grey[100],
        }}
      >
        <img
          src={`data:image/jpeg;base64,${Buffer.from(resource.userImage).toString('base64')}`}
          alt={`${resource.firstName} ${resource.lastName}`}
          style={{
            width: sizeMap[size],
            height: sizeMap[size],
            borderRadius: '50%',
          }}
        />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        width: sizeMap[size],
        height: sizeMap[size],
        backgroundColor: beige[100],
        borderRadius: '50%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Typography
        variant="body2"
        fontSize={size === 'small' ? 8 : size === 'medium' ? 24 : 32}
        sx={{ textTransform: 'uppercase' }}
      >
        {resource.firstName[0]}
        {resource.lastName[0]}
      </Typography>
    </Box>
  );
}
