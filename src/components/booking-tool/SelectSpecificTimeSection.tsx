import { Button } from '@ui/components/Button/Button';
import { Stack, Typography, Box } from '@mui/material';
import { useIntl } from 'react-intl';
import Timestamp from '../booking-shared/Timestamp';
import { getAvailabilityColor } from '../booking-shared/CalendarSection';
import { Duration } from '@aira/grpc-api/build/ts_out/google/protobuf/duration';

const availabilityIndicatorStyle = {
  zIndex: 1,
  content: '""',
  left: '11px',
  bottom: '10px',
  position: 'absolute',
  width: '40px',
  height: '6px',
  pointerEvents: 'none',
  borderRadius: '50px',
};

export interface SelectSpecificTimeSectionProps {
  selectedDate: Date | null;
  availableTimes: { start: Date; end: Date; bestTravelTimeBounds?: Duration }[];
  setSelectedTime: ({ start, end }: { start: Date; end: Date }) => void;
  isSelectedTime: ({ start, end }: { start: Date; end: Date }) => boolean;
  trafficLightEnabled?: boolean;
}

export function SelectSpecificTimeSection({
  selectedDate,
  availableTimes,
  setSelectedTime,
  isSelectedTime,
  trafficLightEnabled = false,
}: SelectSpecificTimeSectionProps) {
  const intl = useIntl();
  return (
    <Stack>
      {selectedDate && (
        <>
          <Typography variant="headline3">
            <Timestamp t={selectedDate} variant="long-date" />
          </Typography>
          <Typography mt={1} variant="body1">
            {intl.formatMessage({ id: 'bookingTool.label.timeSlotsAvailable' })}
            <Typography variant="body1Emphasis">{availableTimes.length}</Typography>
          </Typography>
          {!availableTimes.length ? (
            <Typography align="center" mt={6} mb={2} variant="body1">
              {intl.formatMessage({ id: 'bookingTool.body.noTimeSlotsAvailable' })}
            </Typography>
          ) : (
            <Box
              pt={4}
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, 60px)',
                gridGap: '1rem',
                justifyContent: 'space-between',
              }}
            >
              {availableTimes.map((availability) => (
                <Button
                  key={availability.start.toISOString()}
                  onClick={() => setSelectedTime(availability)}
                  variant={isSelectedTime(availability) ? 'contained' : 'outlined'}
                  color="primary"
                  sx={
                    trafficLightEnabled
                      ? {
                          '::before': {
                            ...availabilityIndicatorStyle,
                            display: 'block',
                            background: getAvailabilityColor([availability], false, trafficLightEnabled),
                          },
                        }
                      : undefined
                  }
                >
                  <Timestamp t={availability.start} variant="time-short" />
                </Button>
              ))}
            </Box>
          )}
        </>
      )}
    </Stack>
  );
}
