import { But<PERSON>, <PERSON>ack, useMediaQuery } from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { enGB } from 'date-fns/locale';
import { FormattedMessage } from 'react-intl';
import { Groundwork } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/api/gateway/groundwork/v1/model';
import { Region } from '@aira/grpc-api/build/ts_out/com/aira/acquisition/contract/energy/solution/v3/model';
import { GroundworkContext } from 'context/groundwork-context';
import { RegionContext } from '../booking-shared/RegionContext';
import SurveysPlanningContainer from 'components/surveys-planning/SurveysPlanningContainer';
import { RegionContext as RegionContext2 } from 'context/RegionContext';
import { DateRangeProvider } from 'components/surveys-planning/contexts/DateRangeContext';
import { SurveyTypeProvider } from 'components/surveys-planning/contexts/SurveyTypeContext';
import { SurveyResourcesProvider } from 'components/surveys-planning/contexts/SurveyResourcesContext';
import { SurveysProvider } from 'components/surveys-planning/contexts/SurveysContext';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import BookingToolContent from 'components/booking-shared/BookingToolContent';

export default function BookingTool({
  groundwork,
  region,
  bookingType,
  hasPermission,
}: {
  groundwork: Groundwork;
  region: Region;
  bookingType: 'sales' | 'technicalSurvey';
  hasPermission: boolean;
}) {
  const isMobile = useMediaQuery('(max-width: 1500px)');
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={enGB}>
      <GroundworkContext.Provider value={groundwork}>
        <RegionContext.Provider value={region}>
          <Stack
            direction="row"
            sx={{
              height: '100%',
              width: '100%',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
            }}
          >
            <Stack
              sx={{
                height: '100%',
                width: isMobile ? '100%' : '560px',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <BookingToolContent installationGroundworkId={groundwork.id!.value} bookingType={bookingType} />
            </Stack>
            {!isMobile && hasPermission && (
              <Stack
                sx={{
                  width: 'calc(100vw - 800px)',
                  height: '100%',
                  position: 'sticky',
                  top: 155,
                  borderRadius: '8px',
                  overflow: 'hidden',
                }}
              >
                <Stack direction="row" justifyContent="flex-end" alignItems="center" mb={2}>
                  <Button
                    variant="rounded"
                    size="small"
                    onClick={() => {
                      window.open(`/surveys-planning/${region.id!.value}`, '_blank');
                    }}
                    startIcon={<ArrowRightTopSquareOutlinedIcon />}
                  >
                    <FormattedMessage id="bookingTool.button.openMap" defaultMessage="Open map" />
                  </Button>
                </Stack>
                <RegionContext2.Provider value={region}>
                  <DateRangeProvider>
                    <SurveyTypeProvider bookingType={bookingType}>
                      <SurveyResourcesProvider>
                        <SurveysProvider>
                          <div
                            style={{
                              height: '100%',
                              width: '100%',
                              borderRadius: '22px',
                              overflow: 'hidden',
                              marginTop: '4px',
                              boxShadow:
                                '0px 406px 114px 0px rgba(0, 0, 0, 0.00), 0px 260px 104px 0px rgba(0, 0, 0, 0.01), 0px 146px 88px 0px rgba(0, 0, 0, 0.05), 0px 65px 65px 0px rgba(0, 0, 0, 0.09), 0px 16px 36px 0px rgba(0, 0, 0, 0.10)',
                            }}
                          >
                            <SurveysPlanningContainer fullScreen={false} bookingType={bookingType} />
                          </div>
                        </SurveysProvider>
                      </SurveyResourcesProvider>
                    </SurveyTypeProvider>
                  </DateRangeProvider>
                </RegionContext2.Provider>
              </Stack>
            )}
          </Stack>
        </RegionContext.Provider>
      </GroundworkContext.Provider>
    </LocalizationProvider>
  );
}
