import { Box, Stack, Typography, Button } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { formatTimeWindowEnd, formatTimeWindowStart, timeWindows } from '../booking-shared/timeWindows';
import type { AvailabilitiesForDate } from '../booking-shared/types';
import Timestamp from '../booking-shared/Timestamp';

export interface SelectTimeWindowSectionProps {
  selectedDate: Date | null;
  dayAvailability: AvailabilitiesForDate | null;
  setSelectedTimeWindow: (timeWindow: 'morning' | 'afternoon' | null) => void;
  selectedTimeWindow: 'morning' | 'afternoon' | null;
}

export function SelectTimeWindowSection({
  selectedDate,
  dayAvailability,
  setSelectedTimeWindow,
  selectedTimeWindow,
}: SelectTimeWindowSectionProps) {
  const intl = useIntl();
  return (
    <Stack>
      {selectedDate && (
        <>
          <Typography variant="headline3">
            <Timestamp t={selectedDate} variant="long-date" />
          </Typography>
          <Typography mt={1} variant="body1">
            {intl.formatMessage({ id: 'bookingTool.label.timeSlotsAvailable' })}
          </Typography>
          {!(dayAvailability?.morningAvailabilities?.length || dayAvailability?.afternoonAvailabilities?.length) ? (
            <Typography align="center" mt={6} mb={2} variant="body1">
              {intl.formatMessage({ id: 'bookingTool.body.noTimeSlotsAvailable' })}
            </Typography>
          ) : (
            <Box
              pt={4}
              sx={{
                display: 'flex',
                direction: 'row',
                gap: '30px',
              }}
            >
              <Button
                disabled={!dayAvailability?.morningAvailabilities?.length}
                onClick={() => setSelectedTimeWindow('morning')}
                variant={selectedTimeWindow === 'morning' ? 'contained' : 'outlined'}
                color="primary"
              >
                <FormattedMessage id="bookingTool.timeWindow.morning" />
                {': '}
                {formatTimeWindowStart(timeWindows.morning)}
                {' - '}
                {formatTimeWindowEnd(timeWindows.morning)}
              </Button>
              <Button
                disabled={!dayAvailability?.afternoonAvailabilities?.length}
                onClick={() => setSelectedTimeWindow('afternoon')}
                variant={selectedTimeWindow === 'afternoon' ? 'contained' : 'outlined'}
                color="primary"
              >
                <FormattedMessage id="bookingTool.timeWindow.afternoon" />
                {': '}
                {formatTimeWindowStart(timeWindows.afternoon)}
                {' - '}
                {formatTimeWindowEnd(timeWindows.afternoon)}
              </Button>
            </Box>
          )}
        </>
      )}
    </Stack>
  );
}
