import { z } from 'zod';

export enum ZoneId {
  ONE = 1,
  TWO = 2,
}

export enum EmitterType {
  RADIATOR = 'radiator',
  UNDERFLOOR = 'underfloor',
}

export const FEATURE_CHOICES = ['heating', 'cooling', 'both'] as const;
export const THERMOSTAT_TYPES = ['wireless', 'wired'] as const;

export const outdoorUnitCapacity = z.enum(['capacity6kw', 'capacity8kw', 'capacity12kw']);
export const unitowerHotWaterTankSize = z.enum(['dhw100', 'dhw250']);
export const hydroboxHotWaterTankSize = z.enum(['dhwNone', 'dhw150', 'dhw200', 'dhw250', 'dhw300']);
export const domesticHotWaterTankSize = z.union([unitowerHotWaterTankSize, hydroboxHotWaterTankSize]);
const unitower = z.literal('unitower');
const hydrobox = z.literal('hydrobox');
export const indoorUnitType = z.union([unitower, hydrobox]);
export const zoneId = z.nativeEnum(ZoneId);

export const featureChoice = z.enum(FEATURE_CHOICES);
export const emitterType = z.nativeEnum(EmitterType);
export const thermostatType = z.enum(THERMOSTAT_TYPES);
export const mixingValve = z.enum(['shared', 'separate']);
export const additionalElectricHeating = z.enum(['0kwHeating3kwBackup', '3kwHeating3kwBackup', '6kwHeating3kwBackup']);

export const heatingCurve = z.tuple([z.number(), z.number(), z.number(), z.number(), z.number()]);
export const coolingCurve = z.tuple([z.number(), z.number(), z.number()]);
export const zone = z.object({
  emitterType: emitterType.optional(),
  featureChoice: featureChoice.optional(),
  outdoorDesignTemperature: z.number().optional(),
  flowTemperature: z.number().optional(),
  thermostatType: thermostatType.optional(),
  coolingCurveTargetFlowTemperatures: z.tuple([z.number(), z.number(), z.number()]),
  minHeatSupplyTemp: z.number(),
  minCoolSupplyTemp: z.number(),
  maxCoolSupplyTemp: z.number(),
});

export const legionellaCycle = z.object({
  enabled: z.boolean(),
  targetTemperature: z.number(),
});

export const energyBalance = z.object({
  startCompressor: z.number(),
  allowImmersionHeater: z.number(),
});

export const thresholds = z.object({
  averageTempBlockHeating: z.number(),
  averageTempBlockImmersionHeater: z.number(),
  averageTempAllowCooling: z.number(),
});

export const prioTime = z.object({
  heatingMinutes: z.number(),
  dhwMinutes: z.number(),
});

export const unitowerPackage = z.object({
  indoorUnitType: unitower.optional(),
  domesticHotWaterTankSize: unitowerHotWaterTankSize.optional(),
});

export const hydroboxPackage = z.object({
  indoorUnitType: hydrobox.optional(),
  domesticHotWaterTankSize: hydroboxHotWaterTankSize.optional(),
});

export const indoorPackage = z.union([unitowerPackage, hydroboxPackage]);

export const heatPumpConfig = z.object({
  outdoorUnitCapacity: outdoorUnitCapacity.optional(),
  indoorPackage: indoorPackage.optional(),
  mixingValve: mixingValve.optional(),
  zones: z.record(zoneId, zone.optional()),
  legionellaCycle,
  additionalElectricHeating: additionalElectricHeating.optional(),
  energyBalance,
  thresholds,
  prioTime,
  nightModeEnabled: z.boolean(),
});

export type AdditionalElectricHeating = z.infer<typeof additionalElectricHeating>;
export type OutdoorUnitCapacity = z.infer<typeof outdoorUnitCapacity>;
export type DomesticHotWaterTankSize = z.infer<typeof domesticHotWaterTankSize>;
export type UnitowerHotWaterTankSize = z.infer<typeof unitowerHotWaterTankSize>;
export type HydroboxHotWaterTankSize = z.infer<typeof hydroboxHotWaterTankSize>;
export type FeatureChoice = z.infer<typeof featureChoice>;
export type ThermostatType = z.infer<typeof thermostatType>;
export type MixingValve = z.infer<typeof mixingValve>;
export type Zone = z.infer<typeof zone>;
export type LegionellaCycle = z.infer<typeof legionellaCycle>;
export type EnergyBalance = z.infer<typeof energyBalance>;
export type Thresholds = z.infer<typeof thresholds>;
export type PrioTime = z.infer<typeof prioTime>;
export type HeatPumpConfig = z.infer<typeof heatPumpConfig>;
export type HeatingCurve = z.infer<typeof heatingCurve>;
export type CoolingCurve = z.infer<typeof coolingCurve>;
export type IndoorUnitType = z.infer<typeof indoorUnitType>;
export type IndoorPackage = z.infer<typeof indoorPackage>;

export function hasHeating(testedZone: Zone | CalculatedZone) {
  return testedZone.featureChoice && ['heating', 'both'].includes(testedZone.featureChoice);
}

export function hasCooling(testedZone: Zone | CalculatedZone) {
  return testedZone.featureChoice && ['cooling', 'both'].includes(testedZone.featureChoice);
}

// Types sent over tRPC

export const calculatedZone = z.object({
  featureChoice,
  maximumSupplySetpoint: z.number().optional(),
  heatingCurve: heatingCurve.optional(),
  minHeatSupplyTemp: z.number(),
  coolingCurve: coolingCurve.optional(),
  minCoolSupplyTemp: z.number(),
  maxCoolSupplyTemp: z.number(),
  thermostatType,
  odt: z.number().optional(),
  flowTemperature: z.number().optional(),
});

export const calculatedHeatPumpConfig = z.object({
  domesticHotWaterTankSize,
  outdoorUnitCapacity,
  indoorUnitType,
  legionellaCycle,
  additionalElectricHeating,
  energyBalance,
  thresholds,
  prioTime,
  zone1: calculatedZone,
  zone2: calculatedZone.optional(),
  mixingValve: mixingValve.optional(),
  nightModeEnabled: z.boolean(),
});

export type CalculatedZone = z.infer<typeof calculatedZone>;
export type CalculatedHeatPumpConfig = z.infer<typeof calculatedHeatPumpConfig>;
