import { DEFAULT_CONFIG_VALUES, assignInitialDataWithDefaultFallbacks } from './configStore';

describe('configStore', () => {
  it('setInitialData maintains the default parameters if the server data is null or undefined', () => {
    const initialDataWithDefaults = assignInitialDataWithDefaultFallbacks({
      zones: undefined,
      legionellaCycle: undefined,
      additionalElectricHeating: undefined,
      thresholds: undefined,
      prioTime: undefined,
      energyBalance: {
        allowImmersionHeater: -500,
        startCompressor: -800,
      },
    });

    expect(initialDataWithDefaults.prioTime).toEqual(DEFAULT_CONFIG_VALUES.prioTime);
    expect(initialDataWithDefaults.thresholds).toEqual(DEFAULT_CONFIG_VALUES.thresholds);
    expect(initialDataWithDefaults.additionalElectricHeating).toEqual(DEFAULT_CONFIG_VALUES.additionalElectricHeating);
    expect(initialDataWithDefaults.energyBalance).toEqual({
      allowImmersionHeater: -500,
      startCompressor: -800,
    });
  });
});
