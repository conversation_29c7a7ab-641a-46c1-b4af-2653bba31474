import { create } from 'zustand';
import assignWith from 'lodash/assignWith';
import {
  AdditionalElectricHeating,
  EnergyBalance,
  HeatPumpConfig,
  HydroboxHotWaterTankSize,
  IndoorUnitType,
  LegionellaCycle,
  MixingValve,
  OutdoorUnitCapacity,
  PrioTime,
  Thresholds,
  UnitowerHotWaterTankSize,
  Zone,
  ZoneId,
} from './types';
import { COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES } from '../utils/configCalculator';

export const ZONE_DEFAULTS: Zone = {
  coolingCurveTargetFlowTemperatures: COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES,
  minHeatSupplyTemp: 10,
  minCoolSupplyTemp: 10,
  maxCoolSupplyTemp: 20,
  thermostatType: 'wireless',
};

export const DEFAULT_CONFIG_VALUES: HeatPumpConfig = {
  // TODO: Remove the default values when the app team has made the necessary changes to make this optional in their API
  legionellaCycle: {
    enabled: true,
    targetTemperature: 65,
  },
  additionalElectricHeating: undefined,
  energyBalance: {
    startCompressor: -30,
    allowImmersionHeater: -600,
  },
  thresholds: {
    averageTempBlockHeating: 17.0,
    averageTempBlockImmersionHeater: 6.0,
    averageTempAllowCooling: 22.0,
  },
  prioTime: {
    heatingMinutes: 15,
    dhwMinutes: 30,
  },
  zones: {
    [ZoneId.ONE]: ZONE_DEFAULTS,
    [ZoneId.TWO]: undefined,
  },
  nightModeEnabled: false,
};

export enum ZonesConfigurationKind {
  UNSET = '',
  ONE_ZONE = 'oneZoneNoMixingValve',
  TWO_ZONES_ONE_MIXING_VALVE = 'twoZonesOneMixingValve',
  TWO_ZONES_TWO_MIXING_VALVES = 'twoZonesTwoMixingValves',
}

export interface HeatPumpConfigStore extends HeatPumpConfig {
  zonesConfiguration: ZonesConfigurationKind;
  configHasChanged: boolean;
  setNightModeEnabled(nightModeEnabled: boolean): void;
  setConfigHasChanged(hasChanged: boolean): void;
  setZonesConfiguration(configKind: ZonesConfigurationKind): void;
  setInitialData(initialData: Partial<HeatPumpConfig>): void;
  setOutdoorUnitCapacity(outdoorUnitCapacity: OutdoorUnitCapacity): void;
  setLegionellaCycle(legionellaCycle: Partial<LegionellaCycle>): void;
  setAdditionalElectricHeating(additionalElectricHeating: Partial<AdditionalElectricHeating>): void;
  setEnergyBalance(energyBalance: Partial<EnergyBalance>): void;
  setThresholds(thresholds: Partial<Thresholds>): void;
  setPrioTime(prioTime: Partial<PrioTime>): void;
  setMixingValve(mixingValve: MixingValve): void;
  setZone(zoneId: ZoneId, zone: Partial<Zone>): void;
  setIndoorUnitType: (indoorUnitType: IndoorUnitType) => void;
  setUnitowerTankSize(unitowerHotWaterTankSize: UnitowerHotWaterTankSize): void;
  setHydroboxTankSize(hydroboxHotWaterTankSize: HydroboxHotWaterTankSize): void;
  clearDomesticHotWaterTankSize: () => void;
}

function getMixingValveForZonesConfiguration(configKind: ZonesConfigurationKind): MixingValve | undefined {
  switch (configKind) {
    case ZonesConfigurationKind.TWO_ZONES_ONE_MIXING_VALVE:
      return 'shared';
    case ZonesConfigurationKind.TWO_ZONES_TWO_MIXING_VALVES:
      return 'separate';
    // no default
  }
  return undefined;
}

function getZonesConfigurationFromHeatPumpConfig(heatPumpConfig: HeatPumpConfig): ZonesConfigurationKind {
  if (heatPumpConfig.zones[ZoneId.TWO] == null) {
    return ZonesConfigurationKind.ONE_ZONE;
  }
  if (heatPumpConfig.mixingValve === 'shared') {
    return ZonesConfigurationKind.TWO_ZONES_ONE_MIXING_VALVE;
  }
  if (heatPumpConfig.mixingValve === 'separate') {
    return ZonesConfigurationKind.TWO_ZONES_TWO_MIXING_VALVES;
  }
  return ZonesConfigurationKind.UNSET;
}

export function assignInitialDataWithDefaultFallbacks(data: Partial<HeatPumpConfig>): HeatPumpConfig {
  const modifiedData = assignWith({ ...DEFAULT_CONFIG_VALUES }, data, (objValue, srcValue) =>
    srcValue == null ? objValue : srcValue,
  );
  return modifiedData;
}

export const useHeatPumpConfigStore = create<HeatPumpConfigStore>()((set, get) => ({
  ...DEFAULT_CONFIG_VALUES,
  zonesConfiguration: ZonesConfigurationKind.UNSET,
  configHasChanged: false,
  setNightModeEnabled(nightModeEnabled) {
    set({ nightModeEnabled, configHasChanged: true });
  },
  setConfigHasChanged(hasChanged) {
    set({ configHasChanged: hasChanged });
  },
  setZonesConfiguration(configKind) {
    const { zones } = get();
    switch (configKind) {
      case ZonesConfigurationKind.ONE_ZONE:
        set({
          zonesConfiguration: configKind,
          mixingValve: undefined,
          zones: {
            ...zones,
            // Remove Zone 2
            [ZoneId.TWO]: undefined,
          },
          configHasChanged: true,
        });
        break;
      case ZonesConfigurationKind.TWO_ZONES_ONE_MIXING_VALVE:
      case ZonesConfigurationKind.TWO_ZONES_TWO_MIXING_VALVES:
        // Add a second zone if there isn't already one
        if (zones[ZoneId.TWO] == null) {
          set({
            zonesConfiguration: configKind,
            zones: {
              ...zones,
              [ZoneId.TWO]: {
                ...ZONE_DEFAULTS,
                thermostatType: zones[ZoneId.ONE]?.thermostatType,
              },
            },
            mixingValve: getMixingValveForZonesConfiguration(configKind),
            configHasChanged: true,
          });
        } else {
          set({
            zonesConfiguration: configKind,
            mixingValve: getMixingValveForZonesConfiguration(configKind),
            configHasChanged: true,
          });
        }
        break;
      // no default
    }
  },
  setInitialData(initialData) {
    set((currentState) => {
      // Keep the defaults if the initialData fields are undefined or null
      const modifiedData = assignInitialDataWithDefaultFallbacks(initialData);
      return {
        ...modifiedData,
        zonesConfiguration: getZonesConfigurationFromHeatPumpConfig({ ...currentState, ...modifiedData }),
        configHasChanged: false,
      };
    });
  },
  setOutdoorUnitCapacity(outdoorUnitCapacity) {
    set({ outdoorUnitCapacity, configHasChanged: true });
  },
  setMixingValve(mixingValve) {
    set({ mixingValve, configHasChanged: true });
  },
  setLegionellaCycle(legionellaCycle) {
    set((currentState) => ({
      legionellaCycle: {
        ...currentState.legionellaCycle,
        ...legionellaCycle,
      },
      configHasChanged: true,
    }));
  },
  setAdditionalElectricHeating(additionalElectricHeating) {
    set({ additionalElectricHeating, configHasChanged: true });
  },
  setEnergyBalance(energyBalance) {
    set((currentState) => ({
      energyBalance: {
        ...currentState.energyBalance,
        ...energyBalance,
      },
      configHasChanged: true,
    }));
  },
  setThresholds(thresholds) {
    set((currentState) => ({
      thresholds: {
        ...currentState.thresholds,
        ...thresholds,
      },
      configHasChanged: true,
    }));
  },
  setPrioTime(prioTime) {
    set((currentState) => ({
      prioTime: {
        ...currentState.prioTime,
        ...prioTime,
      },
      configHasChanged: true,
    }));
  },
  setZone(zoneId, zone) {
    set((currentState) => ({
      configHasChanged: true,
      zones: {
        ...currentState.zones,
        [zoneId]: {
          coolingCurveTargetFlowTemperatures: COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES,
          ...currentState.zones[zoneId],
          ...zone,
        },
      },
    }));
  },
  setIndoorUnitType(indoorUnitType) {
    set({
      configHasChanged: true,
      indoorPackage: {
        indoorUnitType,
        domesticHotWaterTankSize: undefined,
      },
    });
  },
  setUnitowerTankSize(unitowerHotWaterTankSize) {
    set({
      configHasChanged: true,
      indoorPackage: {
        indoorUnitType: 'unitower',
        domesticHotWaterTankSize: unitowerHotWaterTankSize,
      },
    });
  },
  setHydroboxTankSize(hydroboxHotWaterTankSize) {
    set({
      configHasChanged: true,
      indoorPackage: {
        indoorUnitType: 'hydrobox',
        domesticHotWaterTankSize: hydroboxHotWaterTankSize,
      },
    });
  },
  clearDomesticHotWaterTankSize() {
    set({
      configHasChanged: true,
      indoorPackage: {
        ...get().indoorPackage,
        domesticHotWaterTankSize: undefined,
      },
    });
  },
}));
