import { Box, Stack, Typography, useMediaQuery } from '@mui/material';
import { Accordion } from '@ui/components/Accordion/Accordion';
import { Button } from '@ui/components/Button/Button';
import { ErrorCard } from '@ui/components/ErrorCard/ErrorCard';
import { Heading } from '@ui/components/Heading/Heading';
import { FormattedMessage, useIntl } from 'react-intl';
import toast from 'react-hot-toast';
import HeatDesignCard from 'components/heat-design/HeatDesignCard';
import { beige, grey } from '@ui/theme/colors';
import { useHeatPumpParameters } from 'hooks/useHeatPumpParameters';
import SaveIcon from '@ui/components/Icons/material/Save';
import { useHeatPumpConfigStore } from './stores/configStore';
import { ZoneConfig } from './ZoneConfig';
import { calculateConfig } from './utils/configCalculator';
import { ZoneId } from './stores/types';
import AdvancedHeatPumpSettings from './advanced-settings/AdvancedHeatPumpSettings';
import ZonesConfiguration from './ZonesConfiguration';
import { useSaveHeatPumpConfig } from './hooks/useSaveHeatPumpConfig';
import DomesticHotWaterTankSizeSelect from './DomesticHotWaterTankSizeSelect';
import OutdoorUnitCapacitySelect from './OutdoorUnitCapacitySelect';
import IndoorUnitTypeSelect from './IndoorUnitTypeSelect';
import AdditionalElectricHeating from './AdditionalElectricHeating';
import SilentNightMode from './SilentNightMode';
import { ConstraintViolationError } from './components/ConstraintViolationError';

interface HeatPumpConfigProps {
  energySolutionId: string;
  installationGroundworkId: string;
}

export default function HeatPumpConfig({ energySolutionId, installationGroundworkId }: HeatPumpConfigProps) {
  const intl = useIntl();
  const store = useHeatPumpConfigStore();
  const notifySave = () =>
    toast(intl.formatMessage({ id: 'common.notify.saveSuccess' }), { position: 'bottom-center' });

  const { data: savedParameters } = useHeatPumpParameters(installationGroundworkId);
  const {
    saveHeatPumpConfig,
    isUpdatingPumpConfig: isSaving,
    updatingPumpConfigError,
  } = useSaveHeatPumpConfig({
    onSuccess: () => {
      notifySave();
    },
  });

  const calculatedConfig = calculateConfig(store);

  const handleSendData = async () => {
    try {
      await saveHeatPumpConfig(installationGroundworkId);
    } catch (_error) {
      // TODO: in the future we should display the error message to the user
      // do nothing
    }
  };

  const isMobile = useMediaQuery('(max-width: 700px)');

  return (
    <Stack width="100%" direction="row" justifyContent="center">
      <Box
        component="section"
        sx={{
          overflowX: 'visible',
          overflowY: 'auto',
          height: '100%',
          padding: '32px 32px 40px 32px',
          maxWidth: isMobile ? '100vw' : '1000px',
          display: 'flex',
          flex: '1 1 auto',
          flexDirection: 'column',
        }}
      >
        <Stack spacing={2} mb={1}>
          <Heading level={1} variant="headline1">
            {intl.formatMessage({ id: 'heatPumpConfig.title' })}
          </Heading>
          <HeatDesignCard variant="light">
            <Stack>
              <Stack spacing={2}>
                <Heading level={2} variant="headline2">
                  {intl.formatMessage({ id: 'heatPumpConfig.general.title' })}
                </Heading>
                <ConstraintViolationError />
                <OutdoorUnitCapacitySelect />
                <IndoorUnitTypeSelect />
                <DomesticHotWaterTankSizeSelect />
                <SilentNightMode />
                <AdditionalElectricHeating />
                <hr style={{ border: 'none', borderTop: `1px solid ${grey[500]}`, margin: '40px 0' }} />

                <ZonesConfiguration />

                <ZoneConfig
                  zoneId={ZoneId.ONE}
                  energySolutionId={energySolutionId}
                  installationGroundworkId={installationGroundworkId}
                />
                {store.zones[ZoneId.TWO] && (
                  <ZoneConfig
                    zoneId={ZoneId.TWO}
                    energySolutionId={energySolutionId}
                    installationGroundworkId={installationGroundworkId}
                  />
                )}

                <AdvancedHeatPumpSettings />
              </Stack>
            </Stack>
          </HeatDesignCard>
          {updatingPumpConfigError && (
            <ErrorCard
              title={intl.formatMessage({ id: 'common.notify.error' })}
              text={updatingPumpConfigError.message}
              sx={{ mt: 1, mb: 2 }}
            />
          )}
          <Button
            onClick={handleSendData}
            disabled={!store.configHasChanged || isSaving || !calculatedConfig}
            sx={{
              ':disabled': { backgroundColor: beige[100] },
            }}
          >
            <SaveIcon sx={{ mr: 1, mb: '4px' }} />
            <FormattedMessage
              id={isSaving ? 'heatPumpConfig.saveButtonTitle.saving' : 'heatPumpConfig.saveButtonTitle'}
            />
          </Button>
        </Stack>
        {!savedParameters && (
          <Typography textAlign="end" variant="body2">
            <FormattedMessage id="heatPumpConfig.neverSaved" />
          </Typography>
        )}
        {savedParameters && savedParameters.updatedAt && (
          <Typography textAlign="end" variant="body2">
            <FormattedMessage
              id="heatPumpConfig.lastUpdatedAt"
              values={{ updatedAt: new Date(savedParameters.updatedAt).toLocaleString() }}
            />
          </Typography>
        )}
        {savedParameters && (
          <Accordion
            defaultExpanded={false}
            headingLevel={2}
            sx={{
              backgroundColor: beige[150],
              background: beige[150],
              padding: '16px 0',
              '.MuiAccordionSummary-root': {
                p: '0px 16px',
              },
            }}
            header={intl.formatMessage({ id: 'heatPumpConfig.output' })}
          >
            <Stack>
              <pre>{JSON.stringify(savedParameters, null, 2)}</pre>
            </Stack>
          </Accordion>
        )}
      </Box>
    </Stack>
  );
}
