import {
  AdditionalElectricHeating,
  GetHeatPumpParametersResponse,
  HeatPumpParameters,
  OutdoorUnit,
  ZoneId as ProtobufZoneId,
  Zone as ProtobufZone,
  TankSize,
  IndoorUnitType,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.v1';
import { inferRouterOutputs } from '@trpc/server';
import { AppRouter } from 'server/api/root';
import { AdminEnergySolutionPresentation } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.solution.v1';
import { EMITTER_RADIATOR_SUPPLY_POINT } from './utils/configCalculator';

export function mockZone(overrides?: Partial<ProtobufZone>): ProtobufZone {
  return {
    id: ProtobufZoneId.ZONE_ID_1,
    heating: {
      maximumSupplySetpoint: EMITTER_RADIATOR_SUPPLY_POINT,
      minHeatSupplyTemp: 10,
      p1Supply: 65,
      p2Supply: 63,
      p3Supply: 55,
      p4Supply: 46,
      p5Supply: 28,
    },
    thermostat: {
      thermostatType: {
        $case: 'noThermostat',
        noThermostat: {},
      },
    },
    ...overrides,
  };
}

export function createGetHeatPumpParametersResponse(
  overrides?: Partial<HeatPumpParameters>,
): GetHeatPumpParametersResponse {
  const parameters = HeatPumpParameters.create({
    additionalElectricHeating: AdditionalElectricHeating.ADDITIONAL_ELECTRIC_HEATING_0KW_HEATING_3KW_BACKUP,
    legionellaCycle: {
      enabled: true,
      targetTemperature: 65,
    },
    energyBalance: {
      startCompressor: -30,
      allowImmersionHeater: -600,
    },
    thresholds: {
      averageTempBlockHeating: 17.0,
      averageTempBlockImmersionHeater: 6.0,
      averageTempAllowCooling: 22.0,
    },
    prioTime: {
      heatingMinutes: 15,
      dhwMinutes: 30,
    },
    zones: [
      {
        id: ProtobufZoneId.ZONE_ID_1,
        heating: {
          maximumSupplySetpoint: EMITTER_RADIATOR_SUPPLY_POINT,
          minHeatSupplyTemp: 10,
          p1Supply: 65,
          p2Supply: 63,
          p3Supply: 55,
          p4Supply: 46,
          p5Supply: 28,
        },
        thermostat: {
          thermostatType: {
            $case: 'noThermostat',
            noThermostat: {},
          },
        },
      },
    ],
    tankSize: TankSize.TANK_SIZE_250_LITERS,
    numberOfMixingValves: 1,
    outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_12KW,
    indoorUnitType: IndoorUnitType.INDOOR_UNIT_TYPE_UNITOWER,
    ...overrides,
  });

  return {
    parameters,
    createdAt: new Date(2024, 1, 1),
    updatedAt: new Date(2024, 2, 2),
  };
}

const adminEnergySolutionPresentation = AdminEnergySolutionPresentation.create({
  region: {
    iso3166: {
      $case: 'country',
      country: 1,
    },
  },
  presentation: {
    id: { value: '086f0e76-5340-43b8-98dc-8d3802cc92b5' },
    products: [
      {
        productId: {
          value: '72240c29-e53b-48f9-9eeb-a2f5378b8c1c',
        },
        sku: 'tmp-aira-12kw',
        quantity: 1,
        price: {
          currencyCode: 'GBP',
          minorAmount: 729000,
          taxDetails: {
            minorAmountExcludingTax: 729000,
            taxMinorAmount: 0,
            taxRate: 0,
          },
        },
        displayName: 'Aira 12kW',
        productDetails: {
          details: {
            $case: 'heatPumpOutdoorUnit',
            heatPumpOutdoorUnit: {
              effect: 12,
            },
          },
        },
        productMode: 1,
        compatibilityGroup: {
          id: {
            value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
          },
          name: 'Aira',
        },
      },
      {
        productId: {
          value: '98a91741-7281-4fea-b0bb-6b409796686e',
        },
        sku: '103742',
        quantity: 1,
        price: {
          currencyCode: 'GBP',
          minorAmount: 230000,
          taxDetails: {
            minorAmountExcludingTax: 230000,
            taxMinorAmount: 0,
            taxRate: 0,
          },
        },
        displayName: 'All in One - 250L water tank',
        productDetails: {
          details: {
            $case: 'heatPumpIndoorUnit',
            heatPumpIndoorUnit: {
              capacity: {
                value: 217,
                unit: 'L',
              },
            },
          },
        },
        productMode: 1,
        compatibilityGroup: {
          id: {
            value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
          },
          name: 'Aira',
        },
      },
    ],
  },
});
type Solution = inferRouterOutputs<AppRouter>['AiraBackend']['getGrpcEnergySolution'];
export const solution: Solution = {
  solution: adminEnergySolutionPresentation,
};

type Products = inferRouterOutputs<AppRouter>['AiraBackend']['getProducts'];

const BATTERY_DETAILS = {
  $case: 'battery',
  battery: {
    batteryEnergy: {
      value: 10.24,
      unit: 'kWh',
    },
  },
} as const;

const EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE = {
  amountOfPanels: 12,
  peakEffectKw: 0,
  batteryEnergy: {
    value: 10.24,
    unit: 'kWh',
  },
} as const;

const EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS = {
  details: {
    $case: 'solarArrayAndBatteryBundle',
    solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
  },
} as const;

const EXAMPLE_SOLAR_ARRAY_DETAILS = {
  $case: 'solarArray',
  solarArray: {
    peakEffectKw: 0,
    amountOfPanels: 14,
  },
} as const;

export const products: Products = {
  heatPumpOutdoorUnit: [
    {
      id: {
        value: 'e0c104be-fb82-442d-8b03-ff6ffcbd558b',
      },
      sku: '101333',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: '3.5kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'cfaaeb3f-90bf-40d1-aa8c-a226faeca36e',
      },
      sku: '101336',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '10kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '45b40c4a-0e4d-4f6b-9a9a-a9b56e787071',
      },
      sku: '101334',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '5kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '24854aaf-653c-4101-9644-980d5e7d75d5',
      },
      sku: '101335',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 789000,
      },
      displayName: '7kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'c24c0bea-790c-463c-bf23-01305f2d8ab9',
      },
      sku: '101348',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 829000,
      },
      displayName: '3.5kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'd8e5e8f1-15bd-4ab3-9a3f-6935e2a83d71',
      },
      sku: '101337',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '12kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'f970a13a-c396-41d0-9b5f-c95ae7deb2d0',
      },
      sku: '101338',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 829000,
      },
      displayName: '3.5kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '29e5adf7-0375-498c-91ab-a8de4f6d2113',
      },
      sku: '101339',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '5kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'ae913740-e7d4-4d20-8146-0b2885d5127f',
      },
      sku: '100075',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 629000,
      },
      displayName: 'Aira 6kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 6,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'a5aa80c0-e819-419c-be09-bd277b1428b8',
      },
      sku: '100076',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 679000,
      },
      displayName: 'Aira 8kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 8,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '63f52fce-4569-4682-97c2-7d8ac78da7b6',
      },
      sku: '101340',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 889000,
      },
      displayName: '7kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '92fd564a-bc0e-43ea-bf33-3d272c1313a6',
      },
      sku: '101341',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '10kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '26be215c-5f35-4ed9-936d-adecd10e140a',
      },
      sku: '101342',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '12kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '4dc6c36e-fc0a-41a1-8614-58d2a6841b6e',
      },
      sku: '101343',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: '3.5kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'a07afefd-0066-4e20-8e97-97288724a8e1',
      },
      sku: '101344',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '5kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5175da22-4a18-4936-9196-a9eb72952e67',
      },
      sku: '101345',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 789000,
      },
      displayName: '7kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '72240c29-e53b-48f9-9eeb-a2f5378b8c1c',
      },
      sku: 'tmp-aira-12kw',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: 'Aira 12kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'bb177420-dfb2-44cf-8482-6fa3e3550617',
      },
      sku: '101346',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '10kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '0507ead6-f8fb-4b80-a40b-1034c2816498',
      },
      sku: '101347',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '12kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b4470045-f3a5-4467-bc96-7cec20ea1ff1',
      },
      sku: '101349',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '5kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b1663adf-c2f3-458c-9f11-66995d92cf6c',
      },
      sku: '101350',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 889000,
      },
      displayName: '7kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '69ea9d40-7e56-4a22-92b6-214c3457bd0d',
      },
      sku: '101351',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '10kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'e5d65758-b2a7-45cb-a751-84e469b195d5',
      },
      sku: '101352',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '12kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
  ],
  heatPumpIndoorUnit: [
    {
      id: {
        value: 'af141217-8bf6-458d-b8f7-377b5935829f',
      },
      sku: '101356',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - 150L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 135,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '05ef61ce-9da0-4dfd-9c70-a25c3449100a',
      },
      sku: '101357',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 120000,
      },
      displayName: 'Hydraulic or Interface - 200L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 170,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '6611194a-52d2-45f3-b932-a502ce83b3e0',
      },
      sku: '101358',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 140000,
      },
      displayName: 'Hydraulic or Interface - 250L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 229,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5b40d35d-0fb1-4344-b4fd-a8c91b6ed8aa',
      },
      sku: '101359',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 160000,
      },
      displayName: 'Hydraulic or Interface - 300L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 254,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'd083f204-6161-404c-b385-3aa560ecad58',
      },
      sku: '101360',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Interface - Pre plumbed 150L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 135,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '2c3e9a65-8e0c-45c9-98c4-d9f6ac41474b',
      },
      sku: '101361',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 120000,
      },
      displayName: 'Interface - Pre plumbed 200L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 170,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b7de2dd6-fd91-40ae-91ae-5d09e5d34e18',
      },
      sku: '101355',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Unitower - 185L internal tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 185,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '98a91741-7281-4fea-b0bb-6b409796686e',
      },
      sku: '103742',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 230000,
      },
      displayName: 'All in One - 250L water tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 217,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '11731518-f8b7-4ab6-9b97-d133d3da1e48',
      },
      sku: '103798',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 180000,
      },
      displayName: 'All in One - 100L water tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 85,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'cc3b7c1b-8011-4b44-b3c3-e3af4a841bec',
      },
      sku: '101362',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 140000,
      },
      displayName: 'Interface - Pre plumbed 250L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 229,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
  ],
  installationPackage: [
    {
      id: {
        value: '46fcaf1b-05c2-4c8f-891e-96e5874f2068',
      },
      sku: '101190',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Unitower (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '1b6ad06c-8a00-41ef-8628-d85391642d62',
      },
      sku: '101192',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Unitower (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '21b96321-898f-473f-bee4-3da9e5f8c7a9',
      },
      sku: '101193',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Interface (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'f40949b3-dc4a-4a3e-affe-63990947f6b7',
      },
      sku: '101191',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Unitower (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '0125040e-f2b5-4f0d-8b29-f6bd42b7b82d',
      },
      sku: '101194',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Interface (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '180a5308-6b4e-4d0b-ac1f-0f1e8f21f7f6',
      },
      sku: '101198',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Hydraulic (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'aad84616-68b0-416d-95d3-e5c6621304ee',
      },
      sku: '101196',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Hydraulic (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '8cb09fd3-5e48-4a0e-b614-1f36030e0b67',
      },
      sku: '101197',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Hydraulic (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '3b4972d7-1ec4-4d22-a2e3-d78b104f262f',
      },
      sku: '101195',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Interface (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
  ],
  outdoorMounting: [
    {
      id: {
        value: 'd68d7c65-fcca-4d11-9206-ec915639b78b',
      },
      sku: '900017',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Yes (for 12kW unit)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'f7287645-d574-432c-be75-4f4913562474',
      },
      sku: '900016',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Yes (for 6kw & 8kW unit)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'c2a3a99f-a7a4-4a33-a0e2-e7f132de62fe',
      },
      sku: 'ghost-item-stand-no',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'No',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 2,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
  ],
  bufferTank: [
    {
      id: {
        value: '718d21fe-2eef-4971-8d88-1a69a334766b',
      },
      sku: '101353',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: '45 liter (system smaller or equal to 7kW)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '3bbec06a-b921-4cec-8fa6-583b10fe0291',
      },
      sku: '900014',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 20100,
      },
      displayName: '40L',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '5cefe310-6171-4636-a1a7-dbaa8af26bba',
      },
      sku: '101354',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: '100 liter (system larger than 7 kW)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '6f485443-c006-468b-b4cd-24869b837634',
      },
      sku: '900015',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 20100,
      },
      displayName: '100L',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
  ],
  outdoorUnitPipeConnection: [],
  heatingSystem: [
    {
      id: {
        value: 'fab7b34f-5ee4-44bc-bad1-c3c6bf4a668d',
      },
      sku: '101363',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Single circuit',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '0e9331da-a967-4ab1-9d89-0f94f10f7577',
      },
      sku: '101364',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Unitower - Multiple circuits - One temperature',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5da6bcc1-5320-4299-9b42-334949533f9f',
      },
      sku: '101365',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Unitower - Multiple circuits - Multiple temperatures',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'e70ec555-cd59-4a81-9187-a0542eab6723',
      },
      sku: '101366',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - Multiple circuits - One temperature',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'bbbe82ad-8ed2-4d46-a3ea-e05ea34d4fa9',
      },
      sku: '101367',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - Multiple circuits - Multiple temperatures',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
  ],
  installationKit: [],
  hotWaterExpansionVessel: [],
  undergroundPiping: [],
  stonewallDrilling: [],
  installationScaffolding: [],
  isolatorFittings: [],
  electricalBonding: [],
  liftingEquipment: [],
  technicalSurvey: [],
  systemDesign: [],
  digging: [],
  oilTankRemoval: [],
  meterBoxChange: [],
  manifold: [],
  radiatorControl: [],
  systemFlush: [],
  rePiping: [],
  underfloorHeatingCommission: [],
  indoorUnitOutdoorUnitDistance: [],
  temperatureZones: [],
  joinery: [],
  heatingRoom: [],
  oilWallRemoval: [],
  oilTransport: [],
  outdoorUnitWall: [],
  solar: [
    {
      id: {
        value: '917f6d66-34a6-43ac-be4e-cfc6d4735b73',
      },
      sku: 'solar-array-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '14 panels, 6.2kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '7c7b3d53-633c-4cdb-b53a-fe3c8313665e',
      },
      sku: 'solar-array-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 649000,
      },
      displayName: '6 panels, 2.6kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'fd2e3ebe-350e-4b21-ab68-366a7d587ac6',
      },
      sku: 'solar-array-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 699000,
      },
      displayName: '8 panels, 3.5kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '1d41da3c-681b-43d1-8d5c-3753fcb72d22',
      },
      sku: 'solar-array-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '10 panels, 4.4kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '394ec15d-d166-48d9-a605-fcbe850aa07b',
      },
      sku: 'solar-array-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 799000,
      },
      displayName: '12 panels, 5.3kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
  ],
  solarAndBattery: [
    {
      id: {
        value: '734169a1-177f-4968-8a6b-74dd4f0de860',
      },
      sku: 'solar-array-battery-05kwh-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 949000,
      },
      displayName: '10 panels, 4.4kWp + 5.12kWh storage',
      details: {
        details: {
          $case: 'solarArrayAndBatteryBundle',
          solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '5e2549ee-1a0d-44a1-ac27-b704bfc0d81c',
      },
      sku: 'solar-array-battery-05kwh-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '6 panels, 2.6kWp + 5.12kWh storage',
      details: {
        details: {
          $case: 'solarArrayAndBatteryBundle',
          solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '87b591a9-55f2-4acd-af74-9732455ef942',
      },
      sku: 'solar-array-battery-05kwh-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1049000,
      },
      displayName: '14 panels, 6.2kWp + 5.12kWh storage',
      details: {
        details: {
          $case: 'solarArrayAndBatteryBundle',
          solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '6690013a-5547-4fe5-8a53-af518006ad6b',
      },
      sku: 'solar-array-battery-10kwh-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 999000,
      },
      displayName: '6 panels, 2.6kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'c71feadf-5805-4acb-8dc4-b6a6556215d0',
      },
      sku: 'solar-array-battery-05kwh-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 999000,
      },
      displayName: '12 panels, 5.3kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'b548f9bc-4f05-45a1-9922-4defafb391d5',
      },
      sku: 'solar-array-battery-10kwh-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1199000,
      },
      displayName: '14 panels, 6.2kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '56139032-6ca2-4195-8f20-c3e01cc3b094',
      },
      sku: 'solar-array-battery-10kwh-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1049000,
      },
      displayName: '8 panels, 3.5kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '55bf6eb2-868e-484c-a349-a8b3cb999aed',
      },
      sku: 'solar-array-battery-10kwh-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1099000,
      },
      displayName: '10 panels, 4.4kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'cc458b9a-996e-4bf4-bf6a-dcaef6bb0d3e',
      },
      sku: 'solar-array-battery-05kwh-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 899000,
      },
      displayName: '8 panels, 3.5kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '4379e144-1d1a-47e6-a136-84bb7dc3c42d',
      },
      sku: 'solar-array-battery-10kwh-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1149000,
      },
      displayName: '12 panels, 5.3kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
  ],
  addons: [
    {
      id: {
        value: '8f53b80c-9ec1-4861-913d-b28d6dbb1412',
      },
      sku: '102419',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 12500,
      },
      displayName: 'Radiator (S)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '6cf878fd-4b0c-44c4-b682-68a1083c90a9',
      },
      sku: '102421',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 25000,
      },
      displayName: 'Radiator (L)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'c535afd9-c071-4225-a1b5-8c0c2f4a9d20',
      },
      sku: '102420',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 15000,
      },
      displayName: 'Radiator (M)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '2b3ff6c9-390b-45b5-81b6-2fc62250438c',
      },
      sku: '102422',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 60000,
      },
      displayName: 'Radiator (design)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '88eff5cd-6056-4f41-b44d-0dce3cbdb791',
      },
      sku: '101677',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 7000,
      },
      displayName: 'EPC',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'FEE',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'bfb88a18-50fd-4672-b9bc-237e6712e401',
      },
      sku: '101676',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 32200,
      },
      displayName: 'Planning permission',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'FEE',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
  ],
  accessories: [],
  heatingCircuitsPackage: [],
  miscellaneous: [
    {
      id: {
        value: 'b67576f9-448f-40ea-8836-996e56afcc5c',
      },
      sku: 'custom-4',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 4',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: 'fa591ea7-f0d5-41f4-b507-755ab930e807',
      },
      sku: 'custom-5',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 5',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '12f51671-fcbf-4782-ae06-fe884d902542',
      },
      sku: 'custom-1',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 1',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '75ef0e2e-1bef-4a28-9813-afb1c85efa29',
      },
      sku: 'custom-2',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 2',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '16c4b8b0-ab04-4cbd-a654-c8c8b687531f',
      },
      sku: 'custom-3',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 3',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
  ],
  insulation: [
    {
      id: {
        value: '2007c41b-6963-4b7c-973b-b6a4131cb97f',
      },
      sku: 'tmp-loft-ins',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Loft insulation',
      details: {
        details: {
          $case: 'insulation',
          insulation: {
            category: 'LOFT',
          },
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '4553c4da-edbf-4c3b-991f-d08ea86437ab',
      },
      sku: 'tmp-cavity-ins',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Cavity wall insulation',
      details: {
        details: {
          $case: 'insulation',
          insulation: {
            category: 'CAVITY_WALL',
          },
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
  ],
  evCharger: [
    {
      id: {
        value: 'ff6240ec-0c0c-475f-82d3-cc0f12f07680',
      },
      sku: 'tmp-ev-charger',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 132500,
      },
      displayName: 'Myenergi Zappi 7kW Fast charger',
      details: {
        details: {
          $case: 'evCharger',
          evCharger: {},
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
  ],
  battery: [
    {
      id: {
        value: 'c0bf9de0-77bc-479f-a86b-623b90bd7805',
      },
      sku: 'tmp-battery-5kwh',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 269000,
      },
      displayName: '5.12kWh storage',
      details: {
        details: BATTERY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '5df13b47-b43e-48bb-862c-70f2321f6ee0',
      },
      sku: 'tmp-battery-10kwh',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 498000,
      },
      displayName: '10.24kWh storage',
      details: {
        details: BATTERY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
  ],
  allProducts: [
    {
      id: {
        value: 'c0bf9de0-77bc-479f-a86b-623b90bd7805',
      },
      sku: 'tmp-battery-5kwh',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 269000,
      },
      displayName: '5.12kWh storage',
      details: {
        details: BATTERY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'ff6240ec-0c0c-475f-82d3-cc0f12f07680',
      },
      sku: 'tmp-ev-charger',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 132500,
      },
      displayName: 'Myenergi Zappi 7kW Fast charger',
      details: {
        details: {
          $case: 'evCharger',
          evCharger: {},
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '2007c41b-6963-4b7c-973b-b6a4131cb97f',
      },
      sku: 'tmp-loft-ins',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Loft insulation',
      details: {
        details: {
          $case: 'insulation',
          insulation: {
            category: 'LOFT',
          },
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '4553c4da-edbf-4c3b-991f-d08ea86437ab',
      },
      sku: 'tmp-cavity-ins',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Cavity wall insulation',
      details: {
        details: {
          $case: 'insulation',
          insulation: {
            category: 'CAVITY_WALL',
          },
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '8f53b80c-9ec1-4861-913d-b28d6dbb1412',
      },
      sku: '102419',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 12500,
      },
      displayName: 'Radiator (S)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'b67576f9-448f-40ea-8836-996e56afcc5c',
      },
      sku: 'custom-4',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 4',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: 'fa591ea7-f0d5-41f4-b507-755ab930e807',
      },
      sku: 'custom-5',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 5',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '5df13b47-b43e-48bb-862c-70f2321f6ee0',
      },
      sku: 'tmp-battery-10kwh',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 498000,
      },
      displayName: '10.24kWh storage',
      details: {
        details: BATTERY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '12f51671-fcbf-4782-ae06-fe884d902542',
      },
      sku: 'custom-1',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 1',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '75ef0e2e-1bef-4a28-9813-afb1c85efa29',
      },
      sku: 'custom-2',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 2',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '16c4b8b0-ab04-4cbd-a654-c8c8b687531f',
      },
      sku: 'custom-3',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Custom product 3',
      details: {
        details: {
          $case: 'dynamic',
          dynamic: {},
        },
      },
      pricingMode: 2,
      productMode: 1,
    },
    {
      id: {
        value: '6cf878fd-4b0c-44c4-b682-68a1083c90a9',
      },
      sku: '102421',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 25000,
      },
      displayName: 'Radiator (L)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '917f6d66-34a6-43ac-be4e-cfc6d4735b73',
      },
      sku: 'solar-array-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '14 panels, 6.2kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'e0c104be-fb82-442d-8b03-ff6ffcbd558b',
      },
      sku: '101333',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: '3.5kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'c535afd9-c071-4225-a1b5-8c0c2f4a9d20',
      },
      sku: '102420',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 15000,
      },
      displayName: 'Radiator (M)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'cfaaeb3f-90bf-40d1-aa8c-a226faeca36e',
      },
      sku: '101336',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '10kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '2b3ff6c9-390b-45b5-81b6-2fc62250438c',
      },
      sku: '102422',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 60000,
      },
      displayName: 'Radiator (design)',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'RADIATOR',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '734169a1-177f-4968-8a6b-74dd4f0de860',
      },
      sku: 'solar-array-battery-05kwh-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 949000,
      },
      displayName: '10 panels, 4.4kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '5e2549ee-1a0d-44a1-ac27-b704bfc0d81c',
      },
      sku: 'solar-array-battery-05kwh-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '6 panels, 2.6kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '45b40c4a-0e4d-4f6b-9a9a-a9b56e787071',
      },
      sku: '101334',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '5kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '24854aaf-653c-4101-9644-980d5e7d75d5',
      },
      sku: '101335',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 789000,
      },
      displayName: '7kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'c24c0bea-790c-463c-bf23-01305f2d8ab9',
      },
      sku: '101348',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 829000,
      },
      displayName: '3.5kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '718d21fe-2eef-4971-8d88-1a69a334766b',
      },
      sku: '101353',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: '45 liter (system smaller or equal to 7kW)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '87b591a9-55f2-4acd-af74-9732455ef942',
      },
      sku: 'solar-array-battery-05kwh-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1049000,
      },
      displayName: '14 panels, 6.2kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '6690013a-5547-4fe5-8a53-af518006ad6b',
      },
      sku: 'solar-array-battery-10kwh-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 999000,
      },
      displayName: '6 panels, 2.6kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'c71feadf-5805-4acb-8dc4-b6a6556215d0',
      },
      sku: 'solar-array-battery-05kwh-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 999000,
      },
      displayName: '12 panels, 5.3kWp + 5.12kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '88eff5cd-6056-4f41-b44d-0dce3cbdb791',
      },
      sku: '101677',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 7000,
      },
      displayName: 'EPC',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'FEE',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'd8e5e8f1-15bd-4ab3-9a3f-6935e2a83d71',
      },
      sku: '101337',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '12kW Hydraulic',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'f970a13a-c396-41d0-9b5f-c95ae7deb2d0',
      },
      sku: '101338',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 829000,
      },
      displayName: '3.5kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'fab7b34f-5ee4-44bc-bad1-c3c6bf4a668d',
      },
      sku: '101363',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Single circuit',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '0e9331da-a967-4ab1-9d89-0f94f10f7577',
      },
      sku: '101364',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Unitower - Multiple circuits - One temperature',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5da6bcc1-5320-4299-9b42-334949533f9f',
      },
      sku: '101365',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Unitower - Multiple circuits - Multiple temperatures',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'e70ec555-cd59-4a81-9187-a0542eab6723',
      },
      sku: '101366',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - Multiple circuits - One temperature',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b548f9bc-4f05-45a1-9922-4defafb391d5',
      },
      sku: 'solar-array-battery-10kwh-14',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1199000,
      },
      displayName: '14 panels, 6.2kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '46fcaf1b-05c2-4c8f-891e-96e5874f2068',
      },
      sku: '101190',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Unitower (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '56139032-6ca2-4195-8f20-c3e01cc3b094',
      },
      sku: 'solar-array-battery-10kwh-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1049000,
      },
      displayName: '8 panels, 3.5kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '55bf6eb2-868e-484c-a349-a8b3cb999aed',
      },
      sku: 'solar-array-battery-10kwh-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1099000,
      },
      displayName: '10 panels, 4.4kWp + 10.24kWh storage',
      details: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE_DETAILS,
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '29e5adf7-0375-498c-91ab-a8de4f6d2113',
      },
      sku: '101339',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '5kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'ae913740-e7d4-4d20-8146-0b2885d5127f',
      },
      sku: '100075',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 629000,
      },
      displayName: 'Aira 6kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 6,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'a5aa80c0-e819-419c-be09-bd277b1428b8',
      },
      sku: '100076',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 679000,
      },
      displayName: 'Aira 8kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 8,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '7c7b3d53-633c-4cdb-b53a-fe3c8313665e',
      },
      sku: 'solar-array-06',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 649000,
      },
      displayName: '6 panels, 2.6kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'fd2e3ebe-350e-4b21-ab68-366a7d587ac6',
      },
      sku: 'solar-array-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 699000,
      },
      displayName: '8 panels, 3.5kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '1d41da3c-681b-43d1-8d5c-3753fcb72d22',
      },
      sku: 'solar-array-10',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '10 panels, 4.4kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '394ec15d-d166-48d9-a605-fcbe850aa07b',
      },
      sku: 'solar-array-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 799000,
      },
      displayName: '12 panels, 5.3kWp',
      details: {
        details: EXAMPLE_SOLAR_ARRAY_DETAILS,
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '3bbec06a-b921-4cec-8fa6-583b10fe0291',
      },
      sku: '900014',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 20100,
      },
      displayName: '40L',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '63f52fce-4569-4682-97c2-7d8ac78da7b6',
      },
      sku: '101340',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 889000,
      },
      displayName: '7kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'bfb88a18-50fd-4672-b9bc-237e6712e401',
      },
      sku: '101676',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 32200,
      },
      displayName: 'Planning permission',
      details: {
        details: {
          $case: 'addon',
          addon: {
            category: 'FEE',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'cc458b9a-996e-4bf4-bf6a-dcaef6bb0d3e',
      },
      sku: 'solar-array-battery-05kwh-08',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 899000,
      },
      displayName: '8 panels, 3.5kWp + 5.12kWh storage',
      details: {
        details: {
          $case: 'solarArrayAndBatteryBundle',
          solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '92fd564a-bc0e-43ea-bf33-3d272c1313a6',
      },
      sku: '101341',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '10kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '4379e144-1d1a-47e6-a136-84bb7dc3c42d',
      },
      sku: 'solar-array-battery-10kwh-12',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1149000,
      },
      displayName: '12 panels, 5.3kWp + 10.24kWh storage',
      details: {
        details: {
          $case: 'solarArrayAndBatteryBundle',
          solarArrayAndBatteryBundle: EXAMPLE_SOLAR_ARRAY_AND_BATTERY_BUNDLE,
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: 'af141217-8bf6-458d-b8f7-377b5935829f',
      },
      sku: '101356',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - 150L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 135,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '05ef61ce-9da0-4dfd-9c70-a25c3449100a',
      },
      sku: '101357',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 120000,
      },
      displayName: 'Hydraulic or Interface - 200L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 170,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '6611194a-52d2-45f3-b932-a502ce83b3e0',
      },
      sku: '101358',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 140000,
      },
      displayName: 'Hydraulic or Interface - 250L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 229,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5b40d35d-0fb1-4344-b4fd-a8c91b6ed8aa',
      },
      sku: '101359',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 160000,
      },
      displayName: 'Hydraulic or Interface - 300L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 254,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'd083f204-6161-404c-b385-3aa560ecad58',
      },
      sku: '101360',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Interface - Pre plumbed 150L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 135,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '26be215c-5f35-4ed9-936d-adecd10e140a',
      },
      sku: '101342',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '12kW Unitower',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '4dc6c36e-fc0a-41a1-8614-58d2a6841b6e',
      },
      sku: '101343',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: '3.5kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'a07afefd-0066-4e20-8e97-97288724a8e1',
      },
      sku: '101344',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 749000,
      },
      displayName: '5kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '5175da22-4a18-4936-9196-a9eb72952e67',
      },
      sku: '101345',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 789000,
      },
      displayName: '7kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'bbbe82ad-8ed2-4d46-a3ea-e05ea34d4fa9',
      },
      sku: '101367',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 100000,
      },
      displayName: 'Hydraulic or Interface - Multiple circuits - Multiple temperatures',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'HEATING_SYSTEM',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '1b6ad06c-8a00-41ef-8628-d85391642d62',
      },
      sku: '101192',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Unitower (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '2c3e9a65-8e0c-45c9-98c4-d9f6ac41474b',
      },
      sku: '101361',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 120000,
      },
      displayName: 'Interface - Pre plumbed 200L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 170,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b7de2dd6-fd91-40ae-91ae-5d09e5d34e18',
      },
      sku: '101355',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Unitower - 185L internal tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 185,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '21b96321-898f-473f-bee4-3da9e5f8c7a9',
      },
      sku: '101193',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Interface (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'f40949b3-dc4a-4a3e-affe-63990947f6b7',
      },
      sku: '101191',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Unitower (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
    },
    {
      id: {
        value: '0125040e-f2b5-4f0d-8b29-f6bd42b7b82d',
      },
      sku: '101194',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Interface (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'd68d7c65-fcca-4d11-9206-ec915639b78b',
      },
      sku: '900017',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Yes (for 12kW unit)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'f7287645-d574-432c-be75-4f4913562474',
      },
      sku: '900016',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'Yes (for 6kw & 8kW unit)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '5cefe310-6171-4636-a1a7-dbaa8af26bba',
      },
      sku: '101354',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: '100 liter (system larger than 7 kW)',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '98a91741-7281-4fea-b0bb-6b409796686e',
      },
      sku: '103742',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 230000,
      },
      displayName: 'All in One - 250L water tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 217,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '180a5308-6b4e-4d0b-ac1f-0f1e8f21f7f6',
      },
      sku: '101198',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Hydraulic (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '6f485443-c006-468b-b4cd-24869b837634',
      },
      sku: '900015',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 20100,
      },
      displayName: '100L',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'BUFFER_TANK',
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '11731518-f8b7-4ab6-9b97-d133d3da1e48',
      },
      sku: '103798',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 180000,
      },
      displayName: 'All in One - 100L water tank',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 85,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'aad84616-68b0-416d-95d3-e5c6621304ee',
      },
      sku: '101196',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 470000,
      },
      displayName: 'S install - Hydraulic (<8 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 1,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '8cb09fd3-5e48-4a0e-b614-1f36030e0b67',
      },
      sku: '101197',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 520000,
      },
      displayName: 'M install - Hydraulic (8-12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 2,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'c2a3a99f-a7a4-4a33-a0e2-e7f132de62fe',
      },
      sku: 'ghost-item-stand-no',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 0,
      },
      displayName: 'No',
      details: {
        details: {
          $case: 'installationOption',
          installationOption: {
            category: 'OUTDOOR_MOUNTING',
          },
        },
      },
      pricingMode: 1,
      productMode: 2,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: '3b4972d7-1ec4-4d22-a2e3-d78b104f262f',
      },
      sku: '101195',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 620000,
      },
      displayName: 'L install - Interface (>12 radiators)',
      details: {
        details: {
          $case: 'installationPackage',
          installationPackage: {
            size: 3,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'cc3b7c1b-8011-4b44-b3c3-e3af4a841bec',
      },
      sku: '101362',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 140000,
      },
      displayName: 'Interface - Pre plumbed 250L',
      details: {
        details: {
          $case: 'heatPumpIndoorUnit',
          heatPumpIndoorUnit: {
            capacity: {
              value: 229,
              unit: 'L',
            },
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '72240c29-e53b-48f9-9eeb-a2f5378b8c1c',
      },
      sku: 'tmp-aira-12kw',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 729000,
      },
      displayName: 'Aira 12kW',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'b03622c9-8d68-4059-aef2-6f4c2518c1d8',
        },
        name: 'Aira',
      },
    },
    {
      id: {
        value: 'bb177420-dfb2-44cf-8482-6fa3e3550617',
      },
      sku: '101346',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '10kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '0507ead6-f8fb-4b80-a40b-1034c2816498',
      },
      sku: '101347',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 919000,
      },
      displayName: '12kW Interface',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b4470045-f3a5-4467-bc96-7cec20ea1ff1',
      },
      sku: '101349',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 849000,
      },
      displayName: '5kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 5,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'b1663adf-c2f3-458c-9f11-66995d92cf6c',
      },
      sku: '101350',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 889000,
      },
      displayName: '7kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 7,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: '69ea9d40-7e56-4a22-92b6-214c3457bd0d',
      },
      sku: '101351',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '10kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 10,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
    {
      id: {
        value: 'e5d65758-b2a7-45cb-a751-84e469b195d5',
      },
      sku: '101352',
      iso3166: {
        $case: 'country',
        country: 3,
      },
      price: {
        currencyCode: 'GBP',
        minorAmount: 1019000,
      },
      displayName: '12kW Interface w. backup heater',
      details: {
        details: {
          $case: 'heatPumpOutdoorUnit',
          heatPumpOutdoorUnit: {
            effect: 12,
          },
        },
      },
      pricingMode: 1,
      productMode: 1,
      compatibilityGroup: {
        id: {
          value: 'be32232e-80cf-4180-9600-fdd96b2c3dc3',
        },
        name: 'Vaillant',
      },
    },
  ],
};
