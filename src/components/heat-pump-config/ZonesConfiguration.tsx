import { <PERSON>Control, <PERSON><PERSON>abel, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import Image from 'next/image';
import { MessageKey } from 'messageType';
import { StyledFormattedMessage } from 'utils/localization';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { Button } from '@ui/components/Button/Button';
import { useHeatPumpConfigStore, ZonesConfigurationKind } from './stores/configStore';
import { IndoorUnitType } from './stores/types';

function mapConfigurationToImage(configuration: ZonesConfigurationKind, indoorType?: IndoorUnitType): string | null {
  if (indoorType === undefined) return null;
  switch (configuration) {
    case ZonesConfigurationKind.ONE_ZONE:
      return `/images/heatpump-config/${indoorType}/one_zone.png`;
    case ZonesConfigurationKind.TWO_ZONES_ONE_MIXING_VALVE:
      return `/images/heatpump-config/${indoorType}/two_zones_one_valve.png`;
    case ZonesConfigurationKind.TWO_ZONES_TWO_MIXING_VALVES:
      return `/images/heatpump-config/${indoorType}/two_zones_two_valves.png`;
    default:
      return null;
  }
}

function mapConfigurationToDescription(configuration: ZonesConfigurationKind): MessageKey | null {
  if (configuration === ZonesConfigurationKind.UNSET) return null;
  return `heatPumpConfig.zonesConfiguration.${configuration}.description`;
}

export default function ZonesConfiguration() {
  const { formatMessage } = useIntl();
  const zonesConfiguration = useHeatPumpConfigStore((s) => s.zonesConfiguration);
  const setZonesConfiguration = useHeatPumpConfigStore((s) => s.setZonesConfiguration);
  const indoorUnitType = useHeatPumpConfigStore((s) => s.indoorPackage?.indoorUnitType);
  const image = mapConfigurationToImage(zonesConfiguration, indoorUnitType);
  const description = mapConfigurationToDescription(zonesConfiguration);

  const updateZonesConfiguration = (newConfig: ZonesConfigurationKind) => {
    setZonesConfiguration(newConfig);
  };
  const ZONE_CONFIGURATION_OPTIONS = Object.values(ZonesConfigurationKind).filter(
    (value) => value !== ZonesConfigurationKind.UNSET,
  );

  return (
    <section>
      <FormControl sx={{ mb: 2 }} fullWidth>
        <FormLabel id="configuration-label" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <FormattedMessage id="heatPumpConfig.zonesConfiguration.title" defaultMessage="Configuration" />
          <TooltipAira title={<StyledFormattedMessage id="heatPumpConfig.zonesConfiguration.tooltip" />} />
        </FormLabel>
        <Stack direction="row" gap={2} sx={{ mt: 2 }}>
          {ZONE_CONFIGURATION_OPTIONS.map((option) => (
            <Button
              data-testid={`heat-pump-config-zones-configuration-${option} ${zonesConfiguration === option ? 'active' : 'inactive'}`}
              key={option}
              fullWidth
              variant={zonesConfiguration === option ? 'contained' : 'outlined'}
              onClick={() => updateZonesConfiguration(option)}
            >
              <FormattedMessage values={{ br: <br /> }} id={`heatPumpConfig.zonesConfiguration.${option}.label`} />
            </Button>
          ))}
        </Stack>
      </FormControl>
      {image && (
        <Stack>
          <div
            style={{
              padding: 16,
              borderRadius: 8,
              background: 'white',
              margin: '0 auto',
            }}
          >
            <Image
              src={image}
              width={600}
              height={255}
              style={{ height: 'auto', width: '100%' }}
              alt={formatMessage({ id: 'heatPumpConfig.zonesConfiguration.diagram.altText' })}
            />
          </div>
        </Stack>
      )}
      {description && (
        <Typography mt={2} mb={4}>
          <StyledFormattedMessage id={description} />
        </Typography>
      )}
    </section>
  );
}
