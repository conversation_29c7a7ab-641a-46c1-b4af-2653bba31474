import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Stack } from '@mui/material';
import { useIntl } from 'react-intl';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { capacityToNumberString, getSelectedHeatPumpPackages } from './utils/helpers';
import { useHeatPumpConfigStore } from './stores/configStore';
import { OutdoorUnitCapacity } from './stores/types';
import { WarningMessage } from './components/WarningMessage';

export default function OutdoorUnitCapacitySelect() {
  const store = useHeatPumpConfigStore();
  const intl = useIntl();

  const selectedHeatPumpPackages = useHeatPumpStore((state) => state.selectedHeatPumpPackages);
  const { selectedOutdoorUnitCapacity } = getSelectedHeatPumpPackages(selectedHeatPumpPackages);

  const outdoorUnitMismatch =
    selectedOutdoorUnitCapacity !== undefined &&
    store.outdoorUnitCapacity !== undefined &&
    selectedOutdoorUnitCapacity !== store.outdoorUnitCapacity;

  const updateOutdoorUnitCapacity = (capacity: OutdoorUnitCapacity) => {
    store.setOutdoorUnitCapacity(capacity);
    store.clearDomesticHotWaterTankSize();

    if (store.indoorPackage?.indoorUnitType === 'unitower') {
      switch (capacity) {
        case 'capacity6kw':
        case 'capacity8kw':
          store.setUnitowerTankSize('dhw100');
          break;
        case 'capacity12kw':
          store.setUnitowerTankSize('dhw250');
          break;
        default:
          break;
      }
    }
  };

  return (
    <>
      <FormLabel id="outdoor-unit">{intl.formatMessage({ id: 'heatPumpConfig.outdoorUnitCapacity.title' })}</FormLabel>
      <Stack direction="row" gap={2}>
        <Button
          variant={store.outdoorUnitCapacity === 'capacity6kw' ? 'contained' : 'outlined'}
          style={{ width: '115px' }}
          onClick={() => updateOutdoorUnitCapacity('capacity6kw')}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.outdoorUnitCapacity.capacity6kw' })}
        </Button>
        <Button
          variant={store.outdoorUnitCapacity === 'capacity8kw' ? 'contained' : 'outlined'}
          style={{ width: '115px' }}
          onClick={() => updateOutdoorUnitCapacity('capacity8kw')}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.outdoorUnitCapacity.capacity8kw' })}
        </Button>
        <Button
          variant={store.outdoorUnitCapacity === 'capacity12kw' ? 'contained' : 'outlined'}
          style={{ width: '115px' }}
          onClick={() => updateOutdoorUnitCapacity('capacity12kw')}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.outdoorUnitCapacity.capacity12kw' })}
        </Button>
      </Stack>
      {outdoorUnitMismatch && (
        <WarningMessage
          messageId="heatPumpConfig.outdoorUnitCapacity.capacityMismatch"
          values={{
            selectedOutdoorUnitCapacity: capacityToNumberString(selectedOutdoorUnitCapacity),
          }}
        />
      )}
    </>
  );
}
