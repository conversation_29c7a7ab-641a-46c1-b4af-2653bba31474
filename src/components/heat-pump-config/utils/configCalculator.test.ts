import { DEFAULT_CONFIG_VALUES, ZONE_DEFAULTS } from '../stores/configStore';
import { CalculatedHeatPumpConfig, EmitterType, HeatPumpConfig } from '../stores/types';
import {
  calculateConfig,
  COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES,
  COOLING_CURVE_FIXED_TEMPERATURES,
  coolingCurveGraphData,
  heatCurveGraphData,
  HEATING_CURVE_FIXED_TEMPERATURES,
  MAX_SUPPLY_TEMPERATURES,
  MIN_FLOW_TEMPERATURE,
  OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE,
} from './configCalculator';

const validHeatPumpConfig: HeatPumpConfig = {
  ...DEFAULT_CONFIG_VALUES,
  additionalElectricHeating: '6kwHeating3kwBackup',
  outdoorUnitCapacity: 'capacity12kw',
  indoorPackage: {
    indoorUnitType: 'unitower',
    domesticHotWaterTankSize: 'dhw250',
  },
  mixingValve: 'separate',
  zones: {
    1: {
      ...ZONE_DEFAULTS,
      emitterType: EmitterType.RADIATOR,
      featureChoice: 'both',
      outdoorDesignTemperature: 0,
      flowTemperature: 55,
      thermostatType: 'wireless', // Should match the global property
      coolingCurveTargetFlowTemperatures: [20, 12, 10],
    },
    2: {
      ...ZONE_DEFAULTS,
      emitterType: EmitterType.UNDERFLOOR,
      featureChoice: 'both',
      outdoorDesignTemperature: 5,
      flowTemperature: 40,
      thermostatType: 'wireless',
      coolingCurveTargetFlowTemperatures: [18, 15, 5],
    },
  },
};

describe('calculation of heat pump config', () => {
  it('should correctly calculate the heat pump config for valid input', () => {
    expect(calculateConfig(validHeatPumpConfig)).toEqual({
      additionalElectricHeating: '6kwHeating3kwBackup',
      energyBalance: DEFAULT_CONFIG_VALUES.energyBalance,
      legionellaCycle: DEFAULT_CONFIG_VALUES.legionellaCycle,
      prioTime: DEFAULT_CONFIG_VALUES.prioTime,
      thresholds: DEFAULT_CONFIG_VALUES.thresholds,
      nightModeEnabled: DEFAULT_CONFIG_VALUES.nightModeEnabled,
      outdoorUnitCapacity: 'capacity12kw',
      indoorUnitType: 'unitower',
      domesticHotWaterTankSize: 'dhw250',
      mixingValve: 'separate',
      zone1: {
        featureChoice: 'both',
        maximumSupplySetpoint: 70,
        heatingCurve: [65, 63.75, 55, 46.25, 28.75],
        coolingCurve: [20, 12, 10],
        thermostatType: 'wireless', // Should match the global property
        maxCoolSupplyTemp: ZONE_DEFAULTS.maxCoolSupplyTemp,
        minCoolSupplyTemp: ZONE_DEFAULTS.minCoolSupplyTemp,
        minHeatSupplyTemp: ZONE_DEFAULTS.minHeatSupplyTemp,
        odt: 0,
        flowTemperature: 55,
      },
      zone2: {
        featureChoice: 'both',
        maximumSupplySetpoint: 45,
        heatingCurve: [40, 40, 40, 40, 26.666666666666664],
        coolingCurve: [18, 15, 5],
        thermostatType: 'wireless',
        maxCoolSupplyTemp: ZONE_DEFAULTS.maxCoolSupplyTemp,
        minCoolSupplyTemp: ZONE_DEFAULTS.minCoolSupplyTemp,
        minHeatSupplyTemp: ZONE_DEFAULTS.minHeatSupplyTemp,
        odt: 5,
        flowTemperature: 40,
      },
    } satisfies CalculatedHeatPumpConfig);
  });

  it('should correctly calculate the heat pump config for valid input with only one zone', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        mixingValve: undefined, // Not needed when there is only one zone
        zones: {
          1: validHeatPumpConfig.zones[1],
          2: undefined,
        },
      }),
    ).toEqual({
      additionalElectricHeating: '6kwHeating3kwBackup',
      energyBalance: DEFAULT_CONFIG_VALUES.energyBalance,
      legionellaCycle: DEFAULT_CONFIG_VALUES.legionellaCycle,
      prioTime: DEFAULT_CONFIG_VALUES.prioTime,
      thresholds: DEFAULT_CONFIG_VALUES.thresholds,
      nightModeEnabled: DEFAULT_CONFIG_VALUES.nightModeEnabled,
      outdoorUnitCapacity: 'capacity12kw',
      indoorUnitType: 'unitower',
      domesticHotWaterTankSize: 'dhw250',
      mixingValve: undefined,
      zone1: {
        featureChoice: 'both',
        maximumSupplySetpoint: 70,
        heatingCurve: [65, 63.75, 55, 46.25, 28.75],
        coolingCurve: [20, 12, 10],
        thermostatType: 'wireless', // Should match the global property
        maxCoolSupplyTemp: ZONE_DEFAULTS.maxCoolSupplyTemp,
        minCoolSupplyTemp: ZONE_DEFAULTS.minCoolSupplyTemp,
        minHeatSupplyTemp: ZONE_DEFAULTS.minHeatSupplyTemp,
        odt: 0,
        flowTemperature: 55,
      },
      zone2: undefined,
    } satisfies CalculatedHeatPumpConfig);
  });

  it('should correctly calculate the heat pump config for valid input with only one zone with only heating', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        mixingValve: undefined, // Not needed when there is only one zone
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                featureChoice: 'heating',
              },
              2: undefined,
            },
          }),
      }),
    ).toEqual({
      additionalElectricHeating: '6kwHeating3kwBackup',
      energyBalance: DEFAULT_CONFIG_VALUES.energyBalance,
      legionellaCycle: DEFAULT_CONFIG_VALUES.legionellaCycle,
      prioTime: DEFAULT_CONFIG_VALUES.prioTime,
      thresholds: DEFAULT_CONFIG_VALUES.thresholds,
      nightModeEnabled: DEFAULT_CONFIG_VALUES.nightModeEnabled,
      outdoorUnitCapacity: 'capacity12kw',
      indoorUnitType: 'unitower',
      domesticHotWaterTankSize: 'dhw250',
      mixingValve: undefined,
      zone1: {
        featureChoice: 'heating',
        maximumSupplySetpoint: 70,
        heatingCurve: [65, 63.75, 55, 46.25, 28.75],
        coolingCurve: undefined,
        thermostatType: 'wireless', // Should match the global property
        maxCoolSupplyTemp: ZONE_DEFAULTS.maxCoolSupplyTemp,
        minCoolSupplyTemp: ZONE_DEFAULTS.minCoolSupplyTemp,
        minHeatSupplyTemp: ZONE_DEFAULTS.minHeatSupplyTemp,
        odt: 0,
        flowTemperature: 55,
      },
      zone2: undefined,
    } satisfies CalculatedHeatPumpConfig);
  });

  it('should correctly calculate the heat pump config for valid input with only one zone with only cooling', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        mixingValve: undefined, // Not needed when there is only one zone
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                featureChoice: 'cooling',
              },
              2: undefined,
            },
          }),
      }),
    ).toEqual({
      outdoorUnitCapacity: 'capacity12kw',
      additionalElectricHeating: '6kwHeating3kwBackup',
      energyBalance: DEFAULT_CONFIG_VALUES.energyBalance,
      legionellaCycle: DEFAULT_CONFIG_VALUES.legionellaCycle,
      prioTime: DEFAULT_CONFIG_VALUES.prioTime,
      thresholds: DEFAULT_CONFIG_VALUES.thresholds,
      indoorUnitType: 'unitower',
      domesticHotWaterTankSize: 'dhw250',
      mixingValve: undefined,
      zone1: {
        featureChoice: 'cooling',
        maximumSupplySetpoint: undefined,
        heatingCurve: undefined,
        coolingCurve: [20, 12, 10],
        thermostatType: 'wireless', // Should match the global property
        maxCoolSupplyTemp: ZONE_DEFAULTS.maxCoolSupplyTemp,
        minCoolSupplyTemp: ZONE_DEFAULTS.minCoolSupplyTemp,
        minHeatSupplyTemp: ZONE_DEFAULTS.minHeatSupplyTemp,
      },
      zone2: undefined,
      nightModeEnabled: false,
    });
  });

  it('should return undefined for missing outdoor unit capacity', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        outdoorUnitCapacity: undefined,
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing domestic hot water tank size', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        indoorPackage: undefined,
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing mixing value', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        mixingValve: undefined,
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing zone emitter type', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                emitterType: undefined,
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing zone feature choice', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                featureChoice: undefined,
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing zone outdoor design temperature', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                outdoorDesignTemperature: undefined,
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for invalid zone outdoor design temperature', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                outdoorDesignTemperature: 60,
                // For valid values check: src/components/heat-design/Validator.ts
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing zone flow temperature', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                flowTemperature: undefined,
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for invalid zone flow temperature', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                emitterType: EmitterType.UNDERFLOOR,
                flowTemperature: 50,
                // For valid values check: src/components/heat-pump-config/utils/configCalculator.test.ts#flowTempIsValid()
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for missing zone thermostat', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                thermostatType: undefined,
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for invalid zone cooling curve target flow temperatures', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                coolingCurveTargetFlowTemperatures: [0, 50, 100],
                // For valid values check: src/components/heat-design/Validator.ts
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for invalid zone cooling curve', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                coolingCurveTargetFlowTemperatures: [18, 12, 15],
                // For valid values check: src/components/heat-design/Validator.ts
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });

  it('should return undefined for invalid zone heating curve', () => {
    expect(
      calculateConfig({
        ...validHeatPumpConfig,
        ...(validHeatPumpConfig.zones[1] !== undefined &&
          validHeatPumpConfig.zones[2] !== undefined && {
            zones: {
              1: {
                ...validHeatPumpConfig.zones[1],
                flowTemperature: 30,
                outdoorDesignTemperature: 21,
                // For valid values check: src/components/heat-design/Validator.ts
              },
              2: {
                ...validHeatPumpConfig.zones[2],
              },
            },
          }),
      }),
    ).toBeUndefined();
  });
});

describe('visualisation of heating curve', () => {
  it('should correctly calculate the heating curve points', () => {
    const outdoorDesignTemperature = 0;
    const flowTemperature = 40;
    const graphData = heatCurveGraphData(outdoorDesignTemperature, flowTemperature, EmitterType.RADIATOR);

    expect(graphData.length).toBe(HEATING_CURVE_FIXED_TEMPERATURES.length + 4); // +4 for the top and bottom ledge points
    expect(graphData).toEqual([
      {
        x: -30,
        y: MAX_SUPPLY_TEMPERATURES[EmitterType.RADIATOR].heatingCurve,
      },
      {
        x: -25,
        y: MAX_SUPPLY_TEMPERATURES[EmitterType.RADIATOR].heatingCurve,
      },
      {
        x: HEATING_CURVE_FIXED_TEMPERATURES[0],
        y: 55,
      },
      {
        x: HEATING_CURVE_FIXED_TEMPERATURES[1],
        y: 45,
      },
      {
        x: HEATING_CURVE_FIXED_TEMPERATURES[2],
        y: 40,
      },
      {
        x: HEATING_CURVE_FIXED_TEMPERATURES[3],
        y: 35,
      },
      {
        x: HEATING_CURVE_FIXED_TEMPERATURES[4],
        y: 25,
      },
      {
        x: OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE,
        y: MIN_FLOW_TEMPERATURE,
      },
      {
        x: OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE + 5,
        y: MIN_FLOW_TEMPERATURE,
      },
    ]);
  });

  it('should correctly calculate the cooling curve points', () => {
    const graphData = coolingCurveGraphData([22, 12, 8]);

    expect(graphData.length).toBe(COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES.length + 2); // +2 for the top and bottom ledge points
    expect(graphData).toEqual([
      {
        x: COOLING_CURVE_FIXED_TEMPERATURES[0] - 10,
        y: 22,
      },
      {
        x: COOLING_CURVE_FIXED_TEMPERATURES[0],
        y: 22,
      },
      {
        x: COOLING_CURVE_FIXED_TEMPERATURES[1],
        y: 12,
      },
      {
        x: COOLING_CURVE_FIXED_TEMPERATURES[2],
        y: 8,
      },
      {
        x: COOLING_CURVE_FIXED_TEMPERATURES[2] + 10,
        y: 8,
      },
    ]);
  });
});
