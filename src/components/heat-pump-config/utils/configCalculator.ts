// Code to validate and calculate/map heat pump config

import { propertyIsValid } from 'components/heat-design/Validator';
import { isWithinLimits, toOneDecimalPlace } from 'components/heat-design/utils/helpers';
import { Thresholds } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.v1';
import {
  CalculatedHeatPumpConfig,
  CalculatedZone,
  CoolingCurve,
  coolingCurve,
  DomesticHotWaterTankSize,
  EmitterType,
  EnergyBalance,
  hasCooling,
  hasHeating,
  HeatingCurve,
  HeatPumpConfig,
  LegionellaCycle,
  PrioTime,
  Zone,
  ZoneId,
} from '../stores/types';

// With heat pumps, it is typical to define their heating curve using a set of fixed
// outdoor temperatures i.e. for a given outdoor temperature, what will the flow
// temperature be? Here we define those fixed outdoor temperatures, in Celsius.
export const HEATING_CURVE_FIXED_TEMPERATURES: [number, number, number, number, number] = [-15, -5, 0, 5, 15];

export const COOLING_CURVE_FIXED_TEMPERATURES: [number, number, number] = [20, 30, 40];
export const COOLING_CURVE_DEFAULT_FLOW_TEMPERATURES: [number, number, number] = [20, 15, 10];

export const MIN_FLOW_TEMPERATURE = 20;
export const OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE = 20;

export interface CartesianPoint {
  x: number;
  y: number;
}

export const EMITTER_UNDERFLOOR_SUPPLY_POINT = 45;
export const EMITTER_RADIATOR_SUPPLY_POINT = 70;

// We only allow users of the heat pump configuration to set the max supply temperatures to a certain level
// that is lower than the maximum system supply temperature to ensure some wiggle-room when we later shift
// the heating curve later on as part of the expansion teams' processes.
type MaximumSupplyTemperature = 'heatingCurve' | 'supplySetpoint';
export const MAX_SUPPLY_TEMPERATURES: { [key in EmitterType]: { [tempKey in MaximumSupplyTemperature]: number } } = {
  radiator: {
    supplySetpoint: EMITTER_RADIATOR_SUPPLY_POINT,
    heatingCurve: 65,
  },
  underfloor: {
    supplySetpoint: EMITTER_UNDERFLOOR_SUPPLY_POINT,
    heatingCurve: 40,
  },
};

function throwIfUndefined<T>(t: T | undefined, description: string): T {
  if (t === undefined) {
    throw new Error(`missing: ${description}`);
  }
  return t;
}

function throwIfInvalid<T>(t: T, description: string, isValid: boolean): T {
  if (!isValid) {
    throw new Error(`invalid: ${description}`);
  }
  return t;
}

export function hotWaterTemperature(tankSize?: DomesticHotWaterTankSize): number | undefined {
  switch (tankSize) {
    case 'dhw100':
      return 60;
    case 'dhw150':
    case 'dhw200':
    case 'dhw250':
    case 'dhw300':
      return 55;
    default:
      return undefined;
  }
}

// Calculates x for a given y for a linear curve
function linearCurve(x1: number, y1: number, x2: number, y2: number): (x: number) => number {
  const slope = (y2 - y1) / (x2 - x1);
  const intercept = y1 - slope * x1;
  return (x) => slope * x + intercept;
}

// Calculates y for a given x for a linear curve
function inverseLinearCurve(x1: number, y1: number, x2: number, y2: number): (x: number) => number {
  const slope = (y2 - y1) / (x2 - x1);
  const intercept = y1 - slope * x1;
  return (y) => (intercept - y) / slope;
}

function standardHeatingCurve(outdoorDesignTemperature: number, flowTemperature: number): (x: number) => number {
  return linearCurve(
    OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE,
    MIN_FLOW_TEMPERATURE,
    outdoorDesignTemperature,
    flowTemperature,
  );
}

export function inverseHeatingCurve(outdoorDesignTemperature: number, flowTemperature: number): (x: number) => number {
  return inverseLinearCurve(
    OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE,
    MIN_FLOW_TEMPERATURE,
    outdoorDesignTemperature,
    flowTemperature,
  );
}

function standardCoolingCurve(targetFlowTemperatures: [number, number, number]): (x: number) => number {
  const lineA = linearCurve(
    COOLING_CURVE_FIXED_TEMPERATURES[0],
    targetFlowTemperatures[0],
    COOLING_CURVE_FIXED_TEMPERATURES[1],
    targetFlowTemperatures[1],
  );
  const lineB = linearCurve(
    COOLING_CURVE_FIXED_TEMPERATURES[1],
    targetFlowTemperatures[1],
    COOLING_CURVE_FIXED_TEMPERATURES[2],
    targetFlowTemperatures[2],
  );

  return (x) => {
    if (x > COOLING_CURVE_FIXED_TEMPERATURES[1]) {
      return lineB(x);
    }
    return lineA(x);
  };
}

export function flowTempIsValid(flowTemp?: number, emitterType?: EmitterType): boolean {
  return (
    flowTemp !== undefined &&
    emitterType !== undefined &&
    isWithinLimits(flowTemp, MIN_FLOW_TEMPERATURE, MAX_SUPPLY_TEMPERATURES[emitterType].heatingCurve)
  );
}

// Given X is outdoor temperature and Y is flow temperature, this function checks if the curve is valid
// Points should be strictly increasing in X and decreasing in Y
function zoneCurveIsValid(curve: CartesianPoint[]): boolean {
  return curve.reduce((acc, point, index) => {
    if (index === 0) {
      return true;
    }

    return acc && point.x > curve[index - 1]!.x && point.y <= curve[index - 1]!.y;
  }, true);
}

function heatingCurveIsValid(curve: HeatingCurve): boolean {
  return zoneCurveIsValid(curve.map((y, index) => ({ x: HEATING_CURVE_FIXED_TEMPERATURES[index]!, y })));
}

function coolingCurveIsValid(curve: CoolingCurve): boolean {
  return zoneCurveIsValid(curve.map((y, index) => ({ x: COOLING_CURVE_FIXED_TEMPERATURES[index]!, y })));
}

export function calculateHeatingCurve(zone: Zone): HeatingCurve {
  const curveFunction = standardHeatingCurve(
    throwIfInvalid(
      zone.outdoorDesignTemperature,
      'outdoor design temperature',
      propertyIsValid('heatPumpConfigZone', 'outdoorDesignTemperature', zone.outdoorDesignTemperature ?? -Infinity),
    )!,
    throwIfInvalid(zone.flowTemperature, 'flow temperature', flowTempIsValid(zone.flowTemperature, zone.emitterType))!,
  );

  throwIfUndefined(zone.emitterType, 'emitter type');

  const curve = HEATING_CURVE_FIXED_TEMPERATURES.map(curveFunction) as HeatingCurve;

  const maxSupplyTemperature = MAX_SUPPLY_TEMPERATURES[zone.emitterType!].heatingCurve;

  const heatingCurve: HeatingCurve = [
    Math.min(curve[0], maxSupplyTemperature),
    Math.min(curve[1], maxSupplyTemperature),
    Math.min(curve[2], maxSupplyTemperature),
    Math.min(curve[3], maxSupplyTemperature),
    Math.min(curve[4], maxSupplyTemperature),
  ];

  return throwIfInvalid(heatingCurve, 'heating curve', heatingCurveIsValid(heatingCurve));
}

export function calculateCoolingCurve(zone: Zone): CoolingCurve {
  const { coolingCurveTargetFlowTemperatures: targetFlowTemperatures } = zone;
  targetFlowTemperatures.map((t) =>
    throwIfInvalid(
      t,
      'cooling curve target flow temperature',
      propertyIsValid('heatPumpConfigZone', 'coolingCurveTargetFlowTemperature', t),
    ),
  );

  const curve: CoolingCurve = coolingCurve.parse(
    COOLING_CURVE_FIXED_TEMPERATURES.map(standardCoolingCurve(targetFlowTemperatures)),
  );

  return throwIfInvalid(curve, 'cooling curve', coolingCurveIsValid(curve));
}

export function heatCurveGraphData(odt: number, flowTemp: number, emitterType: EmitterType): CartesianPoint[] {
  const curve = standardHeatingCurve(odt, flowTemp);
  const inverseCurve = inverseHeatingCurve(odt, flowTemp);

  const maxFlowTemperature = MAX_SUPPLY_TEMPERATURES[emitterType].heatingCurve;

  // First, we calculate the flow temperature for each of the fixed outdoor temperatures. It
  // is these flow temperatures that are sent to the heat pump and used for its configuration.
  //
  // FIXME: This should be calculated in one place, not here and the actual data
  const mainCurvePoints = HEATING_CURVE_FIXED_TEMPERATURES.flatMap((outdoorTemperature) => {
    const flowTemperature = curve(outdoorTemperature);

    // However, we also need to ensure that the calculated flow temperatures do not exceed
    // the maximum or minimum flow temperatures. If this happens, we calculate where the curve
    // would meet this limit and add a point there, and then we add the point for the fixed
    // outdoor temperature we are currently looking at - fixed to the flow temperature limit.
    if (flowTemperature > maxFlowTemperature) {
      return [
        { x: outdoorTemperature, y: maxFlowTemperature },
        { x: -inverseCurve(maxFlowTemperature), y: maxFlowTemperature },
      ];
    }
    if (flowTemperature < MIN_FLOW_TEMPERATURE) {
      return [
        { x: outdoorTemperature, y: MIN_FLOW_TEMPERATURE },
        { x: OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE, y: MIN_FLOW_TEMPERATURE },
      ];
    }
    return [{ x: outdoorTemperature, y: flowTemperature }];
  });

  // It is typical when visualising heating curves that curve always extends to the flow
  // temperature limits. However, if all the calculated flow temperatures fall within the limits,
  // that will not be the case. So here, we extend the main heating curve to hit the limits.
  const lowestHeatCurveFlowTemperature = mainCurvePoints[mainCurvePoints.length - 1]?.y;
  const lowerLedge: CartesianPoint[] =
    lowestHeatCurveFlowTemperature !== undefined && lowestHeatCurveFlowTemperature >= MIN_FLOW_TEMPERATURE
      ? [
          { x: OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE, y: MIN_FLOW_TEMPERATURE },
          { x: OUTDOOR_TEMPERATURE_AT_MIN_FLOW_TEMPERATURE + 5, y: MIN_FLOW_TEMPERATURE },
        ]
      : [];
  const highestHeatCurveFlowTemperature = mainCurvePoints[0]?.y;
  const upperLedge: CartesianPoint[] =
    highestHeatCurveFlowTemperature !== undefined && highestHeatCurveFlowTemperature <= maxFlowTemperature
      ? [
          { x: -inverseCurve(maxFlowTemperature) - 5, y: maxFlowTemperature },
          { x: -inverseCurve(maxFlowTemperature), y: maxFlowTemperature },
        ]
      : [];

  return [...upperLedge, ...mainCurvePoints, ...lowerLedge];
}

export function coolingCurveGraphData(targetFlowTemperatures: [number, number, number]): CartesianPoint[] {
  const curve = standardCoolingCurve(targetFlowTemperatures);
  const coolingCurveMainPoints = COOLING_CURVE_FIXED_TEMPERATURES.map((x) => ({ x, y: curve(x) }));

  const minimumFlowTemperature = coolingCurveMainPoints[0]?.y;
  const maximumFlowTemperature = coolingCurveMainPoints[coolingCurveMainPoints.length - 1]?.y;

  return minimumFlowTemperature !== undefined && maximumFlowTemperature !== undefined
    ? [
        { x: COOLING_CURVE_FIXED_TEMPERATURES[0] - 10, y: minimumFlowTemperature },
        ...coolingCurveMainPoints,
        {
          x: COOLING_CURVE_FIXED_TEMPERATURES[COOLING_CURVE_FIXED_TEMPERATURES.length - 1]! + 10,
          y: maximumFlowTemperature,
        },
      ]
    : [];
}

function mapEnergyBalance(energyBalance: EnergyBalance): EnergyBalance {
  return {
    startCompressor: toOneDecimalPlace(energyBalance.startCompressor),
    allowImmersionHeater: toOneDecimalPlace(energyBalance.allowImmersionHeater),
  };
}

function mapThresholds(thresholds: Thresholds): Thresholds {
  return {
    averageTempBlockHeating: toOneDecimalPlace(thresholds.averageTempBlockHeating),
    averageTempBlockImmersionHeater: toOneDecimalPlace(thresholds.averageTempBlockImmersionHeater),
    averageTempAllowCooling: toOneDecimalPlace(thresholds.averageTempAllowCooling),
  };
}

function mapLegionellaCycle(legionellaCycle: LegionellaCycle): LegionellaCycle {
  return {
    enabled: legionellaCycle.enabled,
    targetTemperature: toOneDecimalPlace(legionellaCycle.targetTemperature),
  };
}

function mapPrioTime(prioTime: PrioTime): PrioTime {
  return {
    heatingMinutes: toOneDecimalPlace(prioTime.heatingMinutes),
    dhwMinutes: toOneDecimalPlace(prioTime.dhwMinutes),
  };
}

function calculateZone(zone: Zone): CalculatedZone {
  return {
    featureChoice: throwIfUndefined(zone.featureChoice, 'feature choice'),
    maximumSupplySetpoint: hasHeating(zone)
      ? MAX_SUPPLY_TEMPERATURES[throwIfUndefined(zone.emitterType, 'emitter type')].supplySetpoint
      : undefined,
    heatingCurve: hasHeating(zone) ? calculateHeatingCurve(zone) : undefined,
    coolingCurve: hasCooling(zone) ? calculateCoolingCurve(zone) : undefined,
    thermostatType: throwIfUndefined(zone.thermostatType, 'thermostat type'),
    minHeatSupplyTemp: toOneDecimalPlace(zone.minHeatSupplyTemp),
    minCoolSupplyTemp: toOneDecimalPlace(zone.minCoolSupplyTemp),
    maxCoolSupplyTemp: toOneDecimalPlace(zone.maxCoolSupplyTemp),
    odt: hasHeating(zone)
      ? toOneDecimalPlace(throwIfUndefined(zone.outdoorDesignTemperature, 'outdoor design temperature'))
      : undefined,
    flowTemperature: hasHeating(zone)
      ? toOneDecimalPlace(throwIfUndefined(zone.flowTemperature, 'flow temperature'))
      : undefined,
  };
}

export function calculateConfig(config: HeatPumpConfig): CalculatedHeatPumpConfig | undefined {
  try {
    return {
      outdoorUnitCapacity: throwIfUndefined(config.outdoorUnitCapacity, 'outdoor unit capacity'),
      domesticHotWaterTankSize: throwIfUndefined(config.indoorPackage?.domesticHotWaterTankSize, 'dhw tank size'),
      indoorUnitType: throwIfUndefined(config.indoorPackage?.indoorUnitType, 'indoor unit type'),
      zone1: calculateZone(throwIfUndefined(config.zones[ZoneId.ONE], 'zone 1')),
      zone2: config.zones[ZoneId.TWO] !== undefined ? calculateZone(config.zones[ZoneId.TWO]) : undefined,
      mixingValve:
        config.zones[ZoneId.TWO] !== undefined ? throwIfUndefined(config.mixingValve, 'mixing valve') : undefined,
      additionalElectricHeating: throwIfUndefined(config.additionalElectricHeating, 'additional electric heating'),
      legionellaCycle: mapLegionellaCycle(throwIfUndefined(config.legionellaCycle, 'legionella cycle')),
      energyBalance: mapEnergyBalance(throwIfUndefined(config.energyBalance, 'energy balance')),
      thresholds: mapThresholds(throwIfUndefined(config.thresholds, 'thresholds')),
      prioTime: mapPrioTime(throwIfUndefined(config.prioTime, 'prio time')),
      nightModeEnabled: throwIfUndefined(config.nightModeEnabled, 'silent night mode'),
    };
  } catch (_error) {
    return undefined;
  }
}
