import { IndoorPackage, OutdoorUnitCapacity } from '../stores/types';

enum ConstraintIdentifier {
  UNITOWER_DHW_100_WITH_6_OR_8KW,
  HYDROBOX_DHW_150_WITH_6_OR_8KW,
  HYDROBOX_DHW_300_WITH_8_OR_12KW,
}

type OutdoorCapacityConstraint = {
  constraintIdentifier: ConstraintIdentifier;
  allowedOutdoorUnitCapacities: OutdoorUnitCapacity[];
};

export type IndoorPackageIdentifier = {
  indoorPackage: IndoorPackage;
  skus: string[];
  outdoorCapacityConstraint?: OutdoorCapacityConstraint;
};

const UNITOWER_DHW_100: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'unitower',
    domesticHotWaterTankSize: 'dhw100',
  },
  skus: [
    '103798', // GB
    '800300', // DE
    '800016', // IT
  ],
  outdoorCapacityConstraint: {
    allowedOutdoorUnitCapacities: ['capacity6kw', 'capacity8kw'],
    constraintIdentifier: ConstraintIdentifier.UNITOWER_DHW_100_WITH_6_OR_8KW,
  },
};

const UNITOWER_DHW_250: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'unitower',
    domesticHotWaterTankSize: 'dhw250',
  },
  skus: [
    '103742', // GB
    '800301', // DE
    '800017', // IT
  ],
};

const HYDROBOX_DHW_NONE: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'hydrobox',
    domesticHotWaterTankSize: 'dhwNone',
  },
  skus: [
    'compact-no-dhw', // All markets, temporary SKU
  ],
};

const HYDROBOX_DHW_150: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'hydrobox',
    domesticHotWaterTankSize: 'dhw150',
  },
  skus: [
    '800304', // DE
    '800012', // IT
    '900030', // GB
    '900030', // GB-SCT
  ],
  outdoorCapacityConstraint: {
    allowedOutdoorUnitCapacities: ['capacity6kw', 'capacity8kw'],
    constraintIdentifier: ConstraintIdentifier.HYDROBOX_DHW_150_WITH_6_OR_8KW,
  },
};

const HYDROBOX_DHW_200: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'hydrobox',
    domesticHotWaterTankSize: 'dhw200',
  },
  skus: [
    '800305', // DE
    '800013', // IT
    '900031', // GB
    '900031', // GB-SCT
  ],
};

const HYDROBOX_DHW_250: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'hydrobox',
    domesticHotWaterTankSize: 'dhw250',
  },
  skus: [
    '800306', // DE
    '800014', // IT
    '900032', // GB
    '900032', // GB-SCT
  ],
};

const HYDROBOX_DHW_300: IndoorPackageIdentifier = {
  indoorPackage: {
    indoorUnitType: 'hydrobox',
    domesticHotWaterTankSize: 'dhw300',
  },
  skus: [
    '800307', // DE
    '800015', // IT
    '900033', // GB
    '900033', // GB-SCT
  ],
  outdoorCapacityConstraint: {
    allowedOutdoorUnitCapacities: ['capacity8kw', 'capacity12kw'],
    constraintIdentifier: ConstraintIdentifier.HYDROBOX_DHW_300_WITH_8_OR_12KW,
  },
};

export const INDOOR_IDENTIFIERS: IndoorPackageIdentifier[] = [
  UNITOWER_DHW_100,
  UNITOWER_DHW_250,
  HYDROBOX_DHW_NONE,
  HYDROBOX_DHW_150,
  HYDROBOX_DHW_200,
  HYDROBOX_DHW_250,
  HYDROBOX_DHW_300,
] as const;

export type IndoorPackageAndConstraint = IndoorPackage & {
  // Set if the indoor package is not compatible with the selected outdoor unit capacity
  capacityConstraintViolation?: OutdoorCapacityConstraint;
};

export function getIndoorPackageFromSKU(
  sku: string,
  outdoorUnitCapacity: OutdoorUnitCapacity,
): IndoorPackageAndConstraint | undefined {
  const indoorIdentifier = INDOOR_IDENTIFIERS.find((id) => id.skus.includes(sku));
  if (!indoorIdentifier) {
    return undefined;
  }

  if (
    indoorIdentifier.outdoorCapacityConstraint &&
    !indoorIdentifier?.outdoorCapacityConstraint?.allowedOutdoorUnitCapacities.includes(outdoorUnitCapacity)
  ) {
    return {
      ...indoorIdentifier.indoorPackage,
      capacityConstraintViolation: indoorIdentifier.outdoorCapacityConstraint,
    };
  }

  return indoorIdentifier.indoorPackage;
}
