import type { HeatPumpPackages } from 'components/quotation/stores/HeatPumpPackageStore';
import { HeatPumpOutdoorUnit } from 'types/types';
import { OutdoorUnitCapacity, DomesticHotWaterTankSize, Zone } from '../stores/types';
import { calculateCoolingCurve, calculateHeatingCurve } from './configCalculator';
import { getIndoorPackageFromSKU, IndoorPackageAndConstraint } from './indoorPackageHelper';

export function capacityToNumberString(outdoorUnitCapacity?: OutdoorUnitCapacity): string | undefined {
  switch (outdoorUnitCapacity) {
    case 'capacity6kw':
      return '(6 kW)';
    case 'capacity8kw':
      return '(8 kW)';
    case 'capacity12kw':
      return '(12 kW)';
    default:
      return undefined;
  }
}

export function tankSizeToNumberString(domesticHotWaterTankSize?: DomesticHotWaterTankSize): string | undefined {
  switch (domesticHotWaterTankSize) {
    case 'dhwNone':
      return '(No hot water tank)';
    case 'dhw100':
      return '(100L)';
    case 'dhw150':
      return '(150L)';
    case 'dhw200':
      return '(200L)';
    case 'dhw250':
      return '(250L)';
    case 'dhw300':
      return '(300L)';
    default:
      return undefined;
  }
}

export function toOutdoorUnitCapacity(outdoorUnit?: HeatPumpOutdoorUnit): OutdoorUnitCapacity | undefined {
  switch (outdoorUnit?.effect) {
    case 6:
      return 'capacity6kw';
    case 8:
      return 'capacity8kw';
    case 12:
      return 'capacity12kw';
    default:
      return undefined;
  }
}

export function getSelectedHeatPumpPackages(selectedHeatPumpPackages: HeatPumpPackages): {
  selectedOutdoorUnitCapacity: OutdoorUnitCapacity | undefined;
  selectedIndoorPackageAndConstraint: IndoorPackageAndConstraint | undefined;
} {
  const { heatPumpOutdoorUnit: outdoorUnits, heatPumpIndoorUnit: indoorUnits } = selectedHeatPumpPackages;
  const singleSelectedOutdoorUnit = outdoorUnits && outdoorUnits.length === 1 ? outdoorUnits[0] : undefined;
  const selectedOutdoorUnitCapacity = toOutdoorUnitCapacity(singleSelectedOutdoorUnit);

  const singleSelectedIndoorUnit = indoorUnits && indoorUnits.length === 1 ? indoorUnits[0] : undefined;
  let selectedIndoorPackageAndConstraint: IndoorPackageAndConstraint | undefined;
  if (selectedOutdoorUnitCapacity && singleSelectedIndoorUnit) {
    selectedIndoorPackageAndConstraint = getIndoorPackageFromSKU(
      singleSelectedIndoorUnit.sku,
      selectedOutdoorUnitCapacity,
    );
  }

  return {
    selectedOutdoorUnitCapacity,
    selectedIndoorPackageAndConstraint,
  };
}

export function zoneHeatingCurveIsValid(zone: Zone): boolean {
  try {
    calculateHeatingCurve(zone);
    return true;
  } catch (_error) {
    return false;
  }
}

export function zoneCoolingCurveIsValid(zone: Zone): boolean {
  try {
    calculateCoolingCurve(zone);
    return true;
  } catch (_error) {
    return false;
  }
}
