import { Box, Stack, Typography } from '@mui/material';
import Grid from '@mui/material/Grid';
import { Accordion } from '@ui/components/Accordion/Accordion';
import { InputSlider, RangeInputSlider } from '@ui/components/InputSlider/InputSlider';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { beige } from '@ui/theme/colors';
import { theme } from '@ui/theme/theme';
import { ReactNode } from 'react';
import { FormattedMessage, MessageDescriptor, useIntl } from 'react-intl';
import { useHeatPumpConfigStore, ZONE_DEFAULTS } from '../stores/configStore';
import { ZoneId } from '../stores/types';

const boldedFormattedMessage = (descriptor: MessageDescriptor, zoneId?: number) => (
  <FormattedMessage
    {...descriptor}
    values={{
      strong: (chunks: ReactNode[]) => <strong>{chunks}</strong>,
      br: <br />,
      zone: zoneId,
    }}
  />
);

export default function AdvancedHeatPumpSettings() {
  const intl = useIntl();
  const store = useHeatPumpConfigStore();

  const zoneCoolSupplyLimits = (zoneId: ZoneId) => {
    const min = store.zones[zoneId]?.minCoolSupplyTemp ?? ZONE_DEFAULTS.minCoolSupplyTemp;
    const max = store.zones[zoneId]?.maxCoolSupplyTemp ?? ZONE_DEFAULTS.maxCoolSupplyTemp;
    return [min, max];
  };

  const handleDynamicRangeChange = (newValue: number, key: string) => {
    switch (key) {
      case 'averageTempBlockHeating': {
        if (store.thresholds.averageTempBlockImmersionHeater > newValue - 1) {
          store.setThresholds({ averageTempBlockImmersionHeater: newValue - 1 });
        }
        if (store.thresholds.averageTempAllowCooling < newValue + 3) {
          store.setThresholds({ averageTempAllowCooling: newValue + 3 });
        }
        break;
      }
      case 'energyBalanceCompressor': {
        if (store.energyBalance.allowImmersionHeater > newValue - 10) {
          store.setEnergyBalance({ allowImmersionHeater: newValue - 10 });
        }
        break;
      }
      default:
        break;
    }
  };

  return (
    <Accordion
      defaultExpanded={false}
      headingLevel={2}
      sx={{
        // TODO: Change background to grey 100 if the background is changed to moleskine during the redesign.
        backgroundColor: beige[150],
        background: beige[150],
        padding: '16px 0',
        '.MuiAccordionSummary-root': {
          p: '0px 16px',
        },
      }}
      header={intl.formatMessage({ id: 'heatPumpConfig.advancedSettings' })}
    >
      <Stack
        gap={2}
        maxWidth={600}
        sx={{
          [theme.breakpoints.only('mobile')]: {
            px: 2,
          },
        }}
      >
        <Typography variant="body2">
          {boldedFormattedMessage({ id: 'heatPumpConfig.advancedSettingsDescription' })}
        </Typography>
        <Typography variant="headline3">
          {boldedFormattedMessage({ id: 'heatPumpConfig.advancedSettings.heating.title' })}
        </Typography>
        <InputSlider
          name="heatingOutdoorThreshold"
          min={0}
          max={40}
          value={store.thresholds.averageTempBlockHeating}
          onChange={(value) => store.setThresholds({ averageTempBlockHeating: value })}
          onBlur={(val) => handleDynamicRangeChange(val, 'averageTempBlockHeating')}
          label={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold',
          })}
          tooltipLabel={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.heatingOutdoorThreshold.tooltip',
          })}
        />
        <InputSlider
          name="immersionHeatingOutdoorThreshold"
          min={-50}
          max={store.thresholds.averageTempBlockHeating - 1}
          value={store.thresholds.averageTempBlockImmersionHeater}
          onChange={(value) => store.setThresholds({ averageTempBlockImmersionHeater: value })}
          label={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold',
          })}
          tooltipLabel={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.immersionHeatingOutdoorThreshold.tooltip',
          })}
        />
        <Grid container rowSpacing={6} columnSpacing={2}>
          <Grid size={{ mobile: 6 }}>
            <NumericTextField
              name="energyBalanceCompressor"
              label={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.energyBalanceCompressor',
              })}
              tooltipLabel={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.energyBalanceCompressor.tooltip',
              })}
              value={store.energyBalance.startCompressor}
              onChange={(value) => store.setEnergyBalance({ startCompressor: value })}
              onBlur={(val) => handleDynamicRangeChange(val, 'energyBalanceCompressor')}
              min={-1000}
              max={-5}
              showRangeLabel
            />
          </Grid>
          <Grid size={{ mobile: 6 }}>
            <NumericTextField
              name="energyBalanceImmersionHeating"
              label={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating',
              })}
              tooltipLabel={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.energyBalanceImmersionHeating.tooltip',
              })}
              value={store.energyBalance.allowImmersionHeater}
              onChange={(value) => store.setEnergyBalance({ allowImmersionHeater: value })}
              min={-1000}
              max={store.energyBalance.startCompressor - 10}
              showRangeLabel
            />
          </Grid>
          <Grid size={{ mobile: 6 }}>
            <NumericTextField
              name="priorityTimeHeating"
              label={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.priorityTimeHeating',
              })}
              tooltipLabel={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.priorityTimeHeating.tooltip',
              })}
              value={store.prioTime.heatingMinutes}
              onChange={(value) => store.setPrioTime({ heatingMinutes: value })}
              min={15}
              max={180}
              showRangeLabel
            />
          </Grid>
          <Grid size={{ mobile: 6 }}>
            <NumericTextField
              name="priorityTimeDomesticHotWater"
              label={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater',
              })}
              tooltipLabel={boldedFormattedMessage({
                id: 'heatPumpConfig.advancedSettings.heating.priorityTimeDomesticHotWater.tooltip',
              })}
              value={store.prioTime.dhwMinutes}
              onChange={(value) => store.setPrioTime({ dhwMinutes: value })}
              min={15}
              max={180}
              showRangeLabel
            />
          </Grid>
        </Grid>
        <Typography variant="headline3">
          {boldedFormattedMessage({ id: 'heatPumpConfig.advancedSettings.cooling.title' })}
        </Typography>
        <InputSlider
          name="coolingOutdoorTemperatureThreshold"
          min={store.thresholds.averageTempBlockHeating + 3}
          max={50}
          value={store.thresholds.averageTempAllowCooling}
          onChange={(value) => store.setThresholds({ averageTempAllowCooling: value })}
          label={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold',
          })}
          tooltipLabel={boldedFormattedMessage({
            id: 'heatPumpConfig.advancedSettings.heating.coolingOutdoorTemperatureThreshold.tooltip',
          })}
        />
        {...[ZoneId.ONE, ZoneId.TWO]
          .filter((zoneId) => store.zones[zoneId])
          .map((zoneId) => {
            const featureChoice = store.zones[zoneId]?.featureChoice;
            return (
              <Box key={zoneId}>
                <Typography variant="headline3">
                  {intl.formatMessage({ id: 'heatPumpConfig.zone.title' }, { zone: zoneId })}
                </Typography>
                {featureChoice && ['both', 'heating'].includes(featureChoice) && (
                  <InputSlider
                    name={`minHeatSupplyTemp${zoneId}`}
                    min={5}
                    max={70}
                    value={store.zones[zoneId]!.minHeatSupplyTemp}
                    onChange={(value) => store.setZone(zoneId, { minHeatSupplyTemp: value })}
                    label={boldedFormattedMessage(
                      {
                        id: 'heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp',
                      },
                      zoneId,
                    )}
                    tooltipLabel={boldedFormattedMessage({
                      id: 'heatPumpConfig.advancedSettings.zone.minHeatSupplyTemp.tooltip',
                    })}
                  />
                )}
                {featureChoice && ['both', 'cooling'].includes(featureChoice) && (
                  <RangeInputSlider
                    name={`minCoolSupplyTemp${zoneId}`}
                    min={10}
                    max={30}
                    value={zoneCoolSupplyLimits(zoneId)}
                    onChange={(values) => {
                      store.setZone(zoneId, { minCoolSupplyTemp: values[0], maxCoolSupplyTemp: values[1] });
                    }}
                    label={boldedFormattedMessage(
                      {
                        id: 'heatPumpConfig.advancedSettings.zone.coolSupplyTemp',
                      },
                      zoneId,
                    )}
                    tooltipLabel={boldedFormattedMessage({
                      id: 'heatPumpConfig.advancedSettings.zone.coolSupplyTemp.tooltip',
                    })}
                  />
                )}
              </Box>
            );
          })}
      </Stack>
    </Accordion>
  );
}
