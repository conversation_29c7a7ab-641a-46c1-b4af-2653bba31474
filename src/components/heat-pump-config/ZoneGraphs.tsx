import { Form<PERSON><PERSON>rol, Input<PERSON>abel, Stack, Typography } from '@mui/material';
import { useIntl } from 'react-intl';
import React, { useEffect, useRef } from 'react';
import { blue, grey, red } from '@ui/theme/colors';
import { NumericTextField } from '@ui/components/NumericTextField/NumericTextField';
import { propertyIsValid } from 'components/heat-design/Validator';
import * as d3 from 'd3';
import { useHeatPumpConfigStore } from './stores/configStore';
import { ZoneId } from './stores/types';
import {
  CartesianPoint,
  COOLING_CURVE_FIXED_TEMPERATURES,
  HEATING_CURVE_FIXED_TEMPERATURES,
  MIN_FLOW_TEMPERATURE,
} from './utils/configCalculator';

const INVALID_GRAPH_OPACITY = 0.2;

// For a given outdoor temperature, determine the flow temperature by using the heating curve
function getFlowTemperature(data: CartesianPoint[], outdoorTemperature: number) {
  const bisector = d3.bisector((d: IZoneHeatingGraph['curve'][0]) => d.x).left(data, outdoorTemperature);
  return data[bisector]?.y;
}

// TODO (heating curve):
//  - Detect axis tick overlap (on both axis) and handle
//  - Show an empty graph on empty / invalid input
//  - Add top and bottom limit/ledge values to axis in different fonts

// TODO (cooling curve):
//  - The cooling curve shares a lot with the heating curve, look to extract these to common functions
//  - The cooling curve uses a bunch of magic numbers for styling, try to move away from these
//  - Draw a tick line from the grid line to the fixed input box on the y-axis

export interface IZoneHeatingGraph {
  curve: CartesianPoint[];
  flowTemperature: number;
  outdoorDesignTemperature: number;
  maxSupplyTemperature: number;
  isValid?: boolean;
}

export function ZoneHeatingGraph({
  curve: data,
  flowTemperature,
  outdoorDesignTemperature,
  maxSupplyTemperature,
  isValid = true,
}: IZoneHeatingGraph): React.JSX.Element {
  const intl = useIntl();
  const width = 600;
  const height = 300;
  const margin = { top: 10, right: 20, bottom: 55, left: 115 };
  const opacity = isValid ? 1 : INVALID_GRAPH_OPACITY;

  // x-axis (outdoor temperature) scale
  const x = d3
    .scaleLinear()
    .domain([d3.max(data, (d) => d.x)!, d3.min(data, (d) => d.x)!])
    .range([margin.left, width - margin.right]);

  // y-axis (flow temperature) scale
  const minimumVisibleFlowTemperature = MIN_FLOW_TEMPERATURE - 5;
  const maximumVisibleFlowTemperature = Math.max(maxSupplyTemperature, flowTemperature) + 5;
  const y = d3
    .scaleLinear()
    .domain([minimumVisibleFlowTemperature, maximumVisibleFlowTemperature])
    .range([height - margin.bottom, margin.top]);

  // x-axis (outdoor temperature) visual presentation
  const gx = useRef<SVGSVGElement | null>(null);
  useEffect(() => {
    if (gx.current) {
      const axis = d3.select(gx.current);

      // Draw the axis
      axis
        .call(
          d3
            .axisBottom(x)
            .tickFormat((d) => ((d as number) > 0 ? `+${d} °C` : `${d} °C`))
            .tickValues([...HEATING_CURVE_FIXED_TEMPERATURES])
            .tickSize(0)
            .tickPadding(10),
        )
        .attr('fill', grey[900])
        .attr('font-size', '16px')
        .attr('font-family', 'AiraText')
        .attr('font-weight', 500);

      // Limit the grid lines
      axis
        .selectAll('#x-axis g.tick line')
        .attr('opacity', opacity)
        .attr('stroke', grey[400])
        .attr(
          'y2',
          (outdoorTemperature) =>
            y(getFlowTemperature(data, outdoorTemperature as number) ?? minimumVisibleFlowTemperature) -
            height +
            margin.bottom,
        );
    }
  }, [gx, x, y, data, margin.bottom, minimumVisibleFlowTemperature, opacity]);

  // y-axis (flow temperature) visual presentation
  const gy = useRef<SVGSVGElement | null>(null);
  useEffect(() => {
    if (gy.current) {
      const axis = d3.select(gy.current);
      const flowTemperatures = HEATING_CURVE_FIXED_TEMPERATURES.map((t) => getFlowTemperature(data, t)!);

      // Create a map of temperatures to indices
      const tempIndicesMap = new Map<number, number[]>();
      flowTemperatures.forEach((d, i) => {
        const temp = Math.round(d);
        const index = i + 1;
        if (!tempIndicesMap.has(temp)) {
          tempIndicesMap.set(temp, [index]);
        } else {
          tempIndicesMap.get(temp)!.push(index);
        }
      });

      const uniqueTemps = Array.from(tempIndicesMap.keys());

      // Draw the axis
      axis
        .call(
          d3
            .axisLeft(y)
            .tickFormat((d) => {
              const temp = Math.round(d as number);
              const indices = tempIndicesMap.get(temp)!;
              return `P${indices.join(', ')}: ${Math.round(temp)} °C`;
            })
            .tickValues(uniqueTemps)
            .tickSize(0)
            .tickPadding(10),
        )
        .attr('fill', grey[900])
        .attr('font-size', '16px')
        .attr('font-family', 'AiraText')
        .attr('font-weight', 500);

      // We need to to find the outdoor design temperatures for the grid lines
      // This is done by finding the lowest index of each unique temperature
      // and then mapping that to the corresponding outdoor design temperature
      const outdoorDesignTemperatures = uniqueTemps
        .map((temp) => Math.min(...tempIndicesMap.get(temp)!))
        .map((index) => HEATING_CURVE_FIXED_TEMPERATURES[HEATING_CURVE_FIXED_TEMPERATURES.length - index]!)
        .sort((a, b) => a - b);

      // Limit the grid lines
      axis
        .selectAll('#y-axis g.tick line')
        .attr('stroke', grey[400])
        .attr('opacity', opacity)
        .attr('x2', (_, index) => x(outdoorDesignTemperatures[index]!) - margin.left);
    }
  }, [gy, x, y, data, margin.left, opacity]);

  const line = d3.line(
    (d: IZoneHeatingGraph['curve'][0]) => x(d.x),
    (d: IZoneHeatingGraph['curve'][0]) => y(d.y),
  );

  // This is extracted to its own variable because these values are used both in the positioning
  // of the label and the rotational transformation applied to make it 90° vertical.
  const yAxisLabelPosition = {
    x: -margin.left + margin.left / 5,
    y: margin.top + (height - margin.top - margin.bottom) / 2,
  };

  return (
    <svg viewBox={`0 0 ${width} ${height}`} fontFamily="AiraText" fontWeight={500} data-testid="zone-heating-curve">
      <g id="x-axis" ref={gx} transform={`translate(0,${height - margin.bottom})`}>
        <text
          x={margin.left + (width - margin.left - margin.right) / 2}
          y={margin.bottom - margin.bottom / 5}
          fontSize="14px"
        >
          {intl.formatMessage({ id: 'heatPumpConfig.zone.graph.outdoorDesignTemperature' })}
        </text>
      </g>
      <g id="y-axis" ref={gy} transform={`translate(${margin.left},0)`}>
        <text
          x={yAxisLabelPosition.x}
          y={yAxisLabelPosition.y}
          fontSize="14px"
          textAnchor="middle"
          transform={`rotate(-90, ${yAxisLabelPosition.x}, ${yAxisLabelPosition.y})`}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.zone.graph.flowTemperature' })}
        </text>
      </g>

      <path fill="none" opacity={opacity} stroke={red[500]} strokeWidth="3" d={line(data) ?? undefined} />

      <g id="highlighted-point" opacity={opacity}>
        <circle
          fill={blue[400]}
          stroke={blue[400]}
          strokeWidth="1.5"
          key="12"
          cx={x(outdoorDesignTemperature)}
          cy={y(flowTemperature)}
          r="5"
        />
        <text y={y(flowTemperature)} dominantBaseline="central">
          <tspan x={x(outdoorDesignTemperature)} textAnchor="middle" dy="1.2em">
            {intl.formatMessage({ id: 'heatPumpConfig.zone.graph.odtFlowPointLabel' })}
          </tspan>
          <tspan x={x(outdoorDesignTemperature)} textAnchor="middle" dy="1.2em">
            ({outdoorDesignTemperature}, {flowTemperature})
          </tspan>
        </text>
      </g>

      {!isValid && (
        <foreignObject x={width / 3} y={height / 3} width={width / 2} height={height}>
          <p style={{ color: red[600] }} data-testid="heating-curve-invalid-text">
            {intl.formatMessage({
              id: 'heatPumpConfig.zone.graph.heatingCurveInvalid',
            })}
          </p>
        </foreignObject>
      )}
    </svg>
  );
}

function CoolingGraphFlowInputField({ i, zoneId }: { i: number; zoneId: ZoneId }) {
  const zone = useHeatPumpConfigStore((s) => s.zones[zoneId]);
  const setZone = useHeatPumpConfigStore((s) => s.setZone);

  if (!zone) return null;

  return (
    <FormControl
      sx={{
        flexDirection: 'row',
        '& .MuiFormLabel-root': {
          overflow: 'unset',
          margin: 'auto 5px 12px 0',

          '& span': {
            fontSize: 16,
          },
        },
        '& .MuiInputBase-root': {
          marginRight: '5px',
        },
      }}
    >
      <InputLabel htmlFor="coolingTargetTemp1">
        <Typography variant="inputLabel">{zone.coolingCurveTargetFlowTemperatures.length - i}: </Typography>
      </InputLabel>
      <NumericTextField
        name={`coolingTargetFlowTemperature${i + 1}`}
        value={zone.coolingCurveTargetFlowTemperatures[i]!}
        error={
          !propertyIsValid(
            'heatPumpConfigZone',
            'coolingCurveTargetFlowTemperature',
            zone.coolingCurveTargetFlowTemperatures[i]!,
          )
        }
        onChange={(n) =>
          setZone(zoneId, {
            coolingCurveTargetFlowTemperatures: [
              i === 0 ? n : zone.coolingCurveTargetFlowTemperatures[0],
              i === 1 ? n : zone.coolingCurveTargetFlowTemperatures[1],
              i === 2 ? n : zone.coolingCurveTargetFlowTemperatures[2],
            ],
          })
        }
        size="small"
        inputProps={{
          'data-testid': `cooling-graph-input-${i}`,
        }}
      />
    </FormControl>
  );
}

export interface IZoneCoolingGraph {
  curve: CartesianPoint[];
  zoneId: ZoneId;
  isValid?: boolean;
}

export function ZoneCoolingGraph({ curve: data, zoneId, isValid = true }: IZoneCoolingGraph) {
  const intl = useIntl();
  const zone = useHeatPumpConfigStore((s) => s.zones[zoneId]);

  const width = 600;
  const height = 300;
  const margin = { top: 10, right: 25, bottom: 55, left: 120 };
  const opacity = isValid ? 1 : INVALID_GRAPH_OPACITY;

  // x-axis (outdoor temperature) scale
  const x = d3
    .scaleLinear()
    .domain([d3.min(data, (d) => d.x)!, d3.max(data, (d) => d.x)!])
    .range([margin.left, width - margin.right]);

  // y-axis (flow temperature) scale
  const minimumVisibleFlowTemperature = d3.min(data, (d) => d.y)! - 1;
  const maximumVisibleFlowTemperature = d3.max(data, (d) => d.y)! + 1;
  const y = d3
    .scaleLinear()
    .domain([minimumVisibleFlowTemperature, maximumVisibleFlowTemperature])
    .range([height - margin.bottom, margin.top]);

  // x-axis (outdoor temperature) visual presentation
  const gx = useRef<SVGSVGElement | null>(null);
  useEffect(() => {
    if (gx.current) {
      const axis = d3.select(gx.current);

      // Draw the axis
      axis
        .call(
          d3
            .axisBottom(x)
            .tickFormat((d) => ((d as number) > 0 ? `+${d} °C` : `${d} °C`))
            .tickValues(data.map((d) => d.x))
            .tickSize(0)
            .tickPadding(10),
        )

        .attr('fill', grey[900])
        .attr('font-size', '16px')
        .attr('font-family', 'AiraText')
        .attr('font-weight', 500);

      // Limit the grid lines
      axis
        .selectAll('#x-axis g.tick line')
        .attr('stroke', grey[400])
        .attr('opacity', opacity)
        .attr(
          'y2',
          (outdoorTemperature) =>
            y(getFlowTemperature(data, outdoorTemperature as number) ?? minimumVisibleFlowTemperature) -
            height +
            margin.bottom,
        );
    }
  }, [gx, x, y, data, margin.bottom, minimumVisibleFlowTemperature, opacity]);

  // y-axis (flow temperature) visual presentation
  const gy = useRef<SVGSVGElement | null>(null);
  useEffect(() => {
    if (zone != null && gy.current) {
      const axis = d3.select(gy.current);
      const flowTemperatures = zone.coolingCurveTargetFlowTemperatures;

      // Draw the axis
      axis
        .call(
          d3
            .axisLeft(y)
            .tickFormat(() => '') // Don't show the axis values here, show them with the input boxes
            .tickValues(flowTemperatures)
            .tickSize(0)
            .tickPadding(10),
        )

        .attr('fill', grey[900])
        .attr('font-size', '16px')
        .attr('font-family', 'AiraText')
        .attr('font-weight', 500);

      // Limit the grid lines
      axis
        .selectAll('#y-axis g.tick line')
        .attr('opacity', opacity)
        .attr('stroke', grey[400])
        .attr('x2', (_, index) => x(data[index + 1]?.x ?? COOLING_CURVE_FIXED_TEMPERATURES[0] - 10) - margin.left);
    }
  }, [gy, x, y, data, margin.left, zone, opacity]);

  const line = d3.line(
    (d: IZoneHeatingGraph['curve'][0]) => x(d.x),
    (d: IZoneHeatingGraph['curve'][0]) => y(d.y),
  );

  // This is extracted to its own variable because these values are used both in the positioning
  // of the label and the rotational transformation applied to make it 90° vertical.
  const yAxisLabelPosition = {
    x: -margin.left + margin.left / 5,
    y: margin.top + (height - margin.top - margin.bottom) / 2,
  };

  if (!zone) {
    return null;
  }

  return (
    <svg viewBox={`0 0 ${width} ${height}`} fontFamily="AiraText" fontWeight={500} data-testid="zone-cooling-curve">
      <g id="x-axis" ref={gx} transform={`translate(0,${height - margin.bottom})`}>
        <text
          x={margin.left + (width - margin.left - margin.right) / 2}
          y={margin.bottom - margin.bottom / 5}
          fontSize="14px"
        >
          {intl.formatMessage({ id: 'heatPumpConfig.zone.graph.outdoorDesignTemperature' })}
        </text>
      </g>
      <g id="y-axis" ref={gy} transform={`translate(${margin.left},0)`}>
        <text
          x={yAxisLabelPosition.x}
          y={yAxisLabelPosition.y}
          fontSize="14px"
          textAnchor="middle"
          transform={`rotate(-90, ${yAxisLabelPosition.x}, ${yAxisLabelPosition.y})`}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.zone.graph.flowTemperature' })}
        </text>

        <foreignObject width="70px" height={height - margin.bottom} x={-margin.left + 40}>
          <Stack direction="column" height="100%" width="100%" spacing={2} justifyContent="space-between">
            {zone.coolingCurveTargetFlowTemperatures.map((_, i) => (
              // This array is a fixed-size array, so using the index as a key seems reasonable

              <CoolingGraphFlowInputField key={`CoolingGraphFlowInputField${i}`} i={i} zoneId={zoneId} />
            ))}
          </Stack>
        </foreignObject>
      </g>
      <path opacity={opacity} fill="none" stroke={blue[500]} strokeWidth="3" d={line(data) ?? undefined} />
      {!isValid && (
        <foreignObject x={width / 3} y={height / 3} width={width / 2} height={height}>
          <p style={{ color: red[600] }} data-testid="cooling-curve-invalid-text">
            {intl.formatMessage({
              id: 'heatPumpConfig.zone.graph.coolingCurveInvalid',
            })}
          </p>
        </foreignObject>
      )}
    </svg>
  );
}
