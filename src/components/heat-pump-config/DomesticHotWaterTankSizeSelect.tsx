import { But<PERSON>, <PERSON><PERSON>abel, Stack, Typography } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useMemo } from 'react';
import { useHeatPumpConfigStore } from './stores/configStore';
import { getSelectedHeatPumpPackages, tankSizeToNumberString } from './utils/helpers';
import { hotWaterTemperature } from './utils/configCalculator';
import { WarningMessage } from './components/WarningMessage';
import { INDOOR_IDENTIFIERS } from './utils/indoorPackageHelper';
import { DomesticHotWaterTankSize, HydroboxHotWaterTankSize, UnitowerHotWaterTankSize } from './stores/types';

function findIndoorIdentifier(indoorUnitType: string, domesticHotWaterTankSize: string) {
  return INDOOR_IDENTIFIERS.find(
    (identifier) =>
      identifier.indoorPackage.indoorUnitType === indoorUnitType &&
      identifier.indoorPackage.domesticHotWaterTankSize === domesticHotWaterTankSize,
  );
}

type HydroboxHotWaterTankSizeOption = {
  size: HydroboxHotWaterTankSize;
  indoorIdentifier?: (typeof INDOOR_IDENTIFIERS)[number];
};

type UnitowerHotWaterTankSizeOption = {
  size: UnitowerHotWaterTankSize;
  indoorIdentifier?: (typeof INDOOR_IDENTIFIERS)[number];
};

function DomesticHotWaterTankSizeSelector() {
  const store = useHeatPumpConfigStore();
  const intl = useIntl();

  const { outdoorUnitCapacity, indoorPackage } = store;
  const indoorUnitType = indoorPackage?.indoorUnitType;
  const domesticHotWaterTankSize = store.indoorPackage?.domesticHotWaterTankSize;

  const HYDROBOX_OPTIONS = useMemo(
    () =>
      [
        { size: 'dhwNone', indoorIdentifier: findIndoorIdentifier('hydrobox', 'dhwNone') },
        { size: 'dhw150', indoorIdentifier: findIndoorIdentifier('hydrobox', 'dhw150') },
        { size: 'dhw200', indoorIdentifier: findIndoorIdentifier('hydrobox', 'dhw200') },
        { size: 'dhw250', indoorIdentifier: findIndoorIdentifier('hydrobox', 'dhw250') },
        { size: 'dhw300', indoorIdentifier: findIndoorIdentifier('hydrobox', 'dhw300') },
      ] as HydroboxHotWaterTankSizeOption[],
    [],
  );

  const UNITOWER_OPTIONS = useMemo(
    () =>
      [
        { size: 'dhw100', indoorIdentifier: findIndoorIdentifier('unitower', 'dhw100') },
        { size: 'dhw250', indoorIdentifier: findIndoorIdentifier('unitower', 'dhw250') },
      ] as UnitowerHotWaterTankSizeOption[],
    [],
  );

  if (!outdoorUnitCapacity) {
    return null;
  }

  const options = indoorUnitType === 'hydrobox' ? HYDROBOX_OPTIONS : UNITOWER_OPTIONS;

  const handleClick = (size: DomesticHotWaterTankSize) => {
    if (indoorUnitType === 'hydrobox') {
      store.setHydroboxTankSize(size as HydroboxHotWaterTankSize);
    } else if (indoorUnitType === 'unitower') {
      store.setUnitowerTankSize(size as UnitowerHotWaterTankSize);
    }
  };

  return (
    <Stack direction="row" gap={2} alignItems="center">
      {options.map((option) => {
        const allowedOutdoorUnitCapacities =
          option.indoorIdentifier?.outdoorCapacityConstraint?.allowedOutdoorUnitCapacities;
        const disabled = allowedOutdoorUnitCapacities && !allowedOutdoorUnitCapacities.includes(outdoorUnitCapacity);
        return (
          <Button
            key={option.size}
            variant={domesticHotWaterTankSize === option.size ? 'contained' : 'outlined'}
            onClick={() => handleClick(option.size)}
            disabled={disabled}
            style={{ width: '115px' }}
          >
            {intl.formatMessage({ id: `heatPumpConfig.general.${option.size}` })}
          </Button>
        );
      })}
    </Stack>
  );
}

export default function DomesticHotWaterTankSizeSelect() {
  const store = useHeatPumpConfigStore();
  const selectedHeatPumpPackages = useHeatPumpStore((state) => state.selectedHeatPumpPackages);
  const { selectedIndoorPackageAndConstraint } = getSelectedHeatPumpPackages(selectedHeatPumpPackages);
  const intl = useIntl();

  if (!store.indoorPackage?.indoorUnitType) {
    return null;
  }

  const degrees = hotWaterTemperature(store.indoorPackage?.domesticHotWaterTankSize);
  const temperatureDescription = !!degrees && (
    <Typography variant="body1">
      {intl.formatMessage({ id: 'heatPumpConfig.general.hotWaterTemperature' }, { degrees })}
    </Typography>
  );

  const selectedDomesticHotWaterTankSize = selectedIndoorPackageAndConstraint?.domesticHotWaterTankSize;
  const tankSizeMismatch =
    selectedDomesticHotWaterTankSize !== undefined &&
    store.indoorPackage?.domesticHotWaterTankSize !== undefined &&
    selectedDomesticHotWaterTankSize !== store.indoorPackage?.domesticHotWaterTankSize;

  return (
    <Stack gap={2}>
      <FormLabel id="tank-size-label" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        {intl.formatMessage({ id: 'heatPumpConfig.general.domesticHotWaterTankSize' })}
        <TooltipAira title={<FormattedMessage id="heatPumpConfig.outdoorUnitCapacity.tooltip" />} />
      </FormLabel>
      <DomesticHotWaterTankSizeSelector />
      {temperatureDescription}
      {tankSizeMismatch && (
        <WarningMessage
          messageId="heatPumpConfig.domesticHotWater.tankSizeMismatch"
          values={{
            selectedDomesticHotWaterTankSize: tankSizeToNumberString(selectedDomesticHotWaterTankSize),
          }}
        />
      )}
    </Stack>
  );
}
