import { Box, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { ErrorCard } from '@ui/components/ErrorCard/ErrorCard';
import { useIntl } from 'react-intl';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useEffect } from 'react';
import {
  mapAdditionalElectricHeatingFromProtobuf,
  mapIndoorPackageFromProtobuf,
  mapMixingValvesFromProtobuf,
  mapOutdoorUnitFromProtobuf,
  mapThresholdsFromProtobuf,
  mapZonesConfigFromProtobuf,
} from 'server/mapping/heatPumpConfig';
import { useHeatPumpParameters } from 'hooks/useHeatPumpParameters';
import HeatPumpLoader from 'components/loaders/HeatPumpLoader';
import { useHeatPumpConfigStore } from './stores/configStore';
import { getSelectedHeatPumpPackages } from './utils/helpers';
import { IndoorPackage } from './stores/types';

export default function HeatPumpConfigLoader({
  children,
  installationGroundworkId,
}: {
  children: React.ReactNode;
  installationGroundworkId: string;
}) {
  const intl = useIntl();
  const { data: savedParameters, isLoading } = useHeatPumpParameters(installationGroundworkId);
  const selectedHeatPumpPackages = useHeatPumpStore((state) => state.selectedHeatPumpPackages);
  const selectedCompatibilityGroup = useHeatPumpStore((state) => state.selectedCompatibilityGroup);
  const setInitialData = useHeatPumpConfigStore((s) => s.setInitialData);

  useEffect(() => {
    // Get the selected heat pump packages to store inside the heat pump config store
    const { selectedOutdoorUnitCapacity, selectedIndoorPackageAndConstraint } =
      getSelectedHeatPumpPackages(selectedHeatPumpPackages);
    let selectedIndoorPackage: IndoorPackage | undefined;
    if (selectedIndoorPackageAndConstraint?.capacityConstraintViolation) {
      // Only use the indoor unit type if the capacity constraint is violated
      selectedIndoorPackage = {
        indoorUnitType: selectedIndoorPackageAndConstraint.indoorUnitType,
      };
    } else {
      selectedIndoorPackage = selectedIndoorPackageAndConstraint;
    }

    if (savedParameters?.parameters != null) {
      // Get the saved parameters to store inside the heat pump config store
      setInitialData({
        zones: mapZonesConfigFromProtobuf(savedParameters.parameters.zones),
        legionellaCycle: savedParameters.parameters.legionellaCycle,
        additionalElectricHeating: mapAdditionalElectricHeatingFromProtobuf(
          savedParameters.parameters.additionalElectricHeating,
        ),
        energyBalance: savedParameters.parameters.energyBalance,
        thresholds: mapThresholdsFromProtobuf(savedParameters.parameters.thresholds),
        prioTime: savedParameters.parameters.prioTime,
        mixingValve: mapMixingValvesFromProtobuf(savedParameters.parameters.numberOfMixingValves),

        outdoorUnitCapacity: mapOutdoorUnitFromProtobuf(savedParameters.parameters.outdoorUnit),
        indoorPackage: mapIndoorPackageFromProtobuf(savedParameters.parameters, selectedIndoorPackage),
        nightModeEnabled: savedParameters.parameters.nightModeEnabled,
      });
    } else {
      setInitialData({
        outdoorUnitCapacity: selectedOutdoorUnitCapacity,
        indoorPackage: selectedIndoorPackage,
      });
    }
  }, [savedParameters, setInitialData, selectedHeatPumpPackages]);

  if (isLoading) {
    return (
      <HeatPumpLoader>
        <Typography variant="h6">Importing Heat Pump Config...</Typography>
      </HeatPumpLoader>
    );
  }

  const isAiraProduct = selectedCompatibilityGroup?.name.toLocaleLowerCase().includes('aira') ?? false;

  // If the selected compatibility group is not Aira, show an error card
  if (!isAiraProduct) {
    return (
      <Card
        sx={{
          overflowX: 'visible',
          overflowY: 'auto',
          height: '100vh',
          maxWidth: '100%',
          padding: '20px 0 120px 0',
          '@media(max-width: 700px)': {
            width: '100vw',
          },
          display: 'flex',
          flex: '1 1 auto',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Box sx={{ maxWidth: '750px' }}>
          <ErrorCard
            title={intl.formatMessage({ id: 'heatPumpConfig.notAiraBrand.title' })}
            text={intl.formatMessage({ id: 'heatPumpConfig.notAiraBrand.description' })}
          />
        </Box>
      </Card>
    );
  }

  return children;
}
