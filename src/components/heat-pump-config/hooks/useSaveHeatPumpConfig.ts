import { api } from 'utils/api';
import { useEnergySolutionId } from 'hooks/useEnergySolution';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import { calculateConfig } from '../utils/configCalculator';
import { useHeatPumpConfigStore } from '../stores/configStore';

export function useSaveHeatPumpConfig(callback?: { onSuccess: () => void }) {
  const solutionId = useEnergySolutionId();
  const store = useHeatPumpConfigStore();
  const setConfigHasChanged = useHeatPumpConfigStore((state) => state.setConfigHasChanged);
  const queryClient = useQueryClient();
  const queryKey = getQueryKey(api.InstallationGroundwork.getHeatPumpParameters);
  const invalidateHeatPumpParameters = () => queryClient.invalidateQueries({ queryKey });

  const calculatedConfig = calculateConfig(store);

  const {
    mutateAsync: updateHeatPumpParameters,
    isPending: isUpdatingPumpConfig,
    error: updatingPumpConfigError,
  } = api.InstallationGroundwork.sendHeatPumpParameters.useMutation({ onSuccess: callback?.onSuccess });

  const saveHeatPumpConfig = async (groundWorkId: string) => {
    if (calculatedConfig && solutionId) {
      await updateHeatPumpParameters(
        {
          energySolutionId: solutionId,
          installationGroundworkId: groundWorkId,
          heatPumpConfig: calculatedConfig,
        },
        {
          onSuccess: () => {
            setConfigHasChanged(false);
            invalidateHeatPumpParameters();
          },
        },
      );
    }
  };
  return { saveHeatPumpConfig, isUpdatingPumpConfig, updatingPumpConfigError };
}
