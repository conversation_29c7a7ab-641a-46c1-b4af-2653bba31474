import { Button, FormLabel, Stack } from '@mui/material';
import { FormattedMessage, useIntl } from 'react-intl';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { CountryCode } from 'utils/marketConfigurations';
import { useGroundwork } from 'context/groundwork-context';
import { useHeatPumpConfigStore } from './stores/configStore';

export default function SilentNightMode() {
  const intl = useIntl();
  const { countryCode } = useGroundwork();
  const setNightModeEnabled = useHeatPumpConfigStore((state) => state.setNightModeEnabled);
  const nightModeEnabled = useHeatPumpConfigStore((state) => state.nightModeEnabled);

  if (countryCode !== CountryCode.DE) return null;

  return (
    <Stack gap={1}>
      <FormLabel id="tank-size-label" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <FormattedMessage id="heatPumpConfig.silent.mode" />
        <TooltipAira title={<FormattedMessage id="heatPumpConfig.silent.mode.tooltip" />} />
      </FormLabel>
      <Stack direction="row" gap={2} alignItems="center">
        <Button
          variant={nightModeEnabled ? 'contained' : 'outlined'}
          onClick={() => setNightModeEnabled(true)}
          style={{ width: '115px' }}
        >
          {intl.formatMessage({ id: 'common.yes' })}
        </Button>
        <Button
          variant={!nightModeEnabled ? 'contained' : 'outlined'}
          onClick={() => setNightModeEnabled(false)}
          style={{ width: '115px' }}
        >
          {intl.formatMessage({ id: 'common.no' })}
        </Button>
      </Stack>
    </Stack>
  );
}
