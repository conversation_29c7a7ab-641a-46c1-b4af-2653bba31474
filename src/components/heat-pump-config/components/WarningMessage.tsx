import { SxProps, Typography } from '@mui/material';
import { Card } from '@ui/components/Card/Card';
import { theme } from '@ui/theme/theme';
import { MessageDescriptor } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';

export function WarningMessage({
  messageId,
  values,
  sx,
}: {
  messageId: MessageDescriptor['id'];
  values: Record<string, any>;
  sx?: SxProps;
}) {
  return (
    <Card
      size="small"
      sx={{ borderRadius: 1.5, width: '100%', ...sx }}
      style={{ backgroundColor: theme.palette.warning.light }}
    >
      <Typography variant="body1">
        <StyledFormattedMessage id={messageId} values={values} />
      </Typography>
    </Card>
  );
}
