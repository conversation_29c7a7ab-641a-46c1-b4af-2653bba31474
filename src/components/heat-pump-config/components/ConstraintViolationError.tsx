import { Link, Typography } from '@mui/material';
import { theme } from '@ui/theme/theme';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useIntl } from 'react-intl';
import { StyledFormattedMessage } from 'utils/localization';
import { Card } from '@ui/components/Card/Card';
import { getSelectedHeatPumpPackages } from '../utils/helpers';

// TODO: Remove this error when the backend enforces these constraints
export function ConstraintViolationError() {
  const intl = useIntl();
  function getParentPath(): string {
    const a = window.location.href.split('/');
    a.pop();
    return a.join('/');
  }

  const selectedHeatPumpPackages = useHeatPumpStore((state) => state.selectedHeatPumpPackages);
  const { selectedIndoorPackageAndConstraint, selectedOutdoorUnitCapacity } =
    getSelectedHeatPumpPackages(selectedHeatPumpPackages);

  return (
    selectedOutdoorUnitCapacity &&
    selectedIndoorPackageAndConstraint?.domesticHotWaterTankSize &&
    selectedIndoorPackageAndConstraint.indoorUnitType &&
    selectedIndoorPackageAndConstraint.capacityConstraintViolation && (
      <Card
        size="small"
        sx={{ borderRadius: 3, width: '100%' }}
        id="ErrorCard"
        style={{ backgroundColor: theme.palette.error.light }}
      >
        <Typography variant="headline4">Unallowed selection made</Typography>
        <Typography variant="body1">
          <StyledFormattedMessage
            id="heatPumpConfig.general.domesticHotWaterTankSize.outdoorCapacityConstraintViolation"
            values={{
              productSelectionPage: (
                <Link sx={{ textDecoration: 'underline' }} href={getParentPath()}>
                  {intl.formatMessage({ id: 'heatDesign.title.productSelection' })}
                </Link>
              ),
              indoorUnit: `${intl
                .formatMessage({
                  id: `heatPumpConfig.general.${selectedIndoorPackageAndConstraint.domesticHotWaterTankSize}`,
                })
                .replace(
                  ' liters',
                  'L',
                )} ${intl.formatMessage({ id: `heatPumpConfig.indoorUnitType.${selectedIndoorPackageAndConstraint.indoorUnitType}` })}`,
              selectedOutdoorCapacity: intl.formatMessage({
                id: `heatPumpConfig.outdoorUnitCapacity.${selectedOutdoorUnitCapacity}`,
              }),
              allowedOutdoorUnits:
                selectedIndoorPackageAndConstraint?.capacityConstraintViolation.allowedOutdoorUnitCapacities
                  .map((capacity) => intl.formatMessage({ id: `heatPumpConfig.outdoorUnitCapacity.${capacity}` }))
                  .join(' or '),
            }}
          />
        </Typography>
      </Card>
    )
  );
}
