import { But<PERSON>, <PERSON>Label, Stack } from '@mui/material';
import { TooltipAira } from '@ui/components/Tooltip/Tooltip';
import { FormattedMessage, useIntl } from 'react-intl';
import { useHeatPumpStore } from 'components/quotation/stores/HeatPumpPackageStore';
import { useHeatPumpConfigStore } from './stores/configStore';
import { getSelectedHeatPumpPackages } from './utils/helpers';
import { IndoorUnitType } from './stores/types';
import { WarningMessage } from './components/WarningMessage';

export default function IndoorUnitTypeSelect() {
  const store = useHeatPumpConfigStore();
  const intl = useIntl();

  const selectedHeatPumpPackages = useHeatPumpStore((state) => state.selectedHeatPumpPackages);
  const { selectedIndoorPackageAndConstraint } = getSelectedHeatPumpPackages(selectedHeatPumpPackages);
  const selectedIndoorUnitType = selectedIndoorPackageAndConstraint?.indoorUnitType;
  const indoorUnitTypeMismatch =
    selectedIndoorUnitType !== undefined &&
    store.indoorPackage?.indoorUnitType !== undefined &&
    selectedIndoorUnitType !== store.indoorPackage?.indoorUnitType;

  if (store.outdoorUnitCapacity === undefined) {
    return null;
  }

  const updateIndoorUnitType = (unitType: IndoorUnitType) => {
    store.setIndoorUnitType(unitType);
    store.clearDomesticHotWaterTankSize();

    if (unitType === 'unitower') {
      switch (store.outdoorUnitCapacity) {
        case 'capacity6kw':
        case 'capacity8kw':
          store.setUnitowerTankSize('dhw100');
          break;
        case 'capacity12kw':
          store.setUnitowerTankSize('dhw250');
          break;
        default:
          break;
      }
    }
  };

  return (
    <>
      <FormLabel id="indoor-unit-type" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        {intl.formatMessage({
          id: 'heatPumpConfig.indoorUnitType.title',
        })}
        <TooltipAira title={<FormattedMessage id="heatPumpConfig.indoorUnitType.tooltip" />} />
      </FormLabel>
      <Stack direction="row" gap={2}>
        <Button
          variant={store.indoorPackage?.indoorUnitType === 'unitower' ? 'contained' : 'outlined'}
          style={{ width: '115px' }}
          onClick={() => updateIndoorUnitType('unitower')}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.indoorUnitType.unitower' })}
        </Button>
        <Button
          variant={store.indoorPackage?.indoorUnitType === 'hydrobox' ? 'contained' : 'outlined'}
          style={{ width: '115px' }}
          onClick={() => updateIndoorUnitType('hydrobox')}
        >
          {intl.formatMessage({ id: 'heatPumpConfig.indoorUnitType.hydrobox' })}
        </Button>
      </Stack>
      {indoorUnitTypeMismatch && (
        <WarningMessage
          messageId="heatPumpConfig.indoorUnitType.mismatch"
          values={{
            indoorUnitType: `(${intl.formatMessage({ id: `heatPumpConfig.indoorUnitType.${selectedIndoorUnitType}` })})`,
          }}
        />
      )}
    </>
  );
}
