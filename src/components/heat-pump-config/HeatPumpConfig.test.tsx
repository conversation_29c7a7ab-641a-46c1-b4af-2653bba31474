import React from 'react';
import { cleanup, fireEvent, RenderResult, waitFor } from '@testing-library/react';
import { IntlProvider } from 'react-intl';
import { SetupServer, setupServer } from 'msw/node';
import { mockRouterForHeatDesign, renderWithProviders, trpcMsw } from 'tests/utils/testUtils';
import { SolutionLoader } from 'components/quotation/sections/SolutionLoader';
import {
  OutdoorUnit,
  TankSize,
  ZoneId as ProtobufZoneId,
} from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.v1';
import { HeatDesign as ProtoHeatDesign } from '@aira/installation-groundwork-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.groundwork.heatdesign.v2';
import { Groundwork } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.groundwork.v1';
import { RequestHandler } from 'msw';
import userEvent from '@testing-library/user-event';
import { installationGroundwork } from 'tests/heat-loss/fixtures/asaTestData';
import { GroundworkContextProvider } from 'context/groundwork-context';
import { mocks } from 'tests/utils/mockedTrpcCalls';
import { vi } from 'vitest';
import HeatPumpConfig from './HeatPumpConfig';
import HeatPumpConfigLoader from './HeatPumpConfigLoader';
import { createGetHeatPumpParametersResponse, mockZone, products, solution } from './HeatPumpConfigTestData';
import { ClimateDataLoader } from 'components/heat-design/components/climate/ClimateDataLoader';
import { EMITTER_RADIATOR_SUPPLY_POINT } from './utils/configCalculator';
import untypedAsaHouse from '../../tests/heat-loss/asa_house_response.json';

const INSTALLATION_GROUNDWORK_ID = '0414f3f6-0610-4558-a76f-2fe2a027f017';
const SOLUTION_ID = '2d60e2f7-07b2-47a8-9894-a194c3c7d53b';

const BASE_SERVER = setupServer(
  mocks.getGroundworkForSolution.asa,
  mocks.getProducts.custom(products),
  trpcMsw.AiraBackend.getGrpcEnergySolution.query(() => solution),
  trpcMsw.InstallationGroundwork.sendHeatPumpParameters.mutation(() => ({})),
  mocks.getHeatPumpParameters.simple,
  trpcMsw.AiraBackend.getPostalCodeClimate.query(() => ({
    climate: {
      heatingDegreeDays: 2255,
      externalDesignTemperature: -3.2,
      averageExternalTemperature: 10.2,
    },
  })),
  trpcMsw.HeatLossCalculator.loadHeatDesign.query(() => ({
    heatDesign: untypedAsaHouse as unknown as ProtoHeatDesign,
    isLocked: false,
    result: undefined,
    events: [],
    updatedAt: new Date(),
  })),
);
const ZONE_ONE = 0;
const ZONE_TWO = 1;
const MUI_ERROR_CLASS = 'Mui-error';
const MUI_SELECTED_CLASS = 'MuiButton-contained';
const NUM_COOLING_CURVE_INPUTS = 3;

let heatPumpConfig: RenderResult;
const server: SetupServer = BASE_SERVER;

mockRouterForHeatDesign();
const setupTest = async (...extraHandlers: Array<RequestHandler>) => {
  server.use(...extraHandlers);

  heatPumpConfig = renderWithProviders(
    <IntlProvider locale="en-GB" onError={() => {}} defaultLocale="en-GB" messages={undefined}>
      <GroundworkContextProvider solutionId={SOLUTION_ID}>
        <SolutionLoader>
          <HeatPumpConfigLoader installationGroundworkId={INSTALLATION_GROUNDWORK_ID}>
            <ClimateDataLoader>
              <HeatPumpConfig energySolutionId={SOLUTION_ID} installationGroundworkId={INSTALLATION_GROUNDWORK_ID} />
            </ClimateDataLoader>
          </HeatPumpConfigLoader>
        </SolutionLoader>
      </GroundworkContextProvider>
    </IntlProvider>,
  );
  expect(await heatPumpConfig.findByText('heatPumpConfig.title')).toBeInTheDocument();
};
beforeAll(() => {
  server.listen();
});

afterEach(() => {
  cleanup();
  heatPumpConfig.unmount();
  // Removes only extra handlers added with .use() and not the default handlers
  server.resetHandlers();
});

beforeEach(() => {
  server.restoreHandlers();
});

afterAll(() => {
  server.close();
});

describe('The heat pump configuration loads as expected', () => {
  it('should render config without any saved parameters', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);
  });

  it('should prefill data from the server', async () => {
    const savedParametersResponse = createGetHeatPumpParametersResponse({
      tankSize: TankSize.TANK_SIZE_250_LITERS,
      outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_12KW,
      zones: [
        mockZone({
          id: ProtobufZoneId.ZONE_ID_1,
          thermostat: {
            thermostatType: {
              $case: 'wirelessThermostat',
              wirelessThermostat: {},
            },
          },
        }),
      ],
    });
    await setupTest(mocks.getHeatPumpParameters.custom(savedParametersResponse));

    // Features: "heating" should be selected
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-heating active/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-cooling inactive/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-both inactive/)).toBeInTheDocument();

    // Type of thermostat: "wireless" should be selected
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wireless active/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired inactive/)).toBeInTheDocument();

    // Emitter type: "radiator" should be selected
    expect(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-radiator active/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-underfloor inactive/)).toBeInTheDocument();

    // The correct zones configuration should be selected (single zone, single mixing valve)
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-oneZoneNoMixingValve active/),
    ).toBeInTheDocument();
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve inactive/),
    ).toBeInTheDocument();
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesTwoMixingValves inactive/),
    ).toBeInTheDocument();

    // In the mock test data, there is no saved outdoor design temperature or flow temperature, like it is for
    // historical heat pump configurations. In this situation, the heating curve should not show
    expect(heatPumpConfig.queryByTestId('zone-heating-curve')).toBeNull();
    expect((await heatPumpConfig.findByTestId('odt-test-id')).getElementsByTagName('input')[0]?.value).toBe('');
    expect((await heatPumpConfig.findByTestId('flowTemperature-test-id')).getElementsByTagName('input')[0]?.value).toBe(
      '',
    );
  });

  it('should prefill the odt, flow temperature and show the heating curve, if saved', async () => {
    await setupTest(
      mocks.getHeatPumpParameters.custom(
        createGetHeatPumpParametersResponse({
          zones: [
            mockZone({
              id: ProtobufZoneId.ZONE_ID_1,
              heating: {
                maximumSupplySetpoint: EMITTER_RADIATOR_SUPPLY_POINT,
                minHeatSupplyTemp: 10,
                p1Supply: 65,
                p2Supply: 63,
                p3Supply: 55,
                p4Supply: 46,
                p5Supply: 28,
                odt: 10.1,
                flowTemperature: 45.8,
              },
              thermostat: {
                thermostatType: {
                  $case: 'noThermostat',
                  noThermostat: {},
                },
              },
            }),
          ],
        }),
      ),
    );

    expect((await heatPumpConfig.findByTestId('odt-test-id')).getElementsByTagName('input')[0]).toHaveValue(10.1);
    expect((await heatPumpConfig.findByTestId('flowTemperature-test-id')).getElementsByTagName('input')[0]).toHaveValue(
      45.8,
    );
    expect(await heatPumpConfig.findByTestId('zone-heating-curve')).toBeVisible();
  });

  it('should prefill a two-zone, separate mixing valve configuration', async () => {
    await setupTest(
      mocks.getHeatPumpParameters.custom(
        createGetHeatPumpParametersResponse({
          zones: [
            mockZone({
              id: ProtobufZoneId.ZONE_ID_1,
            }),
            mockZone({
              id: ProtobufZoneId.ZONE_ID_2,
            }),
          ],
          numberOfMixingValves: 1,
        }),
      ),
    );

    // The correct zones configuration should be selected (2 zones, shared mixing valve)
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-oneZoneNoMixingValve inactive/),
    ).toBeInTheDocument();
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve active/),
    ).toBeInTheDocument();
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesTwoMixingValves inactive/),
    ).toBeInTheDocument();
  });

  it('should pre-select outdoor and tank if nothing saved', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);
    expect(
      await heatPumpConfig.findByRole('button', { name: 'heatPumpConfig.outdoorUnitCapacity.capacity12kw' }),
    ).toHaveClass(MUI_SELECTED_CLASS);

    expect(await heatPumpConfig.findByRole('button', { name: 'heatPumpConfig.general.dhw250' })).toHaveClass(
      MUI_SELECTED_CLASS,
    );

    // The warnings should not be visible
    expect(heatPumpConfig.queryByText('heatPumpConfig.outdoorUnitCapacity.capacityMismatch')).toBeNull();
    expect(heatPumpConfig.queryByText('heatPumpConfig.domesticHotWater.tankSizeMismatch')).toBeNull();
  });

  it('should warn about mismatching data', async () => {
    // Given the products in the solution are 12kW and 250L this will result in mismatching data
    const savedParametersResponse = createGetHeatPumpParametersResponse({
      tankSize: TankSize.TANK_SIZE_100_LITERS,
      outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_8KW,
    });
    await setupTest(mocks.getHeatPumpParameters.custom(savedParametersResponse));

    // The warnings should be visible
    expect(heatPumpConfig.getByText('heatPumpConfig.outdoorUnitCapacity.capacityMismatch')).toBeVisible();
    expect(heatPumpConfig.getByText('heatPumpConfig.domesticHotWater.tankSizeMismatch')).toBeVisible();
  });

  it('Add limits to the temperature values', async () => {
    const savedParametersResponse = createGetHeatPumpParametersResponse({
      tankSize: TankSize.TANK_SIZE_100_LITERS,
      outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_8KW,
    });
    await setupTest(mocks.getHeatPumpParameters.custom(savedParametersResponse));

    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve/));
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve active/),
    ).toBeInTheDocument();

    const zoneHeating = heatPumpConfig.getAllByTestId(/heat-pump-config-feature-choice-heating/);
    expect(zoneHeating.length > 0);
    zoneHeating.forEach((el) => fireEvent.click(el));

    // check the emitter types are visible
    expect(heatPumpConfig.getAllByText('heatPumpConfig.zone.emitterType.title')[ZONE_ONE]).toBeVisible();
    expect(heatPumpConfig.getAllByText('heatPumpConfig.zone.emitterType.title')[ZONE_TWO]).toBeVisible();

    // select the emitter type for both
    const emitterTypeRadiator = heatPumpConfig.getAllByTestId(/heat-pump-config-emitter-type-radiator/);
    emitterTypeRadiator.forEach((el) => fireEvent.click(el));

    let floorTempInput: HTMLElement[];
    floorTempInput = heatPumpConfig.getAllByTestId('flowTemperature-test-id').map((el) => el.querySelector('input')!);
    floorTempInput.forEach((el) => fireEvent.change(el, { target: { value: 70 } }));

    floorTempInput = heatPumpConfig
      .getAllByTestId('flowTemperature-test-id')
      .map((element) => element.querySelector('input')!);

    floorTempInput.forEach((el) => expect(el).toHaveValue(70));
    floorTempInput.forEach((el) => expect(el).toHaveAttribute('aria-invalid', 'true'));

    fireEvent.change(floorTempInput[ZONE_ONE]!, { target: { value: 65 } });

    floorTempInput = heatPumpConfig.getAllByTestId('flowTemperature-test-id').map((el) => el.querySelector('input')!);

    expect(floorTempInput[ZONE_ONE]!).toHaveValue(65);
    expect(floorTempInput[ZONE_ONE]!).toHaveAttribute('aria-invalid', 'false');

    expect(floorTempInput[ZONE_TWO]!).toHaveValue(70);
    expect(floorTempInput[ZONE_TWO]!).toHaveAttribute('aria-invalid', 'true');
    expect(heatPumpConfig.getByText('heatPumpConfig.zone.flowTemperature.warning')).toBeVisible();

    fireEvent.change(floorTempInput[ZONE_TWO]!, { target: { value: 65 } });
    expect(floorTempInput[ZONE_TWO]!).toHaveValue(65);
    expect(floorTempInput[ZONE_TWO]!).toHaveAttribute('aria-invalid', 'false');
  });
});

describe('The heat pump configuration can be interacted with', () => {
  it('should allow switching to a two-zone, shared mixing valve configuration', async () => {
    await setupTest(mocks.getHeatPumpParameters.simple);

    await userEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesTwoMixingValves/));
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesTwoMixingValves active/),
    ).toBeInTheDocument();

    // There should be 2 zones titled "Zone 1" and "Zone 2", aka 2 H2 elements
    expect(heatPumpConfig.getAllByText('heatPumpConfig.zone.title', { selector: 'h2' }).length).toBe(2);
  });

  it('should pre-select outdoor and tank if no mismatch', async () => {
    const savedParametersResponse = createGetHeatPumpParametersResponse({
      tankSize: TankSize.TANK_SIZE_250_LITERS,
      outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_12KW,
    });
    await setupTest(
      trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() => ({ parameters: savedParametersResponse })),
    );

    expect(
      await heatPumpConfig.findByRole('button', { name: 'heatPumpConfig.outdoorUnitCapacity.capacity12kw' }),
    ).toHaveClass(MUI_SELECTED_CLASS);

    expect(await heatPumpConfig.findByRole('button', { name: 'heatPumpConfig.general.dhw250' })).toHaveClass(
      MUI_SELECTED_CLASS,
    );

    // The warnings should not be visible
    expect(heatPumpConfig.queryByText('heatPumpConfig.outdoorUnitCapacity.capacityMismatch')).toBeNull();
    expect(heatPumpConfig.queryByText('heatPumpConfig.domesticHotWater.tankSizeMismatch')).toBeNull();
  });
});

describe('Zone config', () => {
  it('restrict zone 2 thermostat options based on those selected for zone 1', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);

    // Wireless thermostat should be selected by default
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wireless active/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired inactive/)).toBeInTheDocument();

    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve/));
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve active/),
    ).toBeInTheDocument();
    expect(heatPumpConfig.getAllByText('heatPumpConfig.zone.title').length).toBe(4);
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wireless active')).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wired inactive')).toBeInTheDocument();
    // Set zone 1's thermostat to wired and check zone 2's options
    await userEvent.click(heatPumpConfig.getByTestId(`zone-1 heat-pump-config-thermostat-type-wired inactive`));
    // 'wired' should be checked since we have selected it in zone 1
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wireless inactive')).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wired active')).toBeInTheDocument();

    // if we select wireless for zone 1, zone 2 should also have wireless selected
    await userEvent.click(heatPumpConfig.getByTestId(`zone-1 heat-pump-config-thermostat-type-wireless inactive`));
    // 'wired' should be checked since we have selected it in zone 1
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wireless active')).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId('zone-2 heat-pump-config-thermostat-type-wired inactive')).toBeInTheDocument();
  });

  it('should show a warning if the inputs for heating curve is invalid', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);

    // First select heating (for zone 1)
    const zoneOneHeating = heatPumpConfig.getByTestId(/zone-1 heat-pump-config-feature-choice-heating/);
    await userEvent.click(zoneOneHeating);
    expect(await heatPumpConfig.findByText('heatPumpConfig.zone.heatingCurveTitle')).toBeVisible();

    // Then select the emitter type: radiator
    await userEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-radiator/));
    expect(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-radiator active/)).toBeInTheDocument();

    // Ensure the heat curve inputs are showing a warning about their input being missing
    expect(
      heatPumpConfig.getByLabelText('heatPumpConfig.zone.outdoorDesignTemperature.title').parentElement,
    ).toHaveClass(MUI_ERROR_CLASS);
    await waitFor(() => {
      expect(heatPumpConfig.getByLabelText('heatPumpConfig.zone.flowTemperature.title').parentElement).toHaveClass(
        MUI_ERROR_CLASS,
      );
    });

    // Then supply the heat curve inputs
    fireEvent.input(heatPumpConfig.getByLabelText('heatPumpConfig.zone.outdoorDesignTemperature.title'), {
      target: { value: '-3' },
    });
    fireEvent.input(heatPumpConfig.getByLabelText('heatPumpConfig.zone.flowTemperature.title'), {
      target: { value: '55' },
    });

    // Ensure the missing input warning is gone
    expect(
      heatPumpConfig.getByLabelText('heatPumpConfig.zone.outdoorDesignTemperature.title').parentElement,
    ).not.toHaveClass(MUI_ERROR_CLASS);
    expect(heatPumpConfig.getByLabelText('heatPumpConfig.zone.flowTemperature.title').parentElement).not.toHaveClass(
      MUI_ERROR_CLASS,
    );

    // And ensure the graph is visible
    expect(heatPumpConfig.getByTestId('zone-heating-curve')).toBeVisible();
  });

  it('should show a warning if the inputs for cooling curve is invalid', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);

    // First select cooling (for zone 1)
    const zoneOneHeating = heatPumpConfig.getByTestId(/zone-1 heat-pump-config-feature-choice-cooling/);
    await userEvent.click(zoneOneHeating);
    expect(await heatPumpConfig.findByText('heatPumpConfig.zone.coolingCurveTitle')).toBeVisible();

    // Ensure there is no missing input warning as we have default values
    for (let i = 1; i < NUM_COOLING_CURVE_INPUTS; i += 1) {
      expect(heatPumpConfig.getByTestId(`cooling-graph-input-${i}`).parentElement).not.toHaveClass(MUI_ERROR_CLASS);
    }

    // Then empty each curve input and check they show a warning
    for (let i = 1; i < NUM_COOLING_CURVE_INPUTS; i += 1) {
      fireEvent.input(heatPumpConfig.getByTestId(`cooling-graph-input-${i}`), {
        target: { value: '' },
      });
      expect(heatPumpConfig.getByTestId(`cooling-graph-input-${i}`).parentElement).toHaveClass(MUI_ERROR_CLASS);
    }

    // And ensure the graph is still visible
    expect(heatPumpConfig.getByTestId('zone-cooling-curve')).toBeVisible();
  });

  it('should show a warning if the calculated heat curve is invalid', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);

    // First select heating (for zone 1)
    const zoneOneHeating = heatPumpConfig.getByTestId(/zone-1 heat-pump-config-feature-choice-heating/);
    await userEvent.click(zoneOneHeating);
    expect(await heatPumpConfig.findByText('heatPumpConfig.zone.heatingCurveTitle')).toBeVisible();

    // Then select the emitter type: radiator
    await userEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-radiator/));
    expect(heatPumpConfig.getByTestId(/heat-pump-config-emitter-type-radiator active/)).toBeInTheDocument();

    // Then supply the heat curve inputs
    fireEvent.input(heatPumpConfig.getByLabelText('heatPumpConfig.zone.outdoorDesignTemperature.title'), {
      target: { value: '21' },
    });
    fireEvent.input(heatPumpConfig.getByLabelText('heatPumpConfig.zone.flowTemperature.title'), {
      target: { value: '45' },
    });

    // Ensure the there is no warning on the inputs
    expect(
      heatPumpConfig.getByLabelText('heatPumpConfig.zone.outdoorDesignTemperature.title').parentElement,
    ).not.toHaveClass(MUI_ERROR_CLASS);
    expect(heatPumpConfig.getByLabelText('heatPumpConfig.zone.flowTemperature.title').parentElement).not.toHaveClass(
      MUI_ERROR_CLASS,
    );

    // And ensure the graph is visible
    expect(heatPumpConfig.getByTestId('zone-heating-curve')).toBeVisible();

    // But ensure there is a warning about the calculated curve
    expect(heatPumpConfig.getByText('heatPumpConfig.zone.graph.heatingCurveInvalid')).toBeVisible();
  });

  it('should show a warning if the calculated cooling curve is invalid', async () => {
    await setupTest(mocks.getHeatPumpParameters.empty);

    // First select cooling (for zone 1)
    const zoneOneHeating = heatPumpConfig.getByTestId(/zone-1 heat-pump-config-feature-choice-cooling/);
    await userEvent.click(zoneOneHeating);
    expect(await heatPumpConfig.findByText('heatPumpConfig.zone.coolingCurveTitle')).toBeVisible();

    // Ensure there is no missing input warning as we have default values
    for (let i = 1; i < NUM_COOLING_CURVE_INPUTS; i += 1) {
      expect(heatPumpConfig.getByTestId(`cooling-graph-input-${i}`).parentElement).not.toHaveClass(MUI_ERROR_CLASS);
    }

    // Set an invalid curve
    fireEvent.input(heatPumpConfig.getByTestId('cooling-graph-input-0'), {
      target: { value: '10' },
    });
    fireEvent.input(heatPumpConfig.getByTestId('cooling-graph-input-1'), {
      target: { value: '15' },
    });
    fireEvent.input(heatPumpConfig.getByTestId('cooling-graph-input-2'), {
      target: { value: '5' },
    });

    // And ensure the graph is visible
    expect(heatPumpConfig.getByTestId('zone-cooling-curve')).toBeVisible();

    // But ensure there is a warning about the calculated curve
    expect(heatPumpConfig.getByText('heatPumpConfig.zone.graph.coolingCurveInvalid')).toBeVisible();
  });
});

describe('Saving should be disabled if the config has not changed', () => {
  it('should enable the save button if the config has changed', async () => {
    const savedParametersResponse = createGetHeatPumpParametersResponse({
      tankSize: TankSize.TANK_SIZE_250_LITERS,
      outdoorUnit: OutdoorUnit.OUTDOOR_UNIT_AIRA_12KW,
    });
    await setupTest(
      trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() =>
        Promise.resolve({
          parameters: savedParametersResponse,
        }),
      ),
    );
    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).toBeDisabled();

    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve/));
    expect(
      heatPumpConfig.getByTestId(/heat-pump-config-zones-configuration-twoZonesOneMixingValve active/),
    ).toBeInTheDocument();

    // Change zone values
    let coolingFeature = heatPumpConfig.getAllByTestId(/heat-pump-config-feature-choice-cooling/);
    coolingFeature.forEach((el) => fireEvent.click(el));
    coolingFeature = heatPumpConfig.getAllByTestId(/heat-pump-config-feature-choice-cooling active/);
    expect(coolingFeature.length > 0);
    coolingFeature.forEach((el) => expect(el).toBeInTheDocument());

    // Type of thermostat
    const thermostatTypes = heatPumpConfig.getAllByTestId(/heat-pump-config-thermostat-type-wired/);
    thermostatTypes.forEach((el) => fireEvent.click(el));
    const activeThermostatTypes = heatPumpConfig.getAllByTestId(/heat-pump-config-thermostat-type-wired active/);
    expect(activeThermostatTypes.length > 0);
    expect(activeThermostatTypes.forEach((type) => expect(type).toBeInTheDocument()));
    // now the save button should be enabled
    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).not.toBeDisabled();

    // save the config
    fireEvent.click(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' }));

    // check if the button is now disabled after saving
    await waitFor(() => {
      expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).toBeDisabled();
    });
  });
  it('when you load the project the button should be disabled', async () => {
    await setupTest(
      trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() =>
        Promise.resolve({
          parameters: createGetHeatPumpParametersResponse(),
        }),
      ),
    );
    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).toBeDisabled();
  });
  it('when you change the value of the zone the button should be enabled', async () => {
    await setupTest(
      trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() =>
        Promise.resolve({
          parameters: createGetHeatPumpParametersResponse(),
        }),
      ),
    );
    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).toBeDisabled();

    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-heating active/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-cooling inactive/)).toBeInTheDocument();

    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wireless inactive/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired inactive/)).toBeInTheDocument();

    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired/));
    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-cooling/)); // change the value of the zone
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-heating inactive/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-cooling active/)).toBeInTheDocument();

    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wireless inactive/)).toBeInTheDocument();
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired active/)).toBeInTheDocument();

    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).not.toBeDisabled();
  });
});

describe('after saving the changes', () => {
  it('should still be shown', async () => {
    const mockMutateAsync = vi.fn();

    const heatPumpParametersResponse = createGetHeatPumpParametersResponse();

    await setupTest(
      trpcMsw.InstallationGroundwork.getHeatPumpParameters.query(() =>
        Promise.resolve({
          parameters: heatPumpParametersResponse,
        }),
      ),
      trpcMsw.InstallationGroundwork.sendHeatPumpParameters.mutation(() => {
        mockMutateAsync(); // This will track if the API call is made
        return {};
      }),
    );

    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired inactive/)).toBeInTheDocument();
    // Here we make the change
    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired/));
    heatPumpParametersResponse.parameters!.zones[0]!.thermostat!.thermostatType = {
      $case: 'wiredThermostat',
      wiredThermostat: {
        indoorUnitRoomTemperatureZone1Source: 1,
        indoorUnitRoomTemperatureZone2Source: 2,
      },
    };
    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired active/)).toBeInTheDocument();
    fireEvent.click(heatPumpConfig.getByTestId(/heat-pump-config-feature-choice-cooling/)); // necessary to enable the save button
    expect(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' })).not.toBeDisabled();

    fireEvent.click(heatPumpConfig.getByRole('button', { name: 'heatPumpConfig.saveButtonTitle' }));

    await waitFor(() => expect(mockMutateAsync).toHaveBeenCalledTimes(1));

    expect(heatPumpConfig.getByTestId(/heat-pump-config-thermostat-type-wired active/)).toBeInTheDocument();
  });
});

describe('Silent night mode visibility', () => {
  it('should not display silent night mode if country is not Germany', async () => {
    const ukGroundwork = {
      ...installationGroundwork,
      location: {
        ...installationGroundwork.location,
        exactAddress: {
          $case: 'exactAddress',
          formattedAddress: 'Redacted Address, TOWN, UNITED KINGDOM',
          postalCode: 'CT14 9LS',
          geometry: {
            long: 1.370979,
            lat: 51.214917,
          },
          iso3166: {
            $case: 'country',
            country: 3,
          },
        },
      },
    };

    await setupTest(
      trpcMsw.AiraBackend.getGroundworkForSolution.query(() => Promise.resolve(ukGroundwork as Groundwork)),
    );

    expect(heatPumpConfig.queryByText('heatPumpConfig.silent.mode')).toBeNull();
  });

  it('should display silent night mode if the country is Germany', async () => {
    const germanGroundwork: Groundwork = {
      ...installationGroundwork,
      location: {
        $case: 'exactAddress',
        exactAddress: {
          formattedAddress: 'Bernauer Str. 111, 13355 Berlin, Germany',
          postalCode: '13355',
          geometry: {
            long: 13.39019,
            lat: 52.535052,
          },
          placeId: 'ChIJZ0KxF_JRqEcRrLHB-4r-U-o',
          identifier: {},
          iso3166: {
            $case: 'country',
            country: 2,
          },
        },
      },
    };

    await setupTest(trpcMsw.AiraBackend.getGroundworkForSolution.query(() => Promise.resolve(germanGroundwork)));

    expect(await heatPumpConfig.findByText('heatPumpConfig.silent.mode')).toBeInTheDocument();

    expect(await heatPumpConfig.findByRole('button', { name: 'common.no' })).toHaveClass(MUI_SELECTED_CLASS);
    expect(await heatPumpConfig.findByRole('button', { name: 'common.yes' })).not.toHaveClass(MUI_SELECTED_CLASS);
  });
});
