import { FormControl, FormGroup, InputLabel, OutlinedInput, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { Heading } from '@ui/components/Heading/Heading';
import { LabelTooltip } from '@ui/components/Tooltip/Tooltip';
import { getAdjustedOutdoorDesignTemperatureCelsius } from 'components/heat-design/utils/calculations';
import { toLocalisedDecimalPlaces } from 'components/heat-design/utils/helpers';
import { propertyIsValid } from 'components/heat-design/Validator';
import { useRouter } from 'next/router';
import { ChangeEvent } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { api } from 'utils/api';
import { StyledFormattedMessage } from 'utils/localization';
import { useHeatPumpConfigStore } from './stores/configStore';
import {
  EmitterType,
  FEATURE_CHOICES,
  hasCooling,
  hasHeating,
  THERMOSTAT_TYPES,
  ThermostatType,
  ZoneId,
} from './stores/types';
import {
  coolingCurveGraphData,
  flowTempIsValid,
  heatCurveGraphData,
  MAX_SUPPLY_TEMPERATURES,
} from './utils/configCalculator';
import { zoneCoolingCurveIsValid, zoneHeatingCurveIsValid } from './utils/helpers';
import { ZoneCoolingGraph, ZoneHeatingGraph } from './ZoneGraphs';

export interface ZoneConfigProps {
  zoneId: ZoneId;
  energySolutionId: string;
  installationGroundworkId: string;
}

function zoneTwoAllowedThermostatTypes(zoneOneThermostatType: ThermostatType): {
  allowedTypes: (ThermostatType | undefined)[];
  defaultType: ThermostatType | undefined;
} {
  switch (zoneOneThermostatType) {
    case 'wired':
      return { allowedTypes: ['wired'], defaultType: 'wired' };
    case 'wireless':
      return { allowedTypes: ['wireless'], defaultType: 'wireless' };
    default:
      return { allowedTypes: ['wired', 'wireless'], defaultType: 'wireless' };
  }
}

/**
 * The thermostat options for Zone 2 depend on those for Zone 1.
 */
function isThermostatOptionEnabled(
  currentZone: ZoneId,
  currentThermostatType: ThermostatType,
  zoneOneThermostatType?: ThermostatType,
): boolean {
  if (zoneOneThermostatType != null && currentZone === ZoneId.TWO) {
    return zoneTwoAllowedThermostatTypes(zoneOneThermostatType).allowedTypes.includes(currentThermostatType);
  }

  return true;
}

export function ZoneConfig({ zoneId, energySolutionId, installationGroundworkId }: ZoneConfigProps) {
  const intl = useIntl();
  const { locale } = useRouter();
  const zone = useHeatPumpConfigStore((s) => s.zones[zoneId]);
  const zoneOne = useHeatPumpConfigStore((s) => s.zones[ZoneId.ONE]);
  const zoneTwo = useHeatPumpConfigStore((s) => s.zones[ZoneId.TWO]);
  const setZone = useHeatPumpConfigStore((s) => s.setZone);

  // Explicitly get the saved heat design, not the heat design that might have been modified in a store
  const heatDesign = api.HeatLossCalculator.loadHeatDesign.useQuery({
    energySolutionId,
    installationGroundworkId,
    demoData: false,
  }).data?.heatDesign;

  const flowTemperature = heatDesign?.dwelling?.systemDesign?.flowTemperatureCelsius;

  const baseOutdoorDesignTemperature = heatDesign?.climate?.baseOutdoorDesignTemperatureCelsius;
  const isDwellingInExposedLocation = heatDesign?.dwelling?.exposedLocation;
  const temperatureCompensation = heatDesign?.dwelling?.temperatureAdjustmentCelsius;
  const adjustedOutdoorDesignTemperature =
    baseOutdoorDesignTemperature !== undefined &&
    isDwellingInExposedLocation !== undefined &&
    temperatureCompensation !== undefined
      ? getAdjustedOutdoorDesignTemperatureCelsius({
          baseOutdoorDesignTemperature,
          isDwellingInExposedLocation,
          temperatureCompensation,
        })
      : undefined;
  const formattedAdjustedOutdoorDesignTemperature =
    adjustedOutdoorDesignTemperature === undefined
      ? undefined
      : toLocalisedDecimalPlaces({ num: adjustedOutdoorDesignTemperature, decimalPlaces: 1, locale: locale });

  if (zone === undefined) {
    return null;
  }

  const heatingCurve =
    zone?.outdoorDesignTemperature !== undefined &&
    zone?.flowTemperature !== undefined &&
    zone?.emitterType !== undefined
      ? heatCurveGraphData(zone.outdoorDesignTemperature, zone.flowTemperature, zone.emitterType)
      : undefined;

  const coolingCurve =
    zone?.coolingCurveTargetFlowTemperatures !== undefined
      ? coolingCurveGraphData(zone.coolingCurveTargetFlowTemperatures)
      : undefined;

  const isFlowTemperatureGreaterThanZoneOne =
    zoneTwo?.flowTemperature !== undefined &&
    zoneOne?.flowTemperature !== undefined &&
    zoneTwo.flowTemperature > zoneOne.flowTemperature &&
    zoneOne?.emitterType === zone?.emitterType &&
    zoneId === ZoneId.TWO; // only show warning if emitter type is the same

  const updateThermostat = (thermostatType: ThermostatType) => {
    setZone(zoneId, { thermostatType });
    if (zone != null && zoneId === ZoneId.ONE) {
      if (zoneTwo !== undefined) {
        const { allowedTypes, defaultType } = zoneTwoAllowedThermostatTypes(thermostatType);
        // If zone 2's thermostat type is not allowed, switch it to a valid one
        if (!allowedTypes.includes(zoneTwo.thermostatType)) {
          setZone(ZoneId.TWO, { thermostatType: defaultType });
        }
      }
    }
  };

  if (!zone) {
    return null;
  }

  return (
    <>
      <Heading level={2} variant="headline2">
        {intl.formatMessage({ id: 'heatPumpConfig.zone.title' }, { zone: zoneId })}
      </Heading>

      <FormControl>
        <Typography fontWeight="500" id="features-label">
          {intl.formatMessage({ id: 'heatPumpConfig.zone.features.title' })}
        </Typography>
        <Stack direction="row" gap={2} sx={{ mt: 2 }}>
          {FEATURE_CHOICES.map((choice) => (
            <Button
              key={choice}
              fullWidth
              data-testid={`zone-${zoneId} heat-pump-config-feature-choice-${choice} ${zone.featureChoice === choice ? 'active' : 'inactive'}`}
              variant={zone.featureChoice === choice ? 'contained' : 'outlined'}
              onClick={() => setZone(zoneId, { featureChoice: choice })}
            >
              <FormattedMessage values={{ br: <br /> }} id={`heatPumpConfig.zone.features.${choice}`} />
            </Button>
          ))}
        </Stack>
      </FormControl>

      <FormControl>
        <Typography fontWeight="500" id="thermostat-type-label" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          {intl.formatMessage({ id: 'heatPumpConfig.zone.thermostatType.title' })}
        </Typography>
        <Stack direction="row" gap={2} sx={{ mt: 2 }}>
          {THERMOSTAT_TYPES.map((type) => (
            <Button
              key={type}
              fullWidth
              data-testid={`zone-${zoneId} heat-pump-config-thermostat-type-${type} ${zone.thermostatType === type ? 'active' : 'inactive'}`}
              variant={zone.thermostatType === type ? 'contained' : 'outlined'}
              onClick={() => updateThermostat(type)}
              disabled={!isThermostatOptionEnabled(zoneId, type, zoneOne?.thermostatType)}
            >
              <FormattedMessage values={{ br: <br /> }} id={`heatPumpConfig.zone.thermostatType.${type}`} />
            </Button>
          ))}
        </Stack>
        {zoneOne?.thermostatType !== undefined && zoneId === ZoneId.TWO && (
          <Typography variant="body2" mt={1}>
            {intl.formatMessage({ id: 'heatPumpConfig.zone.thermostatType.helperText' })}
          </Typography>
        )}
      </FormControl>

      {hasHeating(zone) && (
        <FormControl>
          <Typography fontWeight="500" id="emitter-type-label" sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            {intl.formatMessage({ id: 'heatPumpConfig.zone.emitterType.title' })}
          </Typography>
          <Stack direction="row" gap={2} sx={{ mt: 2 }}>
            {Object.values(EmitterType).map((type) => (
              <Button
                key={type}
                data-testid={`zone-${zoneId} heat-pump-config-emitter-type-${type} ${zone.emitterType === type ? 'active' : 'inactive'}`}
                fullWidth
                variant={zone.emitterType === type ? 'contained' : 'outlined'}
                onClick={() => setZone(zoneId, { emitterType: type })}
              >
                <FormattedMessage values={{ br: <br /> }} id={`heatPumpConfig.zone.emitterType.${type}`} />
              </Button>
            ))}
          </Stack>
        </FormControl>
      )}

      {hasHeating(zone) && (
        <>
          <Heading level={3} variant="headline3">
            {intl.formatMessage({ id: 'heatPumpConfig.zone.heatingCurveTitle' }, { zone: zoneId })}
          </Heading>

          <Stack direction="row" spacing={2}>
            <FormGroup sx={{ width: '50%' }}>
              <InputLabel sx={{ minHeight: '24px' }} htmlFor="outdoorDesignTemperature">
                <Typography variant="inputLabel">
                  {intl.formatMessage({ id: 'heatPumpConfig.zone.outdoorDesignTemperature.title' })}
                </Typography>
              </InputLabel>
              <OutlinedInput
                id="outdoorDesignTemperature"
                data-testid="odt-test-id"
                value={zone.outdoorDesignTemperature ?? ''}
                placeholder={intl.formatMessage({ id: 'heatPumpConfig.zone.temperaturePlaceholder' })}
                type="number"
                error={
                  !propertyIsValid(
                    'heatPumpConfigZone',
                    'outdoorDesignTemperature',
                    zone.outdoorDesignTemperature ?? -Infinity,
                  )
                }
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  const trimmed = e.target.value.trim();
                  if (trimmed === '' || Number.isNaN(Number(trimmed))) {
                    setZone(zoneId, { outdoorDesignTemperature: undefined });
                  } else {
                    setZone(zoneId, { outdoorDesignTemperature: Number(trimmed) });
                  }
                }}
              />
              <Typography variant="body2" mt={1}>
                <StyledFormattedMessage
                  id="heatPumpConfig.zone.outdoorDesignTemperature.subtitle"
                  values={{ heatDesignOdt: formattedAdjustedOutdoorDesignTemperature }}
                />
              </Typography>
            </FormGroup>

            <FormGroup sx={{ width: '50%' }}>
              <InputLabel htmlFor="flowTemperature">
                <LabelTooltip
                  label={
                    <Typography variant="inputLabel">
                      {intl.formatMessage({ id: 'heatPumpConfig.zone.flowTemperature.title' })}
                    </Typography>
                  }
                  tooltipLabel={<FormattedMessage id="heatPumpConfig.zone.flowTemperature.tooltip" />}
                />
              </InputLabel>
              <OutlinedInput
                id="flowTemperature"
                data-testid="flowTemperature-test-id"
                value={zone.flowTemperature ?? ''}
                placeholder={intl.formatMessage({ id: 'heatPumpConfig.zone.temperaturePlaceholder' })}
                type="number"
                error={!flowTempIsValid(zone.flowTemperature, zone.emitterType) || isFlowTemperatureGreaterThanZoneOne}
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  const trimmed = e.target.value.trim();
                  if (trimmed === '' || Number.isNaN(Number(trimmed))) {
                    setZone(zoneId, { flowTemperature: undefined });
                  } else {
                    setZone(zoneId, { flowTemperature: Number(trimmed) });
                  }
                }}
              />
              <Typography variant="body2" mt={1}>
                {isFlowTemperatureGreaterThanZoneOne ? (
                  <StyledFormattedMessage id="heatPumpConfig.zone.flowTemperature.warning" />
                ) : (
                  <StyledFormattedMessage
                    id="heatPumpConfig.zone.flowTemperature.subtitle"
                    values={{ heatDesignFlowTemperature: flowTemperature }}
                  />
                )}
              </Typography>
            </FormGroup>
          </Stack>

          {heatingCurve &&
            zone.flowTemperature !== undefined &&
            zone.outdoorDesignTemperature !== undefined &&
            zone.emitterType && (
              <ZoneHeatingGraph
                curve={heatingCurve}
                flowTemperature={zone.flowTemperature}
                outdoorDesignTemperature={zone.outdoorDesignTemperature}
                maxSupplyTemperature={MAX_SUPPLY_TEMPERATURES[zone.emitterType].heatingCurve}
                isValid={zoneHeatingCurveIsValid(zone)}
              />
            )}
        </>
      )}

      {hasCooling(zone) && coolingCurve && (
        <>
          <Heading level={3} variant="headline3">
            {intl.formatMessage({ id: 'heatPumpConfig.zone.coolingCurveTitle' }, { zone: zoneId })}
          </Heading>

          <ZoneCoolingGraph curve={coolingCurve} zoneId={zoneId} isValid={zoneCoolingCurveIsValid(zone)} />
        </>
      )}
    </>
  );
}
