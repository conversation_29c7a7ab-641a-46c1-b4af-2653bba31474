import { FormLabel, Stack, Typography } from '@mui/material';
import { Button } from '@ui/components/Button/Button';
import { LabelTooltip } from '@ui/components/Tooltip/Tooltip';
import { FormattedMessage } from 'react-intl';
import { useHeatPumpConfigStore } from './stores/configStore';
import { type AdditionalElectricHeating as AdditionalElectricHeatingType } from './stores/types';

const OPTIONS: AdditionalElectricHeatingType[] = ['0kwHeating3kwBackup', '3kwHeating3kwBackup', '6kwHeating3kwBackup'];

export default function AdditionalElectricHeating() {
  const additionalElectricHeating = useHeatPumpConfigStore((s) => s.additionalElectricHeating);
  const setAdditionalElectricHeating = useHeatPumpConfigStore((s) => s.setAdditionalElectricHeating);
  const showWarning =
    additionalElectricHeating === '3kwHeating3kwBackup' || additionalElectricHeating === '6kwHeating3kwBackup';

  return (
    <>
      <LabelTooltip
        label={
          <FormLabel id="additional-heating">
            <FormattedMessage id="heatPumpConfig.advancedSettings.heating.additionalElectricHeating" />
          </FormLabel>
        }
        tooltipLabel={
          <Typography color="white" variant="body2">
            <FormattedMessage id="heatPumpConfig.advancedSettings.heating.additionalElectricHeating.tooltip" />
          </Typography>
        }
      />
      <Stack direction="row" gap={2}>
        {OPTIONS.map((value) => (
          <Button
            key={value}
            fullWidth
            variant={additionalElectricHeating === value ? 'contained' : 'outlined'}
            onClick={() => setAdditionalElectricHeating(value)}
          >
            <FormattedMessage
              values={{ br: <br /> }}
              id={`heatPumpConfig.advancedSettings.heating.additionalElectricHeating.${value}`}
            />
          </Button>
        ))}
      </Stack>
      {showWarning && (
        <Typography variant="body2">
          <FormattedMessage id="heatPumpConfig.advancedSettings.heating.additionalElectricHeating.bridgeWarning" />
        </Typography>
      )}
    </>
  );
}
