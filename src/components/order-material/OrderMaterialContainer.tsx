import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { OrderMaterialsTable } from './OrderMaterialsTable';
import { api } from 'utils/api';
import { useRegionContext } from 'context/RegionContext';
import { useState } from 'react';
import { Button } from '@ui/components/Button/Button';

export enum Tab {
  NOT_ORDERED = 'Not Ordered',
  ORDERED = 'Ordered',
  UPDATED = 'Updated',
}

export default function OrderMaterialContainer() {
  const { id: regionId } = useRegionContext();
  const [selectedTab, setSelectedTab] = useState<Tab>(Tab.NOT_ORDERED);

  const { data: teams } = api.Resource.getTeams.useQuery(
    {
      regionId: regionId!.value,
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  if (!teams) {
    return null;
  }

  return (
    <Stack
      direction="column"
      spacing={2}
      alignItems="flex-start"
      justifyContent="flex-start"
      p={8}
      sx={{ maxHeight: '100vh', overflow: 'auto', mx: 'auto', width: '100vw' }}
    >
      <Typography variant="headline2">Order Materials</Typography>
      <Stack direction="row" spacing={2}>
        <Button
          variant={selectedTab === Tab.NOT_ORDERED ? 'contained' : 'outlined'}
          onClick={() => setSelectedTab(Tab.NOT_ORDERED)}
          size="small"
        >
          Not Ordered
        </Button>
        <Button
          variant={selectedTab === Tab.ORDERED ? 'contained' : 'outlined'}
          onClick={() => setSelectedTab(Tab.ORDERED)}
          size="small"
        >
          Ordered
        </Button>
        <Button
          variant={selectedTab === Tab.UPDATED ? 'contained' : 'outlined'}
          onClick={() => setSelectedTab(Tab.UPDATED)}
          size="small"
        >
          Updated
        </Button>
      </Stack>

      <OrderMaterialsTable teams={teams} selectedTab={selectedTab} />
    </Stack>
  );
}
