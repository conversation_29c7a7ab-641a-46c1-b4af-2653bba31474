import { Typography, Stack } from '@mui/material';
import { HeatPump } from '@ui/components/HeatPump/HeatPump';
import { FormattedMessage } from 'react-intl';
import { api } from 'utils/api';
import { createContext, useContext, useMemo } from 'react';
import { FullInstallationProjectEntity } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.api.gateway.installation.project.v1';
import { useRegionContext } from '../../../context/RegionContext';
import { ProcuredBomWithExternalReferences } from '@aira/bill-of-materials-grpc-api/build/ts_out/com/aira/acquisition/contract/bill/of/materials/v1/model';
import { InstallationProjectStage } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';

interface AllUncompletedProjectsContextType {
  allUncompletedProjects: (FullInstallationProjectEntity & {
    procuredBom: ProcuredBomWithExternalReferences;
  })[];
  isLoadingAllUncompletedProjects: boolean;
}

const AllUncompletedProjectsContext = createContext<AllUncompletedProjectsContextType | undefined>(undefined);

export function AllUncompletedProjectsProvider({ children }: { children: React.ReactNode }) {
  const { id: regionId } = useRegionContext();

  const { data: projects, isLoading: isLoadingProjects } = api.InstallationProject.getInstallationProjects.useQuery(
    {
      operationalUnitId: regionId!.value,
    },
    {
      refetchOnWindowFocus: false,
      trpc: {
        context: {
          skipBatch: true,
        },
      },
      select: (data) => {
        return data.filter(
          (project) =>
            project.installationProject?.stage !==
            InstallationProjectStage.INSTALLATION_PROJECT_STAGE_POST_INSTALLATION,
        );
      },
    },
  );

  const { data: procuredBoms, isLoading: isLoadingProcuredBoms } =
    api.BillOfMaterials.loadProcuredBomsByInstallationGroundworkIds.useQuery(
      {
        installationGroundworkIds: projects
          ?.map((project) => project.energySolution?.groundworkId?.value)
          .filter(Boolean) as string[],
      },
      {
        enabled: !!projects,
        select: (data) =>
          data.procuredBoms.reduce(
            (acc, procuredBom) => {
              if (procuredBom.installationGroundworkId) {
                acc[procuredBom.installationGroundworkId.value] = procuredBom;
              }
              return acc;
            },
            {} as Record<string, ProcuredBomWithExternalReferences>,
          ),
      },
    );

  const allUncompletedProjects = useMemo(() => {
    return projects
      ?.map((project) => {
        if (!project.energySolution?.groundworkId) {
          return null;
        }
        return {
          ...project,
          procuredBom: procuredBoms?.[project.energySolution?.groundworkId?.value],
        };
      })
      .filter(Boolean) as (FullInstallationProjectEntity & { procuredBom: ProcuredBomWithExternalReferences })[];
  }, [projects, procuredBoms]);

  const value = useMemo(
    () => ({
      allUncompletedProjects,
      isLoadingAllUncompletedProjects: isLoadingProjects || isLoadingProcuredBoms,
    }),
    [allUncompletedProjects, isLoadingProjects, isLoadingProcuredBoms],
  );

  if (!isLoadingProjects && !isLoadingProcuredBoms && !allUncompletedProjects?.length) {
    return (
      <Stack
        sx={{
          width: '100%',
          height: 'calc(100vh - 240px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Typography variant="body1">
          <FormattedMessage id="common.notify.error" />
        </Typography>
        <HeatPump />
      </Stack>
    );
  }

  return <AllUncompletedProjectsContext.Provider value={value}>{children}</AllUncompletedProjectsContext.Provider>;
}

export function useAllUncompletedProjects() {
  const context = useContext(AllUncompletedProjectsContext);
  if (context === undefined) {
    throw new Error('useAllUncompletedProjects must be used within a AllUncompletedProjectsProvider');
  }
  return context;
}
