import { Stack, Box, Typography, Skeleton, Checkbox } from '@mui/material';
import { useCallback, useMemo, useState } from 'react';
import { ChevronDown } from '@ui/components/Icons/Chevron/Chevron';
import { ChevronUp } from '@ui/components/Icons/Chevron/Chevron';
import { beige, brandYellow } from '@ui/theme/colors';
import { HousePersonOutsideOutlinedIcon } from '@ui/components/StandardIcons/HousePersonOutsideOutlinedIcon';
import { InstallationProjectTimeline_TimelineStatus } from '@aira/grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import { InstallationProjectJob_JobResourceRole } from '@aira/installation-project-grpc-api/build/ts_out/index.com.aira.acquisition.contract.installation.project.v1';
import Link from 'next/link';
import { ArrowRightTopSquareOutlinedIcon } from '@ui/components/StandardIcons/ArrowRightTopSquareOutlinedIcon';
import { Team } from '@aira/resource-grpc-api/build/ts_out/index.com.aira.acquisition.contract.resource.v1';
import { styled } from '@mui/material/styles';
import { useAllUncompletedProjects } from './contexts/ProjectsContext';
import { Tab } from './OrderMaterialContainer';
import UpdatedConfirmationModal from './UpdatedConfirmationModal';

const TableHeaderCell = styled('td')({
  padding: '16px 24px',
  fontSize: '14px',
  fontWeight: 500,
  border: 'none',
});

const TableCell = styled('td')({
  padding: '16px 24px',
  fontSize: '14px',
  height: '108px',
  fontWeight: 400,
  backgroundColor: '#22222608',
  border: 'none',
  borderBottom: `6px solid ${beige[150]}`,
});

const TableRow = styled('tr')({
  borderCollapse: 'collapse',
});

export function OrderMaterialsTable({ teams, selectedTab }: { teams: Team[]; selectedTab: Tab }) {
  const { allUncompletedProjects, isLoadingAllUncompletedProjects } = useAllUncompletedProjects();
  const [sortColumn, setSortColumn] = useState<{ name: string; asc: boolean }>({ name: 'installationDate', asc: true });
  const [confirmationModalTaskId, setConfirmationModalTaskId] = useState<string | null>(null);

  const projectWithProcuredBom = useMemo(() => {
    return allUncompletedProjects?.filter((project) => {
      const hasBom = project.procuredBom?.procuredBom?.orderedAt;
      const hasNoUncompledTasks = !project.installationProject?.tasks.some(
        (task) =>
          task.workDefinition?.work?.$case === 'syncBillOfMaterials' &&
          task.workDefinition?.work?.syncBillOfMaterials.bomId?.value &&
          task.stage?.stage?.$case &&
          task.stage?.stage?.$case !== 'completed',
      );
      return hasBom && hasNoUncompledTasks;
    });
  }, [allUncompletedProjects]);

  const projectsWithoutProcuredBom = useMemo(() => {
    return allUncompletedProjects?.filter((project) => {
      const hasNoBom = !project.procuredBom?.procuredBom?.orderedAt;
      const hasNoUncompledTasks = !project.installationProject?.tasks.some(
        (task) =>
          task.workDefinition?.work?.$case === 'syncBillOfMaterials' &&
          task.workDefinition?.work?.syncBillOfMaterials.bomId?.value &&
          task.stage?.stage?.$case &&
          task.stage?.stage?.$case !== 'completed',
      );
      return hasNoBom && hasNoUncompledTasks;
    });
  }, [allUncompletedProjects]);

  const updatedProjects = useMemo(() => {
    return allUncompletedProjects?.filter((project) => {
      return project.installationProject?.tasks.some(
        (task) =>
          task.workDefinition?.work?.$case === 'syncBillOfMaterials' &&
          task.workDefinition?.work?.syncBillOfMaterials.bomId?.value &&
          task.stage?.stage?.$case &&
          task.stage?.stage?.$case !== 'completed',
      );
    });
  }, [allUncompletedProjects]);

  const projectsToShow = useMemo(() => {
    if (selectedTab === Tab.NOT_ORDERED) {
      return projectsWithoutProcuredBom;
    }
    if (selectedTab === Tab.ORDERED) {
      return projectWithProcuredBom;
    }
    if (selectedTab === Tab.UPDATED) {
      return updatedProjects;
    }
    return projectsWithoutProcuredBom;
  }, [selectedTab, projectsWithoutProcuredBom, projectWithProcuredBom, updatedProjects]);

  const sortedProjects = useMemo(() => {
    return projectsToShow?.sort((a, b) => {
      switch (sortColumn.name) {
        case 'name':
          return a.contact?.firstName?.localeCompare(b.contact?.firstName ?? '') ?? 0;
        case 'team':
          const aTeamId =
            a.installationProject?.jobs.find(
              (job) => job.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER,
            )?.workSegments[0]?.teamId?.value ?? '';
          const aTeamName = teams.find((team) => team.id?.value === aTeamId)?.name ?? '';
          const bTeamId =
            b.installationProject?.jobs.find(
              (job) => job.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER,
            )?.workSegments[0]?.teamId?.value ?? '';
          const bTeamName = teams.find((team) => team.id?.value === bTeamId)?.name ?? '';
          return aTeamName.localeCompare(bTeamName);
        case 'installationDate': {
          const aInstallationDate = a.installationProject?.timeline?.start?.getTime();
          const bInstallationDate = b.installationProject?.timeline?.start?.getTime();
          if (!aInstallationDate && !bInstallationDate) return 0;
          if (!aInstallationDate) return 1;
          if (!bInstallationDate) return -1;
          return aInstallationDate - bInstallationDate;
        }
        default:
          return 0;
      }
    });
  }, [projectsToShow, sortColumn, teams]);

  const handleMarkAsDone = useCallback((taskId: string) => {
    setConfirmationModalTaskId(taskId);
  }, []);

  const handleCloseConfirmationModal = useCallback(() => {
    setConfirmationModalTaskId(null);
  }, []);

  return (
    <div
      style={{
        height: 'calc(100dvh - 200px)',
        width: 'calc(100vw - 130px)',
        overflow: 'auto',
        paddingRight: '16px',
      }}
    >
      <table style={{ borderCollapse: 'collapse', borderSpacing: 0, border: 'none', width: '100%' }}>
        <thead>
          <tr style={{ position: 'sticky', top: 0, zIndex: 1, backgroundColor: beige[150] }}>
            <TableHeaderCell
              onClick={() => setSortColumn({ name: 'name', asc: !sortColumn.asc })}
              sx={{ cursor: 'pointer', minWidth: '150px' }}
            >
              <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={2}>
                Customer
                {sortColumn.name === 'name' &&
                  (sortColumn.asc ? <ChevronDown height={16} width={16} /> : <ChevronUp height={16} width={16} />)}
              </Stack>
            </TableHeaderCell>
            <TableHeaderCell
              onClick={() => setSortColumn({ name: 'team', asc: !sortColumn.asc })}
              sx={{ cursor: 'pointer', minWidth: '150px' }}
            >
              <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={2}>
                Team
                {sortColumn.name === 'team' &&
                  (sortColumn.asc ? <ChevronDown height={16} width={16} /> : <ChevronUp height={16} width={16} />)}
              </Stack>
            </TableHeaderCell>
            <TableHeaderCell
              onClick={() => setSortColumn({ name: 'installationDate', asc: !sortColumn.asc })}
              sx={{ cursor: 'pointer', minWidth: '200px' }}
            >
              <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={2}>
                Installation date
                {sortColumn.name === 'installationDate' &&
                  (sortColumn.asc ? <ChevronDown height={16} width={16} /> : <ChevronUp height={16} width={16} />)}
              </Stack>
            </TableHeaderCell>
            <TableHeaderCell>Link</TableHeaderCell>
            <TableHeaderCell>PO number</TableHeaderCell>
            <TableHeaderCell>Delivery date</TableHeaderCell>
            <TableHeaderCell>Comment</TableHeaderCell>
            {selectedTab === Tab.UPDATED && <TableHeaderCell>Done</TableHeaderCell>}
          </tr>
        </thead>
        <tbody>
          {isLoadingAllUncompletedProjects
            ? Array.from({ length: 10 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell colSpan={8}>
                    <Skeleton variant="text" width="100%" height={24} />
                  </TableCell>
                </TableRow>
              ))
            : sortedProjects.map((project, index) => {
                const plumberTeamId = project.installationProject?.jobs.find(
                  (job) => job.requiredRole === InstallationProjectJob_JobResourceRole.JOB_RESOURCE_ROLE_PLUMBER,
                )?.workSegments[0]?.teamId?.value;

                const plumberTeam = teams.find((team) => team.id?.value === plumberTeamId);

                const syncedBomTask =
                  selectedTab === Tab.UPDATED
                    ? project.installationProject?.tasks.find(
                        (task) =>
                          task.workDefinition?.work?.$case === 'syncBillOfMaterials' &&
                          task.workDefinition?.work?.syncBillOfMaterials.bomId?.value &&
                          task.stage?.stage?.$case &&
                          task.stage?.stage?.$case !== 'completed',
                      )
                    : null;
                const deliveryDate =
                  project.procuredBom?.procuredBom?.orderedItems
                    .map((item) => item.deliveryDate)
                    .reduce((acc, curr) => {
                      if (acc && curr) {
                        return acc < curr ? acc : curr;
                      }
                      return acc ?? curr;
                    }, undefined) ?? undefined;

                return (
                  <TableRow
                    key={project.installationProject?.id?.value}
                    sx={
                      index === 0
                        ? {
                            '& td:first-of-type': {
                              borderTopLeftRadius: '16px',
                            },
                            '& td:last-of-type': {
                              borderTopRightRadius: '22px',
                            },
                          }
                        : {}
                    }
                  >
                    <TableCell>
                      <Stack direction="row" alignItems="flex-start" justifyContent="flex-start" spacing={2}>
                        <Box sx={{ pt: '2px' }}>
                          <HousePersonOutsideOutlinedIcon height={16} width={16} />
                        </Box>
                        <Stack direction="column" alignItems="flex-start" justifyContent="flex-start" spacing={0}>
                          <Typography variant="body1">
                            {project.contact?.firstName} {project.contact?.lastName}
                          </Typography>
                          <Typography variant="body2">{project.address?.specificDetails?.formattedAddress}</Typography>
                        </Stack>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{plumberTeam?.name}</Typography>
                    </TableCell>

                    <TableCell>
                      <Stack alignItems="flex-start" justifyContent="flex-start" spacing={1}>
                        <Typography variant="body1" sx={{ color: brandYellow[600] }}>
                          {project.installationProject?.timeline?.status ===
                          InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_PRELIMINARY
                            ? 'Preliminary'
                            : project.installationProject?.timeline?.status ===
                                InstallationProjectTimeline_TimelineStatus.TIMELINE_STATUS_CONFIRMED
                              ? 'Confirmed'
                              : ''}
                        </Typography>
                        <Typography variant="body1">
                          {project.installationProject?.timeline?.start?.toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          })}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Link
                        href={`/solution/${project.installationProject?.energySolutionId?.value}/bill-of-materials`}
                      >
                        <Stack direction="row" alignItems="center" justifyContent="flex-start" spacing={1}>
                          <Typography variant="body1">Procure</Typography>{' '}
                          <ArrowRightTopSquareOutlinedIcon height={16} width={16} />
                        </Stack>
                      </Link>
                    </TableCell>

                    <TableCell />
                    <TableCell>
                      <Typography variant="body1">{deliveryDate?.toLocaleDateString()}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{project.procuredBom?.procuredBom?.comment}</Typography>
                    </TableCell>
                    {selectedTab === Tab.UPDATED && (
                      <TableCell>
                        <Checkbox onChange={() => handleMarkAsDone(syncedBomTask?.id?.value ?? '')} />
                      </TableCell>
                    )}
                  </TableRow>
                );
              })}
        </tbody>
      </table>

      <UpdatedConfirmationModal
        isModalOpen={confirmationModalTaskId !== null}
        onClose={handleCloseConfirmationModal}
        taskId={confirmationModalTaskId}
      />
    </div>
  );
}
