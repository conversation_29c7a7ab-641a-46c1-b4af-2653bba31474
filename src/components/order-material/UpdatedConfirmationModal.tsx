import { Button } from '@ui/components/Button/Button';
import { Modal } from '@ui/components/Modal/Modal';
import { Stack, Typography } from '@mui/material';
import { api } from 'utils/api';

export default function UpdatedConfirmationModal({
  isModalOpen,
  onClose,
  taskId,
}: {
  isModalOpen: boolean;
  onClose: () => void;
  taskId: string | null;
}) {
  const { mutateAsync: markAsDone } = api.InstallationProject.markAsOrderMaterialDone.useMutation({
    onSuccess() {
      onClose();
    },
  });
  const handleConfirm = () => {
    if (taskId) {
      markAsDone({ taskId });
    }
  };

  return (
    <Modal isModalOpen={isModalOpen} handleClose={onClose} width="400px" height="auto">
      <Stack gap={1}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="body1Emphasis">Mark as done?</Typography>
        </Stack>
        <Typography>Once you mark the job as done, it’ll disappear from this view.</Typography>
        <Stack direction="row" spacing={2} justifyContent="space-between" mt={2}>
          <Button variant="outlined" onClick={onClose} fullWidth>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleConfirm} fullWidth>
            Done
          </Button>
        </Stack>
      </Stack>
    </Modal>
  );
}
