# Aira Internal web a.k.a. Aerospace

## Contact

The project is owned by the Energy Solution Design team, but much code is shared within the various Acquisition teams.
More granular code ownership will be defined in the `CODEOWNERS` file. Please use
the [#aerospace](https://aira-home-workspace.slack.com/archives/C067HPWAPB7) channel on Slack for communication.

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for developer guidelines.

## Get started

### First-time setup

1. Copy `.env.example` to `.env` and change environment variables in the files as needed.
2. Install `Node.js`, of at least the version specified in the `.nvmrc` file, perhaps using a package manager like `nvm`
   or `fnm`.
3. Install `corepack` e.g. using Homebrew `brew install node corepack` and set it up by running `corepack enable`. This
   is the tool that controls the version of pnpm that is used. You should not have pnpm installed separately.
4. Install and [setup](https://github.com/airahome/infra/blob/main/documentation/PERSONAL.md#aws-sso-login) AWS CLI, as
   internal library dependencies are hosted on AWS CodeArtifact.
5. [Setup](https://github.com/airahome/infra/blob/main/documentation/PERSONAL.md#kubernetes-context-updates) your
   Kubernetes config, so Aerospace can proxy requests to backend services in systest.
6. [Generate an auth certificate](#generate-auth-certificate), which will soon be required for
   the [zero-trust architecture](https://www.notion.so/119-Zero-trust-architecture-504582f7c1744016aba227e25a87d54f) and
   will allow you to connect to backend services.
7. Run `pnpm install && pnpm devWithAuthMac`[*](#running-the-development-server) for the first time whereupon you will
   be
   prompted for your keychain password which then inserts your private key password as an environment variable.
8. Optional: To enable formatting before committing, run `pnpm prepare:hooks`.

### Daily setup

1. Switch and login to your "tools" `AWS_PROFILE` e.g. `export AWS_PROFILE={myteam}-dev-tools; aws sso login`
2. Run `./devsetup.sh`. This performs some checks on your build environment, fetches the token from AWS CodeArtifact and
   adds it to your pnpm config file (location depends on your system).

### Running locally

1. Do the [daily setup steps](#daily-setup) detailed above
2. Start the backend services using either of the approaches described later in this README:
    - the [systest proxy](#backend-services-via-systest-proxy) (recommended)
    - with [docker-compose](#backend-services-via-docker-compose)
4. Run `pnpm install && pnpm devWithAuthMac`[*](#running-the-development-server)

#### Backend services via systest proxy

All the REST APIs and GRPC APIs can now be access by port-forwarding to an nginx proxy (pi-proxy-nginx).

1. Switch and login to your "test" `AWS_PROFILE` e.g. `export AWS_PROFILE={myteam}-dev-test; aws sso login`
2. Run
   `kubectl port-forward deployment/pi-proxy-nginx --namespace team-pi --context arn:aws:eks:eu-north-1:361629632765:cluster/systest 8080:8080`

#### Backend services via docker-compose

If you wish to run backend services locally, check out
the [acquisition-shared](https://github.com/airahome/acquisition-shared/) repository which contains docker-compose files
to start them up. It can also run Aerospace itself.

A docker-compose file for just Aerospace is also contained in this repository. To run it, use `docker-compose up`.

#### Generate Auth certificate

1. Create a PR in the the [auth repository](https://github.com/airahome/auth) that will add the following claims to the
   `app/src/main/resources/access.yaml` file. Copy the access from your teammates. For example:
   ```yaml
   [your-client-id]:
       default-ps: [ systest, uat, prod ]
       team-{myteam}-ps: [ systest, uat, prod ]
   ```
   where `your-client-id` is your `first-name.last-name` as it is in your airahome email address. If you do not have
   access to this repository, ask in the [#team-cloud](https://aira-home-workspace.slack.com/archives/C04QWRMP76F) Slack
   channel.
2. Make sure you have the proxy running that is described in
   the [Backend services via systest proxy](#backend-services-via-systest-proxy) section of this README.
3. In the terminal run `./generate_cert.sh`. (If you use linux you may have to edit the script a little. Please create a
   separate script containing your edits and update the readme).
   You will asked to input your auth client ID that you should get from the cloud team in the previous step. A strong
   password will be automatically generated and stored in keychain on macos (other solutions to follow for linux). These
   inputs will be used to create a private key and certificate request for you. The path to the private key and auth
   client ID will get written into your `.env` file here as `AUTH_PRIVATE_KEY_PATH_OVERRIDE` and `AUTH_CLIENT_ID`. The
   certificate request will then be sent to the auth server to generate a certificate. The certs will be persisted locally
   in `.auth/` and they will also persist a passphrase in the keychain for Mac computers. To fetch the passphrase
   use `security find-generic-password -s "AUTH_PRIVATE_KEY_PASSWORD" -w`.

### Running the development server

We have a few commands for running `pnpm dev` and you should should prefix them with `pnpm &&` as running just
`pnpm install`
installs dependencies, it is a good idea to always run it before starting the development server to make sure you are
running with the right dependencies.

If you have created your private key and password manually the you should run
`pnpm install && pnpm devWithAuthUserInput` which
will prompt your for your private key password.

## Generating backend types for REST API:s

We are moving over all calls to the backend to use gRPC. For our legacy REST calls, we use the OpenAPI definition served
by the backend to generate types. To update these types:

1. Get the latest version of `aira-web-backend` running locally
2. Run `OPENAPI_URL="http://localhost:8080/v3/api-docs" pnpm generate:airaTypes`

This will generate the file `src/types/airaTypes.ts` from the OpenAPI definition served by aira-web-backend on your
local machine. (We used to have this running from the test environment, but not any more. Consider instead migrating
your endpoint to gRPC.)

## Localization

We use [POEditor](https://poeditor.com) for localization. Ask someone in the team, or in
the [#aerospace](https://aira-home-workspace.slack.com/archives/C067HPWAPB7) channel, to add you to the Aira project.

To add a new key, go to the [Aerospace](https://poeditor.com/projects/view?id=683645) project in POEditor, then the "
Terms" tab, then press the plus sign. Try to follow the hierarchy patterns of existing keys. Then go to "Languages" and
add English translations for the new key.

To import keys and new translations into the project, use the Github Actions workflow:

- Go to [Actions for the repository](https://github.com/airahome/aerospace/actions), choose the Fetch
  Translations workflow and do a "Run workflow".
- It will generate a pull request with the new translations. Review the changes and merge the pull request.

## Working with local gRPC contracts

Sometimes you may want to develop locally in Aerospace using contracts that you have not yet published to AWS
CodeArtifact.
To do this, you can refer to the contracts locally. For example, if you have done some edits to the
`installation-groundwork-grpc-api` contract, run `./gradlew build` in the `installation-groundwork` repository to build
the Typescript files and then run the following in this repository:

```bash
$ pnpm add @aira/installation-groundwork-grpc-api@link:../installation-groundwork/installation-groundwork-schema
```

## E2E testing

We are using Playwright for E2E testing.

### Setup

- Make sure the dependencies are up to date by running `pnpm install`
- To install playwright browsers

```bash
$ pnpm playwright install && pnpm playwright install msedge 
```

### Working with tests

- Make sure [systest proxy](#backend-services-via-systest-proxy) is running. Note: Playwright starts a local server if
  not already running.
- To run the test run `pnpm e2e`, or use `pnpm e2e:watch` to open UI mode.

## Logs

To log from the Next.js backend, use `ctx.log.{info,warn,error}`. These logs will include context like `traceId` which
can be used to correlate logs across services.
Each handled request also gets automatically logged (with `info` if successful and `error` if unsuccessful).

Logs can be viewed
in [Cloudwatch Insights](https://eu-north-1.console.aws.amazon.com/cloudwatch/home?region=eu-north-1#logsV2:logs-insights).
You can find suitable queries under Queries -> aerospace. 
