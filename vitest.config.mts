// vitest.config.ts

import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { configDefaults, defineConfig } from 'vitest/config';
import { Environments } from './src/server/api/environments';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

export const BROWSER_BASED_TEST_SOURCE = 'src/**/*.test.browser.{ts,tsx}';
export const NODE_BASED_TEST_SOURCE = 'src/**/*.test.{ts,tsx}';

export default defineConfig({
  define: {
    'process.env': JSON.stringify({
      ENVIRONMENT: Environments.LOCAL,
    }),
    'process.browser': true,
  },
  // according to https://github.com/vitest-dev/vitest/issues/7944#event-17851707752 it seems that without
  // specifying cacheDir vitest might have issues importing things sometimes
  cacheDir: 'node_modules/.vitest',
  optimizeDeps: {
    include: [
      'vite-plugin-node-polyfills/shims/buffer',
      'vite-plugin-node-polyfills/shims/global',
      'vite-plugin-node-polyfills/shims/process',
      '@testing-library/jest-dom/vitest',
      'assert',
    ],
  },
  test: {
    sequence: {
      // Both these things would be good to have enabled, but until someone has time
      // to look closely at why are tests are insufficiently isolated, we'll leave them off
      shuffle: false,
      concurrent: false,
    },
    globals: true,
    environment: 'jsdom', // Use jsdom for browser-like tests
    setupFiles: ['./vitest.setup.mts'],
    coverage: {
      reporter: ['text', 'json', 'html'], // Optional: Add coverage reports
    },
    exclude: [
      ...configDefaults.exclude,
      '**/e2e/**', // Exclude e2e tests from coverage
    ],
    projects: [
      {
        plugins: [tsconfigPaths(), react(), nodePolyfills()],
        extends: true,
        test: {
          name: 'Browser',
          include: [BROWSER_BASED_TEST_SOURCE],
          environment: 'browser',
          browser: {
            enabled: true,
            headless: false,
            provider: 'playwright',
            isolate: true,
            viewport: {
              width: 1920,
              height: 1080,
            },

            instances: [
              {
                browser: 'chromium',
                launch: {
                  channel: 'chromium',
                },
                context: {
                  locale: 'en-GB',
                },
              },
            ],
          },
        },
      },
      {
        extends: true,
        plugins: [tsconfigPaths(), react()],
        test: {
          include: [NODE_BASED_TEST_SOURCE],
          name: 'JSDOM',
        },
      },
    ],
  },
});
