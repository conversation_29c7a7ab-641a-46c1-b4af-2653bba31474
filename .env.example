# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/env/schema.mjs"
# should be updated accordingly.

ENVIRONMENT=development
GOOGLE_API_KEY=AIzaSyDrQSi42y2Nx2G4Pl6uskljR6inIH9dUgI
LOQATE_API_KEY=ZE59-FB15-GA65-ZX87

AZURE_OID=3b2ec3a1-bc97-4c7f-90de-bf168a8c48db
AZURE_EMAIL=<EMAIL>

LIVEKIT_URL=ws://localhost:7880

BACKEND_ENDPOINT=http://localhost:8080/aira-web-backend
BASELINE_ENDPOINT=http://localhost:8080/baseline-calculator
OPEN_ROUTE_SERVICE_ENDPOINT=http://localhost:8080/open-route-service

PUBLIC_WEB_URL=http://localhost:3000
QUOTE_WEB_URL=http://localhost:3002
PAYMENTS_WEB_URL=http://localhost:3003

GRPC_ENDPOINT=localhost:8080
INSTALLATION_GROUNDWORK_GRPC_ENDPOINT=localhost:8080
INSTALLATION_PROJECTS_GRPC_ENDPOINT=localhost:8080
IDENTITY_SERVICE_GRPC_ENDPOINT=localhost:8080
MAGICPLAN_GRPC_ENDPOINT=localhost:8080
RESOURCE_SERVICE_GRPC_ENDPOINT=localhost:8080
MAN_HOURS_GRPC_ENDPOINT=localhost:8080
SERVICE_VISITS_GRPC_ENDPOINT=localhost:8080
BILL_OF_MATERIALS_GRPC_ENDPOINT=localhost:8080
ON_SITE_DOSSIER_GRPC_ENDPOINT=localhost:8080
HUBSPOT_INTEGRATION_GRPC_ENDPOINT=localhost:9123
SALES_SUPPORT_GRPC_ENDPOINT=localhost:8080

BACKOFFICE_ASSISTANT_ENDPOINT=localhost:50051

GRPC_AUTHORITY=aira-web-backend.local
INSTALLATION_GROUNDWORK_GRPC_AUTHORITY=installation-groundwork.local
INSTALLATION_PROJECTS_GRPC_AUTHORITY=installation-projects.local
IDENTITY_SERVICE_GRPC_AUTHORITY=identity.local
MAGICPLAN_GRPC_AUTHORITY=magicplan.local
RESOURCE_SERVICE_GRPC_AUTHORITY=resource.local
MAN_HOURS_GRPC_AUTHORITY=baseline-calculator.local
SERVICE_VISITS_GRPC_AUTHORITY=service-visit.local
BILL_OF_MATERIALS_SERVICE_GRPC_AUTHORITY=bill-of-materials.local
ON_SITE_DOSSIER_GRPC_AUTHORITY=on-site-dossier.local
HUBSPOT_INTEGRATION_GRPC_AUTHORITY=hubspot.local
SALES_SUPPORT_GRPC_AUTHORITY=sales-support.local

BACKOFFICE_ASSISTANT_GRPC_AUTHORITY=backoffice-assistant.local

AUTH_ORIGIN=http://localhost:8080/auth
AWS_REGION=eu-north-1


# When you run the generate_cert.sh script, the path to the private key will get put into 
# your .env file automatically
# If you want to use a different private key, you can copy your path to the .env file
# AUTH_PRIVATE_KEY_PATH_OVERRIDE=/Users/<USER>/.auth/your-auth-client-id.key


# The AUTH_CLIENT_ID will get automatically set When you run the generate_cert.sh script
# AUTH_CLIENT_ID should be your first name and last name separated by a dot
AUTH_CLIENT_ID="firstname.lastname"

# Set these values locally to enable mock authentication (i.e. to bypass 
# Azure AD and fetching of user information from the backend)
# But remove them and use AZURE_EMAIL if you want to mock as an actual user
LOCAL_MOCK_SESSION_EMAIL="<EMAIL>"
LOCAL_MOCK_SESSION_FIRST_NAME="Test User"
LOCAL_MOCK_SESSION_LAST_NAME="User"
LIVEKIT_URL=""

