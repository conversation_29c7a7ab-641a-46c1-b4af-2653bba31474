# This Dockerfile is used to build and test the app. For development and troubleshooting of the CI, check out the
# script ./scripts/docker.sh 

FROM 660263384063.dkr.ecr.eu-north-1.amazonaws.com/docker-hub/library/node:22.15.0-bookworm-slim AS custom_base

# ------------------------------------------------------------------------------
# OS-level dependencies.

FROM custom_base AS os_deps

RUN corepack enable

# This installs the core dependencies for <PERSON><PERSON>, like a whole bunch of Ubuntu packages. 
# We want to run it early in the Dockerfile so it's cached.
RUN pnpm dlx playwright@1.52.0 install-deps

# ------------------------------------------------------------------------------
# App-level dependencies.

FROM os_deps AS app_deps

WORKDIR /build

COPY ./package.json ./
RUN corepack install

COPY ./pnpm-lock.yaml ./
COPY ./patches/ ./patches/
COPY ./.npmrc ./

# A possible improvement here would be to also use a cache for the pnpm packages, using something like:
# RUN --mount=type="cache",target="/root/.wherever-pnpm-caches-stuff/" pnpm install
# This will lead to quicker installs when packages have changed AND we're on the same host machine.
RUN --mount=type=secret,id=npm_token \
    NPM_TOKEN=$(cat /run/secrets/npm_token) && \
    pnpm config set //airahome-660263384063.d.codeartifact.eu-north-1.amazonaws.com/npm/libraries/:_authToken "$NPM_TOKEN"

RUN pnpm config set store-dir /root/pnpm-store
RUN --mount=type=cache,id=pnpm-store,target=/root/pnpm-store,sharing=locked \
    pnpm install --frozen-lockfile

COPY \
    ./eslint.config.mjs \
    ./next.config.mjs \
    ./vitest.config.mts \
    ./vitest.setup.mts \
    ./tsconfig.json \
    ./.prettierrc \
    ./.prettierignore \
    ./

COPY ./__mocks__ ./__mocks__/
COPY ./public/ ./public/
COPY ./.env.ci ./.env
COPY ./src/ ./src/

# ------------------------------------------------------------------------------
# Builder.
FROM app_deps AS builder

ARG BUILD_ID

# This makes sure that the cache busting works correctly.
ENV BUILD_ID=${BUILD_ID}

RUN pnpm run build

# ------------------------------------------------------------------------------
# Tester.
#
# "Building" and "testing" is really conflated in Next.js, since you need to run
# a full "next build" to ensure that the app is working correctly.
FROM builder AS tester

RUN pnpm playwright install chromium --no-shell

COPY CHANGED_FILES ./CHANGED_FILES

ARG TEST_CACHE_BUST=0
RUN pnpm exec prettier --check --no-error-on-unmatched-pattern --ignore-unknown $(cat ./CHANGED_FILES)

COPY ./scripts/test-ci.sh ./scripts/test-ci.sh
RUN pnpm exec ./scripts/test-ci.sh


# ------------------------------------------------------------------------------
# App.
#
# This builds a tight image that contains only the necessary files to run the app.

FROM custom_base AS app

WORKDIR /app

COPY --from=builder /build/public ./public
COPY --from=builder /build/.next/standalone ./
COPY --from=builder /build/.next/static ./.next/static
COPY --from=builder /build/.env ./.env

ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
