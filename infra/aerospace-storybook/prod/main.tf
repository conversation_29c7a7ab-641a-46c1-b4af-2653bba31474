terraform {
  required_version = ">= 1.2.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  backend "s3" {
    bucket = "heatly-infra-prod-prod"
    key    = "aerospace-storybook.tfstate"
    region = "eu-north-1"
  }
}

provider "aws" {
  allowed_account_ids = ["************"]
  region              = "eu-north-1"
}

locals {
  avoid_workspaces  = terraform.workspace == "default" ? null : file("ERROR: Avoid workspaces!")
  account_number    = "************"
  environment       = "prod"
  oidc_provider_url = "oidc.eks.eu-north-1.amazonaws.com/id/48003E19DCD41A993C6D03695DA71357"
}

module "main" {
  source            = "../modules/main"
  account_number    = local.account_number
  environment       = local.environment
  oidc_provider_url = local.oidc_provider_url
}
