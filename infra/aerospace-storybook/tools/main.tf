terraform {
  required_version = ">= 1.2.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  backend "s3" {
    bucket = "heatly-infra-tools-tools"
    key    = "aerospace-storybook.tfstate"
    region = "eu-north-1"
  }
}

provider "aws" {
  profile = "Tools"
  region  = "eu-north-1"
}

locals {
  account_number = "************"
}

module "ecr" {
  source    = "../modules/ecr"
  application_name = "aerospace-storybook"
}
