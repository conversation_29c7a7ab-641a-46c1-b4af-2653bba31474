locals {
  avoid_workspaces  = terraform.workspace == "default" ? null : file("ERROR: Avoid workspaces!")
  account_number    = "************"
  environment       = "prod"
  oidc_provider_url = "oidc.eks.eu-north-1.amazonaws.com/id/052B1CA142D089001828DCAA3210583B"
  application_name  = "aira-internal-web"
}

terraform {
  required_version = ">= 1.2.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.68"
    }
  }

  backend "s3" {
    bucket = "heatly-infra-prod-prod"
    key    = "aira-internal-web.tfstate"
    region = "eu-north-1"
  }
}

provider "aws" {
  allowed_account_ids = [local.account_number]
  region              = "eu-north-1"
  default_tags {
    tags = {
      owner       = "TeamAcqEnergySolutionDesign",
      costOwner   = "TeamAcqEnergySolutionDesign",
      project     = local.application_name,
      environment = local.environment
    }
  }
}

module "main" {
  source            = "../modules/main"
  account_number    = local.account_number
  environment       = local.environment
  oidc_provider_url = local.oidc_provider_url
  team_role_arn     = "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/eu-north-1/AWSReservedSSO_esd-dev@prod_00daf52e873357eb"
  # team-pi need access to the KMS key to decrypt the redis access token, to allow for administration of aerospace-websocket-server.
  additional_kms_read_roles = [
    "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/eu-north-1/AWSReservedSSO_pi-dev@prod_27e2ef03431b9916"
  ]
}

data "aws_sns_topic" "slack_alerts" {
  name = "slack-alerts-team-energy-solution-design-aerospace-prod"
}

module "alarms" {
  source           = "../modules/alarms"
  environment      = local.environment
  application_name = local.application_name
  alarm_sns_topic  = data.aws_sns_topic.slack_alerts.arn
}
