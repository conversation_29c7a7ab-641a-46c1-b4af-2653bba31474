
data "aws_vpc" "vpc" {
  filter {
    name = "tag:Name"
    values = [
      var.environment,
    ]
  }
}

data "aws_subnets" "redis_subnets" {
  filter {
    name = "vpc-id"
    values = [data.aws_vpc.vpc.id]
  }

  tags = {
    Visibility = "private"
  }
}

resource "aws_security_group" "security_group" {
  vpc_id = data.aws_vpc.vpc.id
  description = "Allow Redis traffic."
  name   = "${var.application_name}-${var.environment}-redis-sg"

  tags = {
    Name = "${var.application_name}-${var.environment}-redis-sg"
  }
  ingress {
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.vpc.cidr_block]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [data.aws_vpc.vpc.cidr_block]
  }
}

resource "aws_elasticache_subnet_group" "default" {
  name       = "${var.application_name}-${var.environment}-subnet-group"
  subnet_ids = data.aws_subnets.redis_subnets.ids
}

resource "random_password" "auth_token" {
  length  = 128
  special = false
}

resource "aws_ssm_parameter" "auth_token" {
  name        = "/${var.environment}/${var.application_name}/redis-auth-token"
  description = "Auth token for the Redis cluster."
  type        = "SecureString"
  key_id      = var.kms_key_arn
  value       = random_password.auth_token.result
}

resource "aws_elasticache_replication_group" "redis" {
  replication_group_id = "${var.application_name}-${var.environment}-redis"
  description = "Read/write primary node, and 2 read-only replicas used for failover."

  # security
  at_rest_encryption_enabled = true
  transit_encryption_enabled = true
  auth_token = random_password.auth_token.result
  kms_key_id = var.kms_key_arn

  # config
  engine = "redis"
  engine_version = "7.1"
  node_type = "cache.t4g.small"
  port = 6379
  subnet_group_name = aws_elasticache_subnet_group.default.name
  security_group_ids = [aws_security_group.security_group.id]
  maintenance_window = "tue:02:00-tue:03:00"

  # clustering
  multi_az_enabled = true
  num_cache_clusters = 3
  automatic_failover_enabled = true

  # backup
  final_snapshot_identifier = "${var.application_name}-${var.environment}-redis"
  snapshot_retention_limit = 14
  snapshot_window = "01:00-02:00"
}
