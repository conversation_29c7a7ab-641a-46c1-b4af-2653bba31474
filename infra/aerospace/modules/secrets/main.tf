variable "environment" {}
variable "application_name" {}

data "aws_ssm_parameter" "auth_key" {
  name = "/${var.environment}/${var.application_name}/auth-key"
}

data "aws_ssm_parameter" "auth_key_password" {
  name = "/${var.environment}/${var.application_name}/auth-key-password"
}

data "aws_iam_policy_document" "secrets_ssm_policy_doc" {
  statement {
    actions = [
      "ssm:GetParameters",
      "ssm:GetParameter"
    ]
    resources = [
      data.aws_ssm_parameter.auth_key.arn,
      data.aws_ssm_parameter.auth_key_password.arn
    ]
  }
}

resource "aws_iam_policy" "secrets_ssm_policy" {
  name   = "ssm-${var.application_name}-${var.environment}-policy"
  policy = data.aws_iam_policy_document.secrets_ssm_policy_doc.json
}

output "secrets_policy_arn" {
  value = aws_iam_policy.secrets_ssm_policy.arn
}
