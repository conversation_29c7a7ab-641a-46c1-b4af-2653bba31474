resource "aws_iam_role" "this" {
  name               = "application-${local.application_name}-${var.environment}"
  description        = "Role used by application ${local.application_name} in ${var.environment}"
  assume_role_policy = data.aws_iam_policy_document.this.json

  tags = {
    Name        = "application-${local.application_name}-${var.environment}"
  }
}

data "aws_iam_policy_document" "this" {
  statement {
    effect = "Allow"

    principals {
      type = "Federated"
      identifiers = [
        "arn:aws:iam::${var.account_number}:oidc-provider/${var.oidc_provider_url}"]
    }

    condition {
      test     = "StringEquals"
      variable = "${var.oidc_provider_url}:sub"
      values = [
        "system:serviceaccount:application:${local.application_name}",
        "system:serviceaccount:team-esd:${local.application_name}"
      ]
    }

    condition {
      test     = "StringEquals"
      variable = "${var.oidc_provider_url}:aud"
      values = ["sts.amazonaws.com"]
    }

    actions = ["sts:AssumeRoleWithWebIdentity"]
  }
}

resource "aws_iam_role_policy" "this" {
  name   = "policy"
  role   = aws_iam_role.this.name
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "Dummy",
        Effect = "Allow",
        Action = ["dummy:dummy"], // Placeholder as no real AWS action is needed right now
        Resource = ["*"],
      },
    ],
  })
}

resource "aws_iam_role_policy_attachment" "secrets" {
  policy_arn = module.secrets.secrets_policy_arn
  role       = aws_iam_role.this.name
}
