data "aws_iam_policy_document" "s3_iam_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "s3:GetObject",
    ]
    resources = ["arn:aws:s3:::team-pi-${var.environment}-video-call-media/*"]
  }
}

resource "aws_iam_role_policy" "s3_access" {
  name   = "s3-access"
  role   = aws_iam_role.this.name
  policy = data.aws_iam_policy_document.s3_iam_policy_document.json
}

data "aws_iam_policy_document" "s3_house_photos_policy_document" {
  # Allow listing objects in the bucket (must use bucket ARN without /*)
  statement {
    effect = "Allow"
    actions = [
      "s3:ListBucket",
    ]
    resources = ["arn:aws:s3:::team-pi-${var.environment}-video-call-house-photos"]
  }
  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:DeleteObject",
    ]
    resources = ["arn:aws:s3:::team-pi-${var.environment}-video-call-house-photos/*"]
  }
}

resource "aws_iam_role_policy" "s3_house_photos_access" {
  name   = "s3-house-photos-access"
  role   = aws_iam_role.this.name
  policy = data.aws_iam_policy_document.s3_house_photos_policy_document.json
}

