locals {
  application_name = "aira-internal-web"
}

module "secrets" {
  source           = "../secrets"
  application_name = local.application_name
  environment      = var.environment
}

module "kms" {
  source           = "../kms"
  environment      = var.environment
  application_name = local.application_name
  team_role_arn    = var.team_role_arn
  service_role_arn = aws_iam_role.this.arn
  additional_read_roles = concat(
    var.additional_kms_read_roles,
    ["arn:aws:iam::${var.account_number}:role/application-aerospace-websocket-server-${var.environment}"]
  )
}

module "redis" {
  source           = "../redis"
  environment      = var.environment
  account_number   = var.account_number
  application_name = local.application_name
  kms_key_arn      = module.kms.kms_key_arn
}
