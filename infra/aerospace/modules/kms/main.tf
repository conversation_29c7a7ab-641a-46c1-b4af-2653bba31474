data "aws_caller_identity" "current" {}

resource "aws_kms_key" "kms_key" {
  description         = "Key Management Service key for ${var.application_name} in ${var.environment}"
  enable_key_rotation = true
  policy = templatefile("${path.module}/kms-policy.json.tmpl", {
    aws_account_number     = data.aws_caller_identity.current.account_id
    aws_team_role          = var.team_role_arn
    service_role           = var.service_role_arn
    additional_read_roles  = var.additional_read_roles
  })
  tags = {
    Name = "${var.application_name}-${var.environment}-kms-key"
  }
}

resource "aws_kms_alias" "kms_alias" {
  name          = "alias/${var.application_name}-${var.environment}"
  target_key_id = aws_kms_key.kms_key.key_id
}
