{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "Key Administration",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "${aws_team_role}",
          "${service_role}",
          "arn:aws:iam::${aws_account_number}:role/kms-administrator"
        ]
      },
      "Action": [
        "kms:Create*",
        "kms:Decrypt",
        "kms:Describe*",
        "kms:Enable*",
        "kms:Encrypt",
        "kms:List*",
        "kms:Put*",
        "kms:Update*",
        "kms:Revoke*",
        "kms:Disable*",
        "kms:Get*",
        "kms:Delete*",
        "kms:TagResource",
        "kms:UntagResource",
        "kms:ScheduleKeyDeletion",
        "kms:CancelKeyDeletion"
      ],
      "Resource": "*"
    },
    {
      "Sid": "Key usage",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${aws_account_number}:role/aws-service-role/rds.amazonaws.com/AWSServiceRoleForRDS"
      },
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ],
      "Resource": "*"
    },
    {
      "Sid": "Read access to redis key for websocket server administration",
      "Effect": "Allow",
      "Principal": {
        "AWS": ${jsonencode(additional_read_roles)}
      },
      "Action": [
        "kms:Decrypt",
        "kms:DescribeKey"
      ],
      "Resource": "*"
    }
  ]
}
