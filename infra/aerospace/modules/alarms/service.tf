locals {
  app       = var.application_name
  log_group = "/eks/${var.environment}/team-esd"
  namespace = "${var.environment}/${local.app}"
  tags      = {
    owner       = "TeamAcqEnergySolutionDesign",
    costOwner   = "TeamAcqEnergySolutionDesign",
    project     = var.application_name,
    environment = var.environment
  }
}

resource "aws_cloudwatch_log_metric_filter" "aerospace_error_metric" {
  name           = "${local.app}-${var.environment}-error-metric"
  pattern        = "{($.app=${local.app}) && ($.level=ERROR)}"
  log_group_name = local.log_group

  metric_transformation {
    name          = "ErrorCount"
    namespace     = local.namespace
    value         = "1"
    default_value = "0"
  }
}

resource "aws_cloudwatch_metric_alarm" "aerospace_error_alarm" {
  alarm_name          = "${local.app}-${var.environment}-error-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "ErrorCount"
  namespace           = local.namespace
  period              = 60
  statistic           = "Sum"
  threshold           = 1
  treat_missing_data  = "notBreaching"
  alarm_description   = "This metric monitors the error rates in the ${local.app} service in the environment ${var.environment}"
  alarm_actions       = [var.alarm_sns_topic]
  tags                = local.tags
}
