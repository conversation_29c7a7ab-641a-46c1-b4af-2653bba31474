terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }
}

resource "aws_ecr_repository" "this" {
  name = var.application_name
  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository_policy" "this" {
  policy     = <<EOF
{
 "Version": "2012-10-17",
 "Statement": [
   {
     "Sid": "AllowPull",
     "Effect": "Allow",
     "Principal": {
       "AWS": [
         "arn:aws:iam::361629632765:root",
         "arn:aws:iam::528895488893:root"
       ]
     },
     "Action": [
       "ecr:BatchGetImage",
       "ecr:BatchCheckLayerAvailability",
       "ecr:GetDownloadUrlForLayer"
     ]
   }
 ]
}
EOF
  repository = aws_ecr_repository.this.name
}