locals {
  avoid_workspaces  = terraform.workspace == "default" ? null : file("ERROR: Avoid workspaces!")
  account_number    = "************"
  environment       = "systest"
  oidc_provider_url = "oidc.eks.eu-north-1.amazonaws.com/id/48003E19DCD41A993C6D03695DA71357"
  application_name  = "aira-internal-web"
}

terraform {
  required_version = ">= 1.2.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.68"
    }
  }

  backend "s3" {
    bucket = "heatly-infra-test-systest"
    key    = "aira-internal-web.tfstate"
    region = "eu-north-1"
  }
}

provider "aws" {
  allowed_account_ids = [local.account_number]
  region              = "eu-north-1"
  default_tags {
    tags = {
      owner       = "TeamAcqEnergySolutionDesign",
      costOwner   = "TeamAcqEnergySolutionDesign",
      project     = local.application_name,
      environment = local.environment
    }
  }
}

module "main" {
  source            = "../modules/main"
  account_number    = local.account_number
  environment       = local.environment
  oidc_provider_url = local.oidc_provider_url
  team_role_arn     = "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/eu-north-1/AWSReservedSSO_esd-dev@test_de39466e71db389d"
  # team-pi need access to the KMS key to decrypt the redis access token, to allow for administration of aerospace-websocket-server.
  additional_kms_read_roles = [
    "arn:aws:iam::************:role/aws-reserved/sso.amazonaws.com/eu-north-1/AWSReservedSSO_pi-dev@test_cf7ad4afaede5e0a"
  ]
}
