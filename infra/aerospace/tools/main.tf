terraform {
  required_version = ">= 1.2.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  backend "s3" {
    bucket = "heatly-infra-tools-tools"
    key    = "aira-internal-web.tfstate"
    region = "eu-north-1"
  }
}

provider "aws" {
  region = "eu-north-1"
  default_tags {
    tags = {
      owner     = "TeamAcqEnergySolutionDesign",
      costOwner = "TeamAcqEnergySolutionDesign",
      project   = "aira-internal-web",
    }
  }
}

locals {
  account_number = "************"
}

module "ecr" {
  source    = "../modules/ecr"
  application_name = "aira-internal-web"
}
