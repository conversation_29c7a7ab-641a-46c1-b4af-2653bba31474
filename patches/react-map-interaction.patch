diff --git a/dist/react-map-interaction.js b/dist/react-map-interaction.js
index bbf7e894808b7c7729fd5527d383b8e7beb70ed1..ad19176970f30882d711de45680d87d96eb336a0 100644
--- a/dist/react-map-interaction.js
+++ b/dist/react-map-interaction.js
@@ -2,7 +2,7 @@
 	if(typeof exports === 'object' && typeof module === 'object')
 		module.exports = factory(require("prop-types"), require("react"));
 	else if(typeof define === 'function' && define.amd)
-		define(["prop-types", "React"], factory);
+		define(["prop-types", "react"], factory);
 	else if(typeof exports === 'object')
 		exports["ReactMapInteraction"] = factory(require("prop-types"), require("react"));
 	else
