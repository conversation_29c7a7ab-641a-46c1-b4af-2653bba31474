#!/bin/bash

# This runs in the Docker container to run tests in the CI. 

all_changed_files=$(cat CHANGED_FILES)
changed_files_not_in_src=$(cat CHANGED_FILES | grep -v '^src/')

if [ -n "$changed_files_not_in_src" ]; then
    echo "Files outside of src/ changed: $changed_files_not_in_src"
    echo "Running all tests."
    set -x
    vitest run 
else 
    echo "Only files inside src/ changed, running tests related to those files"
    set -x
    vitest related --run $(cat CHANGED_FILES)
fi
