const fs = require('fs');
const path = require('path');
const ts = require('typescript');
const axios = require('axios');
const querystring = require('querystring');
const readline = require('readline');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
require('dotenv').config({ path: path.join(__dirname, '../.env') }) // Load environment variables from .env file
const API_TOKEN = process.env.POEDITOR_API_TOKEN; // Your POEditor API token
const PROJECT_ID = 683645; // Your POEditor project ID

const languages = ['en-gb', 'it', 'de']

function translationsToSimpleJsonMap(translationsData) {
  const result = {};
  for (const data of translationsData) {
    result[data.term] = data.definition;
  }
  return result;
}


async function fetchTranslations(languageCode) {
  try {
    const formData = new FormData();
    formData.set('api_token', API_TOKEN);
    formData.set('id', PROJECT_ID);
    formData.set('language', languageCode);
    formData.set('type', 'json');
    formData.set('order', 'terms');
    const response = await fetch('https://api.poeditor.com/v2/projects/export', {
      method: 'POST',
      body: formData,
    });
    const json = await response.json();
    const translationsUrl = json.result.url;

    const translationsDataResponse = await fetch(translationsUrl);
    const translationsData = await translationsDataResponse.json();

    return translationsData;
  } catch (error) {
    console.log(`Error when fetching translations for ${languageCode}: `, error);
    process.exit(1);
  }
}

async function fetchAndSaveTranslations(languageCode) {
  const rawTranslations = await fetchTranslations(languageCode);
  const translations = translationsToSimpleJsonMap(rawTranslations);
  const json = JSON.stringify(translations, null, 2);
  const contents = `\
// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = ${json};
`;
  const outputPath = path.join(__dirname, '..', 'public', 'locales', languageCode, 'messages.ts');
  
  // Ensure the directory exists
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  fs.writeFileSync(outputPath, contents);
}

async function addCommitWithChanges() {
  const commitMessage = 'Update translations';
  try {
    // First, add the translation files to staging
    console.log('Staging translation files...');
    const { stdout: addStdout, stderr: addStderr } = await execAsync('git add public/locales/*/messages.ts');
    console.log('Git add output:', addStdout);
    if (addStderr) {
      console.error('Git add stderr:', addStderr);
    }

    // Then commit the changes
    console.log('Committing changes...');
    const { stdout, stderr } = await execAsync(`git commit -m "${commitMessage}"`);
    console.log('Git commit output:', stdout);
    if (stderr) {
      console.error('Git commit stderr:', stderr);
    }
  } catch (error) {
    console.error('Error committing changes:', error.message);
  }
}

async function getTranslationsForAllLanguagesFromPOEditor() {
  for (const languageCode of languages) {
    await fetchAndSaveTranslations(languageCode);
  }
}

function getTranslationTerms() {
  const termMap = {};

  // Get all files in src directory
  function getAllFiles(directory) {
    const files = fs.readdirSync(directory);
    files.forEach((file) => {
      const filePath = path.join(directory, file);
      const fileStat = fs.statSync(filePath);
      if (fileStat.isFile()) {
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        // Find react-intl translation terms in the file content
        const formattedMessageRegex = /<FormattedMessage\s*id=['"]([^'"]+)['"](.*?)\s*(?:defaultMessage=['"]([^'"]+)['"])?/g;
        let reactIntlMatch;
        while ((reactIntlMatch = formattedMessageRegex.exec(fileContent)) !== null) {
          const term = reactIntlMatch[1];
          // check if defaultMessage is present
          const defaultMessageMatch = reactIntlMatch[3] ? reactIntlMatch[3] : '';
          termMap[term] = defaultMessageMatch;
        }

        // Find intl translation terms in the file content
        const intlRegex = /formatMessage\(\{[^}]*id:\s*['"]([^'"]+)['"][^}]*defaultMessage:\s*['"]([^'"]+)['"][^}]*\}\)/g;
        let intlMatch;
        while ((intlMatch = intlRegex.exec(fileContent)) !== null) {
          const term = intlMatch[1];
          // check if defaultMessage is present
          const defaultMessageMatch = intlMatch[2] ? intlMatch[2] : '';
          termMap[term] = defaultMessageMatch;
        }
      } else if (fileStat.isDirectory()) {
        const subDirectory = path.join(directory, file);
        getAllFiles(subDirectory);
      }
    });
  }

  getAllFiles(path.join(__dirname, '../src'));

  // Construct the correct path to the TypeScript file
  const englishTranslationsPath = path.join(__dirname, '..', 'public', 'locales', 'en-gb', 'messages.ts');
  console.log('Looking for English translations at:', englishTranslationsPath);

  if (!fs.existsSync(englishTranslationsPath)) {
    console.error('The file does not exist:', englishTranslationsPath);
    return;
  }

  // Read and parse the TypeScript file
  const englishTranslationsContent = fs.readFileSync(englishTranslationsPath, 'utf-8');
  const englishTranslations = eval(
    ts.transpileModule(englishTranslationsContent, { compilerOptions: { module: ts.ModuleKind.CommonJS } }).outputText,
  );

  const missingTerms = [];
  const missingTranslations = [];
  for (const term in termMap) {
    if (!englishTranslations.hasOwnProperty(term)) {
      missingTerms.push(term);
      if (termMap[term] !== '') {
        missingTranslations.push({ term, defaultMessage: termMap[term] });
      }
    }
    // Check for empty english translations that are not empty in the termMap
    if (englishTranslations.hasOwnProperty(term) && termMap[term] !== '' && englishTranslations[term] === "") {
      missingTranslations.push({ term, defaultMessage: termMap[term] });
    }
  }

  const termsData = missingTerms.map((term) => {
    return {
      term,
      context: '',
      plural: '',
      reference: '',
      comment: '',
    };
  });

  const translationsData = missingTranslations.map((translation) => {
    return {
      term: translation.term,
      context: '',
      translation: {
        content: translation.defaultMessage,
      },
    };
  });

  if (missingTerms.length === 0 && missingTranslations.length === 0) {
    console.log('No missing terms or translation found');
    return;
  }

  if (missingTerms.length > 0) {
  console.log('The following terms are missing in the current branch:');
  console.log(missingTerms);
  } else {
    console.log('All terms are present in the current branch');
  }
  if (missingTranslations.length > 0) {
  console.log('The following translations are missing in the current branch:');
  console.log(missingTranslations);
  }
  console.log('If these terms or translation have already been added to POEditor, they won\'t be overriden');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Do you want to proceed? (yes/no): ', async (answer) => {
    if (answer.toLowerCase() === 'yes') {
      
      const addTermsToPOEditor = async (termsData) => {
        try {
          const response = await axios.post(
            'https://api.poeditor.com/v2/terms/add',
            querystring.stringify({
              api_token: API_TOKEN,
              id: PROJECT_ID,
              data: JSON.stringify(termsData),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            },
          );
          console.log(response.data);
        } catch (error) {
          console.error(error);
        }
      };
      const addTranslationsToPOEditor = async (translationsData) => {
        try {
          const response = await axios.post(
            'https://api.poeditor.com/v2/translations/add',
            querystring.stringify({
              api_token: API_TOKEN,
              id: PROJECT_ID,
              language: 'en-gb',
              data: JSON.stringify(translationsData),
            }),
            {
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
            },
          );
          console.log(response.data);
        } catch (error) {
          console.error(error);
        }
      };
      
      // Execute POEditor operations sequentially and wait for completion
      if (missingTerms.length > 0) {
        console.log('Adding terms to POEditor:');
        await addTermsToPOEditor(termsData);
      }
      
      if (missingTranslations.length > 0) {
        console.log('Adding translations to POEditor (waiting 11 seconds for rate limit)...');
        await new Promise(resolve => setTimeout(resolve, 11000));
        await addTranslationsToPOEditor(translationsData);
      }
      
      console.log('POEditor operations completed.');
    
    } else {
      console.log('Operation cancelled');
    }

    rl.close();
    
    // Ask about creating PR after all operations are complete
    setTimeout(() => {
      const prRL = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      prRL.question('Do you want to add a new commit with the changes? (yes/no): ', async (answer) => {
        if (answer.toLowerCase() === 'yes') {
          try {
            await getTranslationsForAllLanguagesFromPOEditor();
            await addCommitWithChanges();
          } catch (error) {
            console.error(error);
          }
        } else {
          console.log('Operation cancelled');
        }
        prRL.close();
      });
    }, 100);
  });
}

getTranslationTerms();
