#!/usr/bin/env bash

source="/tmp/material"

if [ -d "$source" ]; then
    echo "Material icons already downloaded; remove "$source" to redownload."
else
    tarball="$(npm view @mui/icons-material dist.tarball)"
    echo "Downloading material icons from $tarball..."
    mkdir -p "$source"
    pushd "$source" >& /dev/null
    curl "$tarball" | tar xzf -
    popd >& /dev/null
fi

icons='
Add
ArrowBack
ArrowForward
BathtubOutlined
BedOutlined
CalendarViewWeekOutlined
CheckCircle
CheckOutlined
Close
CloseOutlined
ContentCopy
ContentCopyOutlined
ContentCutOutlined
Delete
DeleteOutline
Download
Edit
Error
ExpandMore
FamilyRestroom
FileDownloadOutlined
HourglassTop
HourglassTopOutlined
InfoOutlined
Lock
LockOpen
OpenInNewOutlined
PersonOutlined
Public
Publish
Save
SaveAltOutlined
SaveOutlined
Search
SettingsOutlined
Timer
'

echo "Copying $(echo $icons | wc -w) icons..."
for i in $icons; do 
    perl -pe 's/^import\screateSvgIcon.*$/import { createSvgIcon } from '\''\@mui\/material'\'';/' < \
         "$source/package/esm/$i.js" > ../src/ui/components/Icons/material/$i.js
done

print-exports() {
    for i in $icons; do 
        echo "export { default as $i } from './$i';"
    done
}

echo "Generating index.js..."
print-exports > ../src/ui/components/Icons/material/index.js

echo "Done."
