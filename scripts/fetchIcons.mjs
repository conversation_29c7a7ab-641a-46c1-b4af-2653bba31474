// @ts-nocheck
import fetch from 'node-fetch';
import fs from 'fs';
import { transform } from '@svgr/core';
import path from 'path';

const FIGMA_TOKEN = 'YOUR_FIGMA_TOKEN';
const FILE_ID = 'qt0yxlUL6z08wouHYJDXmy';

// First you need to create a figma api token by going to figma - accounts - personal access tokens

// To run this script you have to add the following packages:
// pnpm add @svgr/core @svgr/plugin-jsx @svgr/plugin-prettier @svgr/plugin-svgo node-fetch
// then add "fetchIcons": "node ./scripts/fetchIcons.mjs" to package.json scripts
// and run it with pnpm fetchIcons

// The icons are output to an 'icons' directory in the root
// Copy them into the standard icons directory in the project and update the index.ts file
// TODO: Fix output directory and proper updating of the index.ts file

async function fetchIcons() {
  const componentsUrl = `https://api.figma.com/v1/files/${FILE_ID}/components`;

  // Fetch components data
  const componentsResponse = await fetch(componentsUrl, {
    headers: {
      'X-Figma-Token': FIGMA_TOKEN,
    },
  });

  const componentsData = await componentsResponse.json();
  let components = componentsData.meta.components;

  if (!components || components.length === 0) {
    console.log('No components found in the file.');
    return;
  }

  // Filter components to only include those with 'size=300' in the name
  components = components.filter((component) => component.name.includes('size=300'));

  if (components.length === 0) {
    console.log('No components with "size=300" found.');
    return;
  }

  // Get component IDs
  const componentIds = components.map((component) => component.node_id);

  // Fetch node data for components
  const nodeIds = [...new Set(components.map((component) => component.node_id))];
  const nodesUrl = `https://api.figma.com/v1/files/${FILE_ID}/nodes?ids=${nodeIds.join(',')}`;

  const nodesResponse = await fetch(nodesUrl, {
    headers: {
      'X-Figma-Token': FIGMA_TOKEN,
    },
  });

  const nodesData = await nodesResponse.json();
  const nodes = nodesData.nodes; // { [node_id]: node_data }

  // Create a mapping from componentSetId to component set names
  const componentSetIds = [];
  for (const nodeId in nodes) {
    const component = nodes[nodeId].components?.[nodeId];
    if (component && component.componentSetId) {
      componentSetIds.push(component.componentSetId);
    }
  }

  // Fetch the component sets
  const componentSetIdsUnique = [...new Set(componentSetIds)];
  const componentSetsUrl = `https://api.figma.com/v1/files/${FILE_ID}/nodes?ids=${componentSetIdsUnique.join(',')}`;

  const componentSetsResponse = await fetch(componentSetsUrl, {
    headers: {
      'X-Figma-Token': FIGMA_TOKEN,
    },
  });

  const componentSetsData = await componentSetsResponse.json();
  const componentSets = componentSetsData.nodes; // { [node_id]: node_data }

  // Map componentSetId to component set name
  const componentSetIdToName = {};
  for (const setId in componentSets) {
    const componentSetNode = componentSets[setId];
    const componentSetName = componentSetNode?.componentSets?.[setId]?.name;
    if (componentSetName) {
      componentSetIdToName[setId] = componentSetName;
    }
  }

  const indexExports = []; // To collect export statements for index.js

  // Batch processing to handle large numbers of components
  const batchSize = 50; // Adjust batch size if needed

  for (let i = 0; i < componentIds.length; i += batchSize) {
    const batchIds = componentIds.slice(i, i + batchSize);

    // Fetch SVG URLs for the batch
    const imagesUrl = `https://api.figma.com/v1/images/${FILE_ID}?ids=${batchIds.join(',')}&format=svg`;

    const imagesResponse = await fetch(imagesUrl, {
      headers: {
        'X-Figma-Token': FIGMA_TOKEN,
      },
    });

    const imagesData = await imagesResponse.json();
    const images = imagesData.images; // { [node_id]: image_url }

    // For each component in the batch
    for (let id of batchIds) {
      const imageUrl = images[id];
      if (!imageUrl) {
        console.log(`No image URL found for component ${id}`);
        continue;
      }

      // Fetch the SVG data
      const svgResponse = await fetch(imageUrl);
      const svgData = await svgResponse.text();

      // Get node data
      const nodeDataEntry = nodes[id];
      if (!nodeDataEntry) {
        console.log(`No node data found for component ${id}`);
        continue;
      }
      const componentData = nodeDataEntry.components?.[id];
      if (!componentData) {
        console.log(`No component data found for node ${id}`);
        continue;
      }

      // Extract the desired component name using componentSetId
      let componentName;
      if (componentData.componentSetId) {
        const componentSetName = componentSetIdToName[componentData.componentSetId];
        if (componentSetName) {
          componentName = componentSetName;
        }
      }

      if (!componentName) {
        // Fallback to component's own name
        componentName = componentData.name || `Icon_${id}`;
      }

      // Convert component name to CamelCase
      let camelCaseName = toCamelCase(componentName);

      // Ensure the name is a valid TypeScript identifier
      if (/^\d/.test(camelCaseName)) {
        camelCaseName = `Icon${camelCaseName}`;
      }

      // Log the component name
      console.log(`Processing component: ${camelCaseName}`);

      // Custom SVGR template to match the desired output
      const customTemplate = ({ componentName, jsx }, { tpl }) => {
        return tpl`
import { SVGProps } from 'react';

export function ${componentName}(props: SVGProps<SVGSVGElement>) {
  return (
    ${jsx}
  );
}
`;
      };

      // Convert SVG to React component with TypeScript output and custom template
      const tsxCode = await transform(
        svgData,
        {
          plugins: ['@svgr/plugin-svgo', '@svgr/plugin-jsx', '@svgr/plugin-prettier'],
          icon: '24px',
          typescript: true,
          template: customTemplate,
          prettierConfig: {
            semi: true,
            singleQuote: true,
            trailingComma: 'all',
          },
        },
        { componentName: camelCaseName },
      );

      // Insert an extra blank line after the import statement
      const finalCode = tsxCode.replace(/(import.*;)\n/, '$1\n\n');

      // Replace specific fill color with 'currentColor'
      const finalCodeWithColor = finalCode.replace(/fill="#222226"/g, 'fill="currentColor"');

      // Log the generated code
      console.log(`Generated code for ${camelCaseName}:`);
      console.log(finalCodeWithColor);

      // Ensure output directory exists
      const outputDir = './icons';
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      // Save the component to a TSX file inside its own folder
      const filePath = path.join(outputDir, `${camelCaseName}.tsx`);
      fs.writeFileSync(filePath, finalCodeWithColor);
      console.log(`Created component ${camelCaseName}.tsx`);

      // Add export statement for index.js
      indexExports.push(`export * from './${camelCaseName}';`);
    }
  }

  // Generate index.js file
  const indexPath = path.join('./icons', 'index.js');
  fs.writeFileSync(indexPath, indexExports.join('\n'));
  console.log('Generated index.js file.');
}

// Function to convert a string to CamelCase
function toCamelCase(str) {
  const convertedString = str
    .replace(/^[^a-zA-Z]+|[^a-zA-Z0-9]+/g, ' ')
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
  return `${convertedString}Icon`;
}

fetchIcons().catch((err) => console.error(err));
