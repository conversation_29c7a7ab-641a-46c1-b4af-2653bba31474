#!/usr/bin/env bash

# (No "set -e" here as we want to handle errors ourselves and provide useful error messages.)

# This script is useful to test the various Dockerfile stages locally.
# For example, to run the tests exactly as they would run in the CI, you can run:
#   ./scripts/docker.sh tester
# To build the app stage, you can run:
#   ./scripts/docker.sh app
# Then you can run the app with:
#   docker run -it --rm -p 3000:3000 aerospace-app
#
# It can also be useful to build the builder stage to ensure that the build process works correctly.
# Just run ./scripts/docker.sh to get more info. 
#
# NOTE: you need to be authenticated to aira-tools to run this script, as it retrieves the CodeArtifact token.

# ---
# Some helpers

red() { tput setaf 1; echo -n "$@"; tput sgr0; }
green() { tput setaf 2; echo -n "$@"; tput sgr0; }
yellow() { tput setaf 3; echo -n "$@"; tput sgr0; }

loginfo() {
  # Prefix with a nice yellow Aira Emoji to separate our output from the output of other tools. 
  echo "$(yellow "🅰 ") $1" >&2
}

logerror() {
  echo "$(red "🅰  ERROR:") $1" >&2
}

show_help() {
  loginfo "Usage: $0 [OPTIONS] <target-stage>"
  loginfo
  loginfo "Available stages:"
  for i in $(grep -i "^FROM.*AS" main.Dockerfile | awk '{print $NF}'); do
    loginfo "  - $i"
  done
  loginfo
  loginfo "Options:"
  loginfo "  --help, -h          Show this help message"
  loginfo "  --force-tests, -f   Force tests to run in the Docker build – no caching"
}

# ---
# Main script 

POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -f|--force-tests)
      FORCE_TESTS=YES
      shift
      ;;
    -*|--*)
      logerror "Unknown option $1"
      show_help
      exit 1
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

set -- "${POSITIONAL_ARGS[@]}" # restore positional parameters

TARGET="$1"

if [ -z "$TARGET" ]; then
  logerror "No target stage specified."
  show_help
  exit 1
fi

pretty_target="$(green "$TARGET")"

loginfo "Building the $pretty_target Docker stage of Aerospace."

# The CHANGED_FILES file will contain the list of files that have changed since the last commit.
# This is used by vitest as invoked in the Dockerfile to determine which tests to run. 
# When running locally through this script, you probably want to run all tests. This is a 
# weird way of making it do that (see scripts/test-ci.sh)
echo README.md > CHANGED_FILES

# Retrieve CodeArtifact token
export CODEARTIFACT_NPM_TOKEN=$(aws codeartifact get-authorization-token \
  --domain airahome \
  --domain-owner ************ \
  --query authorizationToken \
  --output text)

if [ -z "$CODEARTIFACT_NPM_TOKEN" ]; then
  echo
  logerror "Failed to get CodeArtifact token, needed to run the Aerospace docker builds."
  logerror "Make sure you are authenticated to the tools account on AWS."
  logerror "The setup is documented here:"
  logerror "  https://github.com/airahome/infra/blob/main/documentation/PERSONAL.md"
  exit 1
fi

if !(aws ecr get-login-password | docker login --username AWS --password-stdin "************.dkr.ecr.eu-north-1.amazonaws.com"); then
  logerror "Failed to log in to ECR, needed to fetch the base image."
  logerror "Make sure you are authenticated to the tools account on AWS."
  logerror "The setup is documented here:"
  logerror "  https://github.com/airahome/infra/blob/main/documentation/PERSONAL.md"
  exit 1
fi

if [[ $FORCE_TESTS == "YES" ]]; then
  loginfo "Forcing tests to run in the Docker build."
  TEST_CACHE_BUST=$(date +%s)
else
  TEST_CACHE_BUST=0
fi

# Build the specified stage
docker buildx build \
  --target="$TARGET" \
  --tag "aerospace-${TARGET}" \
  --file ./main.Dockerfile \
  --secret type=env,id=npm_token,src=CODEARTIFACT_NPM_TOKEN \
  --build-arg TEST_CACHE_BUST=${TEST_CACHE_BUST} \
  .
if [ $? -ne 0 ]; then
  logerror
  logerror "Failed to build the Docker image for the $pretty_target stage."
  logerror "Inspect the output using:"
  logerror
  logerror "  docker buildx history logs 2>&1 | less -r"
  exit 1
fi
loginfo "Successfully built the Docker image for the $pretty_target stage."
if [ "$TARGET" = "app" ]; then
  loginfo
  loginfo "You can now run the image with:"
  loginfo "  docker run -it --rm -p 3000:3000 aerospace-app"
fi
loginfo
loginfo "You can shell into the image for inspection with:"
loginfo "  docker run -it --rm aerospace-${TARGET} /bin/bash"
