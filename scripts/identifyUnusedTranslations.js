const fs = require('fs');
const path = require('path');
const ts = require('typescript');
require('dotenv').config({ path: path.join(__dirname, '../.env') }) // Load environment variables from .env file

// 
// Identifies translation keys that exist in POEditor, but might not
// exist in the Aerospace codebase. The user should then review these 
// possible unused keys and remove them from POEditor if they are 
// actually unused.
//
// Usage: node ./scripts/removeUnusedTeram.js
//

function identifyUnusedTranslations() {

  function countKeyUsageByFile(directory, key) {
    const files = fs.readdirSync(directory);
    return files.reduce((count, file) => {
      const filePath = path.join(directory, file);
      const fileStat = fs.statSync(filePath);
      if (fileStat.isFile()) {
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        return count + fileContent.includes(key) ? 1 : 0;

      } else if (fileStat.isDirectory()) {
        const subDirectory = path.join(directory, file);
        return count + countKeyUsageByFile(subDirectory, key);
      }
    }, 0);
  }

  // Construct the correct path to the TypeScript file
  const englishTranslationsPath = path.join(__dirname, '../public/locales/en-gb/messages.ts');
  console.log('Looking for English translations at:', englishTranslationsPath);

  if (!fs.existsSync(englishTranslationsPath)) {
    console.error('The file does not exist:', englishTranslationsPath);
    return;
  }

  // Read and parse the TypeScript file
  const englishTranslationsContent = fs.readFileSync(englishTranslationsPath, 'utf-8');
  const englishTranslations = eval(
    ts.transpileModule(englishTranslationsContent, { compilerOptions: { module: ts.ModuleKind.CommonJS } }).outputText,
  );

  // For each key in the POEditor files, search the entire codebase for it.
  // Not efficient at all, but sufficient.
  let unusedKeyCount = 0;
  console.log("The following keys could not be found in the codebase: ")
  for (const key of Object.keys(englishTranslations)) {
    const usedInFileCount = countKeyUsageByFile(path.join(__dirname, '../src'), key);
    if (usedInFileCount === 0) {
      console.log(key);
      unusedKeyCount+=1;
    }
  }
  console.log(`Total potential unused keys ${unusedKeyCount} (of ${Object.keys(englishTranslations).length})`);
}

identifyUnusedTranslations();
