import * as fs from 'fs';

const languages = ['en-gb', 'it', 'de'] as const;
type LanguageCode = (typeof languages)[number];

function translationsToSimpleJsonMap(translationsData: { term: string; definition: string }[]) {
  const result: Record<string, string> = {};
  for (const data of translationsData) {
    result[data.term] = data.definition;
  }
  return result;
}

function getEnvironmentVariable(key: string) {
  const value = process.env[key];
  if (!value) {
    console.error(`Envirornment variable ${key} is not set`);
    process.exit(1);
  }
  return value;
}

async function fetchTranslations(languageCode: LanguageCode) {
  const apiToken = getEnvironmentVariable('POEDITOR_API_TOKEN');
  const projectId = getEnvironmentVariable('POEDITOR_PROJECT_ID');

  try {
    const formData = new FormData();
    formData.set('api_token', apiToken);
    formData.set('id', projectId);
    formData.set('language', languageCode);
    formData.set('type', 'json');
    formData.set('order', 'terms');
    const response = await fetch('https://api.poeditor.com/v2/projects/export', {
      method: 'POST',
      body: formData,
    });
    const json = await response.json();
    const translationsUrl = json.result.url;

    const translationsDataResponse = await fetch(translationsUrl);
    const translationsData = await translationsDataResponse.json();

    return translationsData;
  } catch (error) {
    console.log(`Error when fetching translations for ${languageCode}: `, error);
    process.exit(1);
  }
}

async function fetchAndSaveTranslations(languageCode: LanguageCode) {
  const rawTranslations = await fetchTranslations(languageCode);
  const translations = translationsToSimpleJsonMap(rawTranslations);
  const json = JSON.stringify(translations, null, 2);
  const contents = `\
// NOTE: This file is automatically generated from poeditor, don't edit directly! See README.
export const messages = ${json};
`;
  fs.writeFileSync(`../public/locales/${languageCode}/messages.ts`, contents);
}

for (const languageCode of languages) {
  await fetchAndSaveTranslations(languageCode);
}
