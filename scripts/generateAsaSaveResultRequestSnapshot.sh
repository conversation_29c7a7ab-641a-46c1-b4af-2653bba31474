#!/usr/bin/env bash
# Run only using the root package.json:
# pnpm generateAsaResultSnaphot


# Runs the asaHouseSaveResultsTest test
# If it fails, extracts the actual expected data
# Save that as json to asa_house_request_report_resuts.json (overwrite)

jest -f ./src/tests/heat-loss/asaHouseSaveResultsTest.test.tsx --all --json --outputFile=asa_save_test_output.json
test_exit_code=$?
if [ $test_exit_code -ne 0 ]; then
  echo "Generating results file..."
  cat asa_save_test_output.json | jq '.testResults[0].assertionResults[0].failureDetails[0].matcherResult.actual' > ./src/tests/heat-loss/asa_house_request_report_results.json
  echo "Done"
fi
rm ./asa_save_test_output.json