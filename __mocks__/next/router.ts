// __mocks__/next/router.ts
import { vi } from 'vitest';

export function createRouterMock() {
  return {
    route: '/',
    pathname: '/',
    query: {},
    asPath: '/',
    basePath: '',
    isLocaleDomain: false,
    isFallback: false,
    isReady: true,
    isPreview: false,
    push: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    prefetch: () => Promise.resolve(),
    beforePopState: vi.fn(),
    events: {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    },
  };
}

// Export a vi.fn() wrapper so it can be changed per test
export const useRouter = vi.fn(() => createRouterMock());

export function resetRouterMock() {
  useRouter.mockImplementation(() => createRouterMock());
}

export default {
  useRouter,
};
