import type { Preview } from '@storybook/react';
import React from 'react';
import { IntlProvider } from 'react-intl';
import { createTheme } from '@mui/material';
import { messages } from '../public/locales/en-gb/messages';
import { AiraThemeProvider } from '@ui/theme/AiraThemeProvider';
import { foregroundThemes } from '@ui/theme/componentsThemes';
import { theme } from '@ui/theme/theme';
import '@ui/styles/globals.css';
import { beige } from '@ui/theme/colors';

const preview: Preview = {
  decorators: [
    (Story) => {
      const darkTheme = createTheme(theme, foregroundThemes.dark);
      return (
        <IntlProvider locale="en" messages={messages}>
          <AiraThemeProvider theme={darkTheme}>
            <Story />
          </AiraThemeProvider>
        </IntlProvider>
      );
    },
  ],
  parameters: {
    layout: 'centered',
    backgrounds: {
      values: [
        { name: 'white', value: '#ffffff' },
        { name: 'beige', value: beige[200] },
        { name: 'grey', value: '#f5f5f5' },
        { name: 'dark', value: '#1c1c1c' },
      ],
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
