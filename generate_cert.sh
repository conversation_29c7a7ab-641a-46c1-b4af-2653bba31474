#!/bin/bash

# This is a mac specific script

# prevent storing commands in history
set +o history

# Start a subshell to isolate sensitive operations
(

    # Create a .auth directory in the user root
    AUTH_DIR="$HOME/.auth"
    mkdir -p "$AUTH_DIR"
    chmod 700 "$AUTH_DIR"  # Ensure the directory is only accessible by the user

    # Get input from the user for their client ID
    read -p "Enter your auth client ID as you supplied to the cloud team in a previous step (typically 'first-name.last-name' as it is in your airahome email address): " AUTH_CLIENT_ID


    # Check there is already a certificate registered with the AUTH_CLIENT_ID
    NUM_REGISTERED_CERTIFICATES=$(curl \
        "http://localhost:8080/auth/certificate/client/${AUTH_CLIENT_ID}" \
        -H "accept: application/json" \
        --silent \
         | node -e 'process.stdout.write(JSON.parse(require("fs").readFileSync(0, "utf-8")).certificates.length.toString())')

    if [ "$?" -ne 0 ]; then
        echo "Failed to connect to the server."
        exit 1
    fi

    if [ "$NUM_REGISTERED_CERTIFICATES" -ne "0" ]; then
        echo "You already have $NUM_REGISTERED_CERTIFICATES certificates registered with the client ID $AUTH_CLIENT_ID."
        exit 1
    fi

    # Check if the curl request was successful, if 404 then client ID is not registered with the cloud team,
    # and otherwise the is some problem connecting to the server

    echo "certificate is not registered with the cloud team."

    # Set the key and CSR filenames
    KEY_FILE="$AUTH_DIR/${AUTH_CLIENT_ID}.key"
    CSR_FILE="$AUTH_DIR/${AUTH_CLIENT_ID}.csr"

    # Check if the key or CSR files already exist
    if [ -f "$KEY_FILE" ] || [ -f "$CSR_FILE" ]; then
        echo "Warning: The following files already exist:"
        [ -f "$KEY_FILE" ] && echo "  - $KEY_FILE"
        [ -f "$CSR_FILE" ] && echo "  - $CSR_FILE"
    
        # Ask the user if they want to continue
        read -p "Do you want to overwrite these files? (y/N): " choice
        
        case "$choice" in
            y|Y ) echo "Proceeding with the operation...";;
            * ) echo "Operation aborted."; exit 1;;
        esac
    fi


    # Generate a secure password in Keychain and handle errors
    if ! security add-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD" -w "$(openssl rand -base64 32)" -T ""; then
        echo "Failed to store password in Keychain."
        exit 1
    fi

    # Generate a private key securely and handle errors
    if ! openssl genpkey \
        -pkeyopt ec_paramgen_curve:secp521r1 \
        -algorithm EC \
        -aes-256-cbc \
        -out "$KEY_FILE" \
        -pass pass:"$(security find-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD" -w)"; then
        echo "Failed to generate private key."
        security delete-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD"  # Remove the password from Keychain if the key generation fails
        exit 1
    fi

    chmod 600 "$KEY_FILE"  # Ensure the key file is only accessible by the user




    # Generate a certificate signing request (CSR) securely and handle errors
    if ! openssl req -new \
        -key "$KEY_FILE" \
        -sha512 \
        -subj "/CN=${AUTH_CLIENT_ID}/C=SE/ST=Stockholm/L=Stockholm/O=Aira" \
        -out "$CSR_FILE" \
        -passin pass:"$(security find-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD" -w)"; then
        echo "Failed to generate CSR."
        rm "$KEY_FILE"  # Remove the key file if the CSR generation fails
        security delete-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD"  # Remove the password from Keychain if the CSR generation fails
        exit 1
    fi

    chmod 600 "$CSR_FILE"  # Ensure the CSR file is only accessible by the user

    # Post the CSR to the certificate endpoint
    CSR_DATA=$(cat "$CSR_FILE")
    RESPONSE=$(curl -X 'POST' \
        'http://localhost:8080/auth/certificate' \
        -H "accept: application/x-x509-user-cert" \
        -H "X-Client-ID: ${AUTH_CLIENT_ID}" \
        -H "Content-Type: application/pkcs10" \
        -d "$CSR_DATA" \
        --write-out "%{http_code}" \
        --silent \
        --output /dev/null)

    # Check if the curl request was successful
    if [ "$RESPONSE" -eq 200 ]; then
        echo "Certificate request was successful."
    elif [ "$RESPONSE" -eq 409 ]; then
        echo "Certificate request failed. The client ID is already has a certificate."
        exit 1
    elif [ "$RESPONSE" -eq 412 ]; then
        echo "Certificate request failed. The client ID is not registered with the cloud team."
        rm "$KEY_FILE" "$CSR_FILE"  # Remove the key and CSR files if the client ID is not registered
        security delete-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD"  # Remove the password from Keychain if the client ID is not registered
        exit 1
    else
        echo "Certificate request failed. An unknown error occurred."
        rm "$KEY_FILE" "$CSR_FILE"  # Remove the key and CSR files if the client ID is not registered
        security delete-generic-password -a "${AUTH_CLIENT_ID}" -s "AUTH_PRIVATE_KEY_PASSWORD"  # Remove the password from Keychain if the client ID is not registered
        exit 1
    fi

    # Output AUTH_CLIENT_ID to the .env file
    echo AUTH_CLIENT_ID=${AUTH_CLIENT_ID} >> .env

    # Output the private key to the .env file
    echo "AUTH_PRIVATE_KEY_PATH_OVERRIDE"=$KEY_FILE >> .env

    echo "Process completed. The private key and Certificate Signing Request are stored in the ~/.auth directory."

)

# Re-enable history
set -o history
