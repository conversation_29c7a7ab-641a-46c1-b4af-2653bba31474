# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# This is following https://yarnpkg.com/getting-started/qa#which-files-should-be-gitignored - "not using Zero-Installs"
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases/*
!.yarn/sdks
!.yarn/versions

# testing
/coverage
/**/__screenshots__/**/*

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# do not commit any .env files to git, except for the .env.example file. https://create.t3.gg/en/usage/env-variables#using-environment-variables
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

.terraform
.idea

*.heatdesign.json

*storybook.log
storybook-static

# vscode
.vscode

# cursor
/.cursor

# playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Used by ./scripts/docker.sh for the Dockerfile setup. 
CHANGED_FILES
.claude

# If you want to have a key and csr for local development, you could put it in .auth-local
.auth-local