{
  "compilerOptions": {
    "target": "es2018",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "checkJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports" : true,
    "jsx": "preserve",
    "incremental": true,
    "noUncheckedIndexedAccess": true,
    "baseUrl": "./src",
    "paths": {
      "@ui/*": [
        "ui/*"
      ],
      "@mocks/*": [
        "../__mocks__/*"
      ],
    },
    "types": [
      "google.maps",
      "node",
      "vitest/globals",
      "@testing-library/jest-dom",
      "@vitest/browser/providers/playwright"
    ]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/*.cjs",
    "**/*.mjs",
    "vitest.config.mts",
    "vitest.setup.mts"
  ],
  "exclude": [
    "node_modules"
  ]
}
