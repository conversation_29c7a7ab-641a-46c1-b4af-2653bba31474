{"technicalSurvey": {"data": [{"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "plan:661cfa58.9028f7ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.25r17g2t86"}], "tags": [{"id": "section.general"}], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.ljhfppsge7"}], "tags": [], "question": "Type of building", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Detached", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q6"}], "tags": [], "question": "Pictures of the outside of the house (all visible sides)*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.h1li9osa8og"}], "tags": [], "question": "Exposed location [R8v4]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.l95tprmo4b8"}], "tags": [{"id": "dwelling.construction-year"}], "question": "Construction year (qualified guess if needed) [wRDe]*", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 2002}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.nji2qfc9h08"}], "tags": [{"id": "dwelling.residents"}], "question": "Max number of potential residents? [nr4c]*", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 1}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q196"}], "tags": [], "question": "Flow rate of cold water (litres / minute)*", "answer": {"$case": "textAnswer", "textAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q197"}], "tags": [], "question": "Pressure of cold water (bar)*", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.2bcd50b6s6"}], "tags": [{"id": "section.extensions"}], "title": "Extensions", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q198"}], "tags": [], "question": "Are there any extensions*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q201"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.9ct4lkgcbo8"}], "tags": [{"id": "section.install-location-outdoor"}], "title": "Outdoor install location", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q8"}], "tags": [{"id": "dwelling.outdoor-install-location-photos"}], "question": "Photos & video of proposed installation location [Fxl2]*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q10"}], "tags": [], "question": "Measure minimum distance to closest window, door, outside socket or air vent [EUbq]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq141"}], "tags": [], "question": "Have you confirmed that the proposed location meets all safety and clearance requirements?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq142"}], "tags": [], "question": "Is there a vent that we need to block?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q11"}], "tags": [], "question": "Distance from outdoor unit to house", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q12"}], "tags": [], "question": "Photos of outdoor primaries route (hint: edit and draw a line on the photo) [PDlS]", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq143"}], "tags": [], "question": "How will the primaries be run?"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q22"}], "tags": [], "question": "How do we deal with condensation water? (Soak away, Piped to drain) [kuBx]", "answer": {"$case": "textAnswer", "textAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q26"}], "tags": [], "question": "Is outdoor trunking required? [gECq]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.k5ea0o7t2u"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73q29.imageSection"}], "tags": [{"id": "section.existing-heat-source"}], "title": "Existing heat source", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q29"}], "tags": [], "question": "Photos of existing heating source and cylinders*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q31"}], "tags": [], "question": "Photos of other components (zone valves, pumps)", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q238"}], "tags": [], "question": "Is there any 8mm pipework in the system*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q240"}], "tags": [], "question": "Is this a one pipe system*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q32"}], "tags": [], "question": "Is there an existing shower pump that needs removing and piping up?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.pbnbfgaj6d8"}], "tags": [], "question": "Number of heating zones*", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q53"}], "tags": [], "question": "Does the house have water underfloor heating?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq162"}], "tags": [], "question": "Photo of flue with surrounding wall", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.upju6qk7gm"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.a8dckn34q28"}], "tags": [{"id": "section.install-location-indoor"}], "title": "Indoor install location", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.4kbdto01q68"}], "tags": [{"id": "dwelling.indoor-install-location-photos"}], "question": "Photos of installation location, including path to it [seEq]*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq164"}], "tags": [], "question": "Have you found a suitable location for the indoor unit with respect to access, weight and space for working?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q64"}], "tags": [], "question": "Minimum height of the path to installation location [aHQS]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q65"}], "tags": [], "question": "Minimum width of the path to installation location [jZV8]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q130"}], "tags": [], "question": "Photos of indoor primaries route (edit and draw a line on the photo) [capd]", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q245"}], "tags": [], "question": "Indoor unit cupboard/space dimensions", "answer": {"$case": "textAnswer", "textAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.jvviqjinbqo"}], "tags": [], "question": "Largest buffer tank size that will fit [5FBY]*"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.26mpm1sfm88"}], "tags": [], "question": "Which unit is being installed? [aIzy]*"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.7ds0j6fous8"}], "tags": [], "question": "Is there an existing D2/drain pipe present? [xcjr]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.2a69op58k18"}], "tags": [], "question": "Have you drawn the pipes from Outdoor to Indoor unit in MagicPlan? [GzmL]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.6l0rgb30ngo"}], "tags": [], "question": "Have you verified that the customer's WiFi reaches the installation location?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.rd7spn68dto"}], "tags": [], "question": "Comments (Potential obstacles on the way, steps, narrow door etc)", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s1"}], "tags": [{"id": "section.default-radiator-data"}], "title": "Radiators", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q217"}], "tags": [], "question": "Take photos of alternative radiator locations (in large rooms, not needed for most bedrooms etc)", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q2"}], "tags": [], "question": "Do you want to set default radiator values?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q107"}], "tags": [], "question": "Detailed photos of radiator, including valves*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q3"}], "tags": [], "question": "Type of heat emitter [PyrS]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Panel [QaSq]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q108"}], "tags": [], "question": "Style? [NJkd]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Compact [HDRQ]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q109"}], "tags": [], "question": "Type [WEjw]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "33 (K3) [Pqo7]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q114"}], "tags": [], "question": "Radiator pipe size [eCIv]", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 0.3, "unit": 1}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q121"}], "tags": [], "question": "Are the radiators performing okay today? [dhPf]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q122"}], "tags": [], "question": "Comment [Bj8F]", "answer": {"$case": "textAnswer", "textAnswer": {"text": "De<PERSON>ult comment for all radiators"}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73q84.imageSection"}], "tags": [{"id": "section.electrical"}], "title": "Electrical", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q84"}], "tags": [], "question": "Photos of electrical meter box (including any isolator)", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq176"}], "tags": [], "question": "Does an isolator need to be installed?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q131"}], "tags": [], "question": "Photo of existing distribution / fuse board showing fuse ratings", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q85"}], "tags": [], "question": "Photo of proposed Aira Fuse box location (dimensions: 259 mm [H] x 234 mm [W] x 113 mm [D])", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq177"}], "tags": [], "question": "Does the cold main have equipotential bonding?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq178"}], "tags": [], "question": "Does the Gas supply have equipotential bonding?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.52oup0pddgg"}], "tags": [], "question": "Looped service? [V7se]"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq175"}], "tags": [], "question": "Is there a generator on site?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.ed70kjhkiho"}], "tags": [], "question": "Phases*"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q133"}], "tags": [], "question": "Earthing arrangement?"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q132"}], "tags": [], "question": "Results from max demand test*", "answer": {"$case": "textAnswer", "textAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq170"}], "tags": [], "question": "DNO: Are you able to see any issues with the supply that might impact the installation?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq171"}], "tags": [], "question": "DNO: Are there any safety concerns with any of the DNO equipment?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq172"}], "tags": [], "question": "DNO: Is there an import or load limiting devices present or proposed for this installation?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.ngsga7e355g"}], "tags": [], "question": "MPAN number for DNO application (make sure it is not blurry)*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.as7v5f56s4o"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.e0jbt1mu9lg"}], "tags": [{"id": "section.other-energy-consuming-producing-goods"}], "title": "Other energy producing / consuming goods", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.rnh64gm6hv8"}], "tags": [], "question": "Does the customer have AC? [j8sh]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq182"}], "tags": [], "question": "Does the customer have an EV charger?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq183"}], "tags": [], "question": "Does the customer have Solar PV?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.8feffdbdq184"}], "tags": [], "question": "Does the customer have a home battery?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q76"}], "tags": [], "question": "Other energy consuming goods [hKRw]*", "answer": {"$case": "multipleChoiceAnswer", "multipleChoiceAnswer": {"choices": [{"text": "Electric Sauna [o4je]", "tags": []}, {"text": "Electric Hot Tub [auBi]", "tags": []}]}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q77"}], "tags": [], "question": "Comments [UCeW]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s3"}], "tags": [{"id": "section.default-floor-data"}], "title": "Floor", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q102"}], "tags": [], "question": "Ground floor type? [m0oQ]"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q103"}], "tags": [], "question": "Is the ground floor insulated?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q105"}], "tags": [], "question": "Are the intermediate floors insulated?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q206"}], "tags": [], "question": "Comments (Future plans for upgrades)", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s2"}], "tags": [{"id": "section.default-roof-data"}], "title": "<PERSON><PERSON>", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q100"}], "tags": [], "question": "Loft insulation thickness*", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q207"}], "tags": [], "question": "Insulation on ceiling or rafters?"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q98"}], "tags": [], "question": "Photos of loft [ntJh]", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q101"}], "tags": [], "question": "Comments [JSrc]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.kbfhmcs7sno"}], "tags": [{"id": "section.default-wall-data"}], "title": "Walls", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q1"}], "tags": [], "question": "Total external wall thickness", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q90"}], "tags": [], "question": "What kind of outside wall structure?"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q96"}], "tags": [], "question": "Inside wall type?"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q97"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.2bcd50b6s7"}], "tags": [{"id": "section.external-door-data"}], "title": "Doors", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q213"}], "tags": [], "question": "External doors"}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q214"}], "tags": [], "question": "Photos of all external doors", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q215"}], "tags": [], "question": "Comments (List year of install on each door, ask customer for estimate)", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s0.7c03oupdbgg"}], "tags": [{"id": "section.default-window-data"}], "title": "Windows", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q216"}], "tags": [], "question": "Comments e.g. are all windows the same age?", "answer": {"$case": "textAnswer", "textAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.jd115eq0crg"}], "tags": [], "question": "Do you want to set default Window values for the whole house?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.2bcd50b6s5"}], "tags": [{"id": "section.pre-installation-requirements"}], "title": "Pre installation requirements", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q195"}], "tags": [], "question": "Is there available parking for our vehicles and space for the materials delivery?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q218"}], "tags": [], "question": "Is a parking permit required?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q219"}], "tags": [], "question": "Do we have an area for safe storage of all materials?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q220"}], "tags": [], "question": "Is the customer aware of any suspected asbestos at the property or is there any sign of asbestos along the proposed route that concerns you?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q227"}], "tags": [], "question": "Is an asbestos survey required?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q223"}], "tags": [], "question": "Do we need to work at height?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q228"}], "tags": [], "question": "Is trenching required?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q230"}], "tags": [], "question": "Do we require lifting equipment?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q232"}], "tags": [], "question": "Is sufficient loft lighting available?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q233"}], "tags": [], "question": "Is the loft space boarded in the area we will be working?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.2bcd50b6q235"}], "tags": [], "question": "Has it been agreed by the customer that the installation team can have access to the customers welfare facilities", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.997b078as6"}], "tags": [{"id": "section.thermostat"}], "title": "Thermostat", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.997b078aq200"}], "tags": [], "question": "Take a photo of the proposed thermostat location*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.997b078aq203"}], "tags": [], "question": "Customer has been informed about open loop setup*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.ec2f4e73s4"}], "tags": [{"id": "section.other"}], "title": "Other", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q126"}], "tags": [], "question": "All objects have been added to the floor plan*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ec2f4e73q0.e19upbasec8"}], "tags": [], "question": "General comments", "answer": {"$case": "textAnswer", "textAnswer": {"text": "<PERSON> was here 2025"}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Project form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "floor:661d0bb1.6f9473ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.5217b6d4e68c43f99f437dbb97c81264"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.70e752f6q1"}], "tags": [], "question": "Does the entire floor have underfloor heating?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Floor level form (old) [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "room:661d0bba.72c83fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.8be33e29s1"}], "tags": [], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.s2k57me7k6g"}], "tags": [], "question": "Build year (if different from main house) [kY9T]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q11"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.b8molmfrlo"}], "tags": [], "title": "Ceiling data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.vhja5o36uqo"}], "tags": [], "question": "Override floor level ceiling data? [IEYt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q12"}], "tags": [], "question": "Are there any roof glazings?", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.gbbrhcilg6"}], "tags": [], "title": "Floor data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.1m82t1tgg2o"}], "tags": [], "question": "Override Floor level floor data? [OiUa]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.rf5g1jkrr9o"}], "tags": [], "question": "Underfloor heating? [gWC2]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Room form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661d0bba.72ebf3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661d0bba.731d1bff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661d0bba.7326abff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661d0bba.733227ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "room:661e7103.01b057ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.8be33e29s1"}], "tags": [], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.s2k57me7k6g"}], "tags": [], "question": "Build year (if different from main house) [kY9T]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q11"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.b8molmfrlo"}], "tags": [], "title": "Ceiling data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.vhja5o36uqo"}], "tags": [], "question": "Override floor level ceiling data? [IEYt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q12"}], "tags": [], "question": "Are there any roof glazings?", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.gbbrhcilg6"}], "tags": [], "title": "Floor data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.1m82t1tgg2o"}], "tags": [], "question": "Override Floor level floor data? [OiUa]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.rf5g1jkrr9o"}], "tags": [], "question": "Underfloor heating? [gWC2]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Room form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "wall_item:661e71d2.f304efff.auto"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.73f16023be294a7393196ea86c76171d"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.ham32ppd65g"}], "tags": [], "question": "Override house Window data? [H8To]*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.d4loa88r65o"}], "tags": [], "question": "Comment [xaoy]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Window form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7103.01c2f3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7103.01d713ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7103.01e763ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7103.01f29bff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "furniture:664b5d19.7e4267ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.9e963288q1.imageSection"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q1"}], "tags": [], "question": "Detailed photos of radiator, including valves*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q2"}], "tags": [], "question": "Do you want to override the project default radiator?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q3"}], "tags": [], "question": "Type of heat emitter [najl]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Bathroom [HSo9]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q14"}], "tags": [], "question": "Finish [FM0j]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Chrome [dMYX]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.vqrquok674o"}], "tags": [], "question": "Radiator pipe size (if different from default) [w0iv]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.a1qq6n1qus"}], "tags": [], "question": "Is the radiator performing ok today? [JAjt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q16"}], "tags": [], "question": "Would a larger radiator fit in this location?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.b6cf77b2q21"}], "tags": [], "question": "Are there additional radiator locations in this room (if you suspect the radiator to be too small)?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.kf3imndo298"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Radiator form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "furniture:664b5d2a.d9fe23ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.9e963288q1.imageSection"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q1"}], "tags": [], "question": "Detailed photos of radiator, including valves*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q2"}], "tags": [], "question": "Do you want to override the project default radiator?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q3"}], "tags": [], "question": "Type of heat emitter [najl]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Panel [QaSq]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q4"}], "tags": [], "question": "Style? [E2zw]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Planar [BufU]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q5"}], "tags": [], "question": "Type [jBYp]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "20 (P2) [cUTl]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.vqrquok674o"}], "tags": [], "question": "Radiator pipe size (if different from default) [w0iv]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.a1qq6n1qus"}], "tags": [], "question": "Is the radiator performing ok today? [JAjt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q16"}], "tags": [], "question": "Would a larger radiator fit in this location?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.b6cf77b2q21"}], "tags": [], "question": "Are there additional radiator locations in this room (if you suspect the radiator to be too small)?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.kf3imndo298"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Radiator form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "room:661e7172.7eaae3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.8be33e29s1"}], "tags": [], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.s2k57me7k6g"}], "tags": [], "question": "Build year (if different from main house) [kY9T]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q11"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.b8molmfrlo"}], "tags": [], "title": "Ceiling data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.vhja5o36uqo"}], "tags": [], "question": "Override floor level ceiling data? [IEYt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q12"}], "tags": [], "question": "Are there any roof glazings?", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.gbbrhcilg6"}], "tags": [], "title": "Floor data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.1m82t1tgg2o"}], "tags": [], "question": "Override Floor level floor data? [OiUa]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.rf5g1jkrr9o"}], "tags": [], "question": "Underfloor heating? [gWC2]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Room form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "wall_item:661e71ab.e2bf33ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.73f16023be294a7393196ea86c76171d"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.ham32ppd65g"}], "tags": [], "question": "Override house Window data? [H8To]*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.d4loa88r65o"}], "tags": [], "question": "Comment [xaoy]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Window form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "wall_item:661e71d2.f304efff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.73f16023be294a7393196ea86c76171d"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.ham32ppd65g"}], "tags": [], "question": "Override house Window data? [H8To]*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.d4loa88r65o"}], "tags": [], "question": "Comment [xaoy]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Window form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "wall_item:667bc0e8.155593ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.9e963288q1.imageSection"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q1"}], "tags": [], "question": "Detailed photos of radiator, including valves*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q2"}], "tags": [], "question": "Do you want to override the project default radiator?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.vqrquok674o"}], "tags": [], "question": "Radiator pipe size (if different from default) [w0iv]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.a1qq6n1qus"}], "tags": [], "question": "Is the radiator performing ok today? [JAjt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q16"}], "tags": [], "question": "Would a larger radiator fit in this location?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.b6cf77b2q21"}], "tags": [], "question": "Are there additional radiator locations in this room (if you suspect the radiator to be too small)?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.kf3imndo298"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Radiator form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "wall_item:667bc0f4.15429fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.9e963288q1.imageSection"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q1"}], "tags": [], "question": "Detailed photos of radiator, including valves*", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q2"}], "tags": [], "question": "Do you want to override the project default radiator?*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q3"}], "tags": [], "question": "Type of heat emitter [najl]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Column [MmsB]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q6"}], "tags": [], "question": "What material? [0raI]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Cast iron [mBdc]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q7"}], "tags": [], "question": "Number of columns [taV8]", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 8}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q8"}], "tags": [], "question": "Depth [0Zqp]", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 0.2, "unit": 1}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q9"}], "tags": [], "question": "No of links [BjDl]", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 2}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.vqrquok674o"}], "tags": [], "question": "Radiator pipe size (if different from default) [w0iv]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.a1qq6n1qus"}], "tags": [], "question": "Is the radiator performing ok today? [JAjt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q16"}], "tags": [], "question": "Would a larger radiator fit in this location?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.b6cf77b2q21"}], "tags": [], "question": "Are there additional radiator locations in this room (if you suspect the radiator to be too small)?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "No", "tags": [{"id": "bool.no"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.9e963288q0.kf3imndo298"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Radiator form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7172.7eb183ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7172.7ec0f7ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7172.7ec9d3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7184.32645fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7184.327ba3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7184.328d13ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7184.329e7fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7172.7ed277ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "furniture:661e71fd.59bae3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.73f16023be294a7393196ea86c76171d"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.ham32ppd65g"}], "tags": [], "question": "Override house Window data? [H8To]*", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.ebe47390q0.d4loa88r65o"}], "tags": [], "question": "Comment [xaoy]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Window form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "room:661e7244.6dc1a3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.8be33e29s1"}], "tags": [], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.s2k57me7k6g"}], "tags": [], "question": "Build year (if different from main house) [kY9T]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q11"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.b8molmfrlo"}], "tags": [], "title": "Ceiling data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.vhja5o36uqo"}], "tags": [], "question": "Override floor level ceiling data? [IEYt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q12"}], "tags": [], "question": "Are there any roof glazings?", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.gbbrhcilg6"}], "tags": [], "title": "Floor data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.1m82t1tgg2o"}], "tags": [], "question": "Override Floor level floor data? [OiUa]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.rf5g1jkrr9o"}], "tags": [], "question": "Underfloor heating? [gWC2]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.9624heiugvg"}], "tags": [], "question": "Type? [yqt8]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Water [QPxw]", "tags": []}}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.v1trdl39dsg"}], "tags": [], "question": "Flow temperature [UGHc]", "answer": {"$case": "numericAnswer", "numericAnswer": {"value": 44}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.778en3ct40o"}], "tags": [], "question": "Pipe spacing [rMCu]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q4"}], "tags": [], "question": "Documentation", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": []}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Room form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7244.6dc823ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7244.6dd96fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7244.6de31fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7244.6ded57ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "floor:661e7306.966fb7ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.5217b6d4e68c43f99f437dbb97c81264"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.70e752f6q1"}], "tags": [], "question": "Does the entire floor have underfloor heating?", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {"choice": {"text": "Yes", "tags": [{"id": "bool.yes"}]}}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Floor level form (old) [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "room:661e7315.80869bff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.8be33e29s1"}], "tags": [], "title": "General", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.s2k57me7k6g"}], "tags": [], "question": "Build year (if different from main house) [kY9T]", "answer": {"$case": "numericAnswer", "numericAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q11"}], "tags": [], "question": "Comments", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.b8molmfrlo"}], "tags": [], "title": "Ceiling data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.vhja5o36uqo"}], "tags": [], "question": "Override floor level ceiling data? [IEYt]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q12"}], "tags": [], "question": "Are there any roof glazings?", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}, {"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "qf.5a6214a9s0.gbbrhcilg6"}], "tags": [], "title": "Floor data", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.1m82t1tgg2o"}], "tags": [], "question": "Override Floor level floor data? [OiUa]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.5a6214a9q0.rf5g1jkrr9o"}], "tags": [], "question": "Underfloor heating? [gWC2]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Room form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7315.808fc7ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7315.80a233ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7315.80abb3ff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "null:661e7315.80b79fff"}], "sections": [{"dataSourceReferences": [{"type": "magicplan.form.section-id", "reference": "disclosure.eccb29cb8ffa4d8f8aec2fbf584b7375"}], "tags": [{"id": "section.autogenerated"}], "title": "", "items": [{"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq0.vdhsvrsd1a8"}], "tags": [], "question": "Is there a heated room on the other side, e.g. a party-wall? [TAE7]", "answer": {"$case": "singleChoiceAnswer", "singleChoiceAnswer": {}}}, {"dataSourceReferences": [{"type": "magicplan.form.field-id", "reference": "qf.35e1094fq1"}], "tags": [], "question": "Comment (e.g. if wall material is different from the rest of the house) [mKj1]", "answer": {"$case": "textAnswer", "textAnswer": {}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": "Wall form [UK]"}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "plan:661cfa58.9028f7ff"}], "sections": [{"dataSourceReferences": [], "tags": [], "title": "Other images", "items": [{"dataSourceReferences": [], "tags": [], "question": "Images", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": [{"id": {"value": "db8dd420-5f95-4ce2-a172-5351a4bc8277"}}, {"id": {"value": "bcd399b9-815d-437d-af06-b6c120d0e5d8"}}]}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": ""}, {"dataSourceReferences": [{"type": "magicplan.symbol.reference", "reference": "radiatorwater:661e7289.58691fff"}], "sections": [{"dataSourceReferences": [], "tags": [], "title": "Other images", "items": [{"dataSourceReferences": [], "tags": [], "question": "Images", "answer": {"$case": "photoAnswer", "photoAnswer": {"photos": [{"id": {"value": "051222a8-d89e-4c92-b928-1288c284c8a0"}}]}}}]}], "iso3166": {"$case": "country", "country": 3}, "language": "en", "title": ""}]}}