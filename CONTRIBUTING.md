# Developer guidelines

Code style and similar guidelines for developers.

We adhere to the company's [Frontend code style guidelines and architecture](https://www.notion.so/35-Frontend-code-style-guidelines-and-architecture-439bcb6c478d4eb4a2c9b15c8fa428ea) (any exceptions should be noted below).

## Testing

We run our unit test suite with [Vitest](https://vitest.dev/). 

There are two "flavors" of these tests:
- Unit tests using React Testing Library (RTL) and JSDOM. These are the ones we have used the most, and they run quickly.
- Browser tests using Vitest's `browser` environment. These run in a real browser. The JSDOM approach shows its limitations and we should move more tests to this environment over time. To be able to run these tests locally, you need to have run `pnpm playwright install`.

Start up `vitest` locally using `pnpm test`. You can filter the tests in various ways, for example by running `pnpm test --project <PERSON><PERSON>er` to just run the Browser tests. See `vitest.config.mts` for details on how to  configure which tests are run in what way.

All these run in the CI on pull requests. If tests are failing on the CI but not locally, it can be useful to run them using Docker locally:
```bash
./scripts/docker.sh tester
```

When writing tests, we recommend to keep tests in the same directory as the file they're testing, with the same name but with a `.test.[extension]` suffix. 

There are also some E2E (end-to-end) tests that are written using Playwright and that run towards a real backend environment. This is currently not automated in the CI, but can be run locally using `pnpm e2e`.

## Naming conventions

These conventions are pulled from the [Google style guide](https://google.github.io/styleguide/tsguide.html).

|Style|Category|
|---|---|
| UpperCamelCase|classes / interfaces / types / enums / decorators / type parameters / component functions in TSX / JSXElement type parameters|
|lowerCamelCase|variables / parameters / functions / methods / properties / module aliases|
|CONSTANT_CASE|global constant values, enum values|

### Examples

#### UpperCamelCase/PascalCase

```ts
class UValue {}

// Do not use "I" as a prefix, like "IError"
interface Error {
  errorCode: string;
  text: string;
}

// Do not use "T" as a prefix, like "TGroundwork"
type Groundwork = {}

// Enums use UpperCamelCase, but enum values use CONSTANT_CASE
enum Animal { DOG, CAT }

// React components (even though they're functions)
function Button() {}
```

#### lowerCamelCase

```tsx
// Variables, functions, methods
const [isOpen, setIsOpen] = useState(false);

function myNamedFunction(myParam) {}

// Module aliases
import { Button } from '@/customComponents/button'

// React props
function MyComponent({ isOpen, radiatorList }) { /* ... */ }
<MyComponent isOpen radiatorList={[/* ... */]} />

// Booleans should be prefixed with "is" or "has"
const hasRadiators = false;
const isDeprecated = true;
```

#### CONSTANT_CASE

```ts
// Enum values (but not enums themselves)
enum Animal { DOG, CAT }

// Constants that should never change, whether or not they're followed by "as const"
const MINUTES_PER_HOUR = 60;
const DAYS_PER_WEEK = 7 as const;
const QUARTERS = [0, 15, 30, 45];
```

