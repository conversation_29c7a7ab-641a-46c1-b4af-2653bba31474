import { defineConfig, devices } from '@playwright/test';
import * as dotenv from 'dotenv';

// Note that this file configures Playwright as it is run by our e2e tests (run with "pnpm e2e")
// We also use Playwright to run our browser-based Vitest tests. Those Playwright instances
// are configured through vitest.config.mts.

dotenv.config();

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.RUNS_ON_CI,
  retries: process.env.RUNS_ON_CI ? 2 : 0,
  workers: process.env.RUNS_ON_CI ? 1 : undefined,
  reporter: process.env.RUNS_ON_CI ? 'dot' : 'list',
  use: {
    baseURL: 'http://localhost:3000',
    screenshot: process.env.RUNS_ON_CI ? 'off' : 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Edge'], channel: 'msedge' },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'pnpm dev',
    url: 'http://127.0.0.1:3000',
    reuseExistingServer: !process.env.RUNS_ON_CI,
  },
});
