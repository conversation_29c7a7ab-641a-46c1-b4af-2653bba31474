{"name": "aira-internal-web", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "check:style": "prettier --check .", "check:types": "tsc --noEmit --tsBuildInfoFile ~/.cache/tsc", "generate:airaTypes": "npx --yes openapi-typescript@7.4.1 ${OPENAPI_URL:-https://web-backend.systest.airahome.com/v3/api-docs} --output ./src/types/airaTypes.ts && sed -i '' 's/${enum}/${string}/g' ./src/types/airaTypes.ts && pnpm lint:fix", "dev": "next dev", "devWithAuthUserInput": "AUTH_PRIVATE_KEY_PASSWORD=$(read -s -p 'Enter the password for the private key: ' PASSWORD && echo $PASSWORD)  next dev", "devWithAuthMac": "./scripts/dev-with-auth-mac.sh", "generateAsaSaveResultRequestSnapshot": "./scripts/generateAsaSaveResultRequestSnapshot.sh", "format": "prettier --write .", "lint": "next lint", "lint:fix": "pnpm format && pnpm lint --fix", "start": "next start --keepAliveTimeout 65000", "test": "vitest", "test:browser": "vitest --browser.headless=false", "e2e": "playwright test", "e2e:watch": "pnpm e2e --ui", "extract:i18n": "formatjs extract 'src/**/*.{js,ts,tsx}' --out-file content/locales/en.json --ignore='**/*.d.ts'", "compile:i18n": "formatjs compile-folder --ast --format simple content/locales content/compiled-locales", "pushNewTermsAndTranslations": "node ./scripts/pushNewTerms.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare:hooks": "husky"}, "lint-staged": {"*.{ts,tsx}": "prettier --write"}, "dependencies": {"@aira/auth-service-wrapper": "1.0.2", "@aira/backoffice-assistant-grpc-client": "0.0.2", "@aira/baseline-calculator-grpc-api": "6.0.0", "@aira/bill-of-materials-grpc-api": "1.3.1", "@aira/grpc-api": "30.4.0", "@aira/hubspot-integration-grpc-api": "4.15.0", "@aira/identity-grpc-api": "7.1.0", "@aira/installation-groundwork-grpc-api": "26.19.0", "@aira/installation-project-grpc-api": "6.2.0", "@aira/magicplan-grpc-api": "5.6.0", "@aira/on-site-dossier-grpc-api": "1.0.1", "@aira/resource-grpc-api": "6.1.1", "@aira/sales-support-grpc-api": "1.7.0", "@aira/service-visit-grpc-api": "1.9.0", "@atlaskit/pragmatic-drag-and-drop": "1.5.0", "@atlaskit/pragmatic-drag-and-drop-auto-scroll": "1.4.0", "@aws-crypto/sha256-js": "5.2.0", "@aws-sdk/client-s3": "^3.839.0", "@aws-sdk/s3-presigned-post": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.839.0", "@bufbuild/protobuf": "2.2.2", "@date-fns/tz": "1.2.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.13.5", "@googlemaps/js-api-loader": "^1.16.10", "@livekit/components-react": "^2.9.14", "@livekit/components-styles": "^1.1.6", "@livekit/track-processors": "^0.6.0", "@mediapipe/tasks-vision": "^0.10.22-rc.20250304", "@mui/material": "^7.2.0", "@mui/system": "^7.2.0", "@mui/x-data-grid": "7.29.9", "@mui/x-date-pickers": "7.29.4", "@next/third-parties": "15.5.3", "@react-querybuilder/dnd": "6.5.1", "@tanstack/react-query": "5.83.0", "@tanstack/react-table": "8.21.3", "@tanstack/react-virtual": "3.13.6", "@tanstack/table-core": "8.21.3", "@tanstack/virtual-core": "^3.13.9", "@trpc/client": "11.5.0", "@trpc/next": "11.5.0", "@trpc/react-query": "11.5.0", "@trpc/server": "11.5.0", "axios": "1.9.0", "csstype": "3.1.3", "d3": "7.9.0", "date-fns": "4.1.0", "deepmerge": "4.3.1", "dompurify": "3.2.6", "framer-motion": "11.2.10", "fuse.js": "7.1.0", "jose": "6.0.8", "json-logic-js": "2.0.5", "leaflet": "1.9.4", "leaflet-defaulticon-compatibility": "0.1.2", "livekit-client": "^2.15.6", "livekit-server-sdk": "^2.13.3", "lodash": "4.17.21", "lru-cache": "11.0.1", "marked": "16.1.2", "mui-tel-input": "9.0.1", "next": "15.5.3", "nice-grpc": "2.1.11", "nice-grpc-common": "2.0.2", "nice-grpc-error-details": "0.2.7", "prom-client": "15.1.3", "prop-types": "15.8.1", "react": "18.3.1", "react-datepicker": "8.4.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.3.1", "react-hook-form": "7.60.0", "react-hot-toast": "2.5.2", "react-intl": "6.6.8", "react-is": "18.3.1", "react-leaflet": "4.2.1", "react-map-interaction": "2.1.0", "react-querybuilder": "6.5.5", "react-signature-canvas": "1.0.7", "socket.io-client": "4.8.1", "superjson": "2.2.2", "tiny-invariant": "1.3.3", "uuid": "11.0.5", "winston": "3.17.0", "zod": "3.24.1", "zustand": "5.0.5"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.23.0", "@formatjs/cli": "6.3.0", "@next/eslint-plugin-next": "^15.3.3", "@open-draft/deferred-promise": "2.2.0", "@playwright/test": "^1.55.0", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-interactions": "8.6.14", "@storybook/addon-links": "8.6.14", "@storybook/blocks": "8.6.14", "@storybook/nextjs": "8.6.14", "@storybook/react": "8.6.14", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "14.6.1", "@types/d3": "7.4.3", "@types/google.maps": "3.58.1", "@types/json-logic-js": "2.0.8", "@types/leaflet": "1.9.17", "@types/lodash": "4.17.16", "@types/node": "22.15.29", "@types/prop-types": "15", "@types/react": "18.3.20", "@types/react-dom": "18.2.13", "@types/react-signature-canvas": "^1", "@types/uuid": "10.0.0", "@vitejs/plugin-react": "4.6.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "babel-plugin-formatjs": "10.5.16", "cross-fetch": "4.1.0", "dotenv": "16.4.5", "eslint": "^9", "eslint-config-next": "15.5.3", "eslint-plugin-react-compiler": "19.0.0-beta-e993439-20250405", "eslint-plugin-react-hooks": "^5.2.0", "husky": "9.1.7", "jsdom": "26.0.0", "lint-staged": "16.1.0", "msw": "2.10.2", "playwright": "1.55.0", "prettier": "3.5.3", "storybook": "8.6.14", "typescript": "5.7.3", "vite": "6.3.5", "vite-plugin-node-polyfills": "0.24.0", "vite-tsconfig-paths": "5.1.4", "vitest": "3.2.4", "webpack": "5.95.0"}, "resolutions": {"tough-cookie": "5.0.0", "csstype": "3.1.3", "@tanstack/virtual-core": "3.13.9"}, "ct3aMetadata": {"initVersion": "7.5.1"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748", "pnpm": {"patchedDependencies": {"react-map-interaction": "patches/react-map-interaction.patch"}, "ignoredBuiltDependencies": ["msw"]}, "overrides": {"react-is": "^18.3.1"}}