// @ts-check
/** @type {import("next").NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  /**
   * If you have the "experimental: { appDir: true }" setting enabled, then you
   * must comment the below `i18n` config out.
   *
   * @see https://github.com/vercel/next.js/issues/41980
   */
  i18n: {
    locales: ['en-GB', 'it', 'de'],
    defaultLocale: 'en-GB',
  },
  output: 'standalone',
  transpilePackages: ['mui-tel-input'],
  experimental: {
    optimizePackageImports: ['@react-querybuilder/material', '@mui/icons-material', '@mui/material', 'date-fns'],
  },
  serverExternalPackages: ['prom-client'],
  images: {
    domains: ['flagcdn.com'], // For flag icons
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.airahome.com',
        port: '443',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '1337',
      },
      {
        protocol: 'http',
        hostname: 'cms',
        port: '1337',
      },
    ],
  },
  generateBuildId: async () => process.env.BUILD_ID || '1337',
  async redirects() {
    return [
      {
        source: '/solution/:solution/installation-booking',
        destination: '/solution/:solution/installation',
        permanent: true,
      },
      {
        source: '/solution/:solution/installation-report',
        destination: '/solution/:solution/installation-documentation',
        permanent: true,
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: '/socket.io/:path*',
        destination: '/api/socket/:path*', // Proxy to Socket.IO API route
      },
    ];
  },
};

export default nextConfig;
