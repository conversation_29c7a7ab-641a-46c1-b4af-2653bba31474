apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/scrape: 'true'
    prometheus.io/port: "8081"
    prometheus.io/path: "/actuator/prometheus"
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app: {{ .Release.Name }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  replicas: {{ .Values.replicas }}
  strategy:
    type: 'RollingUpdate'
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: {{ .Values.replicas }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
        release: {{ .Release.Name }}
        revision: "{{ .Release.Revision }}"
    spec:
      serviceAccountName: {{ .Release.Name }}
      dnsPolicy: Default
      containers:
        - name: {{ .Release.Name }}
          image: "************.dkr.ecr.eu-north-1.amazonaws.com/{{ .Release.Name }}:{{ .Values.image.tag }}"
          imagePullPolicy: Always
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
          resources:
            requests:
              memory: "{{ .Values.resources.memory }}"
              cpu: "{{ .Values.resources.cpuMin }}"
            limits:
              memory: "{{ .Values.resources.memory }}"
              cpu: "{{ .Values.resources.cpuMax }}"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Release.Name }}
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::{{ .Values.account_number }}:role/application-{{ .Release.Name }}-{{ .Values.environment }}"
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Release.Name }}-external
  labels:
    app: {{ .Release.Name }}
  annotations:
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/auth-type: oidc
    alb.ingress.kubernetes.io/auth-idp-oidc: >
      {
        "issuer": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/v2.0",
        "authorizationEndpoint": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/oauth2/v2.0/authorize",
        "tokenEndpoint": "https://login.microsoftonline.com/0c1dd873-b4fb-48ae-8b6d-b6ba1500a5e4/oauth2/v2.0/token",
        "userInfoEndpoint": "https://graph.microsoft.com/oidc/userinfo",
        "secretName": "aerospace-storybook-azure-oidc"
      }
spec:
  ingressClassName: alb-public
  rules:
    - host: "{{ .Values.ingress.external.host }}"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ .Release.Name }}
                port:
                  name: http
