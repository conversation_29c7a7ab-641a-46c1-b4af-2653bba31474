#!/usr/bin/env bash

set -e

main() {
    cd-to-script-directory
    check-tools-account
    set-token-in-pnpm "$(get-token-from-aws)"
}

# From https://stackoverflow.com/a/246128 - makes it possible to run the script from anywhere, even if it is a symlink
cd-to-script-directory() {
    local SOURCE
    local DIR
    SOURCE=${BASH_SOURCE[0]}
    while [ -L "$SOURCE" ]; do
        DIR=$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )
        SOURCE=$(readlink "$SOURCE")
        [[ $SOURCE != /* ]] && SOURCE=$DIR/$SOURCE
    done
    cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1
}

check-tools-account() {
    if ! ( aws sts get-caller-identity --output text 2> /dev/null | grep -q *********** ); then
        echo "You are not logged in to the Tools AWS account."
        echo "Make sure that:"
        echo " - you have set up the AWS CLI: https://github.com/airahome/infra/blob/main/documentation/PERSONAL.md"
        echo " - your AWS_PROFILE is set to the tools profile"
        echo " - your SSO session is still valid (aws sso login)"
        exit 1
    fi
}

get-token-from-aws() {
    if ! aws codeartifact get-authorization-token --domain airahome --region eu-north-1 --domain-owner ************ --query authorizationToken --output text; then
        echo "Failed to get CodeArtifact token"
        exit 1
    fi
}

set-token-in-pnpm() {
    if ! pnpm config set //airahome-************.d.codeartifact.eu-north-1.amazonaws.com/npm/libraries/:_authToken "$1"; then
        echo "Failed to set CodeArtifact token in PNPM"
        exit 1
    fi
    echo "CodeArtifact token set in PNPM"
}

main