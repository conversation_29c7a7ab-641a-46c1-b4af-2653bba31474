version: 2
registries:
  npm-npmjs:
    type: npm-registry
    url: "https://airahome-660263384063.d.codeartifact.eu-north-1.amazonaws.com/npm/libraries/"
    token: ${{secrets.AWS_CODEARTIFACT_TOKEN}}
    replaces-base: true
updates:
- package-ecosystem: "npm"
  directory: "/"
  registries: 
    - npm-npmjs
  schedule:
    interval: "weekly"
  ignore:
    # We should soon be able to upgrade React to v. 19, but until then let's have this.
    - dependency-name: "react"
      update-types: ["version-update:semver-major"]
    - dependency-name: "react-dom"
      update-types: ["version-update:semver-major"]
    - dependency-name: "@types/react"
      update-types: ["version-update:semver-major"]
    - dependency-name: "@types/react-dom"
      update-types: ["version-update:semver-major"]
    
    # Keep this until we have upgraded to MUI >= v6
    - dependency-name: "@mui/material"
      update-types: ["version-update:semver-major"]
    - dependency-name: "@mui/system"
      update-types: ["version-update:semver-major"]
    - dependency-name: "@mui/x-*"
      update-types: ["version-update:semver-major"]

    # Ignore all Aira dependencies for now to avoid authentication errors
    - dependency-name: "@aira/*"

  groups:
    trpc:
      patterns:
        - "@trpc*"
    NextJS:
      patterns:
        - "next"
        - "eslint-config-next"
        - "@next/third-parties"
    MUI:
      patterns:
        - "@mui/*"
      exclude-patterns:
        - "@mui/x-*"
    MUI-X:
      patterns:
        - "@mui/x-*"
    react:
      patterns:
        - "react"
        - "react-dom"
        - "@types/react"
    playwright:
      patterns:
        - "playwright"
        - "@playwright/*"
    storybook:
      patterns:
        - "storybook"
        - "@storybook/*"
    tanstack_table:
      patterns:
        - "@tanstack/react-table"
        - "@tanstack/table-core"
