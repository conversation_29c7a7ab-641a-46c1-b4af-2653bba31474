# This workflow uses a GitHub App to authenticate for committing changes and making a PR, see:
#  - https://github.com/airahome/team-energy-solution-design/blob/main/docs/team-esd-app.md

name: Fetch Translations
permissions:
  contents: read
on:
  workflow_dispatch:

jobs:
  fetch-translations:
    runs-on: [self-hosted, generic]
    environment: prod
    defaults:
      run:
        working-directory: ./scripts
    env:
      BRANCH_NAME: update-translations-from-poeditor-${{ github.run_id }}

    steps:
    - id: 'github-app'
      uses: "airahome/gha-github-app-authentication@v2"
      with:
        app-id: '1275102'
        installation-id: '66832165'
        private-key: "${{ secrets.TEAM_ESD_APP_PRIVATE_KEY }}"

    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: "${{ steps.github-app.outputs.token }}"

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Fetch translations from POEditor
      env:
        POEDITOR_API_TOKEN: ${{ secrets.POEDITOR_API_TOKEN }}
        POEDITOR_PROJECT_ID: ${{ secrets.POEDITOR_PROJECT_ID }}
        AUTHOR: "${{ steps.github-app.outputs.username }}"
        EMAIL: "${{ steps.github-app.outputs.email }}"
      run: |
        node --experimental-strip-types ./fetch-translations.ts
        git checkout -b $BRANCH_NAME
        git add ../public/locales/*
        git \
          -c "user.name=${AUTHOR}" \
          -c "user.email=${EMAIL}" \
          commit \
          -m "Update translations" -m "Triggered by @${{ github.actor }}"
        git push origin $BRANCH_NAME

    - name: Create pull request
      env:
        GH_TOKEN: "${{ steps.github-app.outputs.token }}"
      run: |
        gh pr create -B main -H "$BRANCH_NAME" \
          --title 'Update translations' \
          --body "Update translations with the latest ones from POEditor. Triggered by @${{ github.actor }}."
