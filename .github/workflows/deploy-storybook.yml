name: Storybook deployment
permissions:
  id-token: write            # required to assume our AWS roles
  contents: read
on:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  Build-Storybook:
    runs-on: [ self-hosted, generic ]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"

      - name: Assume role in tools
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::660263384063:role/gha-team-esd-tools
          aws-region: eu-north-1
      - id: install-pnpm
        name: Install pnpm
        uses: pnpm/action-setup@v4
      - id: setup-node
        name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
          cache: 'pnpm'
      - id: get-codeartifact-token
        name: 'Get CodeArtifact token'
        shell: bash
        run: |
          token=$(aws codeartifact get-authorization-token \
            --domain airahome \
            --domain-owner 660263384063 \
            --query authorizationToken \
            --output text)
          echo "token=$token" >> $GITHUB_OUTPUT
      - name: Configure pnpm to use CodeArtifact token
        run: |
          pnpm config set '//airahome-660263384063.d.codeartifact.eu-north-1.amazonaws.com/npm/libraries/:_authToken' ${{ steps.get-codeartifact-token.outputs.token }}
      - name: Setup environment
        run: cp .env.ci .env
      - name: Install dependencies & build storybook
        working-directory: .
        run: |
          pnpm install --frozen-lockfile
          pnpm build-storybook
        env:
          NEXT_PUBLIC_GOOGLE_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_API_KEY }}
      - name: Assume role in tools
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::660263384063:role/gha-team-esd-tools
          aws-region: eu-north-1
      - name: Login to ECR
        id: login_ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Create Docker container and push to ECR
        working-directory: .
        env:
          REPO: ${{ steps.login_ecr.outputs.registry }}/aerospace-storybook
          TAG: aerospace-storybook
          CACHE_TAG: aerospace-storybook-latest-cache
        run: |
          docker buildx build \
            --cache-from ${REPO}:${CACHE_TAG} \
            --file ./storybook.Dockerfile \
            --tag ${REPO}:${TAG} \
            ./
          docker push ${REPO}:${TAG}
          docker tag ${REPO}:${TAG} ${REPO}:${CACHE_TAG}
          docker push ${REPO}:${CACHE_TAG}
          rm -rf storybook-static
  Deploy-Storybook:
    needs: Build-Storybook
    runs-on: [ self-hosted, generic ]
    steps:
      - uses: actions/checkout@v4
      - name: Assume role in prod
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::528895488893:role/gha-team-esd-prod
          aws-region: eu-north-1
      - name: Use K8s prod
        run: |
          aws eks update-kubeconfig --name prod
          kubectl config use-context arn:aws:eks:eu-north-1:528895488893:cluster/prod
          kubectl config set-context --current --namespace=application
      - name: Deploy app using Helm
        run: |
          helm upgrade \
            --install \
            --wait \
            --set image.tag=aerospace-storybook \
            --values kubernetes/aerospace-storybook/values-prod.yaml \
            --namespace team-esd \
            aerospace-storybook \
            ./kubernetes/aerospace-storybook

        