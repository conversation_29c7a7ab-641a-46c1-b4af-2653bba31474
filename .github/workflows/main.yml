name: Build
permissions:
  id-token: write            # required to assume our AWS roles
  contents: write            # required to create releases
  actions: write
  pull-requests: write       # required to allow dependabot PRs to update check results
  checks: write              # required to allow dependabot PRs to update check results
on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: [self-hosted, generic]
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: Assume role in tools
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::660263384063:role/gha-team-esd-tools
          aws-region: eu-north-1

      - id: login-ecr
        name: Log in to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Setup - Docker buildx (to support --cache-to)
        uses: docker/setup-buildx-action@v3

      - name: Vars - Generic
        id: generic-vars
        run: |
          ECR_REPO=aira-internal-web
          VERSION=${{ github.ref_name }}-${{ github.run_number }}-$(echo ${{ github.sha }} | cut -c1-8)
          CACHE_TAG=${{ steps.login-ecr.outputs.registry }}/${ECR_REPO}:aerospace-cache
          
          echo "ecr-repo=${{ steps.login-ecr.outputs.registry }}/${ECR_REPO}" >> $GITHUB_OUTPUT
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "cache-tag=${CACHE_TAG}" >> $GITHUB_OUTPUT

      - name: Dummy step
        id: changed-files
        run: |
          touch CHANGED_FILES
          
      - id: build-image
        name: Build and push docker image
        if: steps.generic-vars.outcome == 'success'
        env:
          CODEARTIFACT_NPM_TOKEN: ${{ secrets.AWS_CODEARTIFACT_TOKEN }}
        run: |
          repo=${{ steps.generic-vars.outputs.ecr-repo }}
          version=${{ steps.generic-vars.outputs.version }}
          cache_tag=${{ steps.generic-vars.outputs.cache-tag }}

          docker buildx build \
            --cache-from ref=${cache_tag},type=registry \
            --cache-to ref=${cache_tag},type=registry,mode=max,image-manifest=true,oci-mediatypes=true \
            --target app \
            --build-arg "BUILD_ID=${version}" \
            --secret type=env,id=npm_token,src=CODEARTIFACT_NPM_TOKEN \
            --tag ${repo}:${version} \
            --tag ${repo}:HEAD \
            --push \
            --file ./main.Dockerfile \
            .
          # docker push --all-tags ${repo}

      - name: 'Trigger deployment workflow'
        if: steps.build-image.outcome == 'success'
        env:
          GITHUB_TOKEN: ${{ github.token }}
        shell: bash
        run: |
          version=${{ steps.generic-vars.outputs.version }}
          gh workflow run deploy.yml --field image-tag=$version --field env=systest

      - name: Tag git commit with build identifier
        if: steps.build-image.outcome == 'success'
        run: |
          tag=${{ steps.generic-vars.outputs.version }}
          git config --global user.name "GHA build agent"
          git config --global user.email "<EMAIL>"
          git tag -af "${tag}" -m "Build ${version}"
          git push --tags
