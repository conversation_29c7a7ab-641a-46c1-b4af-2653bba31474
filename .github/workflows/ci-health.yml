name: Run all tests
description: |
  This workflow runs all tests for the project in the main branch regularly.
  This way we can follow the health of CI runs over time, uncover flaky tests and see how run time changes.
  This can be inspected in the "Insights" tab on Github, and data can also be fetched using the gh
  CLI:
    ```
    gh run list --repo airahome/aerospace --workflow ci-health.yml
    ```
permissions:
  id-token: write            # required to assume our AWS roles
  contents: read
on:
  schedule:
    - cron: '0 * * * *' # Every hour
  workflow_dispatch:

jobs:
  ci-health:
    runs-on: [ self-hosted, generic ]
    steps:
      - id: checkout
        name: 'Check out repository code'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: assume-ecr-role
        name: Assume role in the AWS tools account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::************:role/gha-team-esd-tools
          aws-region: eu-north-1

      - id: login-ecr
        name: Log in to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Setup - Docker buildx (to support --cache-to)
        uses: docker/setup-buildx-action@v3

      - name: Vars - Generic
        id: generic-vars
        run: |
          ECR_REPO=aira-internal-web
          CACHE_TAG=${{ steps.login-ecr.outputs.registry }}/${ECR_REPO}:aerospace-cache

          echo "ecr-repo=${ECR_REPO}" >> $GITHUB_OUTPUT
          echo "cache-tag=${CACHE_TAG}" >> $GITHUB_OUTPUT

      - name: Install dependencies, build Next app and run tests
        env:
          CODEARTIFACT_NPM_TOKEN: ${{ secrets.AWS_CODEARTIFACT_TOKEN }}
        run: |
          # This bypasses our "only run tests that have changed" logic, since when files like package.json are changed,
          # we want to run all tests.
          echo "package.json" > CHANGED_FILES
          docker buildx build \
            --cache-from ref=${{ steps.generic-vars.outputs.cache-tag }},type=registry \
            --cache-to ref=${{ steps.generic-vars.outputs.cache-tag }},type=registry,mode=max,image-manifest=true,oci-mediatypes=true \
            --target tester \
            --build-arg "BUILD_ID=ci-health-build" \
            --build-arg TEST_CACHE_BUST=$(date +%s) \
            --secret type=env,id=npm_token,src=CODEARTIFACT_NPM_TOKEN \
            --file ./main.Dockerfile \
            .
