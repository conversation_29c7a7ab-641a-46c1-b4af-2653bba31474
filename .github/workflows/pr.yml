name: PR Checks
permissions:
  id-token: write            # required to assume our AWS roles
  contents: read
  pull-requests: write       # required to allow dependabot PRs to update check results
  checks: write              # required to allow dependabot PRs to update check results
on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened

# Cancel ongoing runs of this workflow for the same branch or PR.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  pr-checks:
    runs-on: [ self-hosted, generic ]
    steps:
      - id: checkout
        name: 'Check out repository code'
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - id: paths
        uses: dorny/paths-filter@v3
        with:
          filters: |
            kubernetes:
              - 'kubernetes/**'
      - id: lint-helm-main
        name: Check that the main kubernetes file is valid
        if: steps.paths.outputs.kubernetes == 'true'
        uses: airahome/gha-helm-lint@v1
        with:
          deployments: 'aira-internal-web'

      - id: lint-helm-storybook
        name: Check that the storybook kubernetes file is valid
        if: steps.paths.outputs.kubernetes == 'true'
        uses: airahome/gha-helm-lint@v1
        with:
          deployments: 'aerospace-storybook'
          environments: prod

      - id: assume-ecr-role
        name: Assume role in the AWS tools account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::************:role/gha-team-esd-tools
          aws-region: eu-north-1

      - id: login-ecr
        name: Log in to ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Setup - Docker buildx (to support --cache-to)
        uses: docker/setup-buildx-action@v3

      - name: Vars - Generic
        id: generic-vars
        run: |
          ECR_REPO=aira-internal-web
          CACHE_TAG=${{ steps.login-ecr.outputs.registry }}/${ECR_REPO}:aerospace-cache

          echo "ecr-repo=${ECR_REPO}" >> $GITHUB_OUTPUT
          echo "cache-tag=${CACHE_TAG}" >> $GITHUB_OUTPUT

      - name: Determine files changed in this PR
        id: changed-files
        run: |
          base=${{ github.event.pull_request.base.sha }}
          git diff --name-only $base HEAD > CHANGED_FILES
          
      - name: Install dependencies and run tests
        env:
          CODEARTIFACT_NPM_TOKEN: ${{ secrets.AWS_CODEARTIFACT_TOKEN }}
        run: |
          docker buildx build \
            --cache-from ref=${{ steps.generic-vars.outputs.cache-tag }},type=registry \
            --cache-to ref=${{ steps.generic-vars.outputs.cache-tag }},type=registry,mode=max,image-manifest=true,oci-mediatypes=true \
            --target tester \
            --build-arg "BUILD_ID=pr-build" \
            --secret type=env,id=npm_token,src=CODEARTIFACT_NPM_TOKEN \
            --file ./main.Dockerfile \
            .
