name: Update CodeArtifact Token in Secrets
description: |
  This workflow updates the CodeArtifact token in GitHub secrets for Dependabot and Actions.
  For Dependabot, it needs to be set as a secret in this way for it to be able to access our
  CodeArtifact repo at all. For Actions, we keep it in a secret to improve caching in the.
  Docker build.
permissions:
  id-token: write            # required to assume our AWS roles
  contents: 'read'
on:
  schedule:
    - cron: 0 */11 * * *
  # run manually if needed
  workflow_dispatch:

jobs:
  update:
    runs-on: [ self-hosted, generic ]
    steps:
      - id: assume-ecr-role
        name: Assume role in the AWS tools account
        uses: aws-actions/configure-aws-credentials@v4
        with:
          unset-current-credentials: true
          role-to-assume: arn:aws:iam::************:role/gha-team-esd-tools
          aws-region: eu-north-1

      - id: get-codeartifact-token
        name: 'Get CodeArtifact token'
        shell: bash
        run: |
          token=$(aws codeartifact get-authorization-token \
            --domain airahome \
            --domain-owner ************ \
            --query authorizationToken \
            --output text)
          echo "::add-mask::$token"
          echo "token=$token" >> $GITHUB_OUTPUT

      - id: 'github-app'
        uses: "airahome/gha-github-app-authentication@v2"
        with:
          # These values are documented here: https://github.com/airahome/team-energy-solution-design/blob/main/docs/team-esd-app.md
          app-id: '1275102'
          installation-id: '********'
          private-key: "${{ secrets.TEAM_ESD_APP_PRIVATE_KEY }}"

      - id: 'set-secret'
        env:
          GH_TOKEN: "${{ steps.github-app.outputs.token }}"
        run: |
          gh secret set AWS_CODEARTIFACT_TOKEN --app dependabot --body "${{ steps.get-codeartifact-token.outputs.token }}" --repo airahome/aerospace
          gh secret set AWS_CODEARTIFACT_TOKEN --app actions --body "${{ steps.get-codeartifact-token.outputs.token }}" --repo airahome/aerospace
