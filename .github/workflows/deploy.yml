name: Deploy
permissions:
  id-token: write            # required to assume our AWS roles
  contents: read
  issues: write              # required to create deployment issues
on:
  workflow_dispatch:
    inputs:
      image-tag:
        type: string
        description: Docker image tag
        required: true
      env:
        type: choice
        description: the environment to deploy to
        required: true
        options:
          - systest
          - uat
          - prod

jobs:
  deploy:
    runs-on: [self-hosted, generic]
    environment: ${{ inputs.env }}
    steps:
      - id: deploy
        uses: airahome/gha-helm-deployment@v1
        with:
          provisioner-role: 'gha-team-esd'
          deployments: 'aira-internal-web'
          namespace: team-esd
          image-tag: ${{ github.event.inputs.image-tag }}
          env: ${{ github.event.inputs.env }}
          auto-assign-issues: 'true'
